'use client';

import React, { ReactNode } from 'react';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { usePageContext } from '@sitewide/hooks/usePageContext';
import { formatPrice } from '../../utils';
import { PromoItem } from './PromoItem';

export interface ListProps {
  children?: ReactNode;
  currency: string;
  description?: string | undefined;
  displayName: string;
  handleClickApply?: (value: string) => void;
  isAutoApply: boolean;
  isAvailablePromolistEnabled?: boolean;
  isMobile?: boolean;
  onDismiss: () => void;
  promoDiscount?: string | number;
  promoId: string;
  promoMessage?: string | undefined;
  styleClass?: string;
}

export const Separator = () => {
  return <div className='border-cb-coreColor-medium my-4 border-b border-solid' />;
};

export const AppliedPromosHeader = ({ appliedPromotionsCount, children }: { appliedPromotionsCount: number; children?: ReactNode }) => {
  const { localize } = useLocalize();
  const title = children ? `${appliedPromotionsCount} ${children}` : `${localize('promos.appliedPromos')} (${appliedPromotionsCount})`;
  return <div className={`${children ? 'cb-special-sm-emphasis pt-4' : 'cb-base-compact'}`}>{title}</div>;
};

export const PromoList = ({
  currency,
  description,
  displayName,
  isAutoApply,
  isMobile,
  onDismiss,
  children,
  promoDiscount,
  promoId,
  styleClass,
  isAvailablePromolistEnabled,
  handleClickApply,
  promoMessage,
}: ListProps) => {
  const { locale } = usePageContext();

  return (
    <div key={promoId} className='mt-2'>
      <PromoItem
        displayName={displayName}
        promoId={promoId}
        isMobile={isMobile}
        promoDiscount={formatPrice(promoDiscount!, currency, locale)}
        isAutoApply={isAutoApply}
        description={description}
        onDismiss={onDismiss}
        styleClass={`${styleClass} ${promoMessage && 'border border-red-500'}`}
        handleClickApply={handleClickApply}
        isAvailablePromolistEnabled={isAvailablePromolistEnabled ?? false}
      >
        {children}
      </PromoItem>
      {promoMessage && (
        <div role='alert' className='text-cb-textColor-error cb-base-note h-auto pt-2'>
          {promoMessage}
        </div>
      )}
    </div>
  );
};
