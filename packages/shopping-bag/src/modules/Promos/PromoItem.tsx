'use client';

import React, { ReactNode, useState } from 'react';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { Card } from '@ecom-next/core/migration/card';
import { DrawerSlidePosition } from '@ecom-next/core/migration/drawer';
import { ModalDrawerWrapper } from '@ecom-next/core/migration/modal';
import { fetchPromoInfo } from '../../http-client/fetchPromoInfo';
import { getHeaders } from '../../utils/getHeaders';
import { useBreakpoint } from '../../hooks/useBreakPoint';
import { Feature, logNewRelicError } from '../../utils/newrelic-logger';

type Props = {
  children?: ReactNode;
  description?: string;
  displayName: string;
  handleClickApply?: (value: string) => void;
  isAutoApply: boolean;
  isMobile?: boolean;
  onDismiss: () => void;
  promoDiscount: string;
  promoId: string;
} & {
  description?: string;
  displayName: string;
  isAvailablePromolistEnabled: boolean;
  styleClass?: string;
};

export const PromoItem = ({
  children,
  description,
  displayName,
  isAutoApply,
  isMobile: isMobileProp,
  onDismiss,
  promoId,
  styleClass,
  promoDiscount,
  isAvailablePromolistEnabled,
  handleClickApply,
}: Props) => {
  const [isOpen, setIsOpen] = useState(false);
  const [promoMarketingDescription, setPromoMarketingDescription] = useState<string | undefined>('');
  const [promoLegalTerms, setPromoLegalTerms] = useState<string | undefined>('');
  const [isRemoveLoading, setIsRemoveLoading] = useState<boolean>(false);
  const pageContext = usePageContext();
  const headers = getHeaders(pageContext);
  const { locale } = pageContext;
  const { localize } = useLocalize();
  let { isMobile = false } = useBreakpoint();
  isMobile = isMobileProp ?? isMobile;

  const openModal = async () => {
    if (promoId === null) return;
    const response = await fetchPromoInfo(pageContext.ecomApiBaseUrl, promoId, headers);
    if (!response || !Array.isArray(response) || !response.length) {
      return;
    }
    const [{ promotionMessages }] = response;
    const mapPromotionMessages = promotionMessages.map(({ localeCode, marketingDescription, legalTermsText }) => ({
      locale: localeCode,
      marketingDescription: marketingDescription,
      legalTerms: legalTermsText,
    }));

    const isCurrentLocale = (message: { locale: string }) => message.locale === locale;
    const messages = mapPromotionMessages.find(isCurrentLocale);

    setPromoMarketingDescription(messages?.marketingDescription);
    setPromoLegalTerms(messages?.legalTerms);
    setIsOpen(true);
  };

  const closeModal = () => {
    setIsOpen(false);
  };

  const onPromoDismiss = async () => {
    setIsRemoveLoading(true);

    try {
      await onDismiss();
    } catch (err: unknown) {
      logNewRelicError(err as Error, { caller: 'onPromoDismiss()', feature: Feature.PROMO, message: 'Error removing promo' });
    }

    setIsRemoveLoading(false);
  };
  const onClickApply = async () => {
    if (handleClickApply) {
      handleClickApply(displayName);
    }
  };

  return (
    <Card className={styleClass}>
      <div className={`flex justify-between`}>
        <Card.PrimaryInfo description={description} isClickable={true} onClick={openModal} rightLabel={isAvailablePromolistEnabled ? '' : promoDiscount}>
          <span className='cb-special-sm-emphasis underline'>{displayName}</span>
        </Card.PrimaryInfo>
        {isAvailablePromolistEnabled && (
          <div
            role='button'
            tabIndex={0}
            className='cb-special-sm-emphasis primary-info-right-label px-2 pt-2 underline'
            onClick={onClickApply}
            onKeyDown={e => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                onClickApply();
              }
            }}
          >
            Apply
          </div>
        )}
        <ModalDrawerWrapper
          position={DrawerSlidePosition.bottom}
          isOpen={isOpen}
          onClose={closeModal}
          title={localize('promos.details')}
          closeButtonAriaLabel={'close-modal'}
          hasRoundedCorners={true}
          isMobile={isMobile}
          closeIcon={true}
        >
          <div className='cb-base-compact'>
            <div>{displayName}</div>
            {!isAutoApply && promoMarketingDescription && <p className='pt-2'>{promoMarketingDescription}</p>}
            {promoLegalTerms && <p className='pt-2'>{promoLegalTerms}</p>}
          </div>
        </ModalDrawerWrapper>
        {!isAvailablePromolistEnabled && (
          <div data-testid='remove-promo'>
            <Card.DismissButton loadingClass='pt-2' isDismissable={!isAutoApply} onDismiss={onPromoDismiss} isLoading={isRemoveLoading} />
          </div>
        )}
      </div>
      {children && <Card.Note noteClass='bg-cb-coreColor-lighter'>{children}</Card.Note>}
    </Card>
  );
};
