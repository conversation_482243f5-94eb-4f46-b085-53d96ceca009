import type { ProductData } from '@ecom-next/product/legacy/style-level';
import { CSSObject } from '@ecom-next/core/react-stitch';
import { ProductListFulfillment } from '../../utils/bagTypes';
import { Product } from '../../types/xapi-response-types';
import { UserStatus } from '../../types/bag-types';

export type ImgProps = {
  imageUrl: string;
  productName: string;
};

export type NameProps = {
  productName: string;
  productUrl?: string;
};

type PromoFlags = {
  message: string;
  type: string;
};

export type CapiResponse = {
  data: ProductData | null;
  hasError: boolean;
  isLoading: boolean;
};

export type EditBodyProps = {
  capiResponse: CapiResponse;
  localize: (key: string) => string;
  onClose: () => void;
  product: Product;
  userStatus: UserStatus;
};

export type CardExclusiveProps = {
  localize: (key: string) => string;
};

export type AppliedPromotions = {
  code: string;
  discountTotal: string;
  displayName: string;
  flags: PromoFlags[];
  isAutoApply: boolean;
  isLoyaltyPromo: boolean;
  isTenderPromo: boolean;
  promoDescription?: string;
  promoId: string;
};

export type PromoDetailsProps = {
  appliedPromotions: AppliedPromotions[];
  notification: string;
  promoCount?: number;
};

export type ProductFlag = {
  date: string;
  message: string;
  type: string;
};

export type FlagProps = {
  count?: number;
  productFlags: ProductFlag[];
};

export type SavingsProps = {
  currency: string;
  totalItemSavings: number;
};

export type ProductItem = {
  fulfillment: ProductListFulfillment;
  id: string;
  imageUrl: string;
  markdownPrice: number;
  maxOrderQty: number;
  productColor: string;
  productFlags?: ProductFlag[];
  productName: string;
  productUrl: string;
  promotions: Promotion[];
  quantity: number;
  regularPrice: number;
  size: string;
  totalItemSavings: number;
};

export type PromotionProps = {
  promotions: Promotion[];
};

export type Promotion = {
  code: string;
  description: string;
  isAutoApply: boolean;
};

export type ProductCardProps = {
  currency: string;
  customStyles?: CSSObject;
  isSavedList: boolean;
  item: Product;
  productFlags: ProductFlag[];
  promos: PromoDetailsProps;
};

export type ProductPriceProps = {
  currency: string;
  markdownPrice: number;
  regularPrice: number;
};

export type AttributesProps = {
  productColor?: string;
  size?: string;
};

export type PriceProps = {
  amount: number;
  currency: string;
  isMarkedDown?: boolean;
  isTotalSavings?: boolean;
  locale: string;
  savingsText?: string;
};

export type ActionsProps = {
  isSavedList: boolean;
};

export type QuantityProps = {
  quantity?: number;
};
