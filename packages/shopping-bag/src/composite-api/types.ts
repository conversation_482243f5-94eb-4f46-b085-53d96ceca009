import { Brand, Locale, Market } from '@ecom-next/utils/server';
import type { PageAppState } from '@ecom-next/sitewide/app-state-provider';
import type { BagActionHandler } from '../reducers/bagReducer';

export type BaseCompositeAPIRequestParams = {
  action: BagActionHandler;
  pageContext?: PageAppState;
};

export interface RequestHeaders {
  brand: Brand;
  locale: Locale;
  market: Market;
}

export type Item = {
  decision?: Decision;
  fulfillment: Fulfillment;
  item_id: string;
  item_savings?: ItemSavings;
  max_allowed_quantity: number;
  price: Price;
  product: Product;
  promotions: Promotion[];
  quantity: number;
  restriction?: Restriction;
  savings: Savings;
  vendor_details?: VendorDetails;
};

export type SavedItem = Omit<Item, 'max_allowed_quantity' | 'savings' | 'item_savings' | 'promotions' | 'fulfillment' | 'restriction' | 'decision'>;

type OutOfStockItem = {
  brand: Brand;
  category_id: string;
  color: string;
  final_item_price: number;
  item_id: string;
  main_image_path: string;
  product_link: string;
  product_name: string;
  regular_price: number;
  sale_price: number;
  size: string;
  sku: string;
  style_number: string;
  summary_image_path: string;
};

export type PromotionGroup = {
  applied_promotions: Promotion[];
  notification: string;
  promoCount: number;
};

type Promotion = {
  auto_apply: boolean;
  code: string;
  discount_total: number;
  display_name: string;
  flags: Array<{
    message: string;
    type: string;
  }>;
  loyalty_promo: boolean;
  plcc: boolean;
  promo_description: string;
  promo_id: number;
  tender_promo: boolean;
};

export type OrderSummary = {
  base_total: number;
  currency_code: string;
  estimated_tax: number;
  estimated_total: number;
  merchandise_subtotal: number;
  retail_delivery_fee: number;
  savings_summary: SavingsSummary;
  shipping_cost: number;
  sub_total: number;
};

type SavingsSummary = {
  discount_pricing_savings: number;
  markdown_and_promo_savings: number;
  markdown_savings: number;
  promo_count: number;
  promo_savings: number;
  promotion_savings: number;
  reward_savings: number;
  total_savings: number;
};

type Price = {
  final_item_price: number;
  final_price: number;
  regular_price: number;
  sale_price: number;
  total_regular_price: number;
};

type Savings = {
  applied_promotions_savings: number;
  markdown_savings: number;
  reward_savings: number;
  total_savings: number;
};

type ItemSavings = {
  discount_price_savings: number;
  total_item_savings: number;
};

export type Product = {
  bopis_eligible: boolean;
  brand: string;
  color: string;
  color_style_number: string;
  description: string;
  gift_card: boolean;
  gwp_indicator: boolean;
  image_resources: ImageResource[];
  meta_data: ProductMetadata;
  name: string;
  product_flags: ProductFlags[];
  restricted_item: boolean;
  reward_points: number;
  rewards_item: boolean;
  size: string;
  sku: string;
  style_number: string;
};

type ImageResource = {
  url: string;
  usage: string;
};

type ProductMetadata = {
  variant_group: boolean;
  variant_group_id?: string;
  variant_key: string;
};
type ProductFlags = {
  display_messages: string[];
  flag_type: string;
  info_message?: {
    info_type: string;
    messages: string[];
  };
  messages: string[]; // Deprecated. Use display_messages instead. https://developer.gapinc.com/catalog/default/api/ui_composite_bags-v1/definition#/:~:text=description%3A%20%22Deprecated.%20Use%20display_messages%20instead.%22
};

type Fulfillment = {
  bopis_enabled: boolean;
  pickup_store_id?: string;
  selected_type: string;
  ship_enabled: boolean;
};

type VendorDetails = {
  product_type_name: string;
  show_seller_name: boolean;
  vendor_id: string;
  vendor_style_number?: string;
  vendor_upc_code?: string;
  web_vendor_name: string;
};

type Restriction = {
  allowed_customer_segments: string[];
  allowed_customer_types: string[];
  allowed_selling_channels: string[];
  allowed_tender_types: string[];
  restriction_description: string;
  restriction_types: string[];
};

type Decision = {
  allowed_actions: Record<
    string,
    Record<
      'ADD_TO_BAG' | 'CHECKOUT' | 'PURCHASE',
      {
        is_restricted: boolean;
        restriction_description: string;
        restriction_reason: string;
      }
    >
  >;
  product_id: string;
};

type ShippingOption = {
  estimated_delivery_date: string;
  option_id: string;
  option_name: string;
};

export type AfterPayOption = {
  after_pay_eligible: boolean;
  installment_amount: number;
  installment_count: number;
  max_threshold: number;
  min_threshold: number;
};

export type ShippingProgress = {
  amount_left_for_free_shipping: number;
  qualified_for_free_shipping: boolean;
  shipping_threshold: number;
  show_shipping_progress: boolean;
};

type LoyaltyDetails = {
  details: {
    active_points: number;
    formatted_price: number;
    max_points: number;
    min_points: number;
    points_incremental_value: number;
    points_to_value_list: Array<{
      amount: number;
      points: number;
    }>;
    reason_code: number;
    remaining_pts_to_dollar: number;
    rewards_value: number;
    tier: string;
  };
  enrollment_status: string;
};

type ErrorMessages = {
  error_details: Array<{
    field: string;
    message: string;
    resource: string;
  }>;
  message: string;
};

type RewardPointsSummary = {
  balance_reward_points: number;
  item_reward_points: Array<{
    points_redeemed: number;
    points_redeemed_amount: number;
    product_description: string;
    quantity: number;
    sku: string;
  }>;
  order_points_redeemed: number;
  order_points_redeemed_amount: number;
  total_points_redeemed: number;
  total_reward_points: number;
};

type BagSellerType = 'MIXED' | '3PARTY' | 'GAP';

export type CompositeBagResponse = {
  after_pay: AfterPayOption[];
  bag_attributes: {
    bag_id: string;
    bag_items_count: Array<{
      pick_up: number;
      ship: number;
    }>;
    bag_seller_type: BagSellerType;
    bag_type: string;
    user_status: string;
  };
  error_messages: ErrorMessages;
  items: Item[];
  loyalty_details: LoyaltyDetails;
  order_summary: OrderSummary;
  out_of_stock_items: OutOfStockItem[];
  promotions: PromotionGroup;
  reward_points_summary: RewardPointsSummary;
  saved_for_later_items: SavedItem[];
  shipping: ShippingProgress;
  shipping_options: ShippingOption[];
};
