import type { PromotionGroup } from '../composite-api/types';

/**
 * Transforms composite API promotions data to xAPI response format
 */
export const promos = ({ promotions }: { promotions: PromotionGroup }) => {
  const defaultPromo = {
    notification: '',
    applied_promotions: [],
    promoCount: 0,
  };

  const promotionGroup = promotions || defaultPromo;

  const { notification = '', applied_promotions = [] } = promotionGroup;

  const appliedPromotions = (applied_promotions || []).map(promo => ({
    code: promo.code || '',
    displayName: promo.display_name || '',
    promoId: promo.promo_id?.toString() || '',
    // TODO: Verify if isLoyaltyPromo and isTenderPromo are being used in UI
    isTenderPromo: promo.tender_promo || false,
    isLoyaltyPromo: promo.loyalty_promo || false,
    isAutoApply: promo.auto_apply || false,
    promoDescription: promo.promo_description || '',
    viewPromoDetails: null,
    plcc: promo.plcc || false,
    flags: (promo.flags || []).map((flag: { message?: string }) => {
      return {
        message: flag.message || '',
      };
    }),
    discountTotal: promo.discount_total?.toString() || '0.00',
  }));

  return {
    notification,
    appliedPromotions,
    promoCount: appliedPromotions.length,
  };
};
