import { ShoppingBagResponse } from '../types/xapi-response-types';
import type { CompositeBagResponse } from '../composite-api/types';
import { bagAttributes } from './bagAttributes';
import { items as buildItems } from './items';
import { orderSummary } from './orderSummary';
import { promos } from './promos';
import { rewards } from './rewards';
import { savedList } from './savedList';
import { Shipping as buildShipping } from './shipping';
import { buildAfterPay } from './buildAfterPay';

export const buildxAPIData = ({ compositeResponse }: { compositeResponse: CompositeBagResponse }): ShoppingBagResponse => {
  const { items, order_summary, shipping, promotions } = compositeResponse;

  const updatedResponse = {
    actionPayload: {
      isSuccess: true,
      action: 'getShoppingBagPageAction',
    },
    bagAttributes: {
      ...bagAttributes({ compositeResponse }),
      userStatus: bagAttributes({ compositeResponse }).userStatus,
    },
    promos: promos({ promotions }),
    productList: buildItems({ items }),
    afterPay: buildAfterPay(),
    cardSavingsCalcTotal: 0.94,
    currencySymbol: '$',
    globalNotifications: [],
    outOfStockItems: [],
    hasBopisItems: false,
    summaryOfCharges: orderSummary({ order_summary }),
    shipping: buildShipping({ shipping }),
    loyalty: rewards(),
    savedList: savedList(),
  };

  // @ts-ignore eslint-disable-next-line (TODO: Temporary fix for type mismatch)
  return updatedResponse;
};
