import { Shipping } from '../shipping';
import type { ShippingProgress } from '../../composite-api/types';

// Mock data constants
const MOCK_SHIPPING_COMPLETE: ShippingProgress = {
  shipping_threshold: 75,
  qualified_for_free_shipping: true,
  amount_left_for_free_shipping: 0,
  show_shipping_progress: false,
};

const MOCK_SHIPPING_PARTIAL: ShippingProgress = {
  shipping_threshold: 100,
  qualified_for_free_shipping: false,
  amount_left_for_free_shipping: 25.5,
  show_shipping_progress: true,
};

const MOCK_SHIPPING_MINIMAL: ShippingProgress = {
  shipping_threshold: 30,
} as ShippingProgress;

const MOCK_SHIPPING_ZERO_VALUES: ShippingProgress = {
  shipping_threshold: 0,
  qualified_for_free_shipping: false,
  amount_left_for_free_shipping: 0,
  show_shipping_progress: false,
};

// Expected results constants
const EXPECTED_COMPLETE_RESULT = {
  shippingThreshold: 75,
  isQualifiedForFreeShipping: true,
  amountLeftForFreeShipping: 0,
  showShippingProgress: false,
};

const EXPECTED_DEFAULT_RESULT = {
  shippingThreshold: 50,
  isQualifiedForFreeShipping: false,
  amountLeftForFreeShipping: 0,
  showShippingProgress: true,
};

describe('Shipping Builder', () => {
  describe('when shipping data is provided', () => {
    it('should transform complete shipping data correctly', () => {
      const result = Shipping({ shipping: MOCK_SHIPPING_COMPLETE });
      expect(result).toEqual(EXPECTED_COMPLETE_RESULT);
    });

    it('should transform partial shipping data correctly', () => {
      const result = Shipping({ shipping: MOCK_SHIPPING_PARTIAL });

      expect(result).toEqual({
        shippingThreshold: 100,
        isQualifiedForFreeShipping: false,
        amountLeftForFreeShipping: 25.5,
        showShippingProgress: true,
      });
    });

    it('should handle minimal shipping data with defaults', () => {
      const result = Shipping({ shipping: MOCK_SHIPPING_MINIMAL });

      expect(result).toEqual({
        shippingThreshold: 30,
        isQualifiedForFreeShipping: false, // Default
        amountLeftForFreeShipping: 0, // Default
        showShippingProgress: true, // Default
      });
    });

    it('should handle zero values correctly', () => {
      const result = Shipping({ shipping: MOCK_SHIPPING_ZERO_VALUES });

      expect(result).toEqual({
        shippingThreshold: 0,
        isQualifiedForFreeShipping: false,
        amountLeftForFreeShipping: 0,
        showShippingProgress: false,
      });
    });

    it('should handle decimal values correctly', () => {
      const shippingData: ShippingProgress = {
        shipping_threshold: 49.99,
        qualified_for_free_shipping: false,
        amount_left_for_free_shipping: 12.34,
        show_shipping_progress: true,
      };

      const result = Shipping({ shipping: shippingData });

      expect(result.shippingThreshold).toBe(49.99);
      expect(result.amountLeftForFreeShipping).toBe(12.34);
    });
  });

  describe('when shipping data is empty or invalid', () => {
    it('should return default values when shipping is null', () => {
      const result = Shipping({ shipping: null as unknown as ShippingProgress });
      expect(result).toEqual(EXPECTED_DEFAULT_RESULT);
    });

    it('should return default values when shipping is undefined', () => {
      const result = Shipping({ shipping: undefined as unknown as ShippingProgress });
      expect(result).toEqual(EXPECTED_DEFAULT_RESULT);
    });

    it('should return default values when shipping is empty object', () => {
      const result = Shipping({ shipping: {} as ShippingProgress });
      expect(result).toEqual(EXPECTED_DEFAULT_RESULT);
    });
  });

  describe('property mapping validation', () => {
    it('should correctly map snake_case to camelCase', () => {
      const result = Shipping({ shipping: MOCK_SHIPPING_COMPLETE });

      // Verify camelCase properties exist
      expect(result).toHaveProperty('shippingThreshold', 75);
      expect(result).toHaveProperty('isQualifiedForFreeShipping', true);
      expect(result).toHaveProperty('amountLeftForFreeShipping', 0);
      expect(result).toHaveProperty('showShippingProgress', false);

      // Verify snake_case properties don't exist
      expect(result).not.toHaveProperty('shipping_threshold');
      expect(result).not.toHaveProperty('qualified_for_free_shipping');
      expect(result).not.toHaveProperty('amount_left_for_free_shipping');
      expect(result).not.toHaveProperty('show_shipping_progress');
    });

    it('should return correct data types', () => {
      const result = Shipping({ shipping: MOCK_SHIPPING_COMPLETE });

      expect(typeof result.shippingThreshold).toBe('number');
      expect(typeof result.isQualifiedForFreeShipping).toBe('boolean');
      expect(typeof result.amountLeftForFreeShipping).toBe('number');
      expect(typeof result.showShippingProgress).toBe('boolean');
    });
  });

  describe('default values validation', () => {
    it('should use correct default values', () => {
      const result = Shipping({ shipping: {} as ShippingProgress });

      expect(result.shippingThreshold).toBe(50); // Default threshold
      expect(result.isQualifiedForFreeShipping).toBe(false); // Default not qualified
      expect(result.amountLeftForFreeShipping).toBe(0); // Default amount
      expect(result.showShippingProgress).toBe(true); // Default show progress
    });

    it('should override defaults when values are provided', () => {
      const shippingData: ShippingProgress = {
        shipping_threshold: 75,
        qualified_for_free_shipping: true,
        amount_left_for_free_shipping: 25,
        show_shipping_progress: false,
      };

      const result = Shipping({ shipping: shippingData });

      expect(result.shippingThreshold).toBe(75); // Not default 50
      expect(result.isQualifiedForFreeShipping).toBe(true); // Not default false
      expect(result.amountLeftForFreeShipping).toBe(25); // Not default 0
      expect(result.showShippingProgress).toBe(false); // Not default true
    });
  });

  describe('return format validation', () => {
    it('should always return an object with expected properties', () => {
      const result = Shipping({ shipping: MOCK_SHIPPING_COMPLETE });

      expect(typeof result).toBe('object');
      expect(result).toHaveProperty('shippingThreshold');
      expect(result).toHaveProperty('isQualifiedForFreeShipping');
      expect(result).toHaveProperty('amountLeftForFreeShipping');
      expect(result).toHaveProperty('showShippingProgress');
    });

    it('should not return an array', () => {
      const result = Shipping({ shipping: MOCK_SHIPPING_COMPLETE });
      expect(Array.isArray(result)).toBe(false);
    });

    it('should have exactly 4 properties', () => {
      const result = Shipping({ shipping: MOCK_SHIPPING_COMPLETE });
      expect(Object.keys(result)).toHaveLength(4);
    });
  });

  describe('edge cases', () => {
    it('should handle string values in numeric fields', () => {
      const shippingData = {
        shipping_threshold: '100' as unknown as number,
        qualified_for_free_shipping: false,
        amount_left_for_free_shipping: '25.50' as unknown as number,
        show_shipping_progress: true,
      } as ShippingProgress;

      const result = Shipping({ shipping: shippingData });

      // Should handle string values gracefully
      expect(result.shippingThreshold).toBe('100');
      expect(result.amountLeftForFreeShipping).toBe('25.50');
    });

    it('should handle missing individual properties', () => {
      const shippingData = {
        shipping_threshold: 60,
        // Missing other properties
      } as ShippingProgress;

      const result = Shipping({ shipping: shippingData });

      expect(result.shippingThreshold).toBe(60);
      expect(result.isQualifiedForFreeShipping).toBe(false);
      expect(result.amountLeftForFreeShipping).toBe(0);
      expect(result.showShippingProgress).toBe(true);
    });
  });
});
