import { items } from '../items';
import { isWindowDefined } from '../../utils/isWindowDefined';
import type { Item } from '../../composite-api/types';
import compositeResponseMock from './composite-mock.json';

jest.mock('../../utils/isWindowDefined', () => ({
  isWindowDefined: jest.fn().mockReturnValue(true),
}));

describe('items', () => {
  it('should transform item data correctly', () => {
    Object.defineProperty(window, 'location', {
      value: {
        origin: 'https://test.example.com',
      },
    });

    const result = items({ items: compositeResponseMock.items });

    expect(result).toHaveLength(2);

    expect(result[0]).toEqual({
      regularPrice: 55,
      vendorId: '2065717',
      markdownPrice: 17,
      finalItemPrice: 44,
      finalPrice: 44,
      productUrl: 'https://test.example.com/browse/product.do?pid=7348740021601',
      imageUrl: 'https://www1.assets-gap.com/webcontent/0056/740/139/cn56740139.jpg',
      webVendorName: 'TSEATJEWELRY',
      primaryCategoryName: '',
      productName: 'Transcend V-Neck Crop Sports Bra D-DD',
      productColor: 'Bright White',
      size: '3X',
      sku: '7348740021601',
      categoryNumber: '',
      savingsPrice: 74,
      totalItemSavings: 74,
      quantity: 1,
      maxOrderQty: 5,
      brandId: undefined,
      brandAbbrName: 'GAP',
      brandFullName: undefined,
      id: '1dd1b031-34ed-4470-9eb6-913fc3561db1',
      gwpIndicator: false,
      styleNumber: '841560',
      productType: 'Dropship',
      fulfillment: {
        selectedType: 'SHIP',
        pickUpStoreId: '148',
        isShipEnabled: true,
        isBopisEnabled: true,
      },
      promotions: [
        {
          code: 'PROMO123',
          isAutoApply: false,
          description: 'PROMO 123',
          savings: 7.25,
        },
      ],
      appliedRewards: [],
      rewardsItem: true,
      rewardPoints: 500,
      error: {},
      productFlags: [
        {
          flag_type: 'warning',
          messages: ['string'],
          display_messages: ['string'],
          info_message: {
            info_type: 'string',
            messages: ['string'],
          },
        },
      ],
    });

    expect(result[1]).toEqual({
      regularPrice: 55,
      vendorId: '2065717',
      markdownPrice: 17,
      finalItemPrice: 44,
      finalPrice: 44,
      productUrl: 'https://test.example.com/browse/product.do?pid=7348740021601',
      imageUrl: 'https://www1.assets-gap.com/webcontent/0057/942/832/cn57942832.jpg',
      webVendorName: 'TSEATJEWELRY',
      primaryCategoryName: '',
      productName: 'Mesh Racer Run Mid Rise 4 Short',
      productColor: 'Black',
      size: 'M',
      sku: '7348740021601',
      categoryNumber: '',
      savingsPrice: 74,
      totalItemSavings: 74,
      quantity: 2,
      maxOrderQty: 5,
      brandId: undefined,
      brandAbbrName: 'GAP',
      brandFullName: undefined,
      id: '3a34e2cc-0b2e-40d4-a233-3a024821e911',
      gwpIndicator: false,
      styleNumber: '291749',
      productType: 'Dropship',
      fulfillment: {
        selectedType: 'SHIP',
        pickUpStoreId: '148',
        isShipEnabled: true,
        isBopisEnabled: true,
      },
      promotions: [
        {
          code: 'PROMO123',
          isAutoApply: false,
          description: 'PROMO 123',
          savings: 7.25,
        },
      ],
      appliedRewards: [],
      rewardsItem: true,
      rewardPoints: 500,
      error: {},
      productFlags: [
        {
          flag_type: 'warning',
          messages: ['string'],
          display_messages: ['string'],
          info_message: {
            info_type: 'string',
            messages: ['string'],
          },
        },
      ],
    });
  });

  it('should handle undefined window', () => {
    (isWindowDefined as jest.Mock).mockReturnValue(false);

    const result = items({ items: [compositeResponseMock.items[0]] });

    expect(result).toHaveLength(1);
    expect(result[0].productUrl).toBe('/browse/product.do?pid=7348740021601');
  });

  it('should fallback to first image when small is not available', () => {
    const mockItem = compositeResponseMock.items[0];

    const mockItemWithSmallImage: Item = {
      ...mockItem,
      product: {
        ...mockItem.product,
        image_resources: [
          {
            url: 'large.jpg',
            usage: 'large',
          },
        ],
      },
    };
    const result = items({ items: [mockItemWithSmallImage] });

    expect(result[0].imageUrl).toBe('https://www1.assets-gap.com/large.jpg');
  });
});
