import { promos } from '../promos';
import type { PromotionGroup } from '../../composite-api/types';

// Mock data constants with meaningful, user-friendly data
const MOCK_WELCOME_PROMO: PromotionGroup = {
  notification: 'Welcome promotion successfully applied!',
  applied_promotions: [
    {
      code: 'WELCOME20',
      display_name: 'Welcome 20% Off',
      promo_id: 125206,
      tender_promo: false,
      loyalty_promo: false,
      auto_apply: false,
      plcc: false,
      promo_description: 'Get 20% off your first order with us',
      discount_total: 15.5,
      flags: [
        {
          message: 'NEW_CUSTOMER',
          type: 'informational',
        },
        {
          message: 'LIMITED_TIME',
          type: 'informational',
        },
      ],
      view_promo_details: '',
    },
  ],
  promoCount: 0,
};

const MOCK_MULTIPLE_PROMOS: PromotionGroup = {
  notification: 'Multiple promotions successfully applied!',
  applied_promotions: [
    {
      code: 'CARDHOLDERS15',
      display_name: 'Cardholders 15% Off',
      promo_id: 111,
      tender_promo: true,
      loyalty_promo: false,
      auto_apply: false,
      plcc: true,
      promo_description: 'Get 15% off with your store credit card',
      discount_total: 12.25,
      flags: [
        {
          message: 'CREDIT_CARD_REQUIRED',
          type: 'informational',
        },
      ],
      view_promo_details: '',
    },
    {
      code: 'LOYALTY10',
      display_name: 'Loyalty Member 10% Off',
      promo_id: 222,
      tender_promo: false,
      loyalty_promo: true,
      auto_apply: true,
      plcc: false,
      promo_description: 'Automatic 10% discount for loyalty members',
      discount_total: 8.75,
      flags: [
        {
          message: 'LOYALTY_EXCLUSIVE',
          type: 'informational',
        },
        {
          message: 'AUTO_APPLIED',
          type: 'informational',
        },
      ],
      view_promo_details: '',
    },
  ],
  promoCount: 0,
};

const MOCK_PARTIAL_PROMO: PromotionGroup = {
  notification: 'Basic promotion applied',
  applied_promotions: [
    {
      code: 'BASIC50',
      promo_id: 999,
    } as unknown as PromotionGroup['applied_promotions'][0],
  ],
  promoCount: 0,
};

const MOCK_PROMO_NO_FLAGS: PromotionGroup = {
  notification: 'Summer sale promotion applied',
  applied_promotions: [
    {
      code: 'SUMMER2024',
      display_name: 'Summer Sale 25% Off',
      promo_id: 123,
      tender_promo: false,
      loyalty_promo: false,
      auto_apply: false,
      plcc: false,
      promo_description: 'Summer clearance sale - 25% off everything',
      discount_total: 25.0,
      flags: [],
      view_promo_details: '',
    },
  ],
  promoCount: 0,
};

const MOCK_PROMO_NULL_ID: PromotionGroup = {
  notification: 'Flash sale promotion applied',
  applied_promotions: [
    {
      code: 'FLASH30',
      display_name: 'Flash Sale 30% Off',
      promo_id: null,
      tender_promo: false,
      loyalty_promo: false,
      auto_apply: false,
      plcc: false,
      promo_description: '24-hour flash sale - 30% off select items',
      discount_total: 18.5,
      flags: [],
    },
  ],
};

const MOCK_EMPTY_PROMOTIONS: PromotionGroup = {
  notification: 'No promotions currently available',
  applied_promotions: [],
  promoCount: 0,
};

const MOCK_PROMO_ZERO_DISCOUNT: PromotionGroup = {
  notification: 'Free shipping promotion applied',
  applied_promotions: [
    {
      code: 'FREESHIP',
      display_name: 'Free Shipping',
      promo_id: 555,
      tender_promo: false,
      loyalty_promo: false,
      auto_apply: true,
      plcc: false,
      promo_description: 'Free standard shipping on all orders',
      discount_total: 0,
      flags: [
        {
          message: 'FREE_SHIPPING',
          type: 'informational',
        },
      ],
      view_promo_details: '',
    },
  ],
  promoCount: 0,
};

// Expected results constants
const EXPECTED_WELCOME_RESULT = {
  notification: 'Welcome promotion successfully applied!',
  appliedPromotions: [
    {
      code: 'WELCOME20',
      displayName: 'Welcome 20% Off',
      promoId: '125206',
      isTenderPromo: false,
      isLoyaltyPromo: false,
      isAutoApply: false,
      promoDescription: 'Get 20% off your first order with us',
      viewPromoDetails: null,
      plcc: false,
      flags: [{ message: 'NEW_CUSTOMER' }, { message: 'LIMITED_TIME' }],
      discountTotal: '15.5',
    },
  ],
  promoCount: 1,
};

const EXPECTED_DEFAULT_RESULT = {
  notification: '',
  appliedPromotions: [],
  promoCount: 0,
};

describe('Promos Builder', () => {
  describe('when promotion data is provided', () => {
    it('should transform welcome promotion correctly', () => {
      const result = promos({ promotions: MOCK_WELCOME_PROMO });
      expect(result).toEqual(EXPECTED_WELCOME_RESULT);
    });

    it('should handle multiple promotions (credit card + loyalty)', () => {
      const result = promos({ promotions: MOCK_MULTIPLE_PROMOS });

      expect(result.appliedPromotions).toHaveLength(2);
      expect(result.promoCount).toBe(2);
      expect(result.notification).toBe('Multiple promotions successfully applied!');

      expect(result.appliedPromotions[0]).toEqual({
        code: 'CARDHOLDERS15',
        displayName: 'Cardholders 15% Off',
        promoId: '111',
        isTenderPromo: true,
        isLoyaltyPromo: false,
        isAutoApply: false,
        promoDescription: 'Get 15% off with your store credit card',
        viewPromoDetails: null,
        plcc: true,
        flags: [{ message: 'CREDIT_CARD_REQUIRED' }],
        discountTotal: '12.25',
      });

      expect(result.appliedPromotions[1]).toEqual({
        code: 'LOYALTY10',
        displayName: 'Loyalty Member 10% Off',
        promoId: '222',
        isTenderPromo: false,
        isLoyaltyPromo: true,
        isAutoApply: true,
        promoDescription: 'Automatic 10% discount for loyalty members',
        viewPromoDetails: null,
        plcc: false,
        flags: [{ message: 'LOYALTY_EXCLUSIVE' }, { message: 'AUTO_APPLIED' }],
        discountTotal: '8.75',
      });
    });

    it('should handle partial promotion data with defaults', () => {
      const result = promos({ promotions: MOCK_PARTIAL_PROMO });

      expect(result.appliedPromotions[0]).toEqual({
        code: 'BASIC50',
        displayName: '',
        promoId: '999',
        isTenderPromo: false,
        isLoyaltyPromo: false,
        isAutoApply: false,
        promoDescription: '',
        viewPromoDetails: null,
        plcc: false,
        flags: [],
        discountTotal: '0.00',
      });
    });

    it('should handle promotion with empty flags array', () => {
      const result = promos({ promotions: MOCK_PROMO_NO_FLAGS });

      expect(result.appliedPromotions[0].code).toBe('SUMMER2024');
      expect(result.appliedPromotions[0].displayName).toBe('Summer Sale 25% Off');
      expect(result.appliedPromotions[0].flags).toEqual([]);
      expect(result.appliedPromotions[0].discountTotal).toBe('25');
    });

    it('should handle promotion with null promo_id', () => {
      const result = promos({ promotions: MOCK_PROMO_NULL_ID });

      expect(result.appliedPromotions[0].code).toBe('FLASH30');
      expect(result.appliedPromotions[0].promoId).toBe('');
      expect(result.appliedPromotions[0].discountTotal).toBe('18.5');
    });

    it('should handle promotion with zero discount', () => {
      const result = promos({ promotions: MOCK_PROMO_ZERO_DISCOUNT });

      expect(result.appliedPromotions[0].code).toBe('FREESHIP');
      expect(result.appliedPromotions[0].discountTotal).toBe('0');
      expect(result.appliedPromotions[0].flags).toEqual([{ message: 'FREE_SHIPPING' }]);
    });
  });

  describe('when promotion data is empty or invalid', () => {
    it('should return default values when promotions is null', () => {
      const result = promos({ promotions: null });
      expect(result).toEqual(EXPECTED_DEFAULT_RESULT);
    });

    it('should return default values when promotions is undefined', () => {
      const result = promos({ promotions: undefined });
      expect(result).toEqual(EXPECTED_DEFAULT_RESULT);
    });

    it('should handle empty applied_promotions array', () => {
      const result = promos({ promotions: MOCK_EMPTY_PROMOTIONS });

      expect(result).toEqual({
        notification: 'No promotions currently available',
        appliedPromotions: [],
        promoCount: 0,
      });
    });

    it('should handle null applied_promotions', () => {
      const mockNullPromotions: PromotionGroup = {
        notification: 'Valid notification but null promotions',
        applied_promotions: null as unknown as PromotionGroup['applied_promotions'],
        promoCount: 0,
      };

      const result = promos({ promotions: mockNullPromotions });

      expect(result.notification).toBe('Valid notification but null promotions');
      expect(result.appliedPromotions).toEqual([]);
      expect(result.promoCount).toBe(0);
    });
  });

  describe('property mapping validation', () => {
    it('should correctly map snake_case to camelCase', () => {
      const result = promos({ promotions: MOCK_WELCOME_PROMO });
      const appliedPromo = result.appliedPromotions[0];

      // Verify camelCase properties exist
      expect(appliedPromo).toHaveProperty('displayName', 'Welcome 20% Off');
      expect(appliedPromo).toHaveProperty('promoId', '125206');
      expect(appliedPromo).toHaveProperty('isTenderPromo', false);
      expect(appliedPromo).toHaveProperty('isLoyaltyPromo', false);
      expect(appliedPromo).toHaveProperty('isAutoApply', false);
      expect(appliedPromo).toHaveProperty('promoDescription', 'Get 20% off your first order with us');
      expect(appliedPromo).toHaveProperty('discountTotal', '15.5');

      // Verify snake_case properties don't exist
      expect(appliedPromo).not.toHaveProperty('display_name');
      expect(appliedPromo).not.toHaveProperty('promo_id');
      expect(appliedPromo).not.toHaveProperty('tender_promo');
      expect(appliedPromo).not.toHaveProperty('loyalty_promo');
      expect(appliedPromo).not.toHaveProperty('auto_apply');
      expect(appliedPromo).not.toHaveProperty('promo_description');
      expect(appliedPromo).not.toHaveProperty('discount_total');
    });

    it('should return correct data types', () => {
      const result = promos({ promotions: MOCK_WELCOME_PROMO });
      const appliedPromo = result.appliedPromotions[0];

      expect(typeof appliedPromo.code).toBe('string');
      expect(typeof appliedPromo.displayName).toBe('string');
      expect(typeof appliedPromo.promoId).toBe('string');
      expect(typeof appliedPromo.isTenderPromo).toBe('boolean');
      expect(typeof appliedPromo.isLoyaltyPromo).toBe('boolean');
      expect(typeof appliedPromo.isAutoApply).toBe('boolean');
      expect(typeof appliedPromo.promoDescription).toBe('string');
      expect(typeof appliedPromo.plcc).toBe('boolean');
      expect(typeof appliedPromo.discountTotal).toBe('string');
      expect(Array.isArray(appliedPromo.flags)).toBe(true);
      expect(appliedPromo.viewPromoDetails).toBeNull();
    });
  });

  describe('flag transformation', () => {
    it('should transform flags correctly', () => {
      const result = promos({ promotions: MOCK_WELCOME_PROMO });
      const flags = result.appliedPromotions[0].flags;

      expect(flags).toHaveLength(2);
      expect(flags[0]).toEqual({ message: 'NEW_CUSTOMER' });
      expect(flags[1]).toEqual({ message: 'LIMITED_TIME' });
    });

    it('should handle flags with missing message property', () => {
      const mockPromoWithEmptyFlags: PromotionGroup = {
        notification: 'Test notification',
        applied_promotions: [
          {
            code: 'TEST',
            display_name: 'Test Promo',
            promo_id: 123,
            tender_promo: false,
            loyalty_promo: false,
            auto_apply: false,
            plcc: false,
            promo_description: 'Test description',
            discount_total: 5.0,
            flags: [{ message: undefined }, {}] as unknown as PromotionGroup['applied_promotions'][0]['flags'],
            view_promo_details: '',
          },
        ],
        promoCount: 0,
      };

      const result = promos({ promotions: mockPromoWithEmptyFlags });
      const flags = result.appliedPromotions[0].flags;

      expect(flags).toHaveLength(2);
      expect(flags[0]).toEqual({ message: '' });
      expect(flags[1]).toEqual({ message: '' });
    });
  });

  describe('discount total handling', () => {
    it('should convert numeric discount_total to string', () => {
      const result = promos({ promotions: MOCK_WELCOME_PROMO });
      expect(result.appliedPromotions[0].discountTotal).toBe('15.5');
    });

    it('should handle zero discount total', () => {
      const result = promos({ promotions: MOCK_PROMO_ZERO_DISCOUNT });
      expect(result.appliedPromotions[0].discountTotal).toBe('0');
    });

    it('should use default when discount_total is missing', () => {
      const result = promos({ promotions: MOCK_PARTIAL_PROMO });
      expect(result.appliedPromotions[0].discountTotal).toBe('0.00');
    });
  });

  describe('return format validation', () => {
    it('should always return expected structure', () => {
      const result = promos({ promotions: MOCK_WELCOME_PROMO });

      expect(result).toHaveProperty('notification');
      expect(result).toHaveProperty('appliedPromotions');
      expect(result).toHaveProperty('promoCount');
      expect(Array.isArray(result.appliedPromotions)).toBe(true);
      expect(typeof result.promoCount).toBe('number');
      expect(typeof result.notification).toBe('string');
    });

    it('should calculate promoCount correctly', () => {
      const singlePromo = promos({ promotions: MOCK_WELCOME_PROMO });
      expect(singlePromo.promoCount).toBe(1);

      const multiplePromos = promos({ promotions: MOCK_MULTIPLE_PROMOS });
      expect(multiplePromos.promoCount).toBe(2);

      const emptyPromos = promos({ promotions: MOCK_EMPTY_PROMOTIONS });
      expect(emptyPromos.promoCount).toBe(0);
    });
  });

  describe('edge cases', () => {
    it('should handle empty notification', () => {
      const mockEmptyNotification: PromotionGroup = {
        notification: '',
        applied_promotions: [
          {
            code: 'TEST',
            display_name: 'Test',
            promo_id: 123,
            tender_promo: false,
            loyalty_promo: false,
            auto_apply: false,
            plcc: false,
            promo_description: 'Test',
            discount_total: 10,
            flags: [],
            view_promo_details: '',
          },
        ],
        promoCount: 0,
      };

      const result = promos({ promotions: mockEmptyNotification });
      expect(result.notification).toBe('');
      expect(result.appliedPromotions).toHaveLength(1);
    });

    it('should handle string promo_id conversion', () => {
      const mockStringId: PromotionGroup = {
        notification: 'Test',
        applied_promotions: [
          {
            code: 'TEST',
            display_name: 'Test',
            promo_id: '12345' as unknown as number,
            tender_promo: false,
            loyalty_promo: false,
            auto_apply: false,
            plcc: false,
            promo_description: 'Test',
            discount_total: 10,
            flags: [],
            view_promo_details: '',
          },
        ],
        promoCount: 0,
      };

      const result = promos({ promotions: mockStringId });
      expect(result.appliedPromotions[0].promoId).toBe('12345');
    });
  });
});
