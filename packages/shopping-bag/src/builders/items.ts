import type { Item } from '../composite-api/types';
import { isWindowDefined } from '../utils';

export const items = ({ items: itemList }: { items: Item[] }) => {
  return itemList.map(item => {
    const { fulfillment, price, product, promotions, savings, vendor_details } = item;
    const { brand, image_resources } = product;

    const baseUrl = isWindowDefined() ? window.location.origin : '';
    const productUrl = `${baseUrl}/browse/product.do?pid=${product.sku}`;

    const imagePath = image_resources.find(resource => resource.usage === 'small')?.url || image_resources[0].url;
    const imageUrl = `https://www1.assets-gap.com/${imagePath}`;

    return {
      regularPrice: price.regular_price,
      vendorId: vendor_details?.vendor_id,
      markdownPrice: savings.markdown_savings,
      finalItemPrice: price.final_item_price,
      finalPrice: price.final_price,
      productUrl,
      imageUrl,
      webVendorName: vendor_details?.web_vendor_name,
      primaryCategoryName: '',
      productName: product.name,
      productColor: product.color,
      size: product.size,
      sku: product.sku,
      categoryNumber: '',
      savingsPrice: savings.total_savings,
      totalItemSavings: savings.total_savings,
      quantity: item.quantity,
      maxOrderQty: item.max_allowed_quantity,
      brandId: undefined, // TODO: not being used anywhere in the existing code
      brandAbbrName: brand,
      brandFullName: undefined, // TODO: not being used anywhere in the existing code
      id: item.item_id,
      gwpIndicator: product.gwp_indicator,
      styleNumber: product.style_number,
      productType: vendor_details?.product_type_name,
      fulfillment: {
        selectedType: fulfillment.selected_type,
        pickUpStoreId: fulfillment.pickup_store_id,
        isShipEnabled: fulfillment.ship_enabled,
        isBopisEnabled: fulfillment.bopis_enabled,
      },
      promotions: promotions.map(({ auto_apply, code, discount_total, promo_description }) => ({
        code,
        isAutoApply: auto_apply,
        description: promo_description,
        savings: discount_total,
      })),
      appliedRewards: [], // TODO: not being used at the moment
      rewardsItem: product.rewards_item,
      rewardPoints: product.reward_points,
      error: {}, // TODO: not being used anywhere in the existing code
      productFlags: product.product_flags,
    };
  });
};
