import type { ShippingProgress } from '../composite-api/types';

/**
 * Transforms composite API shipping data to xAPI response format
 * Takes first shipping option or returns defaults
 */
export const Shipping = ({ shipping }: { shipping: ShippingProgress }): object => {
  const shippingItem = shipping || {};

  const { shipping_threshold = 50, qualified_for_free_shipping = false, amount_left_for_free_shipping = 0, show_shipping_progress = true } = shippingItem;

  return {
    shippingThreshold: shipping_threshold,
    isQualifiedForFreeShipping: qualified_for_free_shipping,
    amountLeftForFreeShipping: amount_left_for_free_shipping,
    showShippingProgress: show_shipping_progress,
  };
};
