import { useState, useEffect } from 'react';
import { usePageContext } from '@sitewide/hooks/usePageContext';
import { useShoppingBagFeatures } from '@ecom-next/shopping-bag/utils';
import { useAppState } from '@ecom-next/sitewide/app-state-provider';
import { useActiveBagContext } from '../../contexts/ActiveBagProvider';
import { getHeaders, generateUUID } from '../../utils';
import { getAvailablePromosDetails } from './apiActions';
import { AvailablePromosDetails, AvailablePromosRequestHeaders, CustomerOffersOfferValue, BouncebackOffers } from './Promos.types';
import { buildBounceBackOffers } from './buildBounceBackOffers';

export const usePromos = () => {
  const [availablePromosDetails, setAvailablePromosDetails] = useState<AvailablePromosDetails>();
  const [isDatalayerTriggered, setIsDatalayerTriggered] = useState(false);
  const { bagState: { data } = {} } = useActiveBagContext();
  const appliedPromotions = data?.promos?.appliedPromotions || [];
  const isAuthenticated = data?.bagAttributes.userStatus === 'AUTHENTICATED';
  const { isAvailablePromosEnabled } = useShoppingBagFeatures();
  const pageContext = usePageContext();
  const { ecomApiBaseUrl, previewDate, targetEnv, market } = pageContext;

  const { contentType } = useAppState();

  const isPreview = targetEnv === 'preview';
  const previewType = contentType?.toLowerCase();
  const returnPreviewDate = isPreview && previewDate ? previewDate : '';

  const fetchAvailablePromos = async () => {
    const headers = getHeaders(pageContext) as AvailablePromosRequestHeaders;
    headers.append('user-state', 'AUTHENTICATED');
    headers.append('client-id', 'BAG-UI');
    headers.append('correlation-id', generateUUID());

    if (isPreview) {
      headers.append('previewType', previewType);
    }

    const availablePromoData = (await getAvailablePromosDetails(headers, ecomApiBaseUrl, returnPreviewDate)) as AvailablePromosDetails;
    setAvailablePromosDetails(availablePromoData);
  };

  useEffect(() => {
    if (!isAuthenticated || !isAvailablePromosEnabled) return;

    const shouldFetch = data?.productList?.length !== 0 && (!availablePromosDetails || Object.keys(availablePromosDetails).length === 0);
    if (shouldFetch) {
      fetchAvailablePromos();
    }
  }, [data?.productList?.length]);

  const customerOffers: CustomerOffersOfferValue[] = availablePromosDetails?.customer_offers?.flatMap(offer => offer.offer_value) || [];

  const bouncebackOffersRaw: BouncebackOffers[] = availablePromosDetails?.bounceback_offers || [];
  const normalizedBouncebackOffers = buildBounceBackOffers(bouncebackOffersRaw, market) || [];

  const allAvailablePromotions = [...customerOffers, ...normalizedBouncebackOffers];

  const availablePromotions = allAvailablePromotions.filter(offer => !appliedPromotions.some(applied => applied.code === offer.promo_code)) || [];

  return {
    availablePromotions,
    availablePromosDetails,
    isDatalayerTriggered,
    setIsDatalayerTriggered,
  };
};
