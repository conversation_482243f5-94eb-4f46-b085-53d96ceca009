'use client';
import { useEffect, useState } from 'react';
import classNames from 'classnames';
import { Promos as PromoWrapper } from '@ecom-next/shopping-bag/promos';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { useActiveBagContext } from '@ecom-next/shopping-bag/contexts/ActiveBagProvider';
import { signInHyperLink, useShoppingBagFeatures } from '@ecom-next/shopping-bag/utils';
import { DrawerSlidePosition } from '@ecom-next/core/migration/drawer';
import { ModalDrawerWrapper } from '@ecom-next/core/migration/modal';
import { Button } from '@ecom-next/core/migration/button';
import { useBreakpoint } from '../../hooks/useBreakPoint';
import { useDatalayer } from '../../hooks/useDataLayer';
import { usePromos } from './usePromos';

type RemoveEmpPromo = {
  code: string;
  isOpen: boolean;
};

export const Promos = () => {
  const { publishDataLayerEvent } = useDatalayer();
  const { isAvailablePromosEnabled } = useShoppingBagFeatures();
  const [promoClicked, setPromoClicked] = useState<string>('');
  const [isApplyBtnClicked, setIsApplyBtnClicked] = useState<boolean>(false);
  const [promoError, setPromoError] = useState<Record<string, string | undefined>>({});
  const [removeEmpPromo, setRemoveEmpPromo] = useState<RemoveEmpPromo>({
    isOpen: false,
    code: '',
  });

  const { localize } = useLocalize();
  const { isMobile = false } = useBreakpoint();
  const {
    bagState: { data, loading },
    asyncDispatch,
  } = useActiveBagContext();
  const { availablePromotions, availablePromosDetails, setIsDatalayerTriggered, isDatalayerTriggered } = usePromos();

  useEffect(() => {
    if (data?.actionPayload.action === 'applyShoppingBagPromoAction') {
      publishDataLayerEvent({ name: data?.promos?.notification ? 'promo-apply-failure' : 'promo-apply-success' });
    } else if (data?.actionPayload.action === 'removeShoppingBagPromoAction') {
      publishDataLayerEvent({ name: 'promo-remove' });
    }
  }, [data]);
  useEffect(() => {
    if (promoClicked) {
      setPromoError({ [promoClicked]: data?.promos.notification && localize(data?.promos.notification) });
    }
  }, [data?.promos.notification, promoClicked]);

  if (!data) return undefined;

  if (!isDatalayerTriggered && availablePromosDetails !== undefined) {
    publishDataLayerEvent({
      name: 'apply_promo_presented',
      extraAttrs: { promo_count: availablePromotions.length },
    });
    setIsDatalayerTriggered(true);
  }
  const {
    bagAttributes: { userStatus },
    currencySymbol,
    promos: { appliedPromotions },
  } = data;

  const applyPromo = async (value: string) => {
    setPromoClicked('');
    setPromoError({});
    setIsApplyBtnClicked(true);
    publishDataLayerEvent({ name: 'promo-apply-attempt', extraAttrs: { promotion_code: value } });
    await asyncDispatch({ action: 'APPLY_BAG_PROMO', requestPayload: { promoCode: value } });
    setIsApplyBtnClicked(false);
  };
  const applyAvailablePromo = async (value: string) => {
    publishDataLayerEvent({ name: 'promo-apply-attempt', extraAttrs: { promotion_code: value } });
    await asyncDispatch({ action: 'APPLY_BAG_PROMO', requestPayload: { promoCode: value } });
    setPromoClicked(value);
  };

  const closeModal = () => {
    setRemoveEmpPromo({
      isOpen: false,
      code: '',
    });
  };

  const removePromo = async (value: string) => {
    await asyncDispatch({
      action: () => {
        return { actionKey: 'REMOVE_BAG_PROMO', pathVariable: value };
      },
    });

    closeModal();
  };

  const removeHandler = (displayName: string, value: string) => {
    displayName?.toUpperCase().includes('EMPLOYEE')
      ? setRemoveEmpPromo({
          isOpen: true,
          code: value,
        })
      : removePromo(value);
  };

  const recognizedLoyaltyPromoError = () => (
    <>
      {localize('promos.please')} {signInHyperLink(localize('promos.signIn'))} {localize('promos.tierErrorRecognized')}
    </>
  );

  const anonymousLoyaltyPromoError = () => (
    <>
      {localize('promos.please')} {signInHyperLink(localize('promos.signIn'))} {localize('or')} {signInHyperLink(localize('join'))}{' '}
      {localize('promos.tierErrorAnonymous')}
    </>
  );
  const hasPromoApplied = isApplyBtnClicked && loading !== 'SUCCESS';

  return (
    <div className={classNames({ 'mb-2': isMobile }, { 'mb-4': !isMobile })}>
      <PromoWrapper>
        <div className='p-4'>
          <div className='pb-4'>
            <PromoWrapper.Header />
          </div>
          <div className='font-crossbrand bg-cb-coreColor-lighter rounded-md p-4'>
            <PromoWrapper.PromoForm
              handleSubmit={value => applyPromo(value)}
              notification={loading === 'SUCCESS' ? data?.promos.notification : ''}
              isLoading={hasPromoApplied}
              availablePromoClicked={!promoClicked}
            />
            {isAvailablePromosEnabled && (
              <>
                {availablePromotions.length > 0 && (
                  <PromoWrapper.AppliedPromosHeader appliedPromotionsCount={availablePromotions.length}>
                    {localize('promos.availablePromos')}
                  </PromoWrapper.AppliedPromosHeader>
                )}
                {availablePromotions.map(({ promo_id, promo_description, promo_code }, index) => (
                  <PromoWrapper.PromosList
                    key={`promo_id_${index}`}
                    description={promo_description}
                    displayName={promo_code}
                    styleClass='bg-white'
                    isAvailablePromolistEnabled={true}
                    currency=''
                    isAutoApply={false}
                    onDismiss={() => {}}
                    handleClickApply={value => applyAvailablePromo(value)}
                    promoId={promo_id}
                    promoMessage={promoError[promo_code]}
                  />
                ))}
              </>
            )}

            {appliedPromotions.length > 0 && (
              <PromoWrapper.AppliedPromosHeader appliedPromotionsCount={appliedPromotions.length}>
                {localize('promos.appliedPromos')}
              </PromoWrapper.AppliedPromosHeader>
            )}
            {appliedPromotions.map(({ code, displayName, promoId, discountTotal, isAutoApply, promoDescription, flags }) => (
              <>
                <PromoWrapper.PromosList
                  key={promoId}
                  currency={currencySymbol}
                  description={!isAutoApply ? promoDescription : undefined}
                  displayName={displayName}
                  isAutoApply={isAutoApply}
                  styleClass='bg-white'
                  onDismiss={() => removeHandler(displayName, code)}
                  promoDiscount={discountTotal}
                  promoId={promoId}
                  isAvailablePromolistEnabled={false}
                >
                  {flags.length > 0 &&
                    flags.map(flag => {
                      if (flag.message === 'BAGPNP-501520') {
                        if (userStatus === 'RECOGNIZED') {
                          return recognizedLoyaltyPromoError();
                        } else if (userStatus !== 'AUTHENTICATED') {
                          return anonymousLoyaltyPromoError();
                        }
                      }
                      return localize(flag.message);
                    })}
                </PromoWrapper.PromosList>
                <ModalDrawerWrapper
                  position={DrawerSlidePosition.bottom}
                  title={localize('promos.employeeDiscount.header')}
                  hasRoundedCorners
                  isOpen={removeEmpPromo?.isOpen}
                  isMobile={isMobile}
                  onClose={closeModal}
                  closeButtonAriaLabel={localize('promos.employeeDiscount.modalCloseButtonAriaLabel')}
                >
                  <div>
                    <div className='text-cb-textColor-link text-center'>{localize('promos.employeeDiscount.body')}</div>
                    <div>
                      <div className='pt-4'>
                        <Button kind='primary' className='w-full px-[50px]' onClick={() => removePromo(removeEmpPromo?.code)}>
                          <span className='cb-base-default-emphasis text-cb-coreColor-white'>{localize('promos.employeeDiscount.removePromo')}</span>
                        </Button>
                      </div>
                      <div className='pt-2'>
                        <Button kind='flat' fullWidth={true} onClick={closeModal}>
                          {localize('promos.employeeDiscount.cancel')}
                        </Button>
                      </div>
                    </div>
                  </div>
                </ModalDrawerWrapper>
              </>
            ))}
          </div>
        </div>
      </PromoWrapper>
    </div>
  );
};
