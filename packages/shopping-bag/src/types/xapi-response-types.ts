//
// When we migrate to Headless UI, we'll update and move types to a new
// file (directory) with the new types from the Headless UI endpoints.
//

import { Brand } from '@ecom-next/utils/server';
import { BagAttributes, ProductListFulfillment } from '../utils/bagTypes';
import { LoyaltyTier } from '../components/Loyalty/Loyalty.types';
import { ProductFlag, Promotion, PromoDetailsProps } from '../modules/ProductCard/types';

type Product = {
  backOrderDate?: string;
  brandAbbrName: Brand;
  brandFullName?: string;
  brandId?: string;
  categoryNumber: string;
  finalItemPrice: number;
  finalPrice: number;
  fulfillment: ProductListFulfillment;
  gwpIndicator: boolean;
  id: string;
  imageUrl: string;
  markdownPrice: number;
  maxOrderQty: number;
  productColor: string;
  productFlags?: ProductFlag[];
  productName: string;
  productType?: string;
  productUrl: string;
  promotions: Promotion[];
  quantity: number;
  quantityNotification?: string;
  regularPrice: number;
  restrictions?: [
    {
      allowed_customer_types: string[];
    },
  ];
  rewardPoints?: number;
  rewardsItem?: boolean;
  savingsPrice: number;
  size: string;
  sku: string;
  styleNumber: string;
  totalItemSavings: number;
  vendorId: string;
  webVendorName?: string;
};

type SavingsSummary = {
  markdownAndPromoSavings: string;
  markdownSavings: string;
  promoSavings: string;
  rewardSavings: string;
  totalSavings: string;
};

export type SummaryOfCharges = {
  baseTotal: string;
  currencyCode: string;
  myPromos: string;
  myTotal: string;
  savingsSummary: SavingsSummary;
  subTotal: string;
  totalSavings: string;
};

type Shipping = {
  isQualifiedForFreeShipping: boolean;
  shippingThreshold: number;
  showShippingProgress: boolean;
};

export type ItemRewardPoints = {
  pointsRedeemed?: number;
  pointsRedeemedAmount: number;
  productDescription: string;
  quantity?: number;
  sku: string;
}[];

type Loyalty = {
  appliedAmount: number;
  balanceRewardPoints?: number;
  hasAppliedRewards: boolean;
  itemRewardPoints?: ItemRewardPoints;
  rewardsEligibleTotal: number;
  tier: LoyaltyTier;
  orderPointsRedeemedAmount: number;
};

export type ActionPayload = {
  action: string;
  isSuccess: boolean;
};

export type AfterPay = {
  isAfterPayEligible: boolean;
  maxThreshold: number;
  minThreshold: number;
};

export type OutOfStockProduct = {
  brand: string;
  brandAbbrName: Brand;
  categoryId: string;
  color: string;
  finalItemPrice: number;
  imageUrl: string;
  itemId: string;
  mainImagePath: string;
  productLink: string;
  productName: string;
  regularPrice: number;
  salePrice: number;
  size: string;
  sku: string;
  summaryImagePath: string;
};

type ShoppingBagResponse = {
  actionPayload: ActionPayload;
  afterPay: AfterPay;
  bagAttributes: BagAttributes;
  bopisStoreId?: string;
  cardSavingsCalcTotal: number;
  currencySymbol: string;
  globalNotifications?: Array<string>;
  hasBopisItems: boolean;
  loyalty: Loyalty;
  outOfStockItems?: Array<OutOfStockProduct>;
  productList: Array<Product>;
  promos: PromoDetailsProps;
  savedList: Array<Product>;
  shipping: Shipping;
  summaryOfCharges: SummaryOfCharges;
};

export type FulfillmentProps = {
  product: Product;
};

export { type Product, type ShoppingBagResponse };
