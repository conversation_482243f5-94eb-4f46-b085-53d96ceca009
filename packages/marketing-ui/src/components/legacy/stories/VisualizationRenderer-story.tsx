import React, { lazy, useContext, useEffect, useState } from 'react'; // @ts-ignore
import dcVisualizationSdk from 'dc-visualization-sdk'; // @ts-ignore
import { BreakpointContext, BreakpointProvider, type BreakpointProviderProps, LARGE } from '@ecom-next/core/breakpoint-provider'; // @ts-ignore
import { Brands, useEnabledFeatures } from '@ecom-next/core/react-stitch';
import StoryWrapper, { StoryWrapperV2 } from '../../../../.storybook/StoryWrapper';
import JsonDynamicMarketing from '../../json-marketing';
import { DynamicMarketing } from '../../json-marketing.client';
import CmsDynamicMarketing from '../../legacy-mui-entry';
import { MarketingProvider } from '../../marketing-provider';
import FakeProductGrid, { FakeProductGridItem } from './FakeProductGrid';
import {
  brandMapping,
  brandOptions,
  fetchContentItemDataOverHttp,
  localeKeys,
  localeMap,
  normalizeVisualizationContent,
} from './VisualizationRenderer.helpers';

document.addEventListener('click', event => {
  const a = (event.target as HTMLElement).closest('a[href]');
  if (!a) return;
  event.preventDefault();
  alert('Navigation blocked from within Amplience');
});

function VisualizationRenderer(props: { brand: Brands; locale: string }) {
  const { brand: storyBrand, locale: inputLocale } = props;
  const params = new URLSearchParams(window.location.search);
  const contentItemId = params.get('contentItemId');
  const contentApi = params.get('contentApi');
  const brand = params.get('brand') || storyBrand;
  const { greaterOrEqualTo, minWidth } = useContext<BreakpointProviderProps>(BreakpointContext);
  const enabledFeatures = useEnabledFeatures();
  const hasPlpGrid2025 = !!enabledFeatures?.['mui-new-plp-grid-ism-2025'];

  const [ampliencePayload, setAmpliencePayload] = useState<{ content?: Record<string, unknown> }>({});

  /**
   * Check if executing inside of a different window, e.g. true if likely in an iFrame.
   *
   * @returns true if the inside of a frame
   */
  const isInFrame = () => window.self !== window.top;

  const updateAmpliencePayload = payload => {
    let output = payload?.content?.output;
    if (output && typeof output === 'string') {
      output = JSON.parse(output);
    }
    setAmpliencePayload({
      ...payload,
      output,
    });
  };

  const fetchAmplienceContent = () => {
    fetchContentItemDataOverHttp(contentApi, contentItemId).then(data => {
      if (data) {
        updateAmpliencePayload(data);
      }
    });
  };

  // Example API call of how content data is retrived from the content renderer
  // https://marketing-cms.apps.cfplatform.dev.azeus.gaptech.com/storybook/iframe.html?id=v&viewMode=story&contentItemId=8bf3e8e3-bb94-483a-8f60-c23cdefce22d&contentApi=2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io
  useEffect(() => {
    // we'll only directly fetch data from the content-item VSE if we are running outside of a frame
    // inside of a frame we'll rely on the messages sent from the Amplience Dashboard SDK
    if (!isInFrame()) {
      fetchAmplienceContent();
    }

    // @TODO remove this effect when transformers are removed
    const storybookMessageHandler = event => {
      const { data: marketing } = event;
      const { _storybook } = marketing || {};
      if (!_storybook) {
        console.info('No _storybook property in data so skipping event data. Message payload data received', marketing);
        return;
      }
      updateAmpliencePayload(marketing);
    };
    window.addEventListener('message', storybookMessageHandler, false);

    return () => {
      window.removeEventListener('message', storybookMessageHandler, false);
    };
  }, [contentApi, contentItemId]);

  useEffect(() => {
    // if we are running outside of an iFrame, e.g. in a "pop out" visualization,
    // then we can't use the visualization SDK so we won't bother trying to initialize it
    if (!isInFrame()) {
      console.info('Visualization is running outside of a frame, skipping initialization of Amplience SDK');
      return;
    }

    async function visualizationListenerInit() {
      console.info('initializing Amplience Visualization SDK');
      let sdk;
      try {
        sdk = await dcVisualizationSdk.init();
      } catch (error) {
        console.error('Error while initializing Amplience Visualization SDK', error);
        return;
      }

      sdk.form.changed(updateAmpliencePayload);
      const form = await sdk.form.get();
      updateAmpliencePayload(form);
    }

    visualizationListenerInit().catch(e => {
      /**
       * TODO: Improve once this issue is handled
       * {@see https://github.com/amplience/dc-visualization-sdk/issues/21}
       * Unfortunately content is not accessible via the Visualization SDK
       * within the edition / planning context be other issues that cause
       * the sdk.form.* calls to throw. IF there is a contentApi and contentItemId
       * that were fetched from query parameters and IF the visualization sdk
       * related calls failed then there is a good chance this is "static" content
       * anyway without a form and so fetching over http from the content API
       * is a good fallback option.
       */
      fetchAmplienceContent();
      console.error(e);
    });
  }, []);

  if (!contentItemId || !contentApi) {
    return <div>No content params defined</div>;
  }

  return normalizeVisualizationContent(ampliencePayload.content, greaterOrEqualTo(LARGE)).map((normalizedContentItem, index) => {
    /**
     * We are manually setting experimentRunning to false
     * to prevent Optimizly from trying to run
     * and causing a delay in rendering.
     * See withOptimizelyDecorator in core-ui.
     */
    let { output } = normalizedContentItem;
    if (typeof output === 'string') {
      output = JSON.parse(output);
    }
    const finalContent = {
      ...{ ...normalizedContentItem, output },
      errorLogger: console.error,
      experimentRunning: false,
    };

    const conditionalOutput = finalContent.output ?? finalContent;

    const { _meta } = conditionalOutput;
    const isBROrBRFactory = brand === Brands.BananaRepublic || brand === Brands.BananaRepublicFactoryStore;

    let { noOfCards = isBROrBRFactory ? 3 : 4 } = conditionalOutput;
    // checking if the device is mobile or tablet and setting cards to 2 or 1
    if (!greaterOrEqualTo(LARGE)) {
      noOfCards = noOfCards === 1 ? 1 : 2;
    }

    const useGrid = _meta?.schema?.includes('content/v1/ism');
    let width = '100%';

    if (useGrid) {
      require('./assets/styles.css');
      const isGapOrGapFactory = brand === Brands.Gap || brand === Brands.GapFactoryStore;
      const isSingle = _meta?.schema.includes('single');

      // Set width for mobile
      width = !isSingle || noOfCards === 1 ? '100%' : '50%';

      if (isGapOrGapFactory) {
        // Calculation to emulate the new PLP Product Grid for mobile
        width = isSingle ? 'calc(100vw - 176px) / 4' : 'calc(100vw - 144px) / 2';
      }

      // Set width for desktop
      if (greaterOrEqualTo(LARGE)) {
        width = `${100 * ((isSingle ? 1 : 2) / noOfCards)}%`;
      }

      // Calculation to emulate the new PLP Product Grid for desktop
      if (isGapOrGapFactory) {
        width = `${100 * ((isSingle ? 1 : 2) / noOfCards)}%`;

        if (minWidth('x-large')) {
          // lg < 1024
          width = isSingle ? 'calc((100vw - 104px) / 3)' : 'calc((((100vw - 104px) / 3) * 2) + 16px)';
        } else if (minWidth('large')) {
          // xl 1024+
          width = isSingle ? 'calc((100vw - 184px) / 4)' : 'calc((100vw - 152px) / 2)';
        }
      }
    }

    const marketingData = {
      contentData: {
        contentItems: [ampliencePayload.content],
      },
    };

    const storyWrapper = (
      <StoryWrapper
        brand={brand}
        data={conditionalOutput}
        {...conditionalOutput}
        isDesktop={false}
        locale={inputLocale}
        market={'us'}
        StoryComponent={DynamicMarketing}
      />
    );

    const storyWrapperV2 = (
      <StoryWrapperV2
        brand={brand}
        data={conditionalOutput}
        {...conditionalOutput}
        locale={inputLocale}
        market={'us'}
        StoryComponent={DynamicMarketing}
        useProductGrid={useGrid}
        noOfCards={noOfCards}
        isSingle={_meta?.schema?.includes('single')}
      />
    );
    // TODO: the ISM Carousels should use the V2 product grid once the banner
    // dimensions are removed
    const isISMCarousel = useGrid && _meta?.schema?.includes('carousel');

    const marketingGrid =
      hasPlpGrid2025 && !isISMCarousel ? (
        storyWrapperV2
      ) : (
        <FakeProductGrid>
          <FakeProductGridItem width={width}>{storyWrapper}</FakeProductGridItem>
          {Array.from({ length: noOfCards - 1 }).map((_item, indx) => (
            <FakeProductGridItem width={width} noOfCards={noOfCards} key={indx} />
          ))}
        </FakeProductGrid>
      );

    return (
      <React.Fragment key={`marketing_content-${index}`}>
        {!finalContent.rawHtml ? (
          <MarketingProvider value={marketingData} jsonMarketingComponent={JsonDynamicMarketing} cmsMarketingComponent={CmsDynamicMarketing}>
            {useGrid ? marketingGrid : storyWrapper}
          </MarketingProvider>
        ) : (
          <div
            // eslint-disable-next-line react/no-danger
            dangerouslySetInnerHTML={{
              __html: finalContent.rawHtml.HTMLEditorObj,
            }}
          />
        )}
      </React.Fragment>
    );
  });
}

function Template({ brand, locale }: { brand: Brands; locale: string }) {
  return (
    <BreakpointProvider>
      <VisualizationRenderer brand={brand} locale={locale} />
    </BreakpointProvider>
  );
}

export const Component = Template.bind({});

export default {
  title: 'VisualizationRenderer',
  component: Component,
  includeStories: ['Component'],
  argTypes: {
    brand: { control: 'radio', options: brandOptions, mapping: brandMapping },
    locale: { control: 'select', options: localeKeys, mapping: localeMap },
  },
  args: {
    locale: 'EN_US',
  },
};
