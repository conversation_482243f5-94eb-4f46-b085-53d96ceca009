// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SitewideBannerContent should match snapshot for gradient background 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: -webkit-linear-gradient(90deg, #000000,#FFFFFF);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  height: auto;
  position: relative;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      height="0"
      width="0"
    />
  </div>
</DocumentFragment>
`;

exports[`SitewideBannerContent should match snapshot for image background 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-1 {
  width: 100%;
  object-fit: cover;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="product-card-image"
    >
      <img
        class="emotion-1"
        src="https://example.com/i/sample-endpoint/sample-image-name?fmt=webp"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`SitewideBannerContent should match snapshot for solid background 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #FFFFFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  height: auto;
  position: relative;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      height="0"
      width="0"
    />
  </div>
</DocumentFragment>
`;

exports[`SitewideBannerContent should render default content 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: transparent;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  height: auto;
  position: relative;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      height="0"
      width="0"
    />
  </div>
</DocumentFragment>
`;
