'use client';
import { JSX } from 'react'; //@ts-ignore
import { CSSObject, SerializedStyles } from '@ecom-next/core/react-stitch';
import { ScalableText } from '../../../components/Typography/types';
import { CtaProps, LayoutVariantTypes } from '../../global/types';
import { AdvanceImageData } from '../../helpers/ImageEncoder/types';
import { CarouselSettings } from '../../subcomponents/CMSMarketingCarousel/types';
import { CtaButtonStylingProps, CTASize } from '../../subcomponents/CTAButton/index';

interface CtasType {
  cta: CtaProps;
}

export interface ProductCardCtaDropdownType {
  ctaDropdown: CtaProps[];
  label: string;
}
export interface ProductCardTypes {
  buttonStyle?: CtaButtonStylingProps;
  categoryButtonColor?: 'dark' | 'light';
  cta?: CtaProps;
  ctaDropdown?: ProductCardCtaDropdownType;
  ctaDropdownList?: ProductCardCtaDropdownType[];
  ctas?: CtasType[];
  image: AdvanceImageData[];
  rte?: string;
  url?: {
    label: string;
    value: string;
  };
}

export type ProductCardRichTextAlign = 'left' | 'center';

export interface ProductCardsCta {
  buttonStyle?: CtaButtonStylingProps;
  cta?: CtaProps;
}

export interface ProductCardsTopText {
  defaultText?: string;
  mobileOverride?: string;
}

export interface ProductCardsTopAreaProps {
  cta?: ProductCardsCta[];
  ctaDropdownList?: ProductCardCtaDropdownType[];
  ctaSize?: CTASize;
  customCtaDropdownButtonStyles?: CSSObject;
  customCtaDropdownItemsStyles?: CSSObject;
  customCtaStyles?: CSSObject;
  customStyles?: CSSObject;
  scalableText?: ScalableText;
  showCTA?: boolean;
  topRichTextAlign?: ProductCardRichTextAlign;
  topText?: ProductCardsTopText;
}

export type ProductCardProps = ProductCardTypes & {
  productCardCtaButtonStyles?: CSSObject;
  customCtaDropdownButtonStyles?: CSSObject;
  customCtaDropdownItemsStyles?: CSSObject;
  customAnimatedButtonStyles?: CSSObject;
  customDropdownListWrapperStyles?: CSSObject;
  customCtaStyles?: CSSObject;
  customStyles?: CSSObject;
  isCtaConfigurable?: boolean;
  onResize?: (size: number) => void;
  productCardRichTextAlign?: ProductCardRichTextAlign;
  scalableText?: ScalableText;
  spaceAboveCardRichText?: ProductCardsProps['spaceAboveCardRichText'];
  spaceAboveCtaDropdown?: string;
  variant?: LayoutVariantTypes;
  customGapCss?: CSSObject;
};

export interface ProductCardsProps {
  carouselSettings?: CarouselSettings;
  categories: ProductCardProps[];
  categoryButtonColor?: ProductCardTypes['categoryButtonColor'];
  gapBetweenCardsDesktop?: string;
  productCardRichTextAlign?: ProductCardRichTextAlign;
  scalableText?: ScalableText;
  spaceAboveCardRichText?: string;
  spaceAboveCtaDropdown?: string;
  variant: LayoutVariantTypes;
}

export interface CustomPaginationProps {
  appendDots?: (dots: JSX.Element) => JSX.Element;
  className: string;
  css: SerializedStyles;
  customPaging?: ((index: number) => JSX.Element) | undefined;
}
export interface ExtendsProductCardProps extends Omit<ProductCardProps, 'image'> {
  carouselSettings?: CarouselSettings;
  categories: ProductCardsProps['categories'];
  ctaButtonStyle?: CtaButtonStylingProps;
  productCardCtaButtonStyles?: CSSObject;
  customBasicStyles?: CSSObject;
  customCarouselStyles?: CSSObject;
  customCtaDropdownButtonStyles?: CSSObject;
  customCtaDropdownItemsStyles?: CSSObject;
  customDropdownListWrapperStyles?: CSSObject;
  customExposedStyles?: CSSObject;
  customNextButton?: JSX.Element;
  customPrevButton?: JSX.Element;
  customScrollStyles?: CSSObject;
  gapBetweenCardsDesktop?: string;
  isCtaConfigurable?: boolean;
  paginationProps?: CustomPaginationProps;
  useCustomCtaButtons?: boolean;
  useCustomLayout?: boolean;
  variant: LayoutVariantTypes;
  customGapCss?: CSSObject;
}
