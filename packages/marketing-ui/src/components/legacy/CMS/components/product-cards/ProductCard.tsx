'use client';
import React, { JSX } from 'react'; //@ts-ignore
import { styled, useTheme, CSSObject } from '@ecom-next/core/react-stitch';
import BackgroundTypeContainer from '../../subcomponents/BackgroundTypeContainer/index';
import { useViewportIsLarge } from '../../../hooks/useViewportIsLarge/index';
import { RichText } from '../../subcomponents/RichText/index';
import CTADropdown from '../../subcomponents/CTADropdown/index';
import { CTAContainer } from '../../content-types/CTADropdown/components/CTAContainer';
import { RenderCtas } from '../../content-types/WayfindingAndProductCards/br/index';
import { CtaProps } from '../../global/types';
import { CtaButton, CtaButtonStylingProps } from '../../subcomponents/CTAButton/index';
import { ProductCardContainer, getCategoryButtonColor, AnimatedButtonContainer, ProductCardLink } from './styles';
import { ProductCardProps } from './types';

export const ProductCard = ({
  image,
  buttonStyle,
  cta,
  ctas,
  ctaDropdown,
  ctaDropdownList,
  customStyles,
  productCardCtaButtonStyles: customCategoryButtonStyles = {},
  customCtaStyles,
  customCtaDropdownButtonStyles,
  customCtaDropdownItemsStyles,
  customAnimatedButtonStyles,
  customDropdownListWrapperStyles,
  spaceAboveCtaDropdown,
  spaceAboveCardRichText,
  productCardRichTextAlign,
  rte,
  url,
  onResize,
  scalableText,
  categoryButtonColor,
  variant,
  isCtaConfigurable,
  customGapCss = {},
}: ProductCardProps): JSX.Element => {
  const theme = useTheme();
  const isDesktop = useViewportIsLarge();
  const ctaColor = getCategoryButtonColor(categoryButtonColor, theme);
  const hasTextAlign = !!productCardRichTextAlign;
  const customTextAlignment = hasTextAlign ? { textAlign: productCardRichTextAlign } : {};
  const hasCtaDropdown = ctaDropdown && ctaDropdown.ctaDropdown.length > 0;
  const hasCtaDropdownList = ctaDropdownList && ctaDropdownList.length > 0;
  const innerCardGap = {
    gap: spaceAboveCardRichText || spaceAboveCtaDropdown || 20,
  };
  const aspectRatio = '3 / 4';

  type ProductCardCTAProps = {
    buttonStyle: CtaButtonStylingProps | undefined;
    cta: CtaProps | undefined;
    ctaSize?: 'large' | 'medium' | 'small';
  };

  const ProductCardCTA = ({ buttonStyle: buttonStyleProp, cta: ctaProp }: ProductCardCTAProps) => {
    if (ctaProp) {
      return isCtaConfigurable && buttonStyleProp ? (
        <CtaButton ctaButton={ctaProp} ctaButtonStyling={buttonStyleProp} customCtaStyles={customCategoryButtonStyles} />
      ) : (
        <AnimatedButtonContainer ctaColor={ctaColor} href={ctaProp.value} isDesktop={isDesktop} customStyles={customCategoryButtonStyles}>
          <span>{ctaProp.label}</span>
          <div data-testid='animated-arrow'>
            <svg fill='none' height='8' viewBox='0 0 21 8' width='21' xmlns='http://www.w3.org/2000/svg'>
              <path
                d='M20.354 4.35457C20.4477 4.26081 20.5004 4.13366 20.5004 4.00107C20.5004 3.86849 20.4477 3.74134 20.354 3.64757L17.172 0.464575C17.0777 0.373496 16.9514 0.323098 16.8203 0.324238C16.6892 0.325377 16.5638 0.377961 16.4711 0.470665C16.3784 0.563369 16.3258 0.688776 16.3247 0.819874C16.3235 0.950972 16.3739 1.07727 16.465 1.17157L19.293 4.00057L16.465 6.82857C16.3739 6.92288 16.3235 7.04918 16.3247 7.18027C16.3258 7.31137 16.3784 7.43678 16.4711 7.52948C16.5638 7.62219 16.6892 7.67477 16.8203 7.67591C16.9514 7.67705 17.0777 7.62665 17.172 7.53557L20.354 4.35457ZM0 4.50057H20V3.50057H0V4.50057Z'
                fill={ctaColor}
              />
            </svg>
          </div>
        </AnimatedButtonContainer>
      );
    }

    return null;
  };

  const CtaDropdownListWrapper = styled.div(({ dropdownListWrapperStyles }: { dropdownListWrapperStyles?: CSSObject }) => ({
    display: 'flex',
    flexFlow: 'row wrap',
    gap: '15px',
    ...dropdownListWrapperStyles,
  }));

  return (
    <ProductCardContainer customStyles={{ ...innerCardGap, ...customStyles, ...customGapCss }}>
      {url && <ProductCardLink aria-label={url.label} href={url.value} />}
      <BackgroundTypeContainer
        aspectRatio={aspectRatio}
        background={{
          type: 'image',
          images: image,
        }}
        data-testid='product-card-image'
        isDesktop={isDesktop}
        onResize={onResize}
        customCss={customGapCss}
      />
      <ProductCardCTA buttonStyle={buttonStyle} cta={cta} />
      {rte && <RichText css={{ ...customTextAlignment }} disableTextAlign={hasTextAlign} scalableText={scalableText} text={rte} />}
      {ctas?.length && <RenderCtas categoryButtonColor={categoryButtonColor} ctas={ctas} customStyles={customCtaStyles} variant={variant} />}
      {hasCtaDropdown && (
        <>
          <CTAContainer>
            <CTADropdown ctaButtonStyling={buttonStyle} heading={ctaDropdown.label} items={ctaDropdown.ctaDropdown} />
          </CTAContainer>
        </>
      )}
      {hasCtaDropdownList && (
        <CtaDropdownListWrapper dropdownListWrapperStyles={customDropdownListWrapperStyles}>
          {ctaDropdownList.map(({ ctaDropdown: singleCtaDropdown, label }) => (
            <CTADropdown
              key={label}
              buttonStyling={{
                ...customCtaDropdownButtonStyles,
                flexShrink: 0,
              }}
              ctaButtonStyling={buttonStyle}
              heading={label}
              items={singleCtaDropdown}
              itemContainerStyles={customCtaDropdownItemsStyles}
            />
          ))}
        </CtaDropdownListWrapper>
      )}
    </ProductCardContainer>
  );
};

export default ProductCard;
