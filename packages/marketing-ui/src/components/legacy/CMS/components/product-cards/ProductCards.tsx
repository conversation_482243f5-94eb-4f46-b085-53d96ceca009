'use client';
import React, { JSX, useState } from 'react'; //@ts-ignore
import { styled, CSSObject } from '@ecom-next/core/react-stitch';
import { CMSMarketingCarousel } from '../../subcomponents/CMSMarketingCarousel/index';
import { LayoutVariantTypes } from '../../global/types';
import ProductCard from './ProductCard';
import { ExtendsProductCardProps, ProductCardProps } from './types';
import { customCarouselSettings, basicStyles, exposedStyles, scrollStyles, carouselStyles } from './styles';
import { useViewportIsLarge } from '../../../hooks/index';

export const ProductsWrapper = styled.div<{
  customBasicStyles?: CSSObject;
  customCarouselStyles?: CSSObject;
  customExposedStyles?: CSSObject;
  customScrollStyles?: CSSObject;
  gapBetweenCardsDesktop?: string;
  numberOfCards: number;
  useCustomStyle?: boolean;
  variant: LayoutVariantTypes;
}>(({ gapBetweenCardsDesktop, numberOfCards, variant, useCustomStyle, customBasicStyles, customExposedStyles, customScrollStyles, customCarouselStyles }) => {
  if (!useCustomStyle) {
    switch (variant) {
      case 'basic':
        return basicStyles(numberOfCards, gapBetweenCardsDesktop, customBasicStyles);
      case 'exposed':
        return exposedStyles(customExposedStyles);
      case 'scroll':
        return scrollStyles(numberOfCards, customScrollStyles);
      case 'carousel':
        return carouselStyles(customCarouselStyles);
      default:
        return {};
    }
  }
  return {};
});

export const ProductCards = ({
  variant,
  categories,
  categoryButtonColor,
  carouselSettings,
  ctaButtonStyle,
  customStyles = undefined,
  customPrevButton,
  customNextButton,
  gapBetweenCardsDesktop,
  spaceAboveCtaDropdown,
  spaceAboveCardRichText,
  productCardRichTextAlign,
  scalableText,
  useCustomCtaButtons,
  useCustomLayout,
  customBasicStyles = {},
  customScrollStyles = {},
  customExposedStyles = {},
  paginationProps,
  customCarouselStyles = {},
  customCtaStyles = {},
  customCtaDropdownButtonStyles,
  customCtaDropdownItemsStyles,
  customDropdownListWrapperStyles,
  isCtaConfigurable,
  customGapCss = {},
}: ExtendsProductCardProps): JSX.Element => {
  const [height, setHeight] = useState(0);
  const isCarousel = variant === 'carousel';
  const isDesktop = useViewportIsLarge();

  const ProductCardsMap = () => (
    <>
      {categories.map(({ image, buttonStyle, cta, ctas, ctaDropdown, ctaDropdownList, rte, url, productCardCtaButtonStyles }: ProductCardProps) => (
        <ProductCard
          key={image[0].image?.id}
          buttonStyle={buttonStyle || ctaButtonStyle}
          categoryButtonColor={categoryButtonColor}
          cta={cta}
          ctaDropdown={useCustomCtaButtons ? undefined : ctaDropdown}
          ctaDropdownList={ctaDropdownList}
          ctas={ctas}
          customCtaDropdownButtonStyles={customCtaDropdownButtonStyles}
          productCardCtaButtonStyles={productCardCtaButtonStyles}
          customCtaDropdownItemsStyles={customCtaDropdownItemsStyles}
          customDropdownListWrapperStyles={customDropdownListWrapperStyles}
          customCtaStyles={customCtaStyles}
          customStyles={customStyles}
          image={image}
          isCtaConfigurable={isCtaConfigurable}
          productCardRichTextAlign={productCardRichTextAlign}
          rte={rte}
          scalableText={scalableText}
          spaceAboveCardRichText={spaceAboveCardRichText}
          spaceAboveCtaDropdown={spaceAboveCtaDropdown}
          url={url}
          variant={variant}
          customGapCss={customGapCss}
        />
      ))}
    </>
  );

  if (isCarousel) {
    return (
      <ProductsWrapper
        customBasicStyles={customBasicStyles}
        customCarouselStyles={customCarouselStyles}
        customExposedStyles={customExposedStyles}
        gapBetweenCardsDesktop={gapBetweenCardsDesktop}
        numberOfCards={categories.length}
        variant={variant}
      >
        <CMSMarketingCarousel
          arrowPosition={`${height / 2}px`}
          className='product-cards-carousel'
          carouselSettings={customCarouselSettings(carouselSettings)}
          customContainerStyles={{
            '.product-cards-carousel .slick-list': {
              overflow: 'auto',
              overflowX: 'clip',
              overflowY: 'visible',
              transform: 'none',
            },
            '.product-cards-carousel .slick-active': {
              width: Object.keys(customGapCss).length > 0 ? 'auto !important' : undefined,
            },
          }}
          customNextButton={customNextButton}
          customPrevButton={customPrevButton}
          {...paginationProps}
        >
          {categories.map(({ image, buttonStyle, cta, ctas, ctaDropdownList, rte, url, productCardCtaButtonStyles }: ProductCardProps) => (
            <ProductCard
              key={image[0].image?.id}
              buttonStyle={buttonStyle}
              categoryButtonColor={categoryButtonColor}
              cta={cta}
              ctas={ctas}
              ctaDropdownList={ctaDropdownList}
              customCtaDropdownButtonStyles={customCtaDropdownButtonStyles}
              productCardCtaButtonStyles={productCardCtaButtonStyles}
              customCtaDropdownItemsStyles={customCtaDropdownItemsStyles}
              customDropdownListWrapperStyles={customDropdownListWrapperStyles}
              customCtaStyles={customCtaStyles}
              customStyles={customStyles}
              image={image}
              isCtaConfigurable={isCtaConfigurable}
              onResize={setHeight}
              productCardRichTextAlign={productCardRichTextAlign}
              rte={rte}
              scalableText={scalableText}
              spaceAboveCardRichText={spaceAboveCardRichText}
              spaceAboveCtaDropdown={spaceAboveCtaDropdown}
              url={url}
              variant={variant}
              customGapCss={customGapCss}
            />
          ))}
        </CMSMarketingCarousel>
      </ProductsWrapper>
    );
  }

  return useCustomLayout ? (
    <ProductCardsMap />
  ) : (
    <ProductsWrapper
      customBasicStyles={customBasicStyles}
      customExposedStyles={customExposedStyles}
      customScrollStyles={{
        ...customScrollStyles,
        ...(Object.keys(customGapCss).length > 0 &&
          isDesktop && {
            margin: 0,
            padding: 0,
            display: 'flex',
            alignItems: 'flex-start',
            gap: '30px',
            width: 'calc(100vw - 60px)',
            overflowX: 'scroll',
          }),
      }}
      gapBetweenCardsDesktop={gapBetweenCardsDesktop}
      numberOfCards={categories.length}
      variant={variant}
    >
      <ProductCardsMap />
    </ProductsWrapper>
  );
};

export default ProductCards;
