// @ts-nocheck
'use client';
import React from 'react';
import { styled } from '@ecom-next/core/react-stitch';
import { Link } from '@ecom-next/core/migration/link';
import { SpotlightImageImageProps } from './types';
import { useViewportIsLarge } from '../../../hooks';
import BackgroundTypeContainer from '../../subcomponents/BackgroundTypeContainer';
import { imageOverlaysContentExists, SpotlightImageOverlays } from './SpotlightImageOverlays';
import { getInsetPadding } from '../../global/constants';
/**
 * @deprecated  This content type is deprecated and will be removed in a future release.
 * - Reason: Athleta no longer uses SpotlightImage. Do not use in new development.
 * - See https://gapinc.atlassian.net/browse/FUI-5434 for removal tracking.
 */
export const SpotlightImageImage = (props: SpotlightImageImageProps): JSX.Element => {
  const isLargeViewport = useViewportIsLarge();
  const { image, imageOverlays, isFullBleed, desktopScalingPoint } = props;
  const { mainImage, mobileImageOverride, link } = image;

  const useMobileOverrides = !isLargeViewport && !!mobileImageOverride;
  const renderedImage = useMobileOverrides ? mobileImageOverride : mainImage;

  const SpotlightImageImageWrap = styled.div({
    padding: !isFullBleed ? getInsetPadding(isLargeViewport) : 'unset',
  });
  const mobileAspectRatio = '9 / 16';
  const desktopAspectRatio = '16 / 9';
  const aspectRatio = isLargeViewport ? desktopAspectRatio : mobileAspectRatio;

  const overlayContent = imageOverlays && imageOverlaysContentExists(imageOverlays) && (
    <SpotlightImageOverlays
      desktopScalingPoint={desktopScalingPoint}
      detailsLink={imageOverlays?.detailsLink}
      handle={imageOverlays.handle}
      isFullBleed={isFullBleed}
      useGradientBackfill={imageOverlays.useGradientBackfill}
    />
  );

  const GradientBackfillContainer = styled.div({
    alignItems: 'end',
    display: 'flex',
    width: '100%',
  });

  const overlayWithGradientAndContent = imageOverlays?.useGradientBackfill ? (
    <GradientBackfillContainer>{overlayContent}</GradientBackfillContainer>
  ) : (
    overlayContent
  );

  return (
    <SpotlightImageImageWrap>
      {link?.value ? (
        <Link aria-label={link.label} to={link.value}>
          <BackgroundTypeContainer
            aspectRatio={aspectRatio}
            background={{ type: 'image', images: renderedImage }}
            css={{ aspectRatio }}
            isDesktop={isLargeViewport}
          />
          {overlayWithGradientAndContent}
        </Link>
      ) : (
        <>
          <BackgroundTypeContainer
            aspectRatio={aspectRatio}
            background={{ type: 'image', images: renderedImage }}
            css={{ aspectRatio }}
            isDesktop={isLargeViewport}
          />
          {overlayWithGradientAndContent}
        </>
      )}
    </SpotlightImageImageWrap>
  );
};
