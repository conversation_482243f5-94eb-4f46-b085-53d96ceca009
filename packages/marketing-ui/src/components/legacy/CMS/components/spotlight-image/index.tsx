// @ts-nocheck
'use client';
import React, { useEffect, useCallback, useRef, useState } from 'react';
import { SpotlightImageContentType } from '../../content-types/SpotlightImage/types';
import { SpotlightImageWrapper } from './SpotlightImageWrapper';
import { SpotlightImageImage } from './SpotlightImageImage';
import { SpotlightImageHeader } from './SpotlightImageHeader';
import { imageOverlaysContentExists, SpotlightImageOverlays } from './SpotlightImageOverlays';
import type { ScalableText } from '../../../components/Typography';
import { hasContent } from '../../helpers/SpotlightContent';
import useDebouncedWindowResize from '../../../helper/hooks/useDebouncedWindowResize';
/**
 * @deprecated  This content type is deprecated and will be removed in a future release.
 * - Reason: Athleta no longer uses SpotlightImage. Do not use in new development.
 * - See https://gapinc.atlassian.net/browse/FUI-5434 for removal tracking.
 */
export const SpotlightImage = ({
  content,
  general,
  image,
  imageOverlays,
  desktopScalingPoint,
}: Omit<SpotlightImageContentType, '_meta'> & {
  desktopScalingPoint: ScalableText['desktopScalingPoint'];
}): JSX.Element => {
  const overlaysRef = useRef<HTMLDivElement | null>(null);
  const [overlaysHeight, setOverlaysHeight] = useState(0);
  const { layout, background, mobileBackground } = general;
  const isFullBleed = layout === 'fullBleed';
  const currentOverlaysHeight = () => overlaysRef.current?.children[0]?.clientHeight;
  const hasOverlaysContent = !!imageOverlays?.handle?.text || !!imageOverlays?.detailsLink;

  const updateOverlaysHeight = useCallback(() => {
    const height = currentOverlaysHeight();
    typeof height === 'number' && setOverlaysHeight(height);
  }, [setOverlaysHeight]);
  /* This adjusts the position of the header content since it has `position: absolute`.
   * But we only need it in the case of fullBleed, when the verticalAlignment is `bottom`,
   *  _and_ when there is any overlay content authored.
   * Since the overlay content scales, we need to watch the window size too.
   */
  useEffect(() => {
    updateOverlaysHeight();
  }, [updateOverlaysHeight]); // check once after first render
  useDebouncedWindowResize(() => (isFullBleed && hasOverlaysContent && content.verticalAlignment === 'bottom' ? updateOverlaysHeight() : null), 250);

  return (
    <SpotlightImageWrapper background={background} hasContent={hasContent(content)} isFullBleed={isFullBleed} mobileBackground={mobileBackground}>
      <SpotlightImageHeader
        content={content}
        desktopScalingPoint={desktopScalingPoint}
        general={general}
        isFullBleed={isFullBleed}
        overlaysHeight={overlaysHeight}
      />
      <SpotlightImageImage desktopScalingPoint={desktopScalingPoint} image={image} imageOverlays={imageOverlays} isFullBleed={isFullBleed} />
      {isFullBleed && imageOverlays && imageOverlaysContentExists(imageOverlays) && (
        <div ref={overlaysRef} css={{ height: '100%' }}>
          <SpotlightImageOverlays
            desktopScalingPoint={desktopScalingPoint}
            detailsLink={imageOverlays?.detailsLink}
            handle={imageOverlays?.handle}
            isFullBleed={isFullBleed}
            useGradientBackfill={imageOverlays?.useGradientBackfill}
          />
        </div>
      )}
    </SpotlightImageWrapper>
  );
};
