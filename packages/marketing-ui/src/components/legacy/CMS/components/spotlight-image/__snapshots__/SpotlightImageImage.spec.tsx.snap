// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SpotlightImageImage Inset Variant should match snapshots with overlay content 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0 20px 20px 20px;
}

.emotion-1 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-2 {
  width: 100%;
  aspect-ratio: 16/9;
  object-fit: cover;
}

.emotion-3 {
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
}

.emotion-4 {
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  gap: 10px;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  position: absolute;
  padding: 0 15px 10px 15px;
  -webkit-flex-direction: row-reverse;
  -ms-flex-direction: row-reverse;
  flex-direction: row-reverse;
  bottom: 20px;
  width: calc(100% - 40px);
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0) 100%);
}

.emotion-5 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
}

.emotion-6 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 0;
}

.emotion-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
  text-transform: none;
  font-weight: normal;
  min-height: auto;
  font-size: 12px;
}

.emotion-8 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  min-height: auto;
}

.emotion-8:focus {
  outline: none;
}

.emotion-8 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-8 span span {
  padding-left: initial;
}

.emotion-8:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-8:focus-visible {
  outline: auto;
}

.emotion-10 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-10 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-10 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-10 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-10 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.9375vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-10 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-10 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-10 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.9375vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.9375vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.9375vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.9375vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.5625vw);
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.40625vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.25vw);
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.09375vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.9375vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 3.90625vw);
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 3.125vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.734375vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 9.375vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 8.59375vw);
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 7.8125vw);
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 7.8125vw);
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.25vw);
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 5.46875vw);
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.6875vw);
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, 8.90625vw);
  line-height: 1;
  letter-spacing: -0.03125vw;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, 6.5625vw);
  line-height: 1;
  letter-spacing: -0.03125vw;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, 5vw);
  line-height: 1;
  letter-spacing: -0.03125vw;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, 2.65625vw);
  line-height: 1;
  letter-spacing: -0.03125vw;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 1.25vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 1.25vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 1.25vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 7.8125vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-10 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 6.25vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-10 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 1.25vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-10 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 2.34375vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.953125vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.5625vw);
  line-height: 1.1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
        data-testid="product-card-image"
      >
        <img
          alt="Desktop Image"
          class="emotion-2"
          src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/981470_082_BRWH_AT_WOMENS_C69_SU22_Studio_1_1751?fmt=webp"
        />
      </div>
      <div
        class="emotion-3"
      >
        <div
          class="emotion-4"
        >
          <div
            class="emotion-5"
          >
            <div
              class="emotion-6"
            >
              <span
                class="emotion-7"
              >
                prefix
              </span>
            </div>
            <button
              class="emotion-8"
            >
              Details
            </button>
          </div>
          <div
            class="emotion-5"
          >
            <div
              class="emotion-10"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Image overlay text
                  </span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;
