// @ts-nocheck
import React from 'react';
import { queryByLabelText, render, act } from 'test-utils';
import { SMALL, LARGE } from '@ecom-next/core/breakpoint-provider';
import { SpotlightImageImage } from './SpotlightImageImage';
import { SpotlightImageImageProps } from './types';

const testData: SpotlightImageImageProps = {
  image: {
    mainImage: [
      {
        image: {
          _meta: {
            schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
          },
          id: '5f373131-0298-45bf-acc0-bd80db78650c',
          name: '981470_082_BRWH_AT_WOMENS_C69_SU22_Studio_1_1751',
          endpoint: 'athleta',
          defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
        },
        altText: 'Desktop Image',
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
        fliph: false,
        flipv: false,
        enableChroma: false,
        chromaQuality: 80,
      },
    ],
    mobileImageOverride: [
      {
        image: {
          _meta: {
            schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
          },
          id: '5f373131-0298-45bf-acc0-bd80db78650c',
          name: '981470_082_BRWH_AT_WOMENS_C69_SU22_Studio_1_1751',
          endpoint: 'athleta',
          defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
        },
        altText: 'Mobile Image',
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
        fliph: false,
        flipv: false,
        enableChroma: false,
        chromaQuality: 80,
      },
    ],
  },
  isFullBleed: false,
};

const insetWithOverlayData: SpotlightImageImageProps = {
  image: {
    mainImage: [
      {
        image: {
          _meta: {
            schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
          },
          id: '5f373131-0298-45bf-acc0-bd80db78650c',
          name: '981470_082_BRWH_AT_WOMENS_C69_SU22_Studio_1_1751',
          endpoint: 'athleta',
          defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
        },
        altText: 'Desktop Image',
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
        fliph: false,
        flipv: false,
        enableChroma: false,
        chromaQuality: 80,
      },
    ],
    mobileImageOverride: [
      {
        image: {
          _meta: {
            schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
          },
          id: '5f373131-0298-45bf-acc0-bd80db78650c',
          name: '981470_082_BRWH_AT_WOMENS_C69_SU22_Studio_1_1751',
          endpoint: 'athleta',
          defaultHost: '2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io',
        },
        altText: 'Mobile Image',
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
        fliph: false,
        flipv: false,
        enableChroma: false,
        chromaQuality: 80,
      },
    ],
  },
  imageOverlays: {
    handle: {
      placement: 'left',
      text: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Image overlay text</span></p>',
    },
    detailsLink: {
      label: 'Details',
      fontColor: '#f50000',
      prefixLabel: 'prefix',
    },
    useGradientBackfill: true,
  },
  isFullBleed: false,
};

describe('SpotlightImageImage', () => {
  beforeEach(() => {
    console.error = jest.fn();
  });
  describe('Inset Variant', () => {
    it('should match snapshots with overlay content', () => {
      const result = render(<SpotlightImageImage {...insetWithOverlayData} />);
      expect(result.asFragment()).toMatchSnapshot();
    });
    xit('Renders mobileImage given hasMobileOverrides and viewport is not large', () => {
      const container = render(<SpotlightImageImage {...testData} />, {
        breakpoint: SMALL,
      });
      const imageTextsToAssert = container.queryAllByText('Mobile Image');
      expect(imageTextsToAssert[0]).toBeTruthy();
    });

    describe('main image', () => {
      describe('given Large Viewport and no mobileOverrides', () => {
        it('renders as expected', () => {
          const newTestData = {
            ...testData,
            image: {
              ...testData.image,
              mobileImageOverride: undefined,
            },
          };
          const { getByAltText } = render(<SpotlightImageImage {...newTestData} />, {
            breakpoint: LARGE,
          });
          expect(getByAltText('Desktop Image')).toBeInTheDocument();
        });

        it('should have a landscape aspect ratio', () => {
          const newTestData = {
            ...testData,
            image: {
              ...testData.image,
              mobileImageOverride: undefined,
            },
          };
          const { getByAltText } = render(<SpotlightImageImage {...newTestData} />, {
            breakpoint: LARGE,
          });
          expect(getByAltText(newTestData.image.mainImage[0].altText!)).toHaveStyleRule('aspect-ratio', '16/9');
        });
      });

      describe('given Large Viewport and no mobileOverrides and link data', () => {
        it('renders as expected', () => {
          const newTestData = {
            ...testData,
            image: {
              ...testData.image,
              link: {
                label: 'Spotlight Link Url',
                value: 'SpotlightLinkUrl.com',
              },
            },
          };
          const { getByAltText } = render(<SpotlightImageImage {...newTestData} />, {
            breakpoint: LARGE,
          });
          expect(getByAltText('Desktop Image')).toBeInTheDocument();
        });

        it('should have the link wrapper', () => {
          const newTestData = {
            ...testData,
            image: {
              ...testData.image,
              link: {
                label: 'Spotlight Link Url',
                value: 'SpotlightLinkUrl.com',
              },
            },
          };
          const { getByLabelText } = render(<SpotlightImageImage {...newTestData} />, {
            breakpoint: LARGE,
          });
          expect(getByLabelText('Spotlight Link Url')).toBeInTheDocument();
        });
      });

      describe('given small Viewport and no mobileOverrides', () => {
        it('Renders mainImage as expected', () => {
          const newTestData = {
            ...testData,
            image: {
              ...testData.image,
              mobileImageOverride: undefined,
            },
          };
          const { getByAltText } = render(<SpotlightImageImage {...newTestData} />, {
            breakpoint: SMALL,
          });
          expect(getByAltText('Desktop Image')).toBeInTheDocument();
        });
        it('should have a portrait aspect ratio', () => {
          const newTestData = {
            ...testData,
            image: {
              ...testData.image,
              mobileImageOverride: undefined,
            },
          };
          const { getByAltText } = render(<SpotlightImageImage {...newTestData} />, {
            breakpoint: SMALL,
          });
          expect(getByAltText(newTestData.image.mainImage[0].altText!)).toHaveStyleRule('aspect-ratio', '9/16');
        });
      });

      it('Renders mainImage given Large Viewport and mobileOverrides', () => {
        const { getByAltText } = render(<SpotlightImageImage {...testData} />, {
          breakpoint: LARGE,
        });
        expect(getByAltText('Desktop Image')).toBeInTheDocument();
      });
    });

    it('Renders mainImage given Large Viewport and mobileOverrides', () => {
      const { getByAltText } = render(<SpotlightImageImage {...testData} />, {
        breakpoint: LARGE,
      });
      expect(getByAltText('Desktop Image')).toBeInTheDocument();
    });
    it('should have a portrait aspect ratio given a small viewport and mobile overrides', () => {
      const { getByAltText } = render(<SpotlightImageImage {...testData} />, {
        breakpoint: SMALL,
      });
      expect(getByAltText(testData.image.mobileImageOverride![0].altText!)).toHaveStyleRule('aspect-ratio', '9/16');
    });
  });
});
