// @ts-nocheck
'use client';
import React from 'react';
import { CSSObject, styled, useTheme } from '@ecom-next/core/react-stitch';
import { SpotlightImageImageOverlaysProps } from './types';
import { DetailsButton, getDetailsContent } from '../../subcomponents/Details';
import { RichText } from '../../subcomponents/RichText';
import { footerGradientBackfillStyles } from '../../helpers/CSSHelpers';
import { INSET_PADDING_MOBILE, INSET_PADDING_DESKTOP } from '../../global/constants';
import { useViewportIsLarge } from '../../../hooks/useViewportIsLarge';
/**
 * @deprecated  This content type is deprecated and will be removed in a future release.
 * - Reason: Athleta no longer uses SpotlightImage. Do not use in new development.
 * - See https://gapinc.atlassian.net/browse/FUI-5434 for removal tracking.
 */
export const imageOverlaysContentExists = (imageOverlays: SpotlightImageImageOverlaysProps) =>
  imageOverlays?.detailsLink?.label || imageOverlays?.detailsLink?.prefixLabel || imageOverlays?.handle?.text;

export const SpotlightImageOverlays = ({
  handle,
  detailsLink,
  isFullBleed,
  className,
  useGradientBackfill,
  isVideo,
  desktopScalingPoint,
}: SpotlightImageImageOverlaysProps): JSX.Element => {
  const isLargeViewport: boolean = useViewportIsLarge();
  const theme = useTheme();

  const detailsContent = getDetailsContent(theme.brand, detailsLink?.pemoleCode, detailsLink?.htmlModalUrl);

  const isPlacementRight = handle?.placement === 'right';

  const placementRightStyles: CSSObject = isPlacementRight
    ? {
        flexDirection: 'column-reverse',
        alignItems: 'end',
      }
    : {};

  const flexDirection: CSSObject = !detailsLink && !isPlacementRight ? {} : { flexDirection: 'row-reverse' };

  const insetPadding = isLargeViewport ? INSET_PADDING_DESKTOP : INSET_PADDING_MOBILE;

  const layoutPositioning: CSSObject = isFullBleed
    ? {
        bottom: 0,
        width: '100%',
      }
    : {
        bottom: isVideo ? 0 : insetPadding,
        width: isVideo ? '100%' : `calc(100% - ${insetPadding * 2}px)`,
      };

  const gradientBackfillStyles = useGradientBackfill ? footerGradientBackfillStyles : {};

  const SpotlightImageOverlaysWrapper = styled.div({
    boxSizing: 'border-box',
    display: 'flex',
    alignItems: 'end',
    gap: 10,
    justifyContent: 'space-between',
    position: 'absolute',
    padding: isLargeViewport ? '0 15px 10px 15px' : '0 10px 10px 10px',
    ...flexDirection,
    ...placementRightStyles,
    ...layoutPositioning,
    ...gradientBackfillStyles,
  });

  const DetailsWrapper = styled.div`
    display: inline-flex;
  `;

  const RichTextWrapper = styled.div`
    display: inline-flex;
  `;

  return (
    <SpotlightImageOverlaysWrapper className={className}>
      {detailsLink && (
        <DetailsWrapper>
          <DetailsButton label={detailsLink.label} prefix={detailsLink.prefixLabel} value={detailsContent} />
        </DetailsWrapper>
      )}
      {handle?.text && (
        <RichTextWrapper>
          <RichText
            scalableText={{
              enable: true,
              desktopScalingPoint,
            }}
            text={handle.text}
          />
        </RichTextWrapper>
      )}
    </SpotlightImageOverlaysWrapper>
  );
};
