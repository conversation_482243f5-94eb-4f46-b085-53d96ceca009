// @ts-nocheck
'use client';
import React from 'react';
import { styled } from '@ecom-next/core/react-stitch';
import { useViewportIsLarge } from '../../../hooks';
import { RichText } from '../../subcomponents/RichText';
import { CtaButton } from '../../subcomponents/CTAButton';
import ImageOrIcon from '../../subcomponents/ImageOrIcon';
import { CTAButtonsWrap, SpotlightImageHeaderFullBleedWrap, SpotlightImageHeaderGradientWrap } from './styles';
import {
  SpotlightTextContentProps,
  SpotlightTextContentBaseProps,
  SpotlightImageHeaderWrapProps,
  SpotlightImageContentAlign,
  SpotlightCtaButtonsProp,
} from './types';
import { Size } from '../../../components/ComposableButton/components';
/**
 * @deprecated  This content type is deprecated and will be removed in a future release.
 * - Reason: Athleta no longer uses SpotlightImage. Do not use in new development.
 * - See https://gapinc.atlassian.net/browse/FUI-5434 for removal tracking.
 */
export const SpotlightCTAButtons = (props: SpotlightCtaButtonsProp) => {
  const { ctaButtons } = props;
  const isDesktop = useViewportIsLarge();

  return (
    <CTAButtonsWrap>
      {ctaButtons &&
        ctaButtons.map(cta => (
          <CtaButton
            key={`cta-${cta.cta.label.replace(' ', '-')}-${cta.cta.value}`}
            css={{
              pointerEvents: 'auto',
            }}
            ctaButton={cta.cta}
            ctaButtonStyling={cta.buttonStyle}
            ctaSize={isDesktop ? Size.large : Size.small}
          />
        ))}
    </CTAButtonsWrap>
  );
};

export const SpotlightGradientContent = ({
  children,
  desktopContentJustification,
  mobileContentJustification,
  isDesktop,
}: Omit<SpotlightImageHeaderWrapProps, 'verticalAlignment'>) => (
  <SpotlightImageHeaderFullBleedWrap isDesktop={isDesktop}>
    <SpotlightImageHeaderGradientWrap
      css={{
        alignItems: SpotlightImageContentAlign[(isDesktop ? desktopContentJustification : mobileContentJustification) ?? 'right'],
        justifyContent: desktopContentJustification,
      }}
    >
      {children}
    </SpotlightImageHeaderGradientWrap>
  </SpotlightImageHeaderFullBleedWrap>
);

export const SpotlightTextContent = ({ content, contentJustification, desktopScalingPoint }: SpotlightTextContentProps) => {
  const { defaultText, mobileOverride } = content;

  const isMobile = !useViewportIsLarge();
  const renderedText = isMobile && mobileOverride ? mobileOverride : defaultText;

  return (
    <RichText
      css={{
        textAlign: contentJustification ?? 'left',
        a: { pointerEvents: 'auto' },
      }}
      disableTextAlign={!!contentJustification}
      scalableText={{ enable: true, desktopScalingPoint }}
      text={renderedText}
    />
  );
};

export const SpotlightImageHeaderContent = ({ content, isFullBleed, desktopScalingPoint }: SpotlightTextContentBaseProps) => {
  const { ctaButtons, icon, spotlightText, contentJustification: desktopContentJustification, mobileContentJustification, mobileIcon } = content;
  const { useGradientBackfill, defaultText } = spotlightText;
  const hasCtaButtons = ctaButtons !== undefined;
  const hasSpotlightText = defaultText !== undefined && defaultText.length > 0;
  const isDesktop = useViewportIsLarge();
  const iconImage = isDesktop ? icon?.icon : mobileIcon?.icon || icon?.icon;

  const childFragments = (
    <>
      {iconImage && <ImageOrIcon desktopImageOrIconSize={icon?.iconSize} mobileImageOrIconSize={mobileIcon?.iconSize} src={iconImage![0]} />}
      {hasSpotlightText && (
        <SpotlightTextContent
          content={spotlightText}
          contentJustification={isDesktop ? desktopContentJustification : mobileContentJustification}
          desktopScalingPoint={desktopScalingPoint}
        />
      )}
      {hasCtaButtons && <SpotlightCTAButtons ctaButtons={ctaButtons} isFullBleed={isFullBleed} />}
    </>
  );

  const childFragmentsWithFullBleed = (
    <div css={{ width: '100%' }}>
      <SpotlightImageHeaderFullBleedWrap
        css={{
          alignItems: SpotlightImageContentAlign[isDesktop ? desktopContentJustification : mobileContentJustification],
          justifyContent: isDesktop ? desktopContentJustification : mobileContentJustification,
          gap: 20,
        }}
        isDesktop={isDesktop}
      >
        {childFragments}
      </SpotlightImageHeaderFullBleedWrap>
    </div>
  );

  const HeaderGradientContainer = styled.div({
    background: 'linear-gradient(180deg, rgba(0, 0, 0, 0) -0.33%, rgba(0, 0, 0, 0.4) 43.94%, rgba(0, 0, 0, 0) 88.73%)',
    backgroundBlendMode: 'darken',
    width: '100%',
  });

  if (useGradientBackfill && isFullBleed) {
    return (
      <HeaderGradientContainer className='spotlight-background-gradient'>
        <SpotlightGradientContent
          desktopContentJustification={desktopContentJustification}
          isDesktop={isDesktop}
          isFullBleed={isFullBleed}
          mobileContentJustification={mobileContentJustification}
        >
          {childFragments}
        </SpotlightGradientContent>
      </HeaderGradientContainer>
    );
  }

  return isFullBleed ? childFragmentsWithFullBleed : childFragments;
};
