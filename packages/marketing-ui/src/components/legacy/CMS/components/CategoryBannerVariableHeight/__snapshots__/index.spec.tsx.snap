// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CategoryBannerVariableHeight Component CategoryBannerVariableHeight - Feature Flag: newPlpGridFeatureFlag should match snapshot when newPlpGridFeatureFlag is enabled 1`] = `
.emotion-0 {
  width: auto;
}

.emotion-1 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1280/365;
}

.emotion-2 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-3 {
  aspect-ratio: 1280/365;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  margin: 0 3.75%;
}

.emotion-5 {
  display: block;
  text-align: left;
}

.emotion-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
  display: block;
}

.emotion-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
  display: block;
}

.emotion-8 {
  background: transparent;
  aspect-ratio: 1280/365;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
        height="365"
        width="1280"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-3"
            data-testid="CbVariableHeight"
          >
            <div
              class="emotion-4"
              data-testid="DefaultLayout"
            >
              <div
                class="emotion-5"
              >
                <span
                  class="emotion-6"
                />
                <span
                  class="emotion-7"
                />
              </div>
            </div>
          </div>
          <div
            class="emotion-8"
            height="0"
            width="0"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component bannerCustomStyles should return a URL with the correct height value for gap 1`] = `"https://cdn.media.amplience.net/i/gap/981523_002_MASP_AT_WMN_LS_59_SU21_SW_1_0061?fmt=auto&h=1640"`;

exports[`CategoryBannerVariableHeight Component should match snapshots SubCategoryBannerVariableHeight - Banner Lg It should match the snapshot for at - large banner 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1000px;
}

.emotion-1 {
  width: auto;
  max-height: 464px;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1000/464;
}

.emotion-3 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-4 {
  aspect-ratio: 1000/464;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  margin: 0 6.9%;
}

.emotion-6 {
  display: block;
  text-align: left;
}

.emotion-7 {
  color: #faa;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.328125vw, 17px));
  line-height: 1.588235294117647;
  letter-spacing: min(0.1328125vw, 1.7px);
  font-weight: 700;
  text-transform: uppercase;
  display: block;
}

.emotion-8 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(2.8125vw, 36px));
  line-height: 1;
  letter-spacing: min(0.28125vw, 3.6px);
  text-transform: uppercase;
  font-weight: 600;
  display: block;
}

.emotion-9 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-10 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: 1fr;
  grid-auto-rows: 1fr;
  grid-row-gap: 26px;
  grid-column-gap: 40px;
  margin-top: 30px;
  text-align: left;
  z-index: 2;
  width: 100%;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-11 {
  -webkit-flex: 335px 0 1;
  -ms-flex: 335px 0 1;
  flex: 335px 0 1;
}

.emotion-12 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 15px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 55px;
  line-height: 1.3333333333333333;
  padding: 20px 40px;
  width: 100%;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  height: 100%;
}

.emotion-12:focus {
  outline: none;
}

.emotion-12>span {
  padding: 1px 0;
}

.emotion-12:hover,
.emotion-12:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-12:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
}

.emotion-13 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: end;
  justify-content: end;
  padding-bottom: 10px;
  padding-right: 10px;
  z-index: 2;
}

.emotion-14 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: auto;
  line-height: 1.3125;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  line-height: 16px;
  min-height: initial;
  padding: 0;
  margin-left: unset;
  pointer-events: auto;
  text-underline-offset: 2px;
}

.emotion-14:focus {
  outline: none;
}

.emotion-14>span {
  padding: 1px 0;
}

.emotion-14 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.2rem * 0.72);
}

.emotion-14 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-14 span span {
  padding-left: initial;
}

.emotion-14:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-14:focus-visible {
  outline: auto;
}

.emotion-15 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

.emotion-16 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-17 {
  width: 100%;
  height: 100%;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
        data-testid="sub-category-banner"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
            height="464"
            width="1000"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
                data-testid="CbVariableHeight"
              >
                <div
                  class="emotion-5"
                  data-testid="DefaultLayout"
                >
                  <div
                    class="emotion-6"
                  >
                    <span
                      class="emotion-7"
                    >
                      Eyebrow
                    </span>
                    <span
                      class="emotion-8"
                    >
                      Headline
                    </span>
                  </div>
                  <div
                    class="emotion-9"
                  >
                    <div
                      class="emotion-10"
                    >
                      <div
                        class="emotion-11"
                      >
                        <a
                          class="emotion-12"
                          color="dark"
                          href="https://www.athleta.com"
                        >
                          Shop Now
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-13"
                >
                  <button
                    class="emotion-14"
                    data-testid="cmsISMPartialCta"
                  >
                    DETAILS
                  </button>
                </div>
                <a
                  class="emotion-15"
                  href="www.athleta.com"
                  target="_self"
                  title="Banner Link"
                />
              </div>
              <div
                class="emotion-16"
                data-testid="product-card-image"
              >
                <canvas
                  aria-label="casual"
                  class="emotion-17"
                  role="img"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots SubCategoryBannerVariableHeight - Banner Lg It should match the snapshot for gap - large banner 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1000px;
}

.emotion-1 {
  width: auto;
  max-height: 365px;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1000/365;
}

.emotion-3 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-4 {
  aspect-ratio: 1000/365;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  margin: 0 3%;
}

.emotion-6 {
  display: block;
  text-align: left;
}

.emotion-7 {
  color: #faa;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
  display: block;
}

.emotion-8 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
  display: block;
}

.emotion-9 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-10 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: 1fr;
  grid-auto-rows: 1fr;
  grid-row-gap: 10px;
  grid-column-gap: 10px;
  margin-top: 30px;
  text-align: left;
  z-index: 2;
  width: 100%;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-11 {
  -webkit-flex: 335px 0 1;
  -ms-flex: 335px 0 1;
  flex: 335px 0 1;
}

.emotion-12 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 335px;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 14px;
  padding: 12px;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  text-transform: none;
  height: 100%;
}

.emotion-12:focus {
  outline: none;
}

.emotion-12:hover,
.emotion-12:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-12:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-13 {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 2;
}

.emotion-14 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  min-height: auto;
}

.emotion-14:focus {
  outline: none;
}

.emotion-14 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-14 span span {
  padding-left: initial;
}

.emotion-14:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-14:focus-visible {
  outline: auto;
}

.emotion-15 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

.emotion-16 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-17 {
  width: 100%;
  height: 100%;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
        data-testid="sub-category-banner"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
            height="365"
            width="1000"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
                data-testid="CbVariableHeight"
              >
                <div
                  class="emotion-5"
                  data-testid="DefaultLayout"
                >
                  <div
                    class="emotion-6"
                  >
                    <span
                      class="emotion-7"
                    >
                      Eyebrow
                    </span>
                    <span
                      class="emotion-8"
                    >
                      Headline
                    </span>
                  </div>
                  <div
                    class="emotion-9"
                  >
                    <div
                      class="emotion-10"
                    >
                      <div
                        class="emotion-11"
                      >
                        <a
                          class="emotion-12"
                          color="dark"
                          href="https://www.athleta.com"
                        >
                          Shop Now
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-13"
                >
                  <button
                    class="emotion-14"
                    data-testid="cmsISMPartialCta"
                  >
                    DETAILS
                  </button>
                </div>
                <a
                  class="emotion-15"
                  href="www.athleta.com"
                  target="_self"
                  title="Banner Link"
                />
              </div>
              <div
                class="emotion-16"
                data-testid="product-card-image"
              >
                <canvas
                  aria-label="casual"
                  class="emotion-17"
                  role="img"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots SubCategoryBannerVariableHeight - Banner Lg It should match the snapshot for on - large banner 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1000px;
}

.emotion-1 {
  width: auto;
  max-height: 464px;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1000/464;
}

.emotion-3 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-4 {
  aspect-ratio: 1000/464;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  margin: 0 4.5%;
}

.emotion-6 {
  display: block;
  text-align: left;
}

.emotion-7 {
  color: #faa;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.328125vw, 17px));
  line-height: 1;
  letter-spacing: min(0.053125000000000006vw, 0.68px);
  font-weight: 500;
  display: block;
}

.emotion-8 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.75vw, 48px));
  line-height: 1;
  letter-spacing: min(0.22499999999999998vw, 2.88px);
  font-weight: 700;
  display: block;
}

.emotion-9 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-10 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: 1fr;
  grid-auto-rows: 1fr;
  grid-row-gap: 10px;
  grid-column-gap: 40px;
  margin-top: 30px;
  text-align: left;
  z-index: 2;
  width: 100%;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-11 {
  -webkit-flex: 335px 0 1;
  -ms-flex: 335px 0 1;
  flex: 335px 0 1;
}

.emotion-12 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #003764;
  background: transparent;
  border-color: #003764;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  height: 100%;
  min-height: 100%;
}

.emotion-12:focus {
  outline: none;
}

.emotion-12>span {
  padding: 1px 0;
}

.emotion-12:hover,
.emotion-12:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-12:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #000000;
  background-color: transparent;
  border-color: #000000;
}

.emotion-13 {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 2;
}

.emotion-14 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.05px;
  min-height: auto;
  max-height: auto;
  line-height: 1.2857142857142858;
  padding: 0;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #003764;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  text-transform: capitalize;
  text-transform: capitalize;
  font-weight: normal;
  text-transform: capitalize;
  z-index: 1;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 10px;
  line-height: 12px;
  min-height: 10px;
  letter-spacing: normal;
}

.emotion-14:focus {
  outline: none;
}

.emotion-14>span {
  padding: 1px 0;
}

.emotion-14 span span {
  padding-left: 1px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1rem * 0.7);
}

.emotion-14 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #003764;
}

.emotion-14 span span {
  padding-left: initial;
}

.emotion-14:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-14:focus-visible {
  outline: auto;
}

.emotion-15 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

.emotion-16 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-17 {
  width: 100%;
  height: 100%;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
        data-testid="sub-category-banner"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
            height="464"
            width="1000"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
                data-testid="CbVariableHeight"
              >
                <div
                  class="emotion-5"
                  data-testid="DefaultLayout"
                >
                  <div
                    class="emotion-6"
                  >
                    <span
                      class="emotion-7"
                    >
                      Eyebrow
                    </span>
                    <span
                      class="emotion-8"
                    >
                      Headline
                    </span>
                  </div>
                  <div
                    class="emotion-9"
                  >
                    <div
                      class="emotion-10"
                    >
                      <div
                        class="emotion-11"
                      >
                        <a
                          class="emotion-12"
                          color="dark"
                          href="https://www.athleta.com"
                        >
                          Shop Now
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-13"
                >
                  <button
                    class="emotion-14"
                    data-testid="cmsISMPartialCta"
                  >
                    DETAILS
                  </button>
                </div>
                <a
                  class="emotion-15"
                  href="www.athleta.com"
                  target="_self"
                  title="Banner Link"
                />
              </div>
              <div
                class="emotion-16"
                data-testid="product-card-image"
              >
                <canvas
                  aria-label="casual"
                  class="emotion-17"
                  role="img"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots SubCategoryBannerVariableHeight - Banner Lg at 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1000px;
}

.emotion-1 {
  width: auto;
  max-height: 464px;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1000/464;
}

.emotion-3 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-4 {
  aspect-ratio: 1000/464;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  margin: 0 6.9%;
}

.emotion-6 {
  display: block;
  text-align: left;
}

.emotion-7 {
  color: #faa;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.328125vw, 17px));
  line-height: 1.588235294117647;
  letter-spacing: min(0.1328125vw, 1.7px);
  font-weight: 700;
  text-transform: uppercase;
  display: block;
}

.emotion-8 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(2.8125vw, 36px));
  line-height: 1;
  letter-spacing: min(0.28125vw, 3.6px);
  text-transform: uppercase;
  font-weight: 600;
  display: block;
}

.emotion-9 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-10 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: 1fr;
  grid-auto-rows: 1fr;
  grid-row-gap: 26px;
  grid-column-gap: 40px;
  margin-top: 30px;
  text-align: left;
  z-index: 2;
  width: 100%;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-11 {
  -webkit-flex: 335px 0 1;
  -ms-flex: 335px 0 1;
  flex: 335px 0 1;
}

.emotion-12 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 15px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 55px;
  line-height: 1.3333333333333333;
  padding: 20px 40px;
  width: 100%;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  height: 100%;
}

.emotion-12:focus {
  outline: none;
}

.emotion-12>span {
  padding: 1px 0;
}

.emotion-12:hover,
.emotion-12:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-12:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
}

.emotion-13 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: end;
  justify-content: end;
  padding-bottom: 10px;
  padding-right: 10px;
  z-index: 2;
}

.emotion-14 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: auto;
  line-height: 1.3125;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  line-height: 16px;
  min-height: initial;
  padding: 0;
  margin-left: unset;
  pointer-events: auto;
  text-underline-offset: 2px;
}

.emotion-14:focus {
  outline: none;
}

.emotion-14>span {
  padding: 1px 0;
}

.emotion-14 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.2rem * 0.72);
}

.emotion-14 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-14 span span {
  padding-left: initial;
}

.emotion-14:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-14:focus-visible {
  outline: auto;
}

.emotion-15 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

.emotion-16 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-17 {
  width: 100%;
  height: 100%;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
        data-testid="sub-category-banner"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
            height="464"
            width="1000"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
                data-testid="CbVariableHeight"
              >
                <div
                  class="emotion-5"
                  data-testid="DefaultLayout"
                >
                  <div
                    class="emotion-6"
                  >
                    <span
                      class="emotion-7"
                    >
                      Eyebrow
                    </span>
                    <span
                      class="emotion-8"
                    >
                      Headline
                    </span>
                  </div>
                  <div
                    class="emotion-9"
                  >
                    <div
                      class="emotion-10"
                    >
                      <div
                        class="emotion-11"
                      >
                        <a
                          class="emotion-12"
                          color="dark"
                          href="https://www.athleta.com"
                        >
                          Shop Now
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-13"
                >
                  <button
                    class="emotion-14"
                    data-testid="cmsISMPartialCta"
                  >
                    DETAILS
                  </button>
                </div>
                <a
                  class="emotion-15"
                  href="www.athleta.com"
                  target="_self"
                  title="Banner Link"
                />
              </div>
              <div
                class="emotion-16"
                data-testid="product-card-image"
              >
                <canvas
                  aria-label="casual"
                  class="emotion-17"
                  role="img"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
       
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots SubCategoryBannerVariableHeight - Banner Lg gap 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1000px;
}

.emotion-1 {
  width: auto;
  max-height: 365px;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1000/365;
}

.emotion-3 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-4 {
  aspect-ratio: 1000/365;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  margin: 0 3%;
}

.emotion-6 {
  display: block;
  text-align: left;
}

.emotion-7 {
  color: #faa;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
  display: block;
}

.emotion-8 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
  display: block;
}

.emotion-9 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-10 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: 1fr;
  grid-auto-rows: 1fr;
  grid-row-gap: 10px;
  grid-column-gap: 10px;
  margin-top: 30px;
  text-align: left;
  z-index: 2;
  width: 100%;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-11 {
  -webkit-flex: 335px 0 1;
  -ms-flex: 335px 0 1;
  flex: 335px 0 1;
}

.emotion-12 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 335px;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 14px;
  padding: 12px;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  text-transform: none;
  height: 100%;
}

.emotion-12:focus {
  outline: none;
}

.emotion-12:hover,
.emotion-12:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-12:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-13 {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 2;
}

.emotion-14 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  min-height: auto;
}

.emotion-14:focus {
  outline: none;
}

.emotion-14 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-14 span span {
  padding-left: initial;
}

.emotion-14:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-14:focus-visible {
  outline: auto;
}

.emotion-15 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

.emotion-16 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-17 {
  width: 100%;
  height: 100%;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
        data-testid="sub-category-banner"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
            height="365"
            width="1000"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
                data-testid="CbVariableHeight"
              >
                <div
                  class="emotion-5"
                  data-testid="DefaultLayout"
                >
                  <div
                    class="emotion-6"
                  >
                    <span
                      class="emotion-7"
                    >
                      Eyebrow
                    </span>
                    <span
                      class="emotion-8"
                    >
                      Headline
                    </span>
                  </div>
                  <div
                    class="emotion-9"
                  >
                    <div
                      class="emotion-10"
                    >
                      <div
                        class="emotion-11"
                      >
                        <a
                          class="emotion-12"
                          color="dark"
                          href="https://www.athleta.com"
                        >
                          Shop Now
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-13"
                >
                  <button
                    class="emotion-14"
                    data-testid="cmsISMPartialCta"
                  >
                    DETAILS
                  </button>
                </div>
                <a
                  class="emotion-15"
                  href="www.athleta.com"
                  target="_self"
                  title="Banner Link"
                />
              </div>
              <div
                class="emotion-16"
                data-testid="product-card-image"
              >
                <canvas
                  aria-label="casual"
                  class="emotion-17"
                  role="img"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
       
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots SubCategoryBannerVariableHeight - Banner Lg on 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1000px;
}

.emotion-1 {
  width: auto;
  max-height: 464px;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1000/464;
}

.emotion-3 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-4 {
  aspect-ratio: 1000/464;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  margin: 0 4.5%;
}

.emotion-6 {
  display: block;
  text-align: left;
}

.emotion-7 {
  color: #faa;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.328125vw, 17px));
  line-height: 1;
  letter-spacing: min(0.053125000000000006vw, 0.68px);
  font-weight: 500;
  display: block;
}

.emotion-8 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.75vw, 48px));
  line-height: 1;
  letter-spacing: min(0.22499999999999998vw, 2.88px);
  font-weight: 700;
  display: block;
}

.emotion-9 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-10 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: 1fr;
  grid-auto-rows: 1fr;
  grid-row-gap: 10px;
  grid-column-gap: 40px;
  margin-top: 30px;
  text-align: left;
  z-index: 2;
  width: 100%;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-11 {
  -webkit-flex: 335px 0 1;
  -ms-flex: 335px 0 1;
  flex: 335px 0 1;
}

.emotion-12 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #003764;
  background: transparent;
  border-color: #003764;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  height: 100%;
  min-height: 100%;
}

.emotion-12:focus {
  outline: none;
}

.emotion-12>span {
  padding: 1px 0;
}

.emotion-12:hover,
.emotion-12:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-12:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #000000;
  background-color: transparent;
  border-color: #000000;
}

.emotion-13 {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 2;
}

.emotion-14 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.05px;
  min-height: auto;
  max-height: auto;
  line-height: 1.2857142857142858;
  padding: 0;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #003764;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  text-transform: capitalize;
  text-transform: capitalize;
  font-weight: normal;
  text-transform: capitalize;
  z-index: 1;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 10px;
  line-height: 12px;
  min-height: 10px;
  letter-spacing: normal;
}

.emotion-14:focus {
  outline: none;
}

.emotion-14>span {
  padding: 1px 0;
}

.emotion-14 span span {
  padding-left: 1px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1rem * 0.7);
}

.emotion-14 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #003764;
}

.emotion-14 span span {
  padding-left: initial;
}

.emotion-14:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-14:focus-visible {
  outline: auto;
}

.emotion-15 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

.emotion-16 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-17 {
  width: 100%;
  height: 100%;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
        data-testid="sub-category-banner"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
            height="464"
            width="1000"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
                data-testid="CbVariableHeight"
              >
                <div
                  class="emotion-5"
                  data-testid="DefaultLayout"
                >
                  <div
                    class="emotion-6"
                  >
                    <span
                      class="emotion-7"
                    >
                      Eyebrow
                    </span>
                    <span
                      class="emotion-8"
                    >
                      Headline
                    </span>
                  </div>
                  <div
                    class="emotion-9"
                  >
                    <div
                      class="emotion-10"
                    >
                      <div
                        class="emotion-11"
                      >
                        <a
                          class="emotion-12"
                          color="dark"
                          href="https://www.athleta.com"
                        >
                          Shop Now
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-13"
                >
                  <button
                    class="emotion-14"
                    data-testid="cmsISMPartialCta"
                  >
                    DETAILS
                  </button>
                </div>
                <a
                  class="emotion-15"
                  href="www.athleta.com"
                  target="_self"
                  title="Banner Link"
                />
              </div>
              <div
                class="emotion-16"
                data-testid="product-card-image"
              >
                <canvas
                  aria-label="casual"
                  class="emotion-17"
                  role="img"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
       
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots SubCategoryBannerVariableHeight - Banner SM It should match the snapshot for at - small banner 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1000px;
}

.emotion-1 {
  width: auto;
  max-height: 240px;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1000/240;
}

.emotion-3 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-4 {
  aspect-ratio: 1000/240;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  margin: 0 6.9%;
}

.emotion-6 {
  display: block;
  text-align: left;
}

.emotion-7 {
  color: #faa;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.328125vw, 17px));
  line-height: 1.588235294117647;
  letter-spacing: min(0.1328125vw, 1.7px);
  font-weight: 700;
  text-transform: uppercase;
  display: block;
}

.emotion-8 {
  color: #FFFFFF;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(2.8125vw, 36px));
  line-height: 1;
  letter-spacing: min(0.28125vw, 3.6px);
  text-transform: uppercase;
  font-weight: 600;
  display: block;
}

.emotion-9 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-10 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(1, auto);
  grid-auto-rows: 1fr;
  grid-row-gap: 26px;
  grid-column-gap: 40px;
  margin-top: 30px;
  text-align: left;
  z-index: 2;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-12 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 15px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: auto;
  line-height: 1.7333333333333334;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #FFFFFF;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 5px;
  text-decoration-thickness: 1px;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  height: 100%;
  text-align: left;
}

.emotion-12:focus {
  outline: none;
}

.emotion-12>span {
  padding: 1px 0;
}

.emotion-12:hover,
.emotion-12:focus {
  text-shadow: 0 0 2px currentColor;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-12:active {
  text-transform: uppercase;
  text-shadow: none;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-13 {
  box-sizing: border-box;
}

.emotion-14 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: end;
  justify-content: end;
  padding-bottom: 10px;
  padding-right: 10px;
  z-index: 2;
}

.emotion-15 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: auto;
  line-height: 1.3125;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #faa;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  line-height: 16px;
  min-height: initial;
  padding: 0;
  margin-left: unset;
  pointer-events: auto;
  text-underline-offset: 2px;
}

.emotion-15:focus {
  outline: none;
}

.emotion-15>span {
  padding: 1px 0;
}

.emotion-15 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.2rem * 0.72);
}

.emotion-15 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-15 span span {
  padding-left: initial;
}

.emotion-15:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-15:focus-visible {
  outline: auto;
}

.emotion-16 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

.emotion-17 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-18 {
  width: 100%;
  height: 100%;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
        data-testid="sub-category-banner"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
            height="240"
            width="1000"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
                data-testid="CbVariableHeight"
              >
                <div
                  class="emotion-5"
                  data-testid="DefaultLayout"
                >
                  <div
                    class="emotion-6"
                  >
                    <span
                      class="emotion-7"
                    >
                      Eyebrow
                    </span>
                    <span
                      class="emotion-8"
                    >
                      Headline
                    </span>
                  </div>
                  <div
                    class="emotion-9"
                  >
                    <div
                      class="emotion-10"
                    >
                      <div
                        class="emotion-11"
                      >
                        <a
                          class="emotion-12"
                          color="light"
                          href="https://www.athleta.com"
                        >
                          <span
                            class="emotion-13"
                          >
                            Shop Now
                          </span>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-14"
                >
                  <button
                    class="emotion-15"
                    data-testid="cmsISMPartialCta"
                  >
                    DETAILS
                  </button>
                </div>
                <a
                  class="emotion-16"
                  href="www.athleta.com"
                  target="_self"
                  title="Banner Link"
                />
              </div>
              <div
                class="emotion-17"
                data-testid="product-card-image"
              >
                <canvas
                  aria-label=""
                  class="emotion-18"
                  role="img"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots SubCategoryBannerVariableHeight - Banner SM It should match the snapshot for gap - small banner 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1000px;
}

.emotion-1 {
  width: auto;
  max-height: 115px;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1000/115;
}

.emotion-3 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-4 {
  aspect-ratio: 1000/115;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  margin: 0 3%;
}

.emotion-6 {
  display: block;
  text-align: left;
}

.emotion-7 {
  color: #faa;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
  display: block;
}

.emotion-8 {
  color: #FFFFFF;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
  display: block;
}

.emotion-9 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-10 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(1, auto);
  grid-auto-rows: 1fr;
  grid-row-gap: 10px;
  grid-column-gap: 40px;
  margin-top: 30px;
  text-align: left;
  z-index: 2;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-12 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 335px;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #FFFFFF;
  height: auto;
  padding-bottom: 1.8px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 1.8px;
  text-decoration-thickness: 2px;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  text-transform: none;
  height: 100%;
  text-align: left;
}

.emotion-12:focus {
  outline: none;
}

.emotion-12:hover,
.emotion-12:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-12:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-13 {
  box-sizing: border-box;
}

.emotion-14 {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 2;
}

.emotion-15 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #faa;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  min-height: auto;
}

.emotion-15:focus {
  outline: none;
}

.emotion-15 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-15 span span {
  padding-left: initial;
}

.emotion-15:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-15:focus-visible {
  outline: auto;
}

.emotion-16 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

.emotion-17 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-18 {
  width: 100%;
  height: 100%;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
        data-testid="sub-category-banner"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
            height="115"
            width="1000"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
                data-testid="CbVariableHeight"
              >
                <div
                  class="emotion-5"
                  data-testid="DefaultLayout"
                >
                  <div
                    class="emotion-6"
                  >
                    <span
                      class="emotion-7"
                    >
                      Eyebrow
                    </span>
                    <span
                      class="emotion-8"
                    >
                      Headline
                    </span>
                  </div>
                  <div
                    class="emotion-9"
                  >
                    <div
                      class="emotion-10"
                    >
                      <div
                        class="emotion-11"
                      >
                        <a
                          class="emotion-12"
                          color="light"
                          href="https://www.athleta.com"
                        >
                          <span
                            class="emotion-13"
                          >
                            Shop Now
                          </span>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-14"
                >
                  <button
                    class="emotion-15"
                    data-testid="cmsISMPartialCta"
                  >
                    DETAILS
                  </button>
                </div>
                <a
                  class="emotion-16"
                  href="www.athleta.com"
                  target="_self"
                  title="Banner Link"
                />
              </div>
              <div
                class="emotion-17"
                data-testid="product-card-image"
              >
                <canvas
                  aria-label=""
                  class="emotion-18"
                  role="img"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots SubCategoryBannerVariableHeight - Banner SM It should match the snapshot for on - small banner 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1000px;
}

.emotion-1 {
  width: auto;
  max-height: 240px;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1000/240;
}

.emotion-3 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-4 {
  aspect-ratio: 1000/240;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  margin: 0 4.5%;
}

.emotion-6 {
  display: block;
  text-align: left;
}

.emotion-7 {
  color: #faa;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.328125vw, 17px));
  line-height: 1;
  letter-spacing: min(0.053125000000000006vw, 0.68px);
  font-weight: 500;
  display: block;
}

.emotion-8 {
  color: #FFFFFF;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.75vw, 48px));
  line-height: 1;
  letter-spacing: min(0.22499999999999998vw, 2.88px);
  font-weight: 700;
  display: block;
}

.emotion-9 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-10 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(1, auto);
  grid-auto-rows: 1fr;
  grid-row-gap: 10px;
  grid-column-gap: 40px;
  margin-top: 30px;
  text-align: left;
  z-index: 2;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-12 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.05px;
  min-height: auto;
  max-height: auto;
  line-height: 1.8571428571428572;
  padding: 0;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #FFFFFF;
  height: auto;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 3.5px;
  text-decoration-thickness: 3px;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  height: 100%;
  text-align: left;
  min-height: 100%;
}

.emotion-12:focus {
  outline: none;
}

.emotion-12>span {
  padding: 1px 0;
}

.emotion-12:hover,
.emotion-12:focus {
  text-decoration-color: #FFFFFF;
}

.emotion-12:hover span,
.emotion-12:focus span {
  color: #003764;
  background-color: #FFFFFF;
}

.emotion-12:active {
  text-decoration-color: #FFFFFF;
}

.emotion-12:active>span {
  color: #000000;
  background-color: #FFFFFF;
}

.emotion-13 {
  box-sizing: border-box;
}

.emotion-14 {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 2;
}

.emotion-15 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.05px;
  min-height: auto;
  max-height: auto;
  line-height: 1.2857142857142858;
  padding: 0;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #003764;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  text-transform: capitalize;
  text-transform: capitalize;
  font-weight: normal;
  text-transform: capitalize;
  z-index: 1;
  color: #faa;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 10px;
  line-height: 12px;
  min-height: 10px;
  letter-spacing: normal;
}

.emotion-15:focus {
  outline: none;
}

.emotion-15>span {
  padding: 1px 0;
}

.emotion-15 span span {
  padding-left: 1px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1rem * 0.7);
}

.emotion-15 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #003764;
}

.emotion-15 span span {
  padding-left: initial;
}

.emotion-15:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-15:focus-visible {
  outline: auto;
}

.emotion-16 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

.emotion-17 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-18 {
  width: 100%;
  height: 100%;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
        data-testid="sub-category-banner"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
            height="240"
            width="1000"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
                data-testid="CbVariableHeight"
              >
                <div
                  class="emotion-5"
                  data-testid="DefaultLayout"
                >
                  <div
                    class="emotion-6"
                  >
                    <span
                      class="emotion-7"
                    >
                      Eyebrow
                    </span>
                    <span
                      class="emotion-8"
                    >
                      Headline
                    </span>
                  </div>
                  <div
                    class="emotion-9"
                  >
                    <div
                      class="emotion-10"
                    >
                      <div
                        class="emotion-11"
                      >
                        <a
                          class="emotion-12"
                          color="light"
                          href="https://www.athleta.com"
                        >
                          <span
                            class="emotion-13"
                          >
                            Shop Now
                          </span>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-14"
                >
                  <button
                    class="emotion-15"
                    data-testid="cmsISMPartialCta"
                  >
                    DETAILS
                  </button>
                </div>
                <a
                  class="emotion-16"
                  href="www.athleta.com"
                  target="_self"
                  title="Banner Link"
                />
              </div>
              <div
                class="emotion-17"
                data-testid="product-card-image"
              >
                <canvas
                  aria-label=""
                  class="emotion-18"
                  role="img"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots SubCategoryBannerVariableHeight - Banner md It should match the snapshot for at - medium banner 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1000px;
}

.emotion-1 {
  width: auto;
  max-height: 300px;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1000/300;
}

.emotion-3 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-4 {
  aspect-ratio: 1000/300;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  margin: 0 6.9%;
}

.emotion-6 {
  display: block;
  text-align: right;
}

.emotion-7 {
  color: #faa;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.328125vw, 17px));
  line-height: 1.588235294117647;
  letter-spacing: min(0.1328125vw, 1.7px);
  font-weight: 700;
  text-transform: uppercase;
  display: block;
}

.emotion-8 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(2.8125vw, 36px));
  line-height: 1;
  letter-spacing: min(0.28125vw, 3.6px);
  text-transform: uppercase;
  font-weight: 600;
  display: block;
}

.emotion-9 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-10 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: 1fr;
  grid-auto-rows: 1fr;
  grid-row-gap: 26px;
  grid-column-gap: 40px;
  margin-top: 30px;
  text-align: left;
  z-index: 2;
  width: 100%;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-11 {
  -webkit-flex: 335px 0 1;
  -ms-flex: 335px 0 1;
  flex: 335px 0 1;
}

.emotion-12 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 15px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 55px;
  line-height: 1.3333333333333333;
  padding: 20px 40px;
  width: 100%;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  height: 100%;
}

.emotion-12:focus {
  outline: none;
}

.emotion-12>span {
  padding: 1px 0;
}

.emotion-12:hover,
.emotion-12:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-12:active {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-13 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: end;
  justify-content: end;
  padding-bottom: 10px;
  padding-right: 10px;
  z-index: 2;
}

.emotion-14 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: auto;
  line-height: 1.3125;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  line-height: 16px;
  min-height: initial;
  padding: 0;
  margin-left: unset;
  pointer-events: auto;
  text-underline-offset: 2px;
}

.emotion-14:focus {
  outline: none;
}

.emotion-14>span {
  padding: 1px 0;
}

.emotion-14 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.2rem * 0.72);
}

.emotion-14 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-14 span span {
  padding-left: initial;
}

.emotion-14:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-14:focus-visible {
  outline: auto;
}

.emotion-15 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

.emotion-16 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-17 {
  width: 100%;
  height: 100%;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
        data-testid="sub-category-banner"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
            height="300"
            width="1000"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
                data-testid="CbVariableHeight"
              >
                <div
                  class="emotion-5"
                  data-testid="DefaultLayout"
                >
                  <div
                    class="emotion-6"
                  >
                    <span
                      class="emotion-7"
                    >
                      Eyebrow
                    </span>
                    <span
                      class="emotion-8"
                    >
                      Headline
                    </span>
                  </div>
                  <div
                    class="emotion-9"
                  >
                    <div
                      class="emotion-10"
                    >
                      <div
                        class="emotion-11"
                      >
                        <a
                          class="emotion-12"
                          color="dark"
                          href="https://www.athleta.com"
                        >
                          Shop Now
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-13"
                >
                  <button
                    class="emotion-14"
                    data-testid="cmsISMPartialCta"
                  >
                    DETAILS
                  </button>
                </div>
                <a
                  class="emotion-15"
                  href="www.athleta.com"
                  target="_self"
                  title="Banner Link"
                />
              </div>
              <div
                class="emotion-16"
                data-testid="product-card-image"
              >
                <canvas
                  aria-label="casual"
                  class="emotion-17"
                  role="img"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots SubCategoryBannerVariableHeight - Banner md It should match the snapshot for gap - medium banner 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1000px;
}

.emotion-1 {
  width: auto;
  max-height: 275px;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1000/275;
}

.emotion-3 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-4 {
  aspect-ratio: 1000/275;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  margin: 0 3%;
}

.emotion-6 {
  display: block;
  text-align: right;
}

.emotion-7 {
  color: #faa;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
  display: block;
}

.emotion-8 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
  display: block;
}

.emotion-9 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-10 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: 1fr;
  grid-auto-rows: 1fr;
  grid-row-gap: 10px;
  grid-column-gap: 10px;
  margin-top: 30px;
  text-align: left;
  z-index: 2;
  width: 100%;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-11 {
  -webkit-flex: 335px 0 1;
  -ms-flex: 335px 0 1;
  flex: 335px 0 1;
}

.emotion-12 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 335px;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 14px;
  padding: 12px;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  text-transform: none;
  height: 100%;
}

.emotion-12:focus {
  outline: none;
}

.emotion-12:hover,
.emotion-12:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-12:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-13 {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 2;
}

.emotion-14 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  min-height: auto;
}

.emotion-14:focus {
  outline: none;
}

.emotion-14 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-14 span span {
  padding-left: initial;
}

.emotion-14:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-14:focus-visible {
  outline: auto;
}

.emotion-15 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

.emotion-16 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-17 {
  width: 100%;
  height: 100%;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
        data-testid="sub-category-banner"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
            height="275"
            width="1000"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
                data-testid="CbVariableHeight"
              >
                <div
                  class="emotion-5"
                  data-testid="DefaultLayout"
                >
                  <div
                    class="emotion-6"
                  >
                    <span
                      class="emotion-7"
                    >
                      Eyebrow
                    </span>
                    <span
                      class="emotion-8"
                    >
                      Headline
                    </span>
                  </div>
                  <div
                    class="emotion-9"
                  >
                    <div
                      class="emotion-10"
                    >
                      <div
                        class="emotion-11"
                      >
                        <a
                          class="emotion-12"
                          color="dark"
                          href="https://www.athleta.com"
                        >
                          Shop Now
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-13"
                >
                  <button
                    class="emotion-14"
                    data-testid="cmsISMPartialCta"
                  >
                    DETAILS
                  </button>
                </div>
                <a
                  class="emotion-15"
                  href="www.athleta.com"
                  target="_self"
                  title="Banner Link"
                />
              </div>
              <div
                class="emotion-16"
                data-testid="product-card-image"
              >
                <canvas
                  aria-label="casual"
                  class="emotion-17"
                  role="img"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots SubCategoryBannerVariableHeight - Banner md It should match the snapshot for on - medium banner 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1000px;
}

.emotion-1 {
  width: auto;
  max-height: 301px;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1000/301;
}

.emotion-3 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-4 {
  aspect-ratio: 1000/301;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  margin: 0 7%;
}

.emotion-6 {
  display: block;
  text-align: right;
}

.emotion-7 {
  color: #faa;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.328125vw, 17px));
  line-height: 1;
  letter-spacing: min(0.053125000000000006vw, 0.68px);
  font-weight: 500;
  display: block;
}

.emotion-8 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.75vw, 48px));
  line-height: 1;
  letter-spacing: min(0.22499999999999998vw, 2.88px);
  font-weight: 700;
  display: block;
}

.emotion-9 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-10 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: 1fr;
  grid-auto-rows: 1fr;
  grid-row-gap: 10px;
  grid-column-gap: 40px;
  margin-top: 30px;
  text-align: left;
  z-index: 2;
  width: 100%;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-11 {
  -webkit-flex: 335px 0 1;
  -ms-flex: 335px 0 1;
  flex: 335px 0 1;
}

.emotion-12 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  height: 100%;
  min-height: 100%;
}

.emotion-12:focus {
  outline: none;
}

.emotion-12>span {
  padding: 1px 0;
}

.emotion-12:hover,
.emotion-12:focus {
  color: #003764;
  background-color: #FFFFFF;
  border-color: #003764;
}

.emotion-12:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-13 {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 2;
}

.emotion-14 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.05px;
  min-height: auto;
  max-height: auto;
  line-height: 1.2857142857142858;
  padding: 0;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #003764;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  text-transform: capitalize;
  text-transform: capitalize;
  font-weight: normal;
  text-transform: capitalize;
  z-index: 1;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 10px;
  line-height: 12px;
  min-height: 10px;
  letter-spacing: normal;
}

.emotion-14:focus {
  outline: none;
}

.emotion-14>span {
  padding: 1px 0;
}

.emotion-14 span span {
  padding-left: 1px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1rem * 0.7);
}

.emotion-14 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #003764;
}

.emotion-14 span span {
  padding-left: initial;
}

.emotion-14:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-14:focus-visible {
  outline: auto;
}

.emotion-15 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

.emotion-16 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-17 {
  width: 100%;
  height: 100%;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
        data-testid="sub-category-banner"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
            height="301"
            width="1000"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
                data-testid="CbVariableHeight"
              >
                <div
                  class="emotion-5"
                  data-testid="DefaultLayout"
                >
                  <div
                    class="emotion-6"
                  >
                    <span
                      class="emotion-7"
                    >
                      Eyebrow
                    </span>
                    <span
                      class="emotion-8"
                    >
                      Headline
                    </span>
                  </div>
                  <div
                    class="emotion-9"
                  >
                    <div
                      class="emotion-10"
                    >
                      <div
                        class="emotion-11"
                      >
                        <a
                          class="emotion-12"
                          color="dark"
                          href="https://www.athleta.com"
                        >
                          Shop Now
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-13"
                >
                  <button
                    class="emotion-14"
                    data-testid="cmsISMPartialCta"
                  >
                    DETAILS
                  </button>
                </div>
                <a
                  class="emotion-15"
                  href="www.athleta.com"
                  target="_self"
                  title="Banner Link"
                />
              </div>
              <div
                class="emotion-16"
                data-testid="product-card-image"
              >
                <canvas
                  aria-label="casual"
                  class="emotion-17"
                  role="img"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots SubCategoryBannerVariableHeight - Banner md at 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1000px;
}

.emotion-1 {
  width: auto;
  max-height: 300px;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1000/300;
}

.emotion-3 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-4 {
  aspect-ratio: 1000/300;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  margin: 0 6.9%;
}

.emotion-6 {
  display: block;
  text-align: right;
}

.emotion-7 {
  color: #faa;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.328125vw, 17px));
  line-height: 1.588235294117647;
  letter-spacing: min(0.1328125vw, 1.7px);
  font-weight: 700;
  text-transform: uppercase;
  display: block;
}

.emotion-8 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(2.8125vw, 36px));
  line-height: 1;
  letter-spacing: min(0.28125vw, 3.6px);
  text-transform: uppercase;
  font-weight: 600;
  display: block;
}

.emotion-9 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-10 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: 1fr;
  grid-auto-rows: 1fr;
  grid-row-gap: 26px;
  grid-column-gap: 40px;
  margin-top: 30px;
  text-align: left;
  z-index: 2;
  width: 100%;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-11 {
  -webkit-flex: 335px 0 1;
  -ms-flex: 335px 0 1;
  flex: 335px 0 1;
}

.emotion-12 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 15px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 55px;
  line-height: 1.3333333333333333;
  padding: 20px 40px;
  width: 100%;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  height: 100%;
}

.emotion-12:focus {
  outline: none;
}

.emotion-12>span {
  padding: 1px 0;
}

.emotion-12:hover,
.emotion-12:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-12:active {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-13 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: end;
  justify-content: end;
  padding-bottom: 10px;
  padding-right: 10px;
  z-index: 2;
}

.emotion-14 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: auto;
  line-height: 1.3125;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  line-height: 16px;
  min-height: initial;
  padding: 0;
  margin-left: unset;
  pointer-events: auto;
  text-underline-offset: 2px;
}

.emotion-14:focus {
  outline: none;
}

.emotion-14>span {
  padding: 1px 0;
}

.emotion-14 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.2rem * 0.72);
}

.emotion-14 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-14 span span {
  padding-left: initial;
}

.emotion-14:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-14:focus-visible {
  outline: auto;
}

.emotion-15 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

.emotion-16 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-17 {
  width: 100%;
  height: 100%;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
        data-testid="sub-category-banner"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
            height="300"
            width="1000"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
                data-testid="CbVariableHeight"
              >
                <div
                  class="emotion-5"
                  data-testid="DefaultLayout"
                >
                  <div
                    class="emotion-6"
                  >
                    <span
                      class="emotion-7"
                    >
                      Eyebrow
                    </span>
                    <span
                      class="emotion-8"
                    >
                      Headline
                    </span>
                  </div>
                  <div
                    class="emotion-9"
                  >
                    <div
                      class="emotion-10"
                    >
                      <div
                        class="emotion-11"
                      >
                        <a
                          class="emotion-12"
                          color="dark"
                          href="https://www.athleta.com"
                        >
                          Shop Now
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-13"
                >
                  <button
                    class="emotion-14"
                    data-testid="cmsISMPartialCta"
                  >
                    DETAILS
                  </button>
                </div>
                <a
                  class="emotion-15"
                  href="www.athleta.com"
                  target="_self"
                  title="Banner Link"
                />
              </div>
              <div
                class="emotion-16"
                data-testid="product-card-image"
              >
                <canvas
                  aria-label="casual"
                  class="emotion-17"
                  role="img"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
       
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots SubCategoryBannerVariableHeight - Banner md gap 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1000px;
}

.emotion-1 {
  width: auto;
  max-height: 275px;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1000/275;
}

.emotion-3 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-4 {
  aspect-ratio: 1000/275;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  margin: 0 3%;
}

.emotion-6 {
  display: block;
  text-align: right;
}

.emotion-7 {
  color: #faa;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
  display: block;
}

.emotion-8 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
  display: block;
}

.emotion-9 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-10 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: 1fr;
  grid-auto-rows: 1fr;
  grid-row-gap: 10px;
  grid-column-gap: 10px;
  margin-top: 30px;
  text-align: left;
  z-index: 2;
  width: 100%;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-11 {
  -webkit-flex: 335px 0 1;
  -ms-flex: 335px 0 1;
  flex: 335px 0 1;
}

.emotion-12 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 335px;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 14px;
  padding: 12px;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  text-transform: none;
  height: 100%;
}

.emotion-12:focus {
  outline: none;
}

.emotion-12:hover,
.emotion-12:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-12:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-13 {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 2;
}

.emotion-14 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  min-height: auto;
}

.emotion-14:focus {
  outline: none;
}

.emotion-14 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-14 span span {
  padding-left: initial;
}

.emotion-14:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-14:focus-visible {
  outline: auto;
}

.emotion-15 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

.emotion-16 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-17 {
  width: 100%;
  height: 100%;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
        data-testid="sub-category-banner"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
            height="275"
            width="1000"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
                data-testid="CbVariableHeight"
              >
                <div
                  class="emotion-5"
                  data-testid="DefaultLayout"
                >
                  <div
                    class="emotion-6"
                  >
                    <span
                      class="emotion-7"
                    >
                      Eyebrow
                    </span>
                    <span
                      class="emotion-8"
                    >
                      Headline
                    </span>
                  </div>
                  <div
                    class="emotion-9"
                  >
                    <div
                      class="emotion-10"
                    >
                      <div
                        class="emotion-11"
                      >
                        <a
                          class="emotion-12"
                          color="dark"
                          href="https://www.athleta.com"
                        >
                          Shop Now
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-13"
                >
                  <button
                    class="emotion-14"
                    data-testid="cmsISMPartialCta"
                  >
                    DETAILS
                  </button>
                </div>
                <a
                  class="emotion-15"
                  href="www.athleta.com"
                  target="_self"
                  title="Banner Link"
                />
              </div>
              <div
                class="emotion-16"
                data-testid="product-card-image"
              >
                <canvas
                  aria-label="casual"
                  class="emotion-17"
                  role="img"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
       
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots SubCategoryBannerVariableHeight - Banner md on 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1000px;
}

.emotion-1 {
  width: auto;
  max-height: 301px;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1000/301;
}

.emotion-3 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-4 {
  aspect-ratio: 1000/301;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  margin: 0 7%;
}

.emotion-6 {
  display: block;
  text-align: right;
}

.emotion-7 {
  color: #faa;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.328125vw, 17px));
  line-height: 1;
  letter-spacing: min(0.053125000000000006vw, 0.68px);
  font-weight: 500;
  display: block;
}

.emotion-8 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.75vw, 48px));
  line-height: 1;
  letter-spacing: min(0.22499999999999998vw, 2.88px);
  font-weight: 700;
  display: block;
}

.emotion-9 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-10 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: 1fr;
  grid-auto-rows: 1fr;
  grid-row-gap: 10px;
  grid-column-gap: 40px;
  margin-top: 30px;
  text-align: left;
  z-index: 2;
  width: 100%;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-11 {
  -webkit-flex: 335px 0 1;
  -ms-flex: 335px 0 1;
  flex: 335px 0 1;
}

.emotion-12 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  height: 100%;
  min-height: 100%;
}

.emotion-12:focus {
  outline: none;
}

.emotion-12>span {
  padding: 1px 0;
}

.emotion-12:hover,
.emotion-12:focus {
  color: #003764;
  background-color: #FFFFFF;
  border-color: #003764;
}

.emotion-12:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-13 {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 2;
}

.emotion-14 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.05px;
  min-height: auto;
  max-height: auto;
  line-height: 1.2857142857142858;
  padding: 0;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #003764;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  text-transform: capitalize;
  text-transform: capitalize;
  font-weight: normal;
  text-transform: capitalize;
  z-index: 1;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 10px;
  line-height: 12px;
  min-height: 10px;
  letter-spacing: normal;
}

.emotion-14:focus {
  outline: none;
}

.emotion-14>span {
  padding: 1px 0;
}

.emotion-14 span span {
  padding-left: 1px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1rem * 0.7);
}

.emotion-14 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #003764;
}

.emotion-14 span span {
  padding-left: initial;
}

.emotion-14:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-14:focus-visible {
  outline: auto;
}

.emotion-15 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

.emotion-16 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-17 {
  width: 100%;
  height: 100%;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
        data-testid="sub-category-banner"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
            height="301"
            width="1000"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
                data-testid="CbVariableHeight"
              >
                <div
                  class="emotion-5"
                  data-testid="DefaultLayout"
                >
                  <div
                    class="emotion-6"
                  >
                    <span
                      class="emotion-7"
                    >
                      Eyebrow
                    </span>
                    <span
                      class="emotion-8"
                    >
                      Headline
                    </span>
                  </div>
                  <div
                    class="emotion-9"
                  >
                    <div
                      class="emotion-10"
                    >
                      <div
                        class="emotion-11"
                      >
                        <a
                          class="emotion-12"
                          color="dark"
                          href="https://www.athleta.com"
                        >
                          Shop Now
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-13"
                >
                  <button
                    class="emotion-14"
                    data-testid="cmsISMPartialCta"
                  >
                    DETAILS
                  </button>
                </div>
                <a
                  class="emotion-15"
                  href="www.athleta.com"
                  target="_self"
                  title="Banner Link"
                />
              </div>
              <div
                class="emotion-16"
                data-testid="product-card-image"
              >
                <canvas
                  aria-label="casual"
                  class="emotion-17"
                  role="img"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
       
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots for large desktop banner size 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  width: auto;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1280/575;
}

.emotion-3 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-4 {
  aspect-ratio: 1280/575;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: 0 3.75%;
}

.emotion-6 {
  display: block;
  text-align: center;
}

.emotion-7 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
  display: block;
}

.emotion-8 {
  color: #CC0;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
  display: block;
}

.emotion-9 {
  color: pink!important;
}

.emotion-9 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-9 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-9 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-9 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-9 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-9 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-9 * {
  color: pink!important;
}

.emotion-9 .amp-cms--f-0 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--fn-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.40625vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-4 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.09375vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-5 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.125vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.734375vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.59375vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-4 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-5 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-6 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.46875vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-7 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.6875vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headlineAlt-1 {
  color: pink!important;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-2 {
  color: pink!important;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-3 {
  color: pink!important;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-4 {
  color: pink!important;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-5 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-6 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-7 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--promo-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promo-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promoAlt-1 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-9 .amp-cms--subhead-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.953125vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-10 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-11 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(4, auto);
  grid-auto-rows: 1fr;
  grid-row-gap: 10px;
  grid-column-gap: 40px;
  margin-top: 30px;
  text-align: center;
  z-index: 2;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-13 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 335px;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  padding-bottom: 1.8px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 1.8px;
  text-decoration-thickness: 2px;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  text-transform: none;
  height: 100%;
  text-align: center;
}

.emotion-13:focus {
  outline: none;
}

.emotion-13:hover,
.emotion-13:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-13:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-14 {
  box-sizing: border-box;
}

.emotion-24 {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 2;
}

.emotion-25 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #B2B;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  min-height: auto;
}

.emotion-25:focus {
  outline: none;
}

.emotion-25 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-25 span span {
  padding-left: initial;
}

.emotion-25:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-25:focus-visible {
  outline: auto;
}

.emotion-26 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

.emotion-27 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-28 {
  width: 100%;
  aspect-ratio: 1280/575;
  object-fit: cover;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="category-banner"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
          height="575"
          width="1280"
        >
          <div
            class="emotion-3"
          >
            <div
              class="emotion-4"
              data-testid="CbVariableHeight"
            >
              <div
                class="emotion-5"
                data-testid="DefaultLayout"
              >
                <div
                  class="emotion-6"
                >
                  <span
                    class="emotion-7"
                  >
                    Eyebrow
                  </span>
                  <span
                    class="emotion-8"
                  >
                    Headline
                  </span>
                  <div
                    class="emotion-9"
                    color="pink"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic;text-decoration:underline"
                        >
                          This is a test for line breaks 
                        </span>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          H
                        </span>
                        <sub
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          2
                        </sub>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          0 Trademark 
                        </span>
                        <sup
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          R 
                        </sup>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="text-transform:uppercase;font-weight:bold;font-style:italic"
                        >
                          Text
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 1
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 2
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 3
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 4
                        </span>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="emotion-24"
              >
                <button
                  class="emotion-25"
                  data-testid="cmsISMPartialCta"
                >
                  DETAILS
                </button>
              </div>
              <a
                class="emotion-26"
                href="www.athleta.com"
                target="_self"
                title="Banner Link"
              />
            </div>
            <div
              class="emotion-27"
              data-testid="product-card-image"
            >
              <img
                alt="desktop alt"
                class="emotion-28"
                src="https://cdn.media.amplience.net/i/athleta/981523_002_MASP_AT_WMN_LS_59_SU21_SW_1_0061?fmt=webp"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots for medium desktop banner size 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  width: auto;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1280/400;
}

.emotion-3 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-4 {
  aspect-ratio: 1280/400;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: 0 3.75%;
}

.emotion-6 {
  display: block;
  text-align: center;
}

.emotion-7 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
  display: block;
}

.emotion-8 {
  color: #CC0;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
  display: block;
}

.emotion-9 {
  color: pink!important;
}

.emotion-9 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-9 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-9 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-9 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-9 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-9 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-9 * {
  color: pink!important;
}

.emotion-9 .amp-cms--f-0 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--fn-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.40625vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-4 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.09375vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-5 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.125vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.734375vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.59375vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-4 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-5 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-6 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.46875vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-7 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.6875vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headlineAlt-1 {
  color: pink!important;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-2 {
  color: pink!important;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-3 {
  color: pink!important;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-4 {
  color: pink!important;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-5 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-6 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-7 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--promo-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promo-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promoAlt-1 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-9 .amp-cms--subhead-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.953125vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-10 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-11 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(4, auto);
  grid-auto-rows: 1fr;
  grid-row-gap: 10px;
  grid-column-gap: 40px;
  margin-top: 30px;
  text-align: center;
  z-index: 2;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-13 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 335px;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  padding-bottom: 1.8px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 1.8px;
  text-decoration-thickness: 2px;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  text-transform: none;
  height: 100%;
  text-align: center;
}

.emotion-13:focus {
  outline: none;
}

.emotion-13:hover,
.emotion-13:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-13:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-14 {
  box-sizing: border-box;
}

.emotion-24 {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 2;
}

.emotion-25 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #B2B;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  min-height: auto;
}

.emotion-25:focus {
  outline: none;
}

.emotion-25 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-25 span span {
  padding-left: initial;
}

.emotion-25:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-25:focus-visible {
  outline: auto;
}

.emotion-26 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

.emotion-27 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-28 {
  width: 100%;
  aspect-ratio: 1280/400;
  object-fit: cover;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="category-banner"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
          height="400"
          width="1280"
        >
          <div
            class="emotion-3"
          >
            <div
              class="emotion-4"
              data-testid="CbVariableHeight"
            >
              <div
                class="emotion-5"
                data-testid="DefaultLayout"
              >
                <div
                  class="emotion-6"
                >
                  <span
                    class="emotion-7"
                  >
                    Eyebrow
                  </span>
                  <span
                    class="emotion-8"
                  >
                    Headline
                  </span>
                  <div
                    class="emotion-9"
                    color="pink"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic;text-decoration:underline"
                        >
                          This is a test for line breaks 
                        </span>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          H
                        </span>
                        <sub
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          2
                        </sub>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          0 Trademark 
                        </span>
                        <sup
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          R 
                        </sup>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="text-transform:uppercase;font-weight:bold;font-style:italic"
                        >
                          Text
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 1
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 2
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 3
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 4
                        </span>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="emotion-24"
              >
                <button
                  class="emotion-25"
                  data-testid="cmsISMPartialCta"
                >
                  DETAILS
                </button>
              </div>
              <a
                class="emotion-26"
                href="www.athleta.com"
                target="_self"
                title="Banner Link"
              />
            </div>
            <div
              class="emotion-27"
              data-testid="product-card-image"
            >
              <img
                alt="desktop alt"
                class="emotion-28"
                src="https://cdn.media.amplience.net/i/athleta/981523_002_MASP_AT_WMN_LS_59_SU21_SW_1_0061?fmt=webp"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots for small desktop banner size 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  width: auto;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1280/365;
}

.emotion-3 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-4 {
  aspect-ratio: 1280/365;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: 0 3.75%;
}

.emotion-6 {
  display: block;
  text-align: center;
}

.emotion-7 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
  display: block;
}

.emotion-8 {
  color: #CC0;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
  display: block;
}

.emotion-9 {
  color: pink!important;
}

.emotion-9 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-9 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-9 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-9 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-9 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-9 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-9 * {
  color: pink!important;
}

.emotion-9 .amp-cms--f-0 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--fn-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.40625vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-4 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.09375vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-5 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.125vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.734375vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.59375vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-4 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-5 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-6 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.46875vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-7 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.6875vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headlineAlt-1 {
  color: pink!important;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-2 {
  color: pink!important;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-3 {
  color: pink!important;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-4 {
  color: pink!important;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-5 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-6 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-7 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--promo-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promo-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promoAlt-1 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-9 .amp-cms--subhead-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.953125vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-10 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-11 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(4, auto);
  grid-auto-rows: 1fr;
  grid-row-gap: 10px;
  grid-column-gap: 40px;
  margin-top: 30px;
  text-align: center;
  z-index: 2;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-13 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 335px;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  padding-bottom: 1.8px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 1.8px;
  text-decoration-thickness: 2px;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  text-transform: none;
  height: 100%;
  text-align: center;
}

.emotion-13:focus {
  outline: none;
}

.emotion-13:hover,
.emotion-13:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-13:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-14 {
  box-sizing: border-box;
}

.emotion-24 {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 2;
}

.emotion-25 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #B2B;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  min-height: auto;
}

.emotion-25:focus {
  outline: none;
}

.emotion-25 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-25 span span {
  padding-left: initial;
}

.emotion-25:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-25:focus-visible {
  outline: auto;
}

.emotion-26 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

.emotion-27 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-28 {
  width: 100%;
  aspect-ratio: 1280/365;
  object-fit: cover;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="category-banner"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
          height="365"
          width="1280"
        >
          <div
            class="emotion-3"
          >
            <div
              class="emotion-4"
              data-testid="CbVariableHeight"
            >
              <div
                class="emotion-5"
                data-testid="DefaultLayout"
              >
                <div
                  class="emotion-6"
                >
                  <span
                    class="emotion-7"
                  >
                    Eyebrow
                  </span>
                  <span
                    class="emotion-8"
                  >
                    Headline
                  </span>
                  <div
                    class="emotion-9"
                    color="pink"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic;text-decoration:underline"
                        >
                          This is a test for line breaks 
                        </span>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          H
                        </span>
                        <sub
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          2
                        </sub>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          0 Trademark 
                        </span>
                        <sup
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          R 
                        </sup>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="text-transform:uppercase;font-weight:bold;font-style:italic"
                        >
                          Text
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 1
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 2
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 3
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 4
                        </span>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="emotion-24"
              >
                <button
                  class="emotion-25"
                  data-testid="cmsISMPartialCta"
                >
                  DETAILS
                </button>
              </div>
              <a
                class="emotion-26"
                href="www.athleta.com"
                target="_self"
                title="Banner Link"
              />
            </div>
            <div
              class="emotion-27"
              data-testid="product-card-image"
            >
              <img
                alt="desktop alt"
                class="emotion-28"
                src="https://cdn.media.amplience.net/i/athleta/981523_002_MASP_AT_WMN_LS_59_SU21_SW_1_0061?fmt=webp"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots for text justification on the center 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  width: auto;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1280/575;
}

.emotion-3 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-4 {
  aspect-ratio: 1280/575;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  margin: 0 3.75%;
}

.emotion-6 {
  display: block;
  text-align: center;
}

.emotion-7 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
  display: block;
}

.emotion-8 {
  color: #CC0;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
  display: block;
}

.emotion-9 {
  color: pink!important;
}

.emotion-9 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-9 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-9 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-9 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-9 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-9 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-9 * {
  color: pink!important;
}

.emotion-9 .amp-cms--f-0 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--fn-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.40625vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-4 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.09375vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-5 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.125vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.734375vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.59375vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-4 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-5 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-6 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.46875vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-7 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.6875vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headlineAlt-1 {
  color: pink!important;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-2 {
  color: pink!important;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-3 {
  color: pink!important;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-4 {
  color: pink!important;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-5 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-6 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-7 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--promo-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promo-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promoAlt-1 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-9 .amp-cms--subhead-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.953125vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-10 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-11 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(4, auto);
  grid-auto-rows: 1fr;
  grid-row-gap: 10px;
  grid-column-gap: 40px;
  margin-top: 30px;
  text-align: center;
  z-index: 2;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-13 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 335px;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  padding-bottom: 1.8px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 1.8px;
  text-decoration-thickness: 2px;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  text-transform: none;
  height: 100%;
  text-align: center;
}

.emotion-13:focus {
  outline: none;
}

.emotion-13:hover,
.emotion-13:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-13:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-14 {
  box-sizing: border-box;
}

.emotion-24 {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 2;
}

.emotion-25 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #B2B;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  min-height: auto;
}

.emotion-25:focus {
  outline: none;
}

.emotion-25 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-25 span span {
  padding-left: initial;
}

.emotion-25:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-25:focus-visible {
  outline: auto;
}

.emotion-26 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

.emotion-27 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-28 {
  width: 100%;
  aspect-ratio: 1280/575;
  object-fit: cover;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="category-banner"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
          height="575"
          width="1280"
        >
          <div
            class="emotion-3"
          >
            <div
              class="emotion-4"
              data-testid="CbVariableHeight"
            >
              <div
                class="emotion-5"
                data-testid="DefaultLayout"
              >
                <div
                  class="emotion-6"
                >
                  <span
                    class="emotion-7"
                  >
                    Eyebrow
                  </span>
                  <span
                    class="emotion-8"
                  >
                    Headline
                  </span>
                  <div
                    class="emotion-9"
                    color="pink"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic;text-decoration:underline"
                        >
                          This is a test for line breaks 
                        </span>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          H
                        </span>
                        <sub
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          2
                        </sub>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          0 Trademark 
                        </span>
                        <sup
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          R 
                        </sup>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="text-transform:uppercase;font-weight:bold;font-style:italic"
                        >
                          Text
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 1
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 2
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 3
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 4
                        </span>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="emotion-24"
              >
                <button
                  class="emotion-25"
                  data-testid="cmsISMPartialCta"
                >
                  DETAILS
                </button>
              </div>
              <a
                class="emotion-26"
                href="www.athleta.com"
                target="_self"
                title="Banner Link"
              />
            </div>
            <div
              class="emotion-27"
              data-testid="product-card-image"
            >
              <img
                alt="desktop alt"
                class="emotion-28"
                src="https://cdn.media.amplience.net/i/athleta/981523_002_MASP_AT_WMN_LS_59_SU21_SW_1_0061?fmt=webp"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots for text justification on the left 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  width: auto;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1280/575;
}

.emotion-3 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-4 {
  aspect-ratio: 1280/575;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  margin: 0 3.75%;
}

.emotion-6 {
  display: block;
  text-align: left;
}

.emotion-7 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
  display: block;
}

.emotion-8 {
  color: #CC0;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
  display: block;
}

.emotion-9 {
  color: pink!important;
}

.emotion-9 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-9 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-9 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-9 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-9 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-9 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-9 * {
  color: pink!important;
}

.emotion-9 .amp-cms--f-0 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--fn-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.40625vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-4 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.09375vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-5 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.125vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.734375vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.59375vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-4 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-5 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-6 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.46875vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-7 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.6875vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headlineAlt-1 {
  color: pink!important;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-2 {
  color: pink!important;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-3 {
  color: pink!important;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-4 {
  color: pink!important;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-5 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-6 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-7 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--promo-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promo-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promoAlt-1 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-9 .amp-cms--subhead-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.953125vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-10 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-11 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(4, auto);
  grid-auto-rows: 1fr;
  grid-row-gap: 10px;
  grid-column-gap: 40px;
  margin-top: 30px;
  text-align: center;
  z-index: 2;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-13 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 335px;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  padding-bottom: 1.8px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 1.8px;
  text-decoration-thickness: 2px;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  text-transform: none;
  height: 100%;
  text-align: center;
}

.emotion-13:focus {
  outline: none;
}

.emotion-13:hover,
.emotion-13:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-13:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-14 {
  box-sizing: border-box;
}

.emotion-24 {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 2;
}

.emotion-25 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #B2B;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  min-height: auto;
}

.emotion-25:focus {
  outline: none;
}

.emotion-25 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-25 span span {
  padding-left: initial;
}

.emotion-25:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-25:focus-visible {
  outline: auto;
}

.emotion-26 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

.emotion-27 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-28 {
  width: 100%;
  aspect-ratio: 1280/575;
  object-fit: cover;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="category-banner"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
          height="575"
          width="1280"
        >
          <div
            class="emotion-3"
          >
            <div
              class="emotion-4"
              data-testid="CbVariableHeight"
            >
              <div
                class="emotion-5"
                data-testid="DefaultLayout"
              >
                <div
                  class="emotion-6"
                >
                  <span
                    class="emotion-7"
                  >
                    Eyebrow
                  </span>
                  <span
                    class="emotion-8"
                  >
                    Headline
                  </span>
                  <div
                    class="emotion-9"
                    color="pink"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic;text-decoration:underline"
                        >
                          This is a test for line breaks 
                        </span>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          H
                        </span>
                        <sub
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          2
                        </sub>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          0 Trademark 
                        </span>
                        <sup
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          R 
                        </sup>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="text-transform:uppercase;font-weight:bold;font-style:italic"
                        >
                          Text
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 1
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 2
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 3
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 4
                        </span>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="emotion-24"
              >
                <button
                  class="emotion-25"
                  data-testid="cmsISMPartialCta"
                >
                  DETAILS
                </button>
              </div>
              <a
                class="emotion-26"
                href="www.athleta.com"
                target="_self"
                title="Banner Link"
              />
            </div>
            <div
              class="emotion-27"
              data-testid="product-card-image"
            >
              <img
                alt="desktop alt"
                class="emotion-28"
                src="https://cdn.media.amplience.net/i/athleta/981523_002_MASP_AT_WMN_LS_59_SU21_SW_1_0061?fmt=webp"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots for text justification on the right 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  width: auto;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1280/575;
}

.emotion-3 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-4 {
  aspect-ratio: 1280/575;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  margin: 0 3.75%;
}

.emotion-6 {
  display: block;
  text-align: right;
}

.emotion-7 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
  display: block;
}

.emotion-8 {
  color: #CC0;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
  display: block;
}

.emotion-9 {
  color: pink!important;
}

.emotion-9 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-9 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-9 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-9 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-9 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-9 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-9 * {
  color: pink!important;
}

.emotion-9 .amp-cms--f-0 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--fn-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.40625vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-4 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.09375vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-5 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.125vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.734375vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.59375vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-4 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-5 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-6 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.46875vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-7 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.6875vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headlineAlt-1 {
  color: pink!important;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-2 {
  color: pink!important;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-3 {
  color: pink!important;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-4 {
  color: pink!important;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-5 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-6 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-7 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--promo-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promo-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promoAlt-1 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-9 .amp-cms--subhead-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.953125vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-10 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-11 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(4, auto);
  grid-auto-rows: 1fr;
  grid-row-gap: 10px;
  grid-column-gap: 40px;
  margin-top: 30px;
  text-align: center;
  z-index: 2;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-13 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 335px;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  padding-bottom: 1.8px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 1.8px;
  text-decoration-thickness: 2px;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  text-transform: none;
  height: 100%;
  text-align: center;
}

.emotion-13:focus {
  outline: none;
}

.emotion-13:hover,
.emotion-13:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-13:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-14 {
  box-sizing: border-box;
}

.emotion-24 {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 2;
}

.emotion-25 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #B2B;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  min-height: auto;
}

.emotion-25:focus {
  outline: none;
}

.emotion-25 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-25 span span {
  padding-left: initial;
}

.emotion-25:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-25:focus-visible {
  outline: auto;
}

.emotion-26 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

.emotion-27 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-28 {
  width: 100%;
  aspect-ratio: 1280/575;
  object-fit: cover;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="category-banner"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
          height="575"
          width="1280"
        >
          <div
            class="emotion-3"
          >
            <div
              class="emotion-4"
              data-testid="CbVariableHeight"
            >
              <div
                class="emotion-5"
                data-testid="DefaultLayout"
              >
                <div
                  class="emotion-6"
                >
                  <span
                    class="emotion-7"
                  >
                    Eyebrow
                  </span>
                  <span
                    class="emotion-8"
                  >
                    Headline
                  </span>
                  <div
                    class="emotion-9"
                    color="pink"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic;text-decoration:underline"
                        >
                          This is a test for line breaks 
                        </span>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          H
                        </span>
                        <sub
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          2
                        </sub>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          0 Trademark 
                        </span>
                        <sup
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          R 
                        </sup>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="text-transform:uppercase;font-weight:bold;font-style:italic"
                        >
                          Text
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 1
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 2
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 3
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 4
                        </span>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="emotion-24"
              >
                <button
                  class="emotion-25"
                  data-testid="cmsISMPartialCta"
                >
                  DETAILS
                </button>
              </div>
              <a
                class="emotion-26"
                href="www.athleta.com"
                target="_self"
                title="Banner Link"
              />
            </div>
            <div
              class="emotion-27"
              data-testid="product-card-image"
            >
              <img
                alt="desktop alt"
                class="emotion-28"
                src="https://cdn.media.amplience.net/i/athleta/981523_002_MASP_AT_WMN_LS_59_SU21_SW_1_0061?fmt=webp"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots for text placement on the center 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  width: auto;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1280/575;
}

.emotion-3 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-4 {
  aspect-ratio: 1280/575;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: 0 3.75%;
}

.emotion-6 {
  display: block;
  text-align: center;
}

.emotion-7 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
  display: block;
}

.emotion-8 {
  color: #CC0;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
  display: block;
}

.emotion-9 {
  color: pink!important;
}

.emotion-9 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-9 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-9 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-9 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-9 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-9 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-9 * {
  color: pink!important;
}

.emotion-9 .amp-cms--f-0 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--fn-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.40625vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-4 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.09375vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-5 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.125vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.734375vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.59375vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-4 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-5 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-6 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.46875vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-7 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.6875vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headlineAlt-1 {
  color: pink!important;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-2 {
  color: pink!important;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-3 {
  color: pink!important;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-4 {
  color: pink!important;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-5 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-6 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-7 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--promo-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promo-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promoAlt-1 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-9 .amp-cms--subhead-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.953125vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-10 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-11 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(4, auto);
  grid-auto-rows: 1fr;
  grid-row-gap: 10px;
  grid-column-gap: 40px;
  margin-top: 30px;
  text-align: center;
  z-index: 2;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-13 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 335px;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  padding-bottom: 1.8px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 1.8px;
  text-decoration-thickness: 2px;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  text-transform: none;
  height: 100%;
  text-align: center;
}

.emotion-13:focus {
  outline: none;
}

.emotion-13:hover,
.emotion-13:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-13:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-14 {
  box-sizing: border-box;
}

.emotion-24 {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 2;
}

.emotion-25 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #B2B;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  min-height: auto;
}

.emotion-25:focus {
  outline: none;
}

.emotion-25 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-25 span span {
  padding-left: initial;
}

.emotion-25:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-25:focus-visible {
  outline: auto;
}

.emotion-26 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

.emotion-27 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-28 {
  width: 100%;
  aspect-ratio: 1280/575;
  object-fit: cover;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="category-banner"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
          height="575"
          width="1280"
        >
          <div
            class="emotion-3"
          >
            <div
              class="emotion-4"
              data-testid="CbVariableHeight"
            >
              <div
                class="emotion-5"
                data-testid="DefaultLayout"
              >
                <div
                  class="emotion-6"
                >
                  <span
                    class="emotion-7"
                  >
                    Eyebrow
                  </span>
                  <span
                    class="emotion-8"
                  >
                    Headline
                  </span>
                  <div
                    class="emotion-9"
                    color="pink"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic;text-decoration:underline"
                        >
                          This is a test for line breaks 
                        </span>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          H
                        </span>
                        <sub
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          2
                        </sub>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          0 Trademark 
                        </span>
                        <sup
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          R 
                        </sup>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="text-transform:uppercase;font-weight:bold;font-style:italic"
                        >
                          Text
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 1
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 2
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 3
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 4
                        </span>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="emotion-24"
              >
                <button
                  class="emotion-25"
                  data-testid="cmsISMPartialCta"
                >
                  DETAILS
                </button>
              </div>
              <a
                class="emotion-26"
                href="www.athleta.com"
                target="_self"
                title="Banner Link"
              />
            </div>
            <div
              class="emotion-27"
              data-testid="product-card-image"
            >
              <img
                alt="desktop alt"
                class="emotion-28"
                src="https://cdn.media.amplience.net/i/athleta/981523_002_MASP_AT_WMN_LS_59_SU21_SW_1_0061?fmt=webp"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots for text placement on the left 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  width: auto;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1280/575;
}

.emotion-3 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-4 {
  aspect-ratio: 1280/575;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  margin: 0 3.75%;
}

.emotion-6 {
  display: block;
  text-align: center;
}

.emotion-7 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
  display: block;
}

.emotion-8 {
  color: #CC0;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
  display: block;
}

.emotion-9 {
  color: pink!important;
}

.emotion-9 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-9 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-9 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-9 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-9 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-9 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-9 * {
  color: pink!important;
}

.emotion-9 .amp-cms--f-0 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--fn-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.40625vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-4 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.09375vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-5 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.125vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.734375vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.59375vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-4 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-5 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-6 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.46875vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-7 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.6875vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headlineAlt-1 {
  color: pink!important;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-2 {
  color: pink!important;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-3 {
  color: pink!important;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-4 {
  color: pink!important;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-5 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-6 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-7 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--promo-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promo-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promoAlt-1 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-9 .amp-cms--subhead-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.953125vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-10 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-11 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(4, auto);
  grid-auto-rows: 1fr;
  grid-row-gap: 10px;
  grid-column-gap: 40px;
  margin-top: 30px;
  text-align: center;
  z-index: 2;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-13 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 335px;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  padding-bottom: 1.8px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 1.8px;
  text-decoration-thickness: 2px;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  text-transform: none;
  height: 100%;
  text-align: center;
}

.emotion-13:focus {
  outline: none;
}

.emotion-13:hover,
.emotion-13:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-13:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-14 {
  box-sizing: border-box;
}

.emotion-24 {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 2;
}

.emotion-25 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #B2B;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  min-height: auto;
}

.emotion-25:focus {
  outline: none;
}

.emotion-25 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-25 span span {
  padding-left: initial;
}

.emotion-25:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-25:focus-visible {
  outline: auto;
}

.emotion-26 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

.emotion-27 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-28 {
  width: 100%;
  aspect-ratio: 1280/575;
  object-fit: cover;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="category-banner"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
          height="575"
          width="1280"
        >
          <div
            class="emotion-3"
          >
            <div
              class="emotion-4"
              data-testid="CbVariableHeight"
            >
              <div
                class="emotion-5"
                data-testid="DefaultLayout"
              >
                <div
                  class="emotion-6"
                >
                  <span
                    class="emotion-7"
                  >
                    Eyebrow
                  </span>
                  <span
                    class="emotion-8"
                  >
                    Headline
                  </span>
                  <div
                    class="emotion-9"
                    color="pink"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic;text-decoration:underline"
                        >
                          This is a test for line breaks 
                        </span>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          H
                        </span>
                        <sub
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          2
                        </sub>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          0 Trademark 
                        </span>
                        <sup
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          R 
                        </sup>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="text-transform:uppercase;font-weight:bold;font-style:italic"
                        >
                          Text
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 1
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 2
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 3
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 4
                        </span>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="emotion-24"
              >
                <button
                  class="emotion-25"
                  data-testid="cmsISMPartialCta"
                >
                  DETAILS
                </button>
              </div>
              <a
                class="emotion-26"
                href="www.athleta.com"
                target="_self"
                title="Banner Link"
              />
            </div>
            <div
              class="emotion-27"
              data-testid="product-card-image"
            >
              <img
                alt="desktop alt"
                class="emotion-28"
                src="https://cdn.media.amplience.net/i/athleta/981523_002_MASP_AT_WMN_LS_59_SU21_SW_1_0061?fmt=webp"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots for text placement on the right 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  width: auto;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1280/575;
}

.emotion-3 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-4 {
  aspect-ratio: 1280/575;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  margin: 0 3.75%;
}

.emotion-6 {
  display: block;
  text-align: center;
}

.emotion-7 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
  display: block;
}

.emotion-8 {
  color: #CC0;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
  display: block;
}

.emotion-9 {
  color: pink!important;
}

.emotion-9 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-9 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-9 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-9 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-9 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-9 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-9 * {
  color: pink!important;
}

.emotion-9 .amp-cms--f-0 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--fn-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.40625vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-4 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.09375vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-5 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.125vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.734375vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.59375vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-4 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-5 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-6 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.46875vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-7 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.6875vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headlineAlt-1 {
  color: pink!important;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-2 {
  color: pink!important;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-3 {
  color: pink!important;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-4 {
  color: pink!important;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-5 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-6 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-7 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--promo-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promo-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promoAlt-1 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-9 .amp-cms--subhead-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.953125vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-10 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-11 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(4, auto);
  grid-auto-rows: 1fr;
  grid-row-gap: 10px;
  grid-column-gap: 40px;
  margin-top: 30px;
  text-align: center;
  z-index: 2;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-13 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 335px;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  padding-bottom: 1.8px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 1.8px;
  text-decoration-thickness: 2px;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  text-transform: none;
  height: 100%;
  text-align: center;
}

.emotion-13:focus {
  outline: none;
}

.emotion-13:hover,
.emotion-13:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-13:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-14 {
  box-sizing: border-box;
}

.emotion-24 {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 2;
}

.emotion-25 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #B2B;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  min-height: auto;
}

.emotion-25:focus {
  outline: none;
}

.emotion-25 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-25 span span {
  padding-left: initial;
}

.emotion-25:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-25:focus-visible {
  outline: auto;
}

.emotion-26 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

.emotion-27 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-28 {
  width: 100%;
  aspect-ratio: 1280/575;
  object-fit: cover;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="category-banner"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
          height="575"
          width="1280"
        >
          <div
            class="emotion-3"
          >
            <div
              class="emotion-4"
              data-testid="CbVariableHeight"
            >
              <div
                class="emotion-5"
                data-testid="DefaultLayout"
              >
                <div
                  class="emotion-6"
                >
                  <span
                    class="emotion-7"
                  >
                    Eyebrow
                  </span>
                  <span
                    class="emotion-8"
                  >
                    Headline
                  </span>
                  <div
                    class="emotion-9"
                    color="pink"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic;text-decoration:underline"
                        >
                          This is a test for line breaks 
                        </span>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          H
                        </span>
                        <sub
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          2
                        </sub>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          0 Trademark 
                        </span>
                        <sup
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          R 
                        </sup>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="text-transform:uppercase;font-weight:bold;font-style:italic"
                        >
                          Text
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 1
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 2
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 3
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 4
                        </span>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="emotion-24"
              >
                <button
                  class="emotion-25"
                  data-testid="cmsISMPartialCta"
                >
                  DETAILS
                </button>
              </div>
              <a
                class="emotion-26"
                href="www.athleta.com"
                target="_self"
                title="Banner Link"
              />
            </div>
            <div
              class="emotion-27"
              data-testid="product-card-image"
            >
              <img
                alt="desktop alt"
                class="emotion-28"
                src="https://cdn.media.amplience.net/i/athleta/981523_002_MASP_AT_WMN_LS_59_SU21_SW_1_0061?fmt=webp"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots in double column CTA view 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  width: auto;
}

.emotion-2 {
  height: 100%;
  position: relative;
  display: block;
  grid-template-rows: 1fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-3 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 375/235;
}

.emotion-4 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-5 {
  height: 100%;
  overflow: none;
  background: url(https://cdn.media.amplience.net/i/athleta/981523_002_MASP_AT_WMN_LS_59_SU21_SW_1_0061?fmt=auto&h=1640) left no-repeat;
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-6 {
  height: 100%;
  overflow: none;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  background-color: #FFFFFF;
  padding: 20px 20px 30px;
  border: unset;
}

.emotion-7 {
  display: block;
  text-align: center;
}

.emotion-8 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 22px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
  display: block;
}

.emotion-9 {
  color: #CC0;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(12vw, 45px));
  line-height: 1;
  letter-spacing: 0;
  display: block;
}

.emotion-10 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-10 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-10 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-10 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-10 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-10 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-10 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-10 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.266666666666667vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.933333333333333vw, 26px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.4vw, 24px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 55px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 55px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(13.333333333333334vw, 50px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(12vw, 45px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(9.333333333333334vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(13px, min(17.066666666666666vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(13px, min(14.399999999999999vw, 54px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(13px, min(9.066666666666666vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(13px, min(6.4vw, 24px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-10 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-10 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-10 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 22px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.333333333333334vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(4.8vw, 18px));
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-11 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-12 {
  position: relative;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-auto-rows: 1fr;
  grid-row-gap: 10px;
  grid-column-gap: 20px;
  margin-top: 20px;
  text-align: center;
  z-index: 2;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-14 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  padding-bottom: 1.8px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 1.8px;
  text-decoration-thickness: 2px;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  text-transform: none;
  height: 100%;
  text-align: center;
}

.emotion-14:focus {
  outline: none;
}

.emotion-14:hover,
.emotion-14:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-14:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-15 {
  box-sizing: border-box;
}

.emotion-25 {
  position: absolute;
  bottom: unset;
  right: 5px;
  z-index: 2;
}

.emotion-26 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #B2B;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 11px;
  min-height: auto;
}

.emotion-26:focus {
  outline: none;
}

.emotion-26 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-26 span span {
  padding-left: initial;
}

.emotion-26:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-26:focus-visible {
  outline: auto;
}

.emotion-27 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="category-banner"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
          data-testid="CbVariableHeight"
        >
          <div
            class="emotion-3"
            height="235"
            width="375"
          >
            <div
              class="emotion-4"
            >
              <div
                class="emotion-5"
                data-testid="MobileImageTest"
              />
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <div
              class="emotion-7"
            >
              <span
                class="emotion-8"
              >
                Eyebrow
              </span>
              <span
                class="emotion-9"
              >
                Headline
              </span>
              <div
                class="emotion-10"
                color=""
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-1 amp-cms--avenir-next-regular"
                      style="font-weight:bold;font-style:italic;text-decoration:underline"
                    >
                      This is a test for line breaks 
                    </span>
                    <span
                      class="amp-cms--body-1 amp-cms--avenir-next-regular"
                      style="font-weight:bold;font-style:italic"
                    >
                      H
                    </span>
                    <sub
                      class="amp-cms--body-1 amp-cms--avenir-next-regular"
                      style="font-weight:bold;font-style:italic"
                    >
                      2
                    </sub>
                    <span
                      class="amp-cms--body-1 amp-cms--avenir-next-regular"
                      style="font-weight:bold;font-style:italic"
                    >
                      0 Trademark 
                    </span>
                    <sup
                      class="amp-cms--body-1 amp-cms--avenir-next-regular"
                      style="font-weight:bold;font-style:italic"
                    >
                      R 
                    </sup>
                    <span
                      class="amp-cms--body-1 amp-cms--avenir-next-regular"
                      style="text-transform:uppercase;font-weight:bold;font-style:italic"
                    >
                      Text
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <div
              class="emotion-11"
            >
              <div
                class="emotion-12"
              >
                <div
                  class="emotion-13"
                >
                  <a
                    class="emotion-14"
                    color="custom"
                    href="https://www.athleta.com"
                  >
                    <span
                      class="emotion-15"
                    >
                      Shop Now 1
                    </span>
                  </a>
                </div>
                <div
                  class="emotion-13"
                >
                  <a
                    class="emotion-14"
                    color="custom"
                    href="https://www.athleta.com"
                  >
                    <span
                      class="emotion-15"
                    >
                      Shop Now 2
                    </span>
                  </a>
                </div>
                <div
                  class="emotion-13"
                >
                  <a
                    class="emotion-14"
                    color="custom"
                    href="https://www.athleta.com"
                  >
                    <span
                      class="emotion-15"
                    >
                      Shop Now 3
                    </span>
                  </a>
                </div>
                <div
                  class="emotion-13"
                >
                  <a
                    class="emotion-14"
                    color="custom"
                    href="https://www.athleta.com"
                  >
                    <span
                      class="emotion-15"
                    >
                      Shop Now 4
                    </span>
                  </a>
                </div>
              </div>
            </div>
            <div
              class="emotion-25"
            >
              <button
                class="emotion-26"
                data-testid="cmsISMPartialCta"
              >
                DETAILS
              </button>
            </div>
          </div>
          <a
            class="emotion-27"
            href="www.athleta.com"
            target="_self"
            title="Banner Link"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots in hide on desktop view 1`] = `
<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  />
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots in hide on mobile view 1`] = `
<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  />
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots in single column CTA view 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  width: auto;
}

.emotion-2 {
  height: 100%;
  position: relative;
  display: block;
  grid-template-rows: 1fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-3 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 375/235;
}

.emotion-4 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-5 {
  height: 100%;
  overflow: none;
  background: url(https://cdn.media.amplience.net/i/athleta/981523_002_MASP_AT_WMN_LS_59_SU21_SW_1_0061?fmt=auto&h=1640) left no-repeat;
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-6 {
  height: 100%;
  overflow: none;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  background-color: #FFFFFF;
  padding: 20px 20px 30px;
  border: unset;
}

.emotion-7 {
  display: block;
  text-align: center;
}

.emotion-8 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 22px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
  display: block;
}

.emotion-9 {
  color: #CC0;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(12vw, 45px));
  line-height: 1;
  letter-spacing: 0;
  display: block;
}

.emotion-10 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-10 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-10 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-10 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-10 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-10 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-10 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-10 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.266666666666667vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.933333333333333vw, 26px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.4vw, 24px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 55px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 55px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(13.333333333333334vw, 50px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(12vw, 45px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(9.333333333333334vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(13px, min(17.066666666666666vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(13px, min(14.399999999999999vw, 54px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(13px, min(9.066666666666666vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(13px, min(6.4vw, 24px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-10 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-10 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-10 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 22px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.333333333333334vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(4.8vw, 18px));
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-11 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-12 {
  position: relative;
  display: grid;
  grid-template-columns: 1fr;
  grid-auto-rows: 1fr;
  grid-row-gap: 10px;
  grid-column-gap: 20px;
  margin-top: 20px;
  text-align: center;
  z-index: 2;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-14 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  padding-bottom: 1.8px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 1.8px;
  text-decoration-thickness: 2px;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  text-transform: none;
  height: 100%;
  text-align: center;
}

.emotion-14:focus {
  outline: none;
}

.emotion-14:hover,
.emotion-14:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-14:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-15 {
  box-sizing: border-box;
}

.emotion-25 {
  position: absolute;
  bottom: unset;
  right: 5px;
  z-index: 2;
}

.emotion-26 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #B2B;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 11px;
  min-height: auto;
}

.emotion-26:focus {
  outline: none;
}

.emotion-26 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-26 span span {
  padding-left: initial;
}

.emotion-26:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-26:focus-visible {
  outline: auto;
}

.emotion-27 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="category-banner"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
          data-testid="CbVariableHeight"
        >
          <div
            class="emotion-3"
            height="235"
            width="375"
          >
            <div
              class="emotion-4"
            >
              <div
                class="emotion-5"
                data-testid="MobileImageTest"
              />
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <div
              class="emotion-7"
            >
              <span
                class="emotion-8"
              >
                Eyebrow
              </span>
              <span
                class="emotion-9"
              >
                Headline
              </span>
              <div
                class="emotion-10"
                color=""
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-1 amp-cms--avenir-next-regular"
                      style="font-weight:bold;font-style:italic;text-decoration:underline"
                    >
                      This is a test for line breaks 
                    </span>
                    <span
                      class="amp-cms--body-1 amp-cms--avenir-next-regular"
                      style="font-weight:bold;font-style:italic"
                    >
                      H
                    </span>
                    <sub
                      class="amp-cms--body-1 amp-cms--avenir-next-regular"
                      style="font-weight:bold;font-style:italic"
                    >
                      2
                    </sub>
                    <span
                      class="amp-cms--body-1 amp-cms--avenir-next-regular"
                      style="font-weight:bold;font-style:italic"
                    >
                      0 Trademark 
                    </span>
                    <sup
                      class="amp-cms--body-1 amp-cms--avenir-next-regular"
                      style="font-weight:bold;font-style:italic"
                    >
                      R 
                    </sup>
                    <span
                      class="amp-cms--body-1 amp-cms--avenir-next-regular"
                      style="text-transform:uppercase;font-weight:bold;font-style:italic"
                    >
                      Text
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <div
              class="emotion-11"
            >
              <div
                class="emotion-12"
              >
                <div
                  class="emotion-13"
                >
                  <a
                    class="emotion-14"
                    color="custom"
                    href="https://www.athleta.com"
                  >
                    <span
                      class="emotion-15"
                    >
                      Shop Now 1
                    </span>
                  </a>
                </div>
                <div
                  class="emotion-13"
                >
                  <a
                    class="emotion-14"
                    color="custom"
                    href="https://www.athleta.com"
                  >
                    <span
                      class="emotion-15"
                    >
                      Shop Now 2
                    </span>
                  </a>
                </div>
                <div
                  class="emotion-13"
                >
                  <a
                    class="emotion-14"
                    color="custom"
                    href="https://www.athleta.com"
                  >
                    <span
                      class="emotion-15"
                    >
                      Shop Now 3
                    </span>
                  </a>
                </div>
                <div
                  class="emotion-13"
                >
                  <a
                    class="emotion-14"
                    color="custom"
                    href="https://www.athleta.com"
                  >
                    <span
                      class="emotion-15"
                    >
                      Shop Now 4
                    </span>
                  </a>
                </div>
              </div>
            </div>
            <div
              class="emotion-25"
            >
              <button
                class="emotion-26"
                data-testid="cmsISMPartialCta"
              >
                DETAILS
              </button>
            </div>
          </div>
          <a
            class="emotion-27"
            href="www.athleta.com"
            target="_self"
            title="Banner Link"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots in text below image view 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  width: auto;
}

.emotion-2 {
  height: 100%;
  position: relative;
  display: block;
  grid-template-rows: 1fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-3 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 375/235;
}

.emotion-4 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-5 {
  height: 100%;
  overflow: none;
  background: url(https://cdn.media.amplience.net/i/athleta/981523_002_MASP_AT_WMN_LS_59_SU21_SW_1_0061?fmt=auto&h=1640) left no-repeat;
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-6 {
  height: 100%;
  overflow: none;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  background-color: #FFFFFF;
  padding: 20px 20px 30px;
  border: unset;
}

.emotion-7 {
  display: block;
  text-align: center;
}

.emotion-8 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 22px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
  display: block;
}

.emotion-9 {
  color: #CC0;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(12vw, 45px));
  line-height: 1;
  letter-spacing: 0;
  display: block;
}

.emotion-10 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-10 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-10 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-10 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-10 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-10 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-10 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-10 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.266666666666667vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.933333333333333vw, 26px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.4vw, 24px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 55px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 55px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(13.333333333333334vw, 50px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(12vw, 45px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(9.333333333333334vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(13px, min(17.066666666666666vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(13px, min(14.399999999999999vw, 54px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(13px, min(9.066666666666666vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(13px, min(6.4vw, 24px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-10 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-10 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-10 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 22px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.333333333333334vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(4.8vw, 18px));
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-11 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-12 {
  position: relative;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-auto-rows: 1fr;
  grid-row-gap: 10px;
  grid-column-gap: 20px;
  margin-top: 20px;
  text-align: center;
  z-index: 2;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-14 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  padding-bottom: 1.8px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 1.8px;
  text-decoration-thickness: 2px;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  text-transform: none;
  height: 100%;
  text-align: center;
}

.emotion-14:focus {
  outline: none;
}

.emotion-14:hover,
.emotion-14:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-14:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-15 {
  box-sizing: border-box;
}

.emotion-25 {
  position: absolute;
  bottom: unset;
  right: 5px;
  z-index: 2;
}

.emotion-26 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #B2B;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 11px;
  min-height: auto;
}

.emotion-26:focus {
  outline: none;
}

.emotion-26 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-26 span span {
  padding-left: initial;
}

.emotion-26:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-26:focus-visible {
  outline: auto;
}

.emotion-27 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="category-banner"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
          data-testid="CbVariableHeight"
        >
          <div
            class="emotion-3"
            height="235"
            width="375"
          >
            <div
              class="emotion-4"
            >
              <div
                class="emotion-5"
                data-testid="MobileImageTest"
              />
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <div
              class="emotion-7"
            >
              <span
                class="emotion-8"
              >
                Eyebrow
              </span>
              <span
                class="emotion-9"
              >
                Headline
              </span>
              <div
                class="emotion-10"
                color=""
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-1 amp-cms--avenir-next-regular"
                      style="font-weight:bold;font-style:italic;text-decoration:underline"
                    >
                      This is a test for line breaks 
                    </span>
                    <span
                      class="amp-cms--body-1 amp-cms--avenir-next-regular"
                      style="font-weight:bold;font-style:italic"
                    >
                      H
                    </span>
                    <sub
                      class="amp-cms--body-1 amp-cms--avenir-next-regular"
                      style="font-weight:bold;font-style:italic"
                    >
                      2
                    </sub>
                    <span
                      class="amp-cms--body-1 amp-cms--avenir-next-regular"
                      style="font-weight:bold;font-style:italic"
                    >
                      0 Trademark 
                    </span>
                    <sup
                      class="amp-cms--body-1 amp-cms--avenir-next-regular"
                      style="font-weight:bold;font-style:italic"
                    >
                      R 
                    </sup>
                    <span
                      class="amp-cms--body-1 amp-cms--avenir-next-regular"
                      style="text-transform:uppercase;font-weight:bold;font-style:italic"
                    >
                      Text
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <div
              class="emotion-11"
            >
              <div
                class="emotion-12"
              >
                <div
                  class="emotion-13"
                >
                  <a
                    class="emotion-14"
                    color="custom"
                    href="https://www.athleta.com"
                  >
                    <span
                      class="emotion-15"
                    >
                      Shop Now 1
                    </span>
                  </a>
                </div>
                <div
                  class="emotion-13"
                >
                  <a
                    class="emotion-14"
                    color="custom"
                    href="https://www.athleta.com"
                  >
                    <span
                      class="emotion-15"
                    >
                      Shop Now 2
                    </span>
                  </a>
                </div>
                <div
                  class="emotion-13"
                >
                  <a
                    class="emotion-14"
                    color="custom"
                    href="https://www.athleta.com"
                  >
                    <span
                      class="emotion-15"
                    >
                      Shop Now 3
                    </span>
                  </a>
                </div>
                <div
                  class="emotion-13"
                >
                  <a
                    class="emotion-14"
                    color="custom"
                    href="https://www.athleta.com"
                  >
                    <span
                      class="emotion-15"
                    >
                      Shop Now 4
                    </span>
                  </a>
                </div>
              </div>
            </div>
            <div
              class="emotion-25"
            >
              <button
                class="emotion-26"
                data-testid="cmsISMPartialCta"
              >
                DETAILS
              </button>
            </div>
          </div>
          <a
            class="emotion-27"
            href="www.athleta.com"
            target="_self"
            title="Banner Link"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots on desktop view 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  width: auto;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1280/400;
}

.emotion-3 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-4 {
  aspect-ratio: 1280/400;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: 0 3.75%;
}

.emotion-6 {
  display: block;
  text-align: center;
}

.emotion-7 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
  display: block;
}

.emotion-8 {
  color: #CC0;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
  display: block;
}

.emotion-9 {
  color: pink!important;
}

.emotion-9 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-9 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-9 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-9 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-9 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-9 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-9 * {
  color: pink!important;
}

.emotion-9 .amp-cms--f-0 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--fn-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.40625vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-4 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.09375vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-5 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.125vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.734375vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.59375vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-4 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-5 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-6 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.46875vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-7 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.6875vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headlineAlt-1 {
  color: pink!important;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-2 {
  color: pink!important;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-3 {
  color: pink!important;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-4 {
  color: pink!important;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-5 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-6 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-7 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--promo-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promo-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promoAlt-1 {
  color: pink!important;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-9 .amp-cms--subhead-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.953125vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-10 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-11 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(4, auto);
  grid-auto-rows: 1fr;
  grid-row-gap: 10px;
  grid-column-gap: 40px;
  margin-top: 30px;
  text-align: center;
  z-index: 2;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-13 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 335px;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  padding-bottom: 1.8px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 1.8px;
  text-decoration-thickness: 2px;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  text-transform: none;
  height: 100%;
  text-align: center;
}

.emotion-13:focus {
  outline: none;
}

.emotion-13:hover,
.emotion-13:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-13:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-14 {
  box-sizing: border-box;
}

.emotion-24 {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 2;
}

.emotion-25 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #B2B;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  min-height: auto;
}

.emotion-25:focus {
  outline: none;
}

.emotion-25 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-25 span span {
  padding-left: initial;
}

.emotion-25:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-25:focus-visible {
  outline: auto;
}

.emotion-26 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

.emotion-27 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-28 {
  width: 100%;
  aspect-ratio: 1280/400;
  object-fit: cover;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="category-banner"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
          height="400"
          width="1280"
        >
          <div
            class="emotion-3"
          >
            <div
              class="emotion-4"
              data-testid="CbVariableHeight"
            >
              <div
                class="emotion-5"
                data-testid="DefaultLayout"
              >
                <div
                  class="emotion-6"
                >
                  <span
                    class="emotion-7"
                  >
                    Eyebrow
                  </span>
                  <span
                    class="emotion-8"
                  >
                    Headline
                  </span>
                  <div
                    class="emotion-9"
                    color="pink"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic;text-decoration:underline"
                        >
                          This is a test for line breaks 
                        </span>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          H
                        </span>
                        <sub
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          2
                        </sub>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          0 Trademark 
                        </span>
                        <sup
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          R 
                        </sup>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="text-transform:uppercase;font-weight:bold;font-style:italic"
                        >
                          Text
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 1
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 2
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 3
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 4
                        </span>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="emotion-24"
              >
                <button
                  class="emotion-25"
                  data-testid="cmsISMPartialCta"
                >
                  DETAILS
                </button>
              </div>
              <a
                class="emotion-26"
                href="www.athleta.com"
                target="_self"
                title="Banner Link"
              />
            </div>
            <div
              class="emotion-27"
              data-testid="product-card-image"
            >
              <img
                alt="desktop alt"
                class="emotion-28"
                src="https://cdn.media.amplience.net/i/athleta/981523_002_MASP_AT_WMN_LS_59_SU21_SW_1_0061?fmt=webp"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots on mobile/default view 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  width: auto;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 375/235;
}

.emotion-3 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-4 {
  aspect-ratio: 375/235;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  padding: 4.27%;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: 0;
}

.emotion-6 {
  display: block;
  text-align: center;
}

.emotion-7 {
  color: black;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 22px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
  display: block;
}

.emotion-8 {
  color: #CC0;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(12vw, 45px));
  line-height: 1;
  letter-spacing: 0;
  display: block;
}

.emotion-9 {
  color: pink!important;
}

.emotion-9 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-9 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-9 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-9 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-9 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-9 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-9 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-9 * {
  color: pink!important;
}

.emotion-9 .amp-cms--f-0 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--fn-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.266666666666667vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-4 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-5 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.933333333333333vw, 26px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.4vw, 24px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 55px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 55px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-4 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(13.333333333333334vw, 50px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-5 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(12vw, 45px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-6 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-7 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(9.333333333333334vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headlineAlt-1 {
  color: pink!important;
  font-size: max(13px, min(17.066666666666666vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-2 {
  color: pink!important;
  font-size: max(13px, min(14.399999999999999vw, 54px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-3 {
  color: pink!important;
  font-size: max(13px, min(9.066666666666666vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-4 {
  color: pink!important;
  font-size: max(13px, min(6.4vw, 24px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-5 {
  color: pink!important;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-6 {
  color: pink!important;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-7 {
  color: pink!important;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--promo-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promo-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promoAlt-1 {
  color: pink!important;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-9 .amp-cms--subhead-1 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 22px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-2 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.333333333333334vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-3 {
  color: pink!important;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(4.8vw, 18px));
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-10 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-11 {
  position: relative;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-auto-rows: 1fr;
  grid-row-gap: 10px;
  grid-column-gap: 20px;
  margin-top: 20px;
  text-align: center;
  z-index: 2;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-13 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  padding-bottom: 1.8px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 1.8px;
  text-decoration-thickness: 2px;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  text-transform: none;
  height: 100%;
  text-align: center;
}

.emotion-13:focus {
  outline: none;
}

.emotion-13:hover,
.emotion-13:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-13:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-14 {
  box-sizing: border-box;
}

.emotion-24 {
  position: absolute;
  bottom: 5px;
  right: 5px;
  z-index: 2;
}

.emotion-25 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #B2B;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 11px;
  min-height: auto;
}

.emotion-25:focus {
  outline: none;
}

.emotion-25 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-25 span span {
  padding-left: initial;
}

.emotion-25:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-25:focus-visible {
  outline: auto;
}

.emotion-26 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

.emotion-27 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-28 {
  width: 100%;
  aspect-ratio: 375/235;
  object-fit: cover;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="category-banner"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
          height="235"
          width="375"
        >
          <div
            class="emotion-3"
          >
            <div
              class="emotion-4"
              data-testid="CbVariableHeight"
            >
              <div
                class="emotion-5"
                data-testid="DefaultLayout"
              >
                <div
                  class="emotion-6"
                >
                  <span
                    class="emotion-7"
                  >
                    Eyebrow
                  </span>
                  <span
                    class="emotion-8"
                  >
                    Headline
                  </span>
                  <div
                    class="emotion-9"
                    color="pink"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic;text-decoration:underline"
                        >
                          This is a test for line breaks 
                        </span>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          H
                        </span>
                        <sub
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          2
                        </sub>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          0 Trademark 
                        </span>
                        <sup
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          R 
                        </sup>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="text-transform:uppercase;font-weight:bold;font-style:italic"
                        >
                          Text
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 1
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 2
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 3
                        </span>
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="custom"
                        href="https://www.athleta.com"
                      >
                        <span
                          class="emotion-14"
                        >
                          Shop Now 4
                        </span>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="emotion-24"
              >
                <button
                  class="emotion-25"
                  data-testid="cmsISMPartialCta"
                >
                  DETAILS
                </button>
              </div>
              <a
                class="emotion-26"
                href="www.athleta.com"
                target="_self"
                title="Banner Link"
              />
            </div>
            <div
              class="emotion-27"
              data-testid="product-card-image"
            >
              <img
                alt="desktop alt"
                class="emotion-28"
                src="https://cdn.media.amplience.net/i/athleta/981523_002_MASP_AT_WMN_LS_59_SU21_SW_1_0061?fmt=webp"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots with fixed-width CTAs in double column CTA view 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  width: auto;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 375/450;
}

.emotion-3 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-4 {
  aspect-ratio: 375/450;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  padding: 4.27%;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: 0;
}

.emotion-6 {
  display: block;
  text-align: center;
}

.emotion-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 22px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
  display: block;
}

.emotion-8 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(12vw, 45px));
  line-height: 1;
  letter-spacing: 0;
  display: block;
}

.emotion-9 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-9 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-9 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-9 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-9 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-9 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-9 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-9 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.266666666666667vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.933333333333333vw, 26px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.4vw, 24px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 55px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 55px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(13.333333333333334vw, 50px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(12vw, 45px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(9.333333333333334vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(13px, min(17.066666666666666vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(13px, min(14.399999999999999vw, 54px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(13px, min(9.066666666666666vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(13px, min(6.4vw, 24px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-9 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 22px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.333333333333334vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(4.8vw, 18px));
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-10 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-11 {
  position: relative;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-auto-rows: 1fr;
  grid-row-gap: 10px;
  grid-column-gap: 10px;
  margin-top: 20px;
  text-align: center;
  z-index: 2;
  width: 100%;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-13 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 14px;
  padding: 12px;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #000000;
  color: #FFFFFF;
  border-color: #FFFFFF;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  text-transform: none;
  height: 100%;
}

.emotion-13:focus {
  outline: none;
}

.emotion-13:hover,
.emotion-13:focus {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-13:active {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-20 {
  position: absolute;
  bottom: 5px;
  right: 5px;
  z-index: 2;
}

.emotion-21 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 11px;
  min-height: auto;
}

.emotion-21:focus {
  outline: none;
}

.emotion-21 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-21 span span {
  padding-left: initial;
}

.emotion-21:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-21:focus-visible {
  outline: auto;
}

.emotion-22 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

.emotion-23 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-24 {
  width: 100%;
  aspect-ratio: 375/450;
  object-fit: cover;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="category-banner"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
          height="450"
          width="375"
        >
          <div
            class="emotion-3"
          >
            <div
              class="emotion-4"
              data-testid="CbVariableHeight"
            >
              <div
                class="emotion-5"
                data-testid="DefaultLayout"
              >
                <div
                  class="emotion-6"
                >
                  <span
                    class="emotion-7"
                  >
                    Eyebrow
                  </span>
                  <span
                    class="emotion-8"
                  >
                    Headline
                  </span>
                  <div
                    class="emotion-9"
                    color=""
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic;text-decoration:underline"
                        >
                          This is a test for line breaks 
                        </span>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          H
                        </span>
                        <sub
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          2
                        </sub>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          0 Trademark 
                        </span>
                        <sup
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          R 
                        </sup>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="text-transform:uppercase;font-weight:bold;font-style:italic"
                        >
                          Text
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="light"
                        href="https://www.athleta.com"
                      >
                        Shop Now 1
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="light"
                        href="https://www.athleta.com"
                      >
                        Shop Now 2
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="light"
                        href="https://www.athleta.com"
                      >
                        Shop Now 3
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="light"
                        href="https://www.athleta.com"
                      >
                        Shop Now 4
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="emotion-20"
              >
                <button
                  class="emotion-21"
                  data-testid="cmsISMPartialCta"
                >
                  DETAILS
                </button>
              </div>
              <a
                class="emotion-22"
                href="www.athleta.com"
                target="_self"
                title="Banner Link"
              />
            </div>
            <div
              class="emotion-23"
              data-testid="product-card-image"
            >
              <img
                alt="desktop alt"
                class="emotion-24"
                src="https://cdn.media.amplience.net/i/athleta/981523_002_MASP_AT_WMN_LS_59_SU21_SW_1_0061?fmt=webp"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots with fixed-width CTAs in single column CTA view 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  width: auto;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 375/450;
}

.emotion-3 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-4 {
  aspect-ratio: 375/450;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  padding: 4.27%;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  margin: 0;
}

.emotion-6 {
  display: block;
  text-align: center;
}

.emotion-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 22px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
  display: block;
}

.emotion-8 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(12vw, 45px));
  line-height: 1;
  letter-spacing: 0;
  display: block;
}

.emotion-9 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-9 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-9 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-9 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-9 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-9 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-9 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-9 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.266666666666667vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.933333333333333vw, 26px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.4vw, 24px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 55px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 55px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(13.333333333333334vw, 50px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(12vw, 45px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(9.333333333333334vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(13px, min(17.066666666666666vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(13px, min(14.399999999999999vw, 54px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(13px, min(9.066666666666666vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(13px, min(6.4vw, 24px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-9 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 22px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.333333333333334vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(4.8vw, 18px));
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-10 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-11 {
  position: relative;
  display: grid;
  grid-template-columns: 1fr;
  grid-auto-rows: 1fr;
  grid-row-gap: 10px;
  grid-column-gap: 10px;
  margin-top: 20px;
  text-align: center;
  z-index: 2;
  width: 100%;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-13 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 14px;
  padding: 12px;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #000000;
  color: #FFFFFF;
  border-color: #FFFFFF;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  text-transform: none;
  height: 100%;
}

.emotion-13:focus {
  outline: none;
}

.emotion-13:hover,
.emotion-13:focus {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-13:active {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-20 {
  position: absolute;
  bottom: 5px;
  right: 5px;
  z-index: 2;
}

.emotion-21 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 11px;
  min-height: auto;
}

.emotion-21:focus {
  outline: none;
}

.emotion-21 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-21 span span {
  padding-left: initial;
}

.emotion-21:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-21:focus-visible {
  outline: auto;
}

.emotion-22 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

.emotion-23 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-24 {
  width: 100%;
  aspect-ratio: 375/450;
  object-fit: cover;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="category-banner"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
          height="450"
          width="375"
        >
          <div
            class="emotion-3"
          >
            <div
              class="emotion-4"
              data-testid="CbVariableHeight"
            >
              <div
                class="emotion-5"
                data-testid="DefaultLayout"
              >
                <div
                  class="emotion-6"
                >
                  <span
                    class="emotion-7"
                  >
                    Eyebrow
                  </span>
                  <span
                    class="emotion-8"
                  >
                    Headline
                  </span>
                  <div
                    class="emotion-9"
                    color=""
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic;text-decoration:underline"
                        >
                          This is a test for line breaks 
                        </span>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          H
                        </span>
                        <sub
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          2
                        </sub>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          0 Trademark 
                        </span>
                        <sup
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          R 
                        </sup>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="text-transform:uppercase;font-weight:bold;font-style:italic"
                        >
                          Text
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="light"
                        href="https://www.athleta.com"
                      >
                        Shop Now 1
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="light"
                        href="https://www.athleta.com"
                      >
                        Shop Now 2
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="light"
                        href="https://www.athleta.com"
                      >
                        Shop Now 3
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="light"
                        href="https://www.athleta.com"
                      >
                        Shop Now 4
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="emotion-20"
              >
                <button
                  class="emotion-21"
                  data-testid="cmsISMPartialCta"
                >
                  DETAILS
                </button>
              </div>
              <a
                class="emotion-22"
                href="www.athleta.com"
                target="_self"
                title="Banner Link"
              />
            </div>
            <div
              class="emotion-23"
              data-testid="product-card-image"
            >
              <img
                alt="desktop alt"
                class="emotion-24"
                src="https://cdn.media.amplience.net/i/athleta/981523_002_MASP_AT_WMN_LS_59_SU21_SW_1_0061?fmt=webp"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`CategoryBannerVariableHeight Component should match snapshots with fixed-width CTAs on desktop view 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  width: auto;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1280/575;
}

.emotion-3 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-4 {
  aspect-ratio: 1280/575;
  height: 100%;
  position: absolute;
  display: grid;
  grid-template-rows: 2fr;
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column;
  -webkit-flex-flow: column;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  margin: 0 3.75%;
}

.emotion-6 {
  display: block;
  text-align: left;
}

.emotion-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
  display: block;
}

.emotion-8 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
  display: block;
}

.emotion-9 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-9 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-9 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-9 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-9 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-9 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-9 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.40625vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.09375vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.125vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.734375vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.59375vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.46875vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.6875vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-9 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.953125vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-10 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-11 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: 1fr 1fr;
  grid-auto-rows: 1fr;
  grid-row-gap: 10px;
  grid-column-gap: 10px;
  margin-top: 30px;
  text-align: left;
  z-index: 2;
  width: 100%;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-12 {
  -webkit-flex: 335px 0 1;
  -ms-flex: 335px 0 1;
  flex: 335px 0 1;
}

.emotion-13 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 335px;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 14px;
  padding: 12px;
  width: 100%;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  text-transform: none;
  height: 100%;
}

.emotion-13:focus {
  outline: none;
}

.emotion-13:hover,
.emotion-13:focus {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-13:active {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-20 {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 2;
}

.emotion-21 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  min-height: auto;
}

.emotion-21:focus {
  outline: none;
}

.emotion-21 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-21 span span {
  padding-left: initial;
}

.emotion-21:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-21:focus-visible {
  outline: auto;
}

.emotion-22 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  z-index: 1;
}

.emotion-23 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-24 {
  width: 100%;
  aspect-ratio: 1280/575;
  object-fit: cover;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="category-banner"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
          height="575"
          width="1280"
        >
          <div
            class="emotion-3"
          >
            <div
              class="emotion-4"
              data-testid="CbVariableHeight"
            >
              <div
                class="emotion-5"
                data-testid="DefaultLayout"
              >
                <div
                  class="emotion-6"
                >
                  <span
                    class="emotion-7"
                  >
                    Eyebrow
                  </span>
                  <span
                    class="emotion-8"
                  >
                    Headline
                  </span>
                  <div
                    class="emotion-9"
                    color=""
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic;text-decoration:underline"
                        >
                          This is a test for line breaks 
                        </span>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          H
                        </span>
                        <sub
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          2
                        </sub>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          0 Trademark 
                        </span>
                        <sup
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="font-weight:bold;font-style:italic"
                        >
                          R 
                        </sup>
                        <span
                          class="amp-cms--body-1 amp-cms--avenir-next-regular"
                          style="text-transform:uppercase;font-weight:bold;font-style:italic"
                        >
                          Text
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <div
                    class="emotion-11"
                  >
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="light"
                        href="https://www.athleta.com"
                      >
                        Shop Now 1
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="light"
                        href="https://www.athleta.com"
                      >
                        Shop Now 2
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="light"
                        href="https://www.athleta.com"
                      >
                        Shop Now 3
                      </a>
                    </div>
                    <div
                      class="emotion-12"
                    >
                      <a
                        class="emotion-13"
                        color="light"
                        href="https://www.athleta.com"
                      >
                        Shop Now 4
                      </a>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="emotion-20"
              >
                <button
                  class="emotion-21"
                  data-testid="cmsISMPartialCta"
                >
                  DETAILS
                </button>
              </div>
              <a
                class="emotion-22"
                href="www.athleta.com"
                target="_self"
                title="Banner Link"
              />
            </div>
            <div
              class="emotion-23"
              data-testid="product-card-image"
            >
              <img
                alt="desktop alt"
                class="emotion-24"
                src="https://cdn.media.amplience.net/i/athleta/981523_002_MASP_AT_WMN_LS_59_SU21_SW_1_0061?fmt=webp"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
