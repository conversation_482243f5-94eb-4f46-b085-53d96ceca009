// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<SpotlightSection /> container image Athleta Aspect Ratio should match the expected snapshot for desktop large 1`] = `
.emotion-0 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-1 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: min(24px, 1.6666666666666667vw);
}

.emotion-2 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: min(24px, 1.6666666666666667vw);
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 80.83333333333333%;
  padding: min(32px, 2.2222222222222223vw) min(147px, 10.208333333333334vw);
  gap: min(24px, 1.6666666666666667vw);
  pointer-events: none;
}

.emotion-4 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-5 {
  width: 100%;
  aspect-ratio: 1440/810;
  object-fit: cover;
}

.emotion-6 {
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            />
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="product-card-image"
        >
          <img
            alt="beach-family"
            class="emotion-5"
            src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/SUM_210521_M1620_S00086_BIGIDEARangeofSilhouettes1_F_06_0351_d1?fmt=webp"
          />
        </div>
        <a
          aria-label="link aria label"
          class="emotion-6"
          data-testid="conditional-link"
          href="/link"
          target="_self"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`<SpotlightSection /> container image Athleta Aspect Ratio should match the expected snapshot for desktop medium 1`] = `
.emotion-0 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-1 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: min(24px, 1.6666666666666667vw);
}

.emotion-2 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: min(24px, 1.6666666666666667vw);
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 80.83333333333333%;
  padding: min(32px, 2.2222222222222223vw) min(147px, 10.208333333333334vw);
  gap: min(24px, 1.6666666666666667vw);
  pointer-events: none;
}

.emotion-4 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-5 {
  width: 100%;
  aspect-ratio: 1440/650;
  object-fit: cover;
}

.emotion-6 {
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            />
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="product-card-image"
        >
          <img
            alt="beach-family"
            class="emotion-5"
            src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/SUM_210521_M1620_S00086_BIGIDEARangeofSilhouettes1_F_06_0351_d1?fmt=webp"
          />
        </div>
        <a
          aria-label="link aria label"
          class="emotion-6"
          data-testid="conditional-link"
          href="/link"
          target="_self"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`<SpotlightSection /> container image Athleta Aspect Ratio should match the expected snapshot for desktop small 1`] = `
.emotion-0 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-1 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: min(24px, 1.6666666666666667vw);
}

.emotion-2 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: min(24px, 1.6666666666666667vw);
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 80.83333333333333%;
  padding: min(32px, 2.2222222222222223vw) min(147px, 10.208333333333334vw);
  gap: min(24px, 1.6666666666666667vw);
  pointer-events: none;
}

.emotion-4 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-5 {
  width: 100%;
  aspect-ratio: 1440/360;
  object-fit: cover;
}

.emotion-6 {
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            />
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="product-card-image"
        >
          <img
            alt="beach-family"
            class="emotion-5"
            src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/SUM_210521_M1620_S00086_BIGIDEARangeofSilhouettes1_F_06_0351_d1?fmt=webp"
          />
        </div>
        <a
          aria-label="link aria label"
          class="emotion-6"
          data-testid="conditional-link"
          href="/link"
          target="_self"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`<SpotlightSection /> container image Athleta Aspect Ratio should match the expected snapshot for mobile hero image size large 1`] = `
.emotion-0 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-1 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-2 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  padding: 6.4vw 4.533333333333333vw;
  gap: 6.4vw;
  pointer-events: none;
}

.emotion-4 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-5 {
  width: 100%;
  aspect-ratio: 375/667;
  object-fit: cover;
}

.emotion-6 {
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            />
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="product-card-image"
        >
          <img
            alt="beach-family"
            class="emotion-5"
            src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/SUM_210521_M1620_S00086_BIGIDEARangeofSilhouettes1_F_06_0351_d1?fmt=webp"
          />
        </div>
        <a
          aria-label="link aria label"
          class="emotion-6"
          data-testid="conditional-link"
          href="/link"
          target="_self"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`<SpotlightSection /> container image Athleta Aspect Ratio should match the expected snapshot for mobile hero image size medium 1`] = `
.emotion-0 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-1 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-2 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  padding: 6.4vw 4.533333333333333vw;
  gap: 6.4vw;
  pointer-events: none;
}

.emotion-4 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-5 {
  width: 100%;
  aspect-ratio: 375/500.25;
  object-fit: cover;
}

.emotion-6 {
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            />
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="product-card-image"
        >
          <img
            alt="beach-family"
            class="emotion-5"
            src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/SUM_210521_M1620_S00086_BIGIDEARangeofSilhouettes1_F_06_0351_d1?fmt=webp"
          />
        </div>
        <a
          aria-label="link aria label"
          class="emotion-6"
          data-testid="conditional-link"
          href="/link"
          target="_self"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`<SpotlightSection /> container image Athleta Aspect Ratio should match the expected snapshot for mobile hero image size small 1`] = `
.emotion-0 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-1 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-2 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  padding: 6.4vw 4.533333333333333vw;
  gap: 6.4vw;
  pointer-events: none;
}

.emotion-4 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-5 {
  width: 100%;
  aspect-ratio: 375/333.5;
  object-fit: cover;
}

.emotion-6 {
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            />
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="product-card-image"
        >
          <img
            alt="beach-family"
            class="emotion-5"
            src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/SUM_210521_M1620_S00086_BIGIDEARangeofSilhouettes1_F_06_0351_d1?fmt=webp"
          />
        </div>
        <a
          aria-label="link aria label"
          class="emotion-6"
          data-testid="conditional-link"
          href="/link"
          target="_self"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`<SpotlightSection /> container image Athleta Aspect Ratio should match the expected snapshot for mobile large 1`] = `
.emotion-0 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-1 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-2 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  padding: 6.4vw 4.533333333333333vw;
  gap: 6.4vw;
  pointer-events: none;
}

.emotion-4 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-5 {
  width: 100%;
  aspect-ratio: 375/667;
  object-fit: cover;
}

.emotion-6 {
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            />
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="product-card-image"
        >
          <img
            alt="beach-family"
            class="emotion-5"
            src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/SUM_210521_M1620_S00086_BIGIDEARangeofSilhouettes1_F_06_0351_d1?fmt=webp"
          />
        </div>
        <a
          aria-label="link aria label"
          class="emotion-6"
          data-testid="conditional-link"
          href="/link"
          target="_self"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`<SpotlightSection /> container image Athleta Aspect Ratio should match the expected snapshot for mobile medium 1`] = `
.emotion-0 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-1 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-2 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  padding: 6.4vw 4.533333333333333vw;
  gap: 6.4vw;
  pointer-events: none;
}

.emotion-4 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-5 {
  width: 100%;
  aspect-ratio: 375/500.25;
  object-fit: cover;
}

.emotion-6 {
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            />
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="product-card-image"
        >
          <img
            alt="beach-family"
            class="emotion-5"
            src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/SUM_210521_M1620_S00086_BIGIDEARangeofSilhouettes1_F_06_0351_d1?fmt=webp"
          />
        </div>
        <a
          aria-label="link aria label"
          class="emotion-6"
          data-testid="conditional-link"
          href="/link"
          target="_self"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`<SpotlightSection /> container image Athleta Aspect Ratio should match the expected snapshot for mobile small 1`] = `
.emotion-0 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-1 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-2 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  padding: 6.4vw 4.533333333333333vw;
  gap: 6.4vw;
  pointer-events: none;
}

.emotion-4 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-5 {
  width: 100%;
  aspect-ratio: 375/333.5;
  object-fit: cover;
}

.emotion-6 {
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            />
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="product-card-image"
        >
          <img
            alt="beach-family"
            class="emotion-5"
            src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/SUM_210521_M1620_S00086_BIGIDEARangeofSilhouettes1_F_06_0351_d1?fmt=webp"
          />
        </div>
        <a
          aria-label="link aria label"
          class="emotion-6"
          data-testid="conditional-link"
          href="/link"
          target="_self"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`<SpotlightSection /> container image Old Navy Aspect Ratio should match the expected snapshot for desktop large 1`] = `
.emotion-0 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-1 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: min(24px, 1.6666666666666667vw);
}

.emotion-2 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: min(24px, 1.6666666666666667vw);
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 80.83333333333333%;
  padding: min(32px, 2.2222222222222223vw) min(147px, 10.208333333333334vw);
  gap: min(24px, 1.6666666666666667vw);
  pointer-events: none;
}

.emotion-4 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-5 {
  width: 100%;
  aspect-ratio: 1440/800;
  object-fit: cover;
}

.emotion-6 {
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            />
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="product-card-image"
        >
          <img
            alt="beach-family"
            class="emotion-5"
            src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/SUM_210521_M1620_S00086_BIGIDEARangeofSilhouettes1_F_06_0351_d1?fmt=webp"
          />
        </div>
        <a
          aria-label="link aria label"
          class="emotion-6"
          data-testid="conditional-link"
          href="/link"
          target="_self"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`<SpotlightSection /> container image Old Navy Aspect Ratio should match the expected snapshot for desktop medium 1`] = `
.emotion-0 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-1 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: min(24px, 1.6666666666666667vw);
}

.emotion-2 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: min(24px, 1.6666666666666667vw);
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 80.83333333333333%;
  padding: min(32px, 2.2222222222222223vw) min(147px, 10.208333333333334vw);
  gap: min(24px, 1.6666666666666667vw);
  pointer-events: none;
}

.emotion-4 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-5 {
  width: 100%;
  aspect-ratio: 1440/480;
  object-fit: cover;
}

.emotion-6 {
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            />
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="product-card-image"
        >
          <img
            alt="beach-family"
            class="emotion-5"
            src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/SUM_210521_M1620_S00086_BIGIDEARangeofSilhouettes1_F_06_0351_d1?fmt=webp"
          />
        </div>
        <a
          aria-label="link aria label"
          class="emotion-6"
          data-testid="conditional-link"
          href="/link"
          target="_self"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`<SpotlightSection /> container image Old Navy Aspect Ratio should match the expected snapshot for desktop small 1`] = `
.emotion-0 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-1 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: min(24px, 1.6666666666666667vw);
}

.emotion-2 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: min(24px, 1.6666666666666667vw);
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 80.83333333333333%;
  padding: min(32px, 2.2222222222222223vw) min(147px, 10.208333333333334vw);
  gap: min(24px, 1.6666666666666667vw);
  pointer-events: none;
}

.emotion-4 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-5 {
  width: 100%;
  aspect-ratio: 1440/320;
  object-fit: cover;
}

.emotion-6 {
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            />
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="product-card-image"
        >
          <img
            alt="beach-family"
            class="emotion-5"
            src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/SUM_210521_M1620_S00086_BIGIDEARangeofSilhouettes1_F_06_0351_d1?fmt=webp"
          />
        </div>
        <a
          aria-label="link aria label"
          class="emotion-6"
          data-testid="conditional-link"
          href="/link"
          target="_self"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`<SpotlightSection /> container image Old Navy Aspect Ratio should match the expected snapshot for desktop xsmall 1`] = `
.emotion-0 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-1 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: min(24px, 1.6666666666666667vw);
}

.emotion-2 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: min(24px, 1.6666666666666667vw);
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 80.83333333333333%;
  padding: min(32px, 2.2222222222222223vw) min(147px, 10.208333333333334vw);
  gap: min(24px, 1.6666666666666667vw);
  pointer-events: none;
}

.emotion-4 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-5 {
  width: 100%;
  aspect-ratio: 1440/150;
  object-fit: cover;
}

.emotion-6 {
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            />
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="product-card-image"
        >
          <img
            alt="beach-family"
            class="emotion-5"
            src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/SUM_210521_M1620_S00086_BIGIDEARangeofSilhouettes1_F_06_0351_d1?fmt=webp"
          />
        </div>
        <a
          aria-label="link aria label"
          class="emotion-6"
          data-testid="conditional-link"
          href="/link"
          target="_self"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`<SpotlightSection /> container image Old Navy Aspect Ratio should match the expected snapshot for mobile hero image size large 1`] = `
.emotion-0 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-1 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-2 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  padding: 6.4vw 4.533333333333333vw;
  gap: 6.4vw;
  pointer-events: none;
}

.emotion-4 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-5 {
  width: 100%;
  aspect-ratio: 375/468;
  object-fit: cover;
}

.emotion-6 {
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            />
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="product-card-image"
        >
          <img
            alt="beach-family"
            class="emotion-5"
            src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/SUM_210521_M1620_S00086_BIGIDEARangeofSilhouettes1_F_06_0351_d1?fmt=webp"
          />
        </div>
        <a
          aria-label="link aria label"
          class="emotion-6"
          data-testid="conditional-link"
          href="/link"
          target="_self"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`<SpotlightSection /> container image Old Navy Aspect Ratio should match the expected snapshot for mobile hero image size medium 1`] = `
.emotion-0 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-1 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-2 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  padding: 6.4vw 4.533333333333333vw;
  gap: 6.4vw;
  pointer-events: none;
}

.emotion-4 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-5 {
  width: 100%;
  aspect-ratio: 375/375;
  object-fit: cover;
}

.emotion-6 {
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            />
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="product-card-image"
        >
          <img
            alt="beach-family"
            class="emotion-5"
            src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/SUM_210521_M1620_S00086_BIGIDEARangeofSilhouettes1_F_06_0351_d1?fmt=webp"
          />
        </div>
        <a
          aria-label="link aria label"
          class="emotion-6"
          data-testid="conditional-link"
          href="/link"
          target="_self"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`<SpotlightSection /> container image Old Navy Aspect Ratio should match the expected snapshot for mobile hero image size small 1`] = `
.emotion-0 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-1 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-2 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  padding: 6.4vw 4.533333333333333vw;
  gap: 6.4vw;
  pointer-events: none;
}

.emotion-4 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-5 {
  width: 100%;
  aspect-ratio: 375/234;
  object-fit: cover;
}

.emotion-6 {
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            />
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="product-card-image"
        >
          <img
            alt="beach-family"
            class="emotion-5"
            src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/SUM_210521_M1620_S00086_BIGIDEARangeofSilhouettes1_F_06_0351_d1?fmt=webp"
          />
        </div>
        <a
          aria-label="link aria label"
          class="emotion-6"
          data-testid="conditional-link"
          href="/link"
          target="_self"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`<SpotlightSection /> container image Old Navy Aspect Ratio should match the expected snapshot for mobile hero image size xsmall 1`] = `
.emotion-0 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-1 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-2 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  padding: 6.4vw 4.533333333333333vw;
  gap: 6.4vw;
  pointer-events: none;
}

.emotion-4 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-5 {
  width: 100%;
  aspect-ratio: 375/200;
  object-fit: cover;
}

.emotion-6 {
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            />
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="product-card-image"
        >
          <img
            alt="beach-family"
            class="emotion-5"
            src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/SUM_210521_M1620_S00086_BIGIDEARangeofSilhouettes1_F_06_0351_d1?fmt=webp"
          />
        </div>
        <a
          aria-label="link aria label"
          class="emotion-6"
          data-testid="conditional-link"
          href="/link"
          target="_self"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`<SpotlightSection /> container image Old Navy Aspect Ratio should match the expected snapshot for mobile large 1`] = `
.emotion-0 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-1 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-2 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  padding: 6.4vw 4.533333333333333vw;
  gap: 6.4vw;
  pointer-events: none;
}

.emotion-4 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-5 {
  width: 100%;
  aspect-ratio: 375/468;
  object-fit: cover;
}

.emotion-6 {
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            />
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="product-card-image"
        >
          <img
            alt="beach-family"
            class="emotion-5"
            src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/SUM_210521_M1620_S00086_BIGIDEARangeofSilhouettes1_F_06_0351_d1?fmt=webp"
          />
        </div>
        <a
          aria-label="link aria label"
          class="emotion-6"
          data-testid="conditional-link"
          href="/link"
          target="_self"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`<SpotlightSection /> container image Old Navy Aspect Ratio should match the expected snapshot for mobile medium 1`] = `
.emotion-0 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-1 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-2 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  padding: 6.4vw 4.533333333333333vw;
  gap: 6.4vw;
  pointer-events: none;
}

.emotion-4 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-5 {
  width: 100%;
  aspect-ratio: 375/375;
  object-fit: cover;
}

.emotion-6 {
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            />
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="product-card-image"
        >
          <img
            alt="beach-family"
            class="emotion-5"
            src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/SUM_210521_M1620_S00086_BIGIDEARangeofSilhouettes1_F_06_0351_d1?fmt=webp"
          />
        </div>
        <a
          aria-label="link aria label"
          class="emotion-6"
          data-testid="conditional-link"
          href="/link"
          target="_self"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`<SpotlightSection /> container image Old Navy Aspect Ratio should match the expected snapshot for mobile small 1`] = `
.emotion-0 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-1 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-2 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  padding: 6.4vw 4.533333333333333vw;
  gap: 6.4vw;
  pointer-events: none;
}

.emotion-4 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-5 {
  width: 100%;
  aspect-ratio: 375/234;
  object-fit: cover;
}

.emotion-6 {
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            />
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="product-card-image"
        >
          <img
            alt="beach-family"
            class="emotion-5"
            src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/SUM_210521_M1620_S00086_BIGIDEARangeofSilhouettes1_F_06_0351_d1?fmt=webp"
          />
        </div>
        <a
          aria-label="link aria label"
          class="emotion-6"
          data-testid="conditional-link"
          href="/link"
          target="_self"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`<SpotlightSection /> container image Old Navy Aspect Ratio should match the expected snapshot for mobile xsmall 1`] = `
.emotion-0 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-1 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-2 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  padding: 6.4vw 4.533333333333333vw;
  gap: 6.4vw;
  pointer-events: none;
}

.emotion-4 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-5 {
  width: 100%;
  aspect-ratio: 375/200;
  object-fit: cover;
}

.emotion-6 {
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            />
          </div>
        </div>
        <div
          class="emotion-4"
          data-testid="product-card-image"
        >
          <img
            alt="beach-family"
            class="emotion-5"
            src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/SUM_210521_M1620_S00086_BIGIDEARangeofSilhouettes1_F_06_0351_d1?fmt=webp"
          />
        </div>
        <a
          aria-label="link aria label"
          class="emotion-6"
          data-testid="conditional-link"
          href="/link"
          target="_self"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`<SpotlightSection /> container image overlay should match snapshot when 'on-cta-redesign-2024' feature flag is enabled 1`] = `
.emotion-0 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-1 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: min(24px, 1.6666666666666667vw);
}

.emotion-2 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: min(24px, 1.6666666666666667vw);
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) -0.33%, rgba(0, 0, 0, 0.4) 43.94%, rgba(0, 0, 0, 0) 88.73%);
  background-blend-mode: darken;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 80.83333333333333%;
  padding: min(32px, 2.2222222222222223vw) min(147px, 10.208333333333334vw);
  gap: min(24px, 1.6666666666666667vw);
  pointer-events: none;
}

.emotion-4 {
  text-align: center;
}

.emotion-4 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-4 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-4 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-4 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-4 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-4 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-4 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-4 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.72px);
  font-weight: 500;
}

.emotion-4 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.1111111111111112vw, 16px));
  line-height: 1.5;
  letter-spacing: min(0.044444444444444446vw, 0.64px);
  font-weight: 500;
}

.emotion-4 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9722222222222222vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.03888888888888889vw, 0.56px);
}

.emotion-4 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.8333333333333334vw, 12px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.9444444444444444vw, 28px));
  line-height: 1;
  letter-spacing: min(0.07777777777777778vw, 1.12px);
  font-weight: 500;
}

.emotion-4 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.597222222222222vw, 23px));
  line-height: 1;
  letter-spacing: min(0.0638888888888889vw, 0.92px);
  font-weight: 500;
}

.emotion-4 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1805555555555556vw, 17px));
  line-height: 1;
  letter-spacing: min(0.04722222222222222vw, 0.68px);
  font-weight: 500;
}

.emotion-4 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8.194444444444445vw, 118px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 72px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.3333333333333335vw, 48px));
  line-height: 1;
  letter-spacing: min(0.2vw, 2.88px);
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.7777777777777777vw, 40px));
  line-height: 1.1;
  letter-spacing: min(0.16666666666666666vw, 2.4px);
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.2222222222222223vw, 32px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-4 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-4 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 24px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-4 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5277777777777777vw, 22px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-4 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: auto;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 60px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-6 {
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  max-width: 264px;
}

.emotion-7 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  height: 50px;
  padding-inline: 15px;
}

.emotion-7:focus {
  outline: none;
}

.emotion-7>span {
  padding: 1px 0;
}

.emotion-7:hover,
.emotion-7:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-7:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-7>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-7>span>span {
  position: relative;
  -webkit-transform: translateX(5px);
  -moz-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

.emotion-8 {
  display: inline-block;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-9 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.emotion-9 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-9 svg path {
  fill: currentColor;
}

.emotion-9 svg rect {
  fill: currentColor;
}

.emotion-11 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0 1rem;
  position: absolute;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
}

.emotion-12 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: 1px solid #000000;
}

.emotion-12:last-child {
  border: none;
}

.emotion-13 {
  cursor: pointer;
  display: block;
  padding: 0.5rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: inherit;
  text-align: center;
  color: #000000;
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-64 {
  background: transparent;
  aspect-ratio: 1440/800;
  aspect-ratio: 1440/800;
  width: 100%;
  height: 100%;
}

.emotion-65 {
  height: 100%;
}

.emotion-66 {
  box-sizing: border-box;
  width: 100%;
  position: absolute;
  bottom: 0;
  pointer-events: none;
  padding: 0px 32px 24px 32px;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0) 100%);
  display: grid;
  grid-template-columns: auto auto;
  grid-template-rows: auto auto;
  grid-template-areas: 'top-left top-right' 'bottom-left bottom-right';
  grid-row-gap: 10px;
}

.emotion-67 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  grid-area: bottom-left;
}

.emotion-68 {
  pointer-events: auto;
}

.emotion-68 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-68 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-68 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-68 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-68 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-68 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-68 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-68 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-68 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-68 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-68 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-68 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.72px);
  font-weight: 500;
}

.emotion-68 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.1111111111111112vw, 16px));
  line-height: 1.5;
  letter-spacing: min(0.044444444444444446vw, 0.64px);
  font-weight: 500;
}

.emotion-68 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9722222222222222vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.03888888888888889vw, 0.56px);
}

.emotion-68 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.8333333333333334vw, 12px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-68 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-68 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.9444444444444444vw, 28px));
  line-height: 1;
  letter-spacing: min(0.07777777777777778vw, 1.12px);
  font-weight: 500;
}

.emotion-68 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.597222222222222vw, 23px));
  line-height: 1;
  letter-spacing: min(0.0638888888888889vw, 0.92px);
  font-weight: 500;
}

.emotion-68 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1805555555555556vw, 17px));
  line-height: 1;
  letter-spacing: min(0.04722222222222222vw, 0.68px);
  font-weight: 500;
}

.emotion-68 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8.194444444444445vw, 118px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-68 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-68 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 72px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-68 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-68 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.3333333333333335vw, 48px));
  line-height: 1;
  letter-spacing: min(0.2vw, 2.88px);
  font-weight: 700;
}

.emotion-68 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.7777777777777777vw, 40px));
  line-height: 1.1;
  letter-spacing: min(0.16666666666666666vw, 2.4px);
  font-weight: 700;
}

.emotion-68 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.2222222222222223vw, 32px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-68 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-68 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-68 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-68 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-68 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-68 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-68 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-68 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-68 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-68 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-68 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 24px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-68 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5277777777777777vw, 22px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-68 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-69 {
  grid-area: bottom-right;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: end;
  justify-content: end;
}

.emotion-70 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 0;
  padding: 0;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
}

.emotion-71 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
  text-transform: none;
  font-weight: 500;
  min-height: auto;
  color: #FFFFFF;
  font-size: 12px;
  letter-spacing: 0.24px;
  line-height: normal;
  padding: 0;
}

.emotion-72 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.05px;
  min-height: auto;
  max-height: auto;
  line-height: 1.2857142857142858;
  padding: 0;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #003764;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  text-transform: capitalize;
  text-transform: capitalize;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 10px;
  line-height: normal;
  min-height: 10px;
  letter-spacing: normal;
  padding: 0;
  margin-left: 8px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  pointer-events: auto;
}

.emotion-72:focus {
  outline: none;
}

.emotion-72>span {
  padding: 1px 0;
}

.emotion-72 span span {
  padding-left: 1px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1rem * 0.7);
}

.emotion-72 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #003764;
}

.emotion-72 span span {
  padding-left: initial;
}

.emotion-72:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-72:focus-visible {
  outline: auto;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--headline-3"
                      style="color:#FFFFFF"
                    >
                      Large Headline Goes Here.
                    </span>
                  </p>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--subhead-1"
                      style="color:#FFFFFF"
                    >
                      Subheading goes here.
                    </span>
                  </p>
                </div>
              </div>
              <div
                class="emotion-5"
              >
                <div
                  class="emotion-6"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-7"
                    color="dark"
                  >
                    <span
                      class="emotion-8"
                      data-id="cta-dropdown-label"
                    >
                      Shop All
                      <span
                        aria-hidden="true"
                        class="emotion-9"
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 12 12"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            clip-rule="evenodd"
                            d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                            fill="#000000"
                            fill-rule="evenodd"
                          />
                        </svg>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-10"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-11"
                    >
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Women"
                          target="_self"
                        >
                          Women
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Men"
                          target="_self"
                        >
                          Men
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Girls"
                          target="_self"
                        >
                          Girls
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Boys"
                          target="_self"
                        >
                          Boys
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Toddler Girls"
                          target="_self"
                        >
                          Toddler Girls
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Toddler Boys"
                          target="_self"
                        >
                          Toddler Boys
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Baby Girls"
                          target="_self"
                        >
                          Baby Girls
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Baby Boys"
                          target="_self"
                        >
                          Baby Boys
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div
                  class="emotion-6"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-7"
                    color="dark"
                  >
                    <span
                      class="emotion-8"
                      data-id="cta-dropdown-label"
                    >
                      Shop Pants
                      <span
                        aria-hidden="true"
                        class="emotion-9"
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 12 12"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            clip-rule="evenodd"
                            d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                            fill="#000000"
                            fill-rule="evenodd"
                          />
                        </svg>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-10"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-11"
                    >
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/jeans"
                          target="_self"
                        >
                          Jeans
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/khakis"
                          target="_self"
                        >
                          Khakis
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/sweatpants"
                          target="_self"
                        >
                          Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div
                  class="emotion-6"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-7"
                    color="dark"
                  >
                    <span
                      class="emotion-8"
                      data-id="cta-dropdown-label"
                    >
                      Shop New
                      <span
                        aria-hidden="true"
                        class="emotion-9"
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 12 12"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            clip-rule="evenodd"
                            d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                            fill="#000000"
                            fill-rule="evenodd"
                          />
                        </svg>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-10"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-11"
                    >
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Women"
                          target="_self"
                        >
                          Women
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Men"
                          target="_self"
                        >
                          Men
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Girls"
                          target="_self"
                        >
                          Girls
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Boys"
                          target="_self"
                        >
                          Boys
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Toddler Girls"
                          target="_self"
                        >
                          Toddler Girls
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Toddler Boys"
                          target="_self"
                        >
                          Toddler Boys
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Baby Girls"
                          target="_self"
                        >
                          Baby Girls
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Maternity"
                          target="_self"
                        >
                          Maternity
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Shop for the Fam Fam"
                          target="_self"
                        >
                          Shop for the Fam Fam
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-64"
          height="0"
          width="0"
        />
        <div
          class="emotion-65"
        >
          <div
            class="emotion-66"
          >
            <div
              class="emotion-67"
            >
              <div
                class="emotion-68"
              >
                <div>
                  Handle Text
                </div>
              </div>
            </div>
            <div
              class="emotion-69"
            >
              <div
                class="emotion-70"
              >
                <span
                  class="emotion-71"
                >
                  Prefix Label
                </span>
              </div>
              <button
                class="emotion-72"
              >
                Details Link
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`<SpotlightSection /> container image overlay should match snapshot with default styles 1`] = `
.emotion-0 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-1 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: min(24px, 1.6666666666666667vw);
}

.emotion-2 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: min(24px, 1.6666666666666667vw);
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) -0.33%, rgba(0, 0, 0, 0.4) 43.94%, rgba(0, 0, 0, 0) 88.73%);
  background-blend-mode: darken;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 80.83333333333333%;
  padding: min(32px, 2.2222222222222223vw) min(147px, 10.208333333333334vw);
  gap: min(24px, 1.6666666666666667vw);
  pointer-events: none;
}

.emotion-4 {
  text-align: center;
}

.emotion-4 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-4 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-4 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-4 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-4 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-4 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-4 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-4 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.72px);
  font-weight: 500;
}

.emotion-4 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.1111111111111112vw, 16px));
  line-height: 1.5;
  letter-spacing: min(0.044444444444444446vw, 0.64px);
  font-weight: 500;
}

.emotion-4 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9722222222222222vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.03888888888888889vw, 0.56px);
}

.emotion-4 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.8333333333333334vw, 12px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.9444444444444444vw, 28px));
  line-height: 1;
  letter-spacing: min(0.07777777777777778vw, 1.12px);
  font-weight: 500;
}

.emotion-4 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.597222222222222vw, 23px));
  line-height: 1;
  letter-spacing: min(0.0638888888888889vw, 0.92px);
  font-weight: 500;
}

.emotion-4 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1805555555555556vw, 17px));
  line-height: 1;
  letter-spacing: min(0.04722222222222222vw, 0.68px);
  font-weight: 500;
}

.emotion-4 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8.194444444444445vw, 118px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 72px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.3333333333333335vw, 48px));
  line-height: 1;
  letter-spacing: min(0.2vw, 2.88px);
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.7777777777777777vw, 40px));
  line-height: 1.1;
  letter-spacing: min(0.16666666666666666vw, 2.4px);
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.2222222222222223vw, 32px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-4 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-4 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 24px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-4 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5277777777777777vw, 22px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-4 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: auto;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 60px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-6 {
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  max-width: 264px;
}

.emotion-7 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  height: 50px;
  padding-inline: 15px;
}

.emotion-7:focus {
  outline: none;
}

.emotion-7>span {
  padding: 1px 0;
}

.emotion-7:hover,
.emotion-7:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-7:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-7>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-7>span>span {
  position: relative;
  -webkit-transform: translateX(5px);
  -moz-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

.emotion-8 {
  display: inline-block;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-9 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.emotion-9 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-9 svg path {
  fill: currentColor;
}

.emotion-9 svg rect {
  fill: currentColor;
}

.emotion-11 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0 1rem;
  position: absolute;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
}

.emotion-12 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: 1px solid #000000;
}

.emotion-12:last-child {
  border: none;
}

.emotion-13 {
  cursor: pointer;
  display: block;
  padding: 0.5rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: inherit;
  text-align: center;
  color: #000000;
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-64 {
  background: transparent;
  aspect-ratio: 1440/800;
  aspect-ratio: 1440/800;
  width: 100%;
  height: 100%;
}

.emotion-65 {
  height: 100%;
}

.emotion-66 {
  box-sizing: border-box;
  width: 100%;
  position: absolute;
  bottom: 0;
  pointer-events: none;
  padding: 0px 32px 24px 32px;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0) 100%);
  display: grid;
  grid-template-columns: auto auto;
  grid-template-rows: auto auto;
  grid-template-areas: 'top-left top-right' 'bottom-left bottom-right';
  grid-row-gap: 10px;
}

.emotion-67 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  grid-area: bottom-left;
}

.emotion-68 {
  pointer-events: auto;
}

.emotion-68 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-68 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-68 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-68 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-68 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-68 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-68 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-68 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-68 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-68 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-68 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-68 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.72px);
  font-weight: 500;
}

.emotion-68 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.1111111111111112vw, 16px));
  line-height: 1.5;
  letter-spacing: min(0.044444444444444446vw, 0.64px);
  font-weight: 500;
}

.emotion-68 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9722222222222222vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.03888888888888889vw, 0.56px);
}

.emotion-68 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.8333333333333334vw, 12px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-68 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-68 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.9444444444444444vw, 28px));
  line-height: 1;
  letter-spacing: min(0.07777777777777778vw, 1.12px);
  font-weight: 500;
}

.emotion-68 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.597222222222222vw, 23px));
  line-height: 1;
  letter-spacing: min(0.0638888888888889vw, 0.92px);
  font-weight: 500;
}

.emotion-68 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1805555555555556vw, 17px));
  line-height: 1;
  letter-spacing: min(0.04722222222222222vw, 0.68px);
  font-weight: 500;
}

.emotion-68 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8.194444444444445vw, 118px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-68 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-68 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 72px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-68 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-68 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.3333333333333335vw, 48px));
  line-height: 1;
  letter-spacing: min(0.2vw, 2.88px);
  font-weight: 700;
}

.emotion-68 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.7777777777777777vw, 40px));
  line-height: 1.1;
  letter-spacing: min(0.16666666666666666vw, 2.4px);
  font-weight: 700;
}

.emotion-68 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.2222222222222223vw, 32px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-68 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-68 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-68 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-68 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-68 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-68 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-68 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-68 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-68 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-68 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-68 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 24px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-68 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5277777777777777vw, 22px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-68 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-69 {
  grid-area: bottom-right;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: end;
  justify-content: end;
}

.emotion-70 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 0;
  padding: 0;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
}

.emotion-71 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
  text-transform: none;
  font-weight: 500;
  min-height: auto;
  color: #FFFFFF;
  font-size: 12px;
  letter-spacing: 0.24px;
  line-height: normal;
  padding: 0;
}

.emotion-72 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.05px;
  min-height: auto;
  max-height: auto;
  line-height: 1.2857142857142858;
  padding: 0;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #003764;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  text-transform: capitalize;
  text-transform: capitalize;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 10px;
  line-height: normal;
  min-height: 10px;
  letter-spacing: normal;
  padding: 0;
  margin-left: 8px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  pointer-events: auto;
}

.emotion-72:focus {
  outline: none;
}

.emotion-72>span {
  padding: 1px 0;
}

.emotion-72 span span {
  padding-left: 1px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1rem * 0.7);
}

.emotion-72 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #003764;
}

.emotion-72 span span {
  padding-left: initial;
}

.emotion-72:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-72:focus-visible {
  outline: auto;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--headline-3"
                      style="color:#FFFFFF"
                    >
                      Large Headline Goes Here.
                    </span>
                  </p>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--subhead-1"
                      style="color:#FFFFFF"
                    >
                      Subheading goes here.
                    </span>
                  </p>
                </div>
              </div>
              <div
                class="emotion-5"
              >
                <div
                  class="emotion-6"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-7"
                    color="dark"
                  >
                    <span
                      class="emotion-8"
                      data-id="cta-dropdown-label"
                    >
                      Shop All
                      <span
                        aria-hidden="true"
                        class="emotion-9"
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 12 12"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            clip-rule="evenodd"
                            d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                            fill="#000000"
                            fill-rule="evenodd"
                          />
                        </svg>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-10"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-11"
                    >
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Women"
                          target="_self"
                        >
                          Women
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Men"
                          target="_self"
                        >
                          Men
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Girls"
                          target="_self"
                        >
                          Girls
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Boys"
                          target="_self"
                        >
                          Boys
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Toddler Girls"
                          target="_self"
                        >
                          Toddler Girls
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Toddler Boys"
                          target="_self"
                        >
                          Toddler Boys
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Baby Girls"
                          target="_self"
                        >
                          Baby Girls
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Baby Boys"
                          target="_self"
                        >
                          Baby Boys
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div
                  class="emotion-6"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-7"
                    color="dark"
                  >
                    <span
                      class="emotion-8"
                      data-id="cta-dropdown-label"
                    >
                      Shop Pants
                      <span
                        aria-hidden="true"
                        class="emotion-9"
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 12 12"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            clip-rule="evenodd"
                            d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                            fill="#000000"
                            fill-rule="evenodd"
                          />
                        </svg>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-10"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-11"
                    >
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/jeans"
                          target="_self"
                        >
                          Jeans
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/khakis"
                          target="_self"
                        >
                          Khakis
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/sweatpants"
                          target="_self"
                        >
                          Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div
                  class="emotion-6"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-7"
                    color="dark"
                  >
                    <span
                      class="emotion-8"
                      data-id="cta-dropdown-label"
                    >
                      Shop New
                      <span
                        aria-hidden="true"
                        class="emotion-9"
                      >
                        <svg
                          fill="none"
                          viewBox="0 0 12 12"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            clip-rule="evenodd"
                            d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                            fill="#000000"
                            fill-rule="evenodd"
                          />
                        </svg>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-10"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-11"
                    >
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Women"
                          target="_self"
                        >
                          Women
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Men"
                          target="_self"
                        >
                          Men
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Girls"
                          target="_self"
                        >
                          Girls
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Boys"
                          target="_self"
                        >
                          Boys
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Toddler Girls"
                          target="_self"
                        >
                          Toddler Girls
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Toddler Boys"
                          target="_self"
                        >
                          Toddler Boys
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Baby Girls"
                          target="_self"
                        >
                          Baby Girls
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Maternity"
                          target="_self"
                        >
                          Maternity
                        </a>
                      </li>
                      <li
                        class="emotion-12"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-13"
                          href="/Shop for the Fam Fam"
                          target="_self"
                        >
                          Shop for the Fam Fam
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-64"
          height="0"
          width="0"
        />
        <div
          class="emotion-65"
        >
          <div
            class="emotion-66"
          >
            <div
              class="emotion-67"
            >
              <div
                class="emotion-68"
              >
                <div>
                  Handle Text
                </div>
              </div>
            </div>
            <div
              class="emotion-69"
            >
              <div
                class="emotion-70"
              >
                <span
                  class="emotion-71"
                >
                  Prefix Label
                </span>
              </div>
              <button
                class="emotion-72"
              >
                Details Link
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
