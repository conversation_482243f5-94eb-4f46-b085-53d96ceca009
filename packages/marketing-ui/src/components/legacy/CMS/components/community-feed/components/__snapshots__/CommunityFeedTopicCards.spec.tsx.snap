// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CommunityFeedTopicCards snapshot tests should match snapshots for large breakpoint 1`] = `
<DocumentFragment>
  .emotion-0 {
  overflow: unset;
}

.emotion-1 {
  overflow: visible;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 20px;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  width: calc(33%);
}

.emotion-3 {
  position: absolute;
  width: 100%;
  height: 100%;
  color: transparent;
}

.emotion-4 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-5 {
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: cover;
}

.emotion-6 {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  margin-top: 20px;
  max-width: 100%;
}

.emotion-6 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-6 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-6 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-6 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-6 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.9375vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-6 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-6 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.9375vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.9375vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.9375vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.9375vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.5625vw);
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.40625vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.25vw);
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.09375vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.9375vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 3.90625vw);
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 3.125vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.734375vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 9.375vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 8.59375vw);
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 7.8125vw);
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 7.8125vw);
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.25vw);
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 5.46875vw);
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.6875vw);
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, 8.90625vw);
  line-height: 1;
  letter-spacing: -0.03125vw;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, 6.5625vw);
  line-height: 1;
  letter-spacing: -0.03125vw;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, 5vw);
  line-height: 1;
  letter-spacing: -0.03125vw;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, 2.65625vw);
  line-height: 1;
  letter-spacing: -0.03125vw;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 1.25vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 1.25vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 1.25vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 7.8125vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 6.25vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 1.25vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-6 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 2.34375vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.953125vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.5625vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-18 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: calc(33%);
}

.emotion-19 {
  position: absolute;
  width: 100%;
  height: 100%;
  color: transparent;
}

.emotion-20 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  gap: 20px;
  margin-top: 20px;
}

.emotion-21 {
  width: 2.430555555555556vw;
  height: 2.430555555555556vw;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-clip-path: circle();
  clip-path: circle();
}

.emotion-22 {
  width: 100%;
  object-fit: cover;
}

.emotion-23 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-23 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-23 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-23 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-23 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.9375vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-23 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-23 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-23 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.9375vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.9375vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.9375vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.9375vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.5625vw);
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.40625vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.25vw);
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.09375vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.9375vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 3.90625vw);
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 3.125vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.734375vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 9.375vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 8.59375vw);
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 7.8125vw);
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 7.8125vw);
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.25vw);
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 5.46875vw);
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.6875vw);
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, 8.90625vw);
  line-height: 1;
  letter-spacing: -0.03125vw;
  font-weight: 400;
}

.emotion-23 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, 6.5625vw);
  line-height: 1;
  letter-spacing: -0.03125vw;
  font-weight: 400;
}

.emotion-23 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, 5vw);
  line-height: 1;
  letter-spacing: -0.03125vw;
  font-weight: 400;
}

.emotion-23 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, 2.65625vw);
  line-height: 1;
  letter-spacing: -0.03125vw;
  font-weight: 400;
}

.emotion-23 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 1.25vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-23 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 1.25vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-23 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 1.25vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-23 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 7.8125vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-23 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 6.25vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-23 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 1.25vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-23 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 2.34375vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.953125vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.5625vw);
  line-height: 1.1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <section
          class="emotion-2"
        >
          <a
            class="emotion-3"
            href="/test-url-1"
          >
            This is the card1 link
          </a>
          <div
            class="emotion-4"
            data-testid="product-card-image"
          >
            <img
              alt="imagealto"
              class="emotion-5"
              src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/169105862_466264801286952_7673584469792986764_n?fmt=webp"
            />
          </div>
          <div
            class="emotion-6"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--body-1"
                >
                  Topic 1 Topic 1Topic 1Topic 1 Topic 1 Topic 1 Topic 1 Topic 1
                </span>
              </p>
            </div>
          </div>
        </section>
        <section
          class="emotion-2"
        >
          <a
            class="emotion-3"
            href="/test-url-2"
          />
          <div
            class="emotion-4"
            data-testid="product-card-image"
          >
            <img
              alt="imagealt"
              class="emotion-5"
              src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/joggers?fmt=webp"
            />
          </div>
          <div
            class="emotion-6"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--body-1"
                >
                  Topic 2
                </span>
              </p>
            </div>
          </div>
        </section>
        <section
          class="emotion-2"
        >
          <a
            class="emotion-3"
          />
          <div
            class="emotion-4"
            data-testid="product-card-image"
          >
            <img
              alt="imagealt3"
              class="emotion-5"
              src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/MicrosoftTeams-image (5)?fmt=webp"
            />
          </div>
          <div
            class="emotion-6"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--body-1"
                >
                  Topic 3
                </span>
              </p>
            </div>
          </div>
        </section>
      </div>
      <div
        class="emotion-1"
      >
        <section
          class="emotion-18"
        >
          <a
            class="emotion-19"
          />
          <div
            class="emotion-20"
          >
            <div
              class="emotion-21"
              data-testid="product-card-image"
            >
              <img
                alt="imagealt"
                class="emotion-22"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/CMS_HOL22_D1_ATG_Tops_S?fmt=webp"
              />
            </div>
            <div
              class="emotion-23"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Author Info
                  </span>
                </p>
              </div>
            </div>
          </div>
        </section>
        <section
          class="emotion-18"
        >
          <a
            class="emotion-19"
          />
          <div
            class="emotion-20"
          >
            <div
              class="emotion-21"
              data-testid="product-card-image"
            >
              <img
                alt="imagealt2"
                class="emotion-22"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/HOL1_Winter-Essentials_S?fmt=webp"
              />
            </div>
            <div
              class="emotion-23"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Author info 2
                  </span>
                </p>
              </div>
            </div>
          </div>
        </section>
        <section
          class="emotion-18"
        >
          <a
            class="emotion-19"
          />
          <div
            class="emotion-20"
          >
            <div
              class="emotion-21"
              data-testid="product-card-image"
            >
              <img
                alt="imagealt3"
                class="emotion-22"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/SPRING1_22_Yoga&Studio_ISM_XL_2x?fmt=webp"
              />
            </div>
            <div
              class="emotion-23"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Author Info 3
                  </span>
                </p>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`CommunityFeedTopicCards snapshot tests should match snapshots for small breakpoint 1`] = `
<DocumentFragment>
  .emotion-0 {
  overflow: auto;
}

.emotion-1 {
  overflow: visible;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 10px;
  margin-left: -10px;
  padding-left: 10px;
  padding-right: 10px;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  min-width: calc(75%);
}

.emotion-3 {
  position: absolute;
  width: 100%;
  height: 100%;
  color: transparent;
}

.emotion-4 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-5 {
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: cover;
}

.emotion-6 {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  margin-top: 16px;
  max-width: 100%;
}

.emotion-6 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-6 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-6 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-6 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-6 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-6 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-6 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-6 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.266666666666667vw);
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.733333333333334vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.2vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 8vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 6.933333333333333vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 6.4vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 16vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 14.666666666666666vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 14.666666666666666vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 13.333333333333334vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 12vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 9.333333333333334vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(13px, 17.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(13px, 14.399999999999999vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(13px, 9.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(13px, 6.4vw);
  line-height: 1.4166666666666667;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 16vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-6 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.866666666666666vw);
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.333333333333334vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 4.8vw);
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-18 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  min-width: calc(75%);
}

.emotion-19 {
  position: absolute;
  width: 100%;
  height: 100%;
  color: transparent;
}

.emotion-20 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  gap: 14px;
  margin-top: 16px;
}

.emotion-21 {
  width: 6.4vw;
  height: 6.4vw;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-clip-path: circle();
  clip-path: circle();
}

.emotion-22 {
  object-fit: fill;
  width: 100%;
  height: 100%;
}

.emotion-23 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-23 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-23 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-23 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-23 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-23 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-23 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-23 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.266666666666667vw);
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.733333333333334vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.2vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 8vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 6.933333333333333vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 6.4vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 16vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 14.666666666666666vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 14.666666666666666vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 13.333333333333334vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 12vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 9.333333333333334vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(13px, 17.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-23 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(13px, 14.399999999999999vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-23 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(13px, 9.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-23 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(13px, 6.4vw);
  line-height: 1.4166666666666667;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-23 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-23 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-23 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-23 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 16vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-23 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-23 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-23 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.866666666666666vw);
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.333333333333334vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-23 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 4.8vw);
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <section
          class="emotion-2"
        >
          <a
            class="emotion-3"
            href="/test-url-1"
          >
            This is the card1 link
          </a>
          <div
            class="emotion-4"
            data-testid="product-card-image"
          >
            <img
              alt="imagealto"
              class="emotion-5"
              src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/169105862_466264801286952_7673584469792986764_n?fmt=webp"
            />
          </div>
          <div
            class="emotion-6"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--body-1"
                >
                  Topic 1 Topic 1Topic 1Topic 1 Topic 1 Topic 1 Topic 1 Topic 1
                </span>
              </p>
            </div>
          </div>
        </section>
        <section
          class="emotion-2"
        >
          <a
            class="emotion-3"
            href="/test-url-2"
          />
          <div
            class="emotion-4"
            data-testid="product-card-image"
          >
            <img
              alt="imagealt"
              class="emotion-5"
              src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/joggers?fmt=webp"
            />
          </div>
          <div
            class="emotion-6"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--body-1"
                >
                  Topic 2
                </span>
              </p>
            </div>
          </div>
        </section>
        <section
          class="emotion-2"
        >
          <a
            class="emotion-3"
          />
          <div
            class="emotion-4"
            data-testid="product-card-image"
          >
            <img
              alt="imagealt3"
              class="emotion-5"
              src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/MicrosoftTeams-image (5)?fmt=webp"
            />
          </div>
          <div
            class="emotion-6"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--body-1"
                >
                  Topic 3
                </span>
              </p>
            </div>
          </div>
        </section>
      </div>
      <div
        class="emotion-1"
      >
        <section
          class="emotion-18"
        >
          <a
            class="emotion-19"
          />
          <div
            class="emotion-20"
          >
            <div
              class="emotion-21"
              data-testid="product-card-image"
            >
              <canvas
                aria-label="imagealt"
                class="emotion-22"
                role="img"
              />
            </div>
            <div
              class="emotion-23"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Author Info
                  </span>
                </p>
              </div>
            </div>
          </div>
        </section>
        <section
          class="emotion-18"
        >
          <a
            class="emotion-19"
          />
          <div
            class="emotion-20"
          >
            <div
              class="emotion-21"
              data-testid="product-card-image"
            >
              <canvas
                aria-label="imagealt2"
                class="emotion-22"
                role="img"
              />
            </div>
            <div
              class="emotion-23"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Author info 2
                  </span>
                </p>
              </div>
            </div>
          </div>
        </section>
        <section
          class="emotion-18"
        >
          <a
            class="emotion-19"
          />
          <div
            class="emotion-20"
          >
            <div
              class="emotion-21"
              data-testid="product-card-image"
            >
              <canvas
                aria-label="imagealt3"
                class="emotion-22"
                role="img"
              />
            </div>
            <div
              class="emotion-23"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Author Info 3
                  </span>
                </p>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  </div>
</DocumentFragment>
`;
