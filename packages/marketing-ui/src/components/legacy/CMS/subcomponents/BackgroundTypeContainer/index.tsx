// @ts-nocheck
'use client';
import React, { HTMLProps, useRef } from 'react';
import { Brands, CSSObject, styled, useEnabledFeatures, useTheme } from '@ecom-next/core/react-stitch';
import { useDebouncedState } from '../../../hooks/useDebouncedState';
import { useResizeObserver } from '../../../hooks/useResizeObserver';
import { generateDualImageBackgroundCSS, useGenerateBackgroundTypeCSSWithAspectRatio } from '../../helpers/CSSHelpers';
import { AdvanceImageData, BackgroundTypeExtensionValue } from '../../global/types';
import { useViewportIsXLarge } from '../../../hooks/useViewportIsLarge';
import { AmplienceResponsiveImage } from '../AmplienceResponsiveImage';
import { useEncodeAdvanceImage } from '../../helpers/ImageEncoder/useEncodeAdvanceImage';

type BackgroundStyleProps = {
  background?: BackgroundTypeExtensionValue;
  scale?: number;
  height?: number;
  width?: number;
  isDesktop?: boolean;
  hasPlpGrid2025?: boolean;
};

export const getAltText = (first: AdvanceImageData, second?: AdvanceImageData) => {
  const altText: string[] = [];
  if (first?.altText) {
    altText.push(first.altText);
  }
  if (second?.altText) {
    altText.push(second.altText);
  }

  return altText;
};

const StyledBackgroundTypeContainerDiv = styled.div<BackgroundStyleProps>(props => {
  const isDesktop = props.isDesktop === undefined ? useViewportIsXLarge() : props.isDesktop;
  const theme = useTheme();
  const isGapBrand = theme.brand === Brands.Gap || theme.brand === Brands.GapFactoryStore;

  if (props.hasPlpGrid2025 && isGapBrand && (props.height === 0 || props.width === 0)) {
    return null;
  }
  return {
    ...useGenerateBackgroundTypeCSSWithAspectRatio({
      ...props,
      viewport: isDesktop ? 'desktop' : 'mobile',
    }),
  };
});

export interface BackgroundTypeContainerProps extends HTMLProps<HTMLDivElement> {
  aspectRatio?: string;
  background?: BackgroundTypeExtensionValue;
  className?: string;
  customCss?: CSSObject;
  fetchPriority?: HTMLImageElement['fetchPriority'];
  height?: number;
  width?: number;
  scale?: number;
  gradientAngle?: number;
  onResize?: Function;
  isDesktop?: boolean;
  isAvatar?: boolean;
}

const BackgroundTypeContainer = (props: BackgroundTypeContainerProps) => {
  const { aspectRatio, background, className, children, customCss, onResize, isDesktop, isAvatar, height, scale, width, ...remainingProps } = props;

  const enabledFeatures = useEnabledFeatures();
  const hasPlpGrid2025 = !!enabledFeatures?.['mui-new-plp-grid-ism-2025'];

  const divProps: React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> = {
    ...remainingProps,
  };

  const divRef = useRef<HTMLDivElement | null>(null);
  const [divHeight, setDivHeight] = useDebouncedState<number>(0, 500);
  const [divWidth, setDivWidth] = useDebouncedState<number>(0, 500);

  const backgroundImageProperties = background?.images?.[0] ?? {
    variations: [],
  };
  const generatedbackgroundImageProperties = generateDualImageBackgroundCSS(
    useEncodeAdvanceImage(backgroundImageProperties, isDesktop ? 'desktop' : 'mobile', {
      aspect: aspectRatio,
      height,
      scale,
    }) ?? ''
  );

  const { background: myImage, ...rest } = generatedbackgroundImageProperties;

  if (background?.type === 'image' && background.images && background.images.length > 0) {
    const [first, second] = background.images;
    const altText = getAltText(first, second);

    /* istanbul ignore if */
    if (altText.length > 0) {
      divProps['aria-label'] = altText.join(' - ');
      divProps.role = 'img';
    }
  }

  /* istanbul ignore next */
  const resizeCallback = (entries: ResizeObserverEntry[]) => {
    const elementHeight = entries[0].contentRect.height;
    const elementWidth = entries[0].contentRect.width;
    if (onResize) {
      onResize(elementHeight);
    }

    setDivHeight(elementHeight);
    setDivWidth(elementWidth);
  };

  useResizeObserver(divRef, resizeCallback);

  const containerClipWidth = isAvatar
    ? {
        width: `${props?.width}vw`,
        height: `${props?.height}vw`,
        display: 'flex',
        clipPath: 'circle()',
      }
    : {
        ...rest,
      };

  return (
    <>
      {background?.type === 'image' && background?.images?.[0] && children === undefined ? (
        <div
          ref={divRef}
          css={{
            ...containerClipWidth,
            ...customCss,
          }}
          data-testid='product-card-image'
        >
          <AmplienceResponsiveImage
            aspectRatio={aspectRatio}
            customContentContainerStyles={{ width: '100%', height: '100%' }}
            displaySize={{ width: divWidth, height: divHeight }}
            imageData={background?.images?.[0]}
            isAvatar={isAvatar}
          />
        </div>
      ) : (
        <StyledBackgroundTypeContainerDiv
          ref={divRef}
          background={background}
          className={className}
          css={{ aspectRatio }}
          isDesktop={isDesktop}
          hasPlpGrid2025={hasPlpGrid2025}
          {...divProps}
          {...{ height: divHeight }}
          {...{ width: divWidth }}
        >
          {children}
        </StyledBackgroundTypeContainerDiv>
      )}
    </>
  );
};

export default BackgroundTypeContainer;
