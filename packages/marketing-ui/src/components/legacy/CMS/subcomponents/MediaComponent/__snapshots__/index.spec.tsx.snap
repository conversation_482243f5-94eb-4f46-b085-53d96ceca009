// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`MediaComponent should render a non vimeo video with controls (play/pause and mute/sound icons) 1`] = `
.emotion-0 {
  position: relative;
  height: 100%;
  pointer-events: auto;
  aspect-ratio: 9/5;
}

.emotion-2 {
  background: none;
  border: none;
  padding: 0;
  height: 24px;
  margin: 0;
  position: absolute;
  bottom: 8px;
  right: 112px;
  width: 24px;
  height: 24px;
}

.emotion-2:is(:not(:focus, :active, :hover, :focus-within)) {
  opacity: 0.64;
}

.emotion-2:is(:hover) {
  opacity: 1;
}

@media (min-width: 767px) {
  .emotion-2 {
    right: 120px;
  }
}

.emotion-3 {
  box-sizing: border-box;
  background: none;
  border: none;
  padding: 0;
  position: relative;
  width: 100%;
  height: 0px;
}

.emotion-3:focus div {
  opacity: 0.84;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  border-radius: 20px 20px 0 0;
}

.emotion-3 div {
  border-radius: 20px 20px 0 0;
}

.emotion-3 .keepOpen,
.emotion-3 .staysOpen {
  opacity: 0.84;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  z-index: 2;
}

.emotion-4 {
  background: none;
  border: none;
  padding: 0;
  height: 24px;
  margin: 0;
  position: absolute;
  bottom: 8px;
  right: 72px;
  z-index: 11;
}

.emotion-4:is(:not(:focus, :active, :hover, :focus-within)) {
  opacity: 0.64;
}

.emotion-4:is(:hover) {
  opacity: 1;
}

@media (min-width: 767px) {
  .emotion-4 {
    right: 80px;
  }
}

.emotion-5 {
  opacity: 0;
  -webkit-transition: opacity 200ms;
  transition: opacity 200ms;
  box-sizing: border-box;
  background: #ffffff;
  border: none;
  padding: 0;
  position: absolute;
  width: 24px;
  height: 60px;
  bottom: 32px;
  right: 80px;
  border-radius: 20px 20px 0 0;
}

.emotion-5:before {
  content: "";
  height: 24px;
  background: #ffffff;
  display: block;
  position: absolute;
  bottom: -24px;
  width: 24px;
  border-bottom-right-radius: 50%;
  border-bottom-left-radius: 50%;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        data-testid="videocomponent-container"
        style="height: 100%; width: 100%; position: relative;"
      >
        <div
          style="height: 100%; position: relative; z-index: 0;"
        >
          <img
            alt="alt"
            decoding="sync"
            fetchpriority="high"
            loading="eager"
            src="https://1oaaw8uqqaz0o1u33g36rq1glc.staging.bigcontent.io/i/bananarepublic/BRSP240529_SITE_USCA_HP_06_IMG_XL?fmt=auto"
            style="height: 100%; left: 0px; object-fit: cover; position: absolute; top: 0px; width: 100%; z-index: -1; opacity: 0; transition: opacity 0.3s ease-in-out; pointer-events: none;"
          />
          <h1
            role="presentation"
          >
            ReactPlayer
          </h1>
        </div>
        <div
          class="player-custom-controls emotion-1"
          data-testid="player-custom-controls"
        >
          <button
            aria-label="Play"
            aria-pressed="false"
            class="emotion-2"
          >
            <svg
              aria-label="play-button"
              fill="none"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                fill="#FFFFFF"
              />
              <path
                d="M7.5 7.43526C7.5 6.78726 8.19225 6.37326 8.76375 6.68076L17.241 11.246C17.3771 11.3194 17.4909 11.4283 17.5702 11.5612C17.6494 11.694 17.6913 11.8458 17.6913 12.0005C17.6913 12.1552 17.6494 12.307 17.5702 12.4398C17.4909 12.5727 17.3771 12.6816 17.241 12.755L8.76375 17.3203C8.63317 17.3906 8.48659 17.4258 8.33831 17.4226C8.19004 17.4193 8.04515 17.3776 7.91781 17.3016C7.79046 17.2255 7.68502 17.1178 7.61179 16.9888C7.53856 16.8598 7.50004 16.7141 7.5 16.5658V7.43526Z"
                fill="#2C2824"
              />
            </svg>
          </button>
          <div
            class="emotion-3"
            data-testid="styled-mute-controls"
          >
            <button
              aria-label="Mute"
              aria-pressed="false"
              class="emotion-4"
            >
              <svg
                aria-label="mute-button"
                fill="none"
                height="24"
                viewBox="0 0 24 24"
                width="24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                  fill="#FFFFFF"
                />
                <g>
                  <path
                    clip-rule="evenodd"
                    d="M14.4996 14.2613C15.7486 13.0124 15.7486 10.9876 14.4996 9.73866C14.28 9.51899 14.28 9.16283 14.4996 8.94316C14.7193 8.72349 15.0755 8.72349 15.2951 8.94316C16.9835 10.6315 16.9835 13.3685 15.2951 15.0568C15.0755 15.2765 14.7193 15.2765 14.4996 15.0568C14.28 14.8372 14.28 14.481 14.4996 14.2613Z"
                    fill="#2C2824"
                    fill-rule="evenodd"
                  />
                  <path
                    clip-rule="evenodd"
                    d="M16.4087 16.034C18.6368 13.8059 18.6368 10.1939 16.4087 7.96586C16.1891 7.74619 16.1891 7.39003 16.4087 7.17036C16.6284 6.95069 16.9846 6.95069 17.2042 7.17036C19.8716 9.83778 19.8716 14.1621 17.2042 16.8295C16.9846 17.0492 16.6284 17.0492 16.4087 16.8295C16.1891 16.6098 16.1891 16.2537 16.4087 16.034Z"
                    fill="#2C2824"
                    fill-rule="evenodd"
                  />
                  <path
                    d="M4.7383 12.8103V12V11.1897C4.7383 10.6374 5.18602 10.1897 5.7383 10.1897H7.14083C7.39437 10.1897 7.63844 10.0934 7.82366 9.92023L11.0042 6.94759C11.14 6.82063 11.319 6.75 11.5049 6.75C11.91 6.75 12.2383 7.07834 12.2383 7.48337V12V16.5166C12.2383 16.9217 11.91 17.25 11.5049 17.25C11.319 17.25 11.14 17.1794 11.0042 17.0524L7.82366 14.0798C7.63844 13.9066 7.39437 13.8103 7.14083 13.8103H5.7383C5.18602 13.8103 4.7383 13.3626 4.7383 12.8103Z"
                    fill="#2C2824"
                  />
                  <rect
                    fill="#2C2824"
                    height="19.3235"
                    transform="rotate(120 21.2347 16.4121)"
                    width="1.5"
                    x="21.2347"
                    y="16.4121"
                  />
                </g>
              </svg>
            </button>
            <div
              class="emotion-5"
              data-testid="styled-input-volume"
              style="background: rgb(255, 255, 255);"
            >
              <div
                role="presentation"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`MediaComponent should render background 1`] = `
<DocumentFragment>
  .emotion-0 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-1 {
  width: 100%;
  aspect-ratio: 4/3;
  object-fit: cover;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="product-card-image"
    >
      <img
        alt="image showing a person"
        class="emotion-1"
        src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/Nav4?fmt=webp"
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`MediaComponent should render carousel in desktop 1`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 100%;
  width: 100%;
  aspect-ratio: 4/1;
}

.emotion-1 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-1 .slick-list {
  overflow: hidden;
}

.emotion-1 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-1 button.slick-next.slick-arrow.slick-next,
.emotion-1 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: undefined;
  height: 44px;
  width: 44px;
}

.emotion-1 button.slick-next.slick-arrow.slick-next>span,
.emotion-1 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-1 button.slick-next.slick-arrow.slick-next svg,
.emotion-1 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-1 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-1 .slick-disabled {
  display: none!important;
}

.emotion-1 .slick-next {
  left: calc(100% - 44px);
}

.emotion-1 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-1 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-2 {
  position: relative;
}

.emotion-2 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-2 .slick-slider .slick-track,
.emotion-2 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-2 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-2 .slick-list:focus {
  outline: none;
}

.emotion-2 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-2 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-2 .slick-track:before,
.emotion-2 .slick-track:after {
  display: table;
  content: "";
}

.emotion-2 .slick-track:after {
  clear: both;
}

.emotion-2 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-2 .slick-slide img {
  display: block;
}

.emotion-2 .slick-slide.slick-loading img {
  display: none;
}

.emotion-2 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-2 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-2 .slick-initialized .slick-slide,
.emotion-2 .slick-vertical .slick-slide {
  display: block;
}

.emotion-2 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-2 .slick-loading .slick-track,
.emotion-2 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-2 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-2 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-2 .slick-prev,
.emotion-2 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-2 .slick-prev:hover,
.emotion-2 .slick-next:hover,
.emotion-2 .slick-prev:focus,
.emotion-2 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-2 .slick-prev.slick-disabled,
.emotion-2 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-2 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-2 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-2 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-2 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-2 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-2 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-2 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-2 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-2 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-2 .slick-dots li button:hover,
.emotion-2 .slick-dots li button:focus {
  outline: none;
}

.emotion-2 .slick-dots li button:hover:before,
.emotion-2 .slick-dots li button:focus:before,
.emotion-2 .slick-dots li button:hover:before,
.emotion-2 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-2 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-3 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-4 {
  cursor: pointer;
  height: 100%;
  width: 100%;
}

.emotion-4:focus-visible {
  outline-offset: -5px;
  outline: auto;
}

.emotion-4:focus-visible:has(canvas) canvas {
  position: relative;
  z-index: -1;
}

.emotion-5 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-6 {
  width: 100%;
  aspect-ratio: 4/1;
  object-fit: cover;
}

.emotion-9 {
  width: 100%;
  height: 100%;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <nav
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div
            class="slick-slider slick-initialized"
            dir="ltr"
          >
            <button
              aria-label="Previous"
              class="slick-prev slick-arrow slick-prev slick-disabled"
              data-role="none"
              disabled=""
            >
              <span
                aria-hidden="true"
                class="emotion-3"
              >
                <svg
                  viewBox="0 0 18 7.742"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                    fill="#2B2B2B"
                    transform="translate(18) rotate(90)"
                  />
                </svg>
              </span>
            </button>
            <div
              class="slick-list"
            >
              <div
                class="slick-track"
                style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
              >
                <div
                  aria-hidden="false"
                  class="slick-slide slick-active slick-current"
                  data-index="0"
                  style="outline: none; width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <a
                      aria-label="frame 1 test banner"
                      class="emotion-4"
                      data-testid="conditional-link"
                      href="#linkwrapper1"
                      target="_self"
                    >
                      <div
                        class="emotion-5"
                        data-testid="product-card-image"
                      >
                        <img
                          alt=""
                          class="emotion-6"
                          src="https://1d9xxafvh577y12766tt2karod.staging.bigcontent.io/i/athleta/ATG_DP_XL_Testing2?fmt=webp"
                        />
                      </div>
                    </a>
                  </div>
                </div>
                <div
                  aria-hidden="true"
                  class="slick-slide"
                  data-index="1"
                  style="outline: none; width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <a
                      aria-label="frame 2 test banner"
                      class="emotion-4"
                      data-testid="conditional-link"
                      href="#linkwrapper2"
                      target="_self"
                    >
                      <div
                        class="emotion-5"
                        data-testid="product-card-image"
                      >
                        <canvas
                          aria-label=""
                          class="emotion-9"
                          role="img"
                        />
                      </div>
                    </a>
                  </div>
                </div>
                <div
                  aria-hidden="true"
                  class="slick-slide"
                  data-index="2"
                  style="outline: none; width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <a
                      aria-label="frame 3 test banner"
                      class="emotion-4"
                      data-testid="conditional-link"
                      href="#linkwrapper3"
                      target="_self"
                    >
                      <div
                        class="emotion-5"
                        data-testid="product-card-image"
                      >
                        <canvas
                          aria-label=""
                          class="emotion-9"
                          role="img"
                        />
                      </div>
                    </a>
                  </div>
                </div>
              </div>
            </div>
            <button
              aria-label="Next"
              class="slick-next slick-arrow slick-next"
              data-role="none"
            >
              <span
                aria-hidden="true"
                class="emotion-3"
              >
                <svg
                  viewBox="0 0 18 7.742"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                    fill="#2B2B2B"
                    transform="translate(18) rotate(90)"
                  />
                </svg>
              </span>
            </button>
          </div>
        </div>
      </nav>
    </div>
  </div>
</DocumentFragment>
`;

exports[`MediaComponent should render carousel in mobile 1`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 100%;
  width: 100%;
  aspect-ratio: 4/1;
}

.emotion-1 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-1 .slick-list {
  overflow: hidden;
}

.emotion-1 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-1 button.slick-next.slick-arrow.slick-next,
.emotion-1 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: undefined;
  height: 44px;
  width: 44px;
}

.emotion-1 button.slick-next.slick-arrow.slick-next>span,
.emotion-1 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-1 button.slick-next.slick-arrow.slick-next svg,
.emotion-1 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-1 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-1 .slick-disabled {
  display: none!important;
}

.emotion-1 .slick-next {
  left: calc(100% - 44px);
}

.emotion-1 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-1 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-2 {
  position: relative;
}

.emotion-2 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-2 .slick-slider .slick-track,
.emotion-2 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-2 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-2 .slick-list:focus {
  outline: none;
}

.emotion-2 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-2 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-2 .slick-track:before,
.emotion-2 .slick-track:after {
  display: table;
  content: "";
}

.emotion-2 .slick-track:after {
  clear: both;
}

.emotion-2 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-2 .slick-slide img {
  display: block;
}

.emotion-2 .slick-slide.slick-loading img {
  display: none;
}

.emotion-2 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-2 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-2 .slick-initialized .slick-slide,
.emotion-2 .slick-vertical .slick-slide {
  display: block;
}

.emotion-2 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-2 .slick-loading .slick-track,
.emotion-2 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-2 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-2 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-2 .slick-prev,
.emotion-2 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-2 .slick-prev:hover,
.emotion-2 .slick-next:hover,
.emotion-2 .slick-prev:focus,
.emotion-2 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-2 .slick-prev.slick-disabled,
.emotion-2 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-2 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-2 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-2 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-2 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-2 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-2 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-2 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-2 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-2 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-2 .slick-dots li button:hover,
.emotion-2 .slick-dots li button:focus {
  outline: none;
}

.emotion-2 .slick-dots li button:hover:before,
.emotion-2 .slick-dots li button:focus:before,
.emotion-2 .slick-dots li button:hover:before,
.emotion-2 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-2 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-3 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-4 {
  cursor: pointer;
  height: 100%;
  width: 100%;
}

.emotion-4:focus-visible {
  outline-offset: -5px;
  outline: auto;
}

.emotion-4:focus-visible:has(canvas) canvas {
  position: relative;
  z-index: -1;
}

.emotion-5 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-6 {
  width: 100%;
  aspect-ratio: 4/1;
  object-fit: cover;
}

.emotion-9 {
  width: 100%;
  height: 100%;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <nav
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div
            class="slick-slider slick-initialized"
            dir="ltr"
          >
            <button
              aria-label="Previous"
              class="slick-prev slick-arrow slick-prev slick-disabled"
              data-role="none"
              disabled=""
            >
              <span
                aria-hidden="true"
                class="emotion-3"
              >
                <svg
                  viewBox="0 0 18 7.742"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                    fill="#2B2B2B"
                    transform="translate(18) rotate(90)"
                  />
                </svg>
              </span>
            </button>
            <div
              class="slick-list"
            >
              <div
                class="slick-track"
                style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
              >
                <div
                  aria-hidden="false"
                  class="slick-slide slick-active slick-current"
                  data-index="0"
                  style="outline: none; width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <a
                      aria-label="frame 1 test banner"
                      class="emotion-4"
                      data-testid="conditional-link"
                      href="#linkwrapper1"
                      target="_self"
                    >
                      <div
                        class="emotion-5"
                        data-testid="product-card-image"
                      >
                        <img
                          alt=""
                          class="emotion-6"
                          src="https://1d9xxafvh577y12766tt2karod.staging.bigcontent.io/i/athleta/100_540x960_00?fmt=webp"
                        />
                      </div>
                    </a>
                  </div>
                </div>
                <div
                  aria-hidden="true"
                  class="slick-slide"
                  data-index="1"
                  style="outline: none; width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <a
                      aria-label="frame 2 test banner"
                      class="emotion-4"
                      data-testid="conditional-link"
                      href="#linkwrapper2"
                      target="_self"
                    >
                      <div
                        class="emotion-5"
                        data-testid="product-card-image"
                      >
                        <canvas
                          aria-label=""
                          class="emotion-9"
                          role="img"
                        />
                      </div>
                    </a>
                  </div>
                </div>
                <div
                  aria-hidden="true"
                  class="slick-slide"
                  data-index="2"
                  style="outline: none; width: 0px;"
                  tabindex="-1"
                >
                  <div>
                    <a
                      aria-label="frame 3 test banner"
                      class="emotion-4"
                      data-testid="conditional-link"
                      href="#linkwrapper3"
                      target="_self"
                    >
                      <div
                        class="emotion-5"
                        data-testid="product-card-image"
                      >
                        <canvas
                          aria-label=""
                          class="emotion-9"
                          role="img"
                        />
                      </div>
                    </a>
                  </div>
                </div>
              </div>
            </div>
            <button
              aria-label="Next"
              class="slick-next slick-arrow slick-next"
              data-role="none"
            >
              <span
                aria-hidden="true"
                class="emotion-3"
              >
                <svg
                  viewBox="0 0 18 7.742"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                    fill="#2B2B2B"
                    transform="translate(18) rotate(90)"
                  />
                </svg>
              </span>
            </button>
          </div>
        </div>
      </nav>
    </div>
  </div>
</DocumentFragment>
`;

exports[`MediaComponent should render video 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
  height: 100%;
  pointer-events: auto;
  aspect-ratio: 4/3;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <h1
        role="presentation"
      >
        ReactPlayer
      </h1>
    </div>
  </div>
</DocumentFragment>
`;

exports[`MediaComponent should render video with autoPlay data 1`] = `
.emotion-0 {
  position: relative;
  height: 100%;
  pointer-events: auto;
  aspect-ratio: 4/3;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <h1
        role="presentation"
      >
        ReactPlayer
      </h1>
    </div>
  </div>
</div>
`;
