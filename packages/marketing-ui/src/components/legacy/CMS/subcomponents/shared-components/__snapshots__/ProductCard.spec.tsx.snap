// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Product Card should render on desktop 1`] = `
.emotion-0 {
  grid-area: product-card;
  width: 100%;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
}

.emotion-1 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-2 {
  width: 100%;
  object-fit: cover;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
        data-testid="product-card-image"
      >
        <img
          alt="alt"
          class="emotion-2"
          src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/HOL1_Winter-Essentials_S?fmt=webp"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`Product Card should render on mobile 1`] = `
.emotion-0 {
  grid-area: product-card;
  width: 100%;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
}

.emotion-1 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-2 {
  width: 100%;
  object-fit: cover;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
        data-testid="product-card-image"
      >
        <img
          alt="alt"
          class="emotion-2"
          src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/HOL1_Winter-Essentials_S?fmt=webp"
        />
      </div>
    </div>
  </div>
</div>
`;
