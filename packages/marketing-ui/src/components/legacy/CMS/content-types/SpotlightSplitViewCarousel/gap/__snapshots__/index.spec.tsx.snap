// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SpotlightCarouselSplitView for Gap autoplay should match snapshots on desktop 1`] = `
.emotion-0 {
  aspect-ratio: 480/645;
}

.emotion-1 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-1 .slick-list {
  overflow: hidden;
}

.emotion-1 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-1 button.slick-next.slick-arrow.slick-next,
.emotion-1 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: undefined;
  height: 44px;
  width: 44px;
}

.emotion-1 button.slick-next.slick-arrow.slick-next>span,
.emotion-1 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-1 button.slick-next.slick-arrow.slick-next svg,
.emotion-1 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-1 .slick-slide {
  margin: 0;
  width: auto;
  aspect-ratio: 480/645;
}

.emotion-1 .slick-disabled {
  display: none!important;
}

.emotion-1 .slick-next {
  left: calc(100% - 44px);
}

.emotion-1 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-1 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-2 {
  position: relative;
}

.emotion-2 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-2 .slick-slider .slick-track,
.emotion-2 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-2 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-2 .slick-list:focus {
  outline: none;
}

.emotion-2 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-2 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-2 .slick-track:before,
.emotion-2 .slick-track:after {
  display: table;
  content: "";
}

.emotion-2 .slick-track:after {
  clear: both;
}

.emotion-2 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-2 .slick-slide img {
  display: block;
}

.emotion-2 .slick-slide.slick-loading img {
  display: none;
}

.emotion-2 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-2 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-2 .slick-initialized .slick-slide,
.emotion-2 .slick-vertical .slick-slide {
  display: block;
}

.emotion-2 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-2 .slick-loading .slick-track,
.emotion-2 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-2 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-2 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-2 .slick-prev,
.emotion-2 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-2 .slick-prev:hover,
.emotion-2 .slick-next:hover,
.emotion-2 .slick-prev:focus,
.emotion-2 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-2 .slick-prev.slick-disabled,
.emotion-2 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-2 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-2 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-2 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-2 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-2 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-2 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-2 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-2 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-2 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-2 .slick-dots li button:hover,
.emotion-2 .slick-dots li button:focus {
  outline: none;
}

.emotion-2 .slick-dots li button:hover:before,
.emotion-2 .slick-dots li button:focus:before,
.emotion-2 .slick-dots li button:hover:before,
.emotion-2 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-2 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-2.pagination-line .slick-dots {
  position: absolute;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-2.pagination-line .slick-dots[data-alignment='left'] {
  left: 2.5vw;
  bottom: 30px;
  width: 264px;
}

.emotion-2.pagination-line .slick-dots[data-alignment='right'] {
  right: 2.5vw;
  bottom: 30px;
  width: 264px;
}

.emotion-2.pagination-line .slick-dots[data-alignment='center'] {
  right: 0;
  left: 0;
  bottom: 30px;
  width: 264px;
  margin-left: auto;
  margin-right: auto;
}

.emotion-2 .gap-spotlight-carousel-pagination-wrapper {
  text-wrap: nowrap;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 8px;
  width: 100%;
  height: 0;
}

.emotion-2.pagination-line li {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  height: auto!important;
  cursor: default!important;
}

.emotion-2 .slick-dots li {
  margin: 0!important;
}

.emotion-2 .slick-dots li button {
  padding: 0!important;
}

.emotion-2 .gap-spotlight-carousel-pagination-line {
  position: relative;
  width: 100%;
  height: 4px;
  padding: 0;
  background-color: rgba(29, 29, 29, 0.3);
}

.emotion-2 button.gap-spotlight-carousel-button::before {
  content: none;
  display: none;
}

.emotion-2 button.gap-spotlight-carousel-button {
  width: 100%;
}

.emotion-2 .slick-active .gap-spotlight-carousel-pagination-line .gap-spotlight-carousel-pagination-progress {
  height: 4px;
  left: 0;
  position: absolute;
  background-color: white;
  -webkit-animation-name: gap-spotlight-carousel-animation;
  animation-name: gap-spotlight-carousel-animation;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
}

.emotion-3 {
  position: absolute;
  border: none;
  padding: 0;
  z-index: 1;
  background: transparent;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: absolute;
  bottom: 49px;
  right: 2.5vw;
  opacity: 0.64;
}

.emotion-3>span {
  display: initial;
}

.emotion-3:hover {
  opacity: 1;
}

.emotion-3 svg path:first-of-type {
  fill: rgba(0, 0, 0, 0.5);
}

.emotion-3 svg path:last-of-type {
  fill: #FFFFFF;
}

.emotion-4 {
  width: 24px;
  height: 24px;
}

.emotion-5 {
  display: inline-block;
  height: 24px;
  width: 24px;
  min-height: 24px;
  min-width: 24px;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-6 {
  background: #CCCCCC;
  aspect-ratio: 480/645;
  position: relative;
  display: grid;
  grid-template-columns: 100%;
  grid-template-rows: 100%;
  width: 100%;
  height: 100%;
}

.emotion-6>* {
  grid-area: 1/1;
}

.emotion-7 {
  -webkit-background-size: cover;
  background-size: cover;
  width: 100%;
  height: 100%;
}

.emotion-8 {
  width: 100%;
  aspect-ratio: 480/645;
  object-fit: cover;
}

.emotion-9 {
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.00) 0%, rgba(0, 0, 0, 0.40) 50.61%, rgba(0, 0, 0, 0.00) 100%);
  width: 100%;
  pointer-events: none;
  z-index: 1;
}

.emotion-10 {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column nowrap;
  -webkit-flex-flow: column nowrap;
  -ms-flex-flow: column nowrap;
  flex-flow: column nowrap;
  padding: 30px 2.5vw;
  aspect-ratio: 480/645;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-11 {
  box-sizing: border-box;
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: calc(100% / 3);
  -ms-flex-preferred-size: calc(100% / 3);
  flex-basis: calc(100% / 3);
  height: 100%;
  padding-top: 0px;
  padding-bottom: 0px;
  min-height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 30px 20px;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  -webkit-align-content: start;
  -ms-flex-line-pack: start;
  align-content: start;
}

.emotion-12 {
  box-sizing: border-box;
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: calc(100% / 3);
  -ms-flex-preferred-size: calc(100% / 3);
  flex-basis: calc(100% / 3);
  height: 100%;
  padding-top: 0px;
  padding-bottom: 0px;
  min-height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 30px 20px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
}

.emotion-13 {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 0;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  -webkit-order: 1;
  -ms-flex-order: 1;
  order: 1;
}

.emotion-14 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-14 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-14 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-14 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-14 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-14 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-14 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-14 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 70px;
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 114px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-14 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-14 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-14 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-14 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-15 {
  box-sizing: border-box;
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: calc(100% / 3);
  -ms-flex-preferred-size: calc(100% / 3);
  flex-basis: calc(100% / 3);
  height: 100%;
  padding-top: 30px;
  padding-bottom: 0px;
  min-height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 30px 20px;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  -webkit-align-content: end;
  -ms-flex-line-pack: end;
  align-content: end;
}

.emotion-16 {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: 0;
  -ms-flex-preferred-size: 0;
  flex-basis: 0;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  padding-bottom: 0;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
  -webkit-order: 1;
  -ms-flex-order: 1;
  order: 1;
}

.emotion-17 {
  height: 105px;
}

.emotion-18 {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  padding-bottom: 0;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
  -webkit-order: 1;
  -ms-flex-order: 1;
  order: 1;
}

.emotion-20 {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: fit-content;
  -ms-flex-preferred-size: fit-content;
  flex-basis: fit-content;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  padding-bottom: 0;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
  -webkit-order: 1;
  -ms-flex-order: 1;
  order: 1;
}

.emotion-21 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-flex-flow: row;
  -webkit-flex-flow: row;
  -ms-flex-flow: row;
  flex-flow: row;
  gap: 20px;
}

.emotion-22 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 18px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
  line-height: 18px;
  text-underline-offset: 14px;
  line-height: 18px;
  pointer-events: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-height: 28px;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
}

.emotion-22:focus {
  outline: none;
}

.emotion-22:hover,
.emotion-22:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-22:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-27 {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: fit-content;
  -ms-flex-preferred-size: fit-content;
  flex-basis: fit-content;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: right;
  -ms-flex-pack: right;
  -webkit-justify-content: right;
  justify-content: right;
  padding-bottom: 0;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
  -webkit-order: 2;
  -ms-flex-order: 2;
  order: 2;
}

.emotion-28 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: absolute;
  bottom: 0;
  right: 0;
  padding: 10px;
  gap: 5px;
  z-index: 1;
  position: initial;
  padding: 0;
  pointer-events: all;
}

.emotion-29 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 0;
}

.emotion-30 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
  text-transform: none;
  font-weight: normal;
  min-height: auto;
  color: #FFFFFF;
  font-size: 12px;
}

.emotion-31 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  min-height: auto;
}

.emotion-31:focus {
  outline: none;
}

.emotion-31 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-31 span span {
  padding-left: initial;
}

.emotion-31:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-31:focus-visible {
  outline: auto;
}

.emotion-32 {
  background: #FF0000;
  aspect-ratio: 480/645;
  position: relative;
  display: grid;
  grid-template-columns: 100%;
  grid-template-rows: 100%;
  width: 100%;
  height: 100%;
}

.emotion-32>* {
  grid-area: 1/1;
}

.emotion-35 {
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
}

.emotion-38 {
  box-sizing: border-box;
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: calc(100% / 3);
  -ms-flex-preferred-size: calc(100% / 3);
  flex-basis: calc(100% / 3);
  height: 100%;
  padding-top: 0px;
  padding-bottom: 30px;
  min-height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 30px 20px;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  -webkit-align-content: start;
  -ms-flex-line-pack: start;
  align-content: start;
}

.emotion-39 {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  padding-bottom: 0;
  -webkit-align-self: start;
  -ms-flex-item-align: start;
  align-self: start;
  -webkit-order: 1;
  -ms-flex-order: 1;
  order: 1;
}

.emotion-47 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 18px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background: transparent;
  border-color: #FFFFFF;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
  line-height: 18px;
  text-underline-offset: 14px;
  line-height: 18px;
  pointer-events: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-height: 28px;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
}

.emotion-47:focus {
  outline: none;
}

.emotion-47:hover,
.emotion-47:focus {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-47:active {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-48 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 18px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 24px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #FFFFFF;
  height: auto;
  padding-bottom: 2.3px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 2.3px;
  text-decoration-thickness: 2.3px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
  text-underline-offset: 5px;
  line-height: 18px;
  text-underline-offset: 5px;
  line-height: 18px;
  pointer-events: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-height: 28px;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  border: none;
}

.emotion-48:focus {
  outline: none;
}

.emotion-48:hover,
.emotion-48:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-48:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-49 {
  box-sizing: border-box;
}

.emotion-50 {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  padding-bottom: 0;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
  -webkit-order: 0;
  -ms-flex-order: 0;
  order: 0;
}

.emotion-208 {
  background: #FFFFFF;
  aspect-ratio: 480/645;
  position: relative;
  display: grid;
  grid-template-columns: 100%;
  grid-template-rows: 100%;
  width: 100%;
  height: 100%;
}

.emotion-208>* {
  grid-area: 1/1;
}

.emotion-209 {
  position: relative;
  height: 100%;
  pointer-events: auto;
  aspect-ratio: 480/645;
  width: 100%;
}

.emotion-210 {
  background: transparent;
  width: 100%;
  pointer-events: none;
  z-index: 1;
}

.emotion-216 {
  box-sizing: border-box;
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: calc(100% / 3);
  -ms-flex-preferred-size: calc(100% / 3);
  flex-basis: calc(100% / 3);
  height: 100%;
  padding-top: 0px;
  padding-bottom: 0px;
  min-height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 30px 20px;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  -webkit-align-content: end;
  -ms-flex-line-pack: end;
  align-content: end;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <style>
        
    :root {
      --mui_spotlight-carousel--split-view-aspect-ratio: 480 / 645;
      --mui_spotlight-carousel--split-view-width: 33.333333333333336%;
      --mui_spotlight-carousel--split-view-maxheight: 645px;
  
      </style>
      <div
        class="emotion-0"
      >
        <div
          class="mui_spotlight-carousel-split-view"
          data-testid="spotlight-carousel-split-view"
        >
          <div
            class="split-view-carousel-frame"
          >
            <nav
              class="emotion-1"
              origin="SpotlightSplitViewCarousel"
            >
              <div
                class="split-view-carousel pagination-line emotion-2"
              >
                <button
                  aria-label="pause"
                  class="split-view-carousel-play-pause-button emotion-3"
                >
                  <div
                    class="emotion-4"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-5"
                    >
                      <svg
                        height="32"
                        viewBox="0 0 32 32"
                        width="32"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M32 16c0 8.837-7.163 16-16 16S0 24.837 0 16 7.163 0 16 0s16 7.163 16 16z"
                          fill="#F2F2F2"
                        />
                        <path
                          d="M10 10a2 2 0 114 0v12a2 2 0 11-4 0V10zM18 10a2 2 0 114 0v12a2 2 0 11-4 0V10z"
                        />
                      </svg>
                    </span>
                  </div>
                </button>
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="-1"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <section
                            class="emotion-6"
                          >
                            <div>
                              <div
                                class="emotion-7"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="A man"
                                  class="emotion-8"
                                  src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/FA222975_M_boot_REV?fmt=webp"
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-9"
                            >
                              <div
                                class="emotion-10"
                                data-testid="flex-content-wrapper"
                              >
                                <div
                                  class="emotion-11"
                                />
                                <div
                                  class="emotion-12"
                                >
                                  <div
                                    class="emotion-13"
                                  >
                                    <div
                                      class="emotion-14"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--headline-3"
                                          >
                                            I am the second frame
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-15"
                                >
                                  <div
                                    class="emotion-16"
                                  >
                                    <img
                                      alt=""
                                      class="emotion-17"
                                      icon="[object Object]"
                                      src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                                    />
                                  </div>
                                  <div
                                    class="emotion-18"
                                  >
                                    <div
                                      class="emotion-14"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--body-1"
                                          >
                                            The Quick Brown Fox Jumps Over The Lazy Dog
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-20"
                                  >
                                    <div
                                      class="emotion-21"
                                    >
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.one"
                                        >
                                          CTA ONE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.two"
                                        >
                                          CTA TWO
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.three"
                                        >
                                          CTA THREE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.four"
                                        >
                                          CTA FOUR
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.five"
                                        >
                                          CTA FIVE
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-27"
                                  >
                                    <div
                                      class="emotion-28"
                                    >
                                      <div
                                        class="emotion-29"
                                      >
                                        <span
                                          class="emotion-30"
                                        >
                                          Prefixo
                                        </span>
                                      </div>
                                      <button
                                        class="emotion-31"
                                      >
                                        Detalhes
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </section>
                        </div>
                      </div>
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <section
                            class="emotion-32"
                          >
                            <div>
                              <div
                                class="emotion-7"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="People"
                                  class="emotion-8"
                                  src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/ADU_21_FA3_GOL_OF__ARCH_J_D_B__2274_WB?fmt=webp"
                                />
                              </div>
                              <a
                                aria-label="Link Not Found"
                                class="emotion-35"
                                data-testid="conditional-link"
                                href="link.not.found"
                                target="_self"
                              />
                            </div>
                            <div
                              class="emotion-9"
                            >
                              <div
                                class="emotion-10"
                                data-testid="flex-content-wrapper"
                              >
                                <div
                                  class="emotion-38"
                                >
                                  <div
                                    class="emotion-39"
                                  >
                                    <div
                                      class="emotion-14"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--body-1"
                                            style="color: #FFF;"
                                          >
                                            The quick brown fox jumps over the lazy dog
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-12"
                                >
                                  <div
                                    class="emotion-13"
                                  >
                                    <div
                                      class="emotion-14"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--headline-4"
                                            style="color: #FFF;"
                                          >
                                            Headline
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-15"
                                >
                                  <div
                                    class="emotion-18"
                                  >
                                    <div
                                      class="emotion-21"
                                    >
                                      <div>
                                        <a
                                          class="emotion-47"
                                          color="light"
                                          href="/cta1"
                                        >
                                          CTA 1
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-48"
                                          color="light"
                                          href="/cta2"
                                        >
                                          <span
                                            class="emotion-49"
                                          >
                                            CTA 2
                                          </span>
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-50"
                                  >
                                    <div
                                      class="emotion-28"
                                    >
                                      <div
                                        class="emotion-29"
                                      >
                                        <span
                                          class="emotion-30"
                                        >
                                          Prefix
                                        </span>
                                      </div>
                                      <button
                                        class="emotion-31"
                                      >
                                        Details
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </section>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <section
                            class="emotion-6"
                          >
                            <div>
                              <div
                                class="emotion-7"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="A man"
                                  class="emotion-8"
                                  src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/FA222975_M_boot_REV?fmt=webp"
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-9"
                            >
                              <div
                                class="emotion-10"
                                data-testid="flex-content-wrapper"
                              >
                                <div
                                  class="emotion-11"
                                />
                                <div
                                  class="emotion-12"
                                >
                                  <div
                                    class="emotion-13"
                                  >
                                    <div
                                      class="emotion-14"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--headline-3"
                                          >
                                            I am the second frame
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-15"
                                >
                                  <div
                                    class="emotion-16"
                                  >
                                    <img
                                      alt=""
                                      class="emotion-17"
                                      icon="[object Object]"
                                      src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                                    />
                                  </div>
                                  <div
                                    class="emotion-18"
                                  >
                                    <div
                                      class="emotion-14"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--body-1"
                                          >
                                            The Quick Brown Fox Jumps Over The Lazy Dog
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-20"
                                  >
                                    <div
                                      class="emotion-21"
                                    >
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.one"
                                        >
                                          CTA ONE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.two"
                                        >
                                          CTA TWO
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.three"
                                        >
                                          CTA THREE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.four"
                                        >
                                          CTA FOUR
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.five"
                                        >
                                          CTA FIVE
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-27"
                                  >
                                    <div
                                      class="emotion-28"
                                    >
                                      <div
                                        class="emotion-29"
                                      >
                                        <span
                                          class="emotion-30"
                                        >
                                          Prefixo
                                        </span>
                                      </div>
                                      <button
                                        class="emotion-31"
                                      >
                                        Detalhes
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </section>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <section
                            class="emotion-6"
                          >
                            <div>
                              <div
                                class="emotion-7"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="A man"
                                  class="emotion-8"
                                  src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/FA222975_M_boot_REV?fmt=webp"
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-9"
                            >
                              <div
                                class="emotion-10"
                                data-testid="flex-content-wrapper"
                              >
                                <div
                                  class="emotion-11"
                                />
                                <div
                                  class="emotion-12"
                                >
                                  <div
                                    class="emotion-13"
                                  >
                                    <div
                                      class="emotion-14"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--headline-3"
                                          >
                                            I am the second frame
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-15"
                                >
                                  <div
                                    class="emotion-16"
                                  >
                                    <img
                                      alt=""
                                      class="emotion-17"
                                      icon="[object Object]"
                                      src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                                    />
                                  </div>
                                  <div
                                    class="emotion-18"
                                  >
                                    <div
                                      class="emotion-14"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--body-1"
                                          >
                                            The Quick Brown Fox Jumps Over The Lazy Dog
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-20"
                                  >
                                    <div
                                      class="emotion-21"
                                    >
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.one"
                                        >
                                          CTA ONE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.two"
                                        >
                                          CTA TWO
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.three"
                                        >
                                          CTA THREE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.four"
                                        >
                                          CTA FOUR
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.five"
                                        >
                                          CTA FIVE
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-27"
                                  >
                                    <div
                                      class="emotion-28"
                                    >
                                      <div
                                        class="emotion-29"
                                      >
                                        <span
                                          class="emotion-30"
                                        >
                                          Prefixo
                                        </span>
                                      </div>
                                      <button
                                        class="emotion-31"
                                      >
                                        Detalhes
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </section>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="3"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <section
                            class="emotion-32"
                          >
                            <div>
                              <div
                                class="emotion-7"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="People"
                                  class="emotion-8"
                                  src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/ADU_21_FA3_GOL_OF__ARCH_J_D_B__2274_WB?fmt=webp"
                                />
                              </div>
                              <a
                                aria-label="Link Not Found"
                                class="emotion-35"
                                data-testid="conditional-link"
                                href="link.not.found"
                                target="_self"
                              />
                            </div>
                            <div
                              class="emotion-9"
                            >
                              <div
                                class="emotion-10"
                                data-testid="flex-content-wrapper"
                              >
                                <div
                                  class="emotion-38"
                                >
                                  <div
                                    class="emotion-39"
                                  >
                                    <div
                                      class="emotion-14"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--body-1"
                                            style="color: #FFF;"
                                          >
                                            The quick brown fox jumps over the lazy dog
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-12"
                                >
                                  <div
                                    class="emotion-13"
                                  >
                                    <div
                                      class="emotion-14"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--headline-4"
                                            style="color: #FFF;"
                                          >
                                            Headline
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-15"
                                >
                                  <div
                                    class="emotion-18"
                                  >
                                    <div
                                      class="emotion-21"
                                    >
                                      <div>
                                        <a
                                          class="emotion-47"
                                          color="light"
                                          href="/cta1"
                                        >
                                          CTA 1
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-48"
                                          color="light"
                                          href="/cta2"
                                        >
                                          <span
                                            class="emotion-49"
                                          >
                                            CTA 2
                                          </span>
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-50"
                                  >
                                    <div
                                      class="emotion-28"
                                    >
                                      <div
                                        class="emotion-29"
                                      >
                                        <span
                                          class="emotion-30"
                                        >
                                          Prefix
                                        </span>
                                      </div>
                                      <button
                                        class="emotion-31"
                                      >
                                        Details
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </section>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="4"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <section
                            class="emotion-6"
                          >
                            <div>
                              <div
                                class="emotion-7"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="A man"
                                  class="emotion-8"
                                  src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/FA222975_M_boot_REV?fmt=webp"
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-9"
                            >
                              <div
                                class="emotion-10"
                                data-testid="flex-content-wrapper"
                              >
                                <div
                                  class="emotion-11"
                                />
                                <div
                                  class="emotion-12"
                                >
                                  <div
                                    class="emotion-13"
                                  >
                                    <div
                                      class="emotion-14"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--headline-3"
                                          >
                                            I am the second frame
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-15"
                                >
                                  <div
                                    class="emotion-16"
                                  >
                                    <img
                                      alt=""
                                      class="emotion-17"
                                      icon="[object Object]"
                                      src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                                    />
                                  </div>
                                  <div
                                    class="emotion-18"
                                  >
                                    <div
                                      class="emotion-14"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--body-1"
                                          >
                                            The Quick Brown Fox Jumps Over The Lazy Dog
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-20"
                                  >
                                    <div
                                      class="emotion-21"
                                    >
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.one"
                                        >
                                          CTA ONE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.two"
                                        >
                                          CTA TWO
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.three"
                                        >
                                          CTA THREE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.four"
                                        >
                                          CTA FOUR
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.five"
                                        >
                                          CTA FIVE
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-27"
                                  >
                                    <div
                                      class="emotion-28"
                                    >
                                      <div
                                        class="emotion-29"
                                      >
                                        <span
                                          class="emotion-30"
                                        >
                                          Prefixo
                                        </span>
                                      </div>
                                      <button
                                        class="emotion-31"
                                      >
                                        Detalhes
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </section>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="5"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <section
                            class="emotion-6"
                          >
                            <div>
                              <div
                                class="emotion-7"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="A man"
                                  class="emotion-8"
                                  src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/FA222975_M_boot_REV?fmt=webp"
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-9"
                            >
                              <div
                                class="emotion-10"
                                data-testid="flex-content-wrapper"
                              >
                                <div
                                  class="emotion-11"
                                />
                                <div
                                  class="emotion-12"
                                >
                                  <div
                                    class="emotion-13"
                                  >
                                    <div
                                      class="emotion-14"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--headline-3"
                                          >
                                            I am the second frame
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-15"
                                >
                                  <div
                                    class="emotion-16"
                                  >
                                    <img
                                      alt=""
                                      class="emotion-17"
                                      icon="[object Object]"
                                      src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                                    />
                                  </div>
                                  <div
                                    class="emotion-18"
                                  >
                                    <div
                                      class="emotion-14"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--body-1"
                                          >
                                            The Quick Brown Fox Jumps Over The Lazy Dog
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-20"
                                  >
                                    <div
                                      class="emotion-21"
                                    >
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.one"
                                        >
                                          CTA ONE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.two"
                                        >
                                          CTA TWO
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.three"
                                        >
                                          CTA THREE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.four"
                                        >
                                          CTA FOUR
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.five"
                                        >
                                          CTA FIVE
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-27"
                                  >
                                    <div
                                      class="emotion-28"
                                    >
                                      <div
                                        class="emotion-29"
                                      >
                                        <span
                                          class="emotion-30"
                                        >
                                          Prefixo
                                        </span>
                                      </div>
                                      <button
                                        class="emotion-31"
                                      >
                                        Detalhes
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </section>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="slick-dots"
                    data-alignment="center"
                  >
                    <ul
                      class="gap-spotlight-carousel-pagination-wrapper"
                    >
                      <li
                        class="slick-active"
                      >
                        <button
                          aria-hidden="false"
                          aria-label="0"
                          class="gap-spotlight-carousel-button"
                        >
                          <div
                            class="gap-spotlight-carousel-pagination-line"
                          >
                            <div
                              class="gap-spotlight-carousel-pagination-progress"
                              style="animation-duration: 3000ms; animation-delay: 1000ms;"
                            />
                          </div>
                        </button>
                      </li>
                      <li
                        class=""
                      >
                        <button
                          aria-hidden="false"
                          aria-label="1"
                          class="gap-spotlight-carousel-button"
                        >
                          <div
                            class="gap-spotlight-carousel-pagination-line"
                          >
                            <div
                              class="gap-spotlight-carousel-pagination-progress"
                              style="animation-duration: 3000ms; animation-delay: 1000ms;"
                            />
                          </div>
                        </button>
                      </li>
                      <li
                        class=""
                      >
                        <button
                          aria-hidden="false"
                          aria-label="2"
                          class="gap-spotlight-carousel-button"
                        >
                          <div
                            class="gap-spotlight-carousel-pagination-line"
                          >
                            <div
                              class="gap-spotlight-carousel-pagination-progress"
                              style="animation-duration: 3000ms; animation-delay: 1000ms;"
                            />
                          </div>
                        </button>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </nav>
          </div>
          <div
            class="split-view-carousel-frame"
          >
            <section
              class="emotion-6"
            >
              <div>
                <div
                  class="emotion-7"
                  data-testid="product-card-image"
                >
                  <img
                    alt="A man"
                    class="emotion-8"
                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/FA222975_M_boot_REV?fmt=webp"
                  />
                </div>
              </div>
              <div
                class="emotion-9"
              >
                <div
                  class="emotion-10"
                  data-testid="flex-content-wrapper"
                >
                  <div
                    class="emotion-11"
                  />
                  <div
                    class="emotion-12"
                  >
                    <div
                      class="emotion-13"
                    >
                      <div
                        class="emotion-14"
                      >
                        <div>
                          <p
                            class="amp-cms--p"
                            style="text-align:left;"
                          >
                            <span
                              class="amp-cms--headline-3"
                            >
                              I am the second frame
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-15"
                  >
                    <div
                      class="emotion-16"
                    >
                      <img
                        alt=""
                        class="emotion-17"
                        icon="[object Object]"
                        src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                      />
                    </div>
                    <div
                      class="emotion-18"
                    >
                      <div
                        class="emotion-14"
                      >
                        <div>
                          <p
                            class="amp-cms--p"
                            style="text-align:left;"
                          >
                            <span
                              class="amp-cms--body-1"
                            >
                              The Quick Brown Fox Jumps Over The Lazy Dog
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                    <div
                      class="emotion-20"
                    >
                      <div
                        class="emotion-21"
                      >
                        <div>
                          <a
                            class="emotion-22"
                            color="dark"
                            href="https://cta.one"
                          >
                            CTA ONE
                          </a>
                        </div>
                        <div>
                          <a
                            class="emotion-22"
                            color="dark"
                            href="https://cta.two"
                          >
                            CTA TWO
                          </a>
                        </div>
                        <div>
                          <a
                            class="emotion-22"
                            color="dark"
                            href="https://cta.three"
                          >
                            CTA THREE
                          </a>
                        </div>
                        <div>
                          <a
                            class="emotion-22"
                            color="dark"
                            href="https://cta.four"
                          >
                            CTA FOUR
                          </a>
                        </div>
                        <div>
                          <a
                            class="emotion-22"
                            color="dark"
                            href="https://cta.five"
                          >
                            CTA FIVE
                          </a>
                        </div>
                      </div>
                    </div>
                    <div
                      class="emotion-27"
                    >
                      <div
                        class="emotion-28"
                      >
                        <div
                          class="emotion-29"
                        >
                          <span
                            class="emotion-30"
                          >
                            Prefixo
                          </span>
                        </div>
                        <button
                          class="emotion-31"
                        >
                          Detalhes
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          </div>
          <div
            class="split-view-carousel-frame"
          >
            <section
              class="emotion-208"
            >
              <div>
                <div
                  class="emotion-209"
                >
                  <h1
                    role="presentation"
                  >
                    ReactPlayer
                  </h1>
                </div>
              </div>
              <div
                class="emotion-210"
              >
                <div
                  class="emotion-10"
                  data-testid="flex-content-wrapper"
                >
                  <div
                    class="emotion-11"
                  />
                  <div
                    class="emotion-12"
                  >
                    <div
                      class="emotion-13"
                    >
                      <div
                        class="emotion-14"
                      >
                        <div>
                          Testing extra frame
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-216"
                  />
                </div>
              </div>
            </section>
          </div>
          <div
            class="split-view-carousel-frame"
          >
            <nav
              class="emotion-1"
              origin="SpotlightSplitViewCarousel"
            >
              <div
                class="split-view-carousel pagination-line emotion-2"
              >
                <button
                  aria-label="pause"
                  class="split-view-carousel-play-pause-button emotion-3"
                >
                  <div
                    class="emotion-4"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-5"
                    >
                      <svg
                        height="32"
                        viewBox="0 0 32 32"
                        width="32"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M32 16c0 8.837-7.163 16-16 16S0 24.837 0 16 7.163 0 16 0s16 7.163 16 16z"
                          fill="#F2F2F2"
                        />
                        <path
                          d="M10 10a2 2 0 114 0v12a2 2 0 11-4 0V10zM18 10a2 2 0 114 0v12a2 2 0 11-4 0V10z"
                        />
                      </svg>
                    </span>
                  </div>
                </button>
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="-1"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <section
                            class="emotion-6"
                          >
                            <div>
                              <div
                                class="emotion-7"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="A man"
                                  class="emotion-8"
                                  src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/FA222975_M_boot_REV?fmt=webp"
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-9"
                            >
                              <div
                                class="emotion-10"
                                data-testid="flex-content-wrapper"
                              >
                                <div
                                  class="emotion-11"
                                />
                                <div
                                  class="emotion-12"
                                >
                                  <div
                                    class="emotion-13"
                                  >
                                    <div
                                      class="emotion-14"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--headline-3"
                                          >
                                            I am the second frame
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-15"
                                >
                                  <div
                                    class="emotion-16"
                                  >
                                    <img
                                      alt=""
                                      class="emotion-17"
                                      icon="[object Object]"
                                      src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                                    />
                                  </div>
                                  <div
                                    class="emotion-18"
                                  >
                                    <div
                                      class="emotion-14"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--body-1"
                                          >
                                            The Quick Brown Fox Jumps Over The Lazy Dog
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-20"
                                  >
                                    <div
                                      class="emotion-21"
                                    >
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.one"
                                        >
                                          CTA ONE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.two"
                                        >
                                          CTA TWO
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.three"
                                        >
                                          CTA THREE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.four"
                                        >
                                          CTA FOUR
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.five"
                                        >
                                          CTA FIVE
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-27"
                                  >
                                    <div
                                      class="emotion-28"
                                    >
                                      <div
                                        class="emotion-29"
                                      >
                                        <span
                                          class="emotion-30"
                                        >
                                          Prefixo
                                        </span>
                                      </div>
                                      <button
                                        class="emotion-31"
                                      >
                                        Detalhes
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </section>
                        </div>
                      </div>
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <section
                            class="emotion-32"
                          >
                            <div>
                              <div
                                class="emotion-7"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="People"
                                  class="emotion-8"
                                  src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/ADU_21_FA3_GOL_OF__ARCH_J_D_B__2274_WB?fmt=webp"
                                />
                              </div>
                              <a
                                aria-label="Link Not Found"
                                class="emotion-35"
                                data-testid="conditional-link"
                                href="link.not.found"
                                target="_self"
                              />
                            </div>
                            <div
                              class="emotion-9"
                            >
                              <div
                                class="emotion-10"
                                data-testid="flex-content-wrapper"
                              >
                                <div
                                  class="emotion-38"
                                >
                                  <div
                                    class="emotion-39"
                                  >
                                    <div
                                      class="emotion-14"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--body-1"
                                            style="color: #FFF;"
                                          >
                                            The quick brown fox jumps over the lazy dog
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-12"
                                >
                                  <div
                                    class="emotion-13"
                                  >
                                    <div
                                      class="emotion-14"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--headline-4"
                                            style="color: #FFF;"
                                          >
                                            Headline
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-15"
                                >
                                  <div
                                    class="emotion-18"
                                  >
                                    <div
                                      class="emotion-21"
                                    >
                                      <div>
                                        <a
                                          class="emotion-47"
                                          color="light"
                                          href="/cta1"
                                        >
                                          CTA 1
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-48"
                                          color="light"
                                          href="/cta2"
                                        >
                                          <span
                                            class="emotion-49"
                                          >
                                            CTA 2
                                          </span>
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-50"
                                  >
                                    <div
                                      class="emotion-28"
                                    >
                                      <div
                                        class="emotion-29"
                                      >
                                        <span
                                          class="emotion-30"
                                        >
                                          Prefix
                                        </span>
                                      </div>
                                      <button
                                        class="emotion-31"
                                      >
                                        Details
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </section>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <section
                            class="emotion-6"
                          >
                            <div>
                              <div
                                class="emotion-7"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="A man"
                                  class="emotion-8"
                                  src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/FA222975_M_boot_REV?fmt=webp"
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-9"
                            >
                              <div
                                class="emotion-10"
                                data-testid="flex-content-wrapper"
                              >
                                <div
                                  class="emotion-11"
                                />
                                <div
                                  class="emotion-12"
                                >
                                  <div
                                    class="emotion-13"
                                  >
                                    <div
                                      class="emotion-14"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--headline-3"
                                          >
                                            I am the second frame
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-15"
                                >
                                  <div
                                    class="emotion-16"
                                  >
                                    <img
                                      alt=""
                                      class="emotion-17"
                                      icon="[object Object]"
                                      src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                                    />
                                  </div>
                                  <div
                                    class="emotion-18"
                                  >
                                    <div
                                      class="emotion-14"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--body-1"
                                          >
                                            The Quick Brown Fox Jumps Over The Lazy Dog
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-20"
                                  >
                                    <div
                                      class="emotion-21"
                                    >
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.one"
                                        >
                                          CTA ONE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.two"
                                        >
                                          CTA TWO
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.three"
                                        >
                                          CTA THREE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.four"
                                        >
                                          CTA FOUR
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.five"
                                        >
                                          CTA FIVE
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-27"
                                  >
                                    <div
                                      class="emotion-28"
                                    >
                                      <div
                                        class="emotion-29"
                                      >
                                        <span
                                          class="emotion-30"
                                        >
                                          Prefixo
                                        </span>
                                      </div>
                                      <button
                                        class="emotion-31"
                                      >
                                        Detalhes
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </section>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <section
                            class="emotion-6"
                          >
                            <div>
                              <div
                                class="emotion-7"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="A man"
                                  class="emotion-8"
                                  src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/FA222975_M_boot_REV?fmt=webp"
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-9"
                            >
                              <div
                                class="emotion-10"
                                data-testid="flex-content-wrapper"
                              >
                                <div
                                  class="emotion-11"
                                />
                                <div
                                  class="emotion-12"
                                >
                                  <div
                                    class="emotion-13"
                                  >
                                    <div
                                      class="emotion-14"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--headline-3"
                                          >
                                            I am the second frame
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-15"
                                >
                                  <div
                                    class="emotion-16"
                                  >
                                    <img
                                      alt=""
                                      class="emotion-17"
                                      icon="[object Object]"
                                      src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                                    />
                                  </div>
                                  <div
                                    class="emotion-18"
                                  >
                                    <div
                                      class="emotion-14"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--body-1"
                                          >
                                            The Quick Brown Fox Jumps Over The Lazy Dog
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-20"
                                  >
                                    <div
                                      class="emotion-21"
                                    >
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.one"
                                        >
                                          CTA ONE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.two"
                                        >
                                          CTA TWO
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.three"
                                        >
                                          CTA THREE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.four"
                                        >
                                          CTA FOUR
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.five"
                                        >
                                          CTA FIVE
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-27"
                                  >
                                    <div
                                      class="emotion-28"
                                    >
                                      <div
                                        class="emotion-29"
                                      >
                                        <span
                                          class="emotion-30"
                                        >
                                          Prefixo
                                        </span>
                                      </div>
                                      <button
                                        class="emotion-31"
                                      >
                                        Detalhes
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </section>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="3"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <section
                            class="emotion-32"
                          >
                            <div>
                              <div
                                class="emotion-7"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="People"
                                  class="emotion-8"
                                  src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/ADU_21_FA3_GOL_OF__ARCH_J_D_B__2274_WB?fmt=webp"
                                />
                              </div>
                              <a
                                aria-label="Link Not Found"
                                class="emotion-35"
                                data-testid="conditional-link"
                                href="link.not.found"
                                target="_self"
                              />
                            </div>
                            <div
                              class="emotion-9"
                            >
                              <div
                                class="emotion-10"
                                data-testid="flex-content-wrapper"
                              >
                                <div
                                  class="emotion-38"
                                >
                                  <div
                                    class="emotion-39"
                                  >
                                    <div
                                      class="emotion-14"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--body-1"
                                            style="color: #FFF;"
                                          >
                                            The quick brown fox jumps over the lazy dog
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-12"
                                >
                                  <div
                                    class="emotion-13"
                                  >
                                    <div
                                      class="emotion-14"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--headline-4"
                                            style="color: #FFF;"
                                          >
                                            Headline
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-15"
                                >
                                  <div
                                    class="emotion-18"
                                  >
                                    <div
                                      class="emotion-21"
                                    >
                                      <div>
                                        <a
                                          class="emotion-47"
                                          color="light"
                                          href="/cta1"
                                        >
                                          CTA 1
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-48"
                                          color="light"
                                          href="/cta2"
                                        >
                                          <span
                                            class="emotion-49"
                                          >
                                            CTA 2
                                          </span>
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-50"
                                  >
                                    <div
                                      class="emotion-28"
                                    >
                                      <div
                                        class="emotion-29"
                                      >
                                        <span
                                          class="emotion-30"
                                        >
                                          Prefix
                                        </span>
                                      </div>
                                      <button
                                        class="emotion-31"
                                      >
                                        Details
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </section>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="4"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <section
                            class="emotion-6"
                          >
                            <div>
                              <div
                                class="emotion-7"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="A man"
                                  class="emotion-8"
                                  src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/FA222975_M_boot_REV?fmt=webp"
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-9"
                            >
                              <div
                                class="emotion-10"
                                data-testid="flex-content-wrapper"
                              >
                                <div
                                  class="emotion-11"
                                />
                                <div
                                  class="emotion-12"
                                >
                                  <div
                                    class="emotion-13"
                                  >
                                    <div
                                      class="emotion-14"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--headline-3"
                                          >
                                            I am the second frame
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-15"
                                >
                                  <div
                                    class="emotion-16"
                                  >
                                    <img
                                      alt=""
                                      class="emotion-17"
                                      icon="[object Object]"
                                      src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                                    />
                                  </div>
                                  <div
                                    class="emotion-18"
                                  >
                                    <div
                                      class="emotion-14"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--body-1"
                                          >
                                            The Quick Brown Fox Jumps Over The Lazy Dog
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-20"
                                  >
                                    <div
                                      class="emotion-21"
                                    >
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.one"
                                        >
                                          CTA ONE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.two"
                                        >
                                          CTA TWO
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.three"
                                        >
                                          CTA THREE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.four"
                                        >
                                          CTA FOUR
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.five"
                                        >
                                          CTA FIVE
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-27"
                                  >
                                    <div
                                      class="emotion-28"
                                    >
                                      <div
                                        class="emotion-29"
                                      >
                                        <span
                                          class="emotion-30"
                                        >
                                          Prefixo
                                        </span>
                                      </div>
                                      <button
                                        class="emotion-31"
                                      >
                                        Detalhes
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </section>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide slick-cloned"
                        data-index="5"
                        style="width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <section
                            class="emotion-6"
                          >
                            <div>
                              <div
                                class="emotion-7"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="A man"
                                  class="emotion-8"
                                  src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/FA222975_M_boot_REV?fmt=webp"
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-9"
                            >
                              <div
                                class="emotion-10"
                                data-testid="flex-content-wrapper"
                              >
                                <div
                                  class="emotion-11"
                                />
                                <div
                                  class="emotion-12"
                                >
                                  <div
                                    class="emotion-13"
                                  >
                                    <div
                                      class="emotion-14"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--headline-3"
                                          >
                                            I am the second frame
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-15"
                                >
                                  <div
                                    class="emotion-16"
                                  >
                                    <img
                                      alt=""
                                      class="emotion-17"
                                      icon="[object Object]"
                                      src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                                    />
                                  </div>
                                  <div
                                    class="emotion-18"
                                  >
                                    <div
                                      class="emotion-14"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--body-1"
                                          >
                                            The Quick Brown Fox Jumps Over The Lazy Dog
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-20"
                                  >
                                    <div
                                      class="emotion-21"
                                    >
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.one"
                                        >
                                          CTA ONE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.two"
                                        >
                                          CTA TWO
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.three"
                                        >
                                          CTA THREE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.four"
                                        >
                                          CTA FOUR
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-22"
                                          color="dark"
                                          href="https://cta.five"
                                        >
                                          CTA FIVE
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-27"
                                  >
                                    <div
                                      class="emotion-28"
                                    >
                                      <div
                                        class="emotion-29"
                                      >
                                        <span
                                          class="emotion-30"
                                        >
                                          Prefixo
                                        </span>
                                      </div>
                                      <button
                                        class="emotion-31"
                                      >
                                        Detalhes
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </section>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="slick-dots"
                    data-alignment="center"
                  >
                    <ul
                      class="gap-spotlight-carousel-pagination-wrapper"
                    >
                      <li
                        class="slick-active"
                      >
                        <button
                          aria-hidden="false"
                          aria-label="0"
                          class="gap-spotlight-carousel-button"
                        >
                          <div
                            class="gap-spotlight-carousel-pagination-line"
                          >
                            <div
                              class="gap-spotlight-carousel-pagination-progress"
                              style="animation-duration: 3000ms; animation-delay: 1000ms;"
                            />
                          </div>
                        </button>
                      </li>
                      <li
                        class=""
                      >
                        <button
                          aria-hidden="false"
                          aria-label="1"
                          class="gap-spotlight-carousel-button"
                        >
                          <div
                            class="gap-spotlight-carousel-pagination-line"
                          >
                            <div
                              class="gap-spotlight-carousel-pagination-progress"
                              style="animation-duration: 3000ms; animation-delay: 1000ms;"
                            />
                          </div>
                        </button>
                      </li>
                      <li
                        class=""
                      >
                        <button
                          aria-hidden="false"
                          aria-label="2"
                          class="gap-spotlight-carousel-button"
                        >
                          <div
                            class="gap-spotlight-carousel-pagination-line"
                          >
                            <div
                              class="gap-spotlight-carousel-pagination-progress"
                              style="animation-duration: 3000ms; animation-delay: 1000ms;"
                            />
                          </div>
                        </button>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </nav>
          </div>
          <div
            class="split-view-carousel-frame"
          >
            <section
              class="emotion-6"
            >
              <div>
                <div
                  class="emotion-7"
                  data-testid="product-card-image"
                >
                  <img
                    alt="A man"
                    class="emotion-8"
                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/FA222975_M_boot_REV?fmt=webp"
                  />
                </div>
              </div>
              <div
                class="emotion-9"
              >
                <div
                  class="emotion-10"
                  data-testid="flex-content-wrapper"
                >
                  <div
                    class="emotion-11"
                  />
                  <div
                    class="emotion-12"
                  >
                    <div
                      class="emotion-13"
                    >
                      <div
                        class="emotion-14"
                      >
                        <div>
                          <p
                            class="amp-cms--p"
                            style="text-align:left;"
                          >
                            <span
                              class="amp-cms--headline-3"
                            >
                              I am the second frame
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-15"
                  >
                    <div
                      class="emotion-16"
                    >
                      <img
                        alt=""
                        class="emotion-17"
                        icon="[object Object]"
                        src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                      />
                    </div>
                    <div
                      class="emotion-18"
                    >
                      <div
                        class="emotion-14"
                      >
                        <div>
                          <p
                            class="amp-cms--p"
                            style="text-align:left;"
                          >
                            <span
                              class="amp-cms--body-1"
                            >
                              The Quick Brown Fox Jumps Over The Lazy Dog
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                    <div
                      class="emotion-20"
                    >
                      <div
                        class="emotion-21"
                      >
                        <div>
                          <a
                            class="emotion-22"
                            color="dark"
                            href="https://cta.one"
                          >
                            CTA ONE
                          </a>
                        </div>
                        <div>
                          <a
                            class="emotion-22"
                            color="dark"
                            href="https://cta.two"
                          >
                            CTA TWO
                          </a>
                        </div>
                        <div>
                          <a
                            class="emotion-22"
                            color="dark"
                            href="https://cta.three"
                          >
                            CTA THREE
                          </a>
                        </div>
                        <div>
                          <a
                            class="emotion-22"
                            color="dark"
                            href="https://cta.four"
                          >
                            CTA FOUR
                          </a>
                        </div>
                        <div>
                          <a
                            class="emotion-22"
                            color="dark"
                            href="https://cta.five"
                          >
                            CTA FIVE
                          </a>
                        </div>
                      </div>
                    </div>
                    <div
                      class="emotion-27"
                    >
                      <div
                        class="emotion-28"
                      >
                        <div
                          class="emotion-29"
                        >
                          <span
                            class="emotion-30"
                          >
                            Prefixo
                          </span>
                        </div>
                        <button
                          class="emotion-31"
                        >
                          Detalhes
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`SpotlightCarouselSplitView for Gap line pagination desktop should render the expected snapshot for bottom center aligned controls 1`] = `
.emotion-0 {
  aspect-ratio: 480/645;
}

.emotion-1 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-1 .slick-list {
  overflow: hidden;
}

.emotion-1 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-1 button.slick-next.slick-arrow.slick-next,
.emotion-1 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: undefined;
  height: 44px;
  width: 44px;
}

.emotion-1 button.slick-next.slick-arrow.slick-next>span,
.emotion-1 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-1 button.slick-next.slick-arrow.slick-next svg,
.emotion-1 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-1 .slick-slide {
  margin: 0;
  width: auto;
  aspect-ratio: 480/645;
}

.emotion-1 .slick-disabled {
  display: none!important;
}

.emotion-1 .slick-next {
  left: calc(100% - 44px);
}

.emotion-1 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-1 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-2 {
  position: relative;
}

.emotion-2 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-2 .slick-slider .slick-track,
.emotion-2 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-2 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-2 .slick-list:focus {
  outline: none;
}

.emotion-2 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-2 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-2 .slick-track:before,
.emotion-2 .slick-track:after {
  display: table;
  content: "";
}

.emotion-2 .slick-track:after {
  clear: both;
}

.emotion-2 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-2 .slick-slide img {
  display: block;
}

.emotion-2 .slick-slide.slick-loading img {
  display: none;
}

.emotion-2 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-2 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-2 .slick-initialized .slick-slide,
.emotion-2 .slick-vertical .slick-slide {
  display: block;
}

.emotion-2 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-2 .slick-loading .slick-track,
.emotion-2 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-2 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-2 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-2 .slick-prev,
.emotion-2 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-2 .slick-prev:hover,
.emotion-2 .slick-next:hover,
.emotion-2 .slick-prev:focus,
.emotion-2 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-2 .slick-prev.slick-disabled,
.emotion-2 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-2 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-2 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-2 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-2 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-2 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-2 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-2 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-2 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-2 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-2 .slick-dots li button:hover,
.emotion-2 .slick-dots li button:focus {
  outline: none;
}

.emotion-2 .slick-dots li button:hover:before,
.emotion-2 .slick-dots li button:focus:before,
.emotion-2 .slick-dots li button:hover:before,
.emotion-2 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-2 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-2.pagination-line .slick-dots {
  position: absolute;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-2.pagination-line .slick-dots[data-alignment='left'] {
  left: 2.5vw;
  bottom: 30px;
  width: 264px;
}

.emotion-2.pagination-line .slick-dots[data-alignment='right'] {
  right: 2.5vw;
  bottom: 30px;
  width: 264px;
}

.emotion-2.pagination-line .slick-dots[data-alignment='center'] {
  right: 0;
  left: 0;
  bottom: 30px;
  width: 264px;
  margin-left: auto;
  margin-right: auto;
}

.emotion-2 .gap-spotlight-carousel-pagination-wrapper {
  text-wrap: nowrap;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 8px;
  width: 100%;
  height: 0;
}

.emotion-2.pagination-line li {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  height: auto!important;
  cursor: default!important;
}

.emotion-2 .slick-dots li {
  margin: 0!important;
}

.emotion-2 .slick-dots li button {
  padding: 0!important;
}

.emotion-2 .gap-spotlight-carousel-pagination-line {
  position: relative;
  width: 100%;
  height: 4px;
  padding: 0;
  background-color: rgba(29, 29, 29, 0.3);
}

.emotion-2 button.gap-spotlight-carousel-button::before {
  content: none;
  display: none;
}

.emotion-2 button.gap-spotlight-carousel-button {
  width: 100%;
}

.emotion-2 .slick-active .gap-spotlight-carousel-pagination-line .gap-spotlight-carousel-pagination-progress {
  height: 4px;
  left: 0;
  position: absolute;
  background-color: white;
  -webkit-animation-name: gap-spotlight-carousel-animation;
  animation-name: gap-spotlight-carousel-animation;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
}

.emotion-3 {
  background: #FF0000;
  aspect-ratio: 480/645;
  position: relative;
  display: grid;
  grid-template-columns: 100%;
  grid-template-rows: 100%;
  width: 100%;
  height: 100%;
}

.emotion-3>* {
  grid-area: 1/1;
}

.emotion-4 {
  -webkit-background-size: cover;
  background-size: cover;
  width: 100%;
  height: 100%;
}

.emotion-5 {
  width: 100%;
  aspect-ratio: 480/645;
  object-fit: cover;
}

.emotion-6 {
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
}

.emotion-7 {
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.00) 0%, rgba(0, 0, 0, 0.40) 50.61%, rgba(0, 0, 0, 0.00) 100%);
  width: 100%;
  pointer-events: none;
  z-index: 1;
}

.emotion-8 {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column nowrap;
  -webkit-flex-flow: column nowrap;
  -ms-flex-flow: column nowrap;
  flex-flow: column nowrap;
  padding: 30px 2.5vw;
  aspect-ratio: 480/645;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-9 {
  box-sizing: border-box;
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: calc(100% / 3);
  -ms-flex-preferred-size: calc(100% / 3);
  flex-basis: calc(100% / 3);
  height: 100%;
  padding-top: 0px;
  padding-bottom: 30px;
  min-height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 30px 20px;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  -webkit-align-content: start;
  -ms-flex-line-pack: start;
  align-content: start;
}

.emotion-10 {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  padding-bottom: 0;
  -webkit-align-self: start;
  -ms-flex-item-align: start;
  align-self: start;
  -webkit-order: 1;
  -ms-flex-order: 1;
  order: 1;
}

.emotion-11 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-11 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-11 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-11 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-11 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-11 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-11 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 70px;
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-size: 114px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-11 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-11 .amp-cms--promoAlt-1 {
  color: #000000;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-11 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-12 {
  box-sizing: border-box;
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: calc(100% / 3);
  -ms-flex-preferred-size: calc(100% / 3);
  flex-basis: calc(100% / 3);
  height: 100%;
  padding-top: 0px;
  padding-bottom: 0px;
  min-height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 30px 20px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
}

.emotion-13 {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 0;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  -webkit-order: 1;
  -ms-flex-order: 1;
  order: 1;
}

.emotion-15 {
  box-sizing: border-box;
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: calc(100% / 3);
  -ms-flex-preferred-size: calc(100% / 3);
  flex-basis: calc(100% / 3);
  height: 100%;
  padding-top: 30px;
  padding-bottom: 0px;
  min-height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 30px 20px;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  -webkit-align-content: end;
  -ms-flex-line-pack: end;
  align-content: end;
}

.emotion-16 {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  padding-bottom: 0;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
  -webkit-order: 1;
  -ms-flex-order: 1;
  order: 1;
}

.emotion-17 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-flex-flow: row;
  -webkit-flex-flow: row;
  -ms-flex-flow: row;
  flex-flow: row;
  gap: 20px;
}

.emotion-18 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 18px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background: transparent;
  border-color: #FFFFFF;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
  line-height: 18px;
  text-underline-offset: 14px;
  line-height: 18px;
  pointer-events: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-height: 28px;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
}

.emotion-18:focus {
  outline: none;
}

.emotion-18:hover,
.emotion-18:focus {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-18:active {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-19 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 18px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 24px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #FFFFFF;
  height: auto;
  padding-bottom: 2.3px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 2.3px;
  text-decoration-thickness: 2.3px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
  text-underline-offset: 5px;
  line-height: 18px;
  text-underline-offset: 5px;
  line-height: 18px;
  pointer-events: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-height: 28px;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  border: none;
}

.emotion-19:focus {
  outline: none;
}

.emotion-19:hover,
.emotion-19:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-19:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-20 {
  box-sizing: border-box;
}

.emotion-21 {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  padding-bottom: 0;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
  -webkit-order: 0;
  -ms-flex-order: 0;
  order: 0;
}

.emotion-22 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: absolute;
  bottom: 0;
  right: 0;
  padding: 10px;
  gap: 5px;
  z-index: 1;
  position: initial;
  padding: 0;
  pointer-events: all;
}

.emotion-23 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 0;
}

.emotion-24 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
  text-transform: none;
  font-weight: normal;
  min-height: auto;
  color: #FFFFFF;
  font-size: 12px;
}

.emotion-25 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  min-height: auto;
}

.emotion-25:focus {
  outline: none;
}

.emotion-25 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-25 span span {
  padding-left: initial;
}

.emotion-25:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-25:focus-visible {
  outline: auto;
}

.emotion-26 {
  background: #CCCCCC;
  aspect-ratio: 480/645;
  position: relative;
  display: grid;
  grid-template-columns: 100%;
  grid-template-rows: 100%;
  width: 100%;
  height: 100%;
}

.emotion-26>* {
  grid-area: 1/1;
}

.emotion-31 {
  box-sizing: border-box;
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: calc(100% / 3);
  -ms-flex-preferred-size: calc(100% / 3);
  flex-basis: calc(100% / 3);
  height: 100%;
  padding-top: 0px;
  padding-bottom: 0px;
  min-height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 30px 20px;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  -webkit-align-content: start;
  -ms-flex-line-pack: start;
  align-content: start;
}

.emotion-36 {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: 0;
  -ms-flex-preferred-size: 0;
  flex-basis: 0;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  padding-bottom: 0;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
  -webkit-order: 1;
  -ms-flex-order: 1;
  order: 1;
}

.emotion-37 {
  height: 105px;
}

.emotion-40 {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: fit-content;
  -ms-flex-preferred-size: fit-content;
  flex-basis: fit-content;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  padding-bottom: 0;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
  -webkit-order: 1;
  -ms-flex-order: 1;
  order: 1;
}

.emotion-42 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 18px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
  line-height: 18px;
  text-underline-offset: 14px;
  line-height: 18px;
  pointer-events: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-height: 28px;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
}

.emotion-42:focus {
  outline: none;
}

.emotion-42:hover,
.emotion-42:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-42:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-47 {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: fit-content;
  -ms-flex-preferred-size: fit-content;
  flex-basis: fit-content;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: right;
  -ms-flex-pack: right;
  -webkit-justify-content: right;
  justify-content: right;
  padding-bottom: 0;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
  -webkit-order: 2;
  -ms-flex-order: 2;
  order: 2;
}

.emotion-104 {
  background: #FFFFFF;
  aspect-ratio: 480/645;
  position: relative;
  display: grid;
  grid-template-columns: 100%;
  grid-template-rows: 100%;
  width: 100%;
  height: 100%;
}

.emotion-104>* {
  grid-area: 1/1;
}

.emotion-105 {
  position: relative;
  height: 100%;
  pointer-events: auto;
  aspect-ratio: 480/645;
  width: 100%;
}

.emotion-106 {
  background: transparent;
  width: 100%;
  pointer-events: none;
  z-index: 1;
}

.emotion-112 {
  box-sizing: border-box;
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: calc(100% / 3);
  -ms-flex-preferred-size: calc(100% / 3);
  flex-basis: calc(100% / 3);
  height: 100%;
  padding-top: 0px;
  padding-bottom: 0px;
  min-height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 30px 20px;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  -webkit-align-content: end;
  -ms-flex-line-pack: end;
  align-content: end;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <style>
        
    :root {
      --mui_spotlight-carousel--split-view-aspect-ratio: 480 / 645;
      --mui_spotlight-carousel--split-view-width: 33.333333333333336%;
      --mui_spotlight-carousel--split-view-maxheight: 645px;
  
      </style>
      <div
        class="emotion-0"
      >
        <div
          class="mui_spotlight-carousel-split-view"
          data-testid="spotlight-carousel-split-view"
        >
          <div
            class="split-view-carousel-frame"
          >
            <nav
              class="emotion-1"
              origin="SpotlightSplitViewCarousel"
            >
              <div
                class="split-view-carousel pagination-line emotion-2"
              >
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <section
                            class="emotion-3"
                          >
                            <div>
                              <div
                                class="emotion-4"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="People"
                                  class="emotion-5"
                                  src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/ADU_21_FA3_GOL_OF__ARCH_J_D_B__2274_WB?fmt=webp"
                                />
                              </div>
                              <a
                                aria-label="Link Not Found"
                                class="emotion-6"
                                data-testid="conditional-link"
                                href="link.not.found"
                                target="_self"
                              />
                            </div>
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="flex-content-wrapper"
                              >
                                <div
                                  class="emotion-9"
                                >
                                  <div
                                    class="emotion-10"
                                  >
                                    <div
                                      class="emotion-11"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--body-1"
                                            style="color: #FFF;"
                                          >
                                            The quick brown fox jumps over the lazy dog
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-12"
                                >
                                  <div
                                    class="emotion-13"
                                  >
                                    <div
                                      class="emotion-11"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--headline-4"
                                            style="color: #FFF;"
                                          >
                                            Headline
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-15"
                                >
                                  <div
                                    class="emotion-16"
                                  >
                                    <div
                                      class="emotion-17"
                                    >
                                      <div>
                                        <a
                                          class="emotion-18"
                                          color="light"
                                          href="/cta1"
                                        >
                                          CTA 1
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-19"
                                          color="light"
                                          href="/cta2"
                                        >
                                          <span
                                            class="emotion-20"
                                          >
                                            CTA 2
                                          </span>
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-21"
                                  >
                                    <div
                                      class="emotion-22"
                                    >
                                      <div
                                        class="emotion-23"
                                      >
                                        <span
                                          class="emotion-24"
                                        >
                                          Prefix
                                        </span>
                                      </div>
                                      <button
                                        class="emotion-25"
                                      >
                                        Details
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </section>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <section
                            class="emotion-26"
                          >
                            <div>
                              <div
                                class="emotion-4"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="A man"
                                  class="emotion-5"
                                  src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/FA222975_M_boot_REV?fmt=webp"
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="flex-content-wrapper"
                              >
                                <div
                                  class="emotion-31"
                                />
                                <div
                                  class="emotion-12"
                                >
                                  <div
                                    class="emotion-13"
                                  >
                                    <div
                                      class="emotion-11"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--headline-3"
                                          >
                                            I am the second frame
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-15"
                                >
                                  <div
                                    class="emotion-36"
                                  >
                                    <img
                                      alt=""
                                      class="emotion-37"
                                      icon="[object Object]"
                                      src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                                    />
                                  </div>
                                  <div
                                    class="emotion-16"
                                  >
                                    <div
                                      class="emotion-11"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--body-1"
                                          >
                                            The Quick Brown Fox Jumps Over The Lazy Dog
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-40"
                                  >
                                    <div
                                      class="emotion-17"
                                    >
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.one"
                                        >
                                          CTA ONE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.two"
                                        >
                                          CTA TWO
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.three"
                                        >
                                          CTA THREE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.four"
                                        >
                                          CTA FOUR
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.five"
                                        >
                                          CTA FIVE
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-47"
                                  >
                                    <div
                                      class="emotion-22"
                                    >
                                      <div
                                        class="emotion-23"
                                      >
                                        <span
                                          class="emotion-24"
                                        >
                                          Prefixo
                                        </span>
                                      </div>
                                      <button
                                        class="emotion-25"
                                      >
                                        Detalhes
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </section>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <section
                            class="emotion-26"
                          >
                            <div>
                              <div
                                class="emotion-4"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="A man"
                                  class="emotion-5"
                                  src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/FA222975_M_boot_REV?fmt=webp"
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="flex-content-wrapper"
                              >
                                <div
                                  class="emotion-31"
                                />
                                <div
                                  class="emotion-12"
                                >
                                  <div
                                    class="emotion-13"
                                  >
                                    <div
                                      class="emotion-11"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--headline-3"
                                          >
                                            I am the second frame
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-15"
                                >
                                  <div
                                    class="emotion-36"
                                  >
                                    <img
                                      alt=""
                                      class="emotion-37"
                                      icon="[object Object]"
                                      src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                                    />
                                  </div>
                                  <div
                                    class="emotion-16"
                                  >
                                    <div
                                      class="emotion-11"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--body-1"
                                          >
                                            The Quick Brown Fox Jumps Over The Lazy Dog
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-40"
                                  >
                                    <div
                                      class="emotion-17"
                                    >
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.one"
                                        >
                                          CTA ONE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.two"
                                        >
                                          CTA TWO
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.three"
                                        >
                                          CTA THREE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.four"
                                        >
                                          CTA FOUR
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.five"
                                        >
                                          CTA FIVE
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-47"
                                  >
                                    <div
                                      class="emotion-22"
                                    >
                                      <div
                                        class="emotion-23"
                                      >
                                        <span
                                          class="emotion-24"
                                        >
                                          Prefixo
                                        </span>
                                      </div>
                                      <button
                                        class="emotion-25"
                                      >
                                        Detalhes
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </section>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="slick-dots"
                    data-alignment="center"
                  >
                    <ul
                      class="gap-spotlight-carousel-pagination-wrapper"
                    >
                      <li
                        class="slick-active"
                      >
                        <button
                          aria-hidden="false"
                          aria-label="0"
                          class="gap-spotlight-carousel-button"
                        >
                          <div
                            class="gap-spotlight-carousel-pagination-line"
                          >
                            <div
                              class="gap-spotlight-carousel-pagination-progress"
                              style="animation-duration: 3000ms; animation-delay: 500ms;"
                            />
                          </div>
                        </button>
                      </li>
                      <li
                        class=""
                      >
                        <button
                          aria-hidden="false"
                          aria-label="1"
                          class="gap-spotlight-carousel-button"
                        >
                          <div
                            class="gap-spotlight-carousel-pagination-line"
                          >
                            <div
                              class="gap-spotlight-carousel-pagination-progress"
                              style="animation-duration: 3000ms; animation-delay: 500ms;"
                            />
                          </div>
                        </button>
                      </li>
                      <li
                        class=""
                      >
                        <button
                          aria-hidden="false"
                          aria-label="2"
                          class="gap-spotlight-carousel-button"
                        >
                          <div
                            class="gap-spotlight-carousel-pagination-line"
                          >
                            <div
                              class="gap-spotlight-carousel-pagination-progress"
                              style="animation-duration: 3000ms; animation-delay: 500ms;"
                            />
                          </div>
                        </button>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </nav>
          </div>
          <div
            class="split-view-carousel-frame"
          >
            <section
              class="emotion-26"
            >
              <div>
                <div
                  class="emotion-4"
                  data-testid="product-card-image"
                >
                  <img
                    alt="A man"
                    class="emotion-5"
                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/FA222975_M_boot_REV?fmt=webp"
                  />
                </div>
              </div>
              <div
                class="emotion-7"
              >
                <div
                  class="emotion-8"
                  data-testid="flex-content-wrapper"
                >
                  <div
                    class="emotion-31"
                  />
                  <div
                    class="emotion-12"
                  >
                    <div
                      class="emotion-13"
                    >
                      <div
                        class="emotion-11"
                      >
                        <div>
                          <p
                            class="amp-cms--p"
                            style="text-align:left;"
                          >
                            <span
                              class="amp-cms--headline-3"
                            >
                              I am the second frame
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-15"
                  >
                    <div
                      class="emotion-36"
                    >
                      <img
                        alt=""
                        class="emotion-37"
                        icon="[object Object]"
                        src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                      />
                    </div>
                    <div
                      class="emotion-16"
                    >
                      <div
                        class="emotion-11"
                      >
                        <div>
                          <p
                            class="amp-cms--p"
                            style="text-align:left;"
                          >
                            <span
                              class="amp-cms--body-1"
                            >
                              The Quick Brown Fox Jumps Over The Lazy Dog
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                    <div
                      class="emotion-40"
                    >
                      <div
                        class="emotion-17"
                      >
                        <div>
                          <a
                            class="emotion-42"
                            color="dark"
                            href="https://cta.one"
                          >
                            CTA ONE
                          </a>
                        </div>
                        <div>
                          <a
                            class="emotion-42"
                            color="dark"
                            href="https://cta.two"
                          >
                            CTA TWO
                          </a>
                        </div>
                        <div>
                          <a
                            class="emotion-42"
                            color="dark"
                            href="https://cta.three"
                          >
                            CTA THREE
                          </a>
                        </div>
                        <div>
                          <a
                            class="emotion-42"
                            color="dark"
                            href="https://cta.four"
                          >
                            CTA FOUR
                          </a>
                        </div>
                        <div>
                          <a
                            class="emotion-42"
                            color="dark"
                            href="https://cta.five"
                          >
                            CTA FIVE
                          </a>
                        </div>
                      </div>
                    </div>
                    <div
                      class="emotion-47"
                    >
                      <div
                        class="emotion-22"
                      >
                        <div
                          class="emotion-23"
                        >
                          <span
                            class="emotion-24"
                          >
                            Prefixo
                          </span>
                        </div>
                        <button
                          class="emotion-25"
                        >
                          Detalhes
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          </div>
          <div
            class="split-view-carousel-frame"
          >
            <section
              class="emotion-104"
            >
              <div>
                <div
                  class="emotion-105"
                >
                  <h1
                    role="presentation"
                  >
                    ReactPlayer
                  </h1>
                </div>
              </div>
              <div
                class="emotion-106"
              >
                <div
                  class="emotion-8"
                  data-testid="flex-content-wrapper"
                >
                  <div
                    class="emotion-31"
                  />
                  <div
                    class="emotion-12"
                  >
                    <div
                      class="emotion-13"
                    >
                      <div
                        class="emotion-11"
                      >
                        <div>
                          Testing extra frame
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-112"
                  />
                </div>
              </div>
            </section>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`SpotlightCarouselSplitView for Gap line pagination desktop should render the expected snapshot for bottom right aligned controls 1`] = `
.emotion-0 {
  aspect-ratio: 480/645;
}

.emotion-1 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-1 .slick-list {
  overflow: hidden;
}

.emotion-1 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-1 button.slick-next.slick-arrow.slick-next,
.emotion-1 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: undefined;
  height: 44px;
  width: 44px;
}

.emotion-1 button.slick-next.slick-arrow.slick-next>span,
.emotion-1 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-1 button.slick-next.slick-arrow.slick-next svg,
.emotion-1 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-1 .slick-slide {
  margin: 0;
  width: auto;
  aspect-ratio: 480/645;
}

.emotion-1 .slick-disabled {
  display: none!important;
}

.emotion-1 .slick-next {
  left: calc(100% - 44px);
}

.emotion-1 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-1 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-2 {
  position: relative;
}

.emotion-2 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-2 .slick-slider .slick-track,
.emotion-2 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-2 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-2 .slick-list:focus {
  outline: none;
}

.emotion-2 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-2 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-2 .slick-track:before,
.emotion-2 .slick-track:after {
  display: table;
  content: "";
}

.emotion-2 .slick-track:after {
  clear: both;
}

.emotion-2 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-2 .slick-slide img {
  display: block;
}

.emotion-2 .slick-slide.slick-loading img {
  display: none;
}

.emotion-2 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-2 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-2 .slick-initialized .slick-slide,
.emotion-2 .slick-vertical .slick-slide {
  display: block;
}

.emotion-2 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-2 .slick-loading .slick-track,
.emotion-2 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-2 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-2 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-2 .slick-prev,
.emotion-2 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-2 .slick-prev:hover,
.emotion-2 .slick-next:hover,
.emotion-2 .slick-prev:focus,
.emotion-2 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-2 .slick-prev.slick-disabled,
.emotion-2 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-2 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-2 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-2 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-2 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-2 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-2 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-2 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-2 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-2 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-2 .slick-dots li button:hover,
.emotion-2 .slick-dots li button:focus {
  outline: none;
}

.emotion-2 .slick-dots li button:hover:before,
.emotion-2 .slick-dots li button:focus:before,
.emotion-2 .slick-dots li button:hover:before,
.emotion-2 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-2 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-2.pagination-line .slick-dots {
  position: absolute;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-2.pagination-line .slick-dots[data-alignment='left'] {
  left: 2.5vw;
  bottom: 30px;
  width: 264px;
}

.emotion-2.pagination-line .slick-dots[data-alignment='right'] {
  right: 2.5vw;
  bottom: 30px;
  width: 264px;
}

.emotion-2.pagination-line .slick-dots[data-alignment='center'] {
  right: 0;
  left: 0;
  bottom: 30px;
  width: 264px;
  margin-left: auto;
  margin-right: auto;
}

.emotion-2 .gap-spotlight-carousel-pagination-wrapper {
  text-wrap: nowrap;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 8px;
  width: 100%;
  height: 0;
}

.emotion-2.pagination-line li {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  height: auto!important;
  cursor: default!important;
}

.emotion-2 .slick-dots li {
  margin: 0!important;
}

.emotion-2 .slick-dots li button {
  padding: 0!important;
}

.emotion-2 .gap-spotlight-carousel-pagination-line {
  position: relative;
  width: 100%;
  height: 4px;
  padding: 0;
  background-color: rgba(29, 29, 29, 0.3);
}

.emotion-2 button.gap-spotlight-carousel-button::before {
  content: none;
  display: none;
}

.emotion-2 button.gap-spotlight-carousel-button {
  width: 100%;
}

.emotion-2 .slick-active .gap-spotlight-carousel-pagination-line .gap-spotlight-carousel-pagination-progress {
  height: 4px;
  left: 0;
  position: absolute;
  background-color: white;
  -webkit-animation-name: gap-spotlight-carousel-animation;
  animation-name: gap-spotlight-carousel-animation;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
}

.emotion-3 {
  background: #FF0000;
  aspect-ratio: 480/645;
  position: relative;
  display: grid;
  grid-template-columns: 100%;
  grid-template-rows: 100%;
  width: 100%;
  height: 100%;
}

.emotion-3>* {
  grid-area: 1/1;
}

.emotion-4 {
  -webkit-background-size: cover;
  background-size: cover;
  width: 100%;
  height: 100%;
}

.emotion-5 {
  width: 100%;
  aspect-ratio: 480/645;
  object-fit: cover;
}

.emotion-6 {
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
}

.emotion-7 {
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.00) 0%, rgba(0, 0, 0, 0.40) 50.61%, rgba(0, 0, 0, 0.00) 100%);
  width: 100%;
  pointer-events: none;
  z-index: 1;
}

.emotion-8 {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column nowrap;
  -webkit-flex-flow: column nowrap;
  -ms-flex-flow: column nowrap;
  flex-flow: column nowrap;
  padding: 30px 2.5vw;
  aspect-ratio: 480/645;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-9 {
  box-sizing: border-box;
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: calc(100% / 3);
  -ms-flex-preferred-size: calc(100% / 3);
  flex-basis: calc(100% / 3);
  height: 100%;
  padding-top: 0px;
  padding-bottom: 30px;
  min-height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 30px 20px;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  -webkit-align-content: start;
  -ms-flex-line-pack: start;
  align-content: start;
}

.emotion-10 {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  padding-bottom: 0;
  -webkit-align-self: start;
  -ms-flex-item-align: start;
  align-self: start;
  -webkit-order: 1;
  -ms-flex-order: 1;
  order: 1;
}

.emotion-11 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-11 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-11 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-11 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-11 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-11 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-11 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 70px;
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-size: 114px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-11 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-11 .amp-cms--promoAlt-1 {
  color: #000000;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-11 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-12 {
  box-sizing: border-box;
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: calc(100% / 3);
  -ms-flex-preferred-size: calc(100% / 3);
  flex-basis: calc(100% / 3);
  height: 100%;
  padding-top: 0px;
  padding-bottom: 0px;
  min-height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 30px 20px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
}

.emotion-13 {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 0;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  -webkit-order: 1;
  -ms-flex-order: 1;
  order: 1;
}

.emotion-15 {
  box-sizing: border-box;
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: calc(100% / 3);
  -ms-flex-preferred-size: calc(100% / 3);
  flex-basis: calc(100% / 3);
  height: 100%;
  padding-top: 30px;
  padding-bottom: 0px;
  min-height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 30px 20px;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  -webkit-align-content: end;
  -ms-flex-line-pack: end;
  align-content: end;
}

.emotion-16 {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  padding-bottom: 0;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
  -webkit-order: 1;
  -ms-flex-order: 1;
  order: 1;
}

.emotion-17 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-flex-flow: row;
  -webkit-flex-flow: row;
  -ms-flex-flow: row;
  flex-flow: row;
  gap: 20px;
}

.emotion-18 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 18px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background: transparent;
  border-color: #FFFFFF;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
  line-height: 18px;
  text-underline-offset: 14px;
  line-height: 18px;
  pointer-events: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-height: 28px;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
}

.emotion-18:focus {
  outline: none;
}

.emotion-18:hover,
.emotion-18:focus {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-18:active {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-19 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 18px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 24px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #FFFFFF;
  height: auto;
  padding-bottom: 2.3px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 2.3px;
  text-decoration-thickness: 2.3px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
  text-underline-offset: 5px;
  line-height: 18px;
  text-underline-offset: 5px;
  line-height: 18px;
  pointer-events: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-height: 28px;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  border: none;
}

.emotion-19:focus {
  outline: none;
}

.emotion-19:hover,
.emotion-19:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-19:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-20 {
  box-sizing: border-box;
}

.emotion-21 {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  padding-bottom: 0;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
  -webkit-order: 0;
  -ms-flex-order: 0;
  order: 0;
}

.emotion-22 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: absolute;
  bottom: 0;
  right: 0;
  padding: 10px;
  gap: 5px;
  z-index: 1;
  position: initial;
  padding: 0;
  pointer-events: all;
}

.emotion-23 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 0;
}

.emotion-24 {
  color: #000000;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
  text-transform: none;
  font-weight: normal;
  min-height: auto;
  color: #FFFFFF;
  font-size: 12px;
}

.emotion-25 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  min-height: auto;
}

.emotion-25:focus {
  outline: none;
}

.emotion-25 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-25 span span {
  padding-left: initial;
}

.emotion-25:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-25:focus-visible {
  outline: auto;
}

.emotion-26 {
  background: #CCCCCC;
  aspect-ratio: 480/645;
  position: relative;
  display: grid;
  grid-template-columns: 100%;
  grid-template-rows: 100%;
  width: 100%;
  height: 100%;
}

.emotion-26>* {
  grid-area: 1/1;
}

.emotion-31 {
  box-sizing: border-box;
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: calc(100% / 3);
  -ms-flex-preferred-size: calc(100% / 3);
  flex-basis: calc(100% / 3);
  height: 100%;
  padding-top: 0px;
  padding-bottom: 0px;
  min-height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 30px 20px;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  -webkit-align-content: start;
  -ms-flex-line-pack: start;
  align-content: start;
}

.emotion-36 {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: 0;
  -ms-flex-preferred-size: 0;
  flex-basis: 0;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  padding-bottom: 0;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
  -webkit-order: 1;
  -ms-flex-order: 1;
  order: 1;
}

.emotion-37 {
  height: 105px;
}

.emotion-40 {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: fit-content;
  -ms-flex-preferred-size: fit-content;
  flex-basis: fit-content;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  padding-bottom: 0;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
  -webkit-order: 1;
  -ms-flex-order: 1;
  order: 1;
}

.emotion-42 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 18px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
  line-height: 18px;
  text-underline-offset: 14px;
  line-height: 18px;
  pointer-events: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-height: 28px;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
}

.emotion-42:focus {
  outline: none;
}

.emotion-42:hover,
.emotion-42:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-42:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-47 {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: fit-content;
  -ms-flex-preferred-size: fit-content;
  flex-basis: fit-content;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: right;
  -ms-flex-pack: right;
  -webkit-justify-content: right;
  justify-content: right;
  padding-bottom: 19px;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
  -webkit-order: 2;
  -ms-flex-order: 2;
  order: 2;
}

.emotion-104 {
  background: #FFFFFF;
  aspect-ratio: 480/645;
  position: relative;
  display: grid;
  grid-template-columns: 100%;
  grid-template-rows: 100%;
  width: 100%;
  height: 100%;
}

.emotion-104>* {
  grid-area: 1/1;
}

.emotion-105 {
  position: relative;
  height: 100%;
  pointer-events: auto;
  aspect-ratio: 480/645;
  width: 100%;
}

.emotion-106 {
  background: transparent;
  width: 100%;
  pointer-events: none;
  z-index: 1;
}

.emotion-112 {
  box-sizing: border-box;
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: calc(100% / 3);
  -ms-flex-preferred-size: calc(100% / 3);
  flex-basis: calc(100% / 3);
  height: 100%;
  padding-top: 0px;
  padding-bottom: 0px;
  min-height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 30px 20px;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  -webkit-align-content: end;
  -ms-flex-line-pack: end;
  align-content: end;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <style>
        
    :root {
      --mui_spotlight-carousel--split-view-aspect-ratio: 480 / 645;
      --mui_spotlight-carousel--split-view-width: 33.333333333333336%;
      --mui_spotlight-carousel--split-view-maxheight: 645px;
  
      </style>
      <div
        class="emotion-0"
      >
        <div
          class="mui_spotlight-carousel-split-view"
          data-testid="spotlight-carousel-split-view"
        >
          <div
            class="split-view-carousel-frame"
          >
            <nav
              class="emotion-1"
              origin="SpotlightSplitViewCarousel"
            >
              <div
                class="split-view-carousel pagination-line emotion-2"
              >
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <section
                            class="emotion-3"
                          >
                            <div>
                              <div
                                class="emotion-4"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="People"
                                  class="emotion-5"
                                  src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/ADU_21_FA3_GOL_OF__ARCH_J_D_B__2274_WB?fmt=webp"
                                />
                              </div>
                              <a
                                aria-label="Link Not Found"
                                class="emotion-6"
                                data-testid="conditional-link"
                                href="link.not.found"
                                target="_self"
                              />
                            </div>
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="flex-content-wrapper"
                              >
                                <div
                                  class="emotion-9"
                                >
                                  <div
                                    class="emotion-10"
                                  >
                                    <div
                                      class="emotion-11"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--body-1"
                                            style="color: #FFF;"
                                          >
                                            The quick brown fox jumps over the lazy dog
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-12"
                                >
                                  <div
                                    class="emotion-13"
                                  >
                                    <div
                                      class="emotion-11"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--headline-4"
                                            style="color: #FFF;"
                                          >
                                            Headline
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-15"
                                >
                                  <div
                                    class="emotion-16"
                                  >
                                    <div
                                      class="emotion-17"
                                    >
                                      <div>
                                        <a
                                          class="emotion-18"
                                          color="light"
                                          href="/cta1"
                                        >
                                          CTA 1
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-19"
                                          color="light"
                                          href="/cta2"
                                        >
                                          <span
                                            class="emotion-20"
                                          >
                                            CTA 2
                                          </span>
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-21"
                                  >
                                    <div
                                      class="emotion-22"
                                    >
                                      <div
                                        class="emotion-23"
                                      >
                                        <span
                                          class="emotion-24"
                                        >
                                          Prefix
                                        </span>
                                      </div>
                                      <button
                                        class="emotion-25"
                                      >
                                        Details
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </section>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <section
                            class="emotion-26"
                          >
                            <div>
                              <div
                                class="emotion-4"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="A man"
                                  class="emotion-5"
                                  src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/FA222975_M_boot_REV?fmt=webp"
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="flex-content-wrapper"
                              >
                                <div
                                  class="emotion-31"
                                />
                                <div
                                  class="emotion-12"
                                >
                                  <div
                                    class="emotion-13"
                                  >
                                    <div
                                      class="emotion-11"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--headline-3"
                                          >
                                            I am the second frame
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-15"
                                >
                                  <div
                                    class="emotion-36"
                                  >
                                    <img
                                      alt=""
                                      class="emotion-37"
                                      icon="[object Object]"
                                      src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                                    />
                                  </div>
                                  <div
                                    class="emotion-16"
                                  >
                                    <div
                                      class="emotion-11"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--body-1"
                                          >
                                            The Quick Brown Fox Jumps Over The Lazy Dog
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-40"
                                  >
                                    <div
                                      class="emotion-17"
                                    >
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.one"
                                        >
                                          CTA ONE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.two"
                                        >
                                          CTA TWO
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.three"
                                        >
                                          CTA THREE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.four"
                                        >
                                          CTA FOUR
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.five"
                                        >
                                          CTA FIVE
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-47"
                                  >
                                    <div
                                      class="emotion-22"
                                    >
                                      <div
                                        class="emotion-23"
                                      >
                                        <span
                                          class="emotion-24"
                                        >
                                          Prefixo
                                        </span>
                                      </div>
                                      <button
                                        class="emotion-25"
                                      >
                                        Detalhes
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </section>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <section
                            class="emotion-26"
                          >
                            <div>
                              <div
                                class="emotion-4"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="A man"
                                  class="emotion-5"
                                  src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/FA222975_M_boot_REV?fmt=webp"
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="flex-content-wrapper"
                              >
                                <div
                                  class="emotion-31"
                                />
                                <div
                                  class="emotion-12"
                                >
                                  <div
                                    class="emotion-13"
                                  >
                                    <div
                                      class="emotion-11"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--headline-3"
                                          >
                                            I am the second frame
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-15"
                                >
                                  <div
                                    class="emotion-36"
                                  >
                                    <img
                                      alt=""
                                      class="emotion-37"
                                      icon="[object Object]"
                                      src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                                    />
                                  </div>
                                  <div
                                    class="emotion-16"
                                  >
                                    <div
                                      class="emotion-11"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--body-1"
                                          >
                                            The Quick Brown Fox Jumps Over The Lazy Dog
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-40"
                                  >
                                    <div
                                      class="emotion-17"
                                    >
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.one"
                                        >
                                          CTA ONE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.two"
                                        >
                                          CTA TWO
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.three"
                                        >
                                          CTA THREE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.four"
                                        >
                                          CTA FOUR
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.five"
                                        >
                                          CTA FIVE
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-47"
                                  >
                                    <div
                                      class="emotion-22"
                                    >
                                      <div
                                        class="emotion-23"
                                      >
                                        <span
                                          class="emotion-24"
                                        >
                                          Prefixo
                                        </span>
                                      </div>
                                      <button
                                        class="emotion-25"
                                      >
                                        Detalhes
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </section>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="slick-dots"
                    data-alignment="right"
                  >
                    <ul
                      class="gap-spotlight-carousel-pagination-wrapper"
                    >
                      <li
                        class="slick-active"
                      >
                        <button
                          aria-hidden="false"
                          aria-label="0"
                          class="gap-spotlight-carousel-button"
                        >
                          <div
                            class="gap-spotlight-carousel-pagination-line"
                          >
                            <div
                              class="gap-spotlight-carousel-pagination-progress"
                              style="animation-duration: 3000ms; animation-delay: 500ms;"
                            />
                          </div>
                        </button>
                      </li>
                      <li
                        class=""
                      >
                        <button
                          aria-hidden="false"
                          aria-label="1"
                          class="gap-spotlight-carousel-button"
                        >
                          <div
                            class="gap-spotlight-carousel-pagination-line"
                          >
                            <div
                              class="gap-spotlight-carousel-pagination-progress"
                              style="animation-duration: 3000ms; animation-delay: 500ms;"
                            />
                          </div>
                        </button>
                      </li>
                      <li
                        class=""
                      >
                        <button
                          aria-hidden="false"
                          aria-label="2"
                          class="gap-spotlight-carousel-button"
                        >
                          <div
                            class="gap-spotlight-carousel-pagination-line"
                          >
                            <div
                              class="gap-spotlight-carousel-pagination-progress"
                              style="animation-duration: 3000ms; animation-delay: 500ms;"
                            />
                          </div>
                        </button>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </nav>
          </div>
          <div
            class="split-view-carousel-frame"
          >
            <section
              class="emotion-26"
            >
              <div>
                <div
                  class="emotion-4"
                  data-testid="product-card-image"
                >
                  <img
                    alt="A man"
                    class="emotion-5"
                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/FA222975_M_boot_REV?fmt=webp"
                  />
                </div>
              </div>
              <div
                class="emotion-7"
              >
                <div
                  class="emotion-8"
                  data-testid="flex-content-wrapper"
                >
                  <div
                    class="emotion-31"
                  />
                  <div
                    class="emotion-12"
                  >
                    <div
                      class="emotion-13"
                    >
                      <div
                        class="emotion-11"
                      >
                        <div>
                          <p
                            class="amp-cms--p"
                            style="text-align:left;"
                          >
                            <span
                              class="amp-cms--headline-3"
                            >
                              I am the second frame
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-15"
                  >
                    <div
                      class="emotion-36"
                    >
                      <img
                        alt=""
                        class="emotion-37"
                        icon="[object Object]"
                        src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                      />
                    </div>
                    <div
                      class="emotion-16"
                    >
                      <div
                        class="emotion-11"
                      >
                        <div>
                          <p
                            class="amp-cms--p"
                            style="text-align:left;"
                          >
                            <span
                              class="amp-cms--body-1"
                            >
                              The Quick Brown Fox Jumps Over The Lazy Dog
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                    <div
                      class="emotion-40"
                    >
                      <div
                        class="emotion-17"
                      >
                        <div>
                          <a
                            class="emotion-42"
                            color="dark"
                            href="https://cta.one"
                          >
                            CTA ONE
                          </a>
                        </div>
                        <div>
                          <a
                            class="emotion-42"
                            color="dark"
                            href="https://cta.two"
                          >
                            CTA TWO
                          </a>
                        </div>
                        <div>
                          <a
                            class="emotion-42"
                            color="dark"
                            href="https://cta.three"
                          >
                            CTA THREE
                          </a>
                        </div>
                        <div>
                          <a
                            class="emotion-42"
                            color="dark"
                            href="https://cta.four"
                          >
                            CTA FOUR
                          </a>
                        </div>
                        <div>
                          <a
                            class="emotion-42"
                            color="dark"
                            href="https://cta.five"
                          >
                            CTA FIVE
                          </a>
                        </div>
                      </div>
                    </div>
                    <div
                      class="emotion-47"
                    >
                      <div
                        class="emotion-22"
                      >
                        <div
                          class="emotion-23"
                        >
                          <span
                            class="emotion-24"
                          >
                            Prefixo
                          </span>
                        </div>
                        <button
                          class="emotion-25"
                        >
                          Detalhes
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          </div>
          <div
            class="split-view-carousel-frame"
          >
            <section
              class="emotion-104"
            >
              <div>
                <div
                  class="emotion-105"
                >
                  <h1
                    role="presentation"
                  >
                    ReactPlayer
                  </h1>
                </div>
              </div>
              <div
                class="emotion-106"
              >
                <div
                  class="emotion-8"
                  data-testid="flex-content-wrapper"
                >
                  <div
                    class="emotion-31"
                  />
                  <div
                    class="emotion-12"
                  >
                    <div
                      class="emotion-13"
                    >
                      <div
                        class="emotion-11"
                      >
                        <div>
                          Testing extra frame
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-112"
                  />
                </div>
              </div>
            </section>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`SpotlightCarouselSplitView for Gap should match snapshot on desktop 1`] = `
.emotion-0 {
  aspect-ratio: 480/645;
}

.emotion-1 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-1 .slick-list {
  overflow: hidden;
}

.emotion-1 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-1 button.slick-next.slick-arrow.slick-next,
.emotion-1 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: undefined;
  height: 44px;
  width: 44px;
}

.emotion-1 button.slick-next.slick-arrow.slick-next>span,
.emotion-1 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-1 button.slick-next.slick-arrow.slick-next svg,
.emotion-1 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-1 .slick-slide {
  margin: 0;
  width: auto;
  aspect-ratio: 480/645;
}

.emotion-1 .slick-disabled {
  display: none!important;
}

.emotion-1 .slick-next {
  left: calc(100% - 44px);
}

.emotion-1 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-1 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-1 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-2 {
  position: relative;
}

.emotion-2 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-2 .slick-slider .slick-track,
.emotion-2 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-2 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-2 .slick-list:focus {
  outline: none;
}

.emotion-2 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-2 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-2 .slick-track:before,
.emotion-2 .slick-track:after {
  display: table;
  content: "";
}

.emotion-2 .slick-track:after {
  clear: both;
}

.emotion-2 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-2 .slick-slide img {
  display: block;
}

.emotion-2 .slick-slide.slick-loading img {
  display: none;
}

.emotion-2 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-2 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-2 .slick-initialized .slick-slide,
.emotion-2 .slick-vertical .slick-slide {
  display: block;
}

.emotion-2 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-2 .slick-loading .slick-track,
.emotion-2 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-2 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-2 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-2 .slick-prev,
.emotion-2 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-2 .slick-prev:hover,
.emotion-2 .slick-next:hover,
.emotion-2 .slick-prev:focus,
.emotion-2 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-2 .slick-prev.slick-disabled,
.emotion-2 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-2 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-2 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-2 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-2 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-2 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-2 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-2 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-2 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-2 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-2 .slick-dots li button:hover,
.emotion-2 .slick-dots li button:focus {
  outline: none;
}

.emotion-2 .slick-dots li button:hover:before,
.emotion-2 .slick-dots li button:focus:before,
.emotion-2 .slick-dots li button:hover:before,
.emotion-2 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-2 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-2 .slick-dots {
  position: unset;
  line-height: 0;
}

.emotion-3 {
  background: #FF0000;
  aspect-ratio: 480/645;
  position: relative;
  display: grid;
  grid-template-columns: 100%;
  grid-template-rows: 100%;
  width: 100%;
  height: 100%;
}

.emotion-3>* {
  grid-area: 1/1;
}

.emotion-4 {
  -webkit-background-size: cover;
  background-size: cover;
  width: 100%;
  height: 100%;
}

.emotion-5 {
  width: 100%;
  aspect-ratio: 480/645;
  object-fit: cover;
}

.emotion-6 {
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
}

.emotion-7 {
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.00) 0%, rgba(0, 0, 0, 0.40) 50.61%, rgba(0, 0, 0, 0.00) 100%);
  width: 100%;
  pointer-events: none;
  z-index: 1;
}

.emotion-8 {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-flow: column nowrap;
  -webkit-flex-flow: column nowrap;
  -ms-flex-flow: column nowrap;
  flex-flow: column nowrap;
  padding: 30px 2.5vw;
  aspect-ratio: 480/645;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-9 {
  box-sizing: border-box;
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: calc(100% / 3);
  -ms-flex-preferred-size: calc(100% / 3);
  flex-basis: calc(100% / 3);
  height: 100%;
  padding-top: 0px;
  padding-bottom: 30px;
  min-height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 30px 20px;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  -webkit-align-content: start;
  -ms-flex-line-pack: start;
  align-content: start;
}

.emotion-10 {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  padding-bottom: 0;
  -webkit-align-self: start;
  -ms-flex-item-align: start;
  align-self: start;
  -webkit-order: 1;
  -ms-flex-order: 1;
  order: 1;
}

.emotion-11 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-11 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-11 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-11 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-11 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-11 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-11 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 70px;
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 114px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-11 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-11 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-11 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-12 {
  box-sizing: border-box;
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: calc(100% / 3);
  -ms-flex-preferred-size: calc(100% / 3);
  flex-basis: calc(100% / 3);
  height: 100%;
  padding-top: 0px;
  padding-bottom: 0px;
  min-height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 30px 20px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
}

.emotion-13 {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding-bottom: 0;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  -webkit-order: 1;
  -ms-flex-order: 1;
  order: 1;
}

.emotion-15 {
  box-sizing: border-box;
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: calc(100% / 3);
  -ms-flex-preferred-size: calc(100% / 3);
  flex-basis: calc(100% / 3);
  height: 100%;
  padding-top: 30px;
  padding-bottom: 0px;
  min-height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 30px 20px;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  -webkit-align-content: end;
  -ms-flex-line-pack: end;
  align-content: end;
}

.emotion-16 {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  padding-bottom: 0;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
  -webkit-order: 1;
  -ms-flex-order: 1;
  order: 1;
}

.emotion-17 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-flex-flow: row;
  -webkit-flex-flow: row;
  -ms-flex-flow: row;
  flex-flow: row;
  gap: 20px;
}

.emotion-18 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 18px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #FFFFFF;
  background: transparent;
  border-color: #FFFFFF;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
  line-height: 18px;
  text-underline-offset: 14px;
  line-height: 18px;
  pointer-events: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-height: 28px;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
}

.emotion-18:focus {
  outline: none;
}

.emotion-18:hover,
.emotion-18:focus {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-18:active {
  color: #000000;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.emotion-19 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 18px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 24px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #FFFFFF;
  height: auto;
  padding-bottom: 2.3px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 2.3px;
  text-decoration-thickness: 2.3px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
  text-underline-offset: 5px;
  line-height: 18px;
  text-underline-offset: 5px;
  line-height: 18px;
  pointer-events: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-height: 28px;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  border: none;
}

.emotion-19:focus {
  outline: none;
}

.emotion-19:hover,
.emotion-19:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-19:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-20 {
  box-sizing: border-box;
}

.emotion-21 {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: auto;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  padding-bottom: 0;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
  -webkit-order: 0;
  -ms-flex-order: 0;
  order: 0;
}

.emotion-22 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: absolute;
  bottom: 0;
  right: 0;
  padding: 10px;
  gap: 5px;
  z-index: 1;
  position: initial;
  padding: 0;
  pointer-events: all;
}

.emotion-23 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 0;
}

.emotion-24 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
  text-transform: none;
  font-weight: normal;
  min-height: auto;
  color: #FFFFFF;
  font-size: 12px;
}

.emotion-25 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  min-height: auto;
}

.emotion-25:focus {
  outline: none;
}

.emotion-25 span svg path {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-25 span span {
  padding-left: initial;
}

.emotion-25:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-25:focus-visible {
  outline: auto;
}

.emotion-26 {
  background: #CCCCCC;
  aspect-ratio: 480/645;
  position: relative;
  display: grid;
  grid-template-columns: 100%;
  grid-template-rows: 100%;
  width: 100%;
  height: 100%;
}

.emotion-26>* {
  grid-area: 1/1;
}

.emotion-31 {
  box-sizing: border-box;
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: calc(100% / 3);
  -ms-flex-preferred-size: calc(100% / 3);
  flex-basis: calc(100% / 3);
  height: 100%;
  padding-top: 0px;
  padding-bottom: 0px;
  min-height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 30px 20px;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  -webkit-align-content: start;
  -ms-flex-line-pack: start;
  align-content: start;
}

.emotion-36 {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: 0;
  -ms-flex-preferred-size: 0;
  flex-basis: 0;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  padding-bottom: 0;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
  -webkit-order: 1;
  -ms-flex-order: 1;
  order: 1;
}

.emotion-37 {
  height: 105px;
}

.emotion-40 {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: fit-content;
  -ms-flex-preferred-size: fit-content;
  flex-basis: fit-content;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  padding-bottom: 0;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
  -webkit-order: 1;
  -ms-flex-order: 1;
  order: 1;
}

.emotion-42 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 18px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
  line-height: 18px;
  text-underline-offset: 14px;
  line-height: 18px;
  pointer-events: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-height: 28px;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
}

.emotion-42:focus {
  outline: none;
}

.emotion-42:hover,
.emotion-42:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-42:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-47 {
  box-sizing: border-box;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: fit-content;
  -ms-flex-preferred-size: fit-content;
  flex-basis: fit-content;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: right;
  -ms-flex-pack: right;
  -webkit-justify-content: right;
  justify-content: right;
  padding-bottom: 0;
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
  -webkit-order: 2;
  -ms-flex-order: 2;
  order: 2;
}

.emotion-104 {
  background: #FFFFFF;
  aspect-ratio: 480/645;
  position: relative;
  display: grid;
  grid-template-columns: 100%;
  grid-template-rows: 100%;
  width: 100%;
  height: 100%;
}

.emotion-104>* {
  grid-area: 1/1;
}

.emotion-105 {
  position: relative;
  height: 100%;
  pointer-events: auto;
  aspect-ratio: 480/645;
  width: 100%;
}

.emotion-106 {
  background: transparent;
  width: 100%;
  pointer-events: none;
  z-index: 1;
}

.emotion-112 {
  box-sizing: border-box;
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -webkit-flex-shrink: 1;
  -ms-flex-negative: 1;
  flex-shrink: 1;
  -webkit-flex-basis: calc(100% / 3);
  -ms-flex-preferred-size: calc(100% / 3);
  flex-basis: calc(100% / 3);
  height: 100%;
  padding-top: 0px;
  padding-bottom: 0px;
  min-height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 30px 20px;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  -webkit-align-content: end;
  -ms-flex-line-pack: end;
  align-content: end;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <style>
        
    :root {
      --mui_spotlight-carousel--split-view-aspect-ratio: 480 / 645;
      --mui_spotlight-carousel--split-view-width: 33.333333333333336%;
      --mui_spotlight-carousel--split-view-maxheight: 645px;
  
      </style>
      <div
        class="emotion-0"
      >
        <div
          class="mui_spotlight-carousel-split-view"
          data-testid="spotlight-carousel-split-view"
        >
          <div
            class="split-view-carousel-frame"
          >
            <nav
              class="emotion-1"
              origin="SpotlightSplitViewCarousel"
            >
              <div
                class="split-view-carousel  emotion-2"
              >
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <section
                            class="emotion-3"
                          >
                            <div>
                              <div
                                class="emotion-4"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="People"
                                  class="emotion-5"
                                  src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/ADU_21_FA3_GOL_OF__ARCH_J_D_B__2274_WB?fmt=webp"
                                />
                              </div>
                              <a
                                aria-label="Link Not Found"
                                class="emotion-6"
                                data-testid="conditional-link"
                                href="link.not.found"
                                target="_self"
                              />
                            </div>
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="flex-content-wrapper"
                              >
                                <div
                                  class="emotion-9"
                                >
                                  <div
                                    class="emotion-10"
                                  >
                                    <div
                                      class="emotion-11"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--body-1"
                                            style="color: #FFF;"
                                          >
                                            The quick brown fox jumps over the lazy dog
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-12"
                                >
                                  <div
                                    class="emotion-13"
                                  >
                                    <div
                                      class="emotion-11"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--headline-4"
                                            style="color: #FFF;"
                                          >
                                            Headline
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-15"
                                >
                                  <div
                                    class="emotion-16"
                                  >
                                    <div
                                      class="emotion-17"
                                    >
                                      <div>
                                        <a
                                          class="emotion-18"
                                          color="light"
                                          href="/cta1"
                                        >
                                          CTA 1
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-19"
                                          color="light"
                                          href="/cta2"
                                        >
                                          <span
                                            class="emotion-20"
                                          >
                                            CTA 2
                                          </span>
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-21"
                                  >
                                    <div
                                      class="emotion-22"
                                    >
                                      <div
                                        class="emotion-23"
                                      >
                                        <span
                                          class="emotion-24"
                                        >
                                          Prefix
                                        </span>
                                      </div>
                                      <button
                                        class="emotion-25"
                                      >
                                        Details
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </section>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <section
                            class="emotion-26"
                          >
                            <div>
                              <div
                                class="emotion-4"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="A man"
                                  class="emotion-5"
                                  src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/FA222975_M_boot_REV?fmt=webp"
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="flex-content-wrapper"
                              >
                                <div
                                  class="emotion-31"
                                />
                                <div
                                  class="emotion-12"
                                >
                                  <div
                                    class="emotion-13"
                                  >
                                    <div
                                      class="emotion-11"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--headline-3"
                                          >
                                            I am the second frame
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-15"
                                >
                                  <div
                                    class="emotion-36"
                                  >
                                    <img
                                      alt=""
                                      class="emotion-37"
                                      icon="[object Object]"
                                      src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                                    />
                                  </div>
                                  <div
                                    class="emotion-16"
                                  >
                                    <div
                                      class="emotion-11"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--body-1"
                                          >
                                            The Quick Brown Fox Jumps Over The Lazy Dog
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-40"
                                  >
                                    <div
                                      class="emotion-17"
                                    >
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.one"
                                        >
                                          CTA ONE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.two"
                                        >
                                          CTA TWO
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.three"
                                        >
                                          CTA THREE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.four"
                                        >
                                          CTA FOUR
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.five"
                                        >
                                          CTA FIVE
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-47"
                                  >
                                    <div
                                      class="emotion-22"
                                    >
                                      <div
                                        class="emotion-23"
                                      >
                                        <span
                                          class="emotion-24"
                                        >
                                          Prefixo
                                        </span>
                                      </div>
                                      <button
                                        class="emotion-25"
                                      >
                                        Detalhes
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </section>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <section
                            class="emotion-26"
                          >
                            <div>
                              <div
                                class="emotion-4"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt="A man"
                                  class="emotion-5"
                                  src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/FA222975_M_boot_REV?fmt=webp"
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="flex-content-wrapper"
                              >
                                <div
                                  class="emotion-31"
                                />
                                <div
                                  class="emotion-12"
                                >
                                  <div
                                    class="emotion-13"
                                  >
                                    <div
                                      class="emotion-11"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--headline-3"
                                          >
                                            I am the second frame
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="emotion-15"
                                >
                                  <div
                                    class="emotion-36"
                                  >
                                    <img
                                      alt=""
                                      class="emotion-37"
                                      icon="[object Object]"
                                      src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                                    />
                                  </div>
                                  <div
                                    class="emotion-16"
                                  >
                                    <div
                                      class="emotion-11"
                                    >
                                      <div>
                                        <p
                                          class="amp-cms--p"
                                          style="text-align:left;"
                                        >
                                          <span
                                            class="amp-cms--body-1"
                                          >
                                            The Quick Brown Fox Jumps Over The Lazy Dog
                                          </span>
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-40"
                                  >
                                    <div
                                      class="emotion-17"
                                    >
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.one"
                                        >
                                          CTA ONE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.two"
                                        >
                                          CTA TWO
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.three"
                                        >
                                          CTA THREE
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.four"
                                        >
                                          CTA FOUR
                                        </a>
                                      </div>
                                      <div>
                                        <a
                                          class="emotion-42"
                                          color="dark"
                                          href="https://cta.five"
                                        >
                                          CTA FIVE
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                  <div
                                    class="emotion-47"
                                  >
                                    <div
                                      class="emotion-22"
                                    >
                                      <div
                                        class="emotion-23"
                                      >
                                        <span
                                          class="emotion-24"
                                        >
                                          Prefixo
                                        </span>
                                      </div>
                                      <button
                                        class="emotion-25"
                                      >
                                        Detalhes
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </section>
                        </div>
                      </div>
                    </div>
                  </div>
                  <ul
                    class="slick-dots"
                    style="display: block;"
                  >
                    <li
                      class="slick-active"
                    >
                      <button>
                        1
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        2
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        3
                      </button>
                    </li>
                  </ul>
                </div>
              </div>
            </nav>
          </div>
          <div
            class="split-view-carousel-frame"
          >
            <section
              class="emotion-26"
            >
              <div>
                <div
                  class="emotion-4"
                  data-testid="product-card-image"
                >
                  <img
                    alt="A man"
                    class="emotion-5"
                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/FA222975_M_boot_REV?fmt=webp"
                  />
                </div>
              </div>
              <div
                class="emotion-7"
              >
                <div
                  class="emotion-8"
                  data-testid="flex-content-wrapper"
                >
                  <div
                    class="emotion-31"
                  />
                  <div
                    class="emotion-12"
                  >
                    <div
                      class="emotion-13"
                    >
                      <div
                        class="emotion-11"
                      >
                        <div>
                          <p
                            class="amp-cms--p"
                            style="text-align:left;"
                          >
                            <span
                              class="amp-cms--headline-3"
                            >
                              I am the second frame
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-15"
                  >
                    <div
                      class="emotion-36"
                    >
                      <img
                        alt=""
                        class="emotion-37"
                        icon="[object Object]"
                        src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                      />
                    </div>
                    <div
                      class="emotion-16"
                    >
                      <div
                        class="emotion-11"
                      >
                        <div>
                          <p
                            class="amp-cms--p"
                            style="text-align:left;"
                          >
                            <span
                              class="amp-cms--body-1"
                            >
                              The Quick Brown Fox Jumps Over The Lazy Dog
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                    <div
                      class="emotion-40"
                    >
                      <div
                        class="emotion-17"
                      >
                        <div>
                          <a
                            class="emotion-42"
                            color="dark"
                            href="https://cta.one"
                          >
                            CTA ONE
                          </a>
                        </div>
                        <div>
                          <a
                            class="emotion-42"
                            color="dark"
                            href="https://cta.two"
                          >
                            CTA TWO
                          </a>
                        </div>
                        <div>
                          <a
                            class="emotion-42"
                            color="dark"
                            href="https://cta.three"
                          >
                            CTA THREE
                          </a>
                        </div>
                        <div>
                          <a
                            class="emotion-42"
                            color="dark"
                            href="https://cta.four"
                          >
                            CTA FOUR
                          </a>
                        </div>
                        <div>
                          <a
                            class="emotion-42"
                            color="dark"
                            href="https://cta.five"
                          >
                            CTA FIVE
                          </a>
                        </div>
                      </div>
                    </div>
                    <div
                      class="emotion-47"
                    >
                      <div
                        class="emotion-22"
                      >
                        <div
                          class="emotion-23"
                        >
                          <span
                            class="emotion-24"
                          >
                            Prefixo
                          </span>
                        </div>
                        <button
                          class="emotion-25"
                        >
                          Detalhes
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          </div>
          <div
            class="split-view-carousel-frame"
          >
            <section
              class="emotion-104"
            >
              <div>
                <div
                  class="emotion-105"
                >
                  <h1
                    role="presentation"
                  >
                    ReactPlayer
                  </h1>
                </div>
              </div>
              <div
                class="emotion-106"
              >
                <div
                  class="emotion-8"
                  data-testid="flex-content-wrapper"
                >
                  <div
                    class="emotion-31"
                  />
                  <div
                    class="emotion-12"
                  >
                    <div
                      class="emotion-13"
                    >
                      <div
                        class="emotion-11"
                      >
                        <div>
                          Testing extra frame
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-112"
                  />
                </div>
              </div>
            </section>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
