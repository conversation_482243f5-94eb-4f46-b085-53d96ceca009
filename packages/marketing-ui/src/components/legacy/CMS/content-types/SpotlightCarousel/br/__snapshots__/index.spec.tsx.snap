// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SpotlightCarousel for Banana Republic Factory Store should match the snapshot for desktop image 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: auto;
  box-sizing: border-box;
}

.emotion-1 {
  background-color: #E1DCCD;
  padding: 16px 24px;
  position: relative;
}

.emotion-1 .mainContentContainer {
  gap: 20px;
  padding: 0;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding-bottom: 24px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-3 {
  pointer-events: none;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 48px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  max-width: 100%;
  padding: 24px 160px;
  position: relative;
  text-align: center;
  z-index: 3;
}

.emotion-4 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-4 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-4 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-4 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-4 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.625vw);
  line-height: 1.5;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-4 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-4 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-4 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.625vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.625vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.625vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.625vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.0416666666666665vw);
  line-height: 1.5;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-4 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, 0.9375vw);
  line-height: 1.4444444444444444;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-4 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 0.8333333333333334vw);
  line-height: 1.375;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-4 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.7291666666666666vw);
  line-height: 1.4285714285714286;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-4 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.625vw);
  line-height: 1.5;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-4 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.0416666666666665vw);
  line-height: 1;
  letter-spacing: 0.078125vw;
  font-weight: 600;
}

.emotion-4 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1;
  letter-spacing: 0.078125vw;
  font-weight: 600;
}

.emotion-4 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.625vw);
  line-height: 1;
  letter-spacing: 0.078125vw;
  font-weight: 600;
}

.emotion-4 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(48px, 5vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(40px, 4.166666666666666vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, 3.3333333333333335vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(28px, 2.5vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 1.6666666666666667vw);
  line-height: 1.125;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 1.25vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.0416666666666665vw);
  line-height: 1.4;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, 3.3333333333333335vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 2.5vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.6666666666666667vw);
  line-height: 1.0625;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(60px, 6.25vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, 0.5208333333333333vw);
  line-height: 1.6;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-4 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 4.166666666666666vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-4 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),'Times New Roman','serif';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 0.9375vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, 1.25vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-4 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.0416666666666665vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  pointer-events: all;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 32px;
  max-width: 100%;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-6 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: 1px solid #000000;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 600;
  height: 56px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-transition: border-color 200ms;
  transition: border-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 280px;
}

.emotion-6[aria-disabled=true] {
  color: rgba(0, 0, 0, 0.25);
  border-color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

.emotion-7 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #FFFFFF;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 600;
  height: 56px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 280px;
}

.emotion-7[aria-disabled=true] {
  color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

.emotion-8 {
  background-color: transparent;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: inline-block;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 400;
  height: 16px;
  letter-spacing: 0.5px;
  line-height: 1.33;
  outline: 0;
  padding: 0;
  text-align: center;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-decoration-color: #000000;
  text-underline-offset: 5px;
  -webkit-transition: text-decoration-color 200ms;
  transition: text-decoration-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.emotion-8[aria-disabled=true] {
  color: #999999;
  pointer-events: none;
  text-decoration-color: #CCCCCC;
}

.emotion-8:hover:not([aria-disabled=true]) {
  text-decoration-color: #000000;
}

.emotion-9 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: transparent;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 350;
  gap: 8px;
  height: 16px;
  letter-spacing: 0.5px;
  line-height: 1.33;
  outline: 0;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.emotion-9 svg {
  fill: #000000;
  -webkit-transition: width 400ms;
  transition: width 400ms;
}

.emotion-9 svg .arrow-line {
  -webkit-transition: -webkit-transform 400ms;
  transition: transform 400ms;
}

.emotion-9:hover:not([aria-disabled=true]) svg {
  width: 102px;
}

.emotion-9:hover:not([aria-disabled=true]) svg .arrow-line {
  -webkit-transform: translateX(-23px) scaleX(1.28);
  -moz-transform: translateX(-23px) scaleX(1.28);
  -ms-transform: translateX(-23px) scaleX(1.28);
  transform: translateX(-23px) scaleX(1.28);
}

.emotion-9[aria-disabled=true] {
  color: #999999;
  pointer-events: none;
}

.emotion-9[aria-disabled=true] svg {
  fill: #999999;
}

.emotion-10 {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 3;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
  pointer-events: none;
}

.emotion-10 [class^=spotlight-details-container] {
  -webkit-align-items: baseline;
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  align-items: baseline;
}

.emotion-11 {
  font-size: 12px;
  line-height: 1.33;
  padding: 0 16px 12px 16px;
  position: relative;
  z-index: 1;
}

.emotion-12 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 0;
  display: inline;
}

.emotion-13 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
  text-transform: none;
  font-weight: 350;
  min-height: auto;
  color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  font-size: 12px;
  line-height: 1.33;
  letter-spacing: 0.5px;
  padding: 0;
  padding-left: 0.5em;
  text-underline-offset: 4px;
  vertical-align: unset;
  pointer-events: auto;
}

.emotion-14 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  position: relative;
  border: none;
  background-color: transparent;
  color: #000000;
  font-weight: 350;
  text-transform: none;
  z-index: 1;
  color: #000000;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  line-height: 1.33;
  letter-spacing: 0.5px;
  padding: 0;
  padding-left: 0.5em;
  text-underline-offset: 4px;
  vertical-align: unset;
  pointer-events: auto;
}

.emotion-14:focus {
  outline: none;
}

.emotion-14::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0.8em;
  right: 0.8em;
  background-color: transparent;
  display: block;
  height: 2px;
}

.emotion-14 span span {
  padding-left: initial;
}

.emotion-14:focus-visible {
  outline: auto;
}

.emotion-15 {
  aspect-ratio: 1920/800;
}

.emotion-16 {
  position: relative;
}

.emotion-17 {
  box-sizing: border-box;
  height: 100%;
  left: 0;
  outline-offset: unset;
  position: absolute;
  top: 0;
  width: 100%;
}

.emotion-18 {
  aspect-ratio: 1920/800;
  display: grid;
  grid-template-areas: "stacked";
}

.emotion-18>* {
  grid-area: stacked;
  overflow: hidden;
}

.emotion-19 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-20 {
  width: 100%;
  aspect-ratio: 1920/800;
  object-fit: cover;
}

.emotion-21 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: 100%;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  width: 100%;
}

.emotion-22 {
  pointer-events: none;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 48px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  max-width: 100%;
  padding: 24px 160px;
  position: relative;
  text-align: start;
  z-index: 3;
}

.emotion-24 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  pointer-events: all;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 32px;
  max-width: 100%;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
}

.emotion-31 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
  text-transform: none;
  font-weight: 350;
  min-height: auto;
  color: #FFFFFF;
  -webkit-text-decoration: none;
  text-decoration: none;
  font-size: 12px;
  line-height: 1.33;
  letter-spacing: 0.5px;
  padding: 0;
  padding-left: 0.5em;
  text-underline-offset: 4px;
  vertical-align: unset;
  pointer-events: auto;
}

.emotion-32 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 700;
  position: relative;
  border: none;
  background-color: transparent;
  color: #000000;
  font-weight: 350;
  text-transform: none;
  z-index: 1;
  color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  line-height: 1.33;
  letter-spacing: 0.5px;
  padding: 0;
  padding-left: 0.5em;
  text-underline-offset: 4px;
  vertical-align: unset;
  pointer-events: auto;
}

.emotion-32:focus {
  outline: none;
}

.emotion-32::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0.8em;
  right: 0.8em;
  background-color: transparent;
  display: block;
  height: 2px;
}

.emotion-32 span span {
  padding-left: initial;
}

.emotion-32:focus-visible {
  outline: auto;
}

.emotion-38 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: 1px solid #000000;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 9px;
  font-weight: 600;
  height: 32px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-transition: border-color 200ms;
  transition: border-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 244px;
}

.emotion-38[aria-disabled=true] {
  color: rgba(0, 0, 0, 0.25);
  border-color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

.emotion-39 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #FFFFFF;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 9px;
  font-weight: 600;
  height: 32px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 244px;
}

.emotion-39[aria-disabled=true] {
  color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="spotlight-carousel"
    >
      <div
        class="emotion-1"
        data-testid="upper-lower-content-area"
      >
        <div
          class="emotion-2"
        >
          <div
            class="mainContentContainer emotion-3"
            data-testid="full-bleed-content-container"
          >
            <div
              class="emotion-4"
            >
              <div>
                <p
                  class="amp-cms--p"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Upper content text
                  </span>
                </p>
              </div>
            </div>
            <div
              class="emotion-5"
              data-testid="cta-buttons-container"
            >
              <a
                class="emotion-6"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                OUTLINE CTA
              </a>
              <a
                class="emotion-7"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                SOLID CTA
              </a>
              <a
                class="emotion-8"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                UNDERLINE CTA
              </a>
              <a
                class="emotion-9"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                ARROW CTA
                <svg
                  height="6"
                  preserveAspectRatio="xMaxYMin"
                  viewBox="0 0 79 6"
                  width="79"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g>
                    <path
                      class="arrow-head"
                      d="M73.5 0L78.5 3L73.5 6L75.6429 3L73.5 0Z"
                    />
                    <rect
                      class="arrow-line"
                      height="1"
                      width="77"
                      y="2.5"
                    />
                  </g>
                </svg>
              </a>
            </div>
          </div>
        </div>
        <div
          class="emotion-10"
        >
          <div
            class="spotlight-details-container emotion-11"
          >
            <div
              class="emotion-12"
            >
              <span
                class="emotion-13"
              >
                Exclusions apply
              </span>
            </div>
            <button
              class="emotion-14"
            >
              Details in upper content
            </button>
          </div>
        </div>
      </div>
      <div
        class="emotion-15"
      >
        <div
          class="emotion-16"
        >
          <a
            aria-label="image link"
            class="emotion-17"
            href="www.imagelink.com"
            target="_self"
          />
          <div
            data-testid="carousel-frame"
            style="background-color: rgb(255, 255, 255);"
          >
            <section
              class="emotion-18"
            >
              <div
                class="emotion-19"
                data-testid="product-card-image"
              >
                <img
                  alt="spotlight single image alt text"
                  class="emotion-20"
                  src="/img.jpg"
                />
              </div>
              <div
                class="emotion-21"
                data-testid="spotlight-content-full-wrapper"
              >
                <div
                  class="mainContentContainer emotion-22"
                  data-testid="full-bleed-content-container"
                >
                  <div
                    class="emotion-4"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-1"
                        >
                          Main content overlay text
                        </span>
                      </p>
                    </div>
                  </div>
                  <div
                    class="emotion-24"
                    data-testid="cta-buttons-container"
                  >
                    <a
                      class="emotion-7"
                      data-testid="composable-btn-br"
                      href="wwww.something.com"
                      role="button"
                    >
                      MAIN CTA 1
                    </a>
                    <a
                      class="emotion-7"
                      data-testid="composable-btn-br"
                      href="www.somethingelse.com"
                      role="button"
                    >
                      MAIN CTA 2
                    </a>
                    <a
                      class="emotion-7"
                      data-testid="composable-btn-br"
                      href="wwww.something.com"
                      role="button"
                    >
                      MAIN CTA 3
                    </a>
                  </div>
                </div>
              </div>
            </section>
          </div>
          <div
            class="emotion-10"
          >
            <div
              class="spotlight-details-container emotion-11"
            >
              <div
                class="emotion-12"
              >
                <span
                  class="emotion-31"
                >
                  Limited time. Excludes Clearance, Leather and Suede Apparel & Accessories and Gift Cards
                </span>
              </div>
              <button
                class="emotion-32"
              >
                Details
              </button>
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-1"
        data-testid="upper-lower-content-area"
      >
        <div
          class="emotion-2"
        >
          <div
            class="mainContentContainer emotion-3"
            data-testid="full-bleed-content-container"
          >
            <div
              class="emotion-4"
            >
              <div>
                <p
                  class="amp-cms--p"
                >
                  <span
                    class="amp-cms--headline-2"
                  >
                    Lower content text
                  </span>
                </p>
              </div>
            </div>
            <div
              class="emotion-5"
              data-testid="cta-buttons-container"
            >
              <a
                class="emotion-38"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                OUTLINE CTA
              </a>
              <a
                class="emotion-39"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                SOLID CTA
              </a>
              <a
                class="emotion-8"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                UNDERLINE CTA
              </a>
              <a
                class="emotion-9"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                ARROW CTA
                <svg
                  height="6"
                  preserveAspectRatio="xMaxYMin"
                  viewBox="0 0 79 6"
                  width="79"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g>
                    <path
                      class="arrow-head"
                      d="M73.5 0L78.5 3L73.5 6L75.6429 3L73.5 0Z"
                    />
                    <rect
                      class="arrow-line"
                      height="1"
                      width="77"
                      y="2.5"
                    />
                  </g>
                </svg>
              </a>
            </div>
          </div>
        </div>
        <div
          class="emotion-10"
        >
          <div
            class="spotlight-details-container emotion-11"
          >
            <div
              class="emotion-12"
            >
              <span
                class="emotion-13"
              >
                Exclusions apply
              </span>
            </div>
            <button
              class="emotion-14"
            >
              Details in lower content
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`SpotlightCarousel for Banana Republic Spotlight Image should match the snapshot for desktop image 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: auto;
  box-sizing: border-box;
}

.emotion-1 {
  background-color: #E1DCCD;
  padding: 16px 24px;
  position: relative;
}

.emotion-1 .mainContentContainer {
  gap: 20px;
  padding: 0;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding-bottom: 24px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-3 {
  pointer-events: none;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 48px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  max-width: 100%;
  padding: 80px;
  position: relative;
  text-align: center;
  z-index: 3;
}

.emotion-4 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-4 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-4 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-4 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-4 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.625vw);
  line-height: 1.5;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-4 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-4 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-4 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.625vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.625vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.625vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.625vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.0416666666666665vw);
  line-height: 1.5;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-4 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, 0.9375vw);
  line-height: 1.4444444444444444;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-4 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 0.8333333333333334vw);
  line-height: 1.375;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-4 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.7291666666666666vw);
  line-height: 1.4285714285714286;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-4 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.625vw);
  line-height: 1.5;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-4 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.0416666666666665vw);
  line-height: 1;
  letter-spacing: 0.078125vw;
  font-weight: 600;
}

.emotion-4 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1;
  letter-spacing: 0.078125vw;
  font-weight: 600;
}

.emotion-4 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.625vw);
  line-height: 1;
  letter-spacing: 0.078125vw;
  font-weight: 600;
}

.emotion-4 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(48px, 5vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(40px, 4.166666666666666vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, 3.3333333333333335vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(28px, 2.5vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 1.6666666666666667vw);
  line-height: 1.125;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 1.25vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.0416666666666665vw);
  line-height: 1.4;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, 3.3333333333333335vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 2.5vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.6666666666666667vw);
  line-height: 1.0625;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(60px, 6.25vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, 0.5208333333333333vw);
  line-height: 1.6;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-4 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 4.166666666666666vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-4 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 0.9375vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, 1.25vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-4 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.0416666666666665vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  pointer-events: all;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 32px;
  max-width: 100%;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-6 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: 1px solid #000000;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 600;
  height: 56px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-transition: border-color 200ms;
  transition: border-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 280px;
}

.emotion-6[aria-disabled=true] {
  color: rgba(0, 0, 0, 0.25);
  border-color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

.emotion-7 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #FFFFFF;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 600;
  height: 56px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 280px;
}

.emotion-7[aria-disabled=true] {
  color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

.emotion-8 {
  background-color: transparent;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: inline-block;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 400;
  height: 16px;
  letter-spacing: 0.5px;
  line-height: 1.33;
  outline: 0;
  padding: 0;
  text-align: center;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-decoration-color: #000000;
  text-underline-offset: 5px;
  -webkit-transition: text-decoration-color 200ms;
  transition: text-decoration-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.emotion-8[aria-disabled=true] {
  color: #999999;
  pointer-events: none;
  text-decoration-color: #CCCCCC;
}

.emotion-8:hover:not([aria-disabled=true]) {
  text-decoration-color: #000000;
}

.emotion-9 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: transparent;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 350;
  gap: 8px;
  height: 16px;
  letter-spacing: 0.5px;
  line-height: 1.33;
  outline: 0;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.emotion-9 svg {
  fill: #000000;
  -webkit-transition: width 400ms;
  transition: width 400ms;
}

.emotion-9 svg .arrow-line {
  -webkit-transition: -webkit-transform 400ms;
  transition: transform 400ms;
}

.emotion-9:hover:not([aria-disabled=true]) svg {
  width: 102px;
}

.emotion-9:hover:not([aria-disabled=true]) svg .arrow-line {
  -webkit-transform: translateX(-23px) scaleX(1.28);
  -moz-transform: translateX(-23px) scaleX(1.28);
  -ms-transform: translateX(-23px) scaleX(1.28);
  transform: translateX(-23px) scaleX(1.28);
}

.emotion-9[aria-disabled=true] {
  color: #999999;
  pointer-events: none;
}

.emotion-9[aria-disabled=true] svg {
  fill: #999999;
}

.emotion-10 {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 3;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
  pointer-events: none;
}

.emotion-10 [class^=spotlight-details-container] {
  -webkit-align-items: baseline;
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  align-items: baseline;
}

.emotion-11 {
  font-size: 12px;
  line-height: 1.33;
  padding: 0 16px 12px 16px;
  position: relative;
  z-index: 1;
}

.emotion-12 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 0;
  display: inline;
}

.emotion-13 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
  text-transform: none;
  font-weight: 350;
  min-height: auto;
  color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  font-size: 12px;
  line-height: 1.33;
  letter-spacing: 0.5px;
  padding: 0;
  padding-left: 0.5em;
  text-underline-offset: 4px;
  vertical-align: unset;
  pointer-events: auto;
}

.emotion-14 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  position: relative;
  border: none;
  background-color: transparent;
  color: #000000;
  font-weight: 350;
  text-transform: none;
  z-index: 1;
  color: #000000;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  line-height: 1.33;
  letter-spacing: 0.5px;
  padding: 0;
  padding-left: 0.5em;
  text-underline-offset: 4px;
  vertical-align: unset;
  pointer-events: auto;
}

.emotion-14:focus {
  outline: none;
}

.emotion-14::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0.8em;
  right: 0.8em;
  background-color: transparent;
  display: block;
  height: 2px;
}

.emotion-14 span span {
  padding-left: initial;
}

.emotion-14:focus-visible {
  outline: auto;
}

.emotion-15 {
  aspect-ratio: 1920/800;
}

.emotion-16 {
  position: relative;
}

.emotion-17 {
  box-sizing: border-box;
  height: 100%;
  left: 0;
  outline-offset: unset;
  position: absolute;
  top: 0;
  width: 100%;
}

.emotion-18 {
  aspect-ratio: 1920/800;
  display: grid;
  grid-template-areas: "stacked";
}

.emotion-18>* {
  grid-area: stacked;
  overflow: hidden;
}

.emotion-19 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-20 {
  width: 100%;
  aspect-ratio: 1920/800;
  object-fit: cover;
}

.emotion-21 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: 100%;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  width: 100%;
}

.emotion-22 {
  pointer-events: none;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 48px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  max-width: 100%;
  padding: 80px;
  position: relative;
  text-align: start;
  z-index: 3;
}

.emotion-24 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  pointer-events: all;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 32px;
  max-width: 100%;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
}

.emotion-31 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
  text-transform: none;
  font-weight: 350;
  min-height: auto;
  color: #FFFFFF;
  -webkit-text-decoration: none;
  text-decoration: none;
  font-size: 12px;
  line-height: 1.33;
  letter-spacing: 0.5px;
  padding: 0;
  padding-left: 0.5em;
  text-underline-offset: 4px;
  vertical-align: unset;
  pointer-events: auto;
}

.emotion-32 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  position: relative;
  border: none;
  background-color: transparent;
  color: #000000;
  font-weight: 350;
  text-transform: none;
  z-index: 1;
  color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  line-height: 1.33;
  letter-spacing: 0.5px;
  padding: 0;
  padding-left: 0.5em;
  text-underline-offset: 4px;
  vertical-align: unset;
  pointer-events: auto;
}

.emotion-32:focus {
  outline: none;
}

.emotion-32::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0.8em;
  right: 0.8em;
  background-color: transparent;
  display: block;
  height: 2px;
}

.emotion-32 span span {
  padding-left: initial;
}

.emotion-32:focus-visible {
  outline: auto;
}

.emotion-38 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: 1px solid #000000;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 9px;
  font-weight: 600;
  height: 32px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-transition: border-color 200ms;
  transition: border-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 244px;
}

.emotion-38[aria-disabled=true] {
  color: rgba(0, 0, 0, 0.25);
  border-color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

.emotion-39 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #FFFFFF;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 9px;
  font-weight: 600;
  height: 32px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 244px;
}

.emotion-39[aria-disabled=true] {
  color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="spotlight-carousel"
    >
      <div
        class="emotion-1"
        data-testid="upper-lower-content-area"
      >
        <div
          class="emotion-2"
        >
          <div
            class="mainContentContainer emotion-3"
            data-testid="full-bleed-content-container"
          >
            <div
              class="emotion-4"
            >
              <div>
                <p
                  class="amp-cms--p"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Upper content text
                  </span>
                </p>
              </div>
            </div>
            <div
              class="emotion-5"
              data-testid="cta-buttons-container"
            >
              <a
                class="emotion-6"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                OUTLINE CTA
              </a>
              <a
                class="emotion-7"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                SOLID CTA
              </a>
              <a
                class="emotion-8"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                UNDERLINE CTA
              </a>
              <a
                class="emotion-9"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                ARROW CTA
                <svg
                  height="6"
                  preserveAspectRatio="xMaxYMin"
                  viewBox="0 0 79 6"
                  width="79"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g>
                    <path
                      class="arrow-head"
                      d="M73.5 0L78.5 3L73.5 6L75.6429 3L73.5 0Z"
                    />
                    <rect
                      class="arrow-line"
                      height="1"
                      width="77"
                      y="2.5"
                    />
                  </g>
                </svg>
              </a>
            </div>
          </div>
        </div>
        <div
          class="emotion-10"
        >
          <div
            class="spotlight-details-container emotion-11"
          >
            <div
              class="emotion-12"
            >
              <span
                class="emotion-13"
              >
                Exclusions apply
              </span>
            </div>
            <button
              class="emotion-14"
            >
              Details in upper content
            </button>
          </div>
        </div>
      </div>
      <div
        class="emotion-15"
      >
        <div
          class="emotion-16"
        >
          <a
            aria-label="image link"
            class="emotion-17"
            href="www.imagelink.com"
            target="_self"
          />
          <div
            data-testid="carousel-frame"
            style="background-color: rgb(255, 255, 255);"
          >
            <section
              class="emotion-18"
            >
              <div
                class="emotion-19"
                data-testid="product-card-image"
              >
                <img
                  alt="spotlight single image alt text"
                  class="emotion-20"
                  src="/img.jpg"
                />
              </div>
              <div
                class="emotion-21"
                data-testid="spotlight-content-full-wrapper"
              >
                <div
                  class="mainContentContainer emotion-22"
                  data-testid="full-bleed-content-container"
                >
                  <div
                    class="emotion-4"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-1"
                        >
                          Main content overlay text
                        </span>
                      </p>
                    </div>
                  </div>
                  <div
                    class="emotion-24"
                    data-testid="cta-buttons-container"
                  >
                    <a
                      class="emotion-7"
                      data-testid="composable-btn-br"
                      href="wwww.something.com"
                      role="button"
                    >
                      MAIN CTA 1
                    </a>
                    <a
                      class="emotion-7"
                      data-testid="composable-btn-br"
                      href="www.somethingelse.com"
                      role="button"
                    >
                      MAIN CTA 2
                    </a>
                    <a
                      class="emotion-7"
                      data-testid="composable-btn-br"
                      href="wwww.something.com"
                      role="button"
                    >
                      MAIN CTA 3
                    </a>
                  </div>
                </div>
              </div>
            </section>
          </div>
          <div
            class="emotion-10"
          >
            <div
              class="spotlight-details-container emotion-11"
            >
              <div
                class="emotion-12"
              >
                <span
                  class="emotion-31"
                >
                  Limited time. Excludes Clearance, Leather and Suede Apparel & Accessories and Gift Cards
                </span>
              </div>
              <button
                class="emotion-32"
              >
                Details
              </button>
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-1"
        data-testid="upper-lower-content-area"
      >
        <div
          class="emotion-2"
        >
          <div
            class="mainContentContainer emotion-3"
            data-testid="full-bleed-content-container"
          >
            <div
              class="emotion-4"
            >
              <div>
                <p
                  class="amp-cms--p"
                >
                  <span
                    class="amp-cms--headline-2"
                  >
                    Lower content text
                  </span>
                </p>
              </div>
            </div>
            <div
              class="emotion-5"
              data-testid="cta-buttons-container"
            >
              <a
                class="emotion-38"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                OUTLINE CTA
              </a>
              <a
                class="emotion-39"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                SOLID CTA
              </a>
              <a
                class="emotion-8"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                UNDERLINE CTA
              </a>
              <a
                class="emotion-9"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                ARROW CTA
                <svg
                  height="6"
                  preserveAspectRatio="xMaxYMin"
                  viewBox="0 0 79 6"
                  width="79"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g>
                    <path
                      class="arrow-head"
                      d="M73.5 0L78.5 3L73.5 6L75.6429 3L73.5 0Z"
                    />
                    <rect
                      class="arrow-line"
                      height="1"
                      width="77"
                      y="2.5"
                    />
                  </g>
                </svg>
              </a>
            </div>
          </div>
        </div>
        <div
          class="emotion-10"
        >
          <div
            class="spotlight-details-container emotion-11"
          >
            <div
              class="emotion-12"
            >
              <span
                class="emotion-13"
              >
                Exclusions apply
              </span>
            </div>
            <button
              class="emotion-14"
            >
              Details in lower content
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`SpotlightCarousel for Banana Republic Spotlight Image should match the snapshot for mobile image 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: auto;
  box-sizing: border-box;
}

.emotion-1 {
  background-color: #E1DCCD;
  padding: 24px;
  position: relative;
}

.emotion-1 .mainContentContainer {
  gap: 12px;
  padding: 0;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding-bottom: 24px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-3 {
  pointer-events: none;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 24px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  max-width: 100%;
  padding: 16px;
  position: relative;
  text-align: center;
  z-index: 3;
}

.emotion-4 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-4 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-4 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-4 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-4 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, 2.666666666666667vw);
  line-height: 1.8;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-4 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-4 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-4 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.2vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-4 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 4.266666666666667vw);
  line-height: 1.625;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-4 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.733333333333334vw);
  line-height: 1.5714285714285714;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-4 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.2vw);
  line-height: 1.6666666666666667;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-4 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, 2.666666666666667vw);
  line-height: 1.8;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-4 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.733333333333334vw);
  line-height: 1;
  letter-spacing: 0.4vw;
  font-weight: 600;
}

.emotion-4 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, 3.2vw);
  line-height: 1;
  letter-spacing: 0.4vw;
  font-weight: 600;
}

.emotion-4 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1;
  letter-spacing: 0.4vw;
  font-weight: 600;
}

.emotion-4 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 12.8vw);
  line-height: 1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 8.533333333333333vw);
  line-height: 1.0625;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 7.466666666666668vw);
  line-height: 1.0714285714285714;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 6.4vw);
  line-height: 1.0833333333333333;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 4.8vw);
  line-height: 1.1111111111111112;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.733333333333334vw);
  line-height: 1.1428571428571428;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 8.533333333333333vw);
  line-height: 1.0625;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 6.4vw);
  line-height: 1.0833333333333333;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 5.333333333333334vw);
  line-height: 1.1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 16vw);
  line-height: 1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, 2.1333333333333333vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-4 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.733333333333334vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 4.266666666666667vw);
  line-height: 1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-4 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.2vw);
  line-height: 1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-5 {
  display: grid;
  pointer-events: all;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 8px 12px;
  max-width: 100%;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  grid-template-rows: auto;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  width: 100%;
  grid-template-columns: repeat(2, 6fr);
}

.emotion-5 a:nth-child(odd) {
  text-align: end;
}

.emotion-5 a:nth-child(even) {
  text-align: start;
}

.emotion-6 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border: 1px solid #000000;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 9px;
  font-weight: 600;
  height: 32px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-transition: border-color 200ms;
  transition: border-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 244px;
  font-size: 10px;
  width: 160px;
}

.emotion-6[aria-disabled=true] {
  color: rgba(0, 0, 0, 0.25);
  border-color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

@media (max-width: 320px) {
  .emotion-6 {
    width: 130px;
  }
}

.emotion-7 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #FFFFFF;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 9px;
  font-weight: 600;
  height: 32px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 244px;
  font-size: 10px;
  width: 160px;
}

.emotion-7[aria-disabled=true] {
  color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

@media (max-width: 320px) {
  .emotion-7 {
    width: 130px;
  }
}

.emotion-8 {
  background-color: transparent;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: inline-block;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 400;
  height: 16px;
  letter-spacing: 0.5px;
  line-height: 1.33;
  outline: 0;
  padding: 0;
  text-align: center;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-decoration-color: #000000;
  text-underline-offset: 5px;
  -webkit-transition: text-decoration-color 200ms;
  transition: text-decoration-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.emotion-8[aria-disabled=true] {
  color: #999999;
  pointer-events: none;
  text-decoration-color: #CCCCCC;
}

.emotion-8:hover:not([aria-disabled=true]) {
  text-decoration-color: #000000;
}

.emotion-9 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: transparent;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 350;
  gap: 8px;
  height: 16px;
  letter-spacing: 0.5px;
  line-height: 1.33;
  outline: 0;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.emotion-9 svg {
  fill: #000000;
  -webkit-transition: width 400ms;
  transition: width 400ms;
}

.emotion-9 svg .arrow-line {
  -webkit-transition: -webkit-transform 400ms;
  transition: transform 400ms;
}

.emotion-9:hover:not([aria-disabled=true]) svg {
  width: 102px;
}

.emotion-9:hover:not([aria-disabled=true]) svg .arrow-line {
  -webkit-transform: translateX(-23px) scaleX(1.28);
  -moz-transform: translateX(-23px) scaleX(1.28);
  -ms-transform: translateX(-23px) scaleX(1.28);
  transform: translateX(-23px) scaleX(1.28);
}

.emotion-9[aria-disabled=true] {
  color: #999999;
  pointer-events: none;
}

.emotion-9[aria-disabled=true] svg {
  fill: #999999;
}

.emotion-10 {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 3;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
  pointer-events: none;
}

.emotion-10 [class^=spotlight-details-container] {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-11 {
  font-size: 8px;
  line-height: 1.5;
  padding: 0 16px 12px 16px;
  position: relative;
  z-index: 1;
}

.emotion-12 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 0;
  display: inline;
}

.emotion-13 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.8;
  letter-spacing: 0.5px;
  font-weight: 350;
  text-transform: none;
  font-weight: 350;
  min-height: auto;
  color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  font-size: 8px;
  line-height: 1.5;
  letter-spacing: 0.1px;
  padding: 0;
  padding-left: 0.5em;
  text-underline-offset: 4px;
  vertical-align: unset;
  pointer-events: auto;
}

.emotion-14 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  position: relative;
  border: none;
  background-color: transparent;
  color: #000000;
  font-weight: 350;
  text-transform: none;
  z-index: 1;
  color: #000000;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 8px;
  line-height: 1.5;
  letter-spacing: 0.1px;
  padding: 0;
  padding-left: 0.5em;
  text-underline-offset: 4px;
  vertical-align: unset;
  pointer-events: auto;
}

.emotion-14:focus {
  outline: none;
}

.emotion-14::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0.8em;
  right: 0.8em;
  background-color: transparent;
  display: block;
  height: 2px;
}

.emotion-14 span span {
  padding-left: initial;
}

.emotion-14:focus-visible {
  outline: auto;
}

.emotion-15 {
  aspect-ratio: 375/550;
}

.emotion-16 {
  position: relative;
}

.emotion-17 {
  box-sizing: border-box;
  height: 100%;
  left: 0;
  outline-offset: unset;
  position: absolute;
  top: 0;
  width: 100%;
}

.emotion-18 {
  aspect-ratio: 375/550;
  display: grid;
  grid-template-areas: "stacked";
}

.emotion-18>* {
  grid-area: stacked;
  overflow: hidden;
}

.emotion-19 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-20 {
  width: 100%;
  aspect-ratio: 375/550;
  object-fit: cover;
}

.emotion-21 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: 100%;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  width: 100%;
}

.emotion-24 {
  display: grid;
  pointer-events: all;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 16px;
  max-width: 100%;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: auto;
  justify-items: center;
  grid-template-columns: repeat(2, 6fr);
}

.emotion-24>* {
  text-align: center;
}

.emotion-24>*:nth-child(3) {
  grid-column: 1/-1;
}

.emotion-31 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.8;
  letter-spacing: 0.5px;
  font-weight: 350;
  text-transform: none;
  font-weight: 350;
  min-height: auto;
  color: #FFFFFF;
  -webkit-text-decoration: none;
  text-decoration: none;
  font-size: 8px;
  line-height: 1.5;
  letter-spacing: 0.1px;
  padding: 0;
  padding-left: 0.5em;
  text-underline-offset: 4px;
  vertical-align: unset;
  pointer-events: auto;
}

.emotion-32 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  position: relative;
  border: none;
  background-color: transparent;
  color: #000000;
  font-weight: 350;
  text-transform: none;
  z-index: 1;
  color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 8px;
  line-height: 1.5;
  letter-spacing: 0.1px;
  padding: 0;
  padding-left: 0.5em;
  text-underline-offset: 4px;
  vertical-align: unset;
  pointer-events: auto;
}

.emotion-32:focus {
  outline: none;
}

.emotion-32::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0.8em;
  right: 0.8em;
  background-color: transparent;
  display: block;
  height: 2px;
}

.emotion-32 span span {
  padding-left: initial;
}

.emotion-32:focus-visible {
  outline: auto;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="spotlight-carousel"
    >
      <div
        class="emotion-1"
        data-testid="upper-lower-content-area"
      >
        <div
          class="emotion-2"
        >
          <div
            class="mainContentContainer emotion-3"
            data-testid="full-bleed-content-container"
          >
            <div
              class="emotion-4"
            >
              <div>
                <p
                  class="amp-cms--p"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Upper mobile block
                  </span>
                </p>
              </div>
            </div>
            <div
              class="emotion-5"
              data-testid="cta-buttons-container"
            >
              <a
                class="emotion-6"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                OUTLINE CTA
              </a>
              <a
                class="emotion-7"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                SOLID CTA
              </a>
              <a
                class="emotion-8"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                UNDERLINE CTA
              </a>
              <a
                class="emotion-9"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                ARROW CTA
                <svg
                  height="6"
                  preserveAspectRatio="xMaxYMin"
                  viewBox="0 0 79 6"
                  width="79"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g>
                    <path
                      class="arrow-head"
                      d="M73.5 0L78.5 3L73.5 6L75.6429 3L73.5 0Z"
                    />
                    <rect
                      class="arrow-line"
                      height="1"
                      width="77"
                      y="2.5"
                    />
                  </g>
                </svg>
              </a>
            </div>
          </div>
        </div>
        <div
          class="emotion-10"
        >
          <div
            class="spotlight-details-container emotion-11"
          >
            <div
              class="emotion-12"
            >
              <span
                class="emotion-13"
              >
                Exclusions apply
              </span>
            </div>
            <button
              class="emotion-14"
            >
              Details in upper content
            </button>
          </div>
        </div>
      </div>
      <div
        class="emotion-15"
      >
        <div
          class="emotion-16"
        >
          <a
            aria-label="image link"
            class="emotion-17"
            href="www.imagelink.com"
            target="_self"
          />
          <div
            data-testid="carousel-frame"
            style="background-color: rgb(255, 255, 255);"
          >
            <section
              class="emotion-18"
            >
              <div
                class="emotion-19"
                data-testid="product-card-image"
              >
                <img
                  alt="mobile spotlight single image alt text"
                  class="emotion-20"
                  src="/img.jpg"
                />
              </div>
              <div
                class="emotion-21"
                data-testid="spotlight-content-full-wrapper"
              >
                <div
                  class="mainContentContainer emotion-3"
                  data-testid="full-bleed-content-container"
                >
                  <div
                    class="emotion-4"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-1"
                        >
                          Mobile Main overlay text
                        </span>
                      </p>
                    </div>
                  </div>
                  <div
                    class="emotion-24"
                    data-testid="cta-buttons-container"
                  >
                    <a
                      class="emotion-7"
                      data-testid="composable-btn-br"
                      href="wwww.something.com"
                      role="button"
                    >
                      MAIN CTA 1
                    </a>
                    <a
                      class="emotion-7"
                      data-testid="composable-btn-br"
                      href="www.somethingelse.com"
                      role="button"
                    >
                      MAIN CTA 2
                    </a>
                    <a
                      class="emotion-7"
                      data-testid="composable-btn-br"
                      href="wwww.something.com"
                      role="button"
                    >
                      MAIN CTA 3
                    </a>
                  </div>
                </div>
              </div>
            </section>
          </div>
          <div
            class="emotion-10"
          >
            <div
              class="spotlight-details-container emotion-11"
            >
              <div
                class="emotion-12"
              >
                <span
                  class="emotion-31"
                >
                  Limited time. Excludes Clearance, Leather and Suede Apparel & Accessories and Gift Cards
                </span>
              </div>
              <button
                class="emotion-32"
              >
                Details
              </button>
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-1"
        data-testid="upper-lower-content-area"
      >
        <div
          class="emotion-2"
        >
          <div
            class="mainContentContainer emotion-3"
            data-testid="full-bleed-content-container"
          >
            <div
              class="emotion-4"
            >
              <div>
                <p
                  class="amp-cms--p"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    mobile lower text
                  </span>
                </p>
              </div>
            </div>
            <div
              class="emotion-5"
              data-testid="cta-buttons-container"
            >
              <a
                class="emotion-6"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                OUTLINE CTA
              </a>
              <a
                class="emotion-7"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                SOLID CTA
              </a>
              <a
                class="emotion-8"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                UNDERLINE CTA
              </a>
              <a
                class="emotion-9"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                ARROW CTA
                <svg
                  height="6"
                  preserveAspectRatio="xMaxYMin"
                  viewBox="0 0 79 6"
                  width="79"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g>
                    <path
                      class="arrow-head"
                      d="M73.5 0L78.5 3L73.5 6L75.6429 3L73.5 0Z"
                    />
                    <rect
                      class="arrow-line"
                      height="1"
                      width="77"
                      y="2.5"
                    />
                  </g>
                </svg>
              </a>
            </div>
          </div>
        </div>
        <div
          class="emotion-10"
        >
          <div
            class="spotlight-details-container emotion-11"
          >
            <div
              class="emotion-12"
            >
              <span
                class="emotion-13"
              >
                Exclusions apply
              </span>
            </div>
            <button
              class="emotion-14"
            >
              Details in lower content
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`SpotlightCarousel for Banana Republic Spotlight Video should match the snapshot for desktop video 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: auto;
  box-sizing: border-box;
}

.emotion-1 {
  background-color: #E1DCCD;
  padding: 16px 24px;
  position: relative;
}

.emotion-1 .mainContentContainer {
  gap: 20px;
  padding: 0;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding-bottom: 24px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-3 {
  pointer-events: none;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 48px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  max-width: 100%;
  padding: 80px;
  position: relative;
  text-align: center;
  z-index: 3;
}

.emotion-4 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-4 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-4 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-4 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-4 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.625vw);
  line-height: 1.5;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-4 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-4 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-4 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.625vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.625vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.625vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.625vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.0416666666666665vw);
  line-height: 1.5;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-4 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, 0.9375vw);
  line-height: 1.4444444444444444;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-4 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 0.8333333333333334vw);
  line-height: 1.375;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-4 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.7291666666666666vw);
  line-height: 1.4285714285714286;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-4 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.625vw);
  line-height: 1.5;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-4 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.0416666666666665vw);
  line-height: 1;
  letter-spacing: 0.078125vw;
  font-weight: 600;
}

.emotion-4 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1;
  letter-spacing: 0.078125vw;
  font-weight: 600;
}

.emotion-4 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.625vw);
  line-height: 1;
  letter-spacing: 0.078125vw;
  font-weight: 600;
}

.emotion-4 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(48px, 5vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(40px, 4.166666666666666vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, 3.3333333333333335vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(28px, 2.5vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 1.6666666666666667vw);
  line-height: 1.125;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 1.25vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.0416666666666665vw);
  line-height: 1.4;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, 3.3333333333333335vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 2.5vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.6666666666666667vw);
  line-height: 1.0625;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(60px, 6.25vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, 0.5208333333333333vw);
  line-height: 1.6;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-4 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 4.166666666666666vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-4 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 0.9375vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-4 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, 1.25vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-4 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.0416666666666665vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  pointer-events: all;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 32px;
  max-width: 100%;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-6 {
  background-color: transparent;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: inline-block;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 400;
  height: 16px;
  letter-spacing: 0.5px;
  line-height: 1.33;
  outline: 0;
  padding: 0;
  text-align: center;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-decoration-color: #000000;
  text-underline-offset: 5px;
  -webkit-transition: text-decoration-color 200ms;
  transition: text-decoration-color 200ms;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.emotion-6[aria-disabled=true] {
  color: #999999;
  pointer-events: none;
  text-decoration-color: #CCCCCC;
}

.emotion-6:hover:not([aria-disabled=true]) {
  text-decoration-color: #000000;
}

.emotion-10 {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 3;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  text-align: center;
  pointer-events: none;
}

.emotion-10 [class^=spotlight-details-container] {
  -webkit-align-items: baseline;
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  align-items: baseline;
}

.emotion-11 {
  font-size: 12px;
  line-height: 1.33;
  padding: 0 16px 12px 16px;
  position: relative;
  z-index: 1;
}

.emotion-12 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 0;
  display: inline;
}

.emotion-13 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
  text-transform: none;
  font-weight: 350;
  min-height: auto;
  color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  font-size: 12px;
  line-height: 1.33;
  letter-spacing: 0.5px;
  padding: 0;
  padding-left: 0.5em;
  text-underline-offset: 4px;
  vertical-align: unset;
  pointer-events: auto;
}

.emotion-14 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  position: relative;
  border: none;
  background-color: transparent;
  color: #000000;
  font-weight: 350;
  text-transform: none;
  z-index: 1;
  color: #000000;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  line-height: 1.33;
  letter-spacing: 0.5px;
  padding: 0;
  padding-left: 0.5em;
  text-underline-offset: 4px;
  vertical-align: unset;
  pointer-events: auto;
}

.emotion-14:focus {
  outline: none;
}

.emotion-14::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0.8em;
  right: 0.8em;
  background-color: transparent;
  display: block;
  height: 2px;
}

.emotion-14 span span {
  padding-left: initial;
}

.emotion-14:focus-visible {
  outline: auto;
}

.emotion-15 {
  aspect-ratio: 1920/800;
}

.emotion-16 {
  position: relative;
}

.emotion-17 {
  box-sizing: border-box;
  height: 100%;
  left: 0;
  outline-offset: unset;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 2;
}

.emotion-18 {
  display: grid;
  position: relative;
  aspect-ratio: 1920/800;
  width: 100%;
  max-height: 800px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  grid-template-areas: "stacked";
}

.emotion-18>* {
  grid-area: stacked;
  overflow: hidden;
}

.emotion-19 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: 100%;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: end;
  justify-content: end;
  width: 100%;
}

.emotion-22 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  pointer-events: all;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 32px;
  max-width: 100%;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-23 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #FFFFFF;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 600;
  height: 56px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 280px;
}

.emotion-23[aria-disabled=true] {
  color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

.emotion-27 {
  width: 100%;
  height: 100%;
}

.emotion-28 {
  position: relative;
  height: 100%;
  pointer-events: auto;
  aspect-ratio: 1920/800;
}

.emotion-32 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 350;
  text-transform: none;
  font-weight: 350;
  min-height: auto;
  color: #FFFFFF;
  -webkit-text-decoration: none;
  text-decoration: none;
  font-size: 12px;
  line-height: 1.33;
  letter-spacing: 0.5px;
  padding: 0;
  padding-left: 0.5em;
  text-underline-offset: 4px;
  vertical-align: unset;
  pointer-events: auto;
}

.emotion-33 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: center;
  padding: 0.5em 0.8em;
  font-size: 1rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  position: relative;
  border: none;
  background-color: transparent;
  color: #000000;
  font-weight: 350;
  text-transform: none;
  z-index: 1;
  color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 12px;
  line-height: 1.33;
  letter-spacing: 0.5px;
  padding: 0;
  padding-left: 0.5em;
  text-underline-offset: 4px;
  vertical-align: unset;
  pointer-events: auto;
}

.emotion-33:focus {
  outline: none;
}

.emotion-33::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0.8em;
  right: 0.8em;
  background-color: transparent;
  display: block;
  height: 2px;
}

.emotion-33 span span {
  padding-left: initial;
}

.emotion-33:focus-visible {
  outline: auto;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="spotlight-carousel"
    >
      <div
        class="emotion-1"
        data-testid="upper-lower-content-area"
      >
        <div
          class="emotion-2"
        >
          <div
            class="mainContentContainer emotion-3"
            data-testid="full-bleed-content-container"
          >
            <div
              class="emotion-4"
            >
              <div>
                <p
                  class="amp-cms--p"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Upper content text
                  </span>
                </p>
              </div>
            </div>
            <div
              class="emotion-5"
              data-testid="cta-buttons-container"
            >
              <a
                class="emotion-6"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                UPPER CONTENT 1
              </a>
              <a
                class="emotion-6"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                UPPER CONTENT 2
              </a>
              <a
                class="emotion-6"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                UPPER CONTENT 3
              </a>
              <a
                class="emotion-6"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                UPPER CONTENT 4
              </a>
            </div>
          </div>
        </div>
        <div
          class="emotion-10"
        >
          <div
            class="spotlight-details-container emotion-11"
          >
            <div
              class="emotion-12"
            >
              <span
                class="emotion-13"
              >
                Exclusions apply
              </span>
            </div>
            <button
              class="emotion-14"
            >
              Details in upper content
            </button>
          </div>
        </div>
      </div>
      <div
        class="emotion-15"
      >
        <div
          class="emotion-16"
        >
          <a
            aria-label="image link"
            class="emotion-17"
            href="www.videolink.com"
            target="_self"
          />
          <div
            data-testid="carousel-frame"
            style="background-color: rgb(255, 255, 255);"
          >
            <section
              class="emotion-18"
              data-testid="sc-video-container"
            >
              <div
                class="emotion-19"
                data-testid="spotlight-content-full-wrapper"
              >
                <div
                  class="mainContentContainer emotion-3"
                  data-testid="full-bleed-content-container"
                >
                  <div
                    class="emotion-4"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--headline-1"
                        >
                          Main video text
                        </span>
                      </p>
                    </div>
                  </div>
                  <div
                    class="emotion-22"
                    data-testid="cta-buttons-container"
                  >
                    <a
                      class="emotion-23"
                      data-testid="composable-btn-br"
                      href="wwww.something.com"
                      role="button"
                    >
                      CTA 1
                    </a>
                    <a
                      class="emotion-23"
                      data-testid="composable-btn-br"
                      href="www.somethingelse.com"
                      role="button"
                    >
                      CTA 2
                    </a>
                    <a
                      class="emotion-23"
                      data-testid="composable-btn-br"
                      href="www.somethingse.com"
                      role="button"
                    >
                      CTA 3
                    </a>
                    <a
                      class="emotion-23"
                      data-testid="composable-btn-br"
                      href="www.somethingel.com"
                      role="button"
                    >
                      CTA 4
                    </a>
                  </div>
                </div>
              </div>
              <div
                class="emotion-27"
              >
                <div
                  class="emotion-28"
                >
                  <div
                    data-testid="videocomponent-container"
                    style="height: 100%; width: 100%; position: relative;"
                  >
                    <div
                      style="height: 100%; position: relative; z-index: 0;"
                    >
                      <img
                        alt="alt"
                        decoding="sync"
                        fetchpriority="high"
                        loading="eager"
                        src="/img.jpg"
                        style="height: 100%; left: 0px; object-fit: cover; position: absolute; top: 0px; width: 100%; z-index: 1; opacity: 1; transition: opacity 0.3s ease-in-out; pointer-events: none;"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </section>
          </div>
          <div
            class="emotion-10"
          >
            <div
              class="spotlight-details-container emotion-11"
            >
              <div
                class="emotion-12"
              >
                <span
                  class="emotion-32"
                >
                  *
                </span>
              </div>
              <button
                class="emotion-33"
              >
                Main content area details
              </button>
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-1"
        data-testid="upper-lower-content-area"
      >
        <div
          class="emotion-2"
        >
          <div
            class="mainContentContainer emotion-3"
            data-testid="full-bleed-content-container"
          >
            <div
              class="emotion-4"
            >
              <div>
                <p
                  class="amp-cms--p"
                >
                  <span
                    class="amp-cms--headline-2"
                  >
                    Lower content text
                  </span>
                </p>
              </div>
            </div>
            <div
              class="emotion-5"
              data-testid="cta-buttons-container"
            >
              <a
                class="emotion-6"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                LOWER CONTENT 1
              </a>
              <a
                class="emotion-6"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                LOWER CONTENT 2
              </a>
              <a
                class="emotion-6"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                LOWER CONTENT 3
              </a>
              <a
                class="emotion-6"
                data-testid="composable-btn-br"
                href="www.something.com"
                role="button"
              >
                LOWER CONTENT 4
              </a>
            </div>
          </div>
        </div>
        <div
          class="emotion-10"
        >
          <div
            class="spotlight-details-container emotion-11"
          >
            <div
              class="emotion-12"
            >
              <span
                class="emotion-13"
              >
                Exclusions apply
              </span>
            </div>
            <button
              class="emotion-14"
            >
              Details in lower content
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`SpotlightCarousel for Banana Republic carousel should match the snapshot for autoplay desktop 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: auto;
  box-sizing: border-box;
}

.emotion-1 {
  aspect-ratio: 1920/800;
}

.emotion-2 {
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-2 .slick-list {
  overflow: hidden;
}

.emotion-2 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-2 button.slick-next.slick-arrow.slick-next,
.emotion-2 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: undefined;
}

.emotion-2 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-2 .slick-disabled {
  display: none!important;
}

.emotion-2 .slick-next {
  left: calc(100% - 44px);
}

.emotion-2 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-2 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-3 {
  position: relative;
}

.emotion-3 .slick-slide {
  pointer-events: none;
}

.emotion-3 .slick-active {
  pointer-events: auto;
}

.emotion-3 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-3 .slick-slider .slick-track,
.emotion-3 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-3 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-3 .slick-list:focus {
  outline: none;
}

.emotion-3 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-3 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-3 .slick-track:before,
.emotion-3 .slick-track:after {
  display: table;
  content: "";
}

.emotion-3 .slick-track:after {
  clear: both;
}

.emotion-3 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-3 .slick-slide img {
  display: block;
}

.emotion-3 .slick-slide.slick-loading img {
  display: none;
}

.emotion-3 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-3 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-3 .slick-initialized .slick-slide,
.emotion-3 .slick-vertical .slick-slide {
  display: block;
}

.emotion-3 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-3 .slick-loading .slick-track,
.emotion-3 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-3 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-3 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-3 .slick-prev,
.emotion-3 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-3 .slick-prev:hover,
.emotion-3 .slick-next:hover,
.emotion-3 .slick-prev:focus,
.emotion-3 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-3 .slick-prev.slick-disabled,
.emotion-3 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-3 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-3 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-3 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-3 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-3 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-3 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-3 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-3 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-3 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-3 .slick-dots li button:hover,
.emotion-3 .slick-dots li button:focus {
  outline: none;
}

.emotion-3 .slick-dots li button:hover:before,
.emotion-3 .slick-dots li button:focus:before,
.emotion-3 .slick-dots li button:hover:before,
.emotion-3 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-3 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 .slick-dots li {
  margin: 0!important;
}

.emotion-3 .slick-dots li button {
  padding-left: 0!important;
  padding-right: 0!important;
}

.emotion-4 {
  position: absolute;
  border: none;
  padding: 0;
  z-index: 1;
  background: transparent;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin: 0 40px 12px 0;
  bottom: 0;
  right: 0;
}

.emotion-4>span {
  display: initial;
}

.emotion-5 {
  width: 32px;
  height: 32px;
}

.emotion-6 {
  display: inline-block;
  height: 32px;
  width: 32px;
  min-height: 32px;
  min-width: 32px;
}

.emotion-6 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-7 {
  position: relative;
}

.emotion-8 {
  box-sizing: border-box;
  height: 100%;
  left: 0;
  outline-offset: unset;
  position: absolute;
  top: 0;
  width: 100%;
}

.emotion-9 {
  aspect-ratio: 1920/800;
  display: grid;
  grid-template-areas: "stacked";
}

.emotion-9>* {
  grid-area: stacked;
  overflow: hidden;
}

.emotion-10 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-11 {
  width: 100%;
  aspect-ratio: 1920/800;
  object-fit: cover;
}

.emotion-12 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: 100%;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  width: 100%;
}

.emotion-13 {
  pointer-events: none;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 48px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  max-width: 100%;
  padding: 80px;
  position: relative;
  text-align: center;
  z-index: 3;
}

.emotion-14 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-14 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-14 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-14 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-14 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.625vw);
  line-height: 1.5;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-14 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-14 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-14 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.625vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.625vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.625vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.625vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.0416666666666665vw);
  line-height: 1.5;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-14 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, 0.9375vw);
  line-height: 1.4444444444444444;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-14 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 0.8333333333333334vw);
  line-height: 1.375;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-14 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.7291666666666666vw);
  line-height: 1.4285714285714286;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-14 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.625vw);
  line-height: 1.5;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-14 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.0416666666666665vw);
  line-height: 1;
  letter-spacing: 0.078125vw;
  font-weight: 600;
}

.emotion-14 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1;
  letter-spacing: 0.078125vw;
  font-weight: 600;
}

.emotion-14 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.625vw);
  line-height: 1;
  letter-spacing: 0.078125vw;
  font-weight: 600;
}

.emotion-14 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(48px, 5vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(40px, 4.166666666666666vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, 3.3333333333333335vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(28px, 2.5vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 1.6666666666666667vw);
  line-height: 1.125;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 1.25vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.0416666666666665vw);
  line-height: 1.4;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-14 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, 3.3333333333333335vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 2.5vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.6666666666666667vw);
  line-height: 1.0625;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(60px, 6.25vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-14 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-14 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, 0.5208333333333333vw);
  line-height: 1.6;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-14 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 4.166666666666666vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-14 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-14 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 0.9375vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 300;
}

.emotion-14 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, 1.25vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-14 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.0416666666666665vw);
  line-height: 1;
  letter-spacing: 0.026041666666666668vw;
  font-weight: 350;
}

.emotion-15 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  pointer-events: all;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 32px;
  max-width: 100%;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-16 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #FFFFFF;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 12px;
  font-weight: 600;
  height: 56px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 280px;
}

.emotion-16[aria-disabled=true] {
  color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="spotlight-carousel"
    >
      <div
        class="emotion-1"
      >
        <nav
          class="emotion-2"
          origin="SpotlightCarousel"
        >
          <div
            class="emotion-3"
          >
            <button
              aria-label="pause"
              class="emotion-4"
            >
              <div
                class="emotion-5"
              >
                <span
                  aria-hidden="true"
                  class="emotion-6"
                >
                  <svg
                    height="48"
                    viewBox="0 0 48 48"
                    width="48"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M48 24c0 13.255-10.745 24-24 24S0 37.255 0 24 10.745 0 24 0s24 10.745 24 24z"
                      fill="#F2F2F2"
                    />
                    <path
                      d="M15 15a3 3 0 116 0v18a3 3 0 11-6 0V15zM27 15a3 3 0 116 0v18a3 3 0 11-6 0V15z"
                    />
                  </svg>
                </span>
              </div>
            </button>
            <div
              class="slick-slider slick-initialized"
              dir="ltr"
            >
              <div
                class="slick-list"
              >
                <div
                  class="slick-track"
                  style="opacity: 1;"
                >
                  <div
                    aria-hidden="false"
                    class="slick-slide slick-active slick-current"
                    data-index="0"
                    style="outline: none; width: 0px; position: relative; left: 0px; opacity: 1; transition: opacity 500ms ease, visibility 500ms ease;"
                    tabindex="-1"
                  >
                    <div>
                      <div
                        class="emotion-7"
                        style="width: 100%; display: inline-block;"
                        tabindex="-1"
                      >
                        <a
                          class="emotion-8"
                          href="/browse/women/classics?cid=1196034"
                          target="_self"
                        />
                        <div
                          data-testid="carousel-frame"
                        >
                          <section
                            class="emotion-9"
                          >
                            <div
                              class="emotion-10"
                              data-testid="product-card-image"
                            >
                              <img
                                alt="SpotlightCarousel Autoplay Frame 1 Image Alt Text"
                                class="emotion-11"
                                src="/img.jpg"
                              />
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="spotlight-content-full-wrapper"
                            >
                              <div
                                class="mainContentContainer emotion-13"
                                data-testid="full-bleed-content-container"
                              >
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                    >
                                      <span
                                        class="amp-cms--headline-2"
                                        style="color:#FFFFFF"
                                      >
                                        The New Classics
                                      </span>
                                    </p>
                                    <p
                                      class="amp-cms--p"
                                    >
                                      <span
                                        class="amp-cms--subhead-3"
                                        style="color:#FFFFFF"
                                      >
                                        desktop carousel
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-15"
                                  data-testid="cta-buttons-container"
                                >
                                  <a
                                    class="emotion-16"
                                    data-testid="composable-btn-br"
                                    href="/browse/women/new-arrivals?cid=48422"
                                    role="button"
                                  >
                                    WOMEN’S NEW ARRIVALS
                                  </a>
                                  <a
                                    class="emotion-16"
                                    data-testid="composable-btn-br"
                                    href="/browse/men/new-arrivals?cid=13846"
                                    role="button"
                                  >
                                    MEN’S NEW ARRIVALS
                                  </a>
                                </div>
                              </div>
                            </div>
                          </section>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    aria-hidden="true"
                    class="slick-slide"
                    data-index="1"
                    style="outline: none; width: 0px; position: relative; left: 0px; opacity: 0; transition: opacity 500ms ease, visibility 500ms ease;"
                    tabindex="-1"
                  >
                    <div>
                      <div
                        class="emotion-7"
                        style="width: 100%; display: inline-block;"
                        tabindex="-1"
                      >
                        <a
                          class="emotion-8"
                          href="/browse/men/new-arrivals?cid=13846"
                          target="_self"
                        />
                        <div
                          data-testid="carousel-frame"
                        >
                          <section
                            class="emotion-9"
                          >
                            <div
                              class="emotion-10"
                              data-testid="product-card-image"
                            >
                              <img
                                alt="SpotlightCarousel Autoplay Frame 2 Image Alt Text"
                                class="emotion-11"
                                src="[object Object]"
                              />
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="spotlight-content-full-wrapper"
                            >
                              <div
                                class="mainContentContainer emotion-13"
                                data-testid="full-bleed-content-container"
                              >
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                    >
                                      <span
                                        class="amp-cms--headline-2"
                                        style="color:#FFFFFF"
                                      >
                                        The New Classics
                                      </span>
                                    </p>
                                    <p
                                      class="amp-cms--p"
                                    >
                                      <span
                                        class="amp-cms--subhead-3"
                                        style="color:#FFFFFF"
                                      >
                                        desktop carousel
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-15"
                                  data-testid="cta-buttons-container"
                                >
                                  <a
                                    class="emotion-16"
                                    data-testid="composable-btn-br"
                                    href="/browse/men/new-arrivals?cid=13846"
                                    role="button"
                                  >
                                    WOMEN'S NEW ARRIVALS
                                  </a>
                                  <a
                                    class="emotion-16"
                                    data-testid="composable-btn-br"
                                    href="/browse/men/new-arrivals?cid=13846"
                                    role="button"
                                  >
                                    MEN'S NEW ARRIVALS
                                  </a>
                                </div>
                              </div>
                            </div>
                          </section>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    aria-hidden="true"
                    class="slick-slide"
                    data-index="2"
                    style="outline: none; width: 0px; position: relative; left: 0px; opacity: 0; transition: opacity 500ms ease, visibility 500ms ease;"
                    tabindex="-1"
                  >
                    <div>
                      <div
                        class="emotion-7"
                        style="width: 100%; display: inline-block;"
                        tabindex="-1"
                      >
                        <a
                          class="emotion-8"
                          href="/browse/men/new-arrivals?cid=13846"
                          target="_self"
                        />
                        <div
                          data-testid="carousel-frame"
                        >
                          <section
                            class="emotion-9"
                          >
                            <div
                              class="emotion-10"
                              data-testid="product-card-image"
                            >
                              <img
                                alt="SpotlightCarousel Autoplay Frame 3 Image Alt Text"
                                class="emotion-11"
                                src="/img.jpg"
                              />
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="spotlight-content-full-wrapper"
                            >
                              <div
                                class="mainContentContainer emotion-13"
                                data-testid="full-bleed-content-container"
                              >
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                    >
                                      <span
                                        class="amp-cms--headline-2"
                                        style="color:#FFFFFF"
                                      >
                                        The New Classics
                                      </span>
                                    </p>
                                    <p
                                      class="amp-cms--p"
                                    >
                                      <span
                                        class="amp-cms--subhead-3"
                                        style="color:#FFFFFF"
                                      >
                                        desktop carousel
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-15"
                                  data-testid="cta-buttons-container"
                                >
                                  <a
                                    class="emotion-16"
                                    data-testid="composable-btn-br"
                                    href="/browse/men/new-arrivals?cid=13846"
                                    role="button"
                                  >
                                    WOMENS'S NEW ARRIVALS
                                  </a>
                                  <a
                                    class="emotion-16"
                                    data-testid="composable-btn-br"
                                    href="/browse/men/new-arrivals?cid=13846"
                                    role="button"
                                  >
                                    MEN'S NEW ARRIVALS
                                  </a>
                                </div>
                              </div>
                            </div>
                          </section>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </nav>
      </div>
    </div>
  </div>
</div>
`;

exports[`SpotlightCarousel for Banana Republic carousel should match the snapshot for autoplay mobile 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: auto;
  box-sizing: border-box;
}

.emotion-1 {
  aspect-ratio: 375/550;
}

.emotion-2 {
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-2 .slick-list {
  overflow: hidden;
}

.emotion-2 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-2 button.slick-next.slick-arrow.slick-next,
.emotion-2 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: undefined;
}

.emotion-2 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-2 .slick-disabled {
  display: none!important;
}

.emotion-2 .slick-next {
  left: calc(100% - 44px);
}

.emotion-2 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-2 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-3 {
  position: relative;
}

.emotion-3 .slick-slide {
  pointer-events: none;
}

.emotion-3 .slick-active {
  pointer-events: auto;
}

.emotion-3 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-3 .slick-slider .slick-track,
.emotion-3 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-3 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-3 .slick-list:focus {
  outline: none;
}

.emotion-3 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-3 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-3 .slick-track:before,
.emotion-3 .slick-track:after {
  display: table;
  content: "";
}

.emotion-3 .slick-track:after {
  clear: both;
}

.emotion-3 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-3 .slick-slide img {
  display: block;
}

.emotion-3 .slick-slide.slick-loading img {
  display: none;
}

.emotion-3 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-3 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-3 .slick-initialized .slick-slide,
.emotion-3 .slick-vertical .slick-slide {
  display: block;
}

.emotion-3 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-3 .slick-loading .slick-track,
.emotion-3 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-3 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-3 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-3 .slick-prev,
.emotion-3 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-3 .slick-prev:hover,
.emotion-3 .slick-next:hover,
.emotion-3 .slick-prev:focus,
.emotion-3 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-3 .slick-prev.slick-disabled,
.emotion-3 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-3 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-3 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-3 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-3 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-3 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-3 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-3 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-3 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-3 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-3 .slick-dots li button:hover,
.emotion-3 .slick-dots li button:focus {
  outline: none;
}

.emotion-3 .slick-dots li button:hover:before,
.emotion-3 .slick-dots li button:focus:before,
.emotion-3 .slick-dots li button:hover:before,
.emotion-3 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-3 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 .slick-dots li {
  margin: 0!important;
}

.emotion-3 .slick-dots li button {
  padding-left: 0!important;
  padding-right: 0!important;
}

.emotion-4 {
  position: absolute;
  border: none;
  padding: 0;
  z-index: 1;
  background: transparent;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  margin: 0 16px 12px 0;
  bottom: 0;
  right: 0;
}

.emotion-4>span {
  display: initial;
}

.emotion-5 {
  width: 24px;
  height: 24px;
}

.emotion-6 {
  display: inline-block;
  height: 24px;
  width: 24px;
  min-height: 24px;
  min-width: 24px;
}

.emotion-6 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-7 {
  position: relative;
}

.emotion-8 {
  box-sizing: border-box;
  height: 100%;
  left: 0;
  outline-offset: unset;
  position: absolute;
  top: 0;
  width: 100%;
}

.emotion-9 {
  aspect-ratio: 375/550;
  display: grid;
  grid-template-areas: "stacked";
}

.emotion-9>* {
  grid-area: stacked;
  overflow: hidden;
}

.emotion-10 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-11 {
  width: 100%;
  aspect-ratio: 375/550;
  object-fit: cover;
}

.emotion-12 {
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: 100%;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  width: 100%;
}

.emotion-13 {
  pointer-events: none;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 24px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  max-width: 100%;
  padding: 24px 16px 64px;
  position: relative;
  text-align: center;
  z-index: 3;
}

.emotion-14 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-14 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-14 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-14 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-14 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, 2.666666666666667vw);
  line-height: 1.8;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-14 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-14 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-14 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-14 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.2vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-14 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 4.266666666666667vw);
  line-height: 1.625;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-14 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.733333333333334vw);
  line-height: 1.5714285714285714;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-14 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.2vw);
  line-height: 1.6666666666666667;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-14 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, 2.666666666666667vw);
  line-height: 1.8;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-14 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.733333333333334vw);
  line-height: 1;
  letter-spacing: 0.4vw;
  font-weight: 600;
}

.emotion-14 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, 3.2vw);
  line-height: 1;
  letter-spacing: 0.4vw;
  font-weight: 600;
}

.emotion-14 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1;
  letter-spacing: 0.4vw;
  font-weight: 600;
}

.emotion-14 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 12.8vw);
  line-height: 1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 8.533333333333333vw);
  line-height: 1.0625;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 7.466666666666668vw);
  line-height: 1.0714285714285714;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 6.4vw);
  line-height: 1.0833333333333333;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 4.8vw);
  line-height: 1.1111111111111112;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-14 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.733333333333334vw);
  line-height: 1.1428571428571428;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 8.533333333333333vw);
  line-height: 1.0625;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 6.4vw);
  line-height: 1.0833333333333333;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 5.333333333333334vw);
  line-height: 1.1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 16vw);
  line-height: 1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-14 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-14 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-14 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, 2.1333333333333333vw);
  line-height: 1.5;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 400;
}

.emotion-14 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-14 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-14 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.733333333333334vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 300;
}

.emotion-14 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 4.266666666666667vw);
  line-height: 1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-14 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 3.2vw);
  line-height: 1;
  letter-spacing: 0.13333333333333333vw;
  font-weight: 350;
}

.emotion-15 {
  display: grid;
  pointer-events: all;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 16px;
  max-width: 100%;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: auto;
  justify-items: center;
  grid-template-columns: repeat(2, 6fr);
}

.emotion-15>* {
  text-align: center;
}

.emotion-16 {
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background-color: #FFFFFF;
  border: 0;
  box-sizing: border-box;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  font-size: 9px;
  font-weight: 600;
  height: 32px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  letter-spacing: 1px;
  line-height: 1.33;
  outline: 0;
  overflow: hidden;
  padding: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  white-space: nowrap;
  width: 244px;
  font-size: 10px;
  width: 160px;
}

.emotion-16[aria-disabled=true] {
  color: rgba(0, 0, 0, 0.25);
  pointer-events: none;
}

@media (max-width: 320px) {
  .emotion-16 {
    width: 130px;
  }
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="spotlight-carousel"
    >
      <div
        class="emotion-1"
      >
        <nav
          class="emotion-2"
          origin="SpotlightCarousel"
        >
          <div
            class="emotion-3"
          >
            <button
              aria-label="pause"
              class="emotion-4"
            >
              <div
                class="emotion-5"
              >
                <span
                  aria-hidden="true"
                  class="emotion-6"
                >
                  <svg
                    height="32"
                    viewBox="0 0 32 32"
                    width="32"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M32 16c0 8.837-7.163 16-16 16S0 24.837 0 16 7.163 0 16 0s16 7.163 16 16z"
                      fill="#F2F2F2"
                    />
                    <path
                      d="M10 10a2 2 0 114 0v12a2 2 0 11-4 0V10zM18 10a2 2 0 114 0v12a2 2 0 11-4 0V10z"
                    />
                  </svg>
                </span>
              </div>
            </button>
            <div
              class="slick-slider slick-initialized"
              dir="ltr"
            >
              <div
                class="slick-list"
              >
                <div
                  class="slick-track"
                  style="opacity: 1;"
                >
                  <div
                    aria-hidden="false"
                    class="slick-slide slick-active slick-current"
                    data-index="0"
                    style="outline: none; width: 0px; position: relative; left: 0px; opacity: 1; transition: opacity 500ms ease, visibility 500ms ease;"
                    tabindex="-1"
                  >
                    <div>
                      <div
                        class="emotion-7"
                        style="width: 100%; display: inline-block;"
                        tabindex="-1"
                      >
                        <a
                          class="emotion-8"
                          href="/browse/women/classics?cid=1196034"
                          target="_self"
                        />
                        <div
                          data-testid="carousel-frame"
                        >
                          <section
                            class="emotion-9"
                          >
                            <div
                              class="emotion-10"
                              data-testid="product-card-image"
                            >
                              <img
                                alt="SpotlightCarousel Autoplay Frame 1 Image Alt Text"
                                class="emotion-11"
                                src="/img.jpg"
                              />
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="spotlight-content-full-wrapper"
                            >
                              <div
                                class="mainContentContainer emotion-13"
                                data-testid="full-bleed-content-container"
                              >
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                    >
                                      <span
                                        class="amp-cms--headline-2"
                                        style="color:#FFFFFF"
                                      >
                                        The New Classics
                                      </span>
                                    </p>
                                    <p
                                      class="amp-cms--p"
                                    >
                                      <span
                                        class="amp-cms--subhead-3"
                                        style="color:#FFFFFF"
                                      >
                                        desktop carousel
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-15"
                                  data-testid="cta-buttons-container"
                                >
                                  <a
                                    class="emotion-16"
                                    data-testid="composable-btn-br"
                                    href="/browse/women/new-arrivals?cid=48422"
                                    role="button"
                                  >
                                    WOMEN’S NEW ARRIVALS
                                  </a>
                                  <a
                                    class="emotion-16"
                                    data-testid="composable-btn-br"
                                    href="/browse/men/new-arrivals?cid=13846"
                                    role="button"
                                  >
                                    MEN’S NEW ARRIVALS
                                  </a>
                                </div>
                              </div>
                            </div>
                          </section>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    aria-hidden="true"
                    class="slick-slide"
                    data-index="1"
                    style="outline: none; width: 0px; position: relative; left: 0px; opacity: 0; transition: opacity 500ms ease, visibility 500ms ease;"
                    tabindex="-1"
                  >
                    <div>
                      <div
                        class="emotion-7"
                        style="width: 100%; display: inline-block;"
                        tabindex="-1"
                      >
                        <a
                          class="emotion-8"
                          href="/browse/men/new-arrivals?cid=13846"
                          target="_self"
                        />
                        <div
                          data-testid="carousel-frame"
                        >
                          <section
                            class="emotion-9"
                          >
                            <div
                              class="emotion-10"
                              data-testid="product-card-image"
                            >
                              <img
                                alt="SpotlightCarousel Autoplay Frame 2 Image Alt Text"
                                class="emotion-11"
                                src="[object Object]"
                              />
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="spotlight-content-full-wrapper"
                            >
                              <div
                                class="mainContentContainer emotion-13"
                                data-testid="full-bleed-content-container"
                              >
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                    >
                                      <span
                                        class="amp-cms--headline-2"
                                        style="color:#FFFFFF"
                                      >
                                        The New Classics
                                      </span>
                                    </p>
                                    <p
                                      class="amp-cms--p"
                                    >
                                      <span
                                        class="amp-cms--subhead-3"
                                        style="color:#FFFFFF"
                                      >
                                        desktop carousel
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-15"
                                  data-testid="cta-buttons-container"
                                >
                                  <a
                                    class="emotion-16"
                                    data-testid="composable-btn-br"
                                    href="/browse/men/new-arrivals?cid=13846"
                                    role="button"
                                  >
                                    WOMEN'S NEW ARRIVALS
                                  </a>
                                  <a
                                    class="emotion-16"
                                    data-testid="composable-btn-br"
                                    href="/browse/men/new-arrivals?cid=13846"
                                    role="button"
                                  >
                                    MEN'S NEW ARRIVALS
                                  </a>
                                </div>
                              </div>
                            </div>
                          </section>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    aria-hidden="true"
                    class="slick-slide"
                    data-index="2"
                    style="outline: none; width: 0px; position: relative; left: 0px; opacity: 0; transition: opacity 500ms ease, visibility 500ms ease;"
                    tabindex="-1"
                  >
                    <div>
                      <div
                        class="emotion-7"
                        style="width: 100%; display: inline-block;"
                        tabindex="-1"
                      >
                        <a
                          class="emotion-8"
                          href="/browse/men/new-arrivals?cid=13846"
                          target="_self"
                        />
                        <div
                          data-testid="carousel-frame"
                        >
                          <section
                            class="emotion-9"
                          >
                            <div
                              class="emotion-10"
                              data-testid="product-card-image"
                            >
                              <img
                                alt="SpotlightCarousel Autoplay Frame 3 Image Alt Text"
                                class="emotion-11"
                                src="/img.jpg"
                              />
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="spotlight-content-full-wrapper"
                            >
                              <div
                                class="mainContentContainer emotion-13"
                                data-testid="full-bleed-content-container"
                              >
                                <div
                                  class="emotion-14"
                                >
                                  <div>
                                    <p
                                      class="amp-cms--p"
                                    >
                                      <span
                                        class="amp-cms--headline-2"
                                        style="color:#FFFFFF"
                                      >
                                        The New Classics
                                      </span>
                                    </p>
                                    <p
                                      class="amp-cms--p"
                                    >
                                      <span
                                        class="amp-cms--subhead-3"
                                        style="color:#FFFFFF"
                                      >
                                        desktop carousel
                                      </span>
                                    </p>
                                  </div>
                                </div>
                                <div
                                  class="emotion-15"
                                  data-testid="cta-buttons-container"
                                >
                                  <a
                                    class="emotion-16"
                                    data-testid="composable-btn-br"
                                    href="/browse/men/new-arrivals?cid=13846"
                                    role="button"
                                  >
                                    WOMENS'S NEW ARRIVALS
                                  </a>
                                  <a
                                    class="emotion-16"
                                    data-testid="composable-btn-br"
                                    href="/browse/men/new-arrivals?cid=13846"
                                    role="button"
                                  >
                                    MEN'S NEW ARRIVALS
                                  </a>
                                </div>
                              </div>
                            </div>
                          </section>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </nav>
      </div>
    </div>
  </div>
</div>
`;
