// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`MediaComponentWrapper should match snapshot for xsmall mobile banner size 1`] = `
.emotion-0 {
  -webkit-background-size: cover;
  background-size: cover;
  aspect-ratio: 390/100;
  width: 100%;
  height: 100%;
}

.emotion-0>img {
  height: 100%;
}

.emotion-1 {
  width: 100%;
  object-fit: cover;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="product-card-image"
    >
      <img
        alt="People"
        class="emotion-1"
        src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/ADU_21_FA3_GOL_OF__ARCH_J_D_B__2274_WB?fmt=webp"
      />
    </div>
  </div>
</div>
`;

exports[`MediaComponentWrapper should match snapshot for xxsmall desktop banner size 1`] = `
.emotion-0 {
  -webkit-background-size: cover;
  background-size: cover;
  width: 100%;
  height: 100%;
}

.emotion-1 {
  width: 100%;
  aspect-ratio: 1440/150;
  object-fit: cover;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="product-card-image"
    >
      <img
        alt="People"
        class="emotion-1"
        src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/ADU_21_FA3_GOL_OF__ARCH_J_D_B__2274_WB?fmt=webp"
      />
    </div>
  </div>
</div>
`;
