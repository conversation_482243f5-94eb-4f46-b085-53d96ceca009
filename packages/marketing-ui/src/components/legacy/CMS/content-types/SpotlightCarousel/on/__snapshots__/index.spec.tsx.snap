// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SpotlightCarousel for Old Navy should match snapshots for desktop with autoplay carousel and primary controlsIconsColor data: large 1`] = `
.emotion-0 {
  max-width: 1440px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: auto;
}

.emotion-1 {
  aspect-ratio: 1440/800;
  width: 100%;
}

.emotion-2 {
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-2 .slick-list {
  overflow: hidden;
}

.emotion-2 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-2 button.slick-next.slick-arrow.slick-next,
.emotion-2 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: undefined;
  height: 44px;
  width: 44px;
  background: #ffffff;
  opacity: 75%;
}

.emotion-2 button.slick-next.slick-arrow.slick-next>span,
.emotion-2 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-2 button.slick-next.slick-arrow.slick-next svg,
.emotion-2 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-2 button.slick-next.slick-arrow.slick-next span svg path,
.emotion-2 button.slick-prev.slick-arrow.slick-prev span svg path {
  fill: #003764;
}

.emotion-2 button.slick-next.slick-arrow.slick-next:hover,
.emotion-2 button.slick-prev.slick-arrow.slick-prev:hover {
  opacity: 100%;
}

.emotion-2 button.slick-next.slick-arrow.slick-next:focus,
.emotion-2 button.slick-prev.slick-arrow.slick-prev:focus {
  background: #ffffff;
}

.emotion-2 button.slick-next.slick-arrow.slick-next:focus-visible,
.emotion-2 button.slick-prev.slick-arrow.slick-prev:focus-visible {
  outline: auto;
}

.emotion-2 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-2 .slick-disabled {
  display: none!important;
}

.emotion-2 .slick-next {
  left: calc(100% - 44px);
}

.emotion-2 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-2 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-2 .on-spotlight-carousel .slick-list {
  overflow-x: clip;
  overflow-y: visible;
}

.emotion-2 .on-spotlight-carousel .slick-list .slick-dots {
  overflow-x: clip;
  overflow-y: visible;
}

.emotion-2 .slick-list:has(button[aria-expanded='false'])~.slick-dots {
  opacity: 1;
  -webkit-transition: opacity 0.2s ease-out 0.5s;
  transition: opacity 0.2s ease-out 0.5s;
}

.emotion-2 .slick-list:has(button[aria-expanded='true'])~.slick-dots {
  opacity: 0;
  -webkit-transition: opacity 0.1s ease-in;
  transition: opacity 0.1s ease-in;
}

.emotion-2 .slick-list:has(button[aria-expanded='false']) {
  position: relative;
  z-index: 0;
  -webkit-transition: z-index 1.2s;
  transition: z-index 1.2s;
}

.emotion-2 .slick-list:has(button[aria-expanded='true']) {
  position: relative;
  z-index: 3;
  -webkit-transition: z-index 0.1s;
  transition: z-index 0.1s;
}

.emotion-2 .slick-slider.slick-initialized>.slick-arrow.slick-prev,
.emotion-2 .slick-slider.slick-initialized>.slick-arrow.slick-next {
  z-index: 4;
}

.emotion-2 .slick-slider ul.slick-dots {
  bottom: 8px;
  width: unset;
  height: 44px;
  margin-left: 50%;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
}

.emotion-2 .slick-slider .slick-dots li.slick-active button:before {
  background-color: #FFFFFF;
}

.emotion-2 .slick-slider ul.slick-dots button:before {
  width: 10px;
  height: 10px;
  opacity: 1;
  border: 1px solid #FFFFFF;
  background-color: transparent;
}

.emotion-3 {
  position: relative;
  width: 100%;
}

.emotion-3 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-3 .slick-slider .slick-track,
.emotion-3 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-3 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-3 .slick-list:focus {
  outline: none;
}

.emotion-3 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-3 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-3 .slick-track:before,
.emotion-3 .slick-track:after {
  display: table;
  content: "";
}

.emotion-3 .slick-track:after {
  clear: both;
}

.emotion-3 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-3 .slick-slide img {
  display: block;
}

.emotion-3 .slick-slide.slick-loading img {
  display: none;
}

.emotion-3 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-3 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-3 .slick-initialized .slick-slide,
.emotion-3 .slick-vertical .slick-slide {
  display: block;
}

.emotion-3 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-3 .slick-loading .slick-track,
.emotion-3 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-3 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-3 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-3 .slick-prev,
.emotion-3 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-3 .slick-prev:hover,
.emotion-3 .slick-next:hover,
.emotion-3 .slick-prev:focus,
.emotion-3 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-3 .slick-prev.slick-disabled,
.emotion-3 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-3 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-3 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-3 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-3 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-3 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-3 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-3 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-3 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-3 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-3 .slick-dots li button:hover,
.emotion-3 .slick-dots li button:focus {
  outline: none;
}

.emotion-3 .slick-dots li button:hover:before,
.emotion-3 .slick-dots li button:focus:before,
.emotion-3 .slick-dots li button:hover:before,
.emotion-3 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-3 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 button.slick-prev {
  margin: 0px 12px;
}

.emotion-3 button.slick-next {
  margin: 0px -12px;
}

.emotion-4 {
  position: absolute;
  border: none;
  padding: 0;
  z-index: 1;
  background: transparent;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  padding: 8px 12px;
  bottom: 0;
  right: 0;
}

.emotion-4>span {
  display: initial;
}

.emotion-5 {
  width: 44px;
  height: 44px;
}

.emotion-6 {
  display: inline-block;
  height: 44px;
  width: 44px;
  min-height: 44px;
  min-width: 44px;
}

.emotion-6 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-7 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-7 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-8 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-9 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: min(24px, 1.6666666666666667vw);
}

.emotion-10 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: min(24px, 1.6666666666666667vw);
}

.emotion-11 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 80.83333333333333%;
  padding: min(32px, 2.2222222222222223vw) min(147px, 10.208333333333334vw);
  gap: min(24px, 1.6666666666666667vw);
  pointer-events: none;
}

.emotion-12 {
  text-align: center;
}

.emotion-12 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-12 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-12 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-12 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-12 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-12 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-12 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-12 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.72px);
  font-weight: 500;
}

.emotion-12 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.1111111111111112vw, 16px));
  line-height: 1.5;
  letter-spacing: min(0.044444444444444446vw, 0.64px);
  font-weight: 500;
}

.emotion-12 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9722222222222222vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.03888888888888889vw, 0.56px);
}

.emotion-12 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.8333333333333334vw, 12px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.9444444444444444vw, 28px));
  line-height: 1;
  letter-spacing: min(0.07777777777777778vw, 1.12px);
  font-weight: 500;
}

.emotion-12 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.597222222222222vw, 23px));
  line-height: 1;
  letter-spacing: min(0.0638888888888889vw, 0.92px);
  font-weight: 500;
}

.emotion-12 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1805555555555556vw, 17px));
  line-height: 1;
  letter-spacing: min(0.04722222222222222vw, 0.68px);
  font-weight: 500;
}

.emotion-12 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8.194444444444445vw, 118px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-12 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-12 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 72px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-12 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-12 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.3333333333333335vw, 48px));
  line-height: 1;
  letter-spacing: min(0.2vw, 2.88px);
  font-weight: 700;
}

.emotion-12 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.7777777777777777vw, 40px));
  line-height: 1.1;
  letter-spacing: min(0.16666666666666666vw, 2.4px);
  font-weight: 700;
}

.emotion-12 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.2222222222222223vw, 32px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-12 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-12 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-12 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-12 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 24px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-12 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5277777777777777vw, 22px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-12 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-13 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: auto;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 60px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-14 {
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  max-width: 264px;
}

.emotion-15 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  height: 50px;
  padding-inline: 15px;
}

.emotion-15:focus {
  outline: none;
}

.emotion-15>span {
  padding: 1px 0;
}

.emotion-15:hover,
.emotion-15:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-15:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-15>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-15>span>span {
  position: relative;
  -webkit-transform: translateX(5px);
  -moz-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

.emotion-16 {
  display: inline-block;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-17 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.emotion-17 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-17 svg path {
  fill: currentColor;
}

.emotion-17 svg rect {
  fill: currentColor;
}

.emotion-19 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0 1rem;
  position: absolute;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
}

.emotion-20 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: 1px solid #000000;
}

.emotion-20:last-child {
  border: none;
}

.emotion-21 {
  cursor: pointer;
  display: block;
  padding: 0.5rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: inherit;
  text-align: center;
  color: #000000;
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-24 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-25 {
  width: 100%;
  aspect-ratio: 1440/800;
  object-fit: cover;
}

.emotion-26 {
  height: 100%;
}

.emotion-27 {
  box-sizing: border-box;
  width: 100%;
  position: absolute;
  bottom: 0;
  pointer-events: none;
  padding: 0px 32px 24px 32px;
  display: grid;
  grid-template-columns: auto auto;
  grid-template-rows: auto auto;
  grid-template-areas: 'top-left top-right' 'bottom-left bottom-right';
  grid-row-gap: 10px;
}

.emotion-28 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  grid-area: bottom-left;
}

.emotion-29 {
  pointer-events: auto;
}

.emotion-29 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-29 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-29 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-29 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-29 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-29 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-29 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-29 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-29 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-29 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-29 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-29 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.72px);
  font-weight: 500;
}

.emotion-29 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.1111111111111112vw, 16px));
  line-height: 1.5;
  letter-spacing: min(0.044444444444444446vw, 0.64px);
  font-weight: 500;
}

.emotion-29 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9722222222222222vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.03888888888888889vw, 0.56px);
}

.emotion-29 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.8333333333333334vw, 12px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-29 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-29 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.9444444444444444vw, 28px));
  line-height: 1;
  letter-spacing: min(0.07777777777777778vw, 1.12px);
  font-weight: 500;
}

.emotion-29 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.597222222222222vw, 23px));
  line-height: 1;
  letter-spacing: min(0.0638888888888889vw, 0.92px);
  font-weight: 500;
}

.emotion-29 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1805555555555556vw, 17px));
  line-height: 1;
  letter-spacing: min(0.04722222222222222vw, 0.68px);
  font-weight: 500;
}

.emotion-29 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8.194444444444445vw, 118px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-29 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-29 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 72px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-29 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-29 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.3333333333333335vw, 48px));
  line-height: 1;
  letter-spacing: min(0.2vw, 2.88px);
  font-weight: 700;
}

.emotion-29 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.7777777777777777vw, 40px));
  line-height: 1.1;
  letter-spacing: min(0.16666666666666666vw, 2.4px);
  font-weight: 700;
}

.emotion-29 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.2222222222222223vw, 32px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-29 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-29 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-29 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-29 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-29 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-29 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-29 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-29 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-29 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-29 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-29 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 24px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-29 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5277777777777777vw, 22px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-29 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-30 {
  grid-area: bottom-left;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
}

.emotion-31 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 0;
  padding: 0;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
}

.emotion-32 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
  text-transform: none;
  font-weight: 500;
  min-height: auto;
  color: #FFFFFF;
  font-size: 12px;
  letter-spacing: 0.24px;
  line-height: normal;
  padding: 0;
}

.emotion-33 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.05px;
  min-height: auto;
  max-height: auto;
  line-height: 1.2857142857142858;
  padding: 0;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #003764;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  text-transform: capitalize;
  text-transform: capitalize;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 10px;
  line-height: normal;
  min-height: 10px;
  letter-spacing: normal;
  padding: 0;
  margin-left: 8px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  pointer-events: auto;
}

.emotion-33:focus {
  outline: none;
}

.emotion-33>span {
  padding: 1px 0;
}

.emotion-33 span span {
  padding-left: 1px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1rem * 0.7);
}

.emotion-33 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #003764;
}

.emotion-33 span span {
  padding-left: initial;
}

.emotion-33:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-33:focus-visible {
  outline: auto;
}

.emotion-34 {
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
}

.emotion-52 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  width: 100%;
  z-index: 2;
  position: relative;
  pointer-events: auto;
  height: 50px;
  padding-inline: 15px;
}

.emotion-52:focus {
  outline: none;
}

.emotion-52>span {
  padding: 1px 0;
}

.emotion-52:hover,
.emotion-52:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-52:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-52>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-52>span>span {
  position: relative;
  -webkit-transform: translateX(5px);
  -moz-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

.emotion-188 {
  background: #d3eaf2;
  width: 100%;
}

.emotion-189 {
  padding: min(24px, 1.6666666666666667vw) min(147px, 10.208333333333334vw) min(40px, 2.7777777777777777vw);
}

.emotion-190 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: min(24px, 1.6666666666666667vw);
}

.emotion-191 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  text-align: center;
  width: 100%;
}

.emotion-192 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-192 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-192 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-192 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-192 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-192 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-192 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-192 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-192 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-192 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-192 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-192 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.72px);
  font-weight: 500;
}

.emotion-192 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.1111111111111112vw, 16px));
  line-height: 1.5;
  letter-spacing: min(0.044444444444444446vw, 0.64px);
  font-weight: 500;
}

.emotion-192 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9722222222222222vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.03888888888888889vw, 0.56px);
}

.emotion-192 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.8333333333333334vw, 12px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-192 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-192 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.9444444444444444vw, 28px));
  line-height: 1;
  letter-spacing: min(0.07777777777777778vw, 1.12px);
  font-weight: 500;
}

.emotion-192 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.597222222222222vw, 23px));
  line-height: 1;
  letter-spacing: min(0.0638888888888889vw, 0.92px);
  font-weight: 500;
}

.emotion-192 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1805555555555556vw, 17px));
  line-height: 1;
  letter-spacing: min(0.04722222222222222vw, 0.68px);
  font-weight: 500;
}

.emotion-192 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8.194444444444445vw, 118px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-192 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-192 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 72px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-192 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-192 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.3333333333333335vw, 48px));
  line-height: 1;
  letter-spacing: min(0.2vw, 2.88px);
  font-weight: 700;
}

.emotion-192 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.7777777777777777vw, 40px));
  line-height: 1.1;
  letter-spacing: min(0.16666666666666666vw, 2.4px);
  font-weight: 700;
}

.emotion-192 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.2222222222222223vw, 32px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-192 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-192 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-192 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-192 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-192 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-192 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-192 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-192 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-192 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-192 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-192 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 24px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-192 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5277777777777777vw, 22px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-192 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

<div>
  <div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
        data-testid="spotlight-carousel-aspect-ratio"
      >
        <nav
          class="emotion-2"
          origin="SpotlightCarousel"
        >
          <div
            class="on-spotlight-carousel emotion-3"
          >
            <button
              aria-label="pause"
              class="emotion-4"
            >
              <div
                class="emotion-5"
              >
                <span
                  aria-hidden="true"
                  class="emotion-6"
                >
                  <svg
                    viewBox="0 0 50 50"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M0 0h50v50H0z"
                      fill="#FFFFFF"
                      opacity=".7"
                    />
                    <path
                      d="M20 19h4v13h-4zM27 19h4v13h-4z"
                      fill="#003764"
                    />
                  </svg>
                </span>
              </div>
            </button>
            <div
              class="slick-slider slick-initialized"
              dir="ltr"
            >
              <button
                aria-label="Previous"
                class="slick-prev slick-arrow slick-prev"
                data-role="none"
              >
                <span
                  aria-hidden="true"
                  class="emotion-7"
                >
                  <svg
                    viewBox="0 0 13.29 8.07"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                      fill="#FFFFFF"
                    />
                  </svg>
                </span>
              </button>
              <div
                class="slick-list"
              >
                <div
                  class="slick-track"
                  style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                >
                  <div
                    aria-hidden="true"
                    class="slick-slide slick-cloned"
                    data-index="-1"
                    style="width: 0px;"
                    tabindex="-1"
                  >
                    <div>
                      <div
                        class="emotion-8"
                        height="0"
                        width="0"
                      >
                        <div
                          class="emotion-9"
                        >
                          <div
                            class="emotion-10"
                          >
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Frame 1
                                    </span>
                                  </p>
                                </div>
                              </div>
                              <div
                                class="emotion-13"
                              >
                                <div
                                  class="emotion-14"
                                  data-testid="ctaDropdownWrapper"
                                >
                                  <button
                                    aria-expanded="false"
                                    class="emotion-15"
                                    color="dark"
                                  >
                                    <span
                                      class="emotion-16"
                                      data-id="cta-dropdown-label"
                                    >
                                      Dropdown
                                      <span
                                        aria-hidden="true"
                                        class="emotion-17"
                                      >
                                        <svg
                                          viewBox="0 0 10.5 10.5"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <path
                                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                                            fill="#003764"
                                          />
                                        </svg>
                                      </span>
                                    </span>
                                  </button>
                                  <div
                                    class="emotion-18"
                                  >
                                    <ul
                                      aria-hidden="true"
                                      class="emotion-19"
                                    >
                                      <li
                                        class="emotion-20"
                                      >
                                        <a
                                          breakpoint="desktop"
                                          class="emotion-21"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                      <li
                                        class="emotion-20"
                                      >
                                        <a
                                          breakpoint="desktop"
                                          class="emotion-21"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                    </ul>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-24"
                          data-testid="product-card-image"
                        >
                          <img
                            alt="img"
                            class="emotion-25"
                            src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/250604_21-M5883_NewArrivals_HighOctane_M_Site_VIBanner_SM_SL2?fmt=webp"
                          />
                        </div>
                        <div
                          class="emotion-26"
                        >
                          <div
                            class="emotion-27"
                          >
                            <div
                              class="emotion-28"
                            >
                              <div
                                class="emotion-29"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      image overlay RTE text 2
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-30"
                            >
                              <div
                                class="emotion-31"
                              >
                                <span
                                  class="emotion-32"
                                >
                                  prefix
                                </span>
                              </div>
                              <button
                                class="emotion-33"
                              >
                                details
                              </button>
                            </div>
                          </div>
                        </div>
                        <a
                          aria-label="frame one link"
                          class="emotion-34"
                          data-testid="conditional-link"
                          href="#link"
                          target="_self"
                        />
                      </div>
                    </div>
                  </div>
                  <div
                    aria-hidden="false"
                    class="slick-slide slick-active slick-current"
                    data-index="0"
                    style="outline: none; width: 0px;"
                    tabindex="-1"
                  >
                    <div>
                      <div
                        class="emotion-8"
                        height="0"
                        width="0"
                      >
                        <div
                          class="emotion-9"
                        >
                          <div
                            class="emotion-10"
                          >
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Frame 1
                                    </span>
                                  </p>
                                </div>
                              </div>
                              <div
                                class="emotion-13"
                              >
                                <div
                                  class="emotion-14"
                                  data-testid="ctaDropdownWrapper"
                                >
                                  <button
                                    aria-expanded="false"
                                    class="emotion-15"
                                    color="dark"
                                  >
                                    <span
                                      class="emotion-16"
                                      data-id="cta-dropdown-label"
                                    >
                                      SHOP LATER
                                      <span
                                        aria-hidden="true"
                                        class="emotion-17"
                                      >
                                        <svg
                                          viewBox="0 0 10.5 10.5"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <path
                                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                                            fill="#003764"
                                          />
                                        </svg>
                                      </span>
                                    </span>
                                  </button>
                                  <div
                                    class="emotion-18"
                                  >
                                    <ul
                                      aria-hidden="true"
                                      class="emotion-19"
                                    >
                                      <li
                                        class="emotion-20"
                                      >
                                        <a
                                          breakpoint="desktop"
                                          class="emotion-21"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                      <li
                                        class="emotion-20"
                                      >
                                        <a
                                          breakpoint="desktop"
                                          class="emotion-21"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                    </ul>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <a
                                    class="cta-label emotion-52"
                                    color="dark"
                                    href="#"
                                    target="_self"
                                  >
                                    SHOP HERE AND CLICK HERE
                                  </a>
                                </div>
                                <div
                                  class="emotion-14"
                                  data-testid="ctaDropdownWrapper"
                                >
                                  <button
                                    aria-expanded="false"
                                    class="emotion-15"
                                    color="dark"
                                  >
                                    <span
                                      class="emotion-16"
                                      data-id="cta-dropdown-label"
                                    >
                                      Dropdown
                                      <span
                                        aria-hidden="true"
                                        class="emotion-17"
                                      >
                                        <svg
                                          viewBox="0 0 10.5 10.5"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <path
                                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                                            fill="#003764"
                                          />
                                        </svg>
                                      </span>
                                    </span>
                                  </button>
                                  <div
                                    class="emotion-18"
                                  >
                                    <ul
                                      aria-hidden="true"
                                      class="emotion-19"
                                    >
                                      <li
                                        class="emotion-20"
                                      >
                                        <a
                                          breakpoint="desktop"
                                          class="emotion-21"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                      <li
                                        class="emotion-20"
                                      >
                                        <a
                                          breakpoint="desktop"
                                          class="emotion-21"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                    </ul>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                  data-testid="ctaDropdownWrapper"
                                >
                                  <button
                                    aria-expanded="false"
                                    class="emotion-15"
                                    color="dark"
                                  >
                                    <span
                                      class="emotion-16"
                                      data-id="cta-dropdown-label"
                                    >
                                      New to Sale
                                      <span
                                        aria-hidden="true"
                                        class="emotion-17"
                                      >
                                        <svg
                                          viewBox="0 0 10.5 10.5"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <path
                                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                                            fill="#003764"
                                          />
                                        </svg>
                                      </span>
                                    </span>
                                  </button>
                                  <div
                                    class="emotion-18"
                                  >
                                    <ul
                                      aria-hidden="true"
                                      class="emotion-19"
                                    >
                                      <li
                                        class="emotion-20"
                                      >
                                        <a
                                          breakpoint="desktop"
                                          class="emotion-21"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                      <li
                                        class="emotion-20"
                                      >
                                        <a
                                          breakpoint="desktop"
                                          class="emotion-21"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                    </ul>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-24"
                          data-testid="product-card-image"
                        >
                          <img
                            alt="img"
                            class="emotion-25"
                            src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/240926_09_TYE_Card_NonCard_CatNav_Jeans_HP_SM_USCA?fmt=webp"
                          />
                        </div>
                        <div
                          class="emotion-26"
                        >
                          <div
                            class="emotion-27"
                          >
                            <div
                              class="emotion-28"
                            >
                              <div
                                class="emotion-29"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      image overlay RTE text 1
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-30"
                            >
                              <div
                                class="emotion-31"
                              >
                                <span
                                  class="emotion-32"
                                >
                                  prefix
                                </span>
                              </div>
                              <button
                                class="emotion-33"
                              >
                                details
                              </button>
                            </div>
                          </div>
                        </div>
                        <a
                          aria-label="frame one link"
                          class="emotion-34"
                          data-testid="conditional-link"
                          href="#link"
                          target="_self"
                        />
                      </div>
                    </div>
                  </div>
                  <div
                    aria-hidden="true"
                    class="slick-slide"
                    data-index="1"
                    style="outline: none; width: 0px;"
                    tabindex="-1"
                  >
                    <div>
                      <div
                        class="emotion-8"
                        height="0"
                        width="0"
                      >
                        <div
                          class="emotion-9"
                        >
                          <div
                            class="emotion-10"
                          >
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Frame 1
                                    </span>
                                  </p>
                                </div>
                              </div>
                              <div
                                class="emotion-13"
                              >
                                <div
                                  class="emotion-14"
                                  data-testid="ctaDropdownWrapper"
                                >
                                  <button
                                    aria-expanded="false"
                                    class="emotion-15"
                                    color="dark"
                                  >
                                    <span
                                      class="emotion-16"
                                      data-id="cta-dropdown-label"
                                    >
                                      Dropdown
                                      <span
                                        aria-hidden="true"
                                        class="emotion-17"
                                      >
                                        <svg
                                          viewBox="0 0 10.5 10.5"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <path
                                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                                            fill="#003764"
                                          />
                                        </svg>
                                      </span>
                                    </span>
                                  </button>
                                  <div
                                    class="emotion-18"
                                  >
                                    <ul
                                      aria-hidden="true"
                                      class="emotion-19"
                                    >
                                      <li
                                        class="emotion-20"
                                      >
                                        <a
                                          breakpoint="desktop"
                                          class="emotion-21"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                      <li
                                        class="emotion-20"
                                      >
                                        <a
                                          breakpoint="desktop"
                                          class="emotion-21"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                    </ul>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-24"
                          data-testid="product-card-image"
                        >
                          <img
                            alt="img"
                            class="emotion-25"
                            src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/250604_21-M5883_NewArrivals_HighOctane_M_Site_VIBanner_SM_SL2?fmt=webp"
                          />
                        </div>
                        <div
                          class="emotion-26"
                        >
                          <div
                            class="emotion-27"
                          >
                            <div
                              class="emotion-28"
                            >
                              <div
                                class="emotion-29"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      image overlay RTE text 2
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-30"
                            >
                              <div
                                class="emotion-31"
                              >
                                <span
                                  class="emotion-32"
                                >
                                  prefix
                                </span>
                              </div>
                              <button
                                class="emotion-33"
                              >
                                details
                              </button>
                            </div>
                          </div>
                        </div>
                        <a
                          aria-label="frame one link"
                          class="emotion-34"
                          data-testid="conditional-link"
                          href="#link"
                          target="_self"
                        />
                      </div>
                    </div>
                  </div>
                  <div
                    aria-hidden="true"
                    class="slick-slide slick-cloned"
                    data-index="2"
                    style="width: 0px;"
                    tabindex="-1"
                  >
                    <div>
                      <div
                        class="emotion-8"
                        height="0"
                        width="0"
                      >
                        <div
                          class="emotion-9"
                        >
                          <div
                            class="emotion-10"
                          >
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Frame 1
                                    </span>
                                  </p>
                                </div>
                              </div>
                              <div
                                class="emotion-13"
                              >
                                <div
                                  class="emotion-14"
                                  data-testid="ctaDropdownWrapper"
                                >
                                  <button
                                    aria-expanded="false"
                                    class="emotion-15"
                                    color="dark"
                                  >
                                    <span
                                      class="emotion-16"
                                      data-id="cta-dropdown-label"
                                    >
                                      SHOP LATER
                                      <span
                                        aria-hidden="true"
                                        class="emotion-17"
                                      >
                                        <svg
                                          viewBox="0 0 10.5 10.5"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <path
                                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                                            fill="#003764"
                                          />
                                        </svg>
                                      </span>
                                    </span>
                                  </button>
                                  <div
                                    class="emotion-18"
                                  >
                                    <ul
                                      aria-hidden="true"
                                      class="emotion-19"
                                    >
                                      <li
                                        class="emotion-20"
                                      >
                                        <a
                                          breakpoint="desktop"
                                          class="emotion-21"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                      <li
                                        class="emotion-20"
                                      >
                                        <a
                                          breakpoint="desktop"
                                          class="emotion-21"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                    </ul>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                >
                                  <a
                                    class="cta-label emotion-52"
                                    color="dark"
                                    href="#"
                                    target="_self"
                                  >
                                    SHOP HERE AND CLICK HERE
                                  </a>
                                </div>
                                <div
                                  class="emotion-14"
                                  data-testid="ctaDropdownWrapper"
                                >
                                  <button
                                    aria-expanded="false"
                                    class="emotion-15"
                                    color="dark"
                                  >
                                    <span
                                      class="emotion-16"
                                      data-id="cta-dropdown-label"
                                    >
                                      Dropdown
                                      <span
                                        aria-hidden="true"
                                        class="emotion-17"
                                      >
                                        <svg
                                          viewBox="0 0 10.5 10.5"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <path
                                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                                            fill="#003764"
                                          />
                                        </svg>
                                      </span>
                                    </span>
                                  </button>
                                  <div
                                    class="emotion-18"
                                  >
                                    <ul
                                      aria-hidden="true"
                                      class="emotion-19"
                                    >
                                      <li
                                        class="emotion-20"
                                      >
                                        <a
                                          breakpoint="desktop"
                                          class="emotion-21"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                      <li
                                        class="emotion-20"
                                      >
                                        <a
                                          breakpoint="desktop"
                                          class="emotion-21"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                    </ul>
                                  </div>
                                </div>
                                <div
                                  class="emotion-14"
                                  data-testid="ctaDropdownWrapper"
                                >
                                  <button
                                    aria-expanded="false"
                                    class="emotion-15"
                                    color="dark"
                                  >
                                    <span
                                      class="emotion-16"
                                      data-id="cta-dropdown-label"
                                    >
                                      New to Sale
                                      <span
                                        aria-hidden="true"
                                        class="emotion-17"
                                      >
                                        <svg
                                          viewBox="0 0 10.5 10.5"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <path
                                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                                            fill="#003764"
                                          />
                                        </svg>
                                      </span>
                                    </span>
                                  </button>
                                  <div
                                    class="emotion-18"
                                  >
                                    <ul
                                      aria-hidden="true"
                                      class="emotion-19"
                                    >
                                      <li
                                        class="emotion-20"
                                      >
                                        <a
                                          breakpoint="desktop"
                                          class="emotion-21"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                      <li
                                        class="emotion-20"
                                      >
                                        <a
                                          breakpoint="desktop"
                                          class="emotion-21"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                    </ul>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-24"
                          data-testid="product-card-image"
                        >
                          <img
                            alt="img"
                            class="emotion-25"
                            src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/240926_09_TYE_Card_NonCard_CatNav_Jeans_HP_SM_USCA?fmt=webp"
                          />
                        </div>
                        <div
                          class="emotion-26"
                        >
                          <div
                            class="emotion-27"
                          >
                            <div
                              class="emotion-28"
                            >
                              <div
                                class="emotion-29"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      image overlay RTE text 1
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-30"
                            >
                              <div
                                class="emotion-31"
                              >
                                <span
                                  class="emotion-32"
                                >
                                  prefix
                                </span>
                              </div>
                              <button
                                class="emotion-33"
                              >
                                details
                              </button>
                            </div>
                          </div>
                        </div>
                        <a
                          aria-label="frame one link"
                          class="emotion-34"
                          data-testid="conditional-link"
                          href="#link"
                          target="_self"
                        />
                      </div>
                    </div>
                  </div>
                  <div
                    aria-hidden="true"
                    class="slick-slide slick-cloned"
                    data-index="3"
                    style="width: 0px;"
                    tabindex="-1"
                  >
                    <div>
                      <div
                        class="emotion-8"
                        height="0"
                        width="0"
                      >
                        <div
                          class="emotion-9"
                        >
                          <div
                            class="emotion-10"
                          >
                            <div
                              class="emotion-11"
                            >
                              <div
                                class="emotion-12"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Frame 1
                                    </span>
                                  </p>
                                </div>
                              </div>
                              <div
                                class="emotion-13"
                              >
                                <div
                                  class="emotion-14"
                                  data-testid="ctaDropdownWrapper"
                                >
                                  <button
                                    aria-expanded="false"
                                    class="emotion-15"
                                    color="dark"
                                  >
                                    <span
                                      class="emotion-16"
                                      data-id="cta-dropdown-label"
                                    >
                                      Dropdown
                                      <span
                                        aria-hidden="true"
                                        class="emotion-17"
                                      >
                                        <svg
                                          viewBox="0 0 10.5 10.5"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <path
                                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                                            fill="#003764"
                                          />
                                        </svg>
                                      </span>
                                    </span>
                                  </button>
                                  <div
                                    class="emotion-18"
                                  >
                                    <ul
                                      aria-hidden="true"
                                      class="emotion-19"
                                    >
                                      <li
                                        class="emotion-20"
                                      >
                                        <a
                                          breakpoint="desktop"
                                          class="emotion-21"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                      <li
                                        class="emotion-20"
                                      >
                                        <a
                                          breakpoint="desktop"
                                          class="emotion-21"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                    </ul>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-24"
                          data-testid="product-card-image"
                        >
                          <img
                            alt="img"
                            class="emotion-25"
                            src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/250604_21-M5883_NewArrivals_HighOctane_M_Site_VIBanner_SM_SL2?fmt=webp"
                          />
                        </div>
                        <div
                          class="emotion-26"
                        >
                          <div
                            class="emotion-27"
                          >
                            <div
                              class="emotion-28"
                            >
                              <div
                                class="emotion-29"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      image overlay RTE text 2
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-30"
                            >
                              <div
                                class="emotion-31"
                              >
                                <span
                                  class="emotion-32"
                                >
                                  prefix
                                </span>
                              </div>
                              <button
                                class="emotion-33"
                              >
                                details
                              </button>
                            </div>
                          </div>
                        </div>
                        <a
                          aria-label="frame one link"
                          class="emotion-34"
                          data-testid="conditional-link"
                          href="#link"
                          target="_self"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <button
                aria-label="Next"
                class="slick-next slick-arrow slick-next"
                data-role="none"
              >
                <span
                  aria-hidden="true"
                  class="emotion-7"
                >
                  <svg
                    viewBox="0 0 13.29 8.07"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                      fill="#FFFFFF"
                    />
                  </svg>
                </span>
              </button>
              <ul
                class="slick-dots"
                style="display: block;"
              >
                <li
                  class="slick-active"
                >
                  <button>
                    1
                  </button>
                </li>
                <li
                  class=""
                >
                  <button>
                    2
                  </button>
                </li>
              </ul>
            </div>
          </div>
        </nav>
      </div>
      <div
        class="emotion-188"
        height="0"
        width="0"
      >
        <div
          class="emotion-189"
        >
          <div
            class="emotion-190"
          >
            <div
              class="emotion-191"
            >
              <div
                class="emotion-192"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Below Content
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <div
              class="emotion-191"
            >
              <div
                class="emotion-13"
              >
                <div
                  class="emotion-14"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-15"
                    color="dark"
                  >
                    <span
                      class="emotion-16"
                      data-id="cta-dropdown-label"
                    >
                      SHOP NOW
                      <span
                        aria-hidden="true"
                        class="emotion-17"
                      >
                        <svg
                          viewBox="0 0 10.5 10.5"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                            fill="#003764"
                          />
                        </svg>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-18"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-19"
                    >
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="#one"
                          target="_self"
                        >
                          Option 1
                        </a>
                      </li>
                      <li
                        class="emotion-20"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-21"
                          href="#two"
                          target="_self"
                        >
                          Option 2
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div
                  class="emotion-14"
                >
                  <a
                    class="cta-label emotion-52"
                    color="dark"
                    href="#"
                    target="_self"
                  >
                    SHOP NOW CLICK HERE
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`SpotlightCarousel for Old Navy should match snapshots for mobile with click through carousel and secondary controlsIconsColor data: small 1`] = `
.emotion-0 {
  max-width: 1440px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: auto;
}

.emotion-1 {
  aspect-ratio: 375/468;
  width: 100%;
}

.emotion-2 {
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-2 .slick-list {
  overflow: hidden;
}

.emotion-2 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-2 button.slick-next.slick-arrow.slick-next,
.emotion-2 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: undefined;
  height: 44px;
  width: 44px;
  background: #ffffff;
  opacity: 75%;
}

.emotion-2 button.slick-next.slick-arrow.slick-next>span,
.emotion-2 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-2 button.slick-next.slick-arrow.slick-next svg,
.emotion-2 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-2 button.slick-next.slick-arrow.slick-next span svg path,
.emotion-2 button.slick-prev.slick-arrow.slick-prev span svg path {
  fill: #003764;
}

.emotion-2 button.slick-next.slick-arrow.slick-next:hover,
.emotion-2 button.slick-prev.slick-arrow.slick-prev:hover {
  opacity: 100%;
}

.emotion-2 button.slick-next.slick-arrow.slick-next:focus,
.emotion-2 button.slick-prev.slick-arrow.slick-prev:focus {
  background: #ffffff;
}

.emotion-2 button.slick-next.slick-arrow.slick-next:focus-visible,
.emotion-2 button.slick-prev.slick-arrow.slick-prev:focus-visible {
  outline: auto;
}

.emotion-2 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-2 .slick-disabled {
  display: none!important;
}

.emotion-2 .slick-next {
  left: calc(100% - 44px);
}

.emotion-2 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-2 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-2 .on-spotlight-carousel .slick-list {
  overflow-x: clip;
  overflow-y: visible;
}

.emotion-2 .on-spotlight-carousel .slick-list .slick-dots {
  overflow-x: clip;
  overflow-y: visible;
}

.emotion-2 .slick-list:has(button[aria-expanded='false'])~.slick-dots {
  opacity: 1;
  -webkit-transition: opacity 0.2s ease-out 0.5s;
  transition: opacity 0.2s ease-out 0.5s;
}

.emotion-2 .slick-list:has(button[aria-expanded='true'])~.slick-dots {
  opacity: 0;
  -webkit-transition: opacity 0.1s ease-in;
  transition: opacity 0.1s ease-in;
}

.emotion-2 .slick-list:has(button[aria-expanded='false']) {
  position: relative;
  z-index: 0;
  -webkit-transition: z-index 1.2s;
  transition: z-index 1.2s;
}

.emotion-2 .slick-list:has(button[aria-expanded='true']) {
  position: relative;
  z-index: 3;
  -webkit-transition: z-index 0.1s;
  transition: z-index 0.1s;
}

.emotion-2 .slick-slider.slick-initialized>.slick-arrow.slick-prev,
.emotion-2 .slick-slider.slick-initialized>.slick-arrow.slick-next {
  z-index: 4;
}

.emotion-2 .slick-slider ul.slick-dots {
  bottom: 4px;
  width: unset;
  height: 44px;
  margin-left: 50%;
  -webkit-transform: translateX(-50%);
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
}

.emotion-2 .slick-slider .slick-dots li.slick-active button:before {
  background-color: #FFFFFF;
}

.emotion-2 .slick-slider ul.slick-dots button:before {
  width: 10px;
  height: 10px;
  opacity: 1;
  border: 1px solid #FFFFFF;
  background-color: transparent;
}

.emotion-3 {
  position: relative;
  width: 100%;
}

.emotion-3 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-3 .slick-slider .slick-track,
.emotion-3 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-3 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-3 .slick-list:focus {
  outline: none;
}

.emotion-3 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-3 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-3 .slick-track:before,
.emotion-3 .slick-track:after {
  display: table;
  content: "";
}

.emotion-3 .slick-track:after {
  clear: both;
}

.emotion-3 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-3 .slick-slide img {
  display: block;
}

.emotion-3 .slick-slide.slick-loading img {
  display: none;
}

.emotion-3 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-3 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-3 .slick-initialized .slick-slide,
.emotion-3 .slick-vertical .slick-slide {
  display: block;
}

.emotion-3 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-3 .slick-loading .slick-track,
.emotion-3 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-3 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-3 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-3 .slick-prev,
.emotion-3 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-3 .slick-prev:hover,
.emotion-3 .slick-next:hover,
.emotion-3 .slick-prev:focus,
.emotion-3 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-3 .slick-prev.slick-disabled,
.emotion-3 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-3 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-3 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-3 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-3 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-3 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-3 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-3 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-3 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-3 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-3 .slick-dots li button:hover,
.emotion-3 .slick-dots li button:focus {
  outline: none;
}

.emotion-3 .slick-dots li button:hover:before,
.emotion-3 .slick-dots li button:focus:before,
.emotion-3 .slick-dots li button:hover:before,
.emotion-3 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-3 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 button.slick-prev {
  margin: 0px 12px;
}

.emotion-3 button.slick-next {
  margin: 0px -12px;
}

.emotion-4 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-5 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-6 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  padding: 6.4vw 4.533333333333333vw;
  gap: 6.4vw;
  pointer-events: none;
}

.emotion-8 {
  text-align: center;
}

.emotion-8 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-8 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-8 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-8 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-8 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-8 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-8 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-8 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 53.76px));
  line-height: 1.5;
  letter-spacing: min(0.08960000000000001vw, 1.29024px);
  font-weight: 500;
}

.emotion-8 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 46.08px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 1.1059199999999998px);
  font-weight: 500;
}

.emotion-8 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 46.08px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 1.1059199999999998px);
}

.emotion-8 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-8 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.133333333333333vw, 88.32px));
  line-height: 1;
  letter-spacing: min(0.24533333333333335vw, 3.5328px);
  font-weight: 500;
}

.emotion-8 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.066666666666666vw, 72.96px));
  line-height: 1;
  letter-spacing: min(0.20266666666666666vw, 2.9184px);
  font-weight: 500;
}

.emotion-8 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 53.76px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.14933333333333335vw, 2.1504000000000003px);
  font-weight: 500;
}

.emotion-8 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(18.133333333333333vw, 261.12px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-8 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(13.333333333333334vw, 192px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-8 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(11.466666666666667vw, 165.12px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-8 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.6vw, 138.24px));
  line-height: 1;
  letter-spacing: min(0.19199999999999998vw, 2.7647999999999997px);
  font-weight: 700;
}

.emotion-8 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.466666666666668vw, 107.52px));
  line-height: 1;
  letter-spacing: min(0.2986666666666667vw, 4.300800000000001px);
  font-weight: 700;
}

.emotion-8 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.4vw, 92.16px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.256vw, 3.6864px);
  font-weight: 700;
}

.emotion-8 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.333333333333334vw, 76.8px));
  line-height: 1;
  letter-spacing: min(0.128vw, 1.8432px);
  font-weight: 700;
}

.emotion-8 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(13px, min(17.066666666666666vw, 245.76px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(13px, min(14.399999999999999vw, 207.35999999999999px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(13px, min(9.066666666666666vw, 130.56px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(13px, min(6.4vw, 92.16px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-8 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-8 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 230.39999999999998px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 153.6px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-8 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-8 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(5.333333333333334vw, 76.8px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-8 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.8vw, 69.12px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-8 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.266666666666667vw, 61.44px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-9 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: auto;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 12px;
  -webkit-align-items: normal;
  -webkit-box-align: normal;
  -ms-flex-align: normal;
  align-items: normal;
}

.emotion-9 * ul {
  position: absolute;
  max-height: 242px;
  overflow: scroll;
}

@supports selector (::-webkit-scrollbar) {
  .emotion-9 * ul {
    padding-left: 11px;
  }

  .emotion-9 * ul::-webkit-scrollbar {
    -webkit-appearance: none;
  }

  .emotion-9 * ul::-webkit-scrollbar:vertical {
    width: 11px;
    background-color: #FFFFFF;
  }

  .emotion-9 * ul::-webkit-scrollbar:horizontal {
    height: 0;
  }

  .emotion-9 * ul::-webkit-scrollbar-corner {
    background-color: #FFFFFF;
  }

  .emotion-9 * ul::-webkit-scrollbar-thumb {
    border-radius: 8px;
    border: 2px solid #FFF;
    background-color: rgba(0, 0, 0, .5);
  }
}

.emotion-10 {
  position: relative;
}

.emotion-11 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  height: 45px;
  padding-inline: 15px;
}

.emotion-11:focus {
  outline: none;
}

.emotion-11>span {
  padding: 1px 0;
}

.emotion-11>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-11>span>span {
  position: relative;
  -webkit-transform: translateX(5px);
  -moz-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

.emotion-12 {
  display: inline-block;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-13 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.emotion-13 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-13 svg path {
  fill: currentColor;
}

.emotion-13 svg rect {
  fill: currentColor;
}

.emotion-15 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: inherit;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
}

.emotion-16 {
  box-sizing: border-box;
  width: auto;
  border-bottom: 1px solid #000000;
}

.emotion-16:last-child {
  border: none;
}

.emotion-17 {
  cursor: pointer;
  display: block;
  padding: 0.75rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: inherit;
  text-align: center;
  color: #000000;
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-20 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-21 {
  width: 100%;
  aspect-ratio: 375/468;
  object-fit: cover;
}

.emotion-22 {
  height: 100%;
}

.emotion-23 {
  box-sizing: border-box;
  width: 100%;
  position: absolute;
  bottom: 0;
  pointer-events: none;
  padding: 0px 16px 12px 16px;
  display: grid;
  grid-template-columns: auto auto;
  grid-template-rows: auto auto;
  grid-template-areas: 'top-left top-right' 'bottom-left bottom-right';
  grid-row-gap: 10px;
}

.emotion-24 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  grid-area: bottom-left;
}

.emotion-25 {
  pointer-events: auto;
}

.emotion-25 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-25 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-25 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-25 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-25 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-25 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-25 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-25 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-25 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-25 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-25 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-25 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 53.76px));
  line-height: 1.5;
  letter-spacing: min(0.08960000000000001vw, 1.29024px);
  font-weight: 500;
}

.emotion-25 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 46.08px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 1.1059199999999998px);
  font-weight: 500;
}

.emotion-25 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 46.08px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 1.1059199999999998px);
}

.emotion-25 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-25 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-25 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.133333333333333vw, 88.32px));
  line-height: 1;
  letter-spacing: min(0.24533333333333335vw, 3.5328px);
  font-weight: 500;
}

.emotion-25 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.066666666666666vw, 72.96px));
  line-height: 1;
  letter-spacing: min(0.20266666666666666vw, 2.9184px);
  font-weight: 500;
}

.emotion-25 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 53.76px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.14933333333333335vw, 2.1504000000000003px);
  font-weight: 500;
}

.emotion-25 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(18.133333333333333vw, 261.12px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-25 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(13.333333333333334vw, 192px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-25 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(11.466666666666667vw, 165.12px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-25 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.6vw, 138.24px));
  line-height: 1;
  letter-spacing: min(0.19199999999999998vw, 2.7647999999999997px);
  font-weight: 700;
}

.emotion-25 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.466666666666668vw, 107.52px));
  line-height: 1;
  letter-spacing: min(0.2986666666666667vw, 4.300800000000001px);
  font-weight: 700;
}

.emotion-25 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.4vw, 92.16px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.256vw, 3.6864px);
  font-weight: 700;
}

.emotion-25 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.333333333333334vw, 76.8px));
  line-height: 1;
  letter-spacing: min(0.128vw, 1.8432px);
  font-weight: 700;
}

.emotion-25 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(13px, min(17.066666666666666vw, 245.76px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-25 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(13px, min(14.399999999999999vw, 207.35999999999999px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-25 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(13px, min(9.066666666666666vw, 130.56px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-25 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(13px, min(6.4vw, 92.16px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-25 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-25 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-25 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-25 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 230.39999999999998px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-25 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 153.6px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-25 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-25 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(5.333333333333334vw, 76.8px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-25 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.8vw, 69.12px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-25 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.266666666666667vw, 61.44px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-26 {
  grid-area: bottom-left;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
}

.emotion-27 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 7px 10px 8px 0;
  padding: 0;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
}

.emotion-28 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
  text-transform: none;
  font-weight: 500;
  min-height: auto;
  color: #FFFFFF;
  font-size: 11px;
  letter-spacing: 0.22px;
  line-height: normal;
  padding: 0;
}

.emotion-29 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.05px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.2857142857142858;
  padding: 0;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #003764;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  text-transform: capitalize;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 10px;
  line-height: normal;
  min-height: 10px;
  letter-spacing: normal;
  padding: 0;
  margin-left: 8px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  pointer-events: auto;
}

.emotion-29:focus {
  outline: none;
}

.emotion-29>span {
  padding: 1px 0;
}

.emotion-29 span span {
  padding-left: 1px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1rem * 0.7);
}

.emotion-29 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #003764;
}

.emotion-29 span span {
  padding-left: initial;
}

.emotion-29:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-29:focus-visible {
  outline: auto;
}

.emotion-30 {
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
}

.emotion-48 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  width: 100%;
  z-index: 2;
  position: relative;
  pointer-events: auto;
  height: 45px;
  padding-inline: 15px;
}

.emotion-48:focus {
  outline: none;
}

.emotion-48>span {
  padding: 1px 0;
}

.emotion-48:hover,
.emotion-48:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-48:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-48>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-48>span>span {
  position: relative;
  -webkit-transform: translateX(5px);
  -moz-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

.emotion-183 {
  background: #d3eaf2;
  width: 100%;
}

.emotion-184 {
  padding: 24px 8px;
}

.emotion-185 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 6.4vw;
}

.emotion-186 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  text-align: center;
  width: 100%;
}

.emotion-187 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-187 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-187 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-187 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-187 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-187 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-187 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-187 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-187 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-187 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-187 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-187 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 53.76px));
  line-height: 1.5;
  letter-spacing: min(0.08960000000000001vw, 1.29024px);
  font-weight: 500;
}

.emotion-187 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 46.08px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 1.1059199999999998px);
  font-weight: 500;
}

.emotion-187 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 46.08px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 1.1059199999999998px);
}

.emotion-187 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-187 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-187 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.133333333333333vw, 88.32px));
  line-height: 1;
  letter-spacing: min(0.24533333333333335vw, 3.5328px);
  font-weight: 500;
}

.emotion-187 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.066666666666666vw, 72.96px));
  line-height: 1;
  letter-spacing: min(0.20266666666666666vw, 2.9184px);
  font-weight: 500;
}

.emotion-187 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 53.76px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.14933333333333335vw, 2.1504000000000003px);
  font-weight: 500;
}

.emotion-187 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(18.133333333333333vw, 261.12px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-187 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(13.333333333333334vw, 192px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-187 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(11.466666666666667vw, 165.12px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-187 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.6vw, 138.24px));
  line-height: 1;
  letter-spacing: min(0.19199999999999998vw, 2.7647999999999997px);
  font-weight: 700;
}

.emotion-187 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.466666666666668vw, 107.52px));
  line-height: 1;
  letter-spacing: min(0.2986666666666667vw, 4.300800000000001px);
  font-weight: 700;
}

.emotion-187 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.4vw, 92.16px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.256vw, 3.6864px);
  font-weight: 700;
}

.emotion-187 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.333333333333334vw, 76.8px));
  line-height: 1;
  letter-spacing: min(0.128vw, 1.8432px);
  font-weight: 700;
}

.emotion-187 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(13px, min(17.066666666666666vw, 245.76px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-187 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(13px, min(14.399999999999999vw, 207.35999999999999px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-187 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(13px, min(9.066666666666666vw, 130.56px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-187 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(13px, min(6.4vw, 92.16px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-187 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-187 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-187 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-187 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 230.39999999999998px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-187 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 153.6px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-187 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-187 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(5.333333333333334vw, 76.8px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-187 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.8vw, 69.12px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-187 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.266666666666667vw, 61.44px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-189 {
  display: grid;
  grid-template-rows: auto;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  justify-items: center;
}

.emotion-189 .cta-label {
  text-align: center;
}

<div>
  <div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
        data-testid="spotlight-carousel-aspect-ratio"
      >
        <nav
          class="emotion-2"
          origin="SpotlightCarousel"
        >
          <div
            class="on-spotlight-carousel emotion-3"
          >
            <div
              class="slick-slider slick-initialized"
              dir="ltr"
            >
              <div
                class="slick-list"
              >
                <div
                  class="slick-track"
                  style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                >
                  <div
                    aria-hidden="true"
                    class="slick-slide slick-cloned"
                    data-index="-1"
                    style="width: 0px;"
                    tabindex="-1"
                  >
                    <div>
                      <div
                        class="emotion-4"
                        height="0"
                        width="0"
                      >
                        <div
                          class="emotion-5"
                        >
                          <div
                            class="emotion-6"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      image overlay override 3
                                    </span>
                                  </p>
                                </div>
                              </div>
                              <div
                                class="emotion-9"
                              >
                                <div
                                  class="emotion-10"
                                  data-testid="ctaDropdownWrapper"
                                >
                                  <button
                                    aria-expanded="false"
                                    class="emotion-11"
                                    color="dark"
                                  >
                                    <span
                                      class="emotion-12"
                                      data-id="cta-dropdown-label"
                                    >
                                      Dropdown
                                      <span
                                        aria-hidden="true"
                                        class="emotion-13"
                                      >
                                        <svg
                                          viewBox="0 0 10.5 10.5"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <path
                                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                                            fill="#003764"
                                          />
                                        </svg>
                                      </span>
                                    </span>
                                  </button>
                                  <div
                                    class="emotion-14"
                                  >
                                    <ul
                                      aria-hidden="true"
                                      class="emotion-15"
                                    >
                                      <li
                                        class="emotion-16"
                                      >
                                        <a
                                          breakpoint="mobile"
                                          class="emotion-17"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                      <li
                                        class="emotion-16"
                                      >
                                        <a
                                          breakpoint="mobile"
                                          class="emotion-17"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                    </ul>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-20"
                          data-testid="product-card-image"
                        >
                          <img
                            alt="img"
                            class="emotion-21"
                            src=""
                          />
                        </div>
                        <div
                          class="emotion-22"
                        >
                          <div
                            class="emotion-23"
                          >
                            <div
                              class="emotion-24"
                            >
                              <div
                                class="emotion-25"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      image overlay override 4
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-26"
                            >
                              <div
                                class="emotion-27"
                              >
                                <span
                                  class="emotion-28"
                                >
                                  prefix
                                </span>
                              </div>
                              <button
                                class="emotion-29"
                              >
                                details
                              </button>
                            </div>
                          </div>
                        </div>
                        <a
                          aria-label="frame one link"
                          class="emotion-30"
                          data-testid="conditional-link"
                          href="#link"
                          target="_self"
                        />
                      </div>
                    </div>
                  </div>
                  <div
                    aria-hidden="false"
                    class="slick-slide slick-active slick-current"
                    data-index="0"
                    style="outline: none; width: 0px;"
                    tabindex="-1"
                  >
                    <div>
                      <div
                        class="emotion-4"
                        height="0"
                        width="0"
                      >
                        <div
                          class="emotion-5"
                        >
                          <div
                            class="emotion-6"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      image overlay override 1
                                    </span>
                                  </p>
                                </div>
                              </div>
                              <div
                                class="emotion-9"
                              >
                                <div
                                  class="emotion-10"
                                  data-testid="ctaDropdownWrapper"
                                >
                                  <button
                                    aria-expanded="false"
                                    class="emotion-11"
                                    color="dark"
                                  >
                                    <span
                                      class="emotion-12"
                                      data-id="cta-dropdown-label"
                                    >
                                      SHOP LATER
                                      <span
                                        aria-hidden="true"
                                        class="emotion-13"
                                      >
                                        <svg
                                          viewBox="0 0 10.5 10.5"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <path
                                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                                            fill="#003764"
                                          />
                                        </svg>
                                      </span>
                                    </span>
                                  </button>
                                  <div
                                    class="emotion-14"
                                  >
                                    <ul
                                      aria-hidden="true"
                                      class="emotion-15"
                                    >
                                      <li
                                        class="emotion-16"
                                      >
                                        <a
                                          breakpoint="mobile"
                                          class="emotion-17"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                      <li
                                        class="emotion-16"
                                      >
                                        <a
                                          breakpoint="mobile"
                                          class="emotion-17"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                    </ul>
                                  </div>
                                </div>
                                <div
                                  class="emotion-10"
                                >
                                  <a
                                    class="cta-label emotion-48"
                                    color="dark"
                                    href="#"
                                    target="_self"
                                  >
                                    SHOP HERE AND CLICK HERE
                                  </a>
                                </div>
                                <div
                                  class="emotion-10"
                                  data-testid="ctaDropdownWrapper"
                                >
                                  <button
                                    aria-expanded="false"
                                    class="emotion-11"
                                    color="dark"
                                  >
                                    <span
                                      class="emotion-12"
                                      data-id="cta-dropdown-label"
                                    >
                                      Dropdown
                                      <span
                                        aria-hidden="true"
                                        class="emotion-13"
                                      >
                                        <svg
                                          viewBox="0 0 10.5 10.5"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <path
                                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                                            fill="#003764"
                                          />
                                        </svg>
                                      </span>
                                    </span>
                                  </button>
                                  <div
                                    class="emotion-14"
                                  >
                                    <ul
                                      aria-hidden="true"
                                      class="emotion-15"
                                    >
                                      <li
                                        class="emotion-16"
                                      >
                                        <a
                                          breakpoint="mobile"
                                          class="emotion-17"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                      <li
                                        class="emotion-16"
                                      >
                                        <a
                                          breakpoint="mobile"
                                          class="emotion-17"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                    </ul>
                                  </div>
                                </div>
                                <div
                                  class="emotion-10"
                                  data-testid="ctaDropdownWrapper"
                                >
                                  <button
                                    aria-expanded="false"
                                    class="emotion-11"
                                    color="dark"
                                  >
                                    <span
                                      class="emotion-12"
                                      data-id="cta-dropdown-label"
                                    >
                                      New to Sale
                                      <span
                                        aria-hidden="true"
                                        class="emotion-13"
                                      >
                                        <svg
                                          viewBox="0 0 10.5 10.5"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <path
                                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                                            fill="#003764"
                                          />
                                        </svg>
                                      </span>
                                    </span>
                                  </button>
                                  <div
                                    class="emotion-14"
                                  >
                                    <ul
                                      aria-hidden="true"
                                      class="emotion-15"
                                    >
                                      <li
                                        class="emotion-16"
                                      >
                                        <a
                                          breakpoint="mobile"
                                          class="emotion-17"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                      <li
                                        class="emotion-16"
                                      >
                                        <a
                                          breakpoint="mobile"
                                          class="emotion-17"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                    </ul>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-20"
                          data-testid="product-card-image"
                        >
                          <img
                            alt="img"
                            class="emotion-21"
                            src=""
                          />
                        </div>
                        <div
                          class="emotion-22"
                        >
                          <div
                            class="emotion-23"
                          >
                            <div
                              class="emotion-24"
                            >
                              <div
                                class="emotion-25"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      image overlay override 2
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-26"
                            >
                              <div
                                class="emotion-27"
                              >
                                <span
                                  class="emotion-28"
                                >
                                  prefix
                                </span>
                              </div>
                              <button
                                class="emotion-29"
                              >
                                details
                              </button>
                            </div>
                          </div>
                        </div>
                        <a
                          aria-label="frame one link"
                          class="emotion-30"
                          data-testid="conditional-link"
                          href="#link"
                          target="_self"
                        />
                      </div>
                    </div>
                  </div>
                  <div
                    aria-hidden="true"
                    class="slick-slide"
                    data-index="1"
                    style="outline: none; width: 0px;"
                    tabindex="-1"
                  >
                    <div>
                      <div
                        class="emotion-4"
                        height="0"
                        width="0"
                      >
                        <div
                          class="emotion-5"
                        >
                          <div
                            class="emotion-6"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      image overlay override 3
                                    </span>
                                  </p>
                                </div>
                              </div>
                              <div
                                class="emotion-9"
                              >
                                <div
                                  class="emotion-10"
                                  data-testid="ctaDropdownWrapper"
                                >
                                  <button
                                    aria-expanded="false"
                                    class="emotion-11"
                                    color="dark"
                                  >
                                    <span
                                      class="emotion-12"
                                      data-id="cta-dropdown-label"
                                    >
                                      Dropdown
                                      <span
                                        aria-hidden="true"
                                        class="emotion-13"
                                      >
                                        <svg
                                          viewBox="0 0 10.5 10.5"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <path
                                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                                            fill="#003764"
                                          />
                                        </svg>
                                      </span>
                                    </span>
                                  </button>
                                  <div
                                    class="emotion-14"
                                  >
                                    <ul
                                      aria-hidden="true"
                                      class="emotion-15"
                                    >
                                      <li
                                        class="emotion-16"
                                      >
                                        <a
                                          breakpoint="mobile"
                                          class="emotion-17"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                      <li
                                        class="emotion-16"
                                      >
                                        <a
                                          breakpoint="mobile"
                                          class="emotion-17"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                    </ul>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-20"
                          data-testid="product-card-image"
                        >
                          <img
                            alt="img"
                            class="emotion-21"
                            src=""
                          />
                        </div>
                        <div
                          class="emotion-22"
                        >
                          <div
                            class="emotion-23"
                          >
                            <div
                              class="emotion-24"
                            >
                              <div
                                class="emotion-25"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      image overlay override 4
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-26"
                            >
                              <div
                                class="emotion-27"
                              >
                                <span
                                  class="emotion-28"
                                >
                                  prefix
                                </span>
                              </div>
                              <button
                                class="emotion-29"
                              >
                                details
                              </button>
                            </div>
                          </div>
                        </div>
                        <a
                          aria-label="frame one link"
                          class="emotion-30"
                          data-testid="conditional-link"
                          href="#link"
                          target="_self"
                        />
                      </div>
                    </div>
                  </div>
                  <div
                    aria-hidden="true"
                    class="slick-slide slick-cloned"
                    data-index="2"
                    style="width: 0px;"
                    tabindex="-1"
                  >
                    <div>
                      <div
                        class="emotion-4"
                        height="0"
                        width="0"
                      >
                        <div
                          class="emotion-5"
                        >
                          <div
                            class="emotion-6"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      image overlay override 1
                                    </span>
                                  </p>
                                </div>
                              </div>
                              <div
                                class="emotion-9"
                              >
                                <div
                                  class="emotion-10"
                                  data-testid="ctaDropdownWrapper"
                                >
                                  <button
                                    aria-expanded="false"
                                    class="emotion-11"
                                    color="dark"
                                  >
                                    <span
                                      class="emotion-12"
                                      data-id="cta-dropdown-label"
                                    >
                                      SHOP LATER
                                      <span
                                        aria-hidden="true"
                                        class="emotion-13"
                                      >
                                        <svg
                                          viewBox="0 0 10.5 10.5"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <path
                                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                                            fill="#003764"
                                          />
                                        </svg>
                                      </span>
                                    </span>
                                  </button>
                                  <div
                                    class="emotion-14"
                                  >
                                    <ul
                                      aria-hidden="true"
                                      class="emotion-15"
                                    >
                                      <li
                                        class="emotion-16"
                                      >
                                        <a
                                          breakpoint="mobile"
                                          class="emotion-17"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                      <li
                                        class="emotion-16"
                                      >
                                        <a
                                          breakpoint="mobile"
                                          class="emotion-17"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                    </ul>
                                  </div>
                                </div>
                                <div
                                  class="emotion-10"
                                >
                                  <a
                                    class="cta-label emotion-48"
                                    color="dark"
                                    href="#"
                                    target="_self"
                                  >
                                    SHOP HERE AND CLICK HERE
                                  </a>
                                </div>
                                <div
                                  class="emotion-10"
                                  data-testid="ctaDropdownWrapper"
                                >
                                  <button
                                    aria-expanded="false"
                                    class="emotion-11"
                                    color="dark"
                                  >
                                    <span
                                      class="emotion-12"
                                      data-id="cta-dropdown-label"
                                    >
                                      Dropdown
                                      <span
                                        aria-hidden="true"
                                        class="emotion-13"
                                      >
                                        <svg
                                          viewBox="0 0 10.5 10.5"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <path
                                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                                            fill="#003764"
                                          />
                                        </svg>
                                      </span>
                                    </span>
                                  </button>
                                  <div
                                    class="emotion-14"
                                  >
                                    <ul
                                      aria-hidden="true"
                                      class="emotion-15"
                                    >
                                      <li
                                        class="emotion-16"
                                      >
                                        <a
                                          breakpoint="mobile"
                                          class="emotion-17"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                      <li
                                        class="emotion-16"
                                      >
                                        <a
                                          breakpoint="mobile"
                                          class="emotion-17"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                    </ul>
                                  </div>
                                </div>
                                <div
                                  class="emotion-10"
                                  data-testid="ctaDropdownWrapper"
                                >
                                  <button
                                    aria-expanded="false"
                                    class="emotion-11"
                                    color="dark"
                                  >
                                    <span
                                      class="emotion-12"
                                      data-id="cta-dropdown-label"
                                    >
                                      New to Sale
                                      <span
                                        aria-hidden="true"
                                        class="emotion-13"
                                      >
                                        <svg
                                          viewBox="0 0 10.5 10.5"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <path
                                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                                            fill="#003764"
                                          />
                                        </svg>
                                      </span>
                                    </span>
                                  </button>
                                  <div
                                    class="emotion-14"
                                  >
                                    <ul
                                      aria-hidden="true"
                                      class="emotion-15"
                                    >
                                      <li
                                        class="emotion-16"
                                      >
                                        <a
                                          breakpoint="mobile"
                                          class="emotion-17"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                      <li
                                        class="emotion-16"
                                      >
                                        <a
                                          breakpoint="mobile"
                                          class="emotion-17"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                    </ul>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-20"
                          data-testid="product-card-image"
                        >
                          <img
                            alt="img"
                            class="emotion-21"
                            src=""
                          />
                        </div>
                        <div
                          class="emotion-22"
                        >
                          <div
                            class="emotion-23"
                          >
                            <div
                              class="emotion-24"
                            >
                              <div
                                class="emotion-25"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      image overlay override 2
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-26"
                            >
                              <div
                                class="emotion-27"
                              >
                                <span
                                  class="emotion-28"
                                >
                                  prefix
                                </span>
                              </div>
                              <button
                                class="emotion-29"
                              >
                                details
                              </button>
                            </div>
                          </div>
                        </div>
                        <a
                          aria-label="frame one link"
                          class="emotion-30"
                          data-testid="conditional-link"
                          href="#link"
                          target="_self"
                        />
                      </div>
                    </div>
                  </div>
                  <div
                    aria-hidden="true"
                    class="slick-slide slick-cloned"
                    data-index="3"
                    style="width: 0px;"
                    tabindex="-1"
                  >
                    <div>
                      <div
                        class="emotion-4"
                        height="0"
                        width="0"
                      >
                        <div
                          class="emotion-5"
                        >
                          <div
                            class="emotion-6"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      image overlay override 3
                                    </span>
                                  </p>
                                </div>
                              </div>
                              <div
                                class="emotion-9"
                              >
                                <div
                                  class="emotion-10"
                                  data-testid="ctaDropdownWrapper"
                                >
                                  <button
                                    aria-expanded="false"
                                    class="emotion-11"
                                    color="dark"
                                  >
                                    <span
                                      class="emotion-12"
                                      data-id="cta-dropdown-label"
                                    >
                                      Dropdown
                                      <span
                                        aria-hidden="true"
                                        class="emotion-13"
                                      >
                                        <svg
                                          viewBox="0 0 10.5 10.5"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <path
                                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                                            fill="#003764"
                                          />
                                        </svg>
                                      </span>
                                    </span>
                                  </button>
                                  <div
                                    class="emotion-14"
                                  >
                                    <ul
                                      aria-hidden="true"
                                      class="emotion-15"
                                    >
                                      <li
                                        class="emotion-16"
                                      >
                                        <a
                                          breakpoint="mobile"
                                          class="emotion-17"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                      <li
                                        class="emotion-16"
                                      >
                                        <a
                                          breakpoint="mobile"
                                          class="emotion-17"
                                          href="#"
                                          target="_self"
                                        >
                                          Option 1
                                        </a>
                                      </li>
                                    </ul>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="emotion-20"
                          data-testid="product-card-image"
                        >
                          <img
                            alt="img"
                            class="emotion-21"
                            src=""
                          />
                        </div>
                        <div
                          class="emotion-22"
                        >
                          <div
                            class="emotion-23"
                          >
                            <div
                              class="emotion-24"
                            >
                              <div
                                class="emotion-25"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      image overlay override 4
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-26"
                            >
                              <div
                                class="emotion-27"
                              >
                                <span
                                  class="emotion-28"
                                >
                                  prefix
                                </span>
                              </div>
                              <button
                                class="emotion-29"
                              >
                                details
                              </button>
                            </div>
                          </div>
                        </div>
                        <a
                          aria-label="frame one link"
                          class="emotion-30"
                          data-testid="conditional-link"
                          href="#link"
                          target="_self"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <ul
                class="slick-dots"
                style="display: block;"
              >
                <li
                  class="slick-active"
                >
                  <button>
                    1
                  </button>
                </li>
                <li
                  class=""
                >
                  <button>
                    2
                  </button>
                </li>
              </ul>
            </div>
          </div>
        </nav>
      </div>
      <div
        class="emotion-183"
        height="0"
        width="0"
      >
        <div
          class="emotion-184"
        >
          <div
            class="emotion-185"
          >
            <div
              class="emotion-186"
            >
              <div
                class="emotion-187"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      below image text override 1
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <div
              class="emotion-186"
            >
              <div
                class="emotion-189"
                data-testid="linear-layout-wrapper"
              >
                <div
                  class="emotion-10"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-11"
                    color="dark"
                  >
                    <span
                      class="emotion-12"
                      data-id="cta-dropdown-label"
                    >
                      SHOP NOW
                      <span
                        aria-hidden="true"
                        class="emotion-13"
                      >
                        <svg
                          viewBox="0 0 10.5 10.5"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                            fill="#003764"
                          />
                        </svg>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-14"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-15"
                    >
                      <li
                        class="emotion-16"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-17"
                          href="#one"
                          target="_self"
                        >
                          Option 1
                        </a>
                      </li>
                      <li
                        class="emotion-16"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-17"
                          href="#two"
                          target="_self"
                        >
                          Option 2
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div
                  class="emotion-10"
                >
                  <a
                    class="cta-label emotion-48"
                    color="dark"
                    href="#"
                    target="_self"
                  >
                    SHOP NOW CLICK HERE
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`SpotlightCarousel for Old Navy should match snapshots when both on-cta-redesign-2024 feature flag and OnCtaRedesign2024Context are enabled: large 1`] = `
.emotion-0 {
  max-width: 1440px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: auto;
}

.emotion-1 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-2 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: min(24px, 1.6666666666666667vw);
}

.emotion-3 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: min(24px, 1.6666666666666667vw);
}

.emotion-4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 80.83333333333333%;
  padding: min(32px, 2.2222222222222223vw) min(147px, 10.208333333333334vw);
  gap: min(24px, 1.6666666666666667vw);
  pointer-events: none;
}

.emotion-5 {
  text-align: center;
}

.emotion-5 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-5 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-5 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-5 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-5 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-5 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-5 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-5 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.72px);
  font-weight: 500;
}

.emotion-5 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.1111111111111112vw, 16px));
  line-height: 1.5;
  letter-spacing: min(0.044444444444444446vw, 0.64px);
  font-weight: 500;
}

.emotion-5 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9722222222222222vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.03888888888888889vw, 0.56px);
}

.emotion-5 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.8333333333333334vw, 12px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.9444444444444444vw, 28px));
  line-height: 1;
  letter-spacing: min(0.07777777777777778vw, 1.12px);
  font-weight: 500;
}

.emotion-5 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.597222222222222vw, 23px));
  line-height: 1;
  letter-spacing: min(0.0638888888888889vw, 0.92px);
  font-weight: 500;
}

.emotion-5 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1805555555555556vw, 17px));
  line-height: 1;
  letter-spacing: min(0.04722222222222222vw, 0.68px);
  font-weight: 500;
}

.emotion-5 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8.194444444444445vw, 118px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-5 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-5 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 72px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-5 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-5 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.3333333333333335vw, 48px));
  line-height: 1;
  letter-spacing: min(0.2vw, 2.88px);
  font-weight: 700;
}

.emotion-5 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.7777777777777777vw, 40px));
  line-height: 1.1;
  letter-spacing: min(0.16666666666666666vw, 2.4px);
  font-weight: 700;
}

.emotion-5 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.2222222222222223vw, 32px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-5 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-5 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 24px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-5 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5277777777777777vw, 22px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-5 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: auto;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 60px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-7 {
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  max-width: 264px;
}

.emotion-8 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  height: 50px;
  padding-inline: 15px;
}

.emotion-8:focus {
  outline: none;
}

.emotion-8>span {
  padding: 1px 0;
}

.emotion-8:hover,
.emotion-8:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-8:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-8>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-8>span>span {
  position: relative;
  -webkit-transform: translateX(5px);
  -moz-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

.emotion-9 {
  display: inline-block;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-10 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.emotion-10 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-10 svg path {
  fill: currentColor;
}

.emotion-10 svg rect {
  fill: currentColor;
}

.emotion-12 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0 1rem;
  position: absolute;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
}

.emotion-13 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: 1px solid #000000;
}

.emotion-13:last-child {
  border: none;
}

.emotion-14 {
  cursor: pointer;
  display: block;
  padding: 0.5rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: inherit;
  text-align: center;
  color: #000000;
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-18 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  width: 100%;
  z-index: 2;
  position: relative;
  pointer-events: auto;
  height: 50px;
  padding-inline: 15px;
}

.emotion-18:focus {
  outline: none;
}

.emotion-18>span {
  padding: 1px 0;
}

.emotion-18:hover,
.emotion-18:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-18:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-18>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-18>span>span {
  position: relative;
  -webkit-transform: translateX(5px);
  -moz-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

.emotion-39 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-40 {
  width: 100%;
  aspect-ratio: 1440/800;
  object-fit: cover;
}

.emotion-41 {
  height: 100%;
}

.emotion-42 {
  box-sizing: border-box;
  width: 100%;
  position: absolute;
  bottom: 0;
  pointer-events: none;
  padding: 0px 32px 24px 32px;
  display: grid;
  grid-template-columns: auto auto;
  grid-template-rows: auto auto;
  grid-template-areas: 'top-left top-right' 'bottom-left bottom-right';
  grid-row-gap: 10px;
}

.emotion-43 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  grid-area: bottom-left;
}

.emotion-44 {
  pointer-events: auto;
}

.emotion-44 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-44 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-44 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-44 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-44 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-44 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-44 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-44 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-44 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-44 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-44 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-44 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.72px);
  font-weight: 500;
}

.emotion-44 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.1111111111111112vw, 16px));
  line-height: 1.5;
  letter-spacing: min(0.044444444444444446vw, 0.64px);
  font-weight: 500;
}

.emotion-44 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9722222222222222vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.03888888888888889vw, 0.56px);
}

.emotion-44 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.8333333333333334vw, 12px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-44 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-44 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.9444444444444444vw, 28px));
  line-height: 1;
  letter-spacing: min(0.07777777777777778vw, 1.12px);
  font-weight: 500;
}

.emotion-44 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.597222222222222vw, 23px));
  line-height: 1;
  letter-spacing: min(0.0638888888888889vw, 0.92px);
  font-weight: 500;
}

.emotion-44 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1805555555555556vw, 17px));
  line-height: 1;
  letter-spacing: min(0.04722222222222222vw, 0.68px);
  font-weight: 500;
}

.emotion-44 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8.194444444444445vw, 118px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-44 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-44 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 72px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-44 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-44 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.3333333333333335vw, 48px));
  line-height: 1;
  letter-spacing: min(0.2vw, 2.88px);
  font-weight: 700;
}

.emotion-44 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.7777777777777777vw, 40px));
  line-height: 1.1;
  letter-spacing: min(0.16666666666666666vw, 2.4px);
  font-weight: 700;
}

.emotion-44 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.2222222222222223vw, 32px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-44 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-44 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-44 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-44 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-44 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-44 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-44 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-44 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-44 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-44 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-44 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 24px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-44 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5277777777777777vw, 22px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-44 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-45 {
  grid-area: bottom-left;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
}

.emotion-46 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 0;
  padding: 0;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
}

.emotion-47 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
  text-transform: none;
  font-weight: 500;
  min-height: auto;
  color: #FFFFFF;
  font-size: 12px;
  letter-spacing: 0.24px;
  line-height: normal;
  padding: 0;
}

.emotion-48 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.05px;
  min-height: auto;
  max-height: auto;
  line-height: 1.2857142857142858;
  padding: 0;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #003764;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  text-transform: capitalize;
  text-transform: capitalize;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 10px;
  line-height: normal;
  min-height: 10px;
  letter-spacing: normal;
  padding: 0;
  margin-left: 8px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  pointer-events: auto;
}

.emotion-48:focus {
  outline: none;
}

.emotion-48>span {
  padding: 1px 0;
}

.emotion-48 span span {
  padding-left: 1px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1rem * 0.7);
}

.emotion-48 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #003764;
}

.emotion-48 span span {
  padding-left: initial;
}

.emotion-48:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-48:focus-visible {
  outline: auto;
}

.emotion-49 {
  background: #FF0000;
  width: 100%;
}

.emotion-50 {
  padding: min(24px, 1.6666666666666667vw) min(147px, 10.208333333333334vw) min(40px, 2.7777777777777777vw);
}

.emotion-51 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: min(24px, 1.6666666666666667vw);
}

.emotion-52 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  text-align: center;
  width: 100%;
}

.emotion-53 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-53 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-53 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-53 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-53 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-53 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-53 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-53 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-53 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-53 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-53 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-53 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.72px);
  font-weight: 500;
}

.emotion-53 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.1111111111111112vw, 16px));
  line-height: 1.5;
  letter-spacing: min(0.044444444444444446vw, 0.64px);
  font-weight: 500;
}

.emotion-53 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9722222222222222vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.03888888888888889vw, 0.56px);
}

.emotion-53 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.8333333333333334vw, 12px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-53 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-53 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.9444444444444444vw, 28px));
  line-height: 1;
  letter-spacing: min(0.07777777777777778vw, 1.12px);
  font-weight: 500;
}

.emotion-53 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.597222222222222vw, 23px));
  line-height: 1;
  letter-spacing: min(0.0638888888888889vw, 0.92px);
  font-weight: 500;
}

.emotion-53 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1805555555555556vw, 17px));
  line-height: 1;
  letter-spacing: min(0.04722222222222222vw, 0.68px);
  font-weight: 500;
}

.emotion-53 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8.194444444444445vw, 118px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-53 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-53 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 72px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-53 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-53 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.3333333333333335vw, 48px));
  line-height: 1;
  letter-spacing: min(0.2vw, 2.88px);
  font-weight: 700;
}

.emotion-53 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.7777777777777777vw, 40px));
  line-height: 1.1;
  letter-spacing: min(0.16666666666666666vw, 2.4px);
  font-weight: 700;
}

.emotion-53 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.2222222222222223vw, 32px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-53 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-53 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-53 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-53 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-53 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-53 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-53 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-53 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-53 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-53 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-53 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 24px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-53 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5277777777777777vw, 22px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-53 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="emotion-1"
          height="0"
          width="0"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
              >
                <div
                  class="emotion-5"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Frame 1
                      </span>
                    </p>
                  </div>
                </div>
                <div
                  class="emotion-6"
                >
                  <div
                    class="emotion-7"
                    data-testid="ctaDropdownWrapper"
                  >
                    <button
                      aria-expanded="false"
                      class="emotion-8"
                      color="dark"
                    >
                      <span
                        class="emotion-9"
                        data-id="cta-dropdown-label"
                      >
                        SHOP LATER
                        <span
                          aria-hidden="true"
                          class="emotion-10"
                        >
                          <svg
                            fill="none"
                            viewBox="0 0 12 12"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              clip-rule="evenodd"
                              d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                              fill="#000000"
                              fill-rule="evenodd"
                            />
                          </svg>
                        </span>
                      </span>
                    </button>
                    <div
                      class="emotion-11"
                    >
                      <ul
                        aria-hidden="true"
                        class="emotion-12"
                      >
                        <li
                          class="emotion-13"
                        >
                          <a
                            breakpoint="desktop"
                            class="emotion-14"
                            href="#"
                            target="_self"
                          >
                            Option 1
                          </a>
                        </li>
                        <li
                          class="emotion-13"
                        >
                          <a
                            breakpoint="desktop"
                            class="emotion-14"
                            href="#"
                            target="_self"
                          >
                            Option 1
                          </a>
                        </li>
                      </ul>
                    </div>
                  </div>
                  <div
                    class="emotion-7"
                  >
                    <a
                      class="cta-label emotion-18"
                      color="dark"
                      href="#"
                      target="_self"
                    >
                      SHOP HERE AND CLICK HERE
                    </a>
                  </div>
                  <div
                    class="emotion-7"
                    data-testid="ctaDropdownWrapper"
                  >
                    <button
                      aria-expanded="false"
                      class="emotion-8"
                      color="dark"
                    >
                      <span
                        class="emotion-9"
                        data-id="cta-dropdown-label"
                      >
                        Dropdown
                        <span
                          aria-hidden="true"
                          class="emotion-10"
                        >
                          <svg
                            fill="none"
                            viewBox="0 0 12 12"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              clip-rule="evenodd"
                              d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                              fill="#000000"
                              fill-rule="evenodd"
                            />
                          </svg>
                        </span>
                      </span>
                    </button>
                    <div
                      class="emotion-11"
                    >
                      <ul
                        aria-hidden="true"
                        class="emotion-12"
                      >
                        <li
                          class="emotion-13"
                        >
                          <a
                            breakpoint="desktop"
                            class="emotion-14"
                            href="#"
                            target="_self"
                          >
                            Option 1
                          </a>
                        </li>
                        <li
                          class="emotion-13"
                        >
                          <a
                            breakpoint="desktop"
                            class="emotion-14"
                            href="#"
                            target="_self"
                          >
                            Option 1
                          </a>
                        </li>
                      </ul>
                    </div>
                  </div>
                  <div
                    class="emotion-7"
                    data-testid="ctaDropdownWrapper"
                  >
                    <button
                      aria-expanded="false"
                      class="emotion-8"
                      color="dark"
                    >
                      <span
                        class="emotion-9"
                        data-id="cta-dropdown-label"
                      >
                        New to Sale
                        <span
                          aria-hidden="true"
                          class="emotion-10"
                        >
                          <svg
                            fill="none"
                            viewBox="0 0 12 12"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              clip-rule="evenodd"
                              d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                              fill="#000000"
                              fill-rule="evenodd"
                            />
                          </svg>
                        </span>
                      </span>
                    </button>
                    <div
                      class="emotion-11"
                    >
                      <ul
                        aria-hidden="true"
                        class="emotion-12"
                      >
                        <li
                          class="emotion-13"
                        >
                          <a
                            breakpoint="desktop"
                            class="emotion-14"
                            href="#"
                            target="_self"
                          >
                            Option 1
                          </a>
                        </li>
                        <li
                          class="emotion-13"
                        >
                          <a
                            breakpoint="desktop"
                            class="emotion-14"
                            href="#"
                            target="_self"
                          >
                            Option 1
                          </a>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            class="emotion-39"
            data-testid="product-card-image"
          >
            <img
              alt="img"
              class="emotion-40"
              src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/240926_09_TYE_Card_NonCard_CatNav_Jeans_HP_SM_USCA?fmt=webp"
            />
          </div>
          <div
            class="emotion-41"
          >
            <div
              class="emotion-42"
            >
              <div
                class="emotion-43"
              >
                <div
                  class="emotion-44"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        image overlay RTE text 1
                      </span>
                    </p>
                  </div>
                </div>
              </div>
              <div
                class="emotion-45"
              >
                <div
                  class="emotion-46"
                >
                  <span
                    class="emotion-47"
                  >
                    prefix
                  </span>
                </div>
                <button
                  class="emotion-48"
                >
                  details
                </button>
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-49"
          height="0"
          width="0"
        >
          <div
            class="emotion-50"
          >
            <div
              class="emotion-51"
            >
              <div
                class="emotion-52"
              >
                <div
                  class="emotion-53"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Below Content
                      </span>
                    </p>
                  </div>
                </div>
              </div>
              <div
                class="emotion-52"
              >
                <div
                  class="emotion-6"
                >
                  <div
                    class="emotion-7"
                    data-testid="ctaDropdownWrapper"
                  >
                    <button
                      aria-expanded="false"
                      class="emotion-8"
                      color="dark"
                    >
                      <span
                        class="emotion-9"
                        data-id="cta-dropdown-label"
                      >
                        SHOP NOW
                        <span
                          aria-hidden="true"
                          class="emotion-10"
                        >
                          <svg
                            fill="none"
                            viewBox="0 0 12 12"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              clip-rule="evenodd"
                              d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                              fill="#000000"
                              fill-rule="evenodd"
                            />
                          </svg>
                        </span>
                      </span>
                    </button>
                    <div
                      class="emotion-11"
                    >
                      <ul
                        aria-hidden="true"
                        class="emotion-12"
                      >
                        <li
                          class="emotion-13"
                        >
                          <a
                            breakpoint="desktop"
                            class="emotion-14"
                            href="#one"
                            target="_self"
                          >
                            Option 1
                          </a>
                        </li>
                        <li
                          class="emotion-13"
                        >
                          <a
                            breakpoint="desktop"
                            class="emotion-14"
                            href="#two"
                            target="_self"
                          >
                            Option 2
                          </a>
                        </li>
                      </ul>
                    </div>
                  </div>
                  <div
                    class="emotion-7"
                  >
                    <a
                      class="cta-label emotion-18"
                      color="dark"
                      href="#"
                      target="_self"
                    >
                      SHOP NOW CLICK HERE
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      ,
    </div>
  </div>
</div>
`;
