// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CommunityFeedContent should render default content 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: -webkit-linear-gradient(270deg, #C8D9C1,#71786E);
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: 80px 20px;
  position: relative;
  box-sizing: border-box;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 133px;
}

.emotion-2 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-2 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-2 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-2 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-2 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-2 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-2 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.3888888888888888vw);
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.25vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.1111111111111112vw);
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.9722222222222222vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 3.4722222222222223vw);
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.7777777777777777vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.430555555555556vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 8.333333333333332vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 7.638888888888889vw);
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.944444444444445vw);
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.944444444444445vw);
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 5.555555555555555vw);
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.861111111111112vw);
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.166666666666666vw);
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, 7.916666666666666vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, 5.833333333333333vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, 4.444444444444445vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, 2.361111111111111vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 6.944444444444445vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 5.555555555555555vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-2 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 2.083333333333333vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.7361111111111112vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.3888888888888888vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-3 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-3:focus {
  outline: none;
}

.emotion-3:hover,
.emotion-3:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-3:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-4 {
  overflow: unset;
}

.emotion-5 {
  overflow: visible;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 20px;
}

.emotion-6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  width: calc(33%);
}

.emotion-7 {
  position: absolute;
  width: 100%;
  height: 100%;
  color: transparent;
}

.emotion-8 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-9 {
  width: 100%;
  aspect-ratio: 1/1;
  object-fit: cover;
}

.emotion-10 {
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  margin-top: 20px;
  max-width: 100%;
}

.emotion-10 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-10 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-10 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-10 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-10 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-10 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-10 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-10 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.3888888888888888vw);
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.25vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.1111111111111112vw);
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.9722222222222222vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 3.4722222222222223vw);
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.7777777777777777vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.430555555555556vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 8.333333333333332vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 7.638888888888889vw);
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.944444444444445vw);
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.944444444444445vw);
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 5.555555555555555vw);
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.861111111111112vw);
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.166666666666666vw);
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, 7.916666666666666vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, 5.833333333333333vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, 4.444444444444445vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, 2.361111111111111vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 6.944444444444445vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-10 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 5.555555555555555vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-10 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-10 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 2.083333333333333vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.7361111111111112vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.3888888888888888vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-22 {
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: calc(33%);
}

.emotion-23 {
  position: absolute;
  width: 100%;
  height: 100%;
  color: transparent;
}

.emotion-24 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  gap: 20px;
  margin-top: 20px;
}

.emotion-25 {
  width: 2.430555555555556vw;
  height: 2.430555555555556vw;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-clip-path: circle();
  clip-path: circle();
}

.emotion-26 {
  width: 100%;
  object-fit: cover;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <article>
      <div
        class="emotion-0"
        height="0"
        width="0"
      >
        <div
          class="emotion-1"
        >
          <div
            class="emotion-2"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--headlineAlt-3"
                >
                  In the community
                </span>
              </p>
            </div>
          </div>
          <a
            class="emotion-3"
            color="dark"
            href="#"
          >
            Button
          </a>
        </div>
        <div
          class="emotion-4"
        >
          <div
            class="emotion-5"
          >
            <section
              class="emotion-6"
            >
              <a
                class="emotion-7"
                href="/test-url-1"
              >
                This is the card1 link
              </a>
              <div
                class="emotion-8"
                data-testid="product-card-image"
              >
                <img
                  alt="imagealto"
                  class="emotion-9"
                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/169105862_466264801286952_7673584469792986764_n?fmt=webp"
                />
              </div>
              <div
                class="emotion-10"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
                    </span>
                  </p>
                </div>
              </div>
            </section>
            <section
              class="emotion-6"
            >
              <a
                class="emotion-7"
                href="/test-url-2"
              />
              <div
                class="emotion-8"
                data-testid="product-card-image"
              >
                <img
                  alt="imagealt"
                  class="emotion-9"
                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/joggers?fmt=webp"
                />
              </div>
              <div
                class="emotion-10"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Lorem ipsum dolor sit ame
                    </span>
                  </p>
                </div>
              </div>
            </section>
            <section
              class="emotion-6"
            >
              <a
                class="emotion-7"
              />
              <div
                class="emotion-8"
                data-testid="product-card-image"
              >
                <img
                  alt="A person wearing white shirt, red skirt and white shoes."
                  class="emotion-9"
                  src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/530626_082_FLOR_AT_WMN_Tier_C_120_SP22_OE_3_1593?fmt=webp"
                />
              </div>
              <div
                class="emotion-10"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet.
                    </span>
                  </p>
                </div>
              </div>
            </section>
          </div>
          <div
            class="emotion-5"
          >
            <section
              class="emotion-22"
            >
              <a
                class="emotion-23"
              />
              <div
                class="emotion-24"
              >
                <div
                  class="emotion-25"
                  data-testid="product-card-image"
                >
                  <img
                    alt="imagealt"
                    class="emotion-26"
                    src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/CMS_HOL22_D1_ATG_Tops_S?fmt=webp"
                  />
                </div>
                <div
                  class="emotion-2"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                      style="text-align:left;"
                    >
                      <span
                        class="amp-cms--body-3"
                      >
                        Amanda Schwarzenegger, Ashtanga Yoga Instructor and popular yoga blogger
                      </span>
                    </p>
                  </div>
                </div>
              </div>
            </section>
            <section
              class="emotion-22"
            >
              <a
                class="emotion-23"
              />
              <div
                class="emotion-24"
              >
                <div
                  class="emotion-25"
                  data-testid="product-card-image"
                >
                  <img
                    alt="imagealt2"
                    class="emotion-26"
                    src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/HOL1_Winter-Essentials_S?fmt=webp"
                  />
                </div>
                <div
                  class="emotion-2"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                      style="text-align:left;"
                    >
                      <span
                        class="amp-cms--body-3"
                      >
                        Name, Title
                      </span>
                    </p>
                  </div>
                </div>
              </div>
            </section>
            <section
              class="emotion-22"
            >
              <a
                class="emotion-23"
              />
              <div
                class="emotion-24"
              >
                <div
                  class="emotion-25"
                  data-testid="product-card-image"
                >
                  <img
                    alt="imagealt3"
                    class="emotion-26"
                    src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/SPRING1_22_Yoga&Studio_ISM_XL_2x?fmt=webp"
                  />
                </div>
                <div
                  class="emotion-2"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                      style="text-align:left;"
                    >
                      <span
                        class="amp-cms--body-3"
                      >
                        Name, Title
                      </span>
                    </p>
                  </div>
                </div>
              </div>
            </section>
          </div>
        </div>
      </div>
    </article>
  </div>
</DocumentFragment>
`;
