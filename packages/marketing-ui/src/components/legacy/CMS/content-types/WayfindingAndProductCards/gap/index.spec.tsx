import { SMALL, XLARGE } from '@ecom-next/core/breakpoint-provider';
import { render, screen } from 'test-utils';
import React from 'react';
import {
  wayfindingAndProductCardsCarouselData,
  wayfindingAndProductCardsFourCardsExposedOutOfTen,
  wayfindingAndProductCardsSixExposedWithOverTenCards,
  wayfindingAndProductCardsWithSixExposed,
} from '../__fixtures__/carousel-test-data';
import { wayfindingAndProductCardsDefaultData, wayfindingAndProductCardsWithFourExposed } from '../__fixtures__/test-data';
import { wayfindingAndProductCardsCarouselUsecase2 } from '../__fixtures__/vr-wpc-carousel-usecase-2';
import { nextArrowSelector, prevArrowSelector } from '../utilities';
import WayfindingAndProductCards from './index';

describe('WayfindingAndProductCards', () => {
  describe('Desktop', () => {
    it('matches snapshot when it is basic', () => {
      const { asFragment } = render(<WayfindingAndProductCards {...wayfindingAndProductCardsDefaultData} />, {
        // @ts-ignore
        breakpoint: XLARGE,
      });
      expect(asFragment()).toMatchSnapshot();
    });

    it('matches snapshot when it is carousel', () => {
      const { asFragment } = render(<WayfindingAndProductCards {...wayfindingAndProductCardsCarouselData} />, {
        // @ts-ignore
        breakpoint: XLARGE,
      });
      expect(asFragment()).toMatchSnapshot();
    });

    it('does not show arrows on desktop', () => {
      const { container } = render(<WayfindingAndProductCards {...wayfindingAndProductCardsCarouselUsecase2} />, {
        // @ts-ignore
        breakpoint: XLARGE,
      });

      expect(container.querySelector(prevArrowSelector)).not.toBeInTheDocument();
      expect(container.querySelector(nextArrowSelector)).not.toBeInTheDocument();
    });

    it('uses basic layout when numberOfCardsToExpose is "4" and there are 4 cards', () => {
      const { container, asFragment } = render(<WayfindingAndProductCards {...wayfindingAndProductCardsWithFourExposed} />, {
        // @ts-ignore
        breakpoint: XLARGE,
      });

      expect(container.querySelector('.slick-slider')).not.toBeInTheDocument();
      expect(asFragment()).toMatchSnapshot();
    });

    it('uses carousel layout when numberOfCardsToExpose is "4" with partial 5th card and there are 5+ cards', () => {
      const { container, asFragment } = render(<WayfindingAndProductCards {...wayfindingAndProductCardsFourCardsExposedOutOfTen} />, {
        // @ts-ignore
        breakpoint: XLARGE,
      });

      expect(container.querySelector('[class$="-BackgroundTypeContainer"]')).toBeInTheDocument();
      expect(asFragment()).toMatchSnapshot();
    });

    it('uses basic layout when numberOfCardsToExpose is "6" and there are 6 cards', () => {
      const { container, asFragment } = render(<WayfindingAndProductCards {...wayfindingAndProductCardsWithSixExposed} />, {
        // @ts-ignore
        breakpoint: XLARGE,
      });

      expect(container.querySelector('.slick-slider')).not.toBeInTheDocument();
      expect(asFragment()).toMatchSnapshot();
    });

    it('uses carousel layout when numberOfCardsToExpose is "6" with partial 7th card and there are 7+ cards', () => {
      const { container, asFragment } = render(<WayfindingAndProductCards {...wayfindingAndProductCardsSixExposedWithOverTenCards} />, {
        // @ts-ignore
        breakpoint: XLARGE,
      });
      expect(container.querySelector('[class$="-BackgroundTypeContainer"]')).toBeInTheDocument();
      expect(asFragment()).toMatchSnapshot();
    });

    it('adapts slidesToShow to match numberOfCardsToExpose on desktop in carousel mode', () => {
      const propsWithDiffslidesToShowAndNumberOfCardsToExpose = {
        ...wayfindingAndProductCardsFourCardsExposedOutOfTen,
        content: {
          ...wayfindingAndProductCardsFourCardsExposedOutOfTen.content,
          carouselSettings: {
            ...wayfindingAndProductCardsFourCardsExposedOutOfTen.content.carouselSettings,
            slidesToShow: 2,
          },
        },
      };
      const { asFragment } = render(<WayfindingAndProductCards {...propsWithDiffslidesToShowAndNumberOfCardsToExpose} />, {
        // @ts-ignore
        breakpoint: XLARGE,
      });

      expect(asFragment()).toMatchSnapshot();
    });
  });

  describe('Mobile', () => {
    it('matches snapshot when it is exposed', () => {
      const { asFragment } = render(<WayfindingAndProductCards {...wayfindingAndProductCardsDefaultData} />, {
        // @ts-ignore
        breakpoint: SMALL,
      });
      expect(asFragment()).toMatchSnapshot();
    });

    it('matches snapshot when it is slider', () => {
      const { asFragment } = render(<WayfindingAndProductCards {...wayfindingAndProductCardsCarouselData} />, {
        // @ts-ignore
        breakpoint: SMALL,
      });
      expect(asFragment()).toMatchSnapshot();
    });

    it('does not show arrows on mobile', () => {
      const { container } = render(<WayfindingAndProductCards {...wayfindingAndProductCardsCarouselUsecase2} />, {
        // @ts-ignore
        breakpoint: SMALL,
      });
      expect(container.querySelector(prevArrowSelector)).not.toBeInTheDocument();
      expect(container.querySelector(nextArrowSelector)).not.toBeInTheDocument();
    });

    it('renders with exposed layout on mobile when mobileLayout is "exposed"', () => {
      const { container, asFragment } = render(<WayfindingAndProductCards {...wayfindingAndProductCardsWithFourExposed} />, {
        // @ts-ignore
        breakpoint: SMALL,
      });
      expect(container.querySelector('.slick-slider')).not.toBeInTheDocument();
      expect(asFragment()).toMatchSnapshot();
    });
  });
});
