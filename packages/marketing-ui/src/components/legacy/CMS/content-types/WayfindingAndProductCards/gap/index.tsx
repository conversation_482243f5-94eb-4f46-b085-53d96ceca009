'use client';
// @ts-ignore
import { CSSObject } from '@ecom-next/core/react-stitch';
import React from 'react';
import { CarouselSettings } from '@mui/components/legacy/CMS/subcomponents/CMSMarketingCarousel/types';
import { Size } from '../../../../components/ComposableButton/types';
import { useViewportIsLarge } from '../../../../hooks/index';
import { ProductCards, ProductCardsContainer, ProductCardsTopArea } from '../../../components/product-cards/index';
import { ShowHideWrapper } from '../../../subcomponents/ShowHideWrapper/index';
import { WayfindingAndProductCardsProps } from '../types';
import { nextArrowSelector, prevArrowSelector, scalableText, WayfindingAndProductCardsContainer } from '../utilities';
import { FlexDirection } from '../../SpotlightCarousel/types';

const GapWayfindingAndProductCards = (props: WayfindingAndProductCardsProps) => {
  const { general, content } = props;
  const { mobileLayout, categoryButtonColor, numberOfCardsToExpose = '4' } = general;
  const { topText, cta, categories, carouselSettings, ctaDropdownList } = content;
  const isDesktop = useViewportIsLarge();
  const cardsToExposeInDesktop = parseInt(numberOfCardsToExpose, 10);
  const desktopLayout = categories.length > cardsToExposeInDesktop ? 'scroll' : 'basic';
  const isDesktopScroll = isDesktop && desktopLayout === 'scroll';

  const updatedCarouselSettings = {
    ...carouselSettings,
    slidesToShow: isDesktopScroll ? cardsToExposeInDesktop : carouselSettings?.slidesToShow,
  } as CarouselSettings;
  const variant = isDesktop ? desktopLayout : mobileLayout;
  const spaceAboveCardRichText = isDesktop ? '1.04vw' : '2.5vw';
  const rowGapAndSidePadding = 30;
  const wayfindingAndProductCardsContainerCustomStyles = {
    padding: isDesktop ? '60px 0 60px 60px' : '7vw 3vw',
    ...(isDesktop && {
      display: 'flex',
      width: '1440px',
      justifyContent: 'center',
      alignItems: 'flex-start',
      flexDirection: 'column' as FlexDirection,
    }),
  };

  const cardContainerRightMargin = isDesktop ? 10 : undefined;
  const productCardsTopAreaStyles: CSSObject = {
    marginBottom: isDesktop ? '1.04vw' : 'calc(8px + 2vw)',
    columnGap: isDesktop ? '15px' : '20px',
    ...(isDesktopScroll && {
      maxWidth: `calc(100% - ${cardContainerRightMargin}px)`,
    }),
  };

  const productCardsCustomStyles: CSSObject = {
    marginRight: isDesktop ? cardContainerRightMargin : undefined,
    gap: '15px',
  };

  const productCardsCustomBasicStyles: CSSObject = {
    gap: rowGapAndSidePadding,
  };

  const productCardsCustomExposedStyles: CSSObject = {
    gap: rowGapAndSidePadding,
  };

  const productCardsCustomScrollStyles: CSSObject = {
    gap: rowGapAndSidePadding,
    gridTemplateColumns: `repeat(${categories.length}, calc((100vw - 60px - 3vw) / 2.4))`,
    paddingTop: isDesktop ? '15px' : undefined,
  };

  const letterSpacing = isDesktop ? '.32px !important' : '.28px !important';
  const lineHeight = 1;

  const productCardsCustomCarouselStyles = isDesktopScroll
    ? {
        [`& ${nextArrowSelector}`]: {
          left: 'calc(100% + 3px)',
        },
        [`& ${prevArrowSelector}`]: {
          left: '-55px',
        },
      }
    : {};
  const getBoxShadow = (buttonStyle: string): string | undefined => {
    if (!['border', 'outline'].includes(buttonStyle)) {
      return undefined;
    }
    return buttonStyle === 'light' ? 'inset 0 0 0 1px #FFF' : 'inset 0 0 0 1px';
  };
  const { buttonStyle } = cta?.[0] || {};
  const topAreaBoxShadow = getBoxShadow(buttonStyle?.buttonStyle || '');
  const topAreaCustomCtaStyles: CSSObject = {
    alignSelf: 'center',
    marginLeft: 'auto',
    border: 'transparent',
    letterSpacing,
    lineHeight,
    button: {
      letterSpacing,
    },
    a: {
      lineHeight,
      letterSpacing,
      span: {
        letterSpacing,
      },
    },
  };

  const ctaSize = isDesktop ? Size.large : Size.medium;

  const customCtaDropdownButtonStyles: CSSObject = {
    position: 'relative',
    'span[data-id="cta-dropdown-label"]': {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      '& > span': {
        position: 'relative',
        transform: 'none',
      },
    },
  };

  const customCtaDropdownItemsStyles: CSSObject = {
    position: 'relative',
    width: 'min-content',
    li: {
      lineHeight,
    },
  };

  const customTopCtaDropdownItemsStyles: CSSObject = {
    position: 'absolute',
    right: 0,
    width: undefined,
    li: {
      lineHeight,
      a: {
        letterSpacing,
      },
    },
  };

  const wayFindingCategories = (categories || []).map(category => {
    return {
      ...category,
    };
  });

  const customAnimatedButtonStyles: CSSObject = {
    '& > span': {
      letterSpacing,
    },
  };

  const customDropdownListWrapperStyles: CSSObject = {
    '& > div > div': {
      position: 'absolute',
      width: '100%',
      bottom: '100%',
    },
  };

  return (
    <ShowHideWrapper breakpoint='large' showHideBasedOnScreenSize={general.showHideBasedOnScreenSize}>
      <ProductCardsContainer {...general}>
        <WayfindingAndProductCardsContainer customStyles={wayfindingAndProductCardsContainerCustomStyles} isDesktop={isDesktop}>
          <ProductCardsTopArea
            cta={cta}
            ctaDropdownList={ctaDropdownList}
            ctaSize={ctaSize}
            customCtaDropdownButtonStyles={customCtaDropdownButtonStyles}
            customCtaDropdownItemsStyles={customTopCtaDropdownItemsStyles}
            customCtaStyles={topAreaCustomCtaStyles}
            customStyles={productCardsTopAreaStyles}
            scalableText={scalableText}
            showCTA
            topText={topText}
          />
          <ProductCards
            carouselSettings={updatedCarouselSettings}
            categories={wayFindingCategories}
            categoryButtonColor={categoryButtonColor}
            customBasicStyles={productCardsCustomBasicStyles}
            customCarouselStyles={productCardsCustomCarouselStyles}
            customCtaDropdownButtonStyles={customCtaDropdownButtonStyles}
            customDropdownListWrapperStyles={customDropdownListWrapperStyles}
            customCtaDropdownItemsStyles={customCtaDropdownItemsStyles}
            customAnimatedButtonStyles={customAnimatedButtonStyles}
            customExposedStyles={productCardsCustomExposedStyles}
            customScrollStyles={productCardsCustomScrollStyles}
            customStyles={productCardsCustomStyles}
            isCtaConfigurable
            spaceAboveCardRichText={spaceAboveCardRichText}
            variant={variant}
            customGapCss={{
              ...(isDesktop
                ? {
                    marginRight: 0,
                    alignSelf: 'stretch',
                    ...(carouselSettings?.slidesToShow === 6 && cardsToExposeInDesktop === 6 && { aspectRatio: '3 / 4' }),
                    width: cardsToExposeInDesktop === 6 ? '13.54vw' : '21.35vw',
                  }
                : {}),
            }}
          />
        </WayfindingAndProductCardsContainer>
      </ProductCardsContainer>
    </ShowHideWrapper>
  );
};

export default GapWayfindingAndProductCards;
