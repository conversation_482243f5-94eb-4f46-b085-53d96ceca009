// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`WayfindingAndProductCards Desktop adapts slidesToShow to match numberOfCardsToExpose on desktop in carousel mode 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #4CA1AF;
}

.emotion-1 {
  padding: 60px 0 60px 60px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 1440px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  margin-bottom: 1.04vw;
  -webkit-column-gap: 15px;
  column-gap: 15px;
  max-width: calc(100% - 10px);
}

.emotion-3 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-3 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-3 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-3 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-3 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-3 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-3 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.3888888888888888vw);
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.25vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.1111111111111112vw);
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.9722222222222222vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 3.4722222222222223vw);
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.7777777777777777vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.430555555555556vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 8.333333333333332vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 7.638888888888889vw);
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.944444444444445vw);
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.944444444444445vw);
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 5.555555555555555vw);
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.861111111111112vw);
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.166666666666666vw);
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, 7.916666666666666vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, 5.833333333333333vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, 4.444444444444445vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, 2.361111111111111vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 6.944444444444445vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 5.555555555555555vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-3 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 2.083333333333333vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.7361111111111112vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.3888888888888888vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  margin-left: auto;
  border: transparent;
  letter-spacing: .32px!important;
  line-height: 1;
}

.emotion-4:focus {
  outline: none;
}

.emotion-4:hover,
.emotion-4:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-4:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-4 button {
  letter-spacing: .32px!important;
}

.emotion-4 a {
  line-height: 1;
  letter-spacing: .32px!important;
}

.emotion-4 a span {
  letter-spacing: .32px!important;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 30px;
  grid-template-columns: repeat(10, calc((100vw - 60px - 3vw) / 2.4));
  overflow-x: scroll;
  scrollbar-width: none;
  margin: 0;
  padding: 0;
  -ms-overflow-style: none;
  padding-top: 15px;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  width: calc(100vw - 60px);
}

.emotion-5::-webkit-scrollbar {
  display: none;
}

.emotion-6 {
  width: 21.35vw;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  gap: 15px;
  margin-right: 0;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-7 {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
}

.emotion-8 {
  -webkit-background-size: cover;
  background-size: cover;
  margin-right: 0;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  width: 21.35vw;
}

.emotion-9 {
  width: 100%;
  aspect-ratio: 3/4;
  object-fit: cover;
}

.emotion-10 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 335px;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  padding-bottom: 1.8px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 1.8px;
  text-decoration-thickness: 2px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-10:focus {
  outline: none;
}

.emotion-10:hover,
.emotion-10:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-10:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-11 {
  box-sizing: border-box;
}

.emotion-16 {
  color: #000000;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  z-index: 2;
}

.emotion-16>span {
  padding-right: 5px;
}

.emotion-16>div {
  -webkit-transition: all 100ms ease-in-out;
  transition: all 100ms ease-in-out;
}

.emotion-16:hover>div {
  -webkit-transform: translate(5px,0);
  -moz-transform: translate(5px,0);
  -ms-transform: translate(5px,0);
  transform: translate(5px,0);
}

.emotion-17 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-17 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-17 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-17 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-17 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-17 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-17 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-17 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 70px;
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 114px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-17 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-17 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-17 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-17 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      height="0"
      width="0"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-3"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--headline-1"
                >
                  wayfinding rich text 1
                </span>
              </p>
            </div>
          </div>
          <a
            class="emotion-4"
            color="dark"
            href="https://www.gap.com"
          >
            Shop all
          </a>
        </div>
        <div
          class="emotion-5"
        >
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red1"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-10"
              color="dark"
              href="#tops"
            >
              <span
                class="emotion-11"
              >
                Tops
              </span>
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="athleta face mask covid"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_dp_accessories_grid_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-16"
              href="#leggings"
            >
              <span>
                Leggings
              </span>
              <div
                data-testid="animated-arrow"
              >
                <svg
                  fill="none"
                  height="8"
                  viewBox="0 0 21 8"
                  width="21"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20.354 4.35457C20.4477 4.26081 20.5004 4.13366 20.5004 4.00107C20.5004 3.86849 20.4477 3.74134 20.354 3.64757L17.172 0.464575C17.0777 0.373496 16.9514 0.323098 16.8203 0.324238C16.6892 0.325377 16.5638 0.377961 16.4711 0.470665C16.3784 0.563369 16.3258 0.688776 16.3247 0.819874C16.3235 0.950972 16.3739 1.07727 16.465 1.17157L19.293 4.00057L16.465 6.82857C16.3739 6.92288 16.3235 7.04918 16.3247 7.18027C16.3258 7.31137 16.3784 7.43678 16.4711 7.52948C16.5638 7.62219 16.6892 7.67477 16.8203 7.67591C16.9514 7.67705 17.0777 7.62665 17.172 7.53557L20.354 4.35457ZM0 4.50057H20V3.50057H0V4.50057Z"
                    fill="#000000"
                  />
                </svg>
              </div>
            </a>
            <div
              class="emotion-17"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Inclusive sizes
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="girl in pink skating"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/SU22_D2_ATG_DressesRompers_S?fmt=webp"
              />
            </div>
            <div
              class="emotion-17"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    New items
                  </span>
                </p>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-3"
                  >
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cras volutpat, risus in mattis ultrices, libero mauris blandit odio.
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="athleta mask pink"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_dp_accessories_grid_jpg?fmt=webp"
              />
            </div>
            <div
              class="emotion-17"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Great prices
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red2"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-16"
              href="#tops"
            >
              <span>
                Tops
              </span>
              <div
                data-testid="animated-arrow"
              >
                <svg
                  fill="none"
                  height="8"
                  viewBox="0 0 21 8"
                  width="21"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20.354 4.35457C20.4477 4.26081 20.5004 4.13366 20.5004 4.00107C20.5004 3.86849 20.4477 3.74134 20.354 3.64757L17.172 0.464575C17.0777 0.373496 16.9514 0.323098 16.8203 0.324238C16.6892 0.325377 16.5638 0.377961 16.4711 0.470665C16.3784 0.563369 16.3258 0.688776 16.3247 0.819874C16.3235 0.950972 16.3739 1.07727 16.465 1.17157L19.293 4.00057L16.465 6.82857C16.3739 6.92288 16.3235 7.04918 16.3247 7.18027C16.3258 7.31137 16.3784 7.43678 16.4711 7.52948C16.5638 7.62219 16.6892 7.67477 16.8203 7.67591C16.9514 7.67705 17.0777 7.62665 17.172 7.53557L20.354 4.35457ZM0 4.50057H20V3.50057H0V4.50057Z"
                    fill="#000000"
                  />
                </svg>
              </div>
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red3"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-16"
              href="#tops"
            >
              <span>
                Tops
              </span>
              <div
                data-testid="animated-arrow"
              >
                <svg
                  fill="none"
                  height="8"
                  viewBox="0 0 21 8"
                  width="21"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20.354 4.35457C20.4477 4.26081 20.5004 4.13366 20.5004 4.00107C20.5004 3.86849 20.4477 3.74134 20.354 3.64757L17.172 0.464575C17.0777 0.373496 16.9514 0.323098 16.8203 0.324238C16.6892 0.325377 16.5638 0.377961 16.4711 0.470665C16.3784 0.563369 16.3258 0.688776 16.3247 0.819874C16.3235 0.950972 16.3739 1.07727 16.465 1.17157L19.293 4.00057L16.465 6.82857C16.3739 6.92288 16.3235 7.04918 16.3247 7.18027C16.3258 7.31137 16.3784 7.43678 16.4711 7.52948C16.5638 7.62219 16.6892 7.67477 16.8203 7.67591C16.9514 7.67705 17.0777 7.62665 17.172 7.53557L20.354 4.35457ZM0 4.50057H20V3.50057H0V4.50057Z"
                    fill="#000000"
                  />
                </svg>
              </div>
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="athleta face mask covid"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_dp_accessories_grid_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-16"
              href="#leggings"
            >
              <span>
                Leggings
              </span>
              <div
                data-testid="animated-arrow"
              >
                <svg
                  fill="none"
                  height="8"
                  viewBox="0 0 21 8"
                  width="21"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20.354 4.35457C20.4477 4.26081 20.5004 4.13366 20.5004 4.00107C20.5004 3.86849 20.4477 3.74134 20.354 3.64757L17.172 0.464575C17.0777 0.373496 16.9514 0.323098 16.8203 0.324238C16.6892 0.325377 16.5638 0.377961 16.4711 0.470665C16.3784 0.563369 16.3258 0.688776 16.3247 0.819874C16.3235 0.950972 16.3739 1.07727 16.465 1.17157L19.293 4.00057L16.465 6.82857C16.3739 6.92288 16.3235 7.04918 16.3247 7.18027C16.3258 7.31137 16.3784 7.43678 16.4711 7.52948C16.5638 7.62219 16.6892 7.67477 16.8203 7.67591C16.9514 7.67705 17.0777 7.62665 17.172 7.53557L20.354 4.35457ZM0 4.50057H20V3.50057H0V4.50057Z"
                    fill="#000000"
                  />
                </svg>
              </div>
            </a>
            <div
              class="emotion-17"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Inclusive sizes
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="girl in pink skating"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/SU22_D2_ATG_DressesRompers_S?fmt=webp"
              />
            </div>
            <div
              class="emotion-17"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    New items
                  </span>
                </p>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-3"
                  >
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cras volutpat, risus in mattis ultrices, libero mauris blandit odio.
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="athleta mask pink"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_dp_accessories_grid_jpg?fmt=webp"
              />
            </div>
            <div
              class="emotion-17"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Great prices
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red4"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-16"
              href="#tops"
            >
              <span>
                Tops
              </span>
              <div
                data-testid="animated-arrow"
              >
                <svg
                  fill="none"
                  height="8"
                  viewBox="0 0 21 8"
                  width="21"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20.354 4.35457C20.4477 4.26081 20.5004 4.13366 20.5004 4.00107C20.5004 3.86849 20.4477 3.74134 20.354 3.64757L17.172 0.464575C17.0777 0.373496 16.9514 0.323098 16.8203 0.324238C16.6892 0.325377 16.5638 0.377961 16.4711 0.470665C16.3784 0.563369 16.3258 0.688776 16.3247 0.819874C16.3235 0.950972 16.3739 1.07727 16.465 1.17157L19.293 4.00057L16.465 6.82857C16.3739 6.92288 16.3235 7.04918 16.3247 7.18027C16.3258 7.31137 16.3784 7.43678 16.4711 7.52948C16.5638 7.62219 16.6892 7.67477 16.8203 7.67591C16.9514 7.67705 17.0777 7.62665 17.172 7.53557L20.354 4.35457ZM0 4.50057H20V3.50057H0V4.50057Z"
                    fill="#000000"
                  />
                </svg>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`WayfindingAndProductCards Desktop matches snapshot when it is basic 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #4CA1AF;
}

.emotion-1 {
  padding: 60px 0 60px 60px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 1440px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  margin-bottom: 1.04vw;
  -webkit-column-gap: 15px;
  column-gap: 15px;
}

.emotion-3 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-3 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-3 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-3 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-3 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-3 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-3 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.3888888888888888vw);
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.25vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.1111111111111112vw);
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.9722222222222222vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 3.4722222222222223vw);
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.7777777777777777vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.430555555555556vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 8.333333333333332vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 7.638888888888889vw);
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.944444444444445vw);
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.944444444444445vw);
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 5.555555555555555vw);
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.861111111111112vw);
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.166666666666666vw);
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, 7.916666666666666vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, 5.833333333333333vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, 4.444444444444445vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, 2.361111111111111vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 6.944444444444445vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 5.555555555555555vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-3 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 2.083333333333333vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.7361111111111112vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.3888888888888888vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  margin-left: auto;
  border: transparent;
  letter-spacing: .32px!important;
  line-height: 1;
}

.emotion-4:focus {
  outline: none;
}

.emotion-4:hover,
.emotion-4:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-4:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-4 button {
  letter-spacing: .32px!important;
}

.emotion-4 a {
  line-height: 1;
  letter-spacing: .32px!important;
}

.emotion-4 a span {
  letter-spacing: .32px!important;
}

.emotion-5 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
}

.emotion-6 {
  width: 21.35vw;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  gap: 15px;
  margin-right: 0;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-7 {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
}

.emotion-8 {
  -webkit-background-size: cover;
  background-size: cover;
  margin-right: 0;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  width: 21.35vw;
}

.emotion-9 {
  width: 100%;
  aspect-ratio: 3/4;
  object-fit: cover;
}

.emotion-10 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 335px;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 14px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-10:focus {
  outline: none;
}

.emotion-10:hover,
.emotion-10:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-10:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-15 {
  color: #000000;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  z-index: 2;
}

.emotion-15>span {
  padding-right: 5px;
}

.emotion-15>div {
  -webkit-transition: all 100ms ease-in-out;
  transition: all 100ms ease-in-out;
}

.emotion-15:hover>div {
  -webkit-transform: translate(5px,0);
  -moz-transform: translate(5px,0);
  -ms-transform: translate(5px,0);
  transform: translate(5px,0);
}

.emotion-16 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-16 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-16 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-16 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-16 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-16 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-16 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-16 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 70px;
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 114px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-16 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-16 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-16 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-16 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-16 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-16 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-16 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-16 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-16 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-16 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      height="0"
      width="0"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-3"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--headline-1"
                >
                  wayfinding rich text 1
                </span>
              </p>
            </div>
          </div>
          <a
            class="emotion-4"
            color="dark"
            href="https://www.gap.com"
          >
            Shop all
          </a>
        </div>
        <div
          class="emotion-5"
        >
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red1"
                class="emotion-9"
                src=""
              />
            </div>
            <a
              class="emotion-10"
              color="dark"
              href="#tops"
            >
              Tops
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="athleta face mask covid"
                class="emotion-9"
                src=""
              />
            </div>
            <a
              class="emotion-15"
              href="#leggings"
            >
              <span>
                Leggings
              </span>
              <div
                data-testid="animated-arrow"
              >
                <svg
                  fill="none"
                  height="8"
                  viewBox="0 0 21 8"
                  width="21"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20.354 4.35457C20.4477 4.26081 20.5004 4.13366 20.5004 4.00107C20.5004 3.86849 20.4477 3.74134 20.354 3.64757L17.172 0.464575C17.0777 0.373496 16.9514 0.323098 16.8203 0.324238C16.6892 0.325377 16.5638 0.377961 16.4711 0.470665C16.3784 0.563369 16.3258 0.688776 16.3247 0.819874C16.3235 0.950972 16.3739 1.07727 16.465 1.17157L19.293 4.00057L16.465 6.82857C16.3739 6.92288 16.3235 7.04918 16.3247 7.18027C16.3258 7.31137 16.3784 7.43678 16.4711 7.52948C16.5638 7.62219 16.6892 7.67477 16.8203 7.67591C16.9514 7.67705 17.0777 7.62665 17.172 7.53557L20.354 4.35457ZM0 4.50057H20V3.50057H0V4.50057Z"
                    fill="#000000"
                  />
                </svg>
              </div>
            </a>
            <div
              class="emotion-16"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Inclusive sizes
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="girl in pink skating"
                class="emotion-9"
                src=""
              />
            </div>
            <div
              class="emotion-16"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    New items
                  </span>
                </p>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-3"
                  >
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cras volutpat, risus in mattis ultrices, libero mauris blandit odio.
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="athleta mask pink"
                class="emotion-9"
                src=""
              />
            </div>
            <div
              class="emotion-16"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Great prices
                  </span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`WayfindingAndProductCards Desktop matches snapshot when it is carousel 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #4CA1AF;
}

.emotion-1 {
  padding: 60px 0 60px 60px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 1440px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  margin-bottom: 1.04vw;
  -webkit-column-gap: 15px;
  column-gap: 15px;
  max-width: calc(100% - 10px);
}

.emotion-3 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-3 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-3 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-3 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-3 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-3 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-3 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.3888888888888888vw);
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.25vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.1111111111111112vw);
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.9722222222222222vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 3.4722222222222223vw);
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.7777777777777777vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.430555555555556vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 8.333333333333332vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 7.638888888888889vw);
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.944444444444445vw);
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.944444444444445vw);
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 5.555555555555555vw);
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.861111111111112vw);
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.166666666666666vw);
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, 7.916666666666666vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, 5.833333333333333vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, 4.444444444444445vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, 2.361111111111111vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 6.944444444444445vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 5.555555555555555vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-3 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 2.083333333333333vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.7361111111111112vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.3888888888888888vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  margin-left: auto;
  border: transparent;
  letter-spacing: .32px!important;
  line-height: 1;
}

.emotion-4:focus {
  outline: none;
}

.emotion-4:hover,
.emotion-4:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-4:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-4 button {
  letter-spacing: .32px!important;
}

.emotion-4 a {
  line-height: 1;
  letter-spacing: .32px!important;
}

.emotion-4 a span {
  letter-spacing: .32px!important;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 30px;
  grid-template-columns: repeat(10, calc((100vw - 60px - 3vw) / 2.4));
  overflow-x: scroll;
  scrollbar-width: none;
  margin: 0;
  padding: 0;
  -ms-overflow-style: none;
  padding-top: 15px;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  width: calc(100vw - 60px);
}

.emotion-5::-webkit-scrollbar {
  display: none;
}

.emotion-6 {
  width: 21.35vw;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  gap: 15px;
  margin-right: 0;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-7 {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
}

.emotion-8 {
  -webkit-background-size: cover;
  background-size: cover;
  margin-right: 0;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  width: 21.35vw;
}

.emotion-9 {
  width: 100%;
  aspect-ratio: 3/4;
  object-fit: cover;
}

.emotion-10 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 335px;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  padding-bottom: 1.8px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 1.8px;
  text-decoration-thickness: 2px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-10:focus {
  outline: none;
}

.emotion-10:hover,
.emotion-10:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-10:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-11 {
  box-sizing: border-box;
}

.emotion-16 {
  color: #000000;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  z-index: 2;
}

.emotion-16>span {
  padding-right: 5px;
}

.emotion-16>div {
  -webkit-transition: all 100ms ease-in-out;
  transition: all 100ms ease-in-out;
}

.emotion-16:hover>div {
  -webkit-transform: translate(5px,0);
  -moz-transform: translate(5px,0);
  -ms-transform: translate(5px,0);
  transform: translate(5px,0);
}

.emotion-17 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-17 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-17 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-17 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-17 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-17 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-17 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-17 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 70px;
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 114px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-17 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-17 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-17 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-17 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      height="0"
      width="0"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-3"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--headline-1"
                >
                  wayfinding rich text 1
                </span>
              </p>
            </div>
          </div>
          <a
            class="emotion-4"
            color="dark"
            href="https://www.gap.com"
          >
            Shop all
          </a>
        </div>
        <div
          class="emotion-5"
        >
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red1"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-10"
              color="dark"
              href="#tops"
            >
              <span
                class="emotion-11"
              >
                Tops
              </span>
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="athleta face mask covid"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_dp_accessories_grid_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-16"
              href="#leggings"
            >
              <span>
                Leggings
              </span>
              <div
                data-testid="animated-arrow"
              >
                <svg
                  fill="none"
                  height="8"
                  viewBox="0 0 21 8"
                  width="21"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20.354 4.35457C20.4477 4.26081 20.5004 4.13366 20.5004 4.00107C20.5004 3.86849 20.4477 3.74134 20.354 3.64757L17.172 0.464575C17.0777 0.373496 16.9514 0.323098 16.8203 0.324238C16.6892 0.325377 16.5638 0.377961 16.4711 0.470665C16.3784 0.563369 16.3258 0.688776 16.3247 0.819874C16.3235 0.950972 16.3739 1.07727 16.465 1.17157L19.293 4.00057L16.465 6.82857C16.3739 6.92288 16.3235 7.04918 16.3247 7.18027C16.3258 7.31137 16.3784 7.43678 16.4711 7.52948C16.5638 7.62219 16.6892 7.67477 16.8203 7.67591C16.9514 7.67705 17.0777 7.62665 17.172 7.53557L20.354 4.35457ZM0 4.50057H20V3.50057H0V4.50057Z"
                    fill="#000000"
                  />
                </svg>
              </div>
            </a>
            <div
              class="emotion-17"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Inclusive sizes
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="girl in pink skating"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/SU22_D2_ATG_DressesRompers_S?fmt=webp"
              />
            </div>
            <div
              class="emotion-17"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    New items
                  </span>
                </p>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-3"
                  >
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cras volutpat, risus in mattis ultrices, libero mauris blandit odio.
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="athleta mask pink"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_dp_accessories_grid_jpg?fmt=webp"
              />
            </div>
            <div
              class="emotion-17"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Great prices
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red2"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-16"
              href="#tops"
            >
              <span>
                Tops
              </span>
              <div
                data-testid="animated-arrow"
              >
                <svg
                  fill="none"
                  height="8"
                  viewBox="0 0 21 8"
                  width="21"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20.354 4.35457C20.4477 4.26081 20.5004 4.13366 20.5004 4.00107C20.5004 3.86849 20.4477 3.74134 20.354 3.64757L17.172 0.464575C17.0777 0.373496 16.9514 0.323098 16.8203 0.324238C16.6892 0.325377 16.5638 0.377961 16.4711 0.470665C16.3784 0.563369 16.3258 0.688776 16.3247 0.819874C16.3235 0.950972 16.3739 1.07727 16.465 1.17157L19.293 4.00057L16.465 6.82857C16.3739 6.92288 16.3235 7.04918 16.3247 7.18027C16.3258 7.31137 16.3784 7.43678 16.4711 7.52948C16.5638 7.62219 16.6892 7.67477 16.8203 7.67591C16.9514 7.67705 17.0777 7.62665 17.172 7.53557L20.354 4.35457ZM0 4.50057H20V3.50057H0V4.50057Z"
                    fill="#000000"
                  />
                </svg>
              </div>
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red3"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-16"
              href="#tops"
            >
              <span>
                Tops
              </span>
              <div
                data-testid="animated-arrow"
              >
                <svg
                  fill="none"
                  height="8"
                  viewBox="0 0 21 8"
                  width="21"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20.354 4.35457C20.4477 4.26081 20.5004 4.13366 20.5004 4.00107C20.5004 3.86849 20.4477 3.74134 20.354 3.64757L17.172 0.464575C17.0777 0.373496 16.9514 0.323098 16.8203 0.324238C16.6892 0.325377 16.5638 0.377961 16.4711 0.470665C16.3784 0.563369 16.3258 0.688776 16.3247 0.819874C16.3235 0.950972 16.3739 1.07727 16.465 1.17157L19.293 4.00057L16.465 6.82857C16.3739 6.92288 16.3235 7.04918 16.3247 7.18027C16.3258 7.31137 16.3784 7.43678 16.4711 7.52948C16.5638 7.62219 16.6892 7.67477 16.8203 7.67591C16.9514 7.67705 17.0777 7.62665 17.172 7.53557L20.354 4.35457ZM0 4.50057H20V3.50057H0V4.50057Z"
                    fill="#000000"
                  />
                </svg>
              </div>
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="athleta face mask covid"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_dp_accessories_grid_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-16"
              href="#leggings"
            >
              <span>
                Leggings
              </span>
              <div
                data-testid="animated-arrow"
              >
                <svg
                  fill="none"
                  height="8"
                  viewBox="0 0 21 8"
                  width="21"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20.354 4.35457C20.4477 4.26081 20.5004 4.13366 20.5004 4.00107C20.5004 3.86849 20.4477 3.74134 20.354 3.64757L17.172 0.464575C17.0777 0.373496 16.9514 0.323098 16.8203 0.324238C16.6892 0.325377 16.5638 0.377961 16.4711 0.470665C16.3784 0.563369 16.3258 0.688776 16.3247 0.819874C16.3235 0.950972 16.3739 1.07727 16.465 1.17157L19.293 4.00057L16.465 6.82857C16.3739 6.92288 16.3235 7.04918 16.3247 7.18027C16.3258 7.31137 16.3784 7.43678 16.4711 7.52948C16.5638 7.62219 16.6892 7.67477 16.8203 7.67591C16.9514 7.67705 17.0777 7.62665 17.172 7.53557L20.354 4.35457ZM0 4.50057H20V3.50057H0V4.50057Z"
                    fill="#000000"
                  />
                </svg>
              </div>
            </a>
            <div
              class="emotion-17"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Inclusive sizes
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="girl in pink skating"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/SU22_D2_ATG_DressesRompers_S?fmt=webp"
              />
            </div>
            <div
              class="emotion-17"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    New items
                  </span>
                </p>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-3"
                  >
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cras volutpat, risus in mattis ultrices, libero mauris blandit odio.
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="athleta mask pink"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_dp_accessories_grid_jpg?fmt=webp"
              />
            </div>
            <div
              class="emotion-17"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Great prices
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red4"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-16"
              href="#tops"
            >
              <span>
                Tops
              </span>
              <div
                data-testid="animated-arrow"
              >
                <svg
                  fill="none"
                  height="8"
                  viewBox="0 0 21 8"
                  width="21"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20.354 4.35457C20.4477 4.26081 20.5004 4.13366 20.5004 4.00107C20.5004 3.86849 20.4477 3.74134 20.354 3.64757L17.172 0.464575C17.0777 0.373496 16.9514 0.323098 16.8203 0.324238C16.6892 0.325377 16.5638 0.377961 16.4711 0.470665C16.3784 0.563369 16.3258 0.688776 16.3247 0.819874C16.3235 0.950972 16.3739 1.07727 16.465 1.17157L19.293 4.00057L16.465 6.82857C16.3739 6.92288 16.3235 7.04918 16.3247 7.18027C16.3258 7.31137 16.3784 7.43678 16.4711 7.52948C16.5638 7.62219 16.6892 7.67477 16.8203 7.67591C16.9514 7.67705 17.0777 7.62665 17.172 7.53557L20.354 4.35457ZM0 4.50057H20V3.50057H0V4.50057Z"
                    fill="#000000"
                  />
                </svg>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`WayfindingAndProductCards Desktop uses basic layout when numberOfCardsToExpose is "4" and there are 4 cards 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #4CA1AF;
}

.emotion-1 {
  padding: 60px 0 60px 60px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 1440px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  margin-bottom: 1.04vw;
  -webkit-column-gap: 15px;
  column-gap: 15px;
}

.emotion-3 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-3 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-3 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-3 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-3 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-3 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-3 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.3888888888888888vw);
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.25vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.1111111111111112vw);
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.9722222222222222vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 3.4722222222222223vw);
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.7777777777777777vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.430555555555556vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 8.333333333333332vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 7.638888888888889vw);
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.944444444444445vw);
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.944444444444445vw);
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 5.555555555555555vw);
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.861111111111112vw);
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.166666666666666vw);
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, 7.916666666666666vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, 5.833333333333333vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, 4.444444444444445vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, 2.361111111111111vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 6.944444444444445vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 5.555555555555555vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-3 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 2.083333333333333vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.7361111111111112vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.3888888888888888vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  margin-left: auto;
  border: transparent;
  letter-spacing: .32px!important;
  line-height: 1;
}

.emotion-4:focus {
  outline: none;
}

.emotion-4:hover,
.emotion-4:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-4:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-4 button {
  letter-spacing: .32px!important;
}

.emotion-4 a {
  line-height: 1;
  letter-spacing: .32px!important;
}

.emotion-4 a span {
  letter-spacing: .32px!important;
}

.emotion-5 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
}

.emotion-6 {
  width: 21.35vw;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  gap: 15px;
  margin-right: 0;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-7 {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
}

.emotion-8 {
  -webkit-background-size: cover;
  background-size: cover;
  margin-right: 0;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  width: 21.35vw;
}

.emotion-9 {
  width: 100%;
  aspect-ratio: 3/4;
  object-fit: cover;
}

.emotion-10 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 335px;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 14px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-10:focus {
  outline: none;
}

.emotion-10:hover,
.emotion-10:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-10:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-15 {
  color: #000000;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  z-index: 2;
}

.emotion-15>span {
  padding-right: 5px;
}

.emotion-15>div {
  -webkit-transition: all 100ms ease-in-out;
  transition: all 100ms ease-in-out;
}

.emotion-15:hover>div {
  -webkit-transform: translate(5px,0);
  -moz-transform: translate(5px,0);
  -ms-transform: translate(5px,0);
  transform: translate(5px,0);
}

.emotion-16 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-16 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-16 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-16 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-16 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-16 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-16 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-16 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 70px;
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 114px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-16 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-16 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-16 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-16 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-16 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-16 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-16 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-16 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-16 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-16 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      height="0"
      width="0"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-3"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--headline-1"
                >
                  wayfinding rich text 1
                </span>
              </p>
            </div>
          </div>
          <a
            class="emotion-4"
            color="dark"
            href="https://www.gap.com"
          >
            Shop all
          </a>
        </div>
        <div
          class="emotion-5"
        >
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red1"
                class="emotion-9"
                src=""
              />
            </div>
            <a
              class="emotion-10"
              color="dark"
              href="#tops"
            >
              Tops
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="athleta face mask covid"
                class="emotion-9"
                src=""
              />
            </div>
            <a
              class="emotion-15"
              href="#leggings"
            >
              <span>
                Leggings
              </span>
              <div
                data-testid="animated-arrow"
              >
                <svg
                  fill="none"
                  height="8"
                  viewBox="0 0 21 8"
                  width="21"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20.354 4.35457C20.4477 4.26081 20.5004 4.13366 20.5004 4.00107C20.5004 3.86849 20.4477 3.74134 20.354 3.64757L17.172 0.464575C17.0777 0.373496 16.9514 0.323098 16.8203 0.324238C16.6892 0.325377 16.5638 0.377961 16.4711 0.470665C16.3784 0.563369 16.3258 0.688776 16.3247 0.819874C16.3235 0.950972 16.3739 1.07727 16.465 1.17157L19.293 4.00057L16.465 6.82857C16.3739 6.92288 16.3235 7.04918 16.3247 7.18027C16.3258 7.31137 16.3784 7.43678 16.4711 7.52948C16.5638 7.62219 16.6892 7.67477 16.8203 7.67591C16.9514 7.67705 17.0777 7.62665 17.172 7.53557L20.354 4.35457ZM0 4.50057H20V3.50057H0V4.50057Z"
                    fill="#000000"
                  />
                </svg>
              </div>
            </a>
            <div
              class="emotion-16"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Inclusive sizes
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="girl in pink skating"
                class="emotion-9"
                src=""
              />
            </div>
            <div
              class="emotion-16"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    New items
                  </span>
                </p>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-3"
                  >
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cras volutpat, risus in mattis ultrices, libero mauris blandit odio.
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="athleta mask pink"
                class="emotion-9"
                src=""
              />
            </div>
            <div
              class="emotion-16"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Great prices
                  </span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`WayfindingAndProductCards Desktop uses basic layout when numberOfCardsToExpose is "6" and there are 6 cards 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #4CA1AF;
}

.emotion-1 {
  padding: 60px 0 60px 60px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 1440px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  margin-bottom: 1.04vw;
  -webkit-column-gap: 15px;
  column-gap: 15px;
}

.emotion-3 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-3 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-3 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-3 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-3 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-3 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-3 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.3888888888888888vw);
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.25vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.1111111111111112vw);
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.9722222222222222vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 3.4722222222222223vw);
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.7777777777777777vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.430555555555556vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 8.333333333333332vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 7.638888888888889vw);
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.944444444444445vw);
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.944444444444445vw);
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 5.555555555555555vw);
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.861111111111112vw);
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.166666666666666vw);
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, 7.916666666666666vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, 5.833333333333333vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, 4.444444444444445vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, 2.361111111111111vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 6.944444444444445vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 5.555555555555555vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-3 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 2.083333333333333vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.7361111111111112vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.3888888888888888vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  margin-left: auto;
  border: transparent;
  letter-spacing: .32px!important;
  line-height: 1;
}

.emotion-4:focus {
  outline: none;
}

.emotion-4:hover,
.emotion-4:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-4:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-4 button {
  letter-spacing: .32px!important;
}

.emotion-4 a {
  line-height: 1;
  letter-spacing: .32px!important;
}

.emotion-4 a span {
  letter-spacing: .32px!important;
}

.emotion-5 {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 30px;
}

.emotion-6 {
  width: 13.54vw;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  gap: 15px;
  margin-right: 0;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-7 {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
}

.emotion-8 {
  -webkit-background-size: cover;
  background-size: cover;
  margin-right: 0;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  width: 13.54vw;
}

.emotion-9 {
  width: 100%;
  aspect-ratio: 3/4;
  object-fit: cover;
}

.emotion-10 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 335px;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  padding-bottom: 1.8px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 1.8px;
  text-decoration-thickness: 2px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-10:focus {
  outline: none;
}

.emotion-10:hover,
.emotion-10:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-10:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-11 {
  box-sizing: border-box;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      height="0"
      width="0"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-3"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--headline-1"
                >
                  wayfinding rich text 1
                </span>
              </p>
            </div>
          </div>
          <a
            class="emotion-4"
            color="dark"
            href="https://www.gap.com"
          >
            Shop all
          </a>
        </div>
        <div
          class="emotion-5"
        >
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red1"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-10"
              color="dark"
              href="#tops"
            >
              <span
                class="emotion-11"
              >
                Tops
              </span>
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red1"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-10"
              color="dark"
              href="#tops"
            >
              <span
                class="emotion-11"
              >
                Tops
              </span>
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red1"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-10"
              color="dark"
              href="#tops"
            >
              <span
                class="emotion-11"
              >
                Tops
              </span>
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red1"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-10"
              color="dark"
              href="#tops"
            >
              <span
                class="emotion-11"
              >
                Tops
              </span>
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red1"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-10"
              color="dark"
              href="#tops"
            >
              <span
                class="emotion-11"
              >
                Tops
              </span>
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red1"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-10"
              color="dark"
              href="#tops"
            >
              <span
                class="emotion-11"
              >
                Tops
              </span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`WayfindingAndProductCards Desktop uses carousel layout when numberOfCardsToExpose is "4" with partial 5th card and there are 5+ cards 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #4CA1AF;
}

.emotion-1 {
  padding: 60px 0 60px 60px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 1440px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  margin-bottom: 1.04vw;
  -webkit-column-gap: 15px;
  column-gap: 15px;
  max-width: calc(100% - 10px);
}

.emotion-3 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-3 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-3 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-3 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-3 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-3 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-3 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.3888888888888888vw);
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.25vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.1111111111111112vw);
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.9722222222222222vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 3.4722222222222223vw);
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.7777777777777777vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.430555555555556vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 8.333333333333332vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 7.638888888888889vw);
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.944444444444445vw);
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.944444444444445vw);
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 5.555555555555555vw);
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.861111111111112vw);
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.166666666666666vw);
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, 7.916666666666666vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, 5.833333333333333vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, 4.444444444444445vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, 2.361111111111111vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 6.944444444444445vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 5.555555555555555vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-3 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 2.083333333333333vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.7361111111111112vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.3888888888888888vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  margin-left: auto;
  border: transparent;
  letter-spacing: .32px!important;
  line-height: 1;
}

.emotion-4:focus {
  outline: none;
}

.emotion-4:hover,
.emotion-4:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-4:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-4 button {
  letter-spacing: .32px!important;
}

.emotion-4 a {
  line-height: 1;
  letter-spacing: .32px!important;
}

.emotion-4 a span {
  letter-spacing: .32px!important;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 30px;
  grid-template-columns: repeat(10, calc((100vw - 60px - 3vw) / 2.4));
  overflow-x: scroll;
  scrollbar-width: none;
  margin: 0;
  padding: 0;
  -ms-overflow-style: none;
  padding-top: 15px;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  width: calc(100vw - 60px);
}

.emotion-5::-webkit-scrollbar {
  display: none;
}

.emotion-6 {
  width: 21.35vw;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  gap: 15px;
  margin-right: 0;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-7 {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
}

.emotion-8 {
  -webkit-background-size: cover;
  background-size: cover;
  margin-right: 0;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  width: 21.35vw;
}

.emotion-9 {
  width: 100%;
  aspect-ratio: 3/4;
  object-fit: cover;
}

.emotion-10 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 335px;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  padding-bottom: 1.8px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 1.8px;
  text-decoration-thickness: 2px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-10:focus {
  outline: none;
}

.emotion-10:hover,
.emotion-10:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-10:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-11 {
  box-sizing: border-box;
}

.emotion-16 {
  color: #000000;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  z-index: 2;
}

.emotion-16>span {
  padding-right: 5px;
}

.emotion-16>div {
  -webkit-transition: all 100ms ease-in-out;
  transition: all 100ms ease-in-out;
}

.emotion-16:hover>div {
  -webkit-transform: translate(5px,0);
  -moz-transform: translate(5px,0);
  -ms-transform: translate(5px,0);
  transform: translate(5px,0);
}

.emotion-17 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-17 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-17 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-17 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-17 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-17 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-17 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-17 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 70px;
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 114px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-17 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-17 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-17 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-17 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      height="0"
      width="0"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-3"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--headline-1"
                >
                  wayfinding rich text 1
                </span>
              </p>
            </div>
          </div>
          <a
            class="emotion-4"
            color="dark"
            href="https://www.gap.com"
          >
            Shop all
          </a>
        </div>
        <div
          class="emotion-5"
        >
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red1"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-10"
              color="dark"
              href="#tops"
            >
              <span
                class="emotion-11"
              >
                Tops
              </span>
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="athleta face mask covid"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_dp_accessories_grid_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-16"
              href="#leggings"
            >
              <span>
                Leggings
              </span>
              <div
                data-testid="animated-arrow"
              >
                <svg
                  fill="none"
                  height="8"
                  viewBox="0 0 21 8"
                  width="21"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20.354 4.35457C20.4477 4.26081 20.5004 4.13366 20.5004 4.00107C20.5004 3.86849 20.4477 3.74134 20.354 3.64757L17.172 0.464575C17.0777 0.373496 16.9514 0.323098 16.8203 0.324238C16.6892 0.325377 16.5638 0.377961 16.4711 0.470665C16.3784 0.563369 16.3258 0.688776 16.3247 0.819874C16.3235 0.950972 16.3739 1.07727 16.465 1.17157L19.293 4.00057L16.465 6.82857C16.3739 6.92288 16.3235 7.04918 16.3247 7.18027C16.3258 7.31137 16.3784 7.43678 16.4711 7.52948C16.5638 7.62219 16.6892 7.67477 16.8203 7.67591C16.9514 7.67705 17.0777 7.62665 17.172 7.53557L20.354 4.35457ZM0 4.50057H20V3.50057H0V4.50057Z"
                    fill="#000000"
                  />
                </svg>
              </div>
            </a>
            <div
              class="emotion-17"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Inclusive sizes
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="girl in pink skating"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/SU22_D2_ATG_DressesRompers_S?fmt=webp"
              />
            </div>
            <div
              class="emotion-17"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    New items
                  </span>
                </p>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-3"
                  >
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cras volutpat, risus in mattis ultrices, libero mauris blandit odio.
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="athleta mask pink"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_dp_accessories_grid_jpg?fmt=webp"
              />
            </div>
            <div
              class="emotion-17"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Great prices
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red2"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-16"
              href="#tops"
            >
              <span>
                Tops
              </span>
              <div
                data-testid="animated-arrow"
              >
                <svg
                  fill="none"
                  height="8"
                  viewBox="0 0 21 8"
                  width="21"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20.354 4.35457C20.4477 4.26081 20.5004 4.13366 20.5004 4.00107C20.5004 3.86849 20.4477 3.74134 20.354 3.64757L17.172 0.464575C17.0777 0.373496 16.9514 0.323098 16.8203 0.324238C16.6892 0.325377 16.5638 0.377961 16.4711 0.470665C16.3784 0.563369 16.3258 0.688776 16.3247 0.819874C16.3235 0.950972 16.3739 1.07727 16.465 1.17157L19.293 4.00057L16.465 6.82857C16.3739 6.92288 16.3235 7.04918 16.3247 7.18027C16.3258 7.31137 16.3784 7.43678 16.4711 7.52948C16.5638 7.62219 16.6892 7.67477 16.8203 7.67591C16.9514 7.67705 17.0777 7.62665 17.172 7.53557L20.354 4.35457ZM0 4.50057H20V3.50057H0V4.50057Z"
                    fill="#000000"
                  />
                </svg>
              </div>
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red3"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-16"
              href="#tops"
            >
              <span>
                Tops
              </span>
              <div
                data-testid="animated-arrow"
              >
                <svg
                  fill="none"
                  height="8"
                  viewBox="0 0 21 8"
                  width="21"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20.354 4.35457C20.4477 4.26081 20.5004 4.13366 20.5004 4.00107C20.5004 3.86849 20.4477 3.74134 20.354 3.64757L17.172 0.464575C17.0777 0.373496 16.9514 0.323098 16.8203 0.324238C16.6892 0.325377 16.5638 0.377961 16.4711 0.470665C16.3784 0.563369 16.3258 0.688776 16.3247 0.819874C16.3235 0.950972 16.3739 1.07727 16.465 1.17157L19.293 4.00057L16.465 6.82857C16.3739 6.92288 16.3235 7.04918 16.3247 7.18027C16.3258 7.31137 16.3784 7.43678 16.4711 7.52948C16.5638 7.62219 16.6892 7.67477 16.8203 7.67591C16.9514 7.67705 17.0777 7.62665 17.172 7.53557L20.354 4.35457ZM0 4.50057H20V3.50057H0V4.50057Z"
                    fill="#000000"
                  />
                </svg>
              </div>
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="athleta face mask covid"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_dp_accessories_grid_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-16"
              href="#leggings"
            >
              <span>
                Leggings
              </span>
              <div
                data-testid="animated-arrow"
              >
                <svg
                  fill="none"
                  height="8"
                  viewBox="0 0 21 8"
                  width="21"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20.354 4.35457C20.4477 4.26081 20.5004 4.13366 20.5004 4.00107C20.5004 3.86849 20.4477 3.74134 20.354 3.64757L17.172 0.464575C17.0777 0.373496 16.9514 0.323098 16.8203 0.324238C16.6892 0.325377 16.5638 0.377961 16.4711 0.470665C16.3784 0.563369 16.3258 0.688776 16.3247 0.819874C16.3235 0.950972 16.3739 1.07727 16.465 1.17157L19.293 4.00057L16.465 6.82857C16.3739 6.92288 16.3235 7.04918 16.3247 7.18027C16.3258 7.31137 16.3784 7.43678 16.4711 7.52948C16.5638 7.62219 16.6892 7.67477 16.8203 7.67591C16.9514 7.67705 17.0777 7.62665 17.172 7.53557L20.354 4.35457ZM0 4.50057H20V3.50057H0V4.50057Z"
                    fill="#000000"
                  />
                </svg>
              </div>
            </a>
            <div
              class="emotion-17"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Inclusive sizes
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="girl in pink skating"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/SU22_D2_ATG_DressesRompers_S?fmt=webp"
              />
            </div>
            <div
              class="emotion-17"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    New items
                  </span>
                </p>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-3"
                  >
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cras volutpat, risus in mattis ultrices, libero mauris blandit odio.
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="athleta mask pink"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_dp_accessories_grid_jpg?fmt=webp"
              />
            </div>
            <div
              class="emotion-17"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Great prices
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red4"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-16"
              href="#tops"
            >
              <span>
                Tops
              </span>
              <div
                data-testid="animated-arrow"
              >
                <svg
                  fill="none"
                  height="8"
                  viewBox="0 0 21 8"
                  width="21"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20.354 4.35457C20.4477 4.26081 20.5004 4.13366 20.5004 4.00107C20.5004 3.86849 20.4477 3.74134 20.354 3.64757L17.172 0.464575C17.0777 0.373496 16.9514 0.323098 16.8203 0.324238C16.6892 0.325377 16.5638 0.377961 16.4711 0.470665C16.3784 0.563369 16.3258 0.688776 16.3247 0.819874C16.3235 0.950972 16.3739 1.07727 16.465 1.17157L19.293 4.00057L16.465 6.82857C16.3739 6.92288 16.3235 7.04918 16.3247 7.18027C16.3258 7.31137 16.3784 7.43678 16.4711 7.52948C16.5638 7.62219 16.6892 7.67477 16.8203 7.67591C16.9514 7.67705 17.0777 7.62665 17.172 7.53557L20.354 4.35457ZM0 4.50057H20V3.50057H0V4.50057Z"
                    fill="#000000"
                  />
                </svg>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`WayfindingAndProductCards Desktop uses carousel layout when numberOfCardsToExpose is "6" with partial 7th card and there are 7+ cards 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #4CA1AF;
}

.emotion-1 {
  padding: 60px 0 60px 60px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 1440px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  margin-bottom: 1.04vw;
  -webkit-column-gap: 15px;
  column-gap: 15px;
  max-width: calc(100% - 10px);
}

.emotion-3 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-3 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-3 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-3 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-3 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-3 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-3 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.3888888888888888vw);
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.25vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.1111111111111112vw);
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.9722222222222222vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 0.8333333333333334vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 3.4722222222222223vw);
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.7777777777777777vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, 2.430555555555556vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 8.333333333333332vw);
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 7.638888888888889vw);
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.944444444444445vw);
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 6.944444444444445vw);
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 5.555555555555555vw);
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.861111111111112vw);
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 4.166666666666666vw);
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, 7.916666666666666vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, 5.833333333333333vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, 4.444444444444445vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, 2.361111111111111vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 6.944444444444445vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 5.555555555555555vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-3 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 2.083333333333333vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.7361111111111112vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, 1.3888888888888888vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  margin-left: auto;
  border: transparent;
  letter-spacing: .32px!important;
  line-height: 1;
}

.emotion-4:focus {
  outline: none;
}

.emotion-4:hover,
.emotion-4:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-4:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-4 button {
  letter-spacing: .32px!important;
}

.emotion-4 a {
  line-height: 1;
  letter-spacing: .32px!important;
}

.emotion-4 a span {
  letter-spacing: .32px!important;
}

.emotion-5 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 30px;
  grid-template-columns: repeat(10, calc((100vw - 60px - 3vw) / 2.4));
  overflow-x: scroll;
  scrollbar-width: none;
  margin: 0;
  padding: 0;
  -ms-overflow-style: none;
  padding-top: 15px;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  width: calc(100vw - 60px);
}

.emotion-5::-webkit-scrollbar {
  display: none;
}

.emotion-6 {
  width: 13.54vw;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  gap: 15px;
  margin-right: 0;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
}

.emotion-7 {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
}

.emotion-8 {
  -webkit-background-size: cover;
  background-size: cover;
  margin-right: 0;
  -webkit-align-self: stretch;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  width: 13.54vw;
}

.emotion-9 {
  width: 100%;
  aspect-ratio: 3/4;
  object-fit: cover;
}

.emotion-10 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 335px;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  padding-bottom: 1.8px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 1.8px;
  text-decoration-thickness: 2px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-10:focus {
  outline: none;
}

.emotion-10:hover,
.emotion-10:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-10:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-11 {
  box-sizing: border-box;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      height="0"
      width="0"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-3"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--headline-1"
                >
                  wayfinding rich text 1
                </span>
              </p>
            </div>
          </div>
          <a
            class="emotion-4"
            color="dark"
            href="https://www.gap.com"
          >
            Shop all
          </a>
        </div>
        <div
          class="emotion-5"
        >
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red1"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-10"
              color="dark"
              href="#tops"
            >
              <span
                class="emotion-11"
              >
                Tops
              </span>
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red1"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-10"
              color="dark"
              href="#tops"
            >
              <span
                class="emotion-11"
              >
                Tops
              </span>
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red1"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-10"
              color="dark"
              href="#tops"
            >
              <span
                class="emotion-11"
              >
                Tops
              </span>
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red1"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-10"
              color="dark"
              href="#tops"
            >
              <span
                class="emotion-11"
              >
                Tops
              </span>
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red1"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-10"
              color="dark"
              href="#tops"
            >
              <span
                class="emotion-11"
              >
                Tops
              </span>
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red1"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-10"
              color="dark"
              href="#tops"
            >
              <span
                class="emotion-11"
              >
                Tops
              </span>
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red1"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-10"
              color="dark"
              href="#tops"
            >
              <span
                class="emotion-11"
              >
                Tops
              </span>
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red1"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-10"
              color="dark"
              href="#tops"
            >
              <span
                class="emotion-11"
              >
                Tops
              </span>
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red1"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-10"
              color="dark"
              href="#tops"
            >
              <span
                class="emotion-11"
              >
                Tops
              </span>
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red1"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-10"
              color="dark"
              href="#tops"
            >
              <span
                class="emotion-11"
              >
                Tops
              </span>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`WayfindingAndProductCards Mobile matches snapshot when it is exposed 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #FD746C;
}

.emotion-1 {
  padding: 7vw 3vw;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  margin-bottom: calc(8px + 2vw);
  -webkit-column-gap: 20px;
  column-gap: 20px;
}

.emotion-3 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-3 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-3 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-3 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-3 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-3 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-3 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-3 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.266666666666667vw);
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.733333333333334vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.2vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 8vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 6.933333333333333vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 6.4vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 16vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 14.666666666666666vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 14.666666666666666vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 13.333333333333334vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 12vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 9.333333333333334vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(13px, 17.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(13px, 14.399999999999999vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(13px, 9.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(13px, 6.4vw);
  line-height: 1.4166666666666667;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 16vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-3 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.866666666666666vw);
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.333333333333334vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 4.8vw);
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 14px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  margin-left: auto;
  border: transparent;
  letter-spacing: .28px!important;
  line-height: 1;
}

.emotion-4:focus {
  outline: none;
}

.emotion-4:hover,
.emotion-4:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-4:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-4 button {
  letter-spacing: .28px!important;
}

.emotion-4 a {
  line-height: 1;
  letter-spacing: .28px!important;
}

.emotion-4 a span {
  letter-spacing: .28px!important;
}

.emotion-5 {
  display: grid;
  gap: 30px;
  grid-template-columns: 1fr 1fr;
}

.emotion-6 {
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  gap: 15px;
}

.emotion-7 {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
}

.emotion-8 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-9 {
  width: 100%;
  aspect-ratio: 3/4;
  object-fit: cover;
}

.emotion-10 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 14px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-10:focus {
  outline: none;
}

.emotion-10:hover,
.emotion-10:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-10:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-15 {
  color: #000000;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  z-index: 2;
}

.emotion-15>span {
  padding-right: 5px;
}

.emotion-16 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-16 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-16 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-16 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-16 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-16 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-16 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-16 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 45px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-16 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-16 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-16 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-16 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-16 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-16 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-16 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-16 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-16 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-16 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      height="0"
      width="0"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-3"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--headline-1"
                >
                  mobile text override
                </span>
              </p>
            </div>
          </div>
          <a
            class="emotion-4"
            color="dark"
            href="https://www.gap.com"
          >
            Shop all
          </a>
        </div>
        <div
          class="emotion-5"
        >
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red1"
                class="emotion-9"
                src=""
              />
            </div>
            <a
              class="emotion-10"
              color="dark"
              href="#tops"
            >
              Tops
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="athleta face mask covid"
                class="emotion-9"
                src=""
              />
            </div>
            <a
              class="emotion-15"
              href="#leggings"
            >
              <span>
                Leggings
              </span>
              <div
                data-testid="animated-arrow"
              >
                <svg
                  fill="none"
                  height="8"
                  viewBox="0 0 21 8"
                  width="21"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20.354 4.35457C20.4477 4.26081 20.5004 4.13366 20.5004 4.00107C20.5004 3.86849 20.4477 3.74134 20.354 3.64757L17.172 0.464575C17.0777 0.373496 16.9514 0.323098 16.8203 0.324238C16.6892 0.325377 16.5638 0.377961 16.4711 0.470665C16.3784 0.563369 16.3258 0.688776 16.3247 0.819874C16.3235 0.950972 16.3739 1.07727 16.465 1.17157L19.293 4.00057L16.465 6.82857C16.3739 6.92288 16.3235 7.04918 16.3247 7.18027C16.3258 7.31137 16.3784 7.43678 16.4711 7.52948C16.5638 7.62219 16.6892 7.67477 16.8203 7.67591C16.9514 7.67705 17.0777 7.62665 17.172 7.53557L20.354 4.35457ZM0 4.50057H20V3.50057H0V4.50057Z"
                    fill="#000000"
                  />
                </svg>
              </div>
            </a>
            <div
              class="emotion-16"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Inclusive sizes
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="girl in pink skating"
                class="emotion-9"
                src=""
              />
            </div>
            <div
              class="emotion-16"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    New items
                  </span>
                </p>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-3"
                  >
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cras volutpat, risus in mattis ultrices, libero mauris blandit odio.
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="athleta mask pink"
                class="emotion-9"
                src=""
              />
            </div>
            <div
              class="emotion-16"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Great prices
                  </span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`WayfindingAndProductCards Mobile matches snapshot when it is slider 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #FFA;
}

.emotion-1 {
  padding: 7vw 3vw;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  margin-bottom: calc(8px + 2vw);
  -webkit-column-gap: 20px;
  column-gap: 20px;
}

.emotion-3 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-3 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-3 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-3 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-3 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-3 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-3 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-3 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.266666666666667vw);
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.733333333333334vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.2vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 8vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 6.933333333333333vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 6.4vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 16vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 14.666666666666666vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 14.666666666666666vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 13.333333333333334vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 12vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 9.333333333333334vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(13px, 17.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(13px, 14.399999999999999vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(13px, 9.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(13px, 6.4vw);
  line-height: 1.4166666666666667;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 16vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-3 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.866666666666666vw);
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.333333333333334vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 4.8vw);
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 14px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  margin-left: auto;
  border: transparent;
  letter-spacing: .28px!important;
  line-height: 1;
}

.emotion-4:focus {
  outline: none;
}

.emotion-4:hover,
.emotion-4:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-4:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-4 button {
  letter-spacing: .28px!important;
}

.emotion-4 a {
  line-height: 1;
  letter-spacing: .28px!important;
}

.emotion-4 a span {
  letter-spacing: .28px!important;
}

.emotion-5 {
  display: grid;
  gap: 30px;
  grid-template-columns: repeat(10, calc((100vw - 60px - 3vw) / 2.4));
  overflow-x: scroll;
  scrollbar-width: none;
  margin: 0px -10px;
  padding: 0px 10px;
  -ms-overflow-style: none;
}

.emotion-5::-webkit-scrollbar {
  display: none;
}

.emotion-6 {
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  gap: 15px;
}

.emotion-7 {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
}

.emotion-8 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-9 {
  width: 100%;
  aspect-ratio: 3/4;
  object-fit: cover;
}

.emotion-10 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  padding-bottom: 1.8px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 1.8px;
  text-decoration-thickness: 2px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-10:focus {
  outline: none;
}

.emotion-10:hover,
.emotion-10:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-10:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-11 {
  box-sizing: border-box;
}

.emotion-16 {
  color: #000000;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  z-index: 2;
}

.emotion-16>span {
  padding-right: 5px;
}

.emotion-17 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-17 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-17 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-17 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-17 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-17 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-17 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-17 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 45px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-17 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-17 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-17 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-17 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-17 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-17 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      height="0"
      width="0"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-3"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--headline-1"
                >
                  mobile text override
                </span>
              </p>
            </div>
          </div>
          <a
            class="emotion-4"
            color="dark"
            href="https://www.gap.com"
          >
            Shop all
          </a>
        </div>
        <div
          class="emotion-5"
        >
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red1"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-10"
              color="dark"
              href="#tops"
            >
              <span
                class="emotion-11"
              >
                Tops
              </span>
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="athleta face mask covid"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_dp_accessories_grid_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-16"
              href="#leggings"
            >
              <span>
                Leggings
              </span>
              <div
                data-testid="animated-arrow"
              >
                <svg
                  fill="none"
                  height="8"
                  viewBox="0 0 21 8"
                  width="21"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20.354 4.35457C20.4477 4.26081 20.5004 4.13366 20.5004 4.00107C20.5004 3.86849 20.4477 3.74134 20.354 3.64757L17.172 0.464575C17.0777 0.373496 16.9514 0.323098 16.8203 0.324238C16.6892 0.325377 16.5638 0.377961 16.4711 0.470665C16.3784 0.563369 16.3258 0.688776 16.3247 0.819874C16.3235 0.950972 16.3739 1.07727 16.465 1.17157L19.293 4.00057L16.465 6.82857C16.3739 6.92288 16.3235 7.04918 16.3247 7.18027C16.3258 7.31137 16.3784 7.43678 16.4711 7.52948C16.5638 7.62219 16.6892 7.67477 16.8203 7.67591C16.9514 7.67705 17.0777 7.62665 17.172 7.53557L20.354 4.35457ZM0 4.50057H20V3.50057H0V4.50057Z"
                    fill="#000000"
                  />
                </svg>
              </div>
            </a>
            <div
              class="emotion-17"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Inclusive sizes
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="girl in pink skating"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/SU22_D2_ATG_DressesRompers_S?fmt=webp"
              />
            </div>
            <div
              class="emotion-17"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    New items
                  </span>
                </p>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-3"
                  >
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cras volutpat, risus in mattis ultrices, libero mauris blandit odio.
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="athleta mask pink"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_dp_accessories_grid_jpg?fmt=webp"
              />
            </div>
            <div
              class="emotion-17"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Great prices
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red2"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-16"
              href="#tops"
            >
              <span>
                Tops
              </span>
              <div
                data-testid="animated-arrow"
              >
                <svg
                  fill="none"
                  height="8"
                  viewBox="0 0 21 8"
                  width="21"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20.354 4.35457C20.4477 4.26081 20.5004 4.13366 20.5004 4.00107C20.5004 3.86849 20.4477 3.74134 20.354 3.64757L17.172 0.464575C17.0777 0.373496 16.9514 0.323098 16.8203 0.324238C16.6892 0.325377 16.5638 0.377961 16.4711 0.470665C16.3784 0.563369 16.3258 0.688776 16.3247 0.819874C16.3235 0.950972 16.3739 1.07727 16.465 1.17157L19.293 4.00057L16.465 6.82857C16.3739 6.92288 16.3235 7.04918 16.3247 7.18027C16.3258 7.31137 16.3784 7.43678 16.4711 7.52948C16.5638 7.62219 16.6892 7.67477 16.8203 7.67591C16.9514 7.67705 17.0777 7.62665 17.172 7.53557L20.354 4.35457ZM0 4.50057H20V3.50057H0V4.50057Z"
                    fill="#000000"
                  />
                </svg>
              </div>
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red3"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-16"
              href="#tops"
            >
              <span>
                Tops
              </span>
              <div
                data-testid="animated-arrow"
              >
                <svg
                  fill="none"
                  height="8"
                  viewBox="0 0 21 8"
                  width="21"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20.354 4.35457C20.4477 4.26081 20.5004 4.13366 20.5004 4.00107C20.5004 3.86849 20.4477 3.74134 20.354 3.64757L17.172 0.464575C17.0777 0.373496 16.9514 0.323098 16.8203 0.324238C16.6892 0.325377 16.5638 0.377961 16.4711 0.470665C16.3784 0.563369 16.3258 0.688776 16.3247 0.819874C16.3235 0.950972 16.3739 1.07727 16.465 1.17157L19.293 4.00057L16.465 6.82857C16.3739 6.92288 16.3235 7.04918 16.3247 7.18027C16.3258 7.31137 16.3784 7.43678 16.4711 7.52948C16.5638 7.62219 16.6892 7.67477 16.8203 7.67591C16.9514 7.67705 17.0777 7.62665 17.172 7.53557L20.354 4.35457ZM0 4.50057H20V3.50057H0V4.50057Z"
                    fill="#000000"
                  />
                </svg>
              </div>
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="athleta face mask covid"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_dp_accessories_grid_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-16"
              href="#leggings"
            >
              <span>
                Leggings
              </span>
              <div
                data-testid="animated-arrow"
              >
                <svg
                  fill="none"
                  height="8"
                  viewBox="0 0 21 8"
                  width="21"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20.354 4.35457C20.4477 4.26081 20.5004 4.13366 20.5004 4.00107C20.5004 3.86849 20.4477 3.74134 20.354 3.64757L17.172 0.464575C17.0777 0.373496 16.9514 0.323098 16.8203 0.324238C16.6892 0.325377 16.5638 0.377961 16.4711 0.470665C16.3784 0.563369 16.3258 0.688776 16.3247 0.819874C16.3235 0.950972 16.3739 1.07727 16.465 1.17157L19.293 4.00057L16.465 6.82857C16.3739 6.92288 16.3235 7.04918 16.3247 7.18027C16.3258 7.31137 16.3784 7.43678 16.4711 7.52948C16.5638 7.62219 16.6892 7.67477 16.8203 7.67591C16.9514 7.67705 17.0777 7.62665 17.172 7.53557L20.354 4.35457ZM0 4.50057H20V3.50057H0V4.50057Z"
                    fill="#000000"
                  />
                </svg>
              </div>
            </a>
            <div
              class="emotion-17"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Inclusive sizes
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="girl in pink skating"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/SU22_D2_ATG_DressesRompers_S?fmt=webp"
              />
            </div>
            <div
              class="emotion-17"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    New items
                  </span>
                </p>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-3"
                  >
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cras volutpat, risus in mattis ultrices, libero mauris blandit odio.
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="athleta mask pink"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_dp_accessories_grid_jpg?fmt=webp"
              />
            </div>
            <div
              class="emotion-17"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Great prices
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red4"
                class="emotion-9"
                src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/stage_auto_1__Asset_Archive_ATWeb_content_0028_043_968_assets_1022_APP_TCS_BRAS_jpg?fmt=webp"
              />
            </div>
            <a
              class="emotion-16"
              href="#tops"
            >
              <span>
                Tops
              </span>
              <div
                data-testid="animated-arrow"
              >
                <svg
                  fill="none"
                  height="8"
                  viewBox="0 0 21 8"
                  width="21"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20.354 4.35457C20.4477 4.26081 20.5004 4.13366 20.5004 4.00107C20.5004 3.86849 20.4477 3.74134 20.354 3.64757L17.172 0.464575C17.0777 0.373496 16.9514 0.323098 16.8203 0.324238C16.6892 0.325377 16.5638 0.377961 16.4711 0.470665C16.3784 0.563369 16.3258 0.688776 16.3247 0.819874C16.3235 0.950972 16.3739 1.07727 16.465 1.17157L19.293 4.00057L16.465 6.82857C16.3739 6.92288 16.3235 7.04918 16.3247 7.18027C16.3258 7.31137 16.3784 7.43678 16.4711 7.52948C16.5638 7.62219 16.6892 7.67477 16.8203 7.67591C16.9514 7.67705 17.0777 7.62665 17.172 7.53557L20.354 4.35457ZM0 4.50057H20V3.50057H0V4.50057Z"
                    fill="#000000"
                  />
                </svg>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`WayfindingAndProductCards Mobile renders with exposed layout on mobile when mobileLayout is "exposed" 1`] = `
<DocumentFragment>
  .emotion-0 {
  background: #FD746C;
}

.emotion-1 {
  padding: 7vw 3vw;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  margin-bottom: calc(8px + 2vw);
  -webkit-column-gap: 20px;
  column-gap: 20px;
}

.emotion-3 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-3 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-3 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-3 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-3 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-3 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-3 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-3 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 2.933333333333333vw;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.8vw);
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 4.266666666666667vw);
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.733333333333334vw);
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 3.2vw);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 2.666666666666667vw);
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 8vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 6.933333333333333vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, 6.4vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 16vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 14.666666666666666vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 14.666666666666666vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 13.333333333333334vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 12vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 9.333333333333334vw);
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(13px, 17.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(13px, 14.399999999999999vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(13px, 9.066666666666666vw);
  line-height: 1;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(13px, 6.4vw);
  line-height: 1.4166666666666667;
  letter-spacing: -0.07999999999999999vw;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 16vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, 10.666666666666668vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 3.733333333333334vw;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-3 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.866666666666666vw);
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 5.333333333333334vw);
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, 4.8vw);
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-4 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 14px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  margin-left: auto;
  border: transparent;
  letter-spacing: .28px!important;
  line-height: 1;
}

.emotion-4:focus {
  outline: none;
}

.emotion-4:hover,
.emotion-4:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-4:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-4 button {
  letter-spacing: .28px!important;
}

.emotion-4 a {
  line-height: 1;
  letter-spacing: .28px!important;
}

.emotion-4 a span {
  letter-spacing: .28px!important;
}

.emotion-5 {
  display: grid;
  gap: 30px;
  grid-template-columns: 1fr 1fr;
}

.emotion-6 {
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  gap: 15px;
}

.emotion-7 {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
}

.emotion-8 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-9 {
  width: 100%;
  aspect-ratio: 3/4;
  object-fit: cover;
}

.emotion-10 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 14px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-10:focus {
  outline: none;
}

.emotion-10:hover,
.emotion-10:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-10:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-15 {
  color: #000000;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  z-index: 2;
}

.emotion-15>span {
  padding-right: 5px;
}

.emotion-16 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-16 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-16 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-16 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-16 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-16 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-16 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-16 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 45px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-16 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-16 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-16 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-16 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-16 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-16 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-16 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-16 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-16 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-16 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-16 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      height="0"
      width="0"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-3"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--headline-1"
                >
                  mobile text override
                </span>
              </p>
            </div>
          </div>
          <a
            class="emotion-4"
            color="dark"
            href="https://www.gap.com"
          >
            Shop all
          </a>
        </div>
        <div
          class="emotion-5"
        >
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="lady in red1"
                class="emotion-9"
                src=""
              />
            </div>
            <a
              class="emotion-10"
              color="dark"
              href="#tops"
            >
              Tops
            </a>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="athleta face mask covid"
                class="emotion-9"
                src=""
              />
            </div>
            <a
              class="emotion-15"
              href="#leggings"
            >
              <span>
                Leggings
              </span>
              <div
                data-testid="animated-arrow"
              >
                <svg
                  fill="none"
                  height="8"
                  viewBox="0 0 21 8"
                  width="21"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20.354 4.35457C20.4477 4.26081 20.5004 4.13366 20.5004 4.00107C20.5004 3.86849 20.4477 3.74134 20.354 3.64757L17.172 0.464575C17.0777 0.373496 16.9514 0.323098 16.8203 0.324238C16.6892 0.325377 16.5638 0.377961 16.4711 0.470665C16.3784 0.563369 16.3258 0.688776 16.3247 0.819874C16.3235 0.950972 16.3739 1.07727 16.465 1.17157L19.293 4.00057L16.465 6.82857C16.3739 6.92288 16.3235 7.04918 16.3247 7.18027C16.3258 7.31137 16.3784 7.43678 16.4711 7.52948C16.5638 7.62219 16.6892 7.67477 16.8203 7.67591C16.9514 7.67705 17.0777 7.62665 17.172 7.53557L20.354 4.35457ZM0 4.50057H20V3.50057H0V4.50057Z"
                    fill="#000000"
                  />
                </svg>
              </div>
            </a>
            <div
              class="emotion-16"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Inclusive sizes
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="girl in pink skating"
                class="emotion-9"
                src=""
              />
            </div>
            <div
              class="emotion-16"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    New items
                  </span>
                </p>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-3"
                  >
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cras volutpat, risus in mattis ultrices, libero mauris blandit odio.
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            class="emotion-6"
          >
            <a
              aria-label="image label"
              class="emotion-7"
              href="#"
            />
            <div
              class="emotion-8"
              data-testid="product-card-image"
            >
              <img
                alt="athleta mask pink"
                class="emotion-9"
                src=""
              />
            </div>
            <div
              class="emotion-16"
            >
              <div>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                  >
                    Great prices
                  </span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;
