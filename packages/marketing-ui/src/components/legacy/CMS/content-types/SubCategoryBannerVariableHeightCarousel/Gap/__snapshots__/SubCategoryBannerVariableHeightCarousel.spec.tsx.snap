// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SubCategoryBannerVHCarousel Pagination should render pagination on the right when desktop 1`] = `
<DocumentFragment>
  .emotion-0 ul.slick-dots {
  margin-block-start: 0;
  margin-block-end: 0;
  -webkit-padding-start: 0;
  padding-inline-start: 0;
  text-align: right;
  z-index: 2;
  width: 80%;
  right: 0;
  margin-right: 20px;
  margin-bottom: 20px;
  bottom: 0;
  top: inherit;
}

.emotion-0 ul.slick-dots li {
  width: auto;
  height: auto;
  margin: 0;
}

.emotion-0 ul.slick-dots li:not(li:first-child) {
  margin-left: 10px;
}

.emotion-0 ul.slick-dots li button:before {
  color: #2B2B2B;
  width: 10px;
  height: 10px;
  opacity: 1;
  border: 1px solid #2B2B2B;
  border-radius: 50%;
  content: "";
}

.emotion-0 ul.slick-dots li.slick-active button:before {
  color: #2B2B2B;
  background-color: #2B2B2B;
}

.emotion-0 button.pausePlayGapBtn {
  bottom: 0;
  top: inherit;
}

.emotion-1 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-2 {
  position: relative;
}

.emotion-3 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 .slick-list {
  overflow: hidden;
}

.emotion-3 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-3 button.slick-next.slick-arrow.slick-next,
.emotion-3 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: min(13.750000000000002vw, 50%);
  height: 44px;
  width: 44px;
}

.emotion-3 button.slick-next.slick-arrow.slick-next>span,
.emotion-3 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-3 button.slick-next.slick-arrow.slick-next svg,
.emotion-3 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-3 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-3 .slick-disabled {
  display: none!important;
}

.emotion-3 .slick-next {
  left: calc(100% - 44px);
}

.emotion-3 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-3 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-4 {
  position: relative;
}

.emotion-4 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-4 .slick-slider .slick-track,
.emotion-4 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-4 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-4 .slick-list:focus {
  outline: none;
}

.emotion-4 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-4 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-4 .slick-track:before,
.emotion-4 .slick-track:after {
  display: table;
  content: "";
}

.emotion-4 .slick-track:after {
  clear: both;
}

.emotion-4 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-4 .slick-slide img {
  display: block;
}

.emotion-4 .slick-slide.slick-loading img {
  display: none;
}

.emotion-4 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-4 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-4 .slick-initialized .slick-slide,
.emotion-4 .slick-vertical .slick-slide {
  display: block;
}

.emotion-4 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-4 .slick-loading .slick-track,
.emotion-4 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-4 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-4 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-4 .slick-prev,
.emotion-4 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-prev:hover,
.emotion-4 .slick-next:hover,
.emotion-4 .slick-prev:focus,
.emotion-4 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-4 .slick-prev.slick-disabled,
.emotion-4 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-4 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-4 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-4 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-4 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-4 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-4 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-4 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-4 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-4 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-dots li button:hover,
.emotion-4 .slick-dots li button:focus {
  outline: none;
}

.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before,
.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-4 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-5 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-7 {
  max-height: 275px;
  overflow: hidden;
}

.emotion-8 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1280/275;
}

.emotion-9 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-10 {
  width: 100%;
  aspect-ratio: 1280/275;
  object-fit: cover;
}

.emotion-11 {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

.emotion-12 {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  position: absolute;
  height: 100%;
}

.emotion-13 {
  width: 100%;
  height: 100%;
  padding: 50px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: 1fr max-content 1fr;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  row-gap: 24px;
}

.emotion-13 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-13 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 3;
  text-align: center;
}

.emotion-14 {
  box-sizing: content-box;
  text-align: center;
}

.emotion-15 {
  text-align: center;
}

.emotion-15 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-15 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-15 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-15 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-15 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-15 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-15 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 70px;
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 114px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-15 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-16 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  text-align: center;
}

.emotion-17 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 12px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-17:focus {
  outline: none;
}

.emotion-17:hover,
.emotion-17:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-17:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-24 {
  width: 100%;
  height: 100%;
  padding: 50px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  row-gap: 24px;
}

.emotion-24 .rteWrapperDivs {
  grid-column: 1;
  grid-row: 1;
}

.emotion-24 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 2;
  text-align: center;
}

.emotion-25 {
  box-sizing: content-box;
  text-align: start;
}

.emotion-33 {
  width: 100%;
  height: 100%;
  padding: 50px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  row-gap: 24px;
}

.emotion-33 .rteWrapperDivs {
  grid-column: 3;
  grid-row: 2;
}

.emotion-33 .ctaWrapperDivs {
  grid-column: 3;
  grid-row: 2;
  text-align: center;
}

.emotion-34 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  text-align: right;
}

.emotion-35 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  padding-bottom: 1.5px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 1.5px;
  text-decoration-thickness: 1.6px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-35:focus {
  outline: none;
}

.emotion-35:hover,
.emotion-35:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-35:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-36 {
  box-sizing: border-box;
}

.emotion-43 {
  width: 100%;
  height: 100%;
  padding: 50px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: max-content max-content 1fr;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  row-gap: 24px;
}

.emotion-43 .rteWrapperDivs {
  grid-column: 3;
  grid-row: 1;
}

.emotion-43 .ctaWrapperDivs {
  grid-column: 3;
  grid-row: 2;
  text-align: center;
}

.emotion-44 {
  box-sizing: content-box;
  text-align: end;
}

.emotion-47 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 12px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-47:focus {
  outline: none;
}

.emotion-47:hover,
.emotion-47:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-47:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="emotion-1"
          data-testid="category-banner"
        >
          <div
            class="emotion-2"
          >
            <nav
              class="emotion-3"
            >
              <div
                class="emotion-4"
              >
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <button
                    aria-label="Previous"
                    class="slick-prev slick-arrow slick-prev slick-disabled"
                    data-role="none"
                    disabled=""
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-5"
                    >
                      <svg
                        viewBox="0 0 18 7.742"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                          fill="#2B2B2B"
                          transform="translate(18) rotate(90)"
                        />
                      </svg>
                    </span>
                  </button>
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="aspect-ratio-container"
                                height="275"
                                width="1280"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Female red shirt"
                                    class="emotion-10"
                                    src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/210706_26-M1837_EarlyBTS_HP_Primary_CatNav03_US_XL?fmt=webp"
                                  />
                                </div>
                              </div>
                              <a
                                class="emotion-11"
                                href="https://page.not.found"
                                tabindex="-1"
                                target="_self"
                                title="page not found"
                              />
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-13"
                              >
                                <div
                                  class="rteWrapperDivs emotion-14"
                                >
                                  <div
                                    class="emotion-15"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                        >
                                          Lorem Ipsum Dolor
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          Sit amet, consectetur adipiscing
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-16"
                                >
                                  <a
                                    class="emotion-17"
                                    color="dark"
                                    href="https://page.not.found"
                                  >
                                    Buy now
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="aspect-ratio-container"
                                height="275"
                                width="1280"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Man landed and wearing white shirt and jeans"
                                    class="emotion-10"
                                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/tshirt_mobile?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-24"
                              >
                                <div
                                  class="rteWrapperDivs emotion-25"
                                >
                                  <div
                                    class="emotion-15"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                          style="text-transform:uppercase;font-style:italic"
                                        >
                                          Lorem Ipsum Dolor
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          Sit amet, 
                                        </span>
                                        <span
                                          class="amp-cms--body-1"
                                          style="text-decoration:underline"
                                        >
                                          consectetur
                                        </span>
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                           adipiscing
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="aspect-ratio-container"
                                height="275"
                                width="1280"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Boy sitting and smiling"
                                    class="emotion-10"
                                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/G32408_Boys_MOB_anim2?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-33"
                              >
                                <div
                                  class="ctaWrapperDivs emotion-34"
                                >
                                  <a
                                    class="emotion-35"
                                    color="dark"
                                    href="https://page.not.found"
                                  >
                                    <span
                                      class="emotion-36"
                                    >
                                      New Arrivals
                                    </span>
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="3"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="aspect-ratio-container"
                                height="275"
                                width="1280"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Woman landing and wearing white shirt and black pants"
                                    class="emotion-10"
                                    src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/Test-AG-Men-Pants-4?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-43"
                              >
                                <div
                                  class="rteWrapperDivs emotion-44"
                                >
                                  <div
                                    class="emotion-15"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                          style="text-transform:uppercase"
                                        >
                                          Lorem Ipsum Dolor
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                          style="font-style:italic"
                                        >
                                          Sit amet, consectetur adipiscing
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-34"
                                >
                                  <a
                                    class="emotion-47"
                                    color="dark"
                                    href="https://page.not.found"
                                  >
                                    SHOP HERE
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    aria-label="Next"
                    class="slick-next slick-arrow slick-next"
                    data-role="none"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-5"
                    >
                      <svg
                        viewBox="0 0 18 7.742"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                          fill="#2B2B2B"
                          transform="translate(18) rotate(90)"
                        />
                      </svg>
                    </span>
                  </button>
                  <ul
                    class="slick-dots"
                    style="display: block;"
                  >
                    <li
                      class="slick-active"
                    >
                      <button>
                        1
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        2
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        3
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        4
                      </button>
                    </li>
                  </ul>
                </div>
              </div>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`SubCategoryBannerVHCarousel Pagination should render secondary color for carousel desktop 1`] = `
<DocumentFragment>
  .emotion-0 ul.slick-dots {
  margin-block-start: 0;
  margin-block-end: 0;
  -webkit-padding-start: 0;
  padding-inline-start: 0;
  text-align: right;
  z-index: 2;
  width: 80%;
  right: 0;
  margin-right: 20px;
  margin-bottom: 20px;
  bottom: 0;
  top: inherit;
}

.emotion-0 ul.slick-dots li {
  width: auto;
  height: auto;
  margin: 0;
}

.emotion-0 ul.slick-dots li:not(li:first-child) {
  margin-left: 10px;
}

.emotion-0 ul.slick-dots li button:before {
  color: #FFFFFF;
  width: 10px;
  height: 10px;
  opacity: 1;
  border: 1px solid #FFFFFF;
  border-radius: 50%;
  content: "";
}

.emotion-0 ul.slick-dots li.slick-active button:before {
  color: #FFFFFF;
  background-color: #FFFFFF;
}

.emotion-0 button.pausePlayGapBtn {
  bottom: 0;
  top: inherit;
}

.emotion-1 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-2 {
  position: relative;
}

.emotion-3 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 .slick-list {
  overflow: hidden;
}

.emotion-3 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-3 button.slick-next.slick-arrow.slick-next,
.emotion-3 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: min(13.750000000000002vw, 50%);
  height: 44px;
  width: 44px;
}

.emotion-3 button.slick-next.slick-arrow.slick-next>span,
.emotion-3 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-3 button.slick-next.slick-arrow.slick-next svg,
.emotion-3 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-3 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-3 .slick-disabled {
  display: none!important;
}

.emotion-3 .slick-next {
  left: calc(100% - 44px);
}

.emotion-3 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-3 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-4 {
  position: relative;
}

.emotion-4 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-4 .slick-slider .slick-track,
.emotion-4 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-4 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-4 .slick-list:focus {
  outline: none;
}

.emotion-4 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-4 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-4 .slick-track:before,
.emotion-4 .slick-track:after {
  display: table;
  content: "";
}

.emotion-4 .slick-track:after {
  clear: both;
}

.emotion-4 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-4 .slick-slide img {
  display: block;
}

.emotion-4 .slick-slide.slick-loading img {
  display: none;
}

.emotion-4 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-4 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-4 .slick-initialized .slick-slide,
.emotion-4 .slick-vertical .slick-slide {
  display: block;
}

.emotion-4 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-4 .slick-loading .slick-track,
.emotion-4 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-4 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-4 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-4 .slick-prev,
.emotion-4 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-prev:hover,
.emotion-4 .slick-next:hover,
.emotion-4 .slick-prev:focus,
.emotion-4 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-4 .slick-prev.slick-disabled,
.emotion-4 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-4 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-4 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-4 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-4 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-4 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-4 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-4 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-4 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-4 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-dots li button:hover,
.emotion-4 .slick-dots li button:focus {
  outline: none;
}

.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before,
.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-4 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-5 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-7 {
  max-height: 275px;
  overflow: hidden;
}

.emotion-8 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1280/275;
}

.emotion-9 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-10 {
  width: 100%;
  aspect-ratio: 1280/275;
  object-fit: cover;
}

.emotion-11 {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

.emotion-12 {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  position: absolute;
  height: 100%;
}

.emotion-13 {
  width: 100%;
  height: 100%;
  padding: 50px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: 1fr max-content 1fr;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  row-gap: 24px;
}

.emotion-13 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-13 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 3;
  text-align: center;
}

.emotion-14 {
  box-sizing: content-box;
  text-align: center;
}

.emotion-15 {
  text-align: center;
}

.emotion-15 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-15 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-15 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-15 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-15 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-15 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-15 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 70px;
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 114px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-15 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-16 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  text-align: center;
}

.emotion-17 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 12px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-17:focus {
  outline: none;
}

.emotion-17:hover,
.emotion-17:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-17:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-24 {
  width: 100%;
  height: 100%;
  padding: 50px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  row-gap: 24px;
}

.emotion-24 .rteWrapperDivs {
  grid-column: 1;
  grid-row: 1;
}

.emotion-24 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 2;
  text-align: center;
}

.emotion-25 {
  box-sizing: content-box;
  text-align: start;
}

.emotion-33 {
  width: 100%;
  height: 100%;
  padding: 50px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  row-gap: 24px;
}

.emotion-33 .rteWrapperDivs {
  grid-column: 3;
  grid-row: 2;
}

.emotion-33 .ctaWrapperDivs {
  grid-column: 3;
  grid-row: 2;
  text-align: center;
}

.emotion-34 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  text-align: right;
}

.emotion-35 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  padding-bottom: 1.5px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 1.5px;
  text-decoration-thickness: 1.6px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-35:focus {
  outline: none;
}

.emotion-35:hover,
.emotion-35:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-35:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-36 {
  box-sizing: border-box;
}

.emotion-43 {
  width: 100%;
  height: 100%;
  padding: 50px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: max-content max-content 1fr;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  row-gap: 24px;
}

.emotion-43 .rteWrapperDivs {
  grid-column: 3;
  grid-row: 1;
}

.emotion-43 .ctaWrapperDivs {
  grid-column: 3;
  grid-row: 2;
  text-align: center;
}

.emotion-44 {
  box-sizing: content-box;
  text-align: end;
}

.emotion-47 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 12px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-47:focus {
  outline: none;
}

.emotion-47:hover,
.emotion-47:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-47:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="emotion-1"
          data-testid="category-banner"
        >
          <div
            class="emotion-2"
          >
            <nav
              class="emotion-3"
            >
              <div
                class="emotion-4"
              >
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <button
                    aria-label="Previous"
                    class="slick-prev slick-arrow slick-prev slick-disabled"
                    data-role="none"
                    disabled=""
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-5"
                    >
                      <svg
                        viewBox="0 0 18 7.742"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                          fill="#FFFFFF"
                          transform="translate(18) rotate(90)"
                        />
                      </svg>
                    </span>
                  </button>
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="aspect-ratio-container"
                                height="275"
                                width="1280"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Female red shirt"
                                    class="emotion-10"
                                    src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/210706_26-M1837_EarlyBTS_HP_Primary_CatNav03_US_XL?fmt=webp"
                                  />
                                </div>
                              </div>
                              <a
                                class="emotion-11"
                                href="https://page.not.found"
                                tabindex="-1"
                                target="_self"
                                title="page not found"
                              />
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-13"
                              >
                                <div
                                  class="rteWrapperDivs emotion-14"
                                >
                                  <div
                                    class="emotion-15"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                        >
                                          Lorem Ipsum Dolor
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          Sit amet, consectetur adipiscing
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-16"
                                >
                                  <a
                                    class="emotion-17"
                                    color="dark"
                                    href="https://page.not.found"
                                  >
                                    Buy now
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="aspect-ratio-container"
                                height="275"
                                width="1280"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Man landed and wearing white shirt and jeans"
                                    class="emotion-10"
                                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/tshirt_mobile?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-24"
                              >
                                <div
                                  class="rteWrapperDivs emotion-25"
                                >
                                  <div
                                    class="emotion-15"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                          style="text-transform:uppercase;font-style:italic"
                                        >
                                          Lorem Ipsum Dolor
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          Sit amet, 
                                        </span>
                                        <span
                                          class="amp-cms--body-1"
                                          style="text-decoration:underline"
                                        >
                                          consectetur
                                        </span>
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                           adipiscing
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="aspect-ratio-container"
                                height="275"
                                width="1280"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Boy sitting and smiling"
                                    class="emotion-10"
                                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/G32408_Boys_MOB_anim2?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-33"
                              >
                                <div
                                  class="ctaWrapperDivs emotion-34"
                                >
                                  <a
                                    class="emotion-35"
                                    color="dark"
                                    href="https://page.not.found"
                                  >
                                    <span
                                      class="emotion-36"
                                    >
                                      New Arrivals
                                    </span>
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="3"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="aspect-ratio-container"
                                height="275"
                                width="1280"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Woman landing and wearing white shirt and black pants"
                                    class="emotion-10"
                                    src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/Test-AG-Men-Pants-4?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-43"
                              >
                                <div
                                  class="rteWrapperDivs emotion-44"
                                >
                                  <div
                                    class="emotion-15"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                          style="text-transform:uppercase"
                                        >
                                          Lorem Ipsum Dolor
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                          style="font-style:italic"
                                        >
                                          Sit amet, consectetur adipiscing
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-34"
                                >
                                  <a
                                    class="emotion-47"
                                    color="dark"
                                    href="https://page.not.found"
                                  >
                                    SHOP HERE
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    aria-label="Next"
                    class="slick-next slick-arrow slick-next"
                    data-role="none"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-5"
                    >
                      <svg
                        viewBox="0 0 18 7.742"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                          fill="#FFFFFF"
                          transform="translate(18) rotate(90)"
                        />
                      </svg>
                    </span>
                  </button>
                  <ul
                    class="slick-dots"
                    style="display: block;"
                  >
                    <li
                      class="slick-active"
                    >
                      <button>
                        1
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        2
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        3
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        4
                      </button>
                    </li>
                  </ul>
                </div>
              </div>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`SubCategoryBannerVHCarousel Pagination should render secondary color for carousel mobile 1`] = `
<DocumentFragment>
  .emotion-0 ul.slick-dots {
  margin-block-start: 0;
  margin-block-end: 0;
  -webkit-padding-start: 0;
  padding-inline-start: 0;
  text-align: right;
  z-index: 2;
  width: 80%;
  right: 0;
  margin-right: 15px;
  margin-bottom: 15px;
  bottom: inherit;
  top: min(30.933333333333334vw, 116px);
  margin-top: 5px;
}

.emotion-0 ul.slick-dots li {
  width: auto;
  height: auto;
  margin: 0;
}

.emotion-0 ul.slick-dots li:not(li:first-child) {
  margin-left: 10px;
}

.emotion-0 ul.slick-dots li button:before {
  color: #FFFFFF;
  width: 10px;
  height: 10px;
  opacity: 1;
  border: 1px solid #FFFFFF;
  border-radius: 50%;
  content: "";
}

.emotion-0 ul.slick-dots li.slick-active button:before {
  color: #FFFFFF;
  background-color: #FFFFFF;
}

.emotion-0 button.pausePlayGapBtn {
  bottom: inherit;
  top: min(30.933333333333334vw, 116px);
}

.emotion-1 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-2 {
  position: relative;
}

.emotion-3 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 .slick-list {
  overflow: hidden;
}

.emotion-3 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-3 button.slick-next.slick-arrow.slick-next,
.emotion-3 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: min(19.88059701492537vw, 50%);
  height: 44px;
  width: 44px;
}

.emotion-3 button.slick-next.slick-arrow.slick-next>span,
.emotion-3 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-3 button.slick-next.slick-arrow.slick-next svg,
.emotion-3 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-3 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-3 .slick-disabled {
  display: none!important;
}

.emotion-3 .slick-next {
  left: calc(100% - 44px);
}

.emotion-3 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-3 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-4 {
  position: relative;
}

.emotion-4 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-4 .slick-slider .slick-track,
.emotion-4 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-4 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-4 .slick-list:focus {
  outline: none;
}

.emotion-4 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-4 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-4 .slick-track:before,
.emotion-4 .slick-track:after {
  display: table;
  content: "";
}

.emotion-4 .slick-track:after {
  clear: both;
}

.emotion-4 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-4 .slick-slide img {
  display: block;
}

.emotion-4 .slick-slide.slick-loading img {
  display: none;
}

.emotion-4 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-4 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-4 .slick-initialized .slick-slide,
.emotion-4 .slick-vertical .slick-slide {
  display: block;
}

.emotion-4 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-4 .slick-loading .slick-track,
.emotion-4 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-4 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-4 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-4 .slick-prev,
.emotion-4 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-prev:hover,
.emotion-4 .slick-next:hover,
.emotion-4 .slick-prev:focus,
.emotion-4 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-4 .slick-prev.slick-disabled,
.emotion-4 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-4 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-4 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-4 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-4 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-4 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-4 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-4 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-4 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-4 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-dots li button:hover,
.emotion-4 .slick-dots li button:focus {
  outline: none;
}

.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before,
.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-4 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-5 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-7 {
  max-height: 160px;
  overflow: hidden;
}

.emotion-8 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 375/160;
}

.emotion-9 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-10 {
  width: 100%;
  aspect-ratio: 375/160;
  object-fit: cover;
}

.emotion-11 {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

.emotion-12 {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  position: relative;
  height: auto;
}

.emotion-13 {
  width: 100%;
  height: 100%;
  padding: 15px 15px 15px 15px;
  box-sizing: border-box;
  position: static;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  row-gap: 24px;
}

.emotion-13 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-13 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-14 {
  box-sizing: content-box;
  text-align: center;
}

.emotion-15 {
  text-align: center;
}

.emotion-15 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-15 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-15 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-15 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-15 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-15 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-15 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-15 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 45px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-15 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-16 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  text-align: center;
}

.emotion-17 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 12px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-17:focus {
  outline: none;
}

.emotion-17:hover,
.emotion-17:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-17:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-24 {
  width: 100%;
  height: 100%;
  padding: 15px 15px 15px 15px;
  box-sizing: border-box;
  position: static;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  row-gap: 24px;
}

.emotion-24 .rteWrapperDivs {
  grid-column: 1;
  grid-row: 1;
}

.emotion-24 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-25 {
  box-sizing: content-box;
  text-align: left;
}

.emotion-26 {
  text-align: start;
}

.emotion-26 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-26 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-26 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-26 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-26 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-26 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-26 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-26 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 45px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-26 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-26 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-26 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-26 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-33 {
  width: 100%;
  height: 100%;
  padding: 15px 15px 15px 15px;
  box-sizing: border-box;
  position: static;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  row-gap: 24px;
}

.emotion-33 .rteWrapperDivs {
  grid-column: 3;
  grid-row: 2;
}

.emotion-33 .ctaWrapperDivs {
  grid-column: 3;
  grid-row: 2;
}

.emotion-34 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  text-align: end;
}

.emotion-35 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  padding-bottom: 1.5px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 1.5px;
  text-decoration-thickness: 1.6px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-35:focus {
  outline: none;
}

.emotion-35:hover,
.emotion-35:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-35:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-36 {
  box-sizing: border-box;
}

.emotion-43 {
  width: 100%;
  height: 100%;
  padding: 15px 15px 15px 15px;
  box-sizing: border-box;
  position: static;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  row-gap: 24px;
}

.emotion-43 .rteWrapperDivs {
  grid-column: 3;
  grid-row: 1;
}

.emotion-43 .ctaWrapperDivs {
  grid-column: 3;
  grid-row: 1;
}

.emotion-44 {
  box-sizing: content-box;
  text-align: right;
}

.emotion-45 {
  text-align: end;
}

.emotion-45 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-45 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-45 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-45 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-45 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-45 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-45 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-45 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 45px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-45 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-45 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-45 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-45 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-45 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-45 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-45 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-45 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-45 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-45 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-47 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 12px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-47:focus {
  outline: none;
}

.emotion-47:hover,
.emotion-47:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-47:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="emotion-1"
          data-testid="category-banner"
        >
          <div
            class="emotion-2"
          >
            <nav
              class="emotion-3"
            >
              <div
                class="emotion-4"
              >
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <button
                    aria-label="Previous"
                    class="slick-prev slick-arrow slick-prev slick-disabled"
                    data-role="none"
                    disabled=""
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-5"
                    >
                      <svg
                        viewBox="0 0 18 7.742"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                          fill="#FFFFFF"
                          transform="translate(18) rotate(90)"
                        />
                      </svg>
                    </span>
                  </button>
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="aspect-ratio-container"
                                height="160"
                                width="375"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Female red shirt"
                                    class="emotion-10"
                                    src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/210706_26-M1837_EarlyBTS_HP_Primary_CatNav03_US_XL?fmt=webp"
                                  />
                                </div>
                              </div>
                              <a
                                class="emotion-11"
                                href="https://page.not.found"
                                tabindex="-1"
                                target="_self"
                                title="page not found"
                              />
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-13"
                              >
                                <div
                                  class="rteWrapperDivs emotion-14"
                                >
                                  <div
                                    class="emotion-15"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                        >
                                          Lorem Ipsum Dolor
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          Sit amet, consectetur adipiscing
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-16"
                                >
                                  <a
                                    class="emotion-17"
                                    color="dark"
                                    href="https://page.not.found"
                                  >
                                    Buy now
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="aspect-ratio-container"
                                height="160"
                                width="375"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Man landed and wearing white shirt and jeans"
                                    class="emotion-10"
                                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/tshirt_mobile?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-24"
                              >
                                <div
                                  class="rteWrapperDivs emotion-25"
                                >
                                  <div
                                    class="emotion-26"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                          style="text-transform:uppercase;font-style:italic"
                                        >
                                          Lorem Ipsum Dolor
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          Sit amet, 
                                        </span>
                                        <span
                                          class="amp-cms--body-1"
                                          style="text-decoration:underline"
                                        >
                                          consectetur
                                        </span>
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                           adipiscing
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="aspect-ratio-container"
                                height="160"
                                width="375"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Boy sitting and smiling"
                                    class="emotion-10"
                                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/G32408_Boys_MOB_anim2?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-33"
                              >
                                <div
                                  class="ctaWrapperDivs emotion-34"
                                >
                                  <a
                                    class="emotion-35"
                                    color="dark"
                                    href="https://page.not.found"
                                  >
                                    <span
                                      class="emotion-36"
                                    >
                                      New Arrivals
                                    </span>
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="3"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="aspect-ratio-container"
                                height="160"
                                width="375"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Woman landing and wearing white shirt and black pants"
                                    class="emotion-10"
                                    src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/Test-AG-Men-Pants-4?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-43"
                              >
                                <div
                                  class="rteWrapperDivs emotion-44"
                                >
                                  <div
                                    class="emotion-45"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                          style="text-transform:uppercase"
                                        >
                                          Lorem Ipsum Dolor
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                          style="font-style:italic"
                                        >
                                          Sit amet, consectetur adipiscing
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-34"
                                >
                                  <a
                                    class="emotion-47"
                                    color="dark"
                                    href="https://page.not.found"
                                  >
                                    SHOP HERE
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    aria-label="Next"
                    class="slick-next slick-arrow slick-next"
                    data-role="none"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-5"
                    >
                      <svg
                        viewBox="0 0 18 7.742"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                          fill="#FFFFFF"
                          transform="translate(18) rotate(90)"
                        />
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`SubCategoryBannerVariableHeightCarousel should match snapshot 1`] = `
.emotion-0 ul.slick-dots {
  margin-block-start: 0;
  margin-block-end: 0;
  -webkit-padding-start: 0;
  padding-inline-start: 0;
  text-align: right;
  z-index: 2;
  width: 80%;
  right: 0;
  margin-right: 20px;
  margin-bottom: 20px;
  bottom: 0;
  top: inherit;
}

.emotion-0 ul.slick-dots li {
  width: auto;
  height: auto;
  margin: 0;
}

.emotion-0 ul.slick-dots li:not(li:first-child) {
  margin-left: 10px;
}

.emotion-0 ul.slick-dots li button:before {
  color: #2B2B2B;
  width: 10px;
  height: 10px;
  opacity: 1;
  border: 1px solid #2B2B2B;
  border-radius: 50%;
  content: "";
}

.emotion-0 ul.slick-dots li.slick-active button:before {
  color: #2B2B2B;
  background-color: #2B2B2B;
}

.emotion-0 button.pausePlayGapBtn {
  bottom: 0;
  top: inherit;
}

.emotion-1 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-2 {
  position: relative;
}

.emotion-3 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 .slick-list {
  overflow: hidden;
}

.emotion-3 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-3 button.slick-next.slick-arrow.slick-next,
.emotion-3 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: min(13.750000000000002vw, 50%);
  height: 44px;
  width: 44px;
}

.emotion-3 button.slick-next.slick-arrow.slick-next>span,
.emotion-3 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-3 button.slick-next.slick-arrow.slick-next svg,
.emotion-3 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-3 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-3 .slick-disabled {
  display: none!important;
}

.emotion-3 .slick-next {
  left: calc(100% - 44px);
}

.emotion-3 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-3 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-4 {
  position: relative;
}

.emotion-4 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-4 .slick-slider .slick-track,
.emotion-4 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-4 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-4 .slick-list:focus {
  outline: none;
}

.emotion-4 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-4 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-4 .slick-track:before,
.emotion-4 .slick-track:after {
  display: table;
  content: "";
}

.emotion-4 .slick-track:after {
  clear: both;
}

.emotion-4 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-4 .slick-slide img {
  display: block;
}

.emotion-4 .slick-slide.slick-loading img {
  display: none;
}

.emotion-4 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-4 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-4 .slick-initialized .slick-slide,
.emotion-4 .slick-vertical .slick-slide {
  display: block;
}

.emotion-4 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-4 .slick-loading .slick-track,
.emotion-4 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-4 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-4 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-4 .slick-prev,
.emotion-4 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-prev:hover,
.emotion-4 .slick-next:hover,
.emotion-4 .slick-prev:focus,
.emotion-4 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-4 .slick-prev.slick-disabled,
.emotion-4 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-4 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-4 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-4 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-4 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-4 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-4 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-4 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-4 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-4 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-dots li button:hover,
.emotion-4 .slick-dots li button:focus {
  outline: none;
}

.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before,
.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-4 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-5 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-7 {
  max-height: 275px;
  overflow: hidden;
}

.emotion-8 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1280/275;
}

.emotion-9 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-10 {
  width: 100%;
  aspect-ratio: 1280/275;
  object-fit: cover;
}

.emotion-11 {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

.emotion-12 {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  position: absolute;
  height: 100%;
}

.emotion-13 {
  width: 100%;
  height: 100%;
  padding: 50px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: 1fr max-content 1fr;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  row-gap: 24px;
}

.emotion-13 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-13 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 3;
  text-align: center;
}

.emotion-14 {
  box-sizing: content-box;
  text-align: center;
}

.emotion-15 {
  text-align: center;
}

.emotion-15 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-15 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-15 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-15 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-15 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-15 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-15 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 70px;
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 114px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-15 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-16 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  text-align: center;
}

.emotion-17 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 12px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-17:focus {
  outline: none;
}

.emotion-17:hover,
.emotion-17:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-17:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-24 {
  width: 100%;
  height: 100%;
  padding: 50px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  row-gap: 24px;
}

.emotion-24 .rteWrapperDivs {
  grid-column: 1;
  grid-row: 1;
}

.emotion-24 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 2;
  text-align: center;
}

.emotion-25 {
  box-sizing: content-box;
  text-align: start;
}

.emotion-33 {
  width: 100%;
  height: 100%;
  padding: 50px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  row-gap: 24px;
}

.emotion-33 .rteWrapperDivs {
  grid-column: 3;
  grid-row: 2;
}

.emotion-33 .ctaWrapperDivs {
  grid-column: 3;
  grid-row: 2;
  text-align: center;
}

.emotion-34 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  text-align: right;
}

.emotion-35 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  padding-bottom: 1.5px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 1.5px;
  text-decoration-thickness: 1.6px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-35:focus {
  outline: none;
}

.emotion-35:hover,
.emotion-35:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-35:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-36 {
  box-sizing: border-box;
}

.emotion-43 {
  width: 100%;
  height: 100%;
  padding: 50px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: max-content max-content 1fr;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  row-gap: 24px;
}

.emotion-43 .rteWrapperDivs {
  grid-column: 3;
  grid-row: 1;
}

.emotion-43 .ctaWrapperDivs {
  grid-column: 3;
  grid-row: 2;
  text-align: center;
}

.emotion-44 {
  box-sizing: content-box;
  text-align: end;
}

.emotion-47 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 12px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-47:focus {
  outline: none;
}

.emotion-47:hover,
.emotion-47:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-47:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="emotion-1"
          data-testid="category-banner"
        >
          <div
            class="emotion-2"
          >
            <nav
              class="emotion-3"
            >
              <div
                class="emotion-4"
              >
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <button
                    aria-label="Previous"
                    class="slick-prev slick-arrow slick-prev slick-disabled"
                    data-role="none"
                    disabled=""
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-5"
                    >
                      <svg
                        viewBox="0 0 18 7.742"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                          fill="#2B2B2B"
                          transform="translate(18) rotate(90)"
                        />
                      </svg>
                    </span>
                  </button>
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="aspect-ratio-container"
                                height="275"
                                width="1280"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Female red shirt"
                                    class="emotion-10"
                                    src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/210706_26-M1837_EarlyBTS_HP_Primary_CatNav03_US_XL?fmt=webp"
                                  />
                                </div>
                              </div>
                              <a
                                class="emotion-11"
                                href="https://page.not.found"
                                tabindex="-1"
                                target="_self"
                                title="page not found"
                              />
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-13"
                              >
                                <div
                                  class="rteWrapperDivs emotion-14"
                                >
                                  <div
                                    class="emotion-15"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                        >
                                          Lorem Ipsum Dolor
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          Sit amet, consectetur adipiscing
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-16"
                                >
                                  <a
                                    class="emotion-17"
                                    color="dark"
                                    href="https://page.not.found"
                                  >
                                    Buy now
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="aspect-ratio-container"
                                height="275"
                                width="1280"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Man landed and wearing white shirt and jeans"
                                    class="emotion-10"
                                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/tshirt_mobile?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-24"
                              >
                                <div
                                  class="rteWrapperDivs emotion-25"
                                >
                                  <div
                                    class="emotion-15"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                          style="text-transform:uppercase;font-style:italic"
                                        >
                                          Lorem Ipsum Dolor
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          Sit amet, 
                                        </span>
                                        <span
                                          class="amp-cms--body-1"
                                          style="text-decoration:underline"
                                        >
                                          consectetur
                                        </span>
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                           adipiscing
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="aspect-ratio-container"
                                height="275"
                                width="1280"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Boy sitting and smiling"
                                    class="emotion-10"
                                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/G32408_Boys_MOB_anim2?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-33"
                              >
                                <div
                                  class="ctaWrapperDivs emotion-34"
                                >
                                  <a
                                    class="emotion-35"
                                    color="dark"
                                    href="https://page.not.found"
                                  >
                                    <span
                                      class="emotion-36"
                                    >
                                      New Arrivals
                                    </span>
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="3"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="aspect-ratio-container"
                                height="275"
                                width="1280"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Woman landing and wearing white shirt and black pants"
                                    class="emotion-10"
                                    src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/Test-AG-Men-Pants-4?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-43"
                              >
                                <div
                                  class="rteWrapperDivs emotion-44"
                                >
                                  <div
                                    class="emotion-15"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                          style="text-transform:uppercase"
                                        >
                                          Lorem Ipsum Dolor
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                          style="font-style:italic"
                                        >
                                          Sit amet, consectetur adipiscing
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-34"
                                >
                                  <a
                                    class="emotion-47"
                                    color="dark"
                                    href="https://page.not.found"
                                  >
                                    SHOP HERE
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    aria-label="Next"
                    class="slick-next slick-arrow slick-next"
                    data-role="none"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-5"
                    >
                      <svg
                        viewBox="0 0 18 7.742"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                          fill="#2B2B2B"
                          transform="translate(18) rotate(90)"
                        />
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`SubCategoryBannerVariableHeightCarousel should render persistent && below, play and pagination on mobile 1`] = `
<DocumentFragment>
  .emotion-0 ul.slick-dots {
  margin-block-start: 0;
  margin-block-end: 0;
  -webkit-padding-start: 0;
  padding-inline-start: 0;
  text-align: right;
  z-index: 2;
  width: 80%;
  right: 0;
  margin-right: 15px;
  margin-bottom: 15px;
  bottom: 0;
  top: inherit;
}

.emotion-0 ul.slick-dots li {
  width: auto;
  height: auto;
  margin: 0;
}

.emotion-0 ul.slick-dots li:not(li:first-child) {
  margin-left: 10px;
}

.emotion-0 ul.slick-dots li button:before {
  color: #2B2B2B;
  width: 10px;
  height: 10px;
  opacity: 1;
  border: 1px solid #2B2B2B;
  border-radius: 50%;
  content: "";
}

.emotion-0 ul.slick-dots li.slick-active button:before {
  color: #2B2B2B;
  background-color: #2B2B2B;
}

.emotion-0 button.pausePlayGapBtn {
  bottom: 0;
  top: inherit;
}

.emotion-1 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-2 {
  position: relative;
}

.emotion-3 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 .slick-list {
  overflow: hidden;
}

.emotion-3 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-3 button.slick-next.slick-arrow.slick-next,
.emotion-3 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: min(19.88059701492537vw, 50%);
  height: 44px;
  width: 44px;
}

.emotion-3 button.slick-next.slick-arrow.slick-next>span,
.emotion-3 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-3 button.slick-next.slick-arrow.slick-next svg,
.emotion-3 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-3 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-3 .slick-disabled {
  display: none!important;
}

.emotion-3 .slick-next {
  left: calc(100% - 44px);
}

.emotion-3 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-3 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-4 {
  position: relative;
}

.emotion-4 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-4 .slick-slider .slick-track,
.emotion-4 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-4 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-4 .slick-list:focus {
  outline: none;
}

.emotion-4 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-4 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-4 .slick-track:before,
.emotion-4 .slick-track:after {
  display: table;
  content: "";
}

.emotion-4 .slick-track:after {
  clear: both;
}

.emotion-4 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-4 .slick-slide img {
  display: block;
}

.emotion-4 .slick-slide.slick-loading img {
  display: none;
}

.emotion-4 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-4 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-4 .slick-initialized .slick-slide,
.emotion-4 .slick-vertical .slick-slide {
  display: block;
}

.emotion-4 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-4 .slick-loading .slick-track,
.emotion-4 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-4 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-4 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-4 .slick-prev,
.emotion-4 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-prev:hover,
.emotion-4 .slick-next:hover,
.emotion-4 .slick-prev:focus,
.emotion-4 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-4 .slick-prev.slick-disabled,
.emotion-4 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-4 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-4 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-4 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-4 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-4 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-4 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-4 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-4 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-4 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-dots li button:hover,
.emotion-4 .slick-dots li button:focus {
  outline: none;
}

.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before,
.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-4 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-5 {
  position: absolute;
  border: none;
  padding: 0;
  z-index: 1;
  background: transparent;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  height: 44px;
  width: 44px;
}

.emotion-5>span {
  display: initial;
}

.emotion-6 {
  width: 18px;
  height: 18px;
}

.emotion-7 {
  display: inline-block;
  height: 18px;
  width: 18px;
  min-height: 18px;
  min-width: 18px;
}

.emotion-7 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-8 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-8 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-10 {
  max-height: 160px;
  overflow: hidden;
}

.emotion-11 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 375/160;
}

.emotion-12 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-13 {
  width: 100%;
  aspect-ratio: 375/160;
  object-fit: cover;
}

.emotion-14 {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

.emotion-31 {
  width: 100%;
  height: 100%;
  padding: 15px 15px 15px 15px;
  box-sizing: border-box;
  position: static;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(20%,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  row-gap: 24px;
}

.emotion-31 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-31 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-32 {
  box-sizing: content-box;
  text-align: center;
}

.emotion-33 {
  text-align: center;
}

.emotion-33 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-33 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-33 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-33 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-33 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-33 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-33 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-33 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-33 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-33 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-33 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-33 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-33 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-33 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-33 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-33 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-33 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-33 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-33 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-33 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-33 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-33 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-33 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-33 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 45px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-33 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-33 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-33 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-33 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-33 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-33 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-33 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-33 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-33 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-33 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-33 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-33 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-33 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-33 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-33 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-34 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  text-align: center;
}

.emotion-35 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 12px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-35:focus {
  outline: none;
}

.emotion-35:hover,
.emotion-35:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-35:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="emotion-1"
          data-testid="category-banner"
        >
          <div
            class="emotion-2"
          >
            <nav
              class="emotion-3"
            >
              <div
                class="emotion-4"
              >
                <button
                  aria-label="pause"
                  class="pausePlayGapBtn emotion-5"
                >
                  <div
                    class="emotion-6"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-7"
                    >
                      <svg
                        viewBox="0 0 8.093 18"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <g
                          transform="translate(1.125)"
                        >
                          <path
                            d="M.321,18H-1.125V0H.321Z"
                            fill="#2B2B2B"
                            id="Left"
                            transform="translate(0 0)"
                          />
                          <path
                            d="M.321,17.749H-1.125V0H.321Z"
                            fill="#2B2B2B"
                            id="Right"
                            transform="translate(6.646 0)"
                          />
                        </g>
                      </svg>
                    </span>
                  </div>
                </button>
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <button
                    aria-label="Previous"
                    class="slick-prev slick-arrow slick-prev slick-disabled"
                    data-role="none"
                    disabled=""
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-8"
                    >
                      <svg
                        viewBox="0 0 18 7.742"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                          fill="#2B2B2B"
                          transform="translate(18) rotate(90)"
                        />
                      </svg>
                    </span>
                  </button>
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-10"
                            >
                              <div
                                class="emotion-11"
                                data-testid="aspect-ratio-container"
                                height="160"
                                width="375"
                              >
                                <div
                                  class="emotion-12"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Female red shirt"
                                    class="emotion-13"
                                    src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/210706_26-M1837_EarlyBTS_HP_Primary_CatNav03_US_XL?fmt=webp"
                                  />
                                </div>
                              </div>
                              <a
                                class="emotion-14"
                                href="https://page.not.found"
                                tabindex="-1"
                                target="_self"
                                title="page not found"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-10"
                            >
                              <div
                                class="emotion-11"
                                data-testid="aspect-ratio-container"
                                height="160"
                                width="375"
                              >
                                <div
                                  class="emotion-12"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Man landed and wearing white shirt and jeans"
                                    class="emotion-13"
                                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/tshirt_mobile?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-10"
                            >
                              <div
                                class="emotion-11"
                                data-testid="aspect-ratio-container"
                                height="160"
                                width="375"
                              >
                                <div
                                  class="emotion-12"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Boy sitting and smiling"
                                    class="emotion-13"
                                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/G32408_Boys_MOB_anim2?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="3"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-10"
                            >
                              <div
                                class="emotion-11"
                                data-testid="aspect-ratio-container"
                                height="160"
                                width="375"
                              >
                                <div
                                  class="emotion-12"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Woman landing and wearing white shirt and black pants"
                                    class="emotion-13"
                                    src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/Test-AG-Men-Pants-4?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    aria-label="Next"
                    class="slick-next slick-arrow slick-next"
                    data-role="none"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-8"
                    >
                      <svg
                        viewBox="0 0 18 7.742"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                          fill="#2B2B2B"
                          transform="translate(18) rotate(90)"
                        />
                      </svg>
                    </span>
                  </button>
                  <ul
                    class="slick-dots"
                    style="display: block;"
                  >
                    <li
                      class="slick-active"
                    >
                      <button>
                        1
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        2
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        3
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        4
                      </button>
                    </li>
                  </ul>
                </div>
              </div>
            </nav>
            <div
              class="emotion-31"
            >
              <div
                class="rteWrapperDivs emotion-32"
              >
                <div
                  class="emotion-33"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--headline-6"
                      >
                        Lorem Ipsum Dolor
                      </span>
                    </p>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Sit amet, consectetur adipiscing
                      </span>
                    </p>
                  </div>
                </div>
              </div>
              <div
                class="ctaWrapperDivs emotion-34"
              >
                <a
                  class="emotion-35"
                  color="dark"
                  href="https://page.not.found"
                >
                  Buy now
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`SubCategoryBannerVariableHeightCarousel should render persistent below on mobile snapshot 1`] = `
<DocumentFragment>
  .emotion-0 ul.slick-dots {
  margin-block-start: 0;
  margin-block-end: 0;
  -webkit-padding-start: 0;
  padding-inline-start: 0;
  text-align: right;
  z-index: 2;
  width: 80%;
  right: 0;
  margin-right: 15px;
  margin-bottom: 15px;
  bottom: 0;
  top: inherit;
}

.emotion-0 ul.slick-dots li {
  width: auto;
  height: auto;
  margin: 0;
}

.emotion-0 ul.slick-dots li:not(li:first-child) {
  margin-left: 10px;
}

.emotion-0 ul.slick-dots li button:before {
  color: #2B2B2B;
  width: 10px;
  height: 10px;
  opacity: 1;
  border: 1px solid #2B2B2B;
  border-radius: 50%;
  content: "";
}

.emotion-0 ul.slick-dots li.slick-active button:before {
  color: #2B2B2B;
  background-color: #2B2B2B;
}

.emotion-0 button.pausePlayGapBtn {
  bottom: 0;
  top: inherit;
}

.emotion-1 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-2 {
  position: relative;
}

.emotion-3 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 .slick-list {
  overflow: hidden;
}

.emotion-3 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-3 button.slick-next.slick-arrow.slick-next,
.emotion-3 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: min(19.88059701492537vw, 50%);
  height: 44px;
  width: 44px;
}

.emotion-3 button.slick-next.slick-arrow.slick-next>span,
.emotion-3 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-3 button.slick-next.slick-arrow.slick-next svg,
.emotion-3 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-3 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-3 .slick-disabled {
  display: none!important;
}

.emotion-3 .slick-next {
  left: calc(100% - 44px);
}

.emotion-3 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-3 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-4 {
  position: relative;
}

.emotion-4 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-4 .slick-slider .slick-track,
.emotion-4 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-4 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-4 .slick-list:focus {
  outline: none;
}

.emotion-4 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-4 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-4 .slick-track:before,
.emotion-4 .slick-track:after {
  display: table;
  content: "";
}

.emotion-4 .slick-track:after {
  clear: both;
}

.emotion-4 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-4 .slick-slide img {
  display: block;
}

.emotion-4 .slick-slide.slick-loading img {
  display: none;
}

.emotion-4 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-4 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-4 .slick-initialized .slick-slide,
.emotion-4 .slick-vertical .slick-slide {
  display: block;
}

.emotion-4 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-4 .slick-loading .slick-track,
.emotion-4 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-4 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-4 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-4 .slick-prev,
.emotion-4 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-prev:hover,
.emotion-4 .slick-next:hover,
.emotion-4 .slick-prev:focus,
.emotion-4 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-4 .slick-prev.slick-disabled,
.emotion-4 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-4 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-4 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-4 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-4 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-4 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-4 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-4 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-4 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-4 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-dots li button:hover,
.emotion-4 .slick-dots li button:focus {
  outline: none;
}

.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before,
.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-4 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-5 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-7 {
  max-height: 160px;
  overflow: hidden;
}

.emotion-8 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 375/160;
}

.emotion-9 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-10 {
  width: 100%;
  aspect-ratio: 375/160;
  object-fit: cover;
}

.emotion-11 {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

.emotion-28 {
  width: 100%;
  height: 100%;
  padding: 15px 15px 15px 15px;
  box-sizing: border-box;
  position: static;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(20%,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  row-gap: 24px;
}

.emotion-28 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-28 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-29 {
  box-sizing: content-box;
  text-align: center;
}

.emotion-30 {
  text-align: center;
}

.emotion-30 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-30 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-30 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-30 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-30 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-30 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-30 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-30 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 45px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-30 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-30 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-30 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-30 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-31 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  text-align: center;
}

.emotion-32 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 12px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-32:focus {
  outline: none;
}

.emotion-32:hover,
.emotion-32:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-32:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="emotion-1"
          data-testid="category-banner"
        >
          <div
            class="emotion-2"
          >
            <nav
              class="emotion-3"
            >
              <div
                class="emotion-4"
              >
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <button
                    aria-label="Previous"
                    class="slick-prev slick-arrow slick-prev slick-disabled"
                    data-role="none"
                    disabled=""
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-5"
                    >
                      <svg
                        viewBox="0 0 18 7.742"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                          fill="#2B2B2B"
                          transform="translate(18) rotate(90)"
                        />
                      </svg>
                    </span>
                  </button>
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="aspect-ratio-container"
                                height="160"
                                width="375"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Female red shirt"
                                    class="emotion-10"
                                    src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/210706_26-M1837_EarlyBTS_HP_Primary_CatNav03_US_XL?fmt=webp"
                                  />
                                </div>
                              </div>
                              <a
                                class="emotion-11"
                                href="https://page.not.found"
                                tabindex="-1"
                                target="_self"
                                title="page not found"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="aspect-ratio-container"
                                height="160"
                                width="375"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Man landed and wearing white shirt and jeans"
                                    class="emotion-10"
                                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/tshirt_mobile?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="aspect-ratio-container"
                                height="160"
                                width="375"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Boy sitting and smiling"
                                    class="emotion-10"
                                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/G32408_Boys_MOB_anim2?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="3"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="aspect-ratio-container"
                                height="160"
                                width="375"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Woman landing and wearing white shirt and black pants"
                                    class="emotion-10"
                                    src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/Test-AG-Men-Pants-4?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    aria-label="Next"
                    class="slick-next slick-arrow slick-next"
                    data-role="none"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-5"
                    >
                      <svg
                        viewBox="0 0 18 7.742"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                          fill="#2B2B2B"
                          transform="translate(18) rotate(90)"
                        />
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </nav>
            <div
              class="emotion-28"
            >
              <div
                class="rteWrapperDivs emotion-29"
              >
                <div
                  class="emotion-30"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--headline-6"
                      >
                        Lorem Ipsum Dolor
                      </span>
                    </p>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Sit amet, consectetur adipiscing
                      </span>
                    </p>
                  </div>
                </div>
              </div>
              <div
                class="ctaWrapperDivs emotion-31"
              >
                <a
                  class="emotion-32"
                  color="dark"
                  href="https://page.not.found"
                >
                  Buy now
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`should render GapFactory should render GapFactory desktop snaps 1`] = `
<DocumentFragment>
  .emotion-0 ul.slick-dots {
  margin-block-start: 0;
  margin-block-end: 0;
  -webkit-padding-start: 0;
  padding-inline-start: 0;
  text-align: right;
  z-index: 2;
  width: 80%;
  right: 0;
  margin-right: 20px;
  margin-bottom: 20px;
  bottom: 0;
  top: inherit;
}

.emotion-0 ul.slick-dots li {
  width: auto;
  height: auto;
  margin: 0;
}

.emotion-0 ul.slick-dots li:not(li:first-child) {
  margin-left: 10px;
}

.emotion-0 ul.slick-dots li button:before {
  color: #2B2B2B;
  width: 10px;
  height: 10px;
  opacity: 1;
  border: 1px solid #2B2B2B;
  border-radius: 50%;
  content: "";
}

.emotion-0 ul.slick-dots li.slick-active button:before {
  color: #2B2B2B;
  background-color: #2B2B2B;
}

.emotion-0 button.pausePlayGapBtn {
  bottom: 0;
  top: inherit;
}

.emotion-1 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-2 {
  position: relative;
}

.emotion-3 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 .slick-list {
  overflow: hidden;
}

.emotion-3 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-3 button.slick-next.slick-arrow.slick-next,
.emotion-3 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: min(13.750000000000002vw, 50%);
  height: 44px;
  width: 44px;
}

.emotion-3 button.slick-next.slick-arrow.slick-next>span,
.emotion-3 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-3 button.slick-next.slick-arrow.slick-next svg,
.emotion-3 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-3 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-3 .slick-disabled {
  display: none!important;
}

.emotion-3 .slick-next {
  left: calc(100% - 44px);
}

.emotion-3 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-3 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-4 {
  position: relative;
}

.emotion-4 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-4 .slick-slider .slick-track,
.emotion-4 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-4 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-4 .slick-list:focus {
  outline: none;
}

.emotion-4 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-4 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-4 .slick-track:before,
.emotion-4 .slick-track:after {
  display: table;
  content: "";
}

.emotion-4 .slick-track:after {
  clear: both;
}

.emotion-4 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-4 .slick-slide img {
  display: block;
}

.emotion-4 .slick-slide.slick-loading img {
  display: none;
}

.emotion-4 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-4 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-4 .slick-initialized .slick-slide,
.emotion-4 .slick-vertical .slick-slide {
  display: block;
}

.emotion-4 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-4 .slick-loading .slick-track,
.emotion-4 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-4 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-4 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-4 .slick-prev,
.emotion-4 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-prev:hover,
.emotion-4 .slick-next:hover,
.emotion-4 .slick-prev:focus,
.emotion-4 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-4 .slick-prev.slick-disabled,
.emotion-4 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-4 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-4 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-4 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-4 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-4 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-4 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-4 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-4 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-4 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-dots li button:hover,
.emotion-4 .slick-dots li button:focus {
  outline: none;
}

.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before,
.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-4 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-5 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-7 {
  max-height: 275px;
  overflow: hidden;
}

.emotion-8 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 1280/275;
}

.emotion-9 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-10 {
  width: 100%;
  aspect-ratio: 1280/275;
  object-fit: cover;
}

.emotion-11 {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

.emotion-12 {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  position: absolute;
  height: 100%;
}

.emotion-13 {
  width: 100%;
  height: 100%;
  padding: 50px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: 1fr max-content 1fr;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  row-gap: 24px;
}

.emotion-13 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-13 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 3;
  text-align: center;
}

.emotion-14 {
  box-sizing: content-box;
  text-align: center;
}

.emotion-15 {
  text-align: center;
}

.emotion-15 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-15 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-15 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-15 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-15 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-15 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-15 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 0.92;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 120px;
  line-height: 0.8333333333333334;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 110px;
  line-height: 0.8636363636363636;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 0.85;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1.05;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 0.9375;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 70px;
  line-height: 0.9285714285714286;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 0.9166666666666666;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 114px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-15 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 25px;
  line-height: 1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-16 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  text-align: center;
}

.emotion-17 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 12px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-17:focus {
  outline: none;
}

.emotion-17:hover,
.emotion-17:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-17:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-24 {
  width: 100%;
  height: 100%;
  padding: 50px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  row-gap: 24px;
}

.emotion-24 .rteWrapperDivs {
  grid-column: 1;
  grid-row: 1;
}

.emotion-24 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 2;
  text-align: center;
}

.emotion-25 {
  box-sizing: content-box;
  text-align: start;
}

.emotion-33 {
  width: 100%;
  height: 100%;
  padding: 50px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  row-gap: 24px;
}

.emotion-33 .rteWrapperDivs {
  grid-column: 3;
  grid-row: 2;
}

.emotion-33 .ctaWrapperDivs {
  grid-column: 3;
  grid-row: 2;
  text-align: center;
}

.emotion-34 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  text-align: right;
}

.emotion-35 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  padding-bottom: 1.5px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 1.5px;
  text-decoration-thickness: 1.6px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-35:focus {
  outline: none;
}

.emotion-35:hover,
.emotion-35:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-35:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-36 {
  box-sizing: border-box;
}

.emotion-43 {
  width: 100%;
  height: 100%;
  padding: 50px;
  box-sizing: border-box;
  position: absolute;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: grid;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: max-content max-content 1fr;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  row-gap: 24px;
}

.emotion-43 .rteWrapperDivs {
  grid-column: 3;
  grid-row: 1;
}

.emotion-43 .ctaWrapperDivs {
  grid-column: 3;
  grid-row: 2;
  text-align: center;
}

.emotion-44 {
  box-sizing: content-box;
  text-align: end;
}

.emotion-47 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 300px;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 12px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-47:focus {
  outline: none;
}

.emotion-47:hover,
.emotion-47:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-47:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="emotion-1"
          data-testid="category-banner"
        >
          <div
            class="emotion-2"
          >
            <nav
              class="emotion-3"
            >
              <div
                class="emotion-4"
              >
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <button
                    aria-label="Previous"
                    class="slick-prev slick-arrow slick-prev slick-disabled"
                    data-role="none"
                    disabled=""
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-5"
                    >
                      <svg
                        viewBox="0 0 18 7.742"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                          fill="#2B2B2B"
                          transform="translate(18) rotate(90)"
                        />
                      </svg>
                    </span>
                  </button>
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="aspect-ratio-container"
                                height="275"
                                width="1280"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Female red shirt"
                                    class="emotion-10"
                                    src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/210706_26-M1837_EarlyBTS_HP_Primary_CatNav03_US_XL?fmt=webp"
                                  />
                                </div>
                              </div>
                              <a
                                class="emotion-11"
                                href="https://page.not.found"
                                tabindex="-1"
                                target="_self"
                                title="page not found"
                              />
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-13"
                              >
                                <div
                                  class="rteWrapperDivs emotion-14"
                                >
                                  <div
                                    class="emotion-15"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                        >
                                          Lorem Ipsum Dolor
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          Sit amet, consectetur adipiscing
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-16"
                                >
                                  <a
                                    class="emotion-17"
                                    color="dark"
                                    href="https://page.not.found"
                                  >
                                    Buy now
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="aspect-ratio-container"
                                height="275"
                                width="1280"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Man landed and wearing white shirt and jeans"
                                    class="emotion-10"
                                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/tshirt_mobile?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-24"
                              >
                                <div
                                  class="rteWrapperDivs emotion-25"
                                >
                                  <div
                                    class="emotion-15"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                          style="text-transform:uppercase;font-style:italic"
                                        >
                                          Lorem Ipsum Dolor
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          Sit amet, 
                                        </span>
                                        <span
                                          class="amp-cms--body-1"
                                          style="text-decoration:underline"
                                        >
                                          consectetur
                                        </span>
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                           adipiscing
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="aspect-ratio-container"
                                height="275"
                                width="1280"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Boy sitting and smiling"
                                    class="emotion-10"
                                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/G32408_Boys_MOB_anim2?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-33"
                              >
                                <div
                                  class="ctaWrapperDivs emotion-34"
                                >
                                  <a
                                    class="emotion-35"
                                    color="dark"
                                    href="https://page.not.found"
                                  >
                                    <span
                                      class="emotion-36"
                                    >
                                      New Arrivals
                                    </span>
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="3"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="aspect-ratio-container"
                                height="275"
                                width="1280"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Woman landing and wearing white shirt and black pants"
                                    class="emotion-10"
                                    src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/Test-AG-Men-Pants-4?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-43"
                              >
                                <div
                                  class="rteWrapperDivs emotion-44"
                                >
                                  <div
                                    class="emotion-15"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                          style="text-transform:uppercase"
                                        >
                                          Lorem Ipsum Dolor
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                          style="font-style:italic"
                                        >
                                          Sit amet, consectetur adipiscing
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-34"
                                >
                                  <a
                                    class="emotion-47"
                                    color="dark"
                                    href="https://page.not.found"
                                  >
                                    SHOP HERE
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    aria-label="Next"
                    class="slick-next slick-arrow slick-next"
                    data-role="none"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-5"
                    >
                      <svg
                        viewBox="0 0 18 7.742"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                          fill="#2B2B2B"
                          transform="translate(18) rotate(90)"
                        />
                      </svg>
                    </span>
                  </button>
                  <ul
                    class="slick-dots"
                    style="display: block;"
                  >
                    <li
                      class="slick-active"
                    >
                      <button>
                        1
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        2
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        3
                      </button>
                    </li>
                    <li
                      class=""
                    >
                      <button>
                        4
                      </button>
                    </li>
                  </ul>
                </div>
              </div>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`should render GapFactory should render GapFactory mobile snaps 1`] = `
<DocumentFragment>
  .emotion-0 ul.slick-dots {
  margin-block-start: 0;
  margin-block-end: 0;
  -webkit-padding-start: 0;
  padding-inline-start: 0;
  text-align: right;
  z-index: 2;
  width: 80%;
  right: 0;
  margin-right: 15px;
  margin-bottom: 15px;
  bottom: inherit;
  top: min(30.933333333333334vw, 116px);
  margin-top: 5px;
}

.emotion-0 ul.slick-dots li {
  width: auto;
  height: auto;
  margin: 0;
}

.emotion-0 ul.slick-dots li:not(li:first-child) {
  margin-left: 10px;
}

.emotion-0 ul.slick-dots li button:before {
  color: #2B2B2B;
  width: 10px;
  height: 10px;
  opacity: 1;
  border: 1px solid #2B2B2B;
  border-radius: 50%;
  content: "";
}

.emotion-0 ul.slick-dots li.slick-active button:before {
  color: #2B2B2B;
  background-color: #2B2B2B;
}

.emotion-0 button.pausePlayGapBtn {
  bottom: inherit;
  top: min(30.933333333333334vw, 116px);
}

.emotion-1 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-2 {
  position: relative;
}

.emotion-3 {
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-3 .slick-list {
  overflow: hidden;
}

.emotion-3 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-3 button.slick-next.slick-arrow.slick-next,
.emotion-3 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: min(19.88059701492537vw, 50%);
  height: 44px;
  width: 44px;
}

.emotion-3 button.slick-next.slick-arrow.slick-next>span,
.emotion-3 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-3 button.slick-next.slick-arrow.slick-next svg,
.emotion-3 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-3 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-3 .slick-disabled {
  display: none!important;
}

.emotion-3 .slick-next {
  left: calc(100% - 44px);
}

.emotion-3 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-3 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-4 {
  position: relative;
}

.emotion-4 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-4 .slick-slider .slick-track,
.emotion-4 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-4 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-4 .slick-list:focus {
  outline: none;
}

.emotion-4 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-4 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-4 .slick-track:before,
.emotion-4 .slick-track:after {
  display: table;
  content: "";
}

.emotion-4 .slick-track:after {
  clear: both;
}

.emotion-4 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-4 .slick-slide img {
  display: block;
}

.emotion-4 .slick-slide.slick-loading img {
  display: none;
}

.emotion-4 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-4 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-4 .slick-initialized .slick-slide,
.emotion-4 .slick-vertical .slick-slide {
  display: block;
}

.emotion-4 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-4 .slick-loading .slick-track,
.emotion-4 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-4 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-4 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-4 .slick-prev,
.emotion-4 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-prev:hover,
.emotion-4 .slick-next:hover,
.emotion-4 .slick-prev:focus,
.emotion-4 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-4 .slick-prev.slick-disabled,
.emotion-4 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-4 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-4 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-4 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-4 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-4 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-4 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-4 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-4 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-4 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-4 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-4 .slick-dots li button:hover,
.emotion-4 .slick-dots li button:focus {
  outline: none;
}

.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before,
.emotion-4 .slick-dots li button:hover:before,
.emotion-4 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-4 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-5 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-5 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-7 {
  max-height: 160px;
  overflow: hidden;
}

.emotion-8 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 375/160;
}

.emotion-9 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-10 {
  width: 100%;
  aspect-ratio: 375/160;
  object-fit: cover;
}

.emotion-11 {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1;
}

.emotion-12 {
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  position: relative;
  height: auto;
}

.emotion-13 {
  width: 100%;
  height: 100%;
  padding: 15px 15px 15px 15px;
  box-sizing: border-box;
  position: static;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  row-gap: 24px;
}

.emotion-13 .rteWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-13 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-14 {
  box-sizing: content-box;
  text-align: center;
}

.emotion-15 {
  text-align: center;
}

.emotion-15 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-15 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-15 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-15 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-15 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-15 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-15 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1.1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 45px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-15 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-15 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-15 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-15 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-15 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-16 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  text-align: center;
}

.emotion-17 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 12px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-17:focus {
  outline: none;
}

.emotion-17:hover,
.emotion-17:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-17:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-24 {
  width: 100%;
  height: 100%;
  padding: 15px 15px 15px 15px;
  box-sizing: border-box;
  position: static;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  row-gap: 24px;
}

.emotion-24 .rteWrapperDivs {
  grid-column: 1;
  grid-row: 1;
}

.emotion-24 .ctaWrapperDivs {
  grid-column: 2;
  grid-row: 2;
}

.emotion-25 {
  box-sizing: content-box;
  text-align: left;
}

.emotion-26 {
  text-align: start;
}

.emotion-26 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-26 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-26 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-26 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-26 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-26 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-26 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-26 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-26 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-26 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-26 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-26 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-26 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-26 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1.1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-26 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 45px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-26 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-26 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-26 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-26 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-26 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-26 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-26 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-26 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-26 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-33 {
  width: 100%;
  height: 100%;
  padding: 15px 15px 15px 15px;
  box-sizing: border-box;
  position: static;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  row-gap: 24px;
}

.emotion-33 .rteWrapperDivs {
  grid-column: 3;
  grid-row: 2;
}

.emotion-33 .ctaWrapperDivs {
  grid-column: 3;
  grid-row: 2;
}

.emotion-34 {
  display: block;
  width: 100%;
  position: relative;
  z-index: 2;
  text-align: end;
}

.emotion-35 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  padding-bottom: 1.5px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 1.5px;
  text-decoration-thickness: 1.6px;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-35:focus {
  outline: none;
}

.emotion-35:hover,
.emotion-35:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-35:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-36 {
  box-sizing: border-box;
}

.emotion-43 {
  width: 100%;
  height: 100%;
  padding: 15px 15px 15px 15px;
  box-sizing: border-box;
  position: static;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  grid-template-columns: repeat(3, minmax(0,max-content));
  -webkit-align-content: center;
  -ms-flex-line-pack: center;
  align-content: center;
  grid-template-rows: repeat(3, minmax(0, max-content));
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  row-gap: 24px;
}

.emotion-43 .rteWrapperDivs {
  grid-column: 3;
  grid-row: 1;
}

.emotion-43 .ctaWrapperDivs {
  grid-column: 3;
  grid-row: 1;
}

.emotion-44 {
  box-sizing: content-box;
  text-align: right;
}

.emotion-45 {
  text-align: end;
}

.emotion-45 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-45 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-45 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-45 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-45 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-45 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-45 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-45 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-45 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 26px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-45 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-45 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-45 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-45 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 55px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-45 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1.1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-45 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 45px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-45 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-45 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 35px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-45 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-45 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-45 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-45 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-45 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-45 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-45 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-45 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-45 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-45 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-45 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-45 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-47 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 12px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  text-transform: none;
}

.emotion-47:focus {
  outline: none;
}

.emotion-47:hover,
.emotion-47:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-47:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
    >
      <div
        class="emotion-0"
      >
        <div
          class="emotion-1"
          data-testid="category-banner"
        >
          <div
            class="emotion-2"
          >
            <nav
              class="emotion-3"
            >
              <div
                class="emotion-4"
              >
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <button
                    aria-label="Previous"
                    class="slick-prev slick-arrow slick-prev slick-disabled"
                    data-role="none"
                    disabled=""
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-5"
                    >
                      <svg
                        viewBox="0 0 18 7.742"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                          fill="#2B2B2B"
                          transform="translate(18) rotate(90)"
                        />
                      </svg>
                    </span>
                  </button>
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="aspect-ratio-container"
                                height="160"
                                width="375"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Female red shirt"
                                    class="emotion-10"
                                    src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/210706_26-M1837_EarlyBTS_HP_Primary_CatNav03_US_XL?fmt=webp"
                                  />
                                </div>
                              </div>
                              <a
                                class="emotion-11"
                                href="https://page.not.found"
                                tabindex="-1"
                                target="_self"
                                title="page not found"
                              />
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-13"
                              >
                                <div
                                  class="rteWrapperDivs emotion-14"
                                >
                                  <div
                                    class="emotion-15"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                        >
                                          Lorem Ipsum Dolor
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          Sit amet, consectetur adipiscing
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-16"
                                >
                                  <a
                                    class="emotion-17"
                                    color="dark"
                                    href="https://page.not.found"
                                  >
                                    Buy now
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="aspect-ratio-container"
                                height="160"
                                width="375"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Man landed and wearing white shirt and jeans"
                                    class="emotion-10"
                                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/tshirt_mobile?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-24"
                              >
                                <div
                                  class="rteWrapperDivs emotion-25"
                                >
                                  <div
                                    class="emotion-26"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                          style="text-transform:uppercase;font-style:italic"
                                        >
                                          Lorem Ipsum Dolor
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                          Sit amet, 
                                        </span>
                                        <span
                                          class="amp-cms--body-1"
                                          style="text-decoration:underline"
                                        >
                                          consectetur
                                        </span>
                                        <span
                                          class="amp-cms--body-1"
                                        >
                                           adipiscing
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="aspect-ratio-container"
                                height="160"
                                width="375"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Boy sitting and smiling"
                                    class="emotion-10"
                                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/G32408_Boys_MOB_anim2?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-33"
                              >
                                <div
                                  class="ctaWrapperDivs emotion-34"
                                >
                                  <a
                                    class="emotion-35"
                                    color="dark"
                                    href="https://page.not.found"
                                  >
                                    <span
                                      class="emotion-36"
                                    >
                                      New Arrivals
                                    </span>
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="3"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            class="emotion-2"
                          >
                            <div
                              class="emotion-7"
                            >
                              <div
                                class="emotion-8"
                                data-testid="aspect-ratio-container"
                                height="160"
                                width="375"
                              >
                                <div
                                  class="emotion-9"
                                  data-testid="product-card-image"
                                >
                                  <img
                                    alt="Woman landing and wearing white shirt and black pants"
                                    class="emotion-10"
                                    src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/Test-AG-Men-Pants-4?fmt=webp"
                                  />
                                </div>
                              </div>
                            </div>
                            <div
                              class="emotion-12"
                              data-testid="absolute-wrapper"
                            >
                              <div
                                class="emotion-43"
                              >
                                <div
                                  class="rteWrapperDivs emotion-44"
                                >
                                  <div
                                    class="emotion-45"
                                  >
                                    <div>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--headline-6"
                                          style="text-transform:uppercase"
                                        >
                                          Lorem Ipsum Dolor
                                        </span>
                                      </p>
                                      <p
                                        class="amp-cms--p"
                                      >
                                        <span
                                          class="amp-cms--body-1"
                                          style="font-style:italic"
                                        >
                                          Sit amet, consectetur adipiscing
                                        </span>
                                      </p>
                                    </div>
                                  </div>
                                </div>
                                <div
                                  class="ctaWrapperDivs emotion-34"
                                >
                                  <a
                                    class="emotion-47"
                                    color="dark"
                                    href="https://page.not.found"
                                  >
                                    SHOP HERE
                                  </a>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    aria-label="Next"
                    class="slick-next slick-arrow slick-next"
                    data-role="none"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-5"
                    >
                      <svg
                        viewBox="0 0 18 7.742"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                          fill="#2B2B2B"
                          transform="translate(18) rotate(90)"
                        />
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;
