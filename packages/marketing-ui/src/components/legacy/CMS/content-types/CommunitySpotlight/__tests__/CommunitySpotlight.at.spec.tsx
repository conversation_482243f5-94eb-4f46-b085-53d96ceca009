// @ts-nocheck
import React from 'react';
import { render, act } from 'test-utils';
import { SMALL, LARGE, Size } from '@ecom-next/core/breakpoint-provider';
import { Brands } from '@ecom-next/core/react-stitch';
import AthletaCommunitySpotlight, { CommunitySpotlightProps } from '../CommunitySpotlight.at';
import { communitySpotlightData, communitySpotlightMinimumData } from '../__fixtures__/test-data';

describe('CommunitySpotlight Athleta', () => {
  const renderComponent = (props?: Partial<CommunitySpotlightProps>, breakpoint: Size = LARGE) =>
    render(<AthletaCommunitySpotlight {...communitySpotlightData} {...props} />, {
      breakpoint,
      appState: {
        brandName: Brands.Athleta,
      } as any, // eslint-disable-line @typescript-eslint/no-explicit-any
    });

  it('Should match snapshots', () => {
    const result = renderComponent();
    expect(result.asFragment()).toMatchSnapshot();
  });

  it('Should match snapshots for minimum content', () => {
    const breakpoint: Size = LARGE;

    const result = render(<AthletaCommunitySpotlight {...communitySpotlightMinimumData} />, {
      breakpoint,
      appState: {
        brandName: Brands.Athleta,
      } as any, // eslint-disable-line @typescript-eslint/no-explicit-any
    });

    expect(result.asFragment()).toMatchSnapshot();
  });

  it('Should match snapshots for mobile', () => {
    const result = renderComponent({}, SMALL);
    expect(result.asFragment()).toMatchSnapshot();
  });

  it('text should be overriden', () => {
    const { getByText } = renderComponent({}, SMALL);
    expect(getByText('The quick brown fox jumps over the lazy dog')).toBeInTheDocument();
  });

  it('top text width should be 75% on desktop', () => {
    const { getByTestId } = renderComponent();
    expect(getByTestId('CommunitySpotlightContentWrapper').firstChild).toHaveStyleRule('width', '75%');
  });

  it('top text width should be 100% on mobile', () => {
    const { getByTestId } = renderComponent({}, SMALL);
    expect(getByTestId('CommunitySpotlightContentWrapper').firstChild).toHaveStyleRule('width', '100%');
  });

  it('should contain an image, bottom text and cta if present', () => {
    const { getByRole, getByTestId } = renderComponent();
    expect(getByRole('img')).toBeInTheDocument();
    expect(getByTestId('bottomTextAndCta').childElementCount).toBe(2);
  });

  it('should not contain an image if it is undefined', () => {
    const { queryByRole } = renderComponent({
      content: {
        topText: communitySpotlightData.content!.topText,
        image: undefined,
        bottomText: communitySpotlightData.content!.bottomText,
      },
    });
    expect(queryByRole('img')).toBeNull();
  });

  it('should not contain bottom text if it is undefined', () => {
    const { getByTestId } = renderComponent({
      content: {
        topText: communitySpotlightData.content!.topText,
        image: communitySpotlightData.content!.image,
        bottomText: undefined,
      },
    });
    expect(getByTestId('bottomTextAndCta').childElementCount).toBe(0);
  });

  it('should not contain cta if it is undefined', () => {
    const { getByTestId } = renderComponent({
      content: {
        topText: communitySpotlightData.content!.topText,
        image: communitySpotlightData.content!.image,
        bottomText: communitySpotlightData.content!.bottomText,
        cta: undefined,
      },
    });
    expect(getByTestId('bottomTextAndCta').childElementCount).toBe(1);
  });

  it('should have aspect ratio 1:1 for image on desktop', () => {
    const { getByRole } = renderComponent();
    expect(getByRole('img')).toHaveStyleRule('aspect-ratio', '1/1');
  });

  it('should have aspect ratio 3:4 (width:height) for image on mobile', () => {
    const { getByRole } = renderComponent({}, SMALL);
    expect(getByRole('img')).toHaveStyleRule('aspect-ratio', '3/4');
  });

  it('on desktop, bottom text and cta should be on the left of image', () => {
    const { getByTestId } = renderComponent();
    expect(getByTestId('ImageTextCtaWrapper')).toHaveStyleRule('flex-direction', 'row-reverse');
  });

  it('on mobile, bottom text and cta should be below image', () => {
    const { getByTestId } = renderComponent({}, SMALL);
    expect(getByTestId('ImageTextCtaWrapper')).toHaveStyleRule('flex-direction', 'column');
  });
});
