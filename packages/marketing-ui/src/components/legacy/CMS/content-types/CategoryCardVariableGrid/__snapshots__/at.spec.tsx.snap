// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CategoryCardVariableGridAthleta snapshots should match for desktop 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: grid;
  gap: 0px;
  grid-template-columns: repeat(4, minmax(0, 1fr));
  overflow-x: unset;
}

.emotion-1 {
  position: relative;
  z-index: 2;
  overflow: hidden;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 314/460;
}

.emotion-3 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-4 {
  width: 100%;
  aspect-ratio: 314/460;
  object-fit: cover;
}

.emotion-5 {
  cursor: pointer;
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 0;
  top: 0;
  left: 0;
}

.emotion-6 {
  text-align: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 100%;
  width: 100%;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  box-sizing: border-box;
  padding: 16px 24px;
  position: absolute;
  top: 0;
  left: 0;
}

.emotion-8 {
  position: initial;
}

.emotion-9 {
  margin-bottom: 20px;
  width: 100%;
  z-index: 0;
}

.emotion-9 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-9 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-9 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-9 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-9 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.emotion-9 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-9 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-9 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5555555555555556;
  letter-spacing: 0.9px;
  font-weight: 500;
}

.emotion-9 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.25;
  letter-spacing: 0px;
  font-weight: 500;
}

.emotion-9 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.7px;
  font-weight: 500;
}

.emotion-9 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0px;
  font-weight: 500;
}

.emotion-9 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.emotion-9 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 17px;
  line-height: 1.588235294117647;
  letter-spacing: 1.7px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.75;
  letter-spacing: 1.6px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.8333333333333333;
  letter-spacing: 0.82px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 2.4px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-9 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 56px;
  line-height: 1;
  letter-spacing: 2.8px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-9 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1;
  letter-spacing: 2.5px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-9 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 2.8px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-9 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 3.6px;
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-9 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1.1428571428571428;
  letter-spacing: 1.51px;
  text-transform: none;
  font-weight: 600;
}

.emotion-9 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.2;
  letter-spacing: 1.3px;
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-9 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 124px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 84px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.4px;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 100px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 80px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
}

.emotion-9 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 0.0625;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.5;
  letter-spacing: 1.2px;
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5555555555555556;
  letter-spacing: 0.36px;
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.1428571428571428;
  letter-spacing: 0.7px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-13 {
  width: 100%;
  height: 100%;
}

.emotion-15 {
  text-align: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 100%;
  width: 100%;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: flex-end;
  justify-content: flex-end;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  box-sizing: border-box;
  padding: 16px 24px;
  position: absolute;
  top: 0;
  left: 0;
}

.emotion-17 {
  position: relative;
}

.emotion-18 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 64px;
  line-height: 1.1111111111111112;
  padding: 23px 40px;
  width: 100%;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
}

.emotion-18:focus {
  outline: none;
}

.emotion-18>span {
  padding: 1px 0;
}

.emotion-18:hover,
.emotion-18:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-18:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
}

.emotion-27 {
  position: relative;
}

.emotion-28 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 60px;
  line-height: 1.25;
  padding: 22px 40px;
  width: 100%;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  background-color: #000000;
  color: #FFFFFF;
}

.emotion-28:focus {
  outline: none;
}

.emotion-28>span {
  padding: 1px 0;
}

.emotion-28>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-28:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-29 {
  display: inline-block;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-30 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: inline-block;
  margin-left: 0.65rem;
}

.emotion-30 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-30 svg path {
  fill: currentColor;
}

.emotion-30 svg rect {
  fill: currentColor;
}

.emotion-32 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0;
  position: absolute;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
}

.emotion-33 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: 1px solid #000000;
  border-left: 1px solid #333333;
  border-right: 1px solid #333333;
  height: 60px;
}

.emotion-33:last-child {
  border-bottom: 1px solid #333333;
}

.emotion-34 {
  cursor: pointer;
  display: block;
  padding: 1.375rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: 16px;
  text-align: center;
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: 100%;
}

.emotion-34:hover,
.emotion-34:focus {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
          height="460"
          width="314"
        >
          <div
            class="emotion-3"
            data-testid="product-card-image"
          >
            <img
              alt="image yellow"
              class="emotion-4"
              src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/BGDB_WMN_1ST_QUOTE_ISM_XL?fmt=webp"
            />
          </div>
        </div>
        <a
          class="emotion-5"
          href="http://gap.card"
          title="cardlink"
        />
        <div
          class="emotion-6"
        >
          <a
            class="emotion-5"
            href="http://gap.card"
            title="cardlink"
          />
          <div
            class="emotion-8"
          >
            <div
              class="emotion-9"
            >
              <div>
                <hr
                  aria-hidden="true"
                  style="display:block;border:0;height:8px;margin:0;background:transparent;"
                />
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                    style="color:#00FF00;font-style:italic"
                  >
                    Lightest Feel Fabric
                  </span>
                </p>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--headline-1"
                  >
                    Transcend
                  </span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
          height="460"
          width="314"
        >
          <div
            class="emotion-3"
            data-testid="product-card-image"
          >
            <canvas
              aria-label="an image"
              class="emotion-13"
              role="img"
            />
          </div>
        </div>
        <a
          class="emotion-5"
          href="http://gap.card"
          title="cardlink"
        />
        <div
          class="emotion-15"
        >
          <a
            class="emotion-5"
            href="http://gap.card"
            title="cardlink"
          />
          <div
            class="emotion-17"
          >
            <a
              class="emotion-18"
              color="dark"
              href="http://gap.com"
            >
              View the collection
            </a>
          </div>
        </div>
      </div>
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
          height="460"
          width="314"
        >
          <div
            class="emotion-3"
            data-testid="product-card-image"
          >
            <img
              alt="another image"
              class="emotion-4"
              src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/MaskGroup4238?fmt=webp"
            />
          </div>
        </div>
        <a
          class="emotion-5"
          href="http://gap.card"
          title="cardlink"
        />
        <div
          class="emotion-15"
        >
          <a
            class="emotion-5"
            href="http://gap.card"
            title="cardlink"
          />
          <div
            class="emotion-17"
          >
            <div
              class="emotion-27"
              data-testid="ctaDropdownWrapper"
            >
              <button
                aria-expanded="false"
                class="emotion-28"
                color="dark"
              >
                <span
                  class="emotion-29"
                  data-id="cta-dropdown-label"
                >
                  Browse sizes
                  <span
                    aria-hidden="true"
                    class="emotion-30"
                  >
                    <svg
                      viewBox="0 0 12.87 13"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M5.43 0h2v13h-2z"
                      />
                      <path
                        d="M12.87 5.5v2H0v-2z"
                      />
                    </svg>
                  </span>
                </span>
              </button>
              <div
                class="emotion-31"
              >
                <ul
                  aria-hidden="true"
                  class="emotion-32"
                >
                  <li
                    class="emotion-33"
                  >
                    <a
                      breakpoint="desktop"
                      class="emotion-34"
                      href="http://gap.com/1"
                      target="_self"
                    >
                      Women
                    </a>
                  </li>
                  <li
                    class="emotion-33"
                  >
                    <a
                      breakpoint="desktop"
                      class="emotion-34"
                      href="http://gap.com/2"
                      target="_self"
                    >
                      Girls
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`CategoryCardVariableGridAthleta snapshots should match for mobile 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: grid;
  gap: 0px;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  overflow-x: unset;
}

.emotion-1 {
  position: relative;
  z-index: 2;
  overflow: hidden;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 314/460;
}

.emotion-3 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-4 {
  width: 100%;
  aspect-ratio: 314/460;
  object-fit: cover;
}

.emotion-5 {
  cursor: pointer;
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 0;
  top: 0;
  left: 0;
}

.emotion-6 {
  text-align: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 100%;
  width: 100%;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  box-sizing: border-box;
  padding: 8px 12px;
  position: absolute;
  top: 0;
  left: 0;
}

.emotion-8 {
  position: initial;
}

.emotion-9 {
  margin-bottom: 20px;
  width: 100%;
  z-index: 0;
}

.emotion-9 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-9 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-9 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-9 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-9 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.emotion-9 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-9 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-9 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.625;
  letter-spacing: 0.8px;
  font-weight: 500;
}

.emotion-9 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0px;
  font-weight: 500;
}

.emotion-9 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.6px;
  font-weight: 500;
}

.emotion-9 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.6;
  letter-spacing: 0px;
  font-weight: 500;
}

.emotion-9 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.emotion-9 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 2;
  letter-spacing: 1.2px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 2;
  letter-spacing: 1.1px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.68px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 1.37px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-9 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 34px;
  line-height: 1;
  letter-spacing: 1.7px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-9 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-9 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 1.8px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-9 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.1818181818181819;
  letter-spacing: 2.2px;
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-9 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.375;
  letter-spacing: 0.8px;
  text-transform: none;
  font-weight: 600;
}

.emotion-9 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0.1px;
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-9 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
}

.emotion-9 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.75;
  letter-spacing: 0.32px;
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.7px;
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 13px;
  line-height: 1;
  letter-spacing: 0.97px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-15 {
  text-align: center;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 100%;
  width: 100%;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: flex-end;
  justify-content: flex-end;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  box-sizing: border-box;
  padding: 8px 12px;
  position: absolute;
  top: 0;
  left: 0;
}

.emotion-17 {
  position: relative;
}

.emotion-18 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 64px;
  line-height: 1.1111111111111112;
  padding: 23px 40px;
  width: 100%;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
}

.emotion-18:focus {
  outline: none;
}

.emotion-18>span {
  padding: 1px 0;
}

.emotion-18:hover,
.emotion-18:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-18:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
}

.emotion-27 {
  position: relative;
}

.emotion-28 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 50px;
  line-height: 1.2857142857142858;
  padding: 18px 40px;
  width: 100%;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  background-color: #000000;
  color: #FFFFFF;
}

.emotion-28:focus {
  outline: none;
}

.emotion-28>span {
  padding: 1px 0;
}

.emotion-28>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-28:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-29 {
  display: inline-block;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-30 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: inline-block;
  margin-left: 0.65rem;
}

.emotion-30 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-30 svg path {
  fill: currentColor;
}

.emotion-30 svg rect {
  fill: currentColor;
}

.emotion-32 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
}

.emotion-33 {
  box-sizing: border-box;
  width: auto;
  border-bottom: 1px solid #000000;
  border-left: 1px solid #333333;
  border-right: 1px solid #333333;
  height: 50px;
}

.emotion-33:last-child {
  border-bottom: 1px solid #333333;
}

.emotion-34 {
  cursor: pointer;
  display: block;
  padding: 1.125rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: 14px;
  text-align: center;
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: 100%;
}

.emotion-34:hover,
.emotion-34:focus {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
          height="460"
          width="314"
        >
          <div
            class="emotion-3"
            data-testid="product-card-image"
          >
            <img
              alt="image yellow"
              class="emotion-4"
              src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/BGDB_WMN_1ST_QUOTE_ISM_XL?fmt=webp"
            />
          </div>
        </div>
        <a
          class="emotion-5"
          href="http://gap.card"
          title="cardlink"
        />
        <div
          class="emotion-6"
        >
          <a
            class="emotion-5"
            href="http://gap.card"
            title="cardlink"
          />
          <div
            class="emotion-8"
          >
            <div
              class="emotion-9"
            >
              <div>
                <hr
                  aria-hidden="true"
                  style="display:block;border:0;height:8px;margin:0;background:transparent;"
                />
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--body-1"
                    style="color:#00FF00;font-style:italic"
                  >
                    Lightest Feel Fabric
                  </span>
                </p>
                <p
                  class="amp-cms--p"
                  style="text-align:left;"
                >
                  <span
                    class="amp-cms--headline-1"
                  >
                    Transcend
                  </span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
          height="460"
          width="314"
        >
          <div
            class="emotion-3"
            data-testid="product-card-image"
          >
            <img
              alt="an image"
              class="emotion-4"
              src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/man-business-suit?fmt=webp"
            />
          </div>
        </div>
        <a
          class="emotion-5"
          href="http://gap.card"
          title="cardlink"
        />
        <div
          class="emotion-15"
        >
          <a
            class="emotion-5"
            href="http://gap.card"
            title="cardlink"
          />
          <div
            class="emotion-17"
          >
            <a
              class="emotion-18"
              color="dark"
              href="http://gap.com"
            >
              View the collection
            </a>
          </div>
        </div>
      </div>
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
          height="460"
          width="314"
        >
          <div
            class="emotion-3"
            data-testid="product-card-image"
          >
            <img
              alt="another image"
              class="emotion-4"
              src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/MaskGroup4238?fmt=webp"
            />
          </div>
        </div>
        <a
          class="emotion-5"
          href="http://gap.card"
          title="cardlink"
        />
        <div
          class="emotion-15"
        >
          <a
            class="emotion-5"
            href="http://gap.card"
            title="cardlink"
          />
          <div
            class="emotion-17"
          >
            <div
              class="emotion-27"
              data-testid="ctaDropdownWrapper"
            >
              <button
                aria-expanded="false"
                class="emotion-28"
                color="dark"
              >
                <span
                  class="emotion-29"
                  data-id="cta-dropdown-label"
                >
                  Browse sizes
                  <span
                    aria-hidden="true"
                    class="emotion-30"
                  >
                    <svg
                      viewBox="0 0 12.87 13"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M5.43 0h2v13h-2z"
                      />
                      <path
                        d="M12.87 5.5v2H0v-2z"
                      />
                    </svg>
                  </span>
                </span>
              </button>
              <div
                class="emotion-31"
              >
                <ul
                  aria-hidden="true"
                  class="emotion-32"
                >
                  <li
                    class="emotion-33"
                  >
                    <a
                      breakpoint="mobile"
                      class="emotion-34"
                      href="http://gap.com/1"
                      target="_self"
                    >
                      Women
                    </a>
                  </li>
                  <li
                    class="emotion-33"
                  >
                    <a
                      breakpoint="mobile"
                      class="emotion-34"
                      href="http://gap.com/2"
                      target="_self"
                    >
                      Girls
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`CategoryCardVariableGridAthleta snapshots should match for mobile with 'carousel' scroll bar and circle cardShape 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: grid;
  gap: 0px;
  grid-template-columns: repeat(6, minmax(40%, 1fr));
  overflow-x: scroll;
}

.emotion-1 {
  position: relative;
  z-index: 2;
  overflow: hidden;
}

.emotion-2 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 204/204;
}

.emotion-3 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-4 {
  width: 100%;
  aspect-ratio: 204/204;
  object-fit: cover;
}

.emotion-5 {
  cursor: pointer;
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 0;
  top: 0;
  left: 0;
}

.emotion-6 {
  margin-bottom: 20px;
  width: 100%;
  z-index: 0;
}

.emotion-6 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-6 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-6 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-6 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-6 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.emotion-6 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-6 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-6 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.625;
  letter-spacing: 0.8px;
  font-weight: 500;
}

.emotion-6 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0px;
  font-weight: 500;
}

.emotion-6 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.6px;
  font-weight: 500;
}

.emotion-6 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.6;
  letter-spacing: 0px;
  font-weight: 500;
}

.emotion-6 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.emotion-6 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 2;
  letter-spacing: 1.2px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 2;
  letter-spacing: 1.1px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0.68px;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 1.37px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-6 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 34px;
  line-height: 1;
  letter-spacing: 1.7px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-6 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 30px;
  line-height: 1;
  letter-spacing: 0.3px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-6 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1;
  letter-spacing: 1.8px;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-6 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 22px;
  line-height: 1.1818181818181819;
  letter-spacing: 2.2px;
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-6 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.375;
  letter-spacing: 0.8px;
  text-transform: none;
  font-weight: 600;
}

.emotion-6 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.4285714285714286;
  letter-spacing: 0.1px;
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-6 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
}

.emotion-6 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.75;
  letter-spacing: 0.32px;
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5714285714285714;
  letter-spacing: 0.7px;
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 13px;
  line-height: 1;
  letter-spacing: 0.97px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-12 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 64px;
  line-height: 1.1111111111111112;
  padding: 23px 40px;
  width: 100%;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
}

.emotion-12:focus {
  outline: none;
}

.emotion-12>span {
  padding: 1px 0;
}

.emotion-12:hover,
.emotion-12:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-12:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
}

.emotion-18 {
  position: relative;
}

.emotion-19 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 50px;
  line-height: 1.2857142857142858;
  padding: 18px 40px;
  width: 100%;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  background-color: #000000;
  color: #FFFFFF;
}

.emotion-19:focus {
  outline: none;
}

.emotion-19>span {
  padding: 1px 0;
}

.emotion-19>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-19:hover {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-20 {
  display: inline-block;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-21 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: inline-block;
  margin-left: 0.65rem;
}

.emotion-21 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-21 svg path {
  fill: currentColor;
}

.emotion-21 svg rect {
  fill: currentColor;
}

.emotion-23 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
}

.emotion-24 {
  box-sizing: border-box;
  width: auto;
  border-bottom: 1px solid #000000;
  border-left: 1px solid #333333;
  border-right: 1px solid #333333;
  height: 50px;
}

.emotion-24:last-child {
  border-bottom: 1px solid #333333;
}

.emotion-25 {
  cursor: pointer;
  display: block;
  padding: 1.125rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: 14px;
  text-align: center;
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: 100%;
}

.emotion-25:hover,
.emotion-25:focus {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
          height="204"
          width="204"
        >
          <div
            class="emotion-3"
            data-testid="product-card-image"
          >
            <img
              alt="image yellow"
              class="emotion-4"
              src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/BGDB_WMN_1ST_QUOTE_ISM_XL?fmt=webp"
            />
          </div>
        </div>
        <a
          class="emotion-5"
          href="http://gap.card"
          title="cardlink"
        />
        <div
          class="emotion-6"
        >
          <div>
            <hr
              aria-hidden="true"
              style="display:block;border:0;height:8px;margin:0;background:transparent;"
            />
            <p
              class="amp-cms--p"
              style="text-align:left;"
            >
              <span
                class="amp-cms--body-1"
                style="color:#00FF00;font-style:italic"
              >
                Lightest Feel Fabric
              </span>
            </p>
            <p
              class="amp-cms--p"
              style="text-align:left;"
            >
              <span
                class="amp-cms--headline-1"
              >
                Transcend
              </span>
            </p>
          </div>
        </div>
      </div>
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
          height="204"
          width="204"
        >
          <div
            class="emotion-3"
            data-testid="product-card-image"
          >
            <img
              alt="an image"
              class="emotion-4"
              src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/man-business-suit?fmt=webp"
            />
          </div>
        </div>
        <a
          class="emotion-5"
          href="http://gap.card"
          title="cardlink"
        />
        <a
          class="emotion-12"
          color="dark"
          href="http://gap.com"
        >
          View the collection
        </a>
      </div>
      <div
        class="emotion-1"
      >
        <div
          class="emotion-2"
          height="204"
          width="204"
        >
          <div
            class="emotion-3"
            data-testid="product-card-image"
          >
            <img
              alt="another image"
              class="emotion-4"
              src="https://2hmc8flz4ofg1dy89cc38f2xm.staging.bigcontent.io/i/athleta/MaskGroup4238?fmt=webp"
            />
          </div>
        </div>
        <a
          class="emotion-5"
          href="http://gap.card"
          title="cardlink"
        />
        <div
          class="emotion-18"
          data-testid="ctaDropdownWrapper"
        >
          <button
            aria-expanded="false"
            class="emotion-19"
            color="dark"
          >
            <span
              class="emotion-20"
              data-id="cta-dropdown-label"
            >
              Browse sizes
              <span
                aria-hidden="true"
                class="emotion-21"
              >
                <svg
                  viewBox="0 0 12.87 13"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M5.43 0h2v13h-2z"
                  />
                  <path
                    d="M12.87 5.5v2H0v-2z"
                  />
                </svg>
              </span>
            </span>
          </button>
          <div
            class="emotion-22"
          >
            <ul
              aria-hidden="true"
              class="emotion-23"
            >
              <li
                class="emotion-24"
              >
                <a
                  breakpoint="mobile"
                  class="emotion-25"
                  href="http://gap.com/1"
                  target="_self"
                >
                  Women
                </a>
              </li>
              <li
                class="emotion-24"
              >
                <a
                  breakpoint="mobile"
                  class="emotion-25"
                  href="http://gap.com/2"
                  target="_self"
                >
                  Girls
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</DocumentFragment>
`;

exports[`CategoryCardVariableGridAthleta snapshots should match for showHideBasedOnScreenSize behavior hides desktop 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  />
</DocumentFragment>
`;

exports[`CategoryCardVariableGridAthleta snapshots should match for showHideBasedOnScreenSize behavior hides mobile 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  />
</DocumentFragment>
`;
