// @ts-nocheck
'use client';
import React, { useCallback } from 'react';
import CTADropdown, { CTADropdownOption } from '../../subcomponents/CTADropdown';
import { RichText } from '../../subcomponents/RichText';
import { CtaButton } from '../../subcomponents/CTAButton';
import { useViewportIsXLarge } from '../../../hooks/useViewportIsLarge';
import { useShowHideBasedOnScreenSize } from '../../../hooks/useShowHideBasedOnScreenSize';
import {
  AppearanceProps,
  Card,
  CategoryCardVariableGridContentType,
  CategoryCardVariableGridItemProps,
  DropdownItemProps,
} from '../../components/CategoryCardVariableGrid/types';
import CategoryCardVariableGridContainer from '../../components/CategoryCardVariableGrid/Container';
import CategoryCardVariableGridItem from '../../components/CategoryCardVariableGrid/Item';

/**
 * @deprecated The CategoryCardVariableGrid component is deprecated and should not be used for new development.
 * Please migrate to alternative navigation or grid solutions.
 */

export const rectAspectRatio = '314:460';

export const aspectRatio = (columns: number, cardShape: AppearanceProps['cardShape']) => {
  const GAP = 16;
  const width = 1304;
  let ratio = '1:1';
  if (cardShape === 'rectangle') {
    ratio = rectAspectRatio;
  } else {
    const totalGapWidth = GAP * (columns - 1);

    const side = (width - totalGapWidth) / columns;
    ratio = `${side}:${side}`;
  }
  return ratio;
};

interface ItemProps {
  appearance: AppearanceProps;
  aspectRatio: CategoryCardVariableGridItemProps['aspectRatio'];
  card: Card;
}

const Item = ({ appearance, aspectRatio, card }: ItemProps) => {
  const { image, url, style, dropdownCta = '', dropdown = [] as DropdownItemProps[], cta = { label: '', value: '' }, text = '' } = card;

  const isDesktopVP = useViewportIsXLarge();

  const renderContent = useCallback(() => {
    switch (style) {
      case 'cta':
        return <CtaButton alignment='center' ctaButton={cta} ctaButtonStyling={appearance.ctaButtonStyling} ctaSize='xl' fullWidth isDesktop={isDesktopVP} />;
      case 'dropdown':
        return (
          <CTADropdown
            heading={dropdownCta}
            itemContainerStyles={{ width: '100%' }}
            items={dropdown.map(
              item =>
                ({
                  value: item.cta.value,
                  label: item.cta.label,
                }) as CTADropdownOption
            )}
            itemStyle={{ width: '100%' }}
          />
        );
      default:
        return <RichText css={{ marginBottom: '20px', width: '100%', zIndex: 0 }} isDesktop={isDesktopVP} style={{}} text={text} />;
    }
  }, [appearance, style, cta, dropdownCta, dropdown, text, isDesktopVP]);

  const verticalAlignment = style === 'label' ? 'middle' : 'bottom';

  return (
    <CategoryCardVariableGridItem
      appearance={appearance}
      aspectRatio={aspectRatio}
      card={card}
      image={image}
      paddingOnElement={isDesktopVP ? '16px 24px ' : '8px 12px'}
      url={url}
      verticalAlignment={verticalAlignment}
    >
      {renderContent()}
    </CategoryCardVariableGridItem>
  );
};
const CategoryCardVariableGridAthleta = ({ categoryCard, cardLayout, cardsVisible, appearance }: CategoryCardVariableGridContentType): JSX.Element | null => {
  const isDesktopVP = useViewportIsXLarge();
  const showHideBasedOnScreenSize = appearance.showHideBasedOnScreenSize;
  if (!useShowHideBasedOnScreenSize(showHideBasedOnScreenSize)) return null;

  const cards = categoryCard;
  const { desktopLayout, mobileLayout, quantity } = cardLayout;
  const scrollOnMobile = mobileLayout === 'carousel' && !isDesktopVP;

  let columns = 4;

  if (isDesktopVP) {
    const [columnString] = desktopLayout.split('x');
    columns = parseInt(columnString, 10);
  } else if (mobileLayout === 'carousel') {
    columns = quantity;
  } else {
    const [columnString] = mobileLayout.split('x');
    columns = parseInt(columnString, 10);
  }

  return (
    <CategoryCardVariableGridContainer
      cardsVisible={cardsVisible}
      columns={columns}
      gap={0}
      scrollOnMobile={scrollOnMobile}
      showHideBasedOnScreenSize={appearance.showHideBasedOnScreenSize}
    >
      {cards.map(card => (
        <Item key={JSON.stringify(card.image)} appearance={appearance} aspectRatio={aspectRatio(columns, appearance.cardShape)} card={card} />
      ))}
    </CategoryCardVariableGridContainer>
  );
};
export default CategoryCardVariableGridAthleta;
