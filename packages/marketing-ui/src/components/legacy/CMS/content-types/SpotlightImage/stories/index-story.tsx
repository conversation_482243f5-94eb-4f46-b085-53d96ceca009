// @ts-nocheck
'use client';
import React from 'react';
import { Story } from '@storybook/react';
import README from '../README.mdx';
import { SpotlightImageContentType } from '../types';
import SpotlightImage from '../index';
import { spotlightFullBleedData, spotlightInsetData, spotlightFullBleedLinkWrapperData } from '../__fixtures__/test-data';
import { NullJSX } from '../../../../stories/story-helpers';

export default {
  title: 'Common/JSON Components (Marketing)/Content-Types/SpotlightImage',
  parameters: {
    docs: {
      page: README,
      description: {
        component:
          '⚠️ **DEPRECATED**: This content type is deprecated and will be removed in a future release. Reason: Athleta no longer uses SpotlightImage. Do not use in new development. See https://gapinc.atlassian.net/browse/FUI-5434 for removal tracking.',
      },
    },
    knobs: {
      disabled: true,
    },
    eyes: { include: false },
  },
  tags: ['visual:check', 'exclude-gap', 'exclude-br', 'exclude-on'],
};

export const Inset: Story<{ data: SpotlightImageContentType }> = props => {
  const content = <SpotlightImage {...props.data} />;

  return (
    <>
      <style>
        {`
  .sb-main-padded{
    padding: 0 !important;
  }`}
      </style>
      <NullJSX content={content}>
        <p>Unsupported by brand</p>
      </NullJSX>
    </>
  );
};
Inset.args = {
  data: spotlightInsetData,
};

export const FullBleed: Story<{ data: SpotlightImageContentType }> = props => {
  const content = <SpotlightImage {...props.data} />;

  return (
    <>
      <style>
        {`
  .sb-main-padded{
    padding: 0 !important;
  }`}
      </style>
      <NullJSX content={content}>
        <p>Unsupported by brand</p>
      </NullJSX>
    </>
  );
};
FullBleed.args = {
  data: spotlightFullBleedData,
};

export const LinkWrapper: Story<{ data: SpotlightImageContentType }> = props => {
  const content = <SpotlightImage {...props.data} />;

  return (
    <>
      <style>
        {`
  .sb-main-padded{
    padding: 0 !important;
  }`}
      </style>
      <NullJSX content={content}>
        <p>Unsupported by brand</p>
      </NullJSX>
    </>
  );
};
LinkWrapper.args = {
  data: spotlightFullBleedLinkWrapperData,
};
