// @ts-nocheck
'use client';
import { SpotlightImageContentType } from './types';
import withBrandComponent from '../withBrandComponent';
import { AthletaSpotlightImage as at } from './SpotlightImage.at';

export type SpotlightSchema = 'https://cms.gap.com/schema/content/v1/spotlight.json';
/**
 * @deprecated  This content type is deprecated and will be removed in a future release.
 * - Reason: Athleta no longer uses SpotlightImage. Do not use in new development.
 * - See https://gapinc.atlassian.net/browse/FUI-5434 for removal tracking.
 */
export default withBrandComponent<SpotlightImageContentType>({
  at,
});
