/\*\*

- @deprecated This content type is deprecated and will be removed in a future release.
- - Reason: Athleta no longer uses SpotlightImage. Do not use in new development.
- - See https://gapinc.atlassian.net/browse/FUI-5434 for removal tracking.
    \*/

## SpotlightImage, aka Spotlight

### What is `SpotlightImage`?

- It is a homepage spotlight containing an image that displays marketing content for Athleta.
- It is a JSON-configurable component that accepts JSON keys generated by our Content Management System, Amplience. Note that this format differs from earlier JSON formats.

#### Default Behavior

- `SpotlightImage` creates a pre-styled banner with a hero `image`, an `icon`, a `rich text`, `CTA button` and a `Details Link` that can be either a HTML modal or a pemole code.
- `SpotlightImage` has 3 background types `solid`, `gradient` and `image`.
- All elements of `SpotlightImage` scale infinitely, except for CTAs and DetailsButton, which have a static size.
- The desktop 100% scaling point is 1280 by default. This is the standard for the Typography system.
- `verticalAlignment` allows you to position the header content (the icon, cta and upper rich text) at the top, middle, or bottom of the content type. It is only available on `Spotlight`'s fullBleed variation.

### Cautions

- Be aware that although `SpotlightImage`'s default scaling point for desktop is 1280, we have set a custom `desktopScalingPoint` of 1440 in the Athleta version of the content type per UX request. The result is that all scaling Rich Text is 11% smaller in desktop.

### Component Props

| Prop          | Type     | Description |
| ------------- | -------- | ----------- |
| general       | `object` |             |
| image         | `object` |             |
| imageOverlays | `object` |             |
| content       | `object` |             |

#### General Props

| Prop             | Type                           | Description                                                                                                                                 |
| ---------------- | ------------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------- |
| layout           | `inset or fullBleed`           | `Defines the component layout`                                                                                                              |
| background       | `BackgroundTypeExtensionValue` | [`gradient`](https://github.gapinc.com/ecomfrontend/marketing-ui/blob/main/src/CMS/types/amplience.ts#L39:L43), `solid`, or `advance-image` |
| mobileBackground | `BackgroundTypeExtensionValue` | [`gradient`](https://github.gapinc.com/ecomfrontend/marketing-ui/blob/main/src/CMS/types/amplience.ts#L39:L43), `solid`, or `advance-image` |

#### Image Props

| Prop                | Type                         | Description                                          |
| ------------------- | ---------------------------- | ---------------------------------------------------- |
| mainImage           | `AdvanceImageExtensionValue` | `the component main image`                           |
| mobileImageOverride | `AdvanceImageExtensionValue` | `the mobile image that will override the main image` |

#### Image Overlays Props

| Prop                | Type     | Description                                                                     |
| ------------------- | -------- | ------------------------------------------------------------------------------- |
| handle              | `object` | `Optional overlay RichText`                                                     |
| detailsLink         | `object` | `Optional details button and prefix`                                            |
| useGradientBackfill | `object` | `Optionally can set the overlay gradient for spotlightText in fullBleed layout` |

#### Content Props

| Prop                       | Type                         | Description                                                                                                                            |
| -------------------------- | ---------------------------- | -------------------------------------------------------------------------------------------------------------------------------------- |
| contentJustification       | `string`                     | `left, center, right`                                                                                                                  |
| mobileContentJustification | `string`                     | `left, center, right`                                                                                                                  |
| verticalAlignment          | `string`                     | `top, middle, bottom`                                                                                                                  |
| desktopIconSize            | `string`                     | `Size for desktop icon`                                                                                                                |
| mobileIconSize             | `string`                     | `Size for mobile icon`                                                                                                                 |
| icon                       | `AdvanceImageExtensionValue` | `TBD`                                                                                                                                  |
| mobileIconOverride         | `AdvanceImageExtensionValue` | `TBD`                                                                                                                                  |
| spotlightText              | `SpotlightImageContent`      | [`spotlightText`](https://github.gapinc.com/ecomfrontend/marketing-ui/blob/main/src/CMS/content-types/SpotlightImage/types.ts#L24:L30) |
| ctaButtons                 | `SpotlightImageCta`          | [cta](https://github.gapinc.com/ecomfrontend/marketing-ui/blob/main/src/CMS/content-types/SpotlightImage/types.ts#L9:L18)              |
| mobileContentJustification | `string`                     | `left, center, right`                                                                                                                  |

#### Content Header Props

| Prop        | Type                           | Description                                                                                                                              |
| ----------- | ------------------------------ | ---------------------------------------------------------------------------------------------------------------------------------------- |
| background  | `BackgroundTypeExtensionValue` | [`gradient`](https://github.gapinc.com/ecomfrontend/marketing-ui/blob/main/src/CMS/types/amplience.ts#L39:L43)                           |
| content     | `SpotlightImageContent`        | [`spotlightText`](https://github.gapinc.com/ecomfrontend/marketing-ui/blob/main/src/CMS/content-types/SpotlightImage/types.ts#L24:L30)\* |
| isFullBleed | `boolean`                      | checks layout prop for value `fullBleed`                                                                                                 |

#### \*_Optional Props - content accepts 2 additional props for overriding the gradient style/colors for a particular brand (addresses corner cases where the default value needs to be overridden -- option currently not available to the author):_

| Prop                  | Type                           | Description                                                                                                    |
| --------------------- | ------------------------------ | -------------------------------------------------------------------------------------------------------------- |
| `useGradientOverride` | `boolean`                      | overrides `useGradientBackfill` for `spotlightText` object in fullBleed layout                                 |
| `background`          | `BackgroundTypeExtensionValue` | [`gradient`](https://github.gapinc.com/ecomfrontend/marketing-ui/blob/main/src/CMS/types/amplience.ts#L39:L43) |

### How to use

```js
const SpotlightImageData = {
  _meta: {
    name: 'Spotlight - Alina',
    schema: 'https://cms.gap.com/schema/content/v1/spotlight.json',
    deliveryId: '7599ee6c-839d-4ea3-8340-f0f373dab9e8',
  },
  general: {
    layout: 'inset',
    background: {
      type: 'solid',
      color: '#FFFABC',
    },
    mobileBackground: [
      {
        mobileBackground: {
          type: 'solid',
          color: '#FFFDEF',
        },
      },
    ],
  },
  image: {
    mainImage: [
      {
        image: {
          _meta: {
            schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
          },
          id: 'b12cb0b6-301b-4d88-924d-374a7f5a1df2',
          name: 'SP221625_Kids_KGShortsShopEBB_midi',
          endpoint: 'gap',
          defaultHost: 'pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io',
        },
        altText: 'Child wearing denim shorts and bright top with fruit illustration',
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
        fliph: false,
        flipv: false,
        enableChroma: false,
        chromaQuality: 80,
      },
    ],
    mobileImageOverride: [
      {
        image: {
          _meta: {
            schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
          },
          id: '74a74692-82cb-44e1-a90d-d0026581a4f0',
          name: 'SP221625_Kids_KGShortsShopEBB_midi_hover',
          endpoint: 'gap',
          defaultHost: 'pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io',
        },
        altText: 'Child wearing denim shorts and flower-shaped sunglasses',
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
        fliph: false,
        flipv: false,
        enableChroma: false,
        chromaQuality: 80,
      },
    ],
  },
  imageOverlays: {
    handle: {
      placement: 'left',
    },
    detailsLink: {
      label: 'Details',
    },
    useGradientBackfill: true,
  },
  content: {
    contentJustification: 'right',
    verticalAlignment: 'top',
    mobileContentJustification: 'center',
    icon: [
      {
        image: {
          _meta: {
            schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
          },
          id: 'c1fab4c1-86a2-4296-8853-da0e334d7763',
          name: 'Gap_BOPIS_car-icon',
          endpoint: 'gap',
          defaultHost: 'pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io',
        },
        altText: '',
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
        fliph: false,
        flipv: false,
        enableChroma: false,
        chromaQuality: 80,
      },
    ],
    mobileIconOverride: [
      {
        image: {
          _meta: {
            schema: 'http://bigcontent.io/cms/schema/v1/core#/definitions/image-link',
          },
          id: '15587c86-1e59-46b9-852d-d318dd80b190',
          name: 'Gap_BOPIS_bag-icon',
          endpoint: 'gap',
          defaultHost: 'pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io',
        },
        altText: '',
        variations: [
          {
            variation: 'desktop',
          },
          {
            variation: 'mobile',
          },
        ],
        fliph: false,
        flipv: false,
        enableChroma: false,
        chromaQuality: 80,
      },
    ],
    spotlightText: {
      useGradientBackfill: false,
      defaultText: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Spotlight Text</span></p>',
      mobileOverride: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Mobile Override Text</span></p>',
    },
    ctaButtons: {
      cta1: {
        cta: {
          label: 'First CTA',
          value: '1',
        },
        buttonStyle: {
          buttonStyle: 'border',
          buttonColor: 'dark',
        },
      },
      cta2: {
        cta: {
          label: 'Second CTA',
          value: '2',
        },
        buttonStyle: {
          buttonStyle: 'chevron',
          buttonColor: 'custom',
          primaryHex: '#f0f',
          secondaryHex: '#ccc',
        },
      },
    },
  },
};
```
