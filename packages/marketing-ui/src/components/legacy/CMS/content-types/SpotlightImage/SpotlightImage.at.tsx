// @ts-nocheck
'use client';
import React from 'react';
import { SpotlightImageContentType } from './types';
import { SpotlightImage } from '../../components/spotlight-image';
import { DESKTOP_SCALE_POINT } from '../../global/constants';
import { ShowHideWrapper } from '../../subcomponents/ShowHideWrapper';
/**
 * @deprecated  This content type is deprecated and will be removed in a future release.
 * - Reason: Athleta no longer uses SpotlightImage. Do not use in new development.
 * - See https://gapinc.atlassian.net/browse/FUI-5434 for removal tracking.
 */
export const AthletaSpotlightImage = ({ content, general, image, imageOverlays }: SpotlightImageContentType): JSX.Element => (
  <ShowHideWrapper breakpoint='large' showHideBasedOnScreenSize={general.showHideBasedOnScreenSize}>
    <SpotlightImage content={content} desktopScalingPoint={DESKTOP_SCALE_POINT} general={general} image={image} imageOverlays={imageOverlays} />
  </ShowHideWrapper>
);
