// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`AT FlexibleBannerHP renders a banner with a carousel, given carousel media data 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  box-sizing: border-box;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  aspect-ratio: 4/1;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 20px;
  box-sizing: border-box;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  aspect-ratio: 4/1;
  height: 100%;
  position: relative;
  width: 100%;
  overflow: clip;
}

.emotion-2 {
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-2 .slick-list {
  overflow: hidden;
}

.emotion-2 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-2 button.slick-next.slick-arrow.slick-next,
.emotion-2 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: undefined;
  background: #00000080;
  opacity: 60%;
  height: 44px;
  width: 44px;
}

.emotion-2 button.slick-next.slick-arrow.slick-next span svg path,
.emotion-2 button.slick-prev.slick-arrow.slick-prev span svg path {
  fill: #FFFFFF80;
}

.emotion-2 button.slick-next.slick-arrow.slick-next:hover,
.emotion-2 button.slick-prev.slick-arrow.slick-prev:hover {
  background: #00000080;
}

.emotion-2 button.slick-next.slick-arrow.slick-next:focus,
.emotion-2 button.slick-prev.slick-arrow.slick-prev:focus {
  background: #00000080;
}

.emotion-2 button.slick-next.slick-arrow.slick-next>span,
.emotion-2 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-2 button.slick-next.slick-arrow.slick-next svg,
.emotion-2 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-2 button.slick-next.slick-arrow.slick-next:focus-visible,
.emotion-2 button.slick-prev.slick-arrow.slick-prev:focus-visible {
  outline: auto;
}

.emotion-2 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-2 .slick-disabled {
  display: none!important;
}

.emotion-2 .slick-next {
  left: calc(100% - 44px);
}

.emotion-2 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-2 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-3 {
  position: relative;
}

.emotion-3 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-3 .slick-slider .slick-track,
.emotion-3 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-3 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-3 .slick-list:focus {
  outline: none;
}

.emotion-3 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-3 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-3 .slick-track:before,
.emotion-3 .slick-track:after {
  display: table;
  content: "";
}

.emotion-3 .slick-track:after {
  clear: both;
}

.emotion-3 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-3 .slick-slide img {
  display: block;
}

.emotion-3 .slick-slide.slick-loading img {
  display: none;
}

.emotion-3 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-3 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-3 .slick-initialized .slick-slide,
.emotion-3 .slick-vertical .slick-slide {
  display: block;
}

.emotion-3 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-3 .slick-loading .slick-track,
.emotion-3 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-3 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-3 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-3 .slick-prev,
.emotion-3 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-3 .slick-prev:hover,
.emotion-3 .slick-next:hover,
.emotion-3 .slick-prev:focus,
.emotion-3 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-3 .slick-prev.slick-disabled,
.emotion-3 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-3 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-3 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-3 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-3 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-3 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-3 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-3 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-3 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-3 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-3 .slick-dots li button:hover,
.emotion-3 .slick-dots li button:focus {
  outline: none;
}

.emotion-3 .slick-dots li button:hover:before,
.emotion-3 .slick-dots li button:focus:before,
.emotion-3 .slick-dots li button:hover:before,
.emotion-3 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-3 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-4 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-4 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 4/1;
}

.emotion-6 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-7 {
  width: 100%;
  aspect-ratio: 4/1;
  object-fit: cover;
}

.emotion-8 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-9:focus-visible {
  outline-offset: -5px;
  outline: auto;
}

.emotion-9:focus-visible video {
  outline-offset: -5px;
  outline: auto;
  z-index: -1;
}

.emotion-10 {
  width: 100%;
  background: none;
  height: 100%;
}

.emotion-12 {
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: 1.3888888888888888vw 2.7777777777777777vw;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  text-align: center;
}

.emotion-14 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  height: 100%;
  padding: 0px;
  gap: 1.3888888888888888vw;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-16 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-17 {
  height: auto;
  max-width: 100%;
}

.emotion-18 {
  min-height: 7.291666666666667vw;
  max-height: 7.291666666666667vw;
}

.emotion-19 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-19 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-19 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-19 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-19 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.6944444444444444vw);
  line-height: 1.5;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 500;
}

.emotion-19 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-19 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-19 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-19 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-19 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-19 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-19 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.25vw);
  line-height: 1.5555555555555556;
  letter-spacing: 0.0625vw;
  font-weight: 500;
}

.emotion-19 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.1111111111111112vw);
  line-height: 1.25;
  letter-spacing: 0vw;
  font-weight: 500;
}

.emotion-19 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.9722222222222222vw);
  line-height: 1.5714285714285714;
  letter-spacing: 0.04861111111111111vw;
  font-weight: 500;
}

.emotion-19 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.8333333333333334vw);
  line-height: 1.5;
  letter-spacing: 0vw;
  font-weight: 500;
}

.emotion-19 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.6944444444444444vw);
  line-height: 1.5;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 500;
}

.emotion-19 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.1805555555555556vw);
  line-height: 1.588235294117647;
  letter-spacing: 0.11805555555555555vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-19 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, 1.1111111111111112vw);
  line-height: 1.75;
  letter-spacing: 0.1111111111111111vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-19 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.8333333333333334vw);
  line-height: 1.8333333333333333;
  letter-spacing: 0.056944444444444436vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-19 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 4.166666666666666vw);
  line-height: 1;
  letter-spacing: 0.16666666666666666vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-19 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 3.888888888888889vw);
  line-height: 1;
  letter-spacing: 0.19444444444444445vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-19 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 3.4722222222222223vw);
  line-height: 1;
  letter-spacing: 0.1736111111111111vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-19 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 2.7777777777777777vw);
  line-height: 1;
  letter-spacing: 0.19444444444444445vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-19 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 2.5vw);
  line-height: 1;
  letter-spacing: 0.25vw;
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-19 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.9444444444444444vw);
  line-height: 1.1428571428571428;
  letter-spacing: 0.1048611111111111vw;
  text-transform: none;
  font-weight: 600;
}

.emotion-19 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.3888888888888888vw);
  line-height: 1.2;
  letter-spacing: 0.09027777777777779vw;
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-19 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 8.61111111111111vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-19 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 5.833333333333333vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-19 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 4.444444444444445vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-19 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 2.361111111111111vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-19 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-19 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-19 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-19 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 6.944444444444445vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-19 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 5.555555555555555vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
}

.emotion-19 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
}

.emotion-19 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.6666666666666667vw);
  line-height: 1.5;
  letter-spacing: 0.08333333333333333vw;
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-19 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.25vw);
  line-height: 1.5555555555555556;
  letter-spacing: 0.025vw;
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-19 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 0.9722222222222222vw);
  line-height: 1.1428571428571428;
  letter-spacing: 0.04861111111111111vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-21 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-column-gap: 2.083333333333333vw;
  column-gap: 2.083333333333333vw;
}

.emotion-22 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  text-align: center;
  min-width: 3.4722222222222223vw;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-23 {
  height: 3.4722222222222223vw;
}

.emotion-24 {
  line-height: 150%;
  font-size: max(10px, 0.8333333333333334vw);
  padding-top: 0.5555555555555556vw;
  max-width: 5.902777777777778vw;
  font-weight: 500;
  text-transform: uppercase;
  color: #111;
}

.emotion-27 {
  line-height: 150%;
  font-size: max(10px, 0.8333333333333334vw);
  padding-top: 0.5555555555555556vw;
  max-width: 5.902777777777778vw;
  font-weight: 500;
  text-transform: uppercase;
  color: #FFFFFF;
}

.emotion-33 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 64px;
  line-height: 1.1111111111111112;
  padding: 23px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  pointer-events: all;
  min-height: auto;
}

.emotion-33:focus {
  outline: none;
}

.emotion-33>span {
  padding: 1px 0;
}

.emotion-33:hover,
.emotion-33:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-33:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
}

.emotion-36 {
  width: 100%;
  height: 100%;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <article
      class="emotion-0"
    >
      <section
        class="emotion-1"
      >
        <div
          style="width: 100%;"
        >
          <nav
            class="emotion-2"
          >
            <div
              class="emotion-3"
            >
              <div
                class="slick-slider slick-initialized"
                dir="ltr"
              >
                <button
                  aria-label="Previous"
                  class="slick-prev slick-arrow slick-prev slick-disabled"
                  data-role="none"
                  disabled=""
                >
                  <span
                    aria-hidden="true"
                    class="emotion-4"
                  >
                    <svg
                      viewBox="0 0 26.78 17.63"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M0 1.95l13 15.68L26.79 1.98 24.53 0 13.08 13.01 2.3.03z"
                        fill="#000000"
                      />
                    </svg>
                  </span>
                </button>
                <div
                  class="slick-list"
                >
                  <div
                    class="slick-track"
                    style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                  >
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active slick-current"
                      data-index="0"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          style="width: 100%; display: inline-block;"
                          tabindex="-1"
                        >
                          <div
                            class="emotion-5"
                            height="1"
                            width="4"
                          >
                            <div
                              class="emotion-6"
                              data-testid="product-card-image"
                            >
                              <img
                                alt=""
                                class="emotion-7"
                                src="https://1d9xxafvh577y12766tt2karod.staging.bigcontent.io/i/athleta/ATG_DP_XL_Testing2?fmt=webp"
                              />
                            </div>
                            <div
                              class="emotion-8"
                            >
                              <a
                                aria-label="frame 1 test banner"
                                class="emotion-9"
                                data-testid="conditional-link"
                                href="#linkwrapper1"
                                style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                target="_self"
                              />
                              <div
                                class="emotion-10"
                                data-testid="gradient-wrapper"
                              >
                                <a
                                  aria-label="frame 1 test banner"
                                  class="emotion-9"
                                  data-testid="conditional-link"
                                  href="#linkwrapper1"
                                  style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                  target="_self"
                                />
                                <div
                                  class="emotion-12"
                                >
                                  <a
                                    aria-label="frame 1 test banner"
                                    class="emotion-9"
                                    data-testid="conditional-link"
                                    href="#linkwrapper1"
                                    style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                    target="_self"
                                  />
                                  <div
                                    class="emotion-14"
                                  >
                                    <a
                                      aria-label="frame 1 test banner"
                                      class="emotion-9"
                                      data-testid="conditional-link"
                                      href="#linkwrapper1"
                                      style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                      target="_self"
                                    >
                                      <div
                                        class="emotion-16"
                                      >
                                        <div
                                          class="emotion-17"
                                          data-testid="advance-image-container-test-id"
                                        >
                                          <img
                                            alt="Default Icon"
                                            class="emotion-18"
                                            maxminheight="7.291666666666667vw"
                                            src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                                          />
                                        </div>
                                      </div>
                                      <div
                                        style="text-align: center;"
                                      >
                                        <div
                                          class="emotion-19"
                                        >
                                          <div>
                                            <p
                                              class="amp-cms--p"
                                            >
                                              <span
                                                class="amp-cms--body-2"
                                              >
                                                Introducing
                                              </span>
                                            </p>
                                            <p
                                              class="amp-cms--p"
                                            >
                                              <span
                                                class="amp-cms--headline-5"
                                              >
                                                THE ULTIMATE II TIGHT
                                              </span>
                                            </p>
                                            <p
                                              class="amp-cms--p"
                                            >
                                              <span
                                                class="amp-cms--body-3"
                                              >
                                                Our #1 sweat ready style just got a major upgrade. Lorem ipsum dolor sit amet.
                                              </span>
                                            </p>
                                            <hr
                                              aria-hidden="true"
                                              style="display:block;border:0;height:8px;margin:0;background:transparent;"
                                            />
                                          </div>
                                        </div>
                                      </div>
                                      <div
                                        class="emotion-16"
                                      >
                                        <div
                                          class="emotion-21"
                                        >
                                          <div
                                            class="emotion-22"
                                          >
                                            <img
                                              alt=""
                                              class="emotion-23"
                                              src="https://athleta.a.bigcontent.io/v1/static/WATER-RESISTANT_dark"
                                            />
                                            <span
                                              class="emotion-24"
                                            >
                                              Water-Resistant
                                            </span>
                                          </div>
                                          <div
                                            class="emotion-22"
                                          >
                                            <img
                                              alt=""
                                              class="emotion-23"
                                              src="https://athleta.a.bigcontent.io/v1/static/WATER-REPELLENT_light"
                                            />
                                            <span
                                              class="emotion-27"
                                            >
                                              Water-Repellent
                                            </span>
                                          </div>
                                          <div
                                            class="emotion-22"
                                          >
                                            <img
                                              alt=""
                                              class="emotion-23"
                                              src="https://athleta.a.bigcontent.io/v1/static/LOW-WATER-USAGE_light"
                                            />
                                            <span
                                              class="emotion-27"
                                            >
                                              Low Water Usage
                                            </span>
                                          </div>
                                        </div>
                                      </div>
                                    </a>
                                    <div
                                      class="emotion-16"
                                    >
                                      <a
                                        aria-label="frame 1 test banner"
                                        class="emotion-9"
                                        data-testid="conditional-link"
                                        href="#linkwrapper1"
                                        style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                        target="_self"
                                      />
                                      <a
                                        class="emotion-33"
                                        color="dark"
                                        href="#button1"
                                      >
                                        Frame 1 CTA
                                      </a>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="1"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          style="width: 100%; display: inline-block;"
                          tabindex="-1"
                        >
                          <div
                            class="emotion-5"
                            height="1"
                            width="4"
                          >
                            <div
                              class="emotion-6"
                              data-testid="product-card-image"
                            >
                              <canvas
                                aria-label=""
                                class="emotion-36"
                                role="img"
                              />
                            </div>
                            <div
                              class="emotion-8"
                            >
                              <a
                                aria-label="frame 2 test banner"
                                class="emotion-9"
                                data-testid="conditional-link"
                                href="#linkwrapper2"
                                style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                target="_self"
                              />
                              <div
                                class="emotion-10"
                                data-testid="gradient-wrapper"
                              >
                                <a
                                  aria-label="frame 2 test banner"
                                  class="emotion-9"
                                  data-testid="conditional-link"
                                  href="#linkwrapper2"
                                  style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                  target="_self"
                                />
                                <div
                                  class="emotion-12"
                                >
                                  <a
                                    aria-label="frame 2 test banner"
                                    class="emotion-9"
                                    data-testid="conditional-link"
                                    href="#linkwrapper2"
                                    style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                    target="_self"
                                  />
                                  <div
                                    class="emotion-14"
                                  >
                                    <a
                                      aria-label="frame 2 test banner"
                                      class="emotion-9"
                                      data-testid="conditional-link"
                                      href="#linkwrapper2"
                                      style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                      target="_self"
                                    >
                                      <div
                                        class="emotion-16"
                                      >
                                        <div
                                          class="emotion-17"
                                          data-testid="advance-image-container-test-id"
                                        >
                                          <img
                                            alt="Default Icon"
                                            class="emotion-18"
                                            maxminheight="7.291666666666667vw"
                                            src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                                          />
                                        </div>
                                      </div>
                                      <div
                                        style="text-align: center;"
                                      >
                                        <div
                                          class="emotion-19"
                                        >
                                          <div>
                                            <p
                                              class="amp-cms--p"
                                            >
                                              <span
                                                class="amp-cms--body-2"
                                              >
                                                Introducing
                                              </span>
                                            </p>
                                            <p
                                              class="amp-cms--p"
                                            >
                                              <span
                                                class="amp-cms--headline-5"
                                              >
                                                THE ULTIMATE II TIGHT
                                              </span>
                                            </p>
                                            <p
                                              class="amp-cms--p"
                                            >
                                              <span
                                                class="amp-cms--body-3"
                                              >
                                                Our #1 sweat ready style just got a major upgrade. Lorem ipsum dolor sit amet.
                                              </span>
                                            </p>
                                            <hr
                                              aria-hidden="true"
                                              style="display:block;border:0;height:8px;margin:0;background:transparent;"
                                            />
                                          </div>
                                        </div>
                                      </div>
                                      <div
                                        class="emotion-16"
                                      >
                                        <div
                                          class="emotion-21"
                                        >
                                          <div
                                            class="emotion-22"
                                          >
                                            <img
                                              alt=""
                                              class="emotion-23"
                                              src="https://athleta.a.bigcontent.io/v1/static/WATER-RESISTANT_dark"
                                            />
                                            <span
                                              class="emotion-24"
                                            >
                                              Water-Resistant
                                            </span>
                                          </div>
                                          <div
                                            class="emotion-22"
                                          >
                                            <img
                                              alt=""
                                              class="emotion-23"
                                              src="https://athleta.a.bigcontent.io/v1/static/WATER-REPELLENT_light"
                                            />
                                            <span
                                              class="emotion-27"
                                            >
                                              Water-Repellent
                                            </span>
                                          </div>
                                          <div
                                            class="emotion-22"
                                          >
                                            <img
                                              alt=""
                                              class="emotion-23"
                                              src="https://athleta.a.bigcontent.io/v1/static/LOW-WATER-USAGE_light"
                                            />
                                            <span
                                              class="emotion-27"
                                            >
                                              Low Water Usage
                                            </span>
                                          </div>
                                        </div>
                                      </div>
                                    </a>
                                    <div
                                      class="emotion-16"
                                    >
                                      <a
                                        aria-label="frame 2 test banner"
                                        class="emotion-9"
                                        data-testid="conditional-link"
                                        href="#linkwrapper2"
                                        style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                        target="_self"
                                      />
                                      <a
                                        class="emotion-33"
                                        color="dark"
                                        href="#button2"
                                      >
                                        Frame 2 CTA
                                      </a>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="2"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          style="width: 100%; display: inline-block;"
                          tabindex="-1"
                        >
                          <div
                            class="emotion-5"
                            height="1"
                            width="4"
                          >
                            <div
                              class="emotion-6"
                              data-testid="product-card-image"
                            >
                              <canvas
                                aria-label=""
                                class="emotion-36"
                                role="img"
                              />
                            </div>
                            <div
                              class="emotion-8"
                            >
                              <a
                                aria-label="frame 3 test banner"
                                class="emotion-9"
                                data-testid="conditional-link"
                                href="#linkwrapper3"
                                style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                target="_self"
                              />
                              <div
                                class="emotion-10"
                                data-testid="gradient-wrapper"
                              >
                                <a
                                  aria-label="frame 3 test banner"
                                  class="emotion-9"
                                  data-testid="conditional-link"
                                  href="#linkwrapper3"
                                  style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                  target="_self"
                                />
                                <div
                                  class="emotion-12"
                                >
                                  <a
                                    aria-label="frame 3 test banner"
                                    class="emotion-9"
                                    data-testid="conditional-link"
                                    href="#linkwrapper3"
                                    style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                    target="_self"
                                  />
                                  <div
                                    class="emotion-14"
                                  >
                                    <a
                                      aria-label="frame 3 test banner"
                                      class="emotion-9"
                                      data-testid="conditional-link"
                                      href="#linkwrapper3"
                                      style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                      target="_self"
                                    >
                                      <div
                                        class="emotion-16"
                                      >
                                        <div
                                          class="emotion-17"
                                          data-testid="advance-image-container-test-id"
                                        >
                                          <img
                                            alt="Default Icon"
                                            class="emotion-18"
                                            maxminheight="7.291666666666667vw"
                                            src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                                          />
                                        </div>
                                      </div>
                                      <div
                                        style="text-align: center;"
                                      >
                                        <div
                                          class="emotion-19"
                                        >
                                          <div>
                                            <p
                                              class="amp-cms--p"
                                            >
                                              <span
                                                class="amp-cms--body-2"
                                              >
                                                Introducing
                                              </span>
                                            </p>
                                            <p
                                              class="amp-cms--p"
                                            >
                                              <span
                                                class="amp-cms--headline-5"
                                              >
                                                THE ULTIMATE II TIGHT
                                              </span>
                                            </p>
                                            <p
                                              class="amp-cms--p"
                                            >
                                              <span
                                                class="amp-cms--body-3"
                                              >
                                                Our #1 sweat ready style just got a major upgrade. Lorem ipsum dolor sit amet.
                                              </span>
                                            </p>
                                            <hr
                                              aria-hidden="true"
                                              style="display:block;border:0;height:8px;margin:0;background:transparent;"
                                            />
                                          </div>
                                        </div>
                                      </div>
                                      <div
                                        class="emotion-16"
                                      >
                                        <div
                                          class="emotion-21"
                                        >
                                          <div
                                            class="emotion-22"
                                          >
                                            <img
                                              alt=""
                                              class="emotion-23"
                                              src="https://athleta.a.bigcontent.io/v1/static/WATER-RESISTANT_dark"
                                            />
                                            <span
                                              class="emotion-24"
                                            >
                                              Water-Resistant
                                            </span>
                                          </div>
                                          <div
                                            class="emotion-22"
                                          >
                                            <img
                                              alt=""
                                              class="emotion-23"
                                              src="https://athleta.a.bigcontent.io/v1/static/WATER-REPELLENT_light"
                                            />
                                            <span
                                              class="emotion-27"
                                            >
                                              Water-Repellent
                                            </span>
                                          </div>
                                          <div
                                            class="emotion-22"
                                          >
                                            <img
                                              alt=""
                                              class="emotion-23"
                                              src="https://athleta.a.bigcontent.io/v1/static/LOW-WATER-USAGE_light"
                                            />
                                            <span
                                              class="emotion-27"
                                            >
                                              Low Water Usage
                                            </span>
                                          </div>
                                        </div>
                                      </div>
                                    </a>
                                    <div
                                      class="emotion-16"
                                    >
                                      <a
                                        aria-label="frame 3 test banner"
                                        class="emotion-9"
                                        data-testid="conditional-link"
                                        href="#linkwrapper3"
                                        style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                        target="_self"
                                      />
                                      <a
                                        class="emotion-33"
                                        color="dark"
                                        href="#button3"
                                      >
                                        Frame 3 CTA
                                      </a>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <button
                  aria-label="Next"
                  class="slick-next slick-arrow slick-next"
                  data-role="none"
                >
                  <span
                    aria-hidden="true"
                    class="emotion-4"
                  >
                    <svg
                      viewBox="0 0 26.78 17.63"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M0 1.95l13 15.68L26.79 1.98 24.53 0 13.08 13.01 2.3.03z"
                        fill="#000000"
                      />
                    </svg>
                  </span>
                </button>
              </div>
            </div>
          </nav>
        </div>
      </section>
    </article>
  </div>
</DocumentFragment>
`;

exports[`AT FlexibleBannerHP renders a banner with a carousel, given carousel media data, with scaleLimitPixels 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  max-width: 1400px;
  box-sizing: border-box;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  aspect-ratio: 4/1;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 20px;
  box-sizing: border-box;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  aspect-ratio: 4/1;
  height: 100%;
  position: relative;
  width: 100%;
  overflow: clip;
}

.emotion-2 {
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-2 .slick-list {
  overflow: hidden;
}

.emotion-2 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-2 button.slick-next.slick-arrow.slick-next,
.emotion-2 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: undefined;
  background: #00000080;
  opacity: 60%;
  height: 44px;
  width: 44px;
}

.emotion-2 button.slick-next.slick-arrow.slick-next span svg path,
.emotion-2 button.slick-prev.slick-arrow.slick-prev span svg path {
  fill: #FFFFFF80;
}

.emotion-2 button.slick-next.slick-arrow.slick-next:hover,
.emotion-2 button.slick-prev.slick-arrow.slick-prev:hover {
  background: #00000080;
}

.emotion-2 button.slick-next.slick-arrow.slick-next:focus,
.emotion-2 button.slick-prev.slick-arrow.slick-prev:focus {
  background: #00000080;
}

.emotion-2 button.slick-next.slick-arrow.slick-next>span,
.emotion-2 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-2 button.slick-next.slick-arrow.slick-next svg,
.emotion-2 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-2 button.slick-next.slick-arrow.slick-next:focus-visible,
.emotion-2 button.slick-prev.slick-arrow.slick-prev:focus-visible {
  outline: auto;
}

.emotion-2 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-2 .slick-disabled {
  display: none!important;
}

.emotion-2 .slick-next {
  left: calc(100% - 44px);
}

.emotion-2 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-2 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-3 {
  position: relative;
}

.emotion-3 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-3 .slick-slider .slick-track,
.emotion-3 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-3 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-3 .slick-list:focus {
  outline: none;
}

.emotion-3 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-3 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-3 .slick-track:before,
.emotion-3 .slick-track:after {
  display: table;
  content: "";
}

.emotion-3 .slick-track:after {
  clear: both;
}

.emotion-3 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-3 .slick-slide img {
  display: block;
}

.emotion-3 .slick-slide.slick-loading img {
  display: none;
}

.emotion-3 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-3 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-3 .slick-initialized .slick-slide,
.emotion-3 .slick-vertical .slick-slide {
  display: block;
}

.emotion-3 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-3 .slick-loading .slick-track,
.emotion-3 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-3 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-3 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-3 .slick-prev,
.emotion-3 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-3 .slick-prev:hover,
.emotion-3 .slick-next:hover,
.emotion-3 .slick-prev:focus,
.emotion-3 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-3 .slick-prev.slick-disabled,
.emotion-3 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-3 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-3 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-3 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-3 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-3 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-3 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-3 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-3 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-3 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-3 .slick-dots li button:hover,
.emotion-3 .slick-dots li button:focus {
  outline: none;
}

.emotion-3 .slick-dots li button:hover:before,
.emotion-3 .slick-dots li button:focus:before,
.emotion-3 .slick-dots li button:hover:before,
.emotion-3 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-3 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-4 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-4 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 4/1;
}

.emotion-6 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-7 {
  width: 100%;
  aspect-ratio: 4/1;
  object-fit: cover;
}

.emotion-8 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-9:focus-visible {
  outline-offset: -5px;
  outline: auto;
}

.emotion-9:focus-visible video {
  outline-offset: -5px;
  outline: auto;
  z-index: -1;
}

.emotion-10 {
  width: 100%;
  background: none;
  height: 100%;
}

.emotion-12 {
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: min(1.3888888888888888vw, 19.444444444444443px) min(2.7777777777777777vw, 38.888888888888886px);
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  text-align: center;
}

.emotion-14 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  height: 100%;
  padding: 0px;
  gap: min(1.3888888888888888vw, 19.444444444444443px);
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
}

.emotion-16 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-17 {
  height: auto;
  max-width: 100%;
}

.emotion-18 {
  min-height: min(
              7.291666666666667vw, 
              102.08333333333333px
            );
  max-height: min(
              7.291666666666667vw, 
              102.08333333333333px
            );
}

.emotion-19 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-19 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-19 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-19 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-19 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 9.722222222222221px));
  line-height: 1.5;
  letter-spacing: min(0.034722222222222224vw, 0.4861111111111111px);
  font-weight: 500;
}

.emotion-19 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-19 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-19 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 11.666666666666666px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-19 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 11.666666666666666px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-19 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 11.666666666666666px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-19 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 11.666666666666666px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-19 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 17.5px));
  line-height: 1.5555555555555556;
  letter-spacing: min(0.0625vw, 0.875px);
  font-weight: 500;
}

.emotion-19 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.1111111111111112vw, 15.555555555555555px));
  line-height: 1.25;
  letter-spacing: min(0vw, 0px);
  font-weight: 500;
}

.emotion-19 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9722222222222222vw, 13.61111111111111px));
  line-height: 1.5714285714285714;
  letter-spacing: min(0.04861111111111111vw, 0.6805555555555555px);
  font-weight: 500;
}

.emotion-19 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.8333333333333334vw, 11.666666666666666px));
  line-height: 1.5;
  letter-spacing: min(0vw, 0px);
  font-weight: 500;
}

.emotion-19 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 9.722222222222221px));
  line-height: 1.5;
  letter-spacing: min(0.034722222222222224vw, 0.4861111111111111px);
  font-weight: 500;
}

.emotion-19 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1805555555555556vw, 16.52777777777778px));
  line-height: 1.588235294117647;
  letter-spacing: min(0.11805555555555555vw, 1.6527777777777777px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-19 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, min(1.1111111111111112vw, 15.555555555555555px));
  line-height: 1.75;
  letter-spacing: min(0.1111111111111111vw, 1.5555555555555556px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-19 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.8333333333333334vw, 11.666666666666666px));
  line-height: 1.8333333333333333;
  letter-spacing: min(0.056944444444444436vw, 0.7972222222222222px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-19 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.166666666666666vw, 58.333333333333336px));
  line-height: 1;
  letter-spacing: min(0.16666666666666666vw, 2.333333333333333px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-19 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(3.888888888888889vw, 54.44444444444444px));
  line-height: 1;
  letter-spacing: min(0.19444444444444445vw, 2.722222222222222px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-19 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(3.4722222222222223vw, 48.61111111111111px));
  line-height: 1;
  letter-spacing: min(0.1736111111111111vw, 2.4305555555555554px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-19 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(2.7777777777777777vw, 38.888888888888886px));
  line-height: 1;
  letter-spacing: min(0.19444444444444445vw, 2.722222222222222px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-19 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(2.5vw, 35px));
  line-height: 1;
  letter-spacing: min(0.25vw, 3.5px);
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-19 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.9444444444444444vw, 27.22222222222222px));
  line-height: 1.1428571428571428;
  letter-spacing: min(0.1048611111111111vw, 1.4680555555555554px);
  text-transform: none;
  font-weight: 600;
}

.emotion-19 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.3888888888888888vw, 19.444444444444443px));
  line-height: 1.2;
  letter-spacing: min(0.09027777777777779vw, 1.2638888888888888px);
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-19 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(8.61111111111111vw, 120.55555555555556px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.3888888888888889px);
  font-weight: 400;
}

.emotion-19 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.833333333333333vw, 81.66666666666667px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.3888888888888889px);
  font-weight: 400;
}

.emotion-19 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.444444444444445vw, 62.22222222222222px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.3888888888888889px);
  font-weight: 400;
}

.emotion-19 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(2.361111111111111vw, 33.05555555555556px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.3888888888888889px);
  font-weight: 400;
}

.emotion-19 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.1111111111111112vw, 15.555555555555555px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-19 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.1111111111111112vw, 15.555555555555555px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-19 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.1111111111111112vw, 15.555555555555555px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-19 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 97.22222222222221px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-19 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 77.77777777777777px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
}

.emotion-19 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.1111111111111112vw, 15.555555555555555px);
  line-height: 0.0625;
  letter-spacing: 0;
}

.emotion-19 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.6666666666666667vw, 23.333333333333332px));
  line-height: 1.5;
  letter-spacing: min(0.08333333333333333vw, 1.1666666666666665px);
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-19 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.25vw, 17.5px));
  line-height: 1.5555555555555556;
  letter-spacing: min(0.025vw, 0.35px);
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-19 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.9722222222222222vw, 13.61111111111111px));
  line-height: 1.1428571428571428;
  letter-spacing: min(0.04861111111111111vw, 0.6805555555555555px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-21 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-column-gap: min(2.083333333333333vw, 29.166666666666668px);
  column-gap: min(2.083333333333333vw, 29.166666666666668px);
}

.emotion-22 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  text-align: center;
  min-width: 3.4722222222222223vw;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-23 {
  height: 3.4722222222222223vw;
  max-height: 48.611111111111114px;
}

.emotion-24 {
  line-height: 150%;
  font-size: max(10px, min(0.8333333333333334vw, 11.666666666666666px));
  padding-top: min(0.5555555555555556vw, 7.777777777777778px);
  max-width: min(5.902777777777778vw, 82.63888888888889px);
  font-weight: 500;
  text-transform: uppercase;
  color: #111;
}

.emotion-27 {
  line-height: 150%;
  font-size: max(10px, min(0.8333333333333334vw, 11.666666666666666px));
  padding-top: min(0.5555555555555556vw, 7.777777777777778px);
  max-width: min(5.902777777777778vw, 82.63888888888889px);
  font-weight: 500;
  text-transform: uppercase;
  color: #FFFFFF;
}

.emotion-33 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: 64px;
  line-height: 1.1111111111111112;
  padding: 23px 40px;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #000000;
  border-color: #000000;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  pointer-events: all;
  min-height: auto;
}

.emotion-33:focus {
  outline: none;
}

.emotion-33>span {
  padding: 1px 0;
}

.emotion-33:hover,
.emotion-33:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-33:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
  -webkit-text-decoration: none;
  text-decoration: none;
  text-shadow: none;
}

.emotion-36 {
  width: 100%;
  height: 100%;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <article
      class="emotion-0"
    >
      <section
        class="emotion-1"
      >
        <div
          style="width: 100%;"
        >
          <nav
            class="emotion-2"
          >
            <div
              class="emotion-3"
            >
              <div
                class="slick-slider slick-initialized"
                dir="ltr"
              >
                <button
                  aria-label="Previous"
                  class="slick-prev slick-arrow slick-prev slick-disabled"
                  data-role="none"
                  disabled=""
                >
                  <span
                    aria-hidden="true"
                    class="emotion-4"
                  >
                    <svg
                      viewBox="0 0 26.78 17.63"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M0 1.95l13 15.68L26.79 1.98 24.53 0 13.08 13.01 2.3.03z"
                        fill="#000000"
                      />
                    </svg>
                  </span>
                </button>
                <div
                  class="slick-list"
                >
                  <div
                    class="slick-track"
                    style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                  >
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active slick-current"
                      data-index="0"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          style="width: 100%; display: inline-block;"
                          tabindex="-1"
                        >
                          <div
                            class="emotion-5"
                            height="1"
                            width="4"
                          >
                            <div
                              class="emotion-6"
                              data-testid="product-card-image"
                            >
                              <img
                                alt=""
                                class="emotion-7"
                                src="https://1d9xxafvh577y12766tt2karod.staging.bigcontent.io/i/athleta/ATG_DP_XL_Testing2?fmt=webp"
                              />
                            </div>
                            <div
                              class="emotion-8"
                            >
                              <a
                                aria-label="frame 1 test banner"
                                class="emotion-9"
                                data-testid="conditional-link"
                                href="#linkwrapper1"
                                style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                target="_self"
                              />
                              <div
                                class="emotion-10"
                                data-testid="gradient-wrapper"
                              >
                                <a
                                  aria-label="frame 1 test banner"
                                  class="emotion-9"
                                  data-testid="conditional-link"
                                  href="#linkwrapper1"
                                  style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                  target="_self"
                                />
                                <div
                                  class="emotion-12"
                                >
                                  <a
                                    aria-label="frame 1 test banner"
                                    class="emotion-9"
                                    data-testid="conditional-link"
                                    href="#linkwrapper1"
                                    style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                    target="_self"
                                  />
                                  <div
                                    class="emotion-14"
                                  >
                                    <a
                                      aria-label="frame 1 test banner"
                                      class="emotion-9"
                                      data-testid="conditional-link"
                                      href="#linkwrapper1"
                                      style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                      target="_self"
                                    >
                                      <div
                                        class="emotion-16"
                                      >
                                        <div
                                          class="emotion-17"
                                          data-testid="advance-image-container-test-id"
                                        >
                                          <img
                                            alt="Default Icon"
                                            class="emotion-18"
                                            maxminheight="min(
            7.291666666666667vw, 
            102.08333333333333px
          )"
                                            src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                                          />
                                        </div>
                                      </div>
                                      <div
                                        style="text-align: center;"
                                      >
                                        <div
                                          class="emotion-19"
                                        >
                                          <div>
                                            <p
                                              class="amp-cms--p"
                                            >
                                              <span
                                                class="amp-cms--body-2"
                                              >
                                                Introducing
                                              </span>
                                            </p>
                                            <p
                                              class="amp-cms--p"
                                            >
                                              <span
                                                class="amp-cms--headline-5"
                                              >
                                                THE ULTIMATE II TIGHT
                                              </span>
                                            </p>
                                            <p
                                              class="amp-cms--p"
                                            >
                                              <span
                                                class="amp-cms--body-3"
                                              >
                                                Our #1 sweat ready style just got a major upgrade. Lorem ipsum dolor sit amet.
                                              </span>
                                            </p>
                                            <hr
                                              aria-hidden="true"
                                              style="display:block;border:0;height:8px;margin:0;background:transparent;"
                                            />
                                          </div>
                                        </div>
                                      </div>
                                      <div
                                        class="emotion-16"
                                      >
                                        <div
                                          class="emotion-21"
                                        >
                                          <div
                                            class="emotion-22"
                                          >
                                            <img
                                              alt=""
                                              class="emotion-23"
                                              src="https://athleta.a.bigcontent.io/v1/static/WATER-RESISTANT_dark"
                                            />
                                            <span
                                              class="emotion-24"
                                            >
                                              Water-Resistant
                                            </span>
                                          </div>
                                          <div
                                            class="emotion-22"
                                          >
                                            <img
                                              alt=""
                                              class="emotion-23"
                                              src="https://athleta.a.bigcontent.io/v1/static/WATER-REPELLENT_light"
                                            />
                                            <span
                                              class="emotion-27"
                                            >
                                              Water-Repellent
                                            </span>
                                          </div>
                                          <div
                                            class="emotion-22"
                                          >
                                            <img
                                              alt=""
                                              class="emotion-23"
                                              src="https://athleta.a.bigcontent.io/v1/static/LOW-WATER-USAGE_light"
                                            />
                                            <span
                                              class="emotion-27"
                                            >
                                              Low Water Usage
                                            </span>
                                          </div>
                                        </div>
                                      </div>
                                    </a>
                                    <div
                                      class="emotion-16"
                                    >
                                      <a
                                        aria-label="frame 1 test banner"
                                        class="emotion-9"
                                        data-testid="conditional-link"
                                        href="#linkwrapper1"
                                        style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                        target="_self"
                                      />
                                      <a
                                        class="emotion-33"
                                        color="dark"
                                        href="#button1"
                                      >
                                        Frame 1 CTA
                                      </a>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="1"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          style="width: 100%; display: inline-block;"
                          tabindex="-1"
                        >
                          <div
                            class="emotion-5"
                            height="1"
                            width="4"
                          >
                            <div
                              class="emotion-6"
                              data-testid="product-card-image"
                            >
                              <canvas
                                aria-label=""
                                class="emotion-36"
                                role="img"
                              />
                            </div>
                            <div
                              class="emotion-8"
                            >
                              <a
                                aria-label="frame 2 test banner"
                                class="emotion-9"
                                data-testid="conditional-link"
                                href="#linkwrapper2"
                                style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                target="_self"
                              />
                              <div
                                class="emotion-10"
                                data-testid="gradient-wrapper"
                              >
                                <a
                                  aria-label="frame 2 test banner"
                                  class="emotion-9"
                                  data-testid="conditional-link"
                                  href="#linkwrapper2"
                                  style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                  target="_self"
                                />
                                <div
                                  class="emotion-12"
                                >
                                  <a
                                    aria-label="frame 2 test banner"
                                    class="emotion-9"
                                    data-testid="conditional-link"
                                    href="#linkwrapper2"
                                    style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                    target="_self"
                                  />
                                  <div
                                    class="emotion-14"
                                  >
                                    <a
                                      aria-label="frame 2 test banner"
                                      class="emotion-9"
                                      data-testid="conditional-link"
                                      href="#linkwrapper2"
                                      style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                      target="_self"
                                    >
                                      <div
                                        class="emotion-16"
                                      >
                                        <div
                                          class="emotion-17"
                                          data-testid="advance-image-container-test-id"
                                        >
                                          <img
                                            alt="Default Icon"
                                            class="emotion-18"
                                            maxminheight="min(
            7.291666666666667vw, 
            102.08333333333333px
          )"
                                            src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                                          />
                                        </div>
                                      </div>
                                      <div
                                        style="text-align: center;"
                                      >
                                        <div
                                          class="emotion-19"
                                        >
                                          <div>
                                            <p
                                              class="amp-cms--p"
                                            >
                                              <span
                                                class="amp-cms--body-2"
                                              >
                                                Introducing
                                              </span>
                                            </p>
                                            <p
                                              class="amp-cms--p"
                                            >
                                              <span
                                                class="amp-cms--headline-5"
                                              >
                                                THE ULTIMATE II TIGHT
                                              </span>
                                            </p>
                                            <p
                                              class="amp-cms--p"
                                            >
                                              <span
                                                class="amp-cms--body-3"
                                              >
                                                Our #1 sweat ready style just got a major upgrade. Lorem ipsum dolor sit amet.
                                              </span>
                                            </p>
                                            <hr
                                              aria-hidden="true"
                                              style="display:block;border:0;height:8px;margin:0;background:transparent;"
                                            />
                                          </div>
                                        </div>
                                      </div>
                                      <div
                                        class="emotion-16"
                                      >
                                        <div
                                          class="emotion-21"
                                        >
                                          <div
                                            class="emotion-22"
                                          >
                                            <img
                                              alt=""
                                              class="emotion-23"
                                              src="https://athleta.a.bigcontent.io/v1/static/WATER-RESISTANT_dark"
                                            />
                                            <span
                                              class="emotion-24"
                                            >
                                              Water-Resistant
                                            </span>
                                          </div>
                                          <div
                                            class="emotion-22"
                                          >
                                            <img
                                              alt=""
                                              class="emotion-23"
                                              src="https://athleta.a.bigcontent.io/v1/static/WATER-REPELLENT_light"
                                            />
                                            <span
                                              class="emotion-27"
                                            >
                                              Water-Repellent
                                            </span>
                                          </div>
                                          <div
                                            class="emotion-22"
                                          >
                                            <img
                                              alt=""
                                              class="emotion-23"
                                              src="https://athleta.a.bigcontent.io/v1/static/LOW-WATER-USAGE_light"
                                            />
                                            <span
                                              class="emotion-27"
                                            >
                                              Low Water Usage
                                            </span>
                                          </div>
                                        </div>
                                      </div>
                                    </a>
                                    <div
                                      class="emotion-16"
                                    >
                                      <a
                                        aria-label="frame 2 test banner"
                                        class="emotion-9"
                                        data-testid="conditional-link"
                                        href="#linkwrapper2"
                                        style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                        target="_self"
                                      />
                                      <a
                                        class="emotion-33"
                                        color="dark"
                                        href="#button2"
                                      >
                                        Frame 2 CTA
                                      </a>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="2"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <div
                          style="width: 100%; display: inline-block;"
                          tabindex="-1"
                        >
                          <div
                            class="emotion-5"
                            height="1"
                            width="4"
                          >
                            <div
                              class="emotion-6"
                              data-testid="product-card-image"
                            >
                              <canvas
                                aria-label=""
                                class="emotion-36"
                                role="img"
                              />
                            </div>
                            <div
                              class="emotion-8"
                            >
                              <a
                                aria-label="frame 3 test banner"
                                class="emotion-9"
                                data-testid="conditional-link"
                                href="#linkwrapper3"
                                style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                target="_self"
                              />
                              <div
                                class="emotion-10"
                                data-testid="gradient-wrapper"
                              >
                                <a
                                  aria-label="frame 3 test banner"
                                  class="emotion-9"
                                  data-testid="conditional-link"
                                  href="#linkwrapper3"
                                  style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                  target="_self"
                                />
                                <div
                                  class="emotion-12"
                                >
                                  <a
                                    aria-label="frame 3 test banner"
                                    class="emotion-9"
                                    data-testid="conditional-link"
                                    href="#linkwrapper3"
                                    style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                    target="_self"
                                  />
                                  <div
                                    class="emotion-14"
                                  >
                                    <a
                                      aria-label="frame 3 test banner"
                                      class="emotion-9"
                                      data-testid="conditional-link"
                                      href="#linkwrapper3"
                                      style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                      target="_self"
                                    >
                                      <div
                                        class="emotion-16"
                                      >
                                        <div
                                          class="emotion-17"
                                          data-testid="advance-image-container-test-id"
                                        >
                                          <img
                                            alt="Default Icon"
                                            class="emotion-18"
                                            maxminheight="min(
            7.291666666666667vw, 
            102.08333333333333px
          )"
                                            src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                                          />
                                        </div>
                                      </div>
                                      <div
                                        style="text-align: center;"
                                      >
                                        <div
                                          class="emotion-19"
                                        >
                                          <div>
                                            <p
                                              class="amp-cms--p"
                                            >
                                              <span
                                                class="amp-cms--body-2"
                                              >
                                                Introducing
                                              </span>
                                            </p>
                                            <p
                                              class="amp-cms--p"
                                            >
                                              <span
                                                class="amp-cms--headline-5"
                                              >
                                                THE ULTIMATE II TIGHT
                                              </span>
                                            </p>
                                            <p
                                              class="amp-cms--p"
                                            >
                                              <span
                                                class="amp-cms--body-3"
                                              >
                                                Our #1 sweat ready style just got a major upgrade. Lorem ipsum dolor sit amet.
                                              </span>
                                            </p>
                                            <hr
                                              aria-hidden="true"
                                              style="display:block;border:0;height:8px;margin:0;background:transparent;"
                                            />
                                          </div>
                                        </div>
                                      </div>
                                      <div
                                        class="emotion-16"
                                      >
                                        <div
                                          class="emotion-21"
                                        >
                                          <div
                                            class="emotion-22"
                                          >
                                            <img
                                              alt=""
                                              class="emotion-23"
                                              src="https://athleta.a.bigcontent.io/v1/static/WATER-RESISTANT_dark"
                                            />
                                            <span
                                              class="emotion-24"
                                            >
                                              Water-Resistant
                                            </span>
                                          </div>
                                          <div
                                            class="emotion-22"
                                          >
                                            <img
                                              alt=""
                                              class="emotion-23"
                                              src="https://athleta.a.bigcontent.io/v1/static/WATER-REPELLENT_light"
                                            />
                                            <span
                                              class="emotion-27"
                                            >
                                              Water-Repellent
                                            </span>
                                          </div>
                                          <div
                                            class="emotion-22"
                                          >
                                            <img
                                              alt=""
                                              class="emotion-23"
                                              src="https://athleta.a.bigcontent.io/v1/static/LOW-WATER-USAGE_light"
                                            />
                                            <span
                                              class="emotion-27"
                                            >
                                              Low Water Usage
                                            </span>
                                          </div>
                                        </div>
                                      </div>
                                    </a>
                                    <div
                                      class="emotion-16"
                                    >
                                      <a
                                        aria-label="frame 3 test banner"
                                        class="emotion-9"
                                        data-testid="conditional-link"
                                        href="#linkwrapper3"
                                        style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                        target="_self"
                                      />
                                      <a
                                        class="emotion-33"
                                        color="dark"
                                        href="#button3"
                                      >
                                        Frame 3 CTA
                                      </a>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <button
                  aria-label="Next"
                  class="slick-next slick-arrow slick-next"
                  data-role="none"
                >
                  <span
                    aria-hidden="true"
                    class="emotion-4"
                  >
                    <svg
                      viewBox="0 0 26.78 17.63"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M0 1.95l13 15.68L26.79 1.98 24.53 0 13.08 13.01 2.3.03z"
                        fill="#000000"
                      />
                    </svg>
                  </span>
                </button>
              </div>
            </div>
          </nav>
        </div>
      </section>
    </article>
  </div>
</DocumentFragment>
`;

exports[`AT FlexibleBannerHP renders a banner with a video, given video media data 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  box-sizing: border-box;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  aspect-ratio: 4/1;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 20px;
  box-sizing: border-box;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  aspect-ratio: 4/1;
  height: 100%;
  position: absolute;
  width: 100%;
  overflow: clip;
}

.emotion-2 {
  position: relative;
  height: 100%;
  pointer-events: auto;
  aspect-ratio: 4/1;
  width: 100%;
}

.emotion-3 {
  background: transparent;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  box-sizing: border-box;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  height: 100%;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  pointer-events: none;
  width: 100%;
  aspect-ratio: 4/1;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  text-align: center;
}

.emotion-4 {
  width: 100%;
  background: none;
}

.emotion-5 {
  pointerevents: auto;
}

.emotion-6 {
  height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: 1.3888888888888888vw 2.7777777777777777vw;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  text-align: center;
}

.emotion-7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  height: initial;
  padding: 0px;
  gap: 1.3888888888888888vw;
}

.emotion-8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-9 {
  height: auto;
  max-width: 100%;
}

.emotion-10 {
  min-height: 7.291666666666667vw;
  max-height: 7.291666666666667vw;
}

.emotion-11 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-11 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-11 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-11 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-11 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.6944444444444444vw);
  line-height: 1.5;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 500;
}

.emotion-11 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-11 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-11 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.25vw);
  line-height: 1.5555555555555556;
  letter-spacing: 0.0625vw;
  font-weight: 500;
}

.emotion-11 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.1111111111111112vw);
  line-height: 1.25;
  letter-spacing: 0vw;
  font-weight: 500;
}

.emotion-11 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.9722222222222222vw);
  line-height: 1.5714285714285714;
  letter-spacing: 0.04861111111111111vw;
  font-weight: 500;
}

.emotion-11 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.8333333333333334vw);
  line-height: 1.5;
  letter-spacing: 0vw;
  font-weight: 500;
}

.emotion-11 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.6944444444444444vw);
  line-height: 1.5;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 500;
}

.emotion-11 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.1805555555555556vw);
  line-height: 1.588235294117647;
  letter-spacing: 0.11805555555555555vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-11 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, 1.1111111111111112vw);
  line-height: 1.75;
  letter-spacing: 0.1111111111111111vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-11 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.8333333333333334vw);
  line-height: 1.8333333333333333;
  letter-spacing: 0.056944444444444436vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-11 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 4.166666666666666vw);
  line-height: 1;
  letter-spacing: 0.16666666666666666vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-11 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 3.888888888888889vw);
  line-height: 1;
  letter-spacing: 0.19444444444444445vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-11 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 3.4722222222222223vw);
  line-height: 1;
  letter-spacing: 0.1736111111111111vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-11 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 2.7777777777777777vw);
  line-height: 1;
  letter-spacing: 0.19444444444444445vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-11 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 2.5vw);
  line-height: 1;
  letter-spacing: 0.25vw;
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-11 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.9444444444444444vw);
  line-height: 1.1428571428571428;
  letter-spacing: 0.1048611111111111vw;
  text-transform: none;
  font-weight: 600;
}

.emotion-11 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.3888888888888888vw);
  line-height: 1.2;
  letter-spacing: 0.09027777777777779vw;
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-11 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 8.61111111111111vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 5.833333333333333vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 4.444444444444445vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 2.361111111111111vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 6.944444444444445vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-11 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 5.555555555555555vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
}

.emotion-11 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.6666666666666667vw);
  line-height: 1.5;
  letter-spacing: 0.08333333333333333vw;
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-11 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.25vw);
  line-height: 1.5555555555555556;
  letter-spacing: 0.025vw;
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-11 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 0.9722222222222222vw);
  line-height: 1.1428571428571428;
  letter-spacing: 0.04861111111111111vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-13 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-column-gap: 2.083333333333333vw;
  column-gap: 2.083333333333333vw;
}

.emotion-14 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  text-align: center;
  min-width: 3.4722222222222223vw;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-15 {
  height: 3.4722222222222223vw;
}

.emotion-16 {
  line-height: 150%;
  font-size: max(10px, 0.8333333333333334vw);
  padding-top: 0.5555555555555556vw;
  max-width: 5.902777777777778vw;
  font-weight: 500;
  text-transform: uppercase;
  color: #111;
}

.emotion-19 {
  line-height: 150%;
  font-size: max(10px, 0.8333333333333334vw);
  padding-top: 0.5555555555555556vw;
  max-width: 5.902777777777778vw;
  font-weight: 500;
  text-transform: uppercase;
  color: #FFFFFF;
}

.emotion-24 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: auto;
  line-height: 1.2777777777777777;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  pointer-events: all;
  min-height: auto;
}

.emotion-24:focus {
  outline: none;
}

.emotion-24>span span {
  height: calc(18px * 0.7133333333333334);
}

.emotion-24 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.5rem * 0.72);
}

.emotion-24 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-24:hover,
.emotion-24:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-24:active {
  text-transform: uppercase;
  text-shadow: none;
}

.emotion-25 {
  box-sizing: border-box;
}

.emotion-26 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-26 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <article
      class="emotion-0"
    >
      <section
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <h1
            role="presentation"
          >
            ReactPlayer
          </h1>
        </div>
      </section>
      <div
        class="emotion-3"
        height="0"
        width="0"
      >
        <div
          class="emotion-4"
          data-testid="gradient-wrapper"
        >
          <div
            class="emotion-5"
          >
            <div
              class="emotion-6"
            >
              <div
                class="emotion-7"
              >
                <div
                  class="emotion-8"
                >
                  <div
                    class="emotion-9"
                    data-testid="advance-image-container-test-id"
                  >
                    <img
                      alt="Default Icon"
                      class="emotion-10"
                      maxminheight="7.291666666666667vw"
                      src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                    />
                  </div>
                </div>
                <div
                  style="text-align: center;"
                >
                  <div
                    class="emotion-11"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-2"
                        >
                          Introducing
                        </span>
                      </p>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--headline-5"
                        >
                          THE ULTIMATE II TIGHT
                        </span>
                      </p>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-3"
                        >
                          Our #1 sweat ready style just got a major upgrade. Lorem ipsum dolor sit amet.
                        </span>
                      </p>
                      <hr
                        aria-hidden="true"
                        style="display:block;border:0;height:8px;margin:0;background:transparent;"
                      />
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-8"
                >
                  <div
                    class="emotion-13"
                  >
                    <div
                      class="emotion-14"
                    >
                      <img
                        alt=""
                        class="emotion-15"
                        src="https://athleta.a.bigcontent.io/v1/static/WATER-RESISTANT_dark"
                      />
                      <span
                        class="emotion-16"
                      >
                        Water-Resistant
                      </span>
                    </div>
                    <div
                      class="emotion-14"
                    >
                      <img
                        alt=""
                        class="emotion-15"
                        src="https://athleta.a.bigcontent.io/v1/static/WATER-REPELLENT_light"
                      />
                      <span
                        class="emotion-19"
                      >
                        Water-Repellent
                      </span>
                    </div>
                    <div
                      class="emotion-14"
                    >
                      <img
                        alt=""
                        class="emotion-15"
                        src="https://athleta.a.bigcontent.io/v1/static/LOW-WATER-USAGE_light"
                      />
                      <span
                        class="emotion-19"
                      >
                        Low Water Usage
                      </span>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-8"
                >
                  <a
                    class="emotion-24"
                    color="dark"
                    href="/VALUE"
                  >
                    <span
                      class="emotion-25"
                    >
                      Shop The Ultimate Collection
                      <span
                        aria-hidden="true"
                        class="emotion-26"
                      >
                        <svg
                          viewBox="0 0 8.207 11.366"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M1.093 11.366L.021 9.983l5.386-4.18L.001 1.35 1.114-.001l7.094 5.843z"
                          />
                          fill=#000000
                        </svg>
                      </span>
                    </span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </article>
  </div>
</DocumentFragment>
`;

exports[`AT FlexibleBannerHP renders a banner with a video, given video media data, with scaleLimitPixels 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  box-sizing: border-box;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  aspect-ratio: 4/1;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 20px;
  box-sizing: border-box;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  aspect-ratio: 4/1;
  height: 100%;
  position: absolute;
  width: 100%;
  overflow: clip;
}

.emotion-2 {
  position: relative;
  height: 100%;
  pointer-events: auto;
  aspect-ratio: 4/1;
  width: 100%;
}

.emotion-3 {
  background: transparent;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  box-sizing: border-box;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  height: 100%;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  pointer-events: none;
  width: 100%;
  aspect-ratio: 4/1;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  text-align: center;
}

.emotion-4 {
  width: 100%;
  background: none;
}

.emotion-5 {
  pointerevents: auto;
}

.emotion-6 {
  height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: 1.3888888888888888vw 2.7777777777777777vw;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  text-align: center;
}

.emotion-7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  height: initial;
  padding: 0px;
  gap: 1.3888888888888888vw;
}

.emotion-8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-9 {
  height: auto;
  max-width: 100%;
}

.emotion-10 {
  min-height: 7.291666666666667vw;
  max-height: 7.291666666666667vw;
}

.emotion-11 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-11 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-11 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-11 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-11 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.6944444444444444vw);
  line-height: 1.5;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 500;
}

.emotion-11 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-11 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-11 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.25vw);
  line-height: 1.5555555555555556;
  letter-spacing: 0.0625vw;
  font-weight: 500;
}

.emotion-11 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.1111111111111112vw);
  line-height: 1.25;
  letter-spacing: 0vw;
  font-weight: 500;
}

.emotion-11 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.9722222222222222vw);
  line-height: 1.5714285714285714;
  letter-spacing: 0.04861111111111111vw;
  font-weight: 500;
}

.emotion-11 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.8333333333333334vw);
  line-height: 1.5;
  letter-spacing: 0vw;
  font-weight: 500;
}

.emotion-11 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.6944444444444444vw);
  line-height: 1.5;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 500;
}

.emotion-11 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.1805555555555556vw);
  line-height: 1.588235294117647;
  letter-spacing: 0.11805555555555555vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-11 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, 1.1111111111111112vw);
  line-height: 1.75;
  letter-spacing: 0.1111111111111111vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-11 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.8333333333333334vw);
  line-height: 1.8333333333333333;
  letter-spacing: 0.056944444444444436vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-11 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 4.166666666666666vw);
  line-height: 1;
  letter-spacing: 0.16666666666666666vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-11 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 3.888888888888889vw);
  line-height: 1;
  letter-spacing: 0.19444444444444445vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-11 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 3.4722222222222223vw);
  line-height: 1;
  letter-spacing: 0.1736111111111111vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-11 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 2.7777777777777777vw);
  line-height: 1;
  letter-spacing: 0.19444444444444445vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-11 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 2.5vw);
  line-height: 1;
  letter-spacing: 0.25vw;
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-11 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.9444444444444444vw);
  line-height: 1.1428571428571428;
  letter-spacing: 0.1048611111111111vw;
  text-transform: none;
  font-weight: 600;
}

.emotion-11 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.3888888888888888vw);
  line-height: 1.2;
  letter-spacing: 0.09027777777777779vw;
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-11 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 8.61111111111111vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 5.833333333333333vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 4.444444444444445vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 2.361111111111111vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-11 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 6.944444444444445vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-11 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 5.555555555555555vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
}

.emotion-11 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
}

.emotion-11 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.6666666666666667vw);
  line-height: 1.5;
  letter-spacing: 0.08333333333333333vw;
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-11 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.25vw);
  line-height: 1.5555555555555556;
  letter-spacing: 0.025vw;
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-11 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 0.9722222222222222vw);
  line-height: 1.1428571428571428;
  letter-spacing: 0.04861111111111111vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-13 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-column-gap: 2.083333333333333vw;
  column-gap: 2.083333333333333vw;
}

.emotion-14 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  text-align: center;
  min-width: 3.4722222222222223vw;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-15 {
  height: 3.4722222222222223vw;
}

.emotion-16 {
  line-height: 150%;
  font-size: max(10px, 0.8333333333333334vw);
  padding-top: 0.5555555555555556vw;
  max-width: 5.902777777777778vw;
  font-weight: 500;
  text-transform: uppercase;
  color: #111;
}

.emotion-19 {
  line-height: 150%;
  font-size: max(10px, 0.8333333333333334vw);
  padding-top: 0.5555555555555556vw;
  max-width: 5.902777777777778vw;
  font-weight: 500;
  text-transform: uppercase;
  color: #FFFFFF;
}

.emotion-24 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: auto;
  line-height: 1.2777777777777777;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  pointer-events: all;
  min-height: auto;
}

.emotion-24:focus {
  outline: none;
}

.emotion-24>span span {
  height: calc(18px * 0.7133333333333334);
}

.emotion-24 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.5rem * 0.72);
}

.emotion-24 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-24:hover,
.emotion-24:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-24:active {
  text-transform: uppercase;
  text-shadow: none;
}

.emotion-25 {
  box-sizing: border-box;
}

.emotion-26 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-26 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <article
      class="emotion-0"
    >
      <section
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <h1
            role="presentation"
          >
            ReactPlayer
          </h1>
        </div>
      </section>
      <div
        class="emotion-3"
        height="0"
        width="0"
      >
        <div
          class="emotion-4"
          data-testid="gradient-wrapper"
        >
          <div
            class="emotion-5"
          >
            <div
              class="emotion-6"
            >
              <div
                class="emotion-7"
              >
                <div
                  class="emotion-8"
                >
                  <div
                    class="emotion-9"
                    data-testid="advance-image-container-test-id"
                  >
                    <img
                      alt="Default Icon"
                      class="emotion-10"
                      maxminheight="7.291666666666667vw"
                      src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                    />
                  </div>
                </div>
                <div
                  style="text-align: center;"
                >
                  <div
                    class="emotion-11"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-2"
                        >
                          Introducing
                        </span>
                      </p>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--headline-5"
                        >
                          THE ULTIMATE II TIGHT
                        </span>
                      </p>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-3"
                        >
                          Our #1 sweat ready style just got a major upgrade. Lorem ipsum dolor sit amet.
                        </span>
                      </p>
                      <hr
                        aria-hidden="true"
                        style="display:block;border:0;height:8px;margin:0;background:transparent;"
                      />
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-8"
                >
                  <div
                    class="emotion-13"
                  >
                    <div
                      class="emotion-14"
                    >
                      <img
                        alt=""
                        class="emotion-15"
                        src="https://athleta.a.bigcontent.io/v1/static/WATER-RESISTANT_dark"
                      />
                      <span
                        class="emotion-16"
                      >
                        Water-Resistant
                      </span>
                    </div>
                    <div
                      class="emotion-14"
                    >
                      <img
                        alt=""
                        class="emotion-15"
                        src="https://athleta.a.bigcontent.io/v1/static/WATER-REPELLENT_light"
                      />
                      <span
                        class="emotion-19"
                      >
                        Water-Repellent
                      </span>
                    </div>
                    <div
                      class="emotion-14"
                    >
                      <img
                        alt=""
                        class="emotion-15"
                        src="https://athleta.a.bigcontent.io/v1/static/LOW-WATER-USAGE_light"
                      />
                      <span
                        class="emotion-19"
                      >
                        Low Water Usage
                      </span>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-8"
                >
                  <a
                    class="emotion-24"
                    color="dark"
                    href="/VALUE"
                  >
                    <span
                      class="emotion-25"
                    >
                      Shop The Ultimate Collection
                      <span
                        aria-hidden="true"
                        class="emotion-26"
                      >
                        <svg
                          viewBox="0 0 8.207 11.366"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M1.093 11.366L.021 9.983l5.386-4.18L.001 1.35 1.114-.001l7.094 5.843z"
                          />
                          fill=#000000
                        </svg>
                      </span>
                    </span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </article>
  </div>
</DocumentFragment>
`;

exports[`AT FlexibleBannerHP renders a banner with an image as its media, given default data 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  box-sizing: border-box;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  aspect-ratio: 4/1;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 20px;
  box-sizing: border-box;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  aspect-ratio: 4/1;
  height: 100%;
  position: absolute;
  width: 100%;
  overflow: clip;
}

.emotion-2 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-3 {
  width: 100%;
  aspect-ratio: 4/1;
  object-fit: cover;
}

.emotion-4 {
  background: transparent;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  box-sizing: border-box;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  height: 100%;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  pointer-events: none;
  width: 100%;
  aspect-ratio: 4/1;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  text-align: center;
}

.emotion-5 {
  width: 100%;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0) 100%);
}

.emotion-6 {
  pointerevents: auto;
}

.emotion-7 {
  height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: 1.3888888888888888vw 2.7777777777777777vw;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  text-align: center;
}

.emotion-8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  height: initial;
  padding: 0px;
  gap: 1.3888888888888888vw;
}

.emotion-9 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-10 {
  height: auto;
  max-width: 100%;
}

.emotion-11 {
  min-height: 7.291666666666667vw;
  max-height: 7.291666666666667vw;
}

.emotion-12 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-12 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-12 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-12 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-12 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.6944444444444444vw);
  line-height: 1.5;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 500;
}

.emotion-12 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-12 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-12 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 0.8333333333333334vw;
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.25vw);
  line-height: 1.5555555555555556;
  letter-spacing: 0.0625vw;
  font-weight: 500;
}

.emotion-12 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 1.1111111111111112vw);
  line-height: 1.25;
  letter-spacing: 0vw;
  font-weight: 500;
}

.emotion-12 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.9722222222222222vw);
  line-height: 1.5714285714285714;
  letter-spacing: 0.04861111111111111vw;
  font-weight: 500;
}

.emotion-12 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.8333333333333334vw);
  line-height: 1.5;
  letter-spacing: 0vw;
  font-weight: 500;
}

.emotion-12 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.6944444444444444vw);
  line-height: 1.5;
  letter-spacing: 0.034722222222222224vw;
  font-weight: 500;
}

.emotion-12 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, 1.1805555555555556vw);
  line-height: 1.588235294117647;
  letter-spacing: 0.11805555555555555vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-12 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, 1.1111111111111112vw);
  line-height: 1.75;
  letter-spacing: 0.1111111111111111vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-12 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, 0.8333333333333334vw);
  line-height: 1.8333333333333333;
  letter-spacing: 0.056944444444444436vw;
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-12 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 4.166666666666666vw);
  line-height: 1;
  letter-spacing: 0.16666666666666666vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-12 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 3.888888888888889vw);
  line-height: 1;
  letter-spacing: 0.19444444444444445vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-12 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 3.4722222222222223vw);
  line-height: 1;
  letter-spacing: 0.1736111111111111vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-12 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 2.7777777777777777vw);
  line-height: 1;
  letter-spacing: 0.19444444444444445vw;
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-12 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 2.5vw);
  line-height: 1;
  letter-spacing: 0.25vw;
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-12 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.9444444444444444vw);
  line-height: 1.1428571428571428;
  letter-spacing: 0.1048611111111111vw;
  text-transform: none;
  font-weight: 600;
}

.emotion-12 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.3888888888888888vw);
  line-height: 1.2;
  letter-spacing: 0.09027777777777779vw;
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-12 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 8.61111111111111vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 5.833333333333333vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 4.444444444444445vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 2.361111111111111vw);
  line-height: 1;
  letter-spacing: -0.027777777777777776vw;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 6.944444444444445vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-12 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 5.555555555555555vw);
  line-height: 1;
  letter-spacing: 0vw;
  font-weight: 600;
}

.emotion-12 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1.1111111111111112vw;
  line-height: 0.0625;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.6666666666666667vw);
  line-height: 1.5;
  letter-spacing: 0.08333333333333333vw;
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-12 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 1.25vw);
  line-height: 1.5555555555555556;
  letter-spacing: 0.025vw;
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-12 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, 0.9722222222222222vw);
  line-height: 1.1428571428571428;
  letter-spacing: 0.04861111111111111vw;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-14 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-column-gap: 2.083333333333333vw;
  column-gap: 2.083333333333333vw;
}

.emotion-15 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  text-align: center;
  min-width: 3.4722222222222223vw;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-16 {
  height: 3.4722222222222223vw;
}

.emotion-17 {
  line-height: 150%;
  font-size: max(10px, 0.8333333333333334vw);
  padding-top: 0.5555555555555556vw;
  max-width: 5.902777777777778vw;
  font-weight: 500;
  text-transform: uppercase;
  color: #111;
}

.emotion-20 {
  line-height: 150%;
  font-size: max(10px, 0.8333333333333334vw);
  padding-top: 0.5555555555555556vw;
  max-width: 5.902777777777778vw;
  font-weight: 500;
  text-transform: uppercase;
  color: #FFFFFF;
}

.emotion-25 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: auto;
  line-height: 1.2777777777777777;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  pointer-events: all;
  min-height: auto;
}

.emotion-25:focus {
  outline: none;
}

.emotion-25>span span {
  height: calc(18px * 0.7133333333333334);
}

.emotion-25 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.5rem * 0.72);
}

.emotion-25 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-25:hover,
.emotion-25:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-25:active {
  text-transform: uppercase;
  text-shadow: none;
}

.emotion-26 {
  box-sizing: border-box;
}

.emotion-27 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-27 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <article
      class="emotion-0"
    >
      <section
        class="emotion-1"
      >
        <div
          class="emotion-2"
          data-testid="product-card-image"
        >
          <img
            alt="image showing a person"
            class="emotion-3"
            src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/Nav4?fmt=webp"
          />
        </div>
      </section>
      <div
        class="emotion-4"
        height="0"
        width="0"
      >
        <div
          class="emotion-5"
          data-testid="gradient-wrapper"
        >
          <div
            class="emotion-6"
          >
            <div
              class="emotion-7"
            >
              <div
                class="emotion-8"
              >
                <div
                  class="emotion-9"
                >
                  <div
                    class="emotion-10"
                    data-testid="advance-image-container-test-id"
                  >
                    <img
                      alt="Default Icon"
                      class="emotion-11"
                      maxminheight="7.291666666666667vw"
                      src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                    />
                  </div>
                </div>
                <div
                  style="text-align: center;"
                >
                  <div
                    class="emotion-12"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-2"
                        >
                          Introducing
                        </span>
                      </p>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--headline-5"
                        >
                          THE ULTIMATE II TIGHT
                        </span>
                      </p>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-3"
                        >
                          Our #1 sweat ready style just got a major upgrade. Lorem ipsum dolor sit amet.
                        </span>
                      </p>
                      <hr
                        aria-hidden="true"
                        style="display:block;border:0;height:8px;margin:0;background:transparent;"
                      />
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-9"
                >
                  <div
                    class="emotion-14"
                  >
                    <div
                      class="emotion-15"
                    >
                      <img
                        alt=""
                        class="emotion-16"
                        src="https://athleta.a.bigcontent.io/v1/static/WATER-RESISTANT_dark"
                      />
                      <span
                        class="emotion-17"
                      >
                        Water-Resistant
                      </span>
                    </div>
                    <div
                      class="emotion-15"
                    >
                      <img
                        alt=""
                        class="emotion-16"
                        src="https://athleta.a.bigcontent.io/v1/static/WATER-REPELLENT_light"
                      />
                      <span
                        class="emotion-20"
                      >
                        Water-Repellent
                      </span>
                    </div>
                    <div
                      class="emotion-15"
                    >
                      <img
                        alt=""
                        class="emotion-16"
                        src="https://athleta.a.bigcontent.io/v1/static/LOW-WATER-USAGE_light"
                      />
                      <span
                        class="emotion-20"
                      >
                        Low Water Usage
                      </span>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-9"
                >
                  <a
                    class="emotion-25"
                    color="dark"
                    href="/VALUE"
                  >
                    <span
                      class="emotion-26"
                    >
                      Shop The Ultimate Collection
                      <span
                        aria-hidden="true"
                        class="emotion-27"
                      >
                        <svg
                          viewBox="0 0 8.207 11.366"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M1.093 11.366L.021 9.983l5.386-4.18L.001 1.35 1.114-.001l7.094 5.843z"
                          />
                          fill=#000000
                        </svg>
                      </span>
                    </span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </article>
  </div>
</DocumentFragment>
`;

exports[`AT FlexibleBannerHP renders a banner with an image as its media, given default data, with scaleLimitPixels 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  max-width: 1400px;
  box-sizing: border-box;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  aspect-ratio: 4/1;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 20px;
  box-sizing: border-box;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  aspect-ratio: 4/1;
  height: 100%;
  position: absolute;
  width: 100%;
  overflow: clip;
}

.emotion-2 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-3 {
  width: 100%;
  aspect-ratio: 4/1;
  object-fit: cover;
}

.emotion-4 {
  background: transparent;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  box-sizing: border-box;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  height: 100%;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  pointer-events: none;
  width: 100%;
  aspect-ratio: 4/1;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  text-align: center;
}

.emotion-5 {
  width: 100%;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0) 100%);
}

.emotion-6 {
  pointerevents: auto;
}

.emotion-7 {
  height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: min(1.3888888888888888vw, 19.444444444444443px) min(2.7777777777777777vw, 38.888888888888886px);
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  text-align: center;
}

.emotion-8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  height: initial;
  padding: 0px;
  gap: min(1.3888888888888888vw, 19.444444444444443px);
}

.emotion-9 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-10 {
  height: auto;
  max-width: 100%;
}

.emotion-11 {
  min-height: min(
              7.291666666666667vw, 
              102.08333333333333px
            );
  max-height: min(
              7.291666666666667vw, 
              102.08333333333333px
            );
}

.emotion-12 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-12 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-12 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-12 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-12 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 9.722222222222221px));
  line-height: 1.5;
  letter-spacing: min(0.034722222222222224vw, 0.4861111111111111px);
  font-weight: 500;
}

.emotion-12 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-12 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-12 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 11.666666666666666px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 11.666666666666666px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 11.666666666666666px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 11.666666666666666px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 17.5px));
  line-height: 1.5555555555555556;
  letter-spacing: min(0.0625vw, 0.875px);
  font-weight: 500;
}

.emotion-12 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.1111111111111112vw, 15.555555555555555px));
  line-height: 1.25;
  letter-spacing: min(0vw, 0px);
  font-weight: 500;
}

.emotion-12 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9722222222222222vw, 13.61111111111111px));
  line-height: 1.5714285714285714;
  letter-spacing: min(0.04861111111111111vw, 0.6805555555555555px);
  font-weight: 500;
}

.emotion-12 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.8333333333333334vw, 11.666666666666666px));
  line-height: 1.5;
  letter-spacing: min(0vw, 0px);
  font-weight: 500;
}

.emotion-12 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 9.722222222222221px));
  line-height: 1.5;
  letter-spacing: min(0.034722222222222224vw, 0.4861111111111111px);
  font-weight: 500;
}

.emotion-12 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1805555555555556vw, 16.52777777777778px));
  line-height: 1.588235294117647;
  letter-spacing: min(0.11805555555555555vw, 1.6527777777777777px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-12 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, min(1.1111111111111112vw, 15.555555555555555px));
  line-height: 1.75;
  letter-spacing: min(0.1111111111111111vw, 1.5555555555555556px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-12 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.8333333333333334vw, 11.666666666666666px));
  line-height: 1.8333333333333333;
  letter-spacing: min(0.056944444444444436vw, 0.7972222222222222px);
  font-weight: 700;
  text-transform: uppercase;
}

.emotion-12 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.166666666666666vw, 58.333333333333336px));
  line-height: 1;
  letter-spacing: min(0.16666666666666666vw, 2.333333333333333px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-12 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(3.888888888888889vw, 54.44444444444444px));
  line-height: 1;
  letter-spacing: min(0.19444444444444445vw, 2.722222222222222px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-12 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(3.4722222222222223vw, 48.61111111111111px));
  line-height: 1;
  letter-spacing: min(0.1736111111111111vw, 2.4305555555555554px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-12 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(2.7777777777777777vw, 38.888888888888886px));
  line-height: 1;
  letter-spacing: min(0.19444444444444445vw, 2.722222222222222px);
  text-transform: uppercase;
  font-weight: 500;
}

.emotion-12 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(2.5vw, 35px));
  line-height: 1;
  letter-spacing: min(0.25vw, 3.5px);
  text-transform: uppercase;
  font-weight: 600;
}

.emotion-12 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.9444444444444444vw, 27.22222222222222px));
  line-height: 1.1428571428571428;
  letter-spacing: min(0.1048611111111111vw, 1.4680555555555554px);
  text-transform: none;
  font-weight: 600;
}

.emotion-12 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.3888888888888888vw, 19.444444444444443px));
  line-height: 1.2;
  letter-spacing: min(0.09027777777777779vw, 1.2638888888888888px);
  text-transform: uppercase;
  font-weight: 700;
}

.emotion-12 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(8.61111111111111vw, 120.55555555555556px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.3888888888888889px);
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.833333333333333vw, 81.66666666666667px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.3888888888888889px);
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.444444444444445vw, 62.22222222222222px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.3888888888888889px);
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(2.361111111111111vw, 33.05555555555556px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.3888888888888889px);
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.1111111111111112vw, 15.555555555555555px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.1111111111111112vw, 15.555555555555555px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-soehne),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.1111111111111112vw, 15.555555555555555px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 97.22222222222221px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-12 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 77.77777777777777px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
}

.emotion-12 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(1.1111111111111112vw, 15.555555555555555px);
  line-height: 0.0625;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.6666666666666667vw, 23.333333333333332px));
  line-height: 1.5;
  letter-spacing: min(0.08333333333333333vw, 1.1666666666666665px);
  font-weight: 400;
  text-transform: uppercase;
}

.emotion-12 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.25vw, 17.5px));
  line-height: 1.5555555555555556;
  letter-spacing: min(0.025vw, 0.35px);
  font-weight: 500;
  text-transform: uppercase;
}

.emotion-12 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.9722222222222222vw, 13.61111111111111px));
  line-height: 1.1428571428571428;
  letter-spacing: min(0.04861111111111111vw, 0.6805555555555555px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-14 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-column-gap: min(2.083333333333333vw, 29.166666666666668px);
  column-gap: min(2.083333333333333vw, 29.166666666666668px);
}

.emotion-15 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  text-align: center;
  min-width: 3.4722222222222223vw;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-16 {
  height: 3.4722222222222223vw;
  max-height: 48.611111111111114px;
}

.emotion-17 {
  line-height: 150%;
  font-size: max(10px, min(0.8333333333333334vw, 11.666666666666666px));
  padding-top: min(0.5555555555555556vw, 7.777777777777778px);
  max-width: min(5.902777777777778vw, 82.63888888888889px);
  font-weight: 500;
  text-transform: uppercase;
  color: #111;
}

.emotion-20 {
  line-height: 150%;
  font-size: max(10px, min(0.8333333333333334vw, 11.666666666666666px));
  padding-top: min(0.5555555555555556vw, 7.777777777777778px);
  max-width: min(5.902777777777778vw, 82.63888888888889px);
  font-weight: 500;
  text-transform: uppercase;
  color: #FFFFFF;
}

.emotion-25 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: none;
  transition: none;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  border-radius: 0;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 0px;
  min-height: auto;
  max-height: auto;
  line-height: 1.2777777777777777;
  padding: 0;
  width: auto;
  font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #000000;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  pointer-events: all;
  min-height: auto;
}

.emotion-25:focus {
  outline: none;
}

.emotion-25>span span {
  height: calc(18px * 0.7133333333333334);
}

.emotion-25 span span {
  padding-left: 3px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.5rem * 0.72);
}

.emotion-25 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #000000;
}

.emotion-25:hover,
.emotion-25:focus {
  text-shadow: 0 0 2px currentColor;
}

.emotion-25:active {
  text-transform: uppercase;
  text-shadow: none;
}

.emotion-26 {
  box-sizing: border-box;
}

.emotion-27 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-27 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <article
      class="emotion-0"
    >
      <section
        class="emotion-1"
      >
        <div
          class="emotion-2"
          data-testid="product-card-image"
        >
          <img
            alt="image showing a person"
            class="emotion-3"
            src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/Nav4?fmt=webp"
          />
        </div>
      </section>
      <div
        class="emotion-4"
        height="0"
        width="0"
      >
        <div
          class="emotion-5"
          data-testid="gradient-wrapper"
        >
          <div
            class="emotion-6"
          >
            <div
              class="emotion-7"
            >
              <div
                class="emotion-8"
              >
                <div
                  class="emotion-9"
                >
                  <div
                    class="emotion-10"
                    data-testid="advance-image-container-test-id"
                  >
                    <img
                      alt="Default Icon"
                      class="emotion-11"
                      maxminheight="min(
            7.291666666666667vw, 
            102.08333333333333px
          )"
                      src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_car-icon?fmt=auto"
                    />
                  </div>
                </div>
                <div
                  style="text-align: center;"
                >
                  <div
                    class="emotion-12"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-2"
                        >
                          Introducing
                        </span>
                      </p>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--headline-5"
                        >
                          THE ULTIMATE II TIGHT
                        </span>
                      </p>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-3"
                        >
                          Our #1 sweat ready style just got a major upgrade. Lorem ipsum dolor sit amet.
                        </span>
                      </p>
                      <hr
                        aria-hidden="true"
                        style="display:block;border:0;height:8px;margin:0;background:transparent;"
                      />
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-9"
                >
                  <div
                    class="emotion-14"
                  >
                    <div
                      class="emotion-15"
                    >
                      <img
                        alt=""
                        class="emotion-16"
                        src="https://athleta.a.bigcontent.io/v1/static/WATER-RESISTANT_dark"
                      />
                      <span
                        class="emotion-17"
                      >
                        Water-Resistant
                      </span>
                    </div>
                    <div
                      class="emotion-15"
                    >
                      <img
                        alt=""
                        class="emotion-16"
                        src="https://athleta.a.bigcontent.io/v1/static/WATER-REPELLENT_light"
                      />
                      <span
                        class="emotion-20"
                      >
                        Water-Repellent
                      </span>
                    </div>
                    <div
                      class="emotion-15"
                    >
                      <img
                        alt=""
                        class="emotion-16"
                        src="https://athleta.a.bigcontent.io/v1/static/LOW-WATER-USAGE_light"
                      />
                      <span
                        class="emotion-20"
                      >
                        Low Water Usage
                      </span>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-9"
                >
                  <a
                    class="emotion-25"
                    color="dark"
                    href="/VALUE"
                  >
                    <span
                      class="emotion-26"
                    >
                      Shop The Ultimate Collection
                      <span
                        aria-hidden="true"
                        class="emotion-27"
                      >
                        <svg
                          viewBox="0 0 8.207 11.366"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M1.093 11.366L.021 9.983l5.386-4.18L.001 1.35 1.114-.001l7.094 5.843z"
                          />
                          fill=#000000
                        </svg>
                      </span>
                    </span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </article>
  </div>
</DocumentFragment>
`;
