// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`Category Cards - Product Cards should match snapshots for Old Navy desktop 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1440px;
}

.emotion-1 {
  background: #EBEBEB;
}

.emotion-2 {
  padding: 2.786377708978328% 2.2222222222222223%;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  margin-bottom: 64px;
  margin: 0 auto 1.744186046511628% auto;
  width: 84.59302325581395%;
}

.emotion-4 {
  width: 100%;
  text-align: center;
}

.emotion-4 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-4 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-4 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-4 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-4 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-4 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-4 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-4 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.72px);
  font-weight: 500;
}

.emotion-4 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.1111111111111112vw, 16px));
  line-height: 1.5;
  letter-spacing: min(0.044444444444444446vw, 0.64px);
  font-weight: 500;
}

.emotion-4 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9722222222222222vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.03888888888888889vw, 0.56px);
}

.emotion-4 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.8333333333333334vw, 12px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.9444444444444444vw, 28px));
  line-height: 1;
  letter-spacing: min(0.07777777777777778vw, 1.12px);
  font-weight: 500;
}

.emotion-4 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.597222222222222vw, 23px));
  line-height: 1;
  letter-spacing: min(0.0638888888888889vw, 0.92px);
  font-weight: 500;
}

.emotion-4 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1805555555555556vw, 17px));
  line-height: 1;
  letter-spacing: min(0.04722222222222222vw, 0.68px);
  font-weight: 500;
}

.emotion-4 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8.194444444444445vw, 118px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 72px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.3333333333333335vw, 48px));
  line-height: 1;
  letter-spacing: min(0.2vw, 2.88px);
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.7777777777777777vw, 40px));
  line-height: 1.1;
  letter-spacing: min(0.16666666666666666vw, 2.4px);
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.2222222222222223vw, 32px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-4 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-4 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 24px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-4 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5277777777777777vw, 22px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-4 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-5 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.emotion-6 {
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  gap: min(1.0416666666666665vw, 15px);
}

.emotion-7 {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
}

.emotion-8 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-9 {
  width: 100%;
  aspect-ratio: 3/4;
  object-fit: cover;
}

.emotion-10 {
  text-align: center;
}

.emotion-10 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-10 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-10 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-10 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-10 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-10 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-10 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-10 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.72px);
  font-weight: 500;
}

.emotion-10 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.1111111111111112vw, 16px));
  line-height: 1.5;
  letter-spacing: min(0.044444444444444446vw, 0.64px);
  font-weight: 500;
}

.emotion-10 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9722222222222222vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.03888888888888889vw, 0.56px);
}

.emotion-10 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.8333333333333334vw, 12px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.9444444444444444vw, 28px));
  line-height: 1;
  letter-spacing: min(0.07777777777777778vw, 1.12px);
  font-weight: 500;
}

.emotion-10 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.597222222222222vw, 23px));
  line-height: 1;
  letter-spacing: min(0.0638888888888889vw, 0.92px);
  font-weight: 500;
}

.emotion-10 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1805555555555556vw, 17px));
  line-height: 1;
  letter-spacing: min(0.04722222222222222vw, 0.68px);
  font-weight: 500;
}

.emotion-10 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8.194444444444445vw, 118px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 72px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.3333333333333335vw, 48px));
  line-height: 1;
  letter-spacing: min(0.2vw, 2.88px);
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.7777777777777777vw, 40px));
  line-height: 1.1;
  letter-spacing: min(0.16666666666666666vw, 2.4px);
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.2222222222222223vw, 32px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-10 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-10 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-10 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-10 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 24px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-10 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5277777777777777vw, 22px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-10 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-11 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: auto;
}

.emotion-12 {
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  max-width: 264px;
}

.emotion-13 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  height: 50px;
  padding-inline: 15px;
}

.emotion-13:focus {
  outline: none;
}

.emotion-13>span {
  padding: 1px 0;
}

.emotion-13:hover,
.emotion-13:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-13:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-13>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-13>span>span {
  position: relative;
  -webkit-transform: translateX(5px);
  -moz-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

.emotion-14 {
  display: inline-block;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-15 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.emotion-15 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-15 svg path {
  fill: currentColor;
}

.emotion-15 svg rect {
  fill: currentColor;
}

.emotion-17 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0 1rem;
  position: absolute;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
}

.emotion-18 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: 1px solid #000000;
}

.emotion-18:last-child {
  border: none;
}

.emotion-19 {
  cursor: pointer;
  display: block;
  padding: 0.5rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: inherit;
  text-align: center;
  color: #000000;
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-28 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  width: 100%;
  z-index: 2;
  position: relative;
  pointer-events: auto;
  height: 50px;
  padding-inline: 15px;
}

.emotion-28:focus {
  outline: none;
}

.emotion-28>span {
  padding: 1px 0;
}

.emotion-28:hover,
.emotion-28:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-28:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-28>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-28>span>span {
  position: relative;
  -webkit-transform: translateX(5px);
  -moz-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
        data-testid="homepage"
      >
        <div
          class="emotion-1"
          height="0"
          width="0"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Headline
Goes Here 
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <div
              class="emotion-5"
            >
              <div
                class="emotion-6"
              >
                <a
                  aria-label="alt text"
                  class="emotion-7"
                  href="cardlink"
                />
                <div
                  class="emotion-8"
                  data-testid="product-card-image"
                >
                  <img
                    alt=""
                    class="emotion-9"
                    src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/220307_38-M6579_Swim_8inBoardShort_M_VIBanner?fmt=webp"
                  />
                </div>
                <div
                  class="emotion-10"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Category Name 1
                      </span>
                    </p>
                  </div>
                </div>
                <div
                  class="emotion-11"
                >
                  <div
                    class="emotion-12"
                    data-testid="ctaDropdownWrapper"
                  >
                    <button
                      aria-expanded="false"
                      class="emotion-13"
                      color="dark"
                    >
                      <span
                        class="emotion-14"
                        data-id="cta-dropdown-label"
                      >
                        CTA ONE
                        <span
                          aria-hidden="true"
                          class="emotion-15"
                        >
                          <svg
                            fill="none"
                            viewBox="0 0 12 12"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              clip-rule="evenodd"
                              d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                              fill="#000000"
                              fill-rule="evenodd"
                            />
                          </svg>
                        </span>
                      </span>
                    </button>
                    <div
                      class="emotion-16"
                    >
                      <ul
                        aria-hidden="true"
                        class="emotion-17"
                      >
                        <li
                          class="emotion-18"
                        >
                          <a
                            breakpoint="desktop"
                            class="emotion-19"
                            href="/CTAValue"
                            target="_self"
                          >
                            CTA ONE
                          </a>
                        </li>
                        <li
                          class="emotion-18"
                        >
                          <a
                            breakpoint="desktop"
                            class="emotion-19"
                            href="/CTAValue"
                            target="_self"
                          >
                            CTA ONE HALF
                          </a>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-8"
                  data-testid="product-card-image"
                >
                  <img
                    alt=""
                    class="emotion-9"
                    src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/220307_43-M6558_Shorts_Denim_Size12_W_VIBanner?fmt=webp"
                  />
                </div>
                <div
                  class="emotion-10"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Category Name 2
                      </span>
                    </p>
                  </div>
                </div>
                <div
                  class="emotion-11"
                >
                  <div
                    class="emotion-12"
                  >
                    <a
                      class="cta-label emotion-28"
                      color="dark"
                      href="/CTATWO"
                      target="_self"
                    >
                      SHOP FOR THE FAM
                    </a>
                  </div>
                </div>
              </div>
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-8"
                  data-testid="product-card-image"
                >
                  <img
                    alt=""
                    class="emotion-9"
                    src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/LSM_210808_M2201_S01365_DenimVI_M_09_v0407_d1?fmt=webp"
                  />
                </div>
                <div
                  class="emotion-10"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Category Name 3
                      </span>
                    </p>
                  </div>
                </div>
                <div
                  class="emotion-11"
                >
                  <div
                    class="emotion-12"
                  >
                    <a
                      class="cta-label emotion-28"
                      color="dark"
                      href="/CTAValue"
                      target="_self"
                    >
                      SHOP FOR THE FAM 1
                    </a>
                  </div>
                </div>
              </div>
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-8"
                  data-testid="product-card-image"
                >
                  <img
                    alt=""
                    class="emotion-9"
                    src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/LSM_210808_M2201_S01365_DenimVI_M_09_v0407_d1?fmt=webp"
                  />
                </div>
                <div
                  class="emotion-10"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Category Name 4
                      </span>
                    </p>
                  </div>
                </div>
                <div
                  class="emotion-11"
                >
                  <div
                    class="emotion-12"
                  >
                    <a
                      class="cta-label emotion-28"
                      color="dark"
                      href="/CTAValue"
                      target="_self"
                    >
                      SHOP FOR THE FAM 2
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Category Cards - Product Cards should match snapshots for Old Navy mobile scroll 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1440px;
}

.emotion-1 {
  background: #F34;
}

.emotion-2 {
  padding: 6.4% 4.266666666666667% 8.533333333333333%;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  margin-bottom: 40px;
  margin: 0 auto 6.997084548104956% auto;
  width: 84.59302325581395%;
}

.emotion-4 {
  width: 100%;
  text-align: center;
}

.emotion-4 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-4 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-4 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-4 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-4 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-4 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-4 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-4 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 53.76px));
  line-height: 1.5;
  letter-spacing: min(0.08960000000000001vw, 1.29024px);
  font-weight: 500;
}

.emotion-4 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 46.08px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 1.1059199999999998px);
  font-weight: 500;
}

.emotion-4 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 46.08px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 1.1059199999999998px);
}

.emotion-4 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.133333333333333vw, 88.32px));
  line-height: 1;
  letter-spacing: min(0.24533333333333335vw, 3.5328px);
  font-weight: 500;
}

.emotion-4 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.066666666666666vw, 72.96px));
  line-height: 1;
  letter-spacing: min(0.20266666666666666vw, 2.9184px);
  font-weight: 500;
}

.emotion-4 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 53.76px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.14933333333333335vw, 2.1504000000000003px);
  font-weight: 500;
}

.emotion-4 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(18.133333333333333vw, 261.12px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(13.333333333333334vw, 192px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(11.466666666666667vw, 165.12px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.6vw, 138.24px));
  line-height: 1;
  letter-spacing: min(0.19199999999999998vw, 2.7647999999999997px);
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.466666666666668vw, 107.52px));
  line-height: 1;
  letter-spacing: min(0.2986666666666667vw, 4.300800000000001px);
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.4vw, 92.16px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.256vw, 3.6864px);
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.333333333333334vw, 76.8px));
  line-height: 1;
  letter-spacing: min(0.128vw, 1.8432px);
  font-weight: 700;
}

.emotion-4 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(13px, min(17.066666666666666vw, 245.76px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(13px, min(14.399999999999999vw, 207.35999999999999px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(13px, min(9.066666666666666vw, 130.56px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(13px, min(6.4vw, 92.16px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 230.39999999999998px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 153.6px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-4 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(5.333333333333334vw, 76.8px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-4 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.8vw, 69.12px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-4 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.266666666666667vw, 61.44px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-5 {
  display: grid;
  gap: 2.666666666666667vw;
  grid-template-columns: repeat(4, 70.39999999999999%);
  overflow-x: scroll;
  scrollbar-width: none;
  margin: unset;
  padding: unset;
  -ms-overflow-style: none;
  margin-left: -4.266666666666667%;
  margin-right: calc(-4.266666666666667% - 1px);
}

.emotion-5::-webkit-scrollbar {
  display: none;
}

.emotion-6 {
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  gap: 3.2vw;
}

.emotion-7 {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
}

.emotion-8 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-9 {
  width: 100%;
  aspect-ratio: 3/4;
  object-fit: cover;
}

.emotion-10 {
  text-align: center;
}

.emotion-10 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-10 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-10 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-10 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-10 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-10 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-10 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-10 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 53.76px));
  line-height: 1.5;
  letter-spacing: min(0.08960000000000001vw, 1.29024px);
  font-weight: 500;
}

.emotion-10 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 46.08px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 1.1059199999999998px);
  font-weight: 500;
}

.emotion-10 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 46.08px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 1.1059199999999998px);
}

.emotion-10 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.133333333333333vw, 88.32px));
  line-height: 1;
  letter-spacing: min(0.24533333333333335vw, 3.5328px);
  font-weight: 500;
}

.emotion-10 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.066666666666666vw, 72.96px));
  line-height: 1;
  letter-spacing: min(0.20266666666666666vw, 2.9184px);
  font-weight: 500;
}

.emotion-10 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 53.76px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.14933333333333335vw, 2.1504000000000003px);
  font-weight: 500;
}

.emotion-10 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(18.133333333333333vw, 261.12px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(13.333333333333334vw, 192px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(11.466666666666667vw, 165.12px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.6vw, 138.24px));
  line-height: 1;
  letter-spacing: min(0.19199999999999998vw, 2.7647999999999997px);
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.466666666666668vw, 107.52px));
  line-height: 1;
  letter-spacing: min(0.2986666666666667vw, 4.300800000000001px);
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.4vw, 92.16px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.256vw, 3.6864px);
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.333333333333334vw, 76.8px));
  line-height: 1;
  letter-spacing: min(0.128vw, 1.8432px);
  font-weight: 700;
}

.emotion-10 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(13px, min(17.066666666666666vw, 245.76px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(13px, min(14.399999999999999vw, 207.35999999999999px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(13px, min(9.066666666666666vw, 130.56px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(13px, min(6.4vw, 92.16px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 230.39999999999998px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-10 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 153.6px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-10 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-10 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(5.333333333333334vw, 76.8px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-10 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.8vw, 69.12px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-10 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.266666666666667vw, 61.44px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-23 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: auto;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 12px;
  -webkit-align-items: normal;
  -webkit-box-align: normal;
  -ms-flex-align: normal;
  align-items: normal;
  display: grid;
  grid-template-rows: auto;
  justify-items: center;
  gap: 8px 5px;
  grid-template-columns: repeat(2, 1fr);
  text-align: center;
  padding: 24px;
  margin: 24px -0.5333333333333333vw 0 -0.5333333333333333vw;
}

.emotion-23>div {
  -webkit-align-self: end;
  -ms-flex-item-align: end;
  align-self: end;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  max-width: 100%;
}

.emotion-23 .cta-label {
  text-align: center;
}

.emotion-24 {
  position: relative;
}

.emotion-25 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  height: 45px;
  padding-inline: 15px;
}

.emotion-25:focus {
  outline: none;
}

.emotion-25>span {
  padding: 1px 0;
}

.emotion-25>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-25>span>span {
  position: relative;
  -webkit-transform: translateX(5px);
  -moz-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

.emotion-26 {
  display: inline-block;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-27 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.emotion-27 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-27 svg path {
  fill: currentColor;
}

.emotion-27 svg rect {
  fill: currentColor;
}

.emotion-29 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: inherit;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
}

.emotion-30 {
  box-sizing: border-box;
  width: auto;
  border-bottom: 1px solid #000000;
}

.emotion-30:last-child {
  border: none;
}

.emotion-31 {
  cursor: pointer;
  display: block;
  padding: 0.75rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: inherit;
  text-align: center;
  color: #000000;
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-35 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  width: 100%;
  z-index: 2;
  position: relative;
  pointer-events: auto;
  height: 45px;
  padding-inline: 15px;
}

.emotion-35:focus {
  outline: none;
}

.emotion-35>span {
  padding: 1px 0;
}

.emotion-35:hover,
.emotion-35:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-35:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-35>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-35>span>span {
  position: relative;
  -webkit-transform: translateX(5px);
  -moz-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
        data-testid="homepage"
      >
        <div
          class="emotion-1"
          height="0"
          width="0"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Mobile headline goes here 
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <div
              class="emotion-5"
            >
              <div
                class="emotion-6"
              >
                <a
                  aria-label="alt text"
                  class="emotion-7"
                  href="cardlink"
                />
                <div
                  class="emotion-8"
                  data-testid="product-card-image"
                >
                  <img
                    alt=""
                    class="emotion-9"
                    src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/220307_38-M6579_Swim_8inBoardShort_M_VIBanner?fmt=webp"
                  />
                </div>
                <div
                  class="emotion-10"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Category Name 1
                      </span>
                    </p>
                  </div>
                </div>
              </div>
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-8"
                  data-testid="product-card-image"
                >
                  <img
                    alt=""
                    class="emotion-9"
                    src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/220307_43-M6558_Shorts_Denim_Size12_W_VIBanner?fmt=webp"
                  />
                </div>
                <div
                  class="emotion-10"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Category Name 2
                      </span>
                    </p>
                  </div>
                </div>
              </div>
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-8"
                  data-testid="product-card-image"
                >
                  <img
                    alt=""
                    class="emotion-9"
                    src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/LSM_210808_M2201_S01365_DenimVI_M_09_v0407_d1?fmt=webp"
                  />
                </div>
                <div
                  class="emotion-10"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Category Name 3
                      </span>
                    </p>
                  </div>
                </div>
              </div>
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-8"
                  data-testid="product-card-image"
                >
                  <img
                    alt=""
                    class="emotion-9"
                    src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/LSM_210808_M2201_S01365_DenimVI_M_09_v0407_d1?fmt=webp"
                  />
                </div>
                <div
                  class="emotion-10"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Category Name 4
                      </span>
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="emotion-23"
            >
              <div
                class="emotion-24"
                data-testid="ctaDropdownWrapper"
              >
                <button
                  aria-expanded="false"
                  class="emotion-25"
                  color="dark"
                >
                  <span
                    class="emotion-26"
                    data-id="cta-dropdown-label"
                  >
                    CTA ONE
                    <span
                      aria-hidden="true"
                      class="emotion-27"
                    >
                      <svg
                        fill="none"
                        viewBox="0 0 12 12"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          clip-rule="evenodd"
                          d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                          fill="#000000"
                          fill-rule="evenodd"
                        />
                      </svg>
                    </span>
                  </span>
                </button>
                <div
                  class="emotion-28"
                >
                  <ul
                    aria-hidden="true"
                    class="emotion-29"
                  >
                    <li
                      class="emotion-30"
                    >
                      <a
                        breakpoint="mobile"
                        class="emotion-31"
                        href="/CTAValue"
                        target="_self"
                      >
                        CTA ONE
                      </a>
                    </li>
                    <li
                      class="emotion-30"
                    >
                      <a
                        breakpoint="mobile"
                        class="emotion-31"
                        href="/CTAValue"
                        target="_self"
                      >
                        CTA ONE HALF
                      </a>
                    </li>
                  </ul>
                </div>
              </div>
              <div
                class="emotion-24"
              >
                <a
                  class="cta-label emotion-35"
                  color="dark"
                  href="/CTATWO"
                  target="_self"
                >
                  SHOP FOR THE FAM
                </a>
              </div>
              <div
                class="emotion-24"
              >
                <a
                  class="cta-label emotion-35"
                  color="dark"
                  href="/CTAValue"
                  target="_self"
                >
                  SHOP FOR THE FAM 1
                </a>
              </div>
              <div
                class="emotion-24"
              >
                <a
                  class="cta-label emotion-35"
                  color="dark"
                  href="/CTAValue"
                  target="_self"
                >
                  SHOP FOR THE FAM 2
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Category Cards - Product Cards should match snapshots for Old Navy mobileExposed 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1440px;
}

.emotion-1 {
  background: #F34;
}

.emotion-2 {
  padding: 6.4% 4.266666666666667% 8.533333333333333%;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  margin-bottom: 40px;
  margin: 0 auto 6.997084548104956% auto;
  width: 84.59302325581395%;
}

.emotion-4 {
  width: 100%;
  text-align: center;
}

.emotion-4 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-4 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-4 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-4 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-4 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-4 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-4 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-4 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 53.76px));
  line-height: 1.5;
  letter-spacing: min(0.08960000000000001vw, 1.29024px);
  font-weight: 500;
}

.emotion-4 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 46.08px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 1.1059199999999998px);
  font-weight: 500;
}

.emotion-4 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 46.08px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 1.1059199999999998px);
}

.emotion-4 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.133333333333333vw, 88.32px));
  line-height: 1;
  letter-spacing: min(0.24533333333333335vw, 3.5328px);
  font-weight: 500;
}

.emotion-4 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.066666666666666vw, 72.96px));
  line-height: 1;
  letter-spacing: min(0.20266666666666666vw, 2.9184px);
  font-weight: 500;
}

.emotion-4 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 53.76px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.14933333333333335vw, 2.1504000000000003px);
  font-weight: 500;
}

.emotion-4 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(18.133333333333333vw, 261.12px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(13.333333333333334vw, 192px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(11.466666666666667vw, 165.12px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.6vw, 138.24px));
  line-height: 1;
  letter-spacing: min(0.19199999999999998vw, 2.7647999999999997px);
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.466666666666668vw, 107.52px));
  line-height: 1;
  letter-spacing: min(0.2986666666666667vw, 4.300800000000001px);
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.4vw, 92.16px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.256vw, 3.6864px);
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.333333333333334vw, 76.8px));
  line-height: 1;
  letter-spacing: min(0.128vw, 1.8432px);
  font-weight: 700;
}

.emotion-4 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(13px, min(17.066666666666666vw, 245.76px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(13px, min(14.399999999999999vw, 207.35999999999999px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(13px, min(9.066666666666666vw, 130.56px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(13px, min(6.4vw, 92.16px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 230.39999999999998px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 153.6px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-4 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(5.333333333333334vw, 76.8px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-4 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.8vw, 69.12px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-4 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.266666666666667vw, 61.44px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-5 {
  display: grid;
  gap: 40px 2.7051397655545535%;
  grid-template-columns: 1fr 1fr;
  display: grid;
  gap: 0 2.7051397655545535%;
  grid-template-areas: '. .' 'CTAs0 CTAs0' '. .' 'CTAs1 CTAs1';
}

.emotion-6 {
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  gap: 12px;
}

.emotion-7 {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
}

.emotion-8 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-9 {
  width: 100%;
  aspect-ratio: 3/4;
  object-fit: cover;
}

.emotion-10 {
  text-align: center;
}

.emotion-10 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-10 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-10 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-10 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-10 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-10 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-10 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-10 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 11px;
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.5;
  letter-spacing: 0.336px;
  font-weight: 500;
}

.emotion-10 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.288px;
  font-weight: 500;
}

.emotion-10 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 12px;
  line-height: 1.5;
  letter-spacing: 0.288px;
}

.emotion-10 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 23px;
  line-height: 1;
  letter-spacing: 0.92px;
  font-weight: 500;
}

.emotion-10 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 19px;
  line-height: 1;
  letter-spacing: 0.76px;
  font-weight: 500;
}

.emotion-10 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 14px;
  line-height: 1.0714285714285714;
  letter-spacing: 0.56px;
  font-weight: 500;
}

.emotion-10 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 68px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 50px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 43px;
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 36px;
  line-height: 1;
  letter-spacing: 0.72px;
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 28px;
  line-height: 1;
  letter-spacing: 1.12px;
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 24px;
  line-height: 1.0833333333333333;
  letter-spacing: 0.96px;
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1;
  letter-spacing: 0.48px;
  font-weight: 700;
}

.emotion-10 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: 64px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: 54px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: 34px;
  line-height: 1;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: 24px;
  line-height: 1.4166666666666667;
  letter-spacing: -0.3px;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 60px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-10 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 40px;
  line-height: 1;
  letter-spacing: 0px;
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-10 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: 14px;
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-10 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 20px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-10 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 18px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-10 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-23 {
  grid-area: CTAs0;
}

.emotion-23:last-of-type {
  margin: 24px 0 0 0;
}

.emotion-23:not(last-of-type) {
  margin: 24px 0;
}

.emotion-24 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: auto;
  display: grid;
  grid-template-rows: auto;
  justify-items: center;
  gap: 8px;
  grid-template-columns: repeat(2, 1fr);
  text-align: center;
  padding: 24px;
}

.emotion-24>div {
  -webkit-align-self: baseline;
  -ms-flex-item-align: baseline;
  align-self: baseline;
  width: 100%;
  max-width: 100%;
}

.emotion-24 .cta-label {
  text-align: center;
}

.emotion-25 {
  position: relative;
  width: 100%;
}

.emotion-26 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  height: 45px;
  padding-inline: 15px;
}

.emotion-26:focus {
  outline: none;
}

.emotion-26>span {
  padding: 1px 0;
}

.emotion-26>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-26>span>span {
  position: relative;
  -webkit-transform: translateX(5px);
  -moz-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

.emotion-27 {
  display: inline-block;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-28 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.emotion-28 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-28 svg path {
  fill: currentColor;
}

.emotion-28 svg rect {
  fill: currentColor;
}

.emotion-30 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: inherit;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
}

.emotion-31 {
  box-sizing: border-box;
  width: auto;
  border-bottom: 1px solid #000000;
}

.emotion-31:last-child {
  border: none;
}

.emotion-32 {
  cursor: pointer;
  display: block;
  padding: 0.75rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: inherit;
  text-align: center;
  color: #000000;
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-36 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  width: 100%;
  z-index: 2;
  position: relative;
  pointer-events: auto;
  height: 45px;
  padding-inline: 15px;
}

.emotion-36:focus {
  outline: none;
}

.emotion-36>span {
  padding: 1px 0;
}

.emotion-36:hover,
.emotion-36:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-36:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-36>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-36>span>span {
  position: relative;
  -webkit-transform: translateX(5px);
  -moz-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

.emotion-37 {
  grid-area: CTAs1;
}

.emotion-37:last-of-type {
  margin: 24px 0 0 0;
}

.emotion-37:not(last-of-type) {
  margin: 24px 0;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
        data-testid="homepage"
      >
        <div
          class="emotion-1"
          height="0"
          width="0"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Mobile headline goes here 
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <div
              class="emotion-5"
            >
              <div
                class="emotion-6"
              >
                <a
                  aria-label="alt text"
                  class="emotion-7"
                  href="cardlink"
                />
                <div
                  class="emotion-8"
                  data-testid="product-card-image"
                >
                  <img
                    alt=""
                    class="emotion-9"
                    src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/220307_38-M6579_Swim_8inBoardShort_M_VIBanner?fmt=webp"
                  />
                </div>
                <div
                  class="emotion-10"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Category Name 1
                      </span>
                    </p>
                  </div>
                </div>
              </div>
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-8"
                  data-testid="product-card-image"
                >
                  <img
                    alt=""
                    class="emotion-9"
                    src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/220307_43-M6558_Shorts_Denim_Size12_W_VIBanner?fmt=webp"
                  />
                </div>
                <div
                  class="emotion-10"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Category Name 2
                      </span>
                    </p>
                  </div>
                </div>
              </div>
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-8"
                  data-testid="product-card-image"
                >
                  <img
                    alt=""
                    class="emotion-9"
                    src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/LSM_210808_M2201_S01365_DenimVI_M_09_v0407_d1?fmt=webp"
                  />
                </div>
                <div
                  class="emotion-10"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Category Name 3
                      </span>
                    </p>
                  </div>
                </div>
              </div>
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-8"
                  data-testid="product-card-image"
                >
                  <img
                    alt=""
                    class="emotion-9"
                    src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/LSM_210808_M2201_S01365_DenimVI_M_09_v0407_d1?fmt=webp"
                  />
                </div>
                <div
                  class="emotion-10"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Category Name 4
                      </span>
                    </p>
                  </div>
                </div>
              </div>
              <div
                class="emotion-23"
              >
                <div
                  class="emotion-24"
                  data-testid="category-cta-container"
                >
                  <div
                    class="emotion-25"
                    data-testid="ctaDropdownWrapper"
                  >
                    <button
                      aria-expanded="false"
                      class="emotion-26"
                      color="dark"
                    >
                      <span
                        class="emotion-27"
                        data-id="cta-dropdown-label"
                      >
                        CTA ONE
                        <span
                          aria-hidden="true"
                          class="emotion-28"
                        >
                          <svg
                            fill="none"
                            viewBox="0 0 12 12"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              clip-rule="evenodd"
                              d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                              fill="#000000"
                              fill-rule="evenodd"
                            />
                          </svg>
                        </span>
                      </span>
                    </button>
                    <div
                      class="emotion-29"
                    >
                      <ul
                        aria-hidden="true"
                        class="emotion-30"
                      >
                        <li
                          class="emotion-31"
                        >
                          <a
                            breakpoint="mobile"
                            class="emotion-32"
                            href="/CTAValue"
                            target="_self"
                          >
                            CTA ONE
                          </a>
                        </li>
                        <li
                          class="emotion-31"
                        >
                          <a
                            breakpoint="mobile"
                            class="emotion-32"
                            href="/CTAValue"
                            target="_self"
                          >
                            CTA ONE HALF
                          </a>
                        </li>
                      </ul>
                    </div>
                  </div>
                  <div
                    class="emotion-25"
                  >
                    <a
                      class="cta-label emotion-36"
                      color="dark"
                      href="/CTATWO"
                      target="_self"
                    >
                      SHOP FOR THE FAM
                    </a>
                  </div>
                </div>
              </div>
              <div
                class="emotion-37"
              >
                <div
                  class="emotion-24"
                  data-testid="category-cta-container"
                >
                  <div
                    class="emotion-25"
                  >
                    <a
                      class="cta-label emotion-36"
                      color="dark"
                      href="/CTAValue"
                      target="_self"
                    >
                      SHOP FOR THE FAM 1
                    </a>
                  </div>
                  <div
                    class="emotion-25"
                  >
                    <a
                      class="cta-label emotion-36"
                      color="dark"
                      href="/CTAValue"
                      target="_self"
                    >
                      SHOP FOR THE FAM 2
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Category Cards - Product Cards should match snapshots for Old Navy when 'on-cta-redesign-2024' feature flag is enabled 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1440px;
}

.emotion-1 {
  background: #EBEBEB;
}

.emotion-2 {
  padding: 2.786377708978328% 2.2222222222222223%;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  margin-bottom: 64px;
  margin: 0 auto 1.744186046511628% auto;
  width: 84.59302325581395%;
}

.emotion-4 {
  width: 100%;
  text-align: center;
}

.emotion-4 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-4 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-4 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-4 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-4 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-4 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-4 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-4 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.72px);
  font-weight: 500;
}

.emotion-4 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.1111111111111112vw, 16px));
  line-height: 1.5;
  letter-spacing: min(0.044444444444444446vw, 0.64px);
  font-weight: 500;
}

.emotion-4 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9722222222222222vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.03888888888888889vw, 0.56px);
}

.emotion-4 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.8333333333333334vw, 12px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-4 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.9444444444444444vw, 28px));
  line-height: 1;
  letter-spacing: min(0.07777777777777778vw, 1.12px);
  font-weight: 500;
}

.emotion-4 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.597222222222222vw, 23px));
  line-height: 1;
  letter-spacing: min(0.0638888888888889vw, 0.92px);
  font-weight: 500;
}

.emotion-4 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1805555555555556vw, 17px));
  line-height: 1;
  letter-spacing: min(0.04722222222222222vw, 0.68px);
  font-weight: 500;
}

.emotion-4 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8.194444444444445vw, 118px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 72px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.3333333333333335vw, 48px));
  line-height: 1;
  letter-spacing: min(0.2vw, 2.88px);
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.7777777777777777vw, 40px));
  line-height: 1.1;
  letter-spacing: min(0.16666666666666666vw, 2.4px);
  font-weight: 700;
}

.emotion-4 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.2222222222222223vw, 32px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-4 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-4 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-4 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-4 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 24px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-4 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5277777777777777vw, 22px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-4 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-5 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.emotion-6 {
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  gap: min(1.0416666666666665vw, 15px);
}

.emotion-7 {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
}

.emotion-8 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-9 {
  width: 100%;
  aspect-ratio: 3/4;
  object-fit: cover;
}

.emotion-10 {
  text-align: center;
}

.emotion-10 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-10 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-10 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-10 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-10 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-10 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-10 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-10 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.72px);
  font-weight: 500;
}

.emotion-10 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.1111111111111112vw, 16px));
  line-height: 1.5;
  letter-spacing: min(0.044444444444444446vw, 0.64px);
  font-weight: 500;
}

.emotion-10 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9722222222222222vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.03888888888888889vw, 0.56px);
}

.emotion-10 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.8333333333333334vw, 12px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.9444444444444444vw, 28px));
  line-height: 1;
  letter-spacing: min(0.07777777777777778vw, 1.12px);
  font-weight: 500;
}

.emotion-10 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.597222222222222vw, 23px));
  line-height: 1;
  letter-spacing: min(0.0638888888888889vw, 0.92px);
  font-weight: 500;
}

.emotion-10 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1805555555555556vw, 17px));
  line-height: 1;
  letter-spacing: min(0.04722222222222222vw, 0.68px);
  font-weight: 500;
}

.emotion-10 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8.194444444444445vw, 118px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 72px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.3333333333333335vw, 48px));
  line-height: 1;
  letter-spacing: min(0.2vw, 2.88px);
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.7777777777777777vw, 40px));
  line-height: 1.1;
  letter-spacing: min(0.16666666666666666vw, 2.4px);
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.2222222222222223vw, 32px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-10 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-10 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-10 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-10 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 24px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-10 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5277777777777777vw, 22px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-10 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-11 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: auto;
}

.emotion-12 {
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  max-width: 264px;
}

.emotion-13 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  height: 50px;
  padding-inline: 15px;
}

.emotion-13:focus {
  outline: none;
}

.emotion-13>span {
  padding: 1px 0;
}

.emotion-13:hover,
.emotion-13:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-13:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-13>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-13>span>span {
  position: relative;
  -webkit-transform: translateX(5px);
  -moz-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

.emotion-14 {
  display: inline-block;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-15 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.emotion-15 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-15 svg path {
  fill: currentColor;
}

.emotion-15 svg rect {
  fill: currentColor;
}

.emotion-17 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0 1rem;
  position: absolute;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
}

.emotion-18 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: 1px solid #000000;
}

.emotion-18:last-child {
  border: none;
}

.emotion-19 {
  cursor: pointer;
  display: block;
  padding: 0.5rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: inherit;
  text-align: center;
  color: #000000;
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-28 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  width: 100%;
  z-index: 2;
  position: relative;
  pointer-events: auto;
  height: 50px;
  padding-inline: 15px;
}

.emotion-28:focus {
  outline: none;
}

.emotion-28>span {
  padding: 1px 0;
}

.emotion-28:hover,
.emotion-28:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-28:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-28>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-28>span>span {
  position: relative;
  -webkit-transform: translateX(5px);
  -moz-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <div
        class="emotion-0"
        data-testid="homepage"
      >
        <div
          class="emotion-1"
          height="0"
          width="0"
        >
          <div
            class="emotion-2"
          >
            <div
              class="emotion-3"
            >
              <div
                class="emotion-4"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Headline
Goes Here 
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <div
              class="emotion-5"
            >
              <div
                class="emotion-6"
              >
                <a
                  aria-label="alt text"
                  class="emotion-7"
                  href="cardlink"
                />
                <div
                  class="emotion-8"
                  data-testid="product-card-image"
                >
                  <img
                    alt=""
                    class="emotion-9"
                    src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/220307_38-M6579_Swim_8inBoardShort_M_VIBanner?fmt=webp"
                  />
                </div>
                <div
                  class="emotion-10"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Category Name 1
                      </span>
                    </p>
                  </div>
                </div>
                <div
                  class="emotion-11"
                >
                  <div
                    class="emotion-12"
                    data-testid="ctaDropdownWrapper"
                  >
                    <button
                      aria-expanded="false"
                      class="emotion-13"
                      color="dark"
                    >
                      <span
                        class="emotion-14"
                        data-id="cta-dropdown-label"
                      >
                        CTA ONE
                        <span
                          aria-hidden="true"
                          class="emotion-15"
                        >
                          <svg
                            fill="none"
                            viewBox="0 0 12 12"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              clip-rule="evenodd"
                              d="M7 0H5v5H0v2h5v5h2V7h5V5H7V0z"
                              fill="#000000"
                              fill-rule="evenodd"
                            />
                          </svg>
                        </span>
                      </span>
                    </button>
                    <div
                      class="emotion-16"
                    >
                      <ul
                        aria-hidden="true"
                        class="emotion-17"
                      >
                        <li
                          class="emotion-18"
                        >
                          <a
                            breakpoint="desktop"
                            class="emotion-19"
                            href="/CTAValue"
                            target="_self"
                          >
                            CTA ONE
                          </a>
                        </li>
                        <li
                          class="emotion-18"
                        >
                          <a
                            breakpoint="desktop"
                            class="emotion-19"
                            href="/CTAValue"
                            target="_self"
                          >
                            CTA ONE HALF
                          </a>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-8"
                  data-testid="product-card-image"
                >
                  <img
                    alt=""
                    class="emotion-9"
                    src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/220307_43-M6558_Shorts_Denim_Size12_W_VIBanner?fmt=webp"
                  />
                </div>
                <div
                  class="emotion-10"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Category Name 2
                      </span>
                    </p>
                  </div>
                </div>
                <div
                  class="emotion-11"
                >
                  <div
                    class="emotion-12"
                  >
                    <a
                      class="cta-label emotion-28"
                      color="dark"
                      href="/CTATWO"
                      target="_self"
                    >
                      SHOP FOR THE FAM
                    </a>
                  </div>
                </div>
              </div>
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-8"
                  data-testid="product-card-image"
                >
                  <img
                    alt=""
                    class="emotion-9"
                    src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/LSM_210808_M2201_S01365_DenimVI_M_09_v0407_d1?fmt=webp"
                  />
                </div>
                <div
                  class="emotion-10"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Category Name 3
                      </span>
                    </p>
                  </div>
                </div>
                <div
                  class="emotion-11"
                >
                  <div
                    class="emotion-12"
                  >
                    <a
                      class="cta-label emotion-28"
                      color="dark"
                      href="/CTAValue"
                      target="_self"
                    >
                      SHOP FOR THE FAM 1
                    </a>
                  </div>
                </div>
              </div>
              <div
                class="emotion-6"
              >
                <div
                  class="emotion-8"
                  data-testid="product-card-image"
                >
                  <img
                    alt=""
                    class="emotion-9"
                    src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/LSM_210808_M2201_S01365_DenimVI_M_09_v0407_d1?fmt=webp"
                  />
                </div>
                <div
                  class="emotion-10"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-1"
                      >
                        Category Name 4
                      </span>
                    </p>
                  </div>
                </div>
                <div
                  class="emotion-11"
                >
                  <div
                    class="emotion-12"
                  >
                    <a
                      class="cta-label emotion-28"
                      color="dark"
                      href="/CTAValue"
                      target="_self"
                    >
                      SHOP FOR THE FAM 2
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
