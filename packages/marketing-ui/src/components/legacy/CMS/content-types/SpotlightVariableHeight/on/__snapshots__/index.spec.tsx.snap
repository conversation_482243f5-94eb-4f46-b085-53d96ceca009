// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SpotlightVariableHeight - Old Navy should match snapshots matches snapshots for desktop 1`] = `
.emotion-0 {
  max-width: 1440px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: auto;
}

.emotion-1 {
  background: rgb(203, 214, 230);
  width: 100%;
}

.emotion-2 {
  padding: min(40px, 2.7777777777777777vw) min(147px, 10.208333333333334vw) min(32px, 2.2222222222222223vw);
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: min(24px, 1.6666666666666667vw);
}

.emotion-4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  text-align: left;
  width: 100%;
}

.emotion-5 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-5 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-5 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-5 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-5 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-5 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-5 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-5 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.72px);
  font-weight: 500;
}

.emotion-5 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.1111111111111112vw, 16px));
  line-height: 1.5;
  letter-spacing: min(0.044444444444444446vw, 0.64px);
  font-weight: 500;
}

.emotion-5 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9722222222222222vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.03888888888888889vw, 0.56px);
}

.emotion-5 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.8333333333333334vw, 12px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.9444444444444444vw, 28px));
  line-height: 1;
  letter-spacing: min(0.07777777777777778vw, 1.12px);
  font-weight: 500;
}

.emotion-5 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.597222222222222vw, 23px));
  line-height: 1;
  letter-spacing: min(0.0638888888888889vw, 0.92px);
  font-weight: 500;
}

.emotion-5 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1805555555555556vw, 17px));
  line-height: 1;
  letter-spacing: min(0.04722222222222222vw, 0.68px);
  font-weight: 500;
}

.emotion-5 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8.194444444444445vw, 118px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-5 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-5 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 72px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-5 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-5 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.3333333333333335vw, 48px));
  line-height: 1;
  letter-spacing: min(0.2vw, 2.88px);
  font-weight: 700;
}

.emotion-5 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.7777777777777777vw, 40px));
  line-height: 1.1;
  letter-spacing: min(0.16666666666666666vw, 2.4px);
  font-weight: 700;
}

.emotion-5 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.2222222222222223vw, 32px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-5 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-5 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 24px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-5 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5277777777777777vw, 22px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-5 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: auto;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  gap: 60px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-8 {
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  max-width: 264px;
}

.emotion-9 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  height: 50px;
  padding-inline: 15px;
}

.emotion-9:focus {
  outline: none;
}

.emotion-9>span {
  padding: 1px 0;
}

.emotion-9:hover,
.emotion-9:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-9:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-9>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-9>span>span {
  position: relative;
  -webkit-transform: translateX(5px);
  -moz-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

.emotion-10 {
  display: inline-block;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-11 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.emotion-11 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-11 svg path {
  fill: currentColor;
}

.emotion-11 svg rect {
  fill: currentColor;
}

.emotion-13 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: 0 1rem;
  position: absolute;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
}

.emotion-14 {
  box-sizing: border-box;
  width: 100%;
  border-bottom: 1px solid #000000;
}

.emotion-14:last-child {
  border: none;
}

.emotion-15 {
  cursor: pointer;
  display: block;
  padding: 0.5rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: inherit;
  text-align: center;
  color: #000000;
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-41 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 48px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 14px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  width: 100%;
  z-index: 2;
  position: relative;
  pointer-events: auto;
  height: 50px;
  padding-inline: 15px;
}

.emotion-41:focus {
  outline: none;
}

.emotion-41>span {
  padding: 1px 0;
}

.emotion-41:hover,
.emotion-41:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-41:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-41>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-41>span>span {
  position: relative;
  -webkit-transform: translateX(5px);
  -moz-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

.emotion-42 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-43 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: min(24px, 1.6666666666666667vw);
}

.emotion-44 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: min(24px, 1.6666666666666667vw);
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) -0.33%, rgba(0, 0, 0, 0.4) 43.94%, rgba(0, 0, 0, 0) 88.73%);
  background-blend-mode: darken;
}

.emotion-45 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 80.83333333333333%;
  padding: min(32px, 2.2222222222222223vw) min(147px, 10.208333333333334vw);
  gap: min(24px, 1.6666666666666667vw);
  pointer-events: none;
}

.emotion-46 {
  text-align: center;
}

.emotion-46 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-46 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-46 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-46 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-46 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-46 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-46 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-46 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-46 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-46 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-46 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-46 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.72px);
  font-weight: 500;
}

.emotion-46 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.1111111111111112vw, 16px));
  line-height: 1.5;
  letter-spacing: min(0.044444444444444446vw, 0.64px);
  font-weight: 500;
}

.emotion-46 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9722222222222222vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.03888888888888889vw, 0.56px);
}

.emotion-46 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.8333333333333334vw, 12px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-46 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-46 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.9444444444444444vw, 28px));
  line-height: 1;
  letter-spacing: min(0.07777777777777778vw, 1.12px);
  font-weight: 500;
}

.emotion-46 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.597222222222222vw, 23px));
  line-height: 1;
  letter-spacing: min(0.0638888888888889vw, 0.92px);
  font-weight: 500;
}

.emotion-46 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1805555555555556vw, 17px));
  line-height: 1;
  letter-spacing: min(0.04722222222222222vw, 0.68px);
  font-weight: 500;
}

.emotion-46 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8.194444444444445vw, 118px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-46 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-46 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 72px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-46 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-46 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.3333333333333335vw, 48px));
  line-height: 1;
  letter-spacing: min(0.2vw, 2.88px);
  font-weight: 700;
}

.emotion-46 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.7777777777777777vw, 40px));
  line-height: 1.1;
  letter-spacing: min(0.16666666666666666vw, 2.4px);
  font-weight: 700;
}

.emotion-46 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.2222222222222223vw, 32px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-46 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-46 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-46 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-46 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-46 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-46 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-46 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-46 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-46 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-46 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-46 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 24px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-46 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5277777777777777vw, 22px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-46 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-47 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: auto;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 60px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-106 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-107 {
  width: 100%;
  aspect-ratio: 1440/800;
  object-fit: cover;
}

.emotion-108 {
  height: 100%;
}

.emotion-109 {
  box-sizing: border-box;
  width: 100%;
  position: absolute;
  bottom: 0;
  pointer-events: none;
  padding: 0px 32px 24px 32px;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0) 100%);
  display: grid;
  grid-template-columns: auto auto;
  grid-template-rows: auto auto;
  grid-template-areas: 'top-left top-right' 'bottom-left bottom-right';
  grid-row-gap: 10px;
}

.emotion-110 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  grid-area: bottom-left;
}

.emotion-111 {
  pointer-events: auto;
}

.emotion-111 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-111 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-111 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-111 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-111 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-111 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-111 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-111 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-111 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-111 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-111 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-111 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 18px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.72px);
  font-weight: 500;
}

.emotion-111 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.1111111111111112vw, 16px));
  line-height: 1.5;
  letter-spacing: min(0.044444444444444446vw, 0.64px);
  font-weight: 500;
}

.emotion-111 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9722222222222222vw, 14px));
  line-height: 1.5;
  letter-spacing: min(0.03888888888888889vw, 0.56px);
}

.emotion-111 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.8333333333333334vw, 12px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-111 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 10px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-111 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.9444444444444444vw, 28px));
  line-height: 1;
  letter-spacing: min(0.07777777777777778vw, 1.12px);
  font-weight: 500;
}

.emotion-111 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.597222222222222vw, 23px));
  line-height: 1;
  letter-spacing: min(0.0638888888888889vw, 0.92px);
  font-weight: 500;
}

.emotion-111 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1805555555555556vw, 17px));
  line-height: 1;
  letter-spacing: min(0.04722222222222222vw, 0.68px);
  font-weight: 500;
}

.emotion-111 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8.194444444444445vw, 118px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-111 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-111 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 72px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-111 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-111 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.3333333333333335vw, 48px));
  line-height: 1;
  letter-spacing: min(0.2vw, 2.88px);
  font-weight: 700;
}

.emotion-111 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.7777777777777777vw, 40px));
  line-height: 1.1;
  letter-spacing: min(0.16666666666666666vw, 2.4px);
  font-weight: 700;
}

.emotion-111 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.2222222222222223vw, 32px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.28px);
  font-weight: 700;
}

.emotion-111 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(7.916666666666666vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-111 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(5.833333333333333vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-111 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(4.444444444444445vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-111 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.361111111111111vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.4px);
  font-weight: 400;
}

.emotion-111 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-111 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-111 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-111 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-111 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-111 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-111 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 24px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-111 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5277777777777777vw, 22px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-111 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 20px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-112 {
  grid-area: bottom-right;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: end;
  justify-content: end;
}

.emotion-113 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 0;
  padding: 0;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
}

.emotion-114 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
  text-transform: none;
  font-weight: 500;
  min-height: auto;
  color: #FFFFFF;
  font-size: 12px;
  letter-spacing: 0.24px;
  line-height: normal;
  padding: 0;
}

.emotion-115 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.05px;
  min-height: auto;
  max-height: auto;
  line-height: 1.2857142857142858;
  padding: 0;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #003764;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  text-transform: capitalize;
  text-transform: capitalize;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 10px;
  line-height: normal;
  min-height: 10px;
  letter-spacing: normal;
  padding: 0;
  margin-left: 8px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  pointer-events: auto;
}

.emotion-115:focus {
  outline: none;
}

.emotion-115>span {
  padding: 1px 0;
}

.emotion-115 span span {
  padding-left: 1px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1rem * 0.7);
}

.emotion-115 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #003764;
}

.emotion-115 span span {
  padding-left: initial;
}

.emotion-115:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-115:focus-visible {
  outline: auto;
}

.emotion-116 {
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
}

.emotion-117 {
  background: -webkit-linear-gradient(45deg, #DDDDFF,#3333FF);
  width: 100%;
}

.emotion-118 {
  padding: min(24px, 1.6666666666666667vw) min(147px, 10.208333333333334vw) min(40px, 2.7777777777777777vw);
}

.emotion-119 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  gap: min(24px, 1.6666666666666667vw);
}

.emotion-120 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  text-align: right;
  width: 100%;
}

.emotion-123 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  height: auto;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: flex-end;
  justify-content: flex-end;
  gap: 60px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

<div>
  <div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
        height="0"
        width="0"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-3"
          >
            <div
              class="emotion-4"
            >
              <div
                class="emotion-5"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--headline-1"
                    >
                      Above section
                    </span>
                  </p>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Above section body
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <div
              class="emotion-4"
            >
              <div
                class="emotion-7"
              >
                <div
                  class="emotion-8"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-9"
                    color="dark"
                  >
                    <span
                      class="emotion-10"
                      data-id="cta-dropdown-label"
                    >
                      Summer Styles
                      <span
                        aria-hidden="true"
                        class="emotion-11"
                      >
                        <svg
                          viewBox="0 0 10.5 10.5"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                            fill="#003764"
                          />
                        </svg>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-12"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-13"
                    >
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-15"
                          href="/Jean Shorts"
                          target="_self"
                        >
                          Jean Shorts
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-15"
                          href="/Dresses"
                          target="_self"
                        >
                          Dresses
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-15"
                          href="/Tank Tops"
                          target="_self"
                        >
                          Tank Tops Tank Tops Tank Tops Tank Tops Tank Tops Tank
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div
                  class="emotion-8"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-9"
                    color="dark"
                  >
                    <span
                      class="emotion-10"
                      data-id="cta-dropdown-label"
                    >
                      Halloween Styles
                      <span
                        aria-hidden="true"
                        class="emotion-11"
                      >
                        <svg
                          viewBox="0 0 10.5 10.5"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                            fill="#003764"
                          />
                        </svg>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-12"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-13"
                    >
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-15"
                          href="/Pajamas"
                          target="_self"
                        >
                          Pajamas
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-15"
                          href="/Graphic Tees"
                          target="_self"
                        >
                          Graphic Tees
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div
                  class="emotion-8"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-9"
                    color="dark"
                  >
                    <span
                      class="emotion-10"
                      data-id="cta-dropdown-label"
                    >
                      Winter Styles
                      <span
                        aria-hidden="true"
                        class="emotion-11"
                      >
                        <svg
                          viewBox="0 0 10.5 10.5"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                            fill="#003764"
                          />
                        </svg>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-12"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-13"
                    >
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-15"
                          href="/Long Coats"
                          target="_self"
                        >
                          Long Coats
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-15"
                          href="/Sweaters"
                          target="_self"
                        >
                          Sweaters
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div
                  class="emotion-8"
                >
                  <a
                    class="cta-label emotion-41"
                    color="dark"
                    href="/Shop All"
                    target="_self"
                  >
                    Shop All
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-42"
        height="0"
        width="0"
      >
        <div
          class="emotion-43"
        >
          <div
            class="emotion-44"
          >
            <div
              class="emotion-45"
            >
              <div
                class="emotion-46"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--headline-3"
                      style="color:#FFFFFF"
                    >
                      Large Headline Goes Here.
                    </span>
                  </p>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--subhead-1"
                      style="color:#FFFFFF"
                    >
                      Subheading goes here.
                    </span>
                  </p>
                </div>
              </div>
              <div
                class="emotion-47"
              >
                <div
                  class="emotion-8"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-9"
                    color="dark"
                  >
                    <span
                      class="emotion-10"
                      data-id="cta-dropdown-label"
                    >
                      Shop All
                      <span
                        aria-hidden="true"
                        class="emotion-11"
                      >
                        <svg
                          viewBox="0 0 10.5 10.5"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                            fill="#003764"
                          />
                        </svg>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-12"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-13"
                    >
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-15"
                          href="/Women"
                          target="_self"
                        >
                          Women
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-15"
                          href="/Men"
                          target="_self"
                        >
                          Men
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-15"
                          href="/Girls"
                          target="_self"
                        >
                          Girls
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-15"
                          href="/Boys"
                          target="_self"
                        >
                          Boys
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-15"
                          href="/Toddler Girls"
                          target="_self"
                        >
                          Toddler Girls
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-15"
                          href="/Toddler Boys"
                          target="_self"
                        >
                          Toddler Boys
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-15"
                          href="/Baby Girls"
                          target="_self"
                        >
                          Baby Girls
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-15"
                          href="/Baby Boys"
                          target="_self"
                        >
                          Baby Boys
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div
                  class="emotion-8"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-9"
                    color="dark"
                  >
                    <span
                      class="emotion-10"
                      data-id="cta-dropdown-label"
                    >
                      Shop Pants
                      <span
                        aria-hidden="true"
                        class="emotion-11"
                      >
                        <svg
                          viewBox="0 0 10.5 10.5"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                            fill="#003764"
                          />
                        </svg>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-12"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-13"
                    >
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-15"
                          href="/jeans"
                          target="_self"
                        >
                          Jeans
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-15"
                          href="/khakis"
                          target="_self"
                        >
                          Khakis
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-15"
                          href="/sweatpants"
                          target="_self"
                        >
                          Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div
                  class="emotion-8"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-9"
                    color="dark"
                  >
                    <span
                      class="emotion-10"
                      data-id="cta-dropdown-label"
                    >
                      Shop New
                      <span
                        aria-hidden="true"
                        class="emotion-11"
                      >
                        <svg
                          viewBox="0 0 10.5 10.5"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                            fill="#003764"
                          />
                        </svg>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-12"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-13"
                    >
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-15"
                          href="/Women"
                          target="_self"
                        >
                          Women
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-15"
                          href="/Men"
                          target="_self"
                        >
                          Men
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-15"
                          href="/Girls"
                          target="_self"
                        >
                          Girls
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-15"
                          href="/Boys"
                          target="_self"
                        >
                          Boys
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-15"
                          href="/Toddler Girls"
                          target="_self"
                        >
                          Toddler Girls
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-15"
                          href="/Toddler Boys"
                          target="_self"
                        >
                          Toddler Boys
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-15"
                          href="/Baby Girls"
                          target="_self"
                        >
                          Baby Girls
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-15"
                          href="/Maternity"
                          target="_self"
                        >
                          Maternity
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-15"
                          href="/Shop for the Fam Fam"
                          target="_self"
                        >
                          Shop for the Fam Fam
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-106"
          data-testid="product-card-image"
        >
          <img
            alt="beach-family"
            class="emotion-107"
            src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/SUM_210521_M1620_S00086_BIGIDEARangeofSilhouettes1_F_06_0351_d1?fmt=webp"
          />
        </div>
        <div
          class="emotion-108"
        >
          <div
            class="emotion-109"
          >
            <div
              class="emotion-110"
            >
              <div
                class="emotion-111"
              >
                <div>
                  Handle Text
                </div>
              </div>
            </div>
            <div
              class="emotion-112"
            >
              <div
                class="emotion-113"
              >
                <span
                  class="emotion-114"
                >
                  Prefix Label
                </span>
              </div>
              <button
                class="emotion-115"
              >
                Details Link
              </button>
            </div>
          </div>
        </div>
        <a
          aria-label="link aria label"
          class="emotion-116"
          data-testid="conditional-link"
          href="/link"
          target="_self"
        />
      </div>
      <div
        class="emotion-117"
        height="0"
        width="0"
      >
        <div
          class="emotion-118"
        >
          <div
            class="emotion-119"
          >
            <div
              class="emotion-120"
            >
              <div
                class="emotion-5"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--headline-1"
                    >
                      Below section
                    </span>
                  </p>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Below section body
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <div
              class="emotion-120"
            >
              <div
                class="emotion-123"
              >
                <div
                  class="emotion-8"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-9"
                    color="dark"
                  >
                    <span
                      class="emotion-10"
                      data-id="cta-dropdown-label"
                    >
                      Shop Sale
                      <span
                        aria-hidden="true"
                        class="emotion-11"
                      >
                        <svg
                          viewBox="0 0 10.5 10.5"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                            fill="#003764"
                          />
                        </svg>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-12"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-13"
                    >
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-15"
                          href="/Mens Sale"
                          target="_self"
                        >
                          Mens Sale
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="desktop"
                          class="emotion-15"
                          href="/Womens Sale"
                          target="_self"
                        >
                          Womens Sale
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`SpotlightVariableHeight - Old Navy should match snapshots matches snapshots for mobile 1`] = `
.emotion-0 {
  max-width: 1440px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: auto;
}

.emotion-1 {
  background: rgb(203, 214, 230);
  width: 100%;
}

.emotion-2 {
  padding: 6.4vw 4.533333333333333vw 8.533333333333333vw;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: 6.4vw;
}

.emotion-4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  text-align: left;
  width: 100%;
}

.emotion-5 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-5 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-5 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-5 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-5 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-5 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-5 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-5 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 53.76px));
  line-height: 1.5;
  letter-spacing: min(0.08960000000000001vw, 1.29024px);
  font-weight: 500;
}

.emotion-5 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 46.08px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 1.1059199999999998px);
  font-weight: 500;
}

.emotion-5 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 46.08px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 1.1059199999999998px);
}

.emotion-5 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.133333333333333vw, 88.32px));
  line-height: 1;
  letter-spacing: min(0.24533333333333335vw, 3.5328px);
  font-weight: 500;
}

.emotion-5 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.066666666666666vw, 72.96px));
  line-height: 1;
  letter-spacing: min(0.20266666666666666vw, 2.9184px);
  font-weight: 500;
}

.emotion-5 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 53.76px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.14933333333333335vw, 2.1504000000000003px);
  font-weight: 500;
}

.emotion-5 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(18.133333333333333vw, 261.12px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-5 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(13.333333333333334vw, 192px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-5 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(11.466666666666667vw, 165.12px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-5 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.6vw, 138.24px));
  line-height: 1;
  letter-spacing: min(0.19199999999999998vw, 2.7647999999999997px);
  font-weight: 700;
}

.emotion-5 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.466666666666668vw, 107.52px));
  line-height: 1;
  letter-spacing: min(0.2986666666666667vw, 4.300800000000001px);
  font-weight: 700;
}

.emotion-5 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.4vw, 92.16px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.256vw, 3.6864px);
  font-weight: 700;
}

.emotion-5 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.333333333333334vw, 76.8px));
  line-height: 1;
  letter-spacing: min(0.128vw, 1.8432px);
  font-weight: 700;
}

.emotion-5 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(13px, min(17.066666666666666vw, 245.76px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(13px, min(14.399999999999999vw, 207.35999999999999px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(13px, min(9.066666666666666vw, 130.56px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(13px, min(6.4vw, 92.16px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 230.39999999999998px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 153.6px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-5 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(5.333333333333334vw, 76.8px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-5 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.8vw, 69.12px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-5 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.266666666666667vw, 61.44px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  gap: 8px;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  display: grid;
  grid-template-rows: auto;
  justify-items: left;
  grid-template-columns: repeat(2, 1fr);
  text-align: left;
}

.emotion-7>div {
  width: 100%;
  max-width: 100%;
  text-align: center;
}

.emotion-7>div>div>ul {
  max-width: 100%;
  width: 100%;
}

.emotion-7>div>div>ul>li {
  max-width: 100%;
  width: 100%;
}

.emotion-7>div>div>ul>li>a {
  max-width: 100%;
  width: 100%;
}

.emotion-7>div .cta-label {
  text-align: center;
  max-width: 100%;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.emotion-8 {
  position: relative;
}

.emotion-9 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: 100%;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  position: relative;
  z-index: 2;
  pointer-events: auto;
  height: 45px;
  padding-inline: 15px;
}

.emotion-9:focus {
  outline: none;
}

.emotion-9>span {
  padding: 1px 0;
}

.emotion-9>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-9>span>span {
  position: relative;
  -webkit-transform: translateX(5px);
  -moz-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

.emotion-10 {
  display: inline-block;
  position: relative;
  width: 100%;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-11 {
  display: inline-block;
  height: 10px;
  width: 10px;
  min-height: 10px;
  min-width: 10px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.emotion-11 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-11 svg path {
  fill: currentColor;
}

.emotion-11 svg rect {
  fill: currentColor;
}

.emotion-13 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background: #FFFFFF;
  box-sizing: border-box;
  list-style-type: none;
  min-width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 390;
  border-width: 0;
  border-style: solid;
  text-align: left;
  letter-spacing: 0;
  pointer-events: auto;
  padding: inherit;
  max-height: 0;
  width: 100%;
  -webkit-transition: max-height .5s ease-in-out,visibility .5s;
  transition: max-height .5s ease-in-out,visibility .5s;
  visibility: hidden;
}

.emotion-14 {
  box-sizing: border-box;
  width: auto;
  border-bottom: 1px solid #000000;
}

.emotion-14:last-child {
  border: none;
}

.emotion-15 {
  cursor: pointer;
  display: block;
  padding: 0.75rem 0;
  -webkit-text-decoration: none;
  text-decoration: none;
  -webkit-transition: color 0.3s ease;
  transition: color 0.3s ease;
  white-space: nowrap;
  font-size: inherit;
  text-align: center;
  color: #000000;
  font-family: var(--font-family-font1),sans-serif;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-41 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.6800000000000002px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.1428571428571428;
  padding: 12px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  width: 100%;
  z-index: 2;
  position: relative;
  pointer-events: auto;
  height: 45px;
  padding-inline: 15px;
}

.emotion-41:focus {
  outline: none;
}

.emotion-41>span {
  padding: 1px 0;
}

.emotion-41:hover,
.emotion-41:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-41:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-41>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-41>span>span {
  position: relative;
  -webkit-transform: translateX(5px);
  -moz-transform: translateX(5px);
  -ms-transform: translateX(5px);
  transform: translateX(5px);
}

.emotion-42 {
  background: transparent;
  position: relative;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-43 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  pointer-events: none;
  gap: 3.2vw;
}

.emotion-44 {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  pointer-events: none;
  gap: 3.2vw;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) -0.33%, rgba(0, 0, 0, 0.4) 43.94%, rgba(0, 0, 0, 0) 88.73%);
  background-blend-mode: darken;
}

.emotion-45 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  padding: 6.4vw 4.533333333333333vw;
  gap: 6.4vw;
  pointer-events: none;
}

.emotion-46 {
  text-align: center;
}

.emotion-46 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-46 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-46 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-46 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-46 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-46 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-46 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-46 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-46 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-46 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-46 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-46 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 53.76px));
  line-height: 1.5;
  letter-spacing: min(0.08960000000000001vw, 1.29024px);
  font-weight: 500;
}

.emotion-46 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 46.08px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 1.1059199999999998px);
  font-weight: 500;
}

.emotion-46 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 46.08px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 1.1059199999999998px);
}

.emotion-46 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-46 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-46 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.133333333333333vw, 88.32px));
  line-height: 1;
  letter-spacing: min(0.24533333333333335vw, 3.5328px);
  font-weight: 500;
}

.emotion-46 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.066666666666666vw, 72.96px));
  line-height: 1;
  letter-spacing: min(0.20266666666666666vw, 2.9184px);
  font-weight: 500;
}

.emotion-46 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 53.76px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.14933333333333335vw, 2.1504000000000003px);
  font-weight: 500;
}

.emotion-46 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(18.133333333333333vw, 261.12px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-46 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(13.333333333333334vw, 192px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-46 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(11.466666666666667vw, 165.12px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-46 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.6vw, 138.24px));
  line-height: 1;
  letter-spacing: min(0.19199999999999998vw, 2.7647999999999997px);
  font-weight: 700;
}

.emotion-46 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.466666666666668vw, 107.52px));
  line-height: 1;
  letter-spacing: min(0.2986666666666667vw, 4.300800000000001px);
  font-weight: 700;
}

.emotion-46 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.4vw, 92.16px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.256vw, 3.6864px);
  font-weight: 700;
}

.emotion-46 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.333333333333334vw, 76.8px));
  line-height: 1;
  letter-spacing: min(0.128vw, 1.8432px);
  font-weight: 700;
}

.emotion-46 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(13px, min(17.066666666666666vw, 245.76px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-46 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(13px, min(14.399999999999999vw, 207.35999999999999px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-46 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(13px, min(9.066666666666666vw, 130.56px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-46 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(13px, min(6.4vw, 92.16px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-46 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-46 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-46 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-46 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 230.39999999999998px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-46 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 153.6px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-46 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-46 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(5.333333333333334vw, 76.8px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-46 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.8vw, 69.12px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-46 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.266666666666667vw, 61.44px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-47 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: auto;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 8px;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  display: grid;
  grid-template-rows: auto;
  justify-items: center;
  grid-template-columns: repeat(2, 1fr);
  text-align: center;
}

.emotion-47 * ul {
  position: absolute;
  max-height: 242px;
  overflow: scroll;
}

@supports selector (::-webkit-scrollbar) {
  .emotion-47 * ul {
    padding-left: 11px;
  }

  .emotion-47 * ul::-webkit-scrollbar {
    -webkit-appearance: none;
  }

  .emotion-47 * ul::-webkit-scrollbar:vertical {
    width: 11px;
    background-color: #FFFFFF;
  }

  .emotion-47 * ul::-webkit-scrollbar:horizontal {
    height: 0;
  }

  .emotion-47 * ul::-webkit-scrollbar-corner {
    background-color: #FFFFFF;
  }

  .emotion-47 * ul::-webkit-scrollbar-thumb {
    border-radius: 8px;
    border: 2px solid #FFF;
    background-color: rgba(0, 0, 0, .5);
  }
}

.emotion-47>div {
  width: 100%;
  max-width: 100%;
}

.emotion-47>div:nth-child(3) {
  grid-column: span 2;
}

.emotion-47>div>div>ul {
  max-width: 100%;
  width: 100%;
}

.emotion-47>div>div>ul>li {
  max-width: 100%;
  width: 100%;
}

.emotion-47>div>div>ul>li>a {
  max-width: 100%;
  width: 100%;
}

.emotion-47>div .cta-label {
  text-align: center;
  max-width: 100%;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.emotion-106 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-107 {
  width: 100%;
  aspect-ratio: 375/468;
  object-fit: cover;
}

.emotion-108 {
  height: 100%;
}

.emotion-109 {
  box-sizing: border-box;
  width: 100%;
  position: absolute;
  bottom: 0;
  pointer-events: none;
  padding: 0px 16px 12px 16px;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0) 100%);
  display: grid;
  grid-template-columns: auto auto;
  grid-template-rows: auto auto;
  grid-template-areas: 'top-left top-right' 'bottom-left bottom-right';
  grid-row-gap: 10px;
}

.emotion-110 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  grid-area: bottom-left;
}

.emotion-111 {
  pointer-events: auto;
}

.emotion-111 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-111 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-111 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-111 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-111 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-111 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-111 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-111 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-111 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-111 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-111 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 42.239999999999995px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-111 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 53.76px));
  line-height: 1.5;
  letter-spacing: min(0.08960000000000001vw, 1.29024px);
  font-weight: 500;
}

.emotion-111 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 46.08px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 1.1059199999999998px);
  font-weight: 500;
}

.emotion-111 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 46.08px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 1.1059199999999998px);
}

.emotion-111 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-111 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 38.4px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-111 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.133333333333333vw, 88.32px));
  line-height: 1;
  letter-spacing: min(0.24533333333333335vw, 3.5328px);
  font-weight: 500;
}

.emotion-111 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.066666666666666vw, 72.96px));
  line-height: 1;
  letter-spacing: min(0.20266666666666666vw, 2.9184px);
  font-weight: 500;
}

.emotion-111 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 53.76px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.14933333333333335vw, 2.1504000000000003px);
  font-weight: 500;
}

.emotion-111 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(18.133333333333333vw, 261.12px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-111 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(13.333333333333334vw, 192px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-111 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(11.466666666666667vw, 165.12px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-111 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.6vw, 138.24px));
  line-height: 1;
  letter-spacing: min(0.19199999999999998vw, 2.7647999999999997px);
  font-weight: 700;
}

.emotion-111 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.466666666666668vw, 107.52px));
  line-height: 1;
  letter-spacing: min(0.2986666666666667vw, 4.300800000000001px);
  font-weight: 700;
}

.emotion-111 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.4vw, 92.16px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.256vw, 3.6864px);
  font-weight: 700;
}

.emotion-111 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.333333333333334vw, 76.8px));
  line-height: 1;
  letter-spacing: min(0.128vw, 1.8432px);
  font-weight: 700;
}

.emotion-111 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(13px, min(17.066666666666666vw, 245.76px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-111 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(13px, min(14.399999999999999vw, 207.35999999999999px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-111 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(13px, min(9.066666666666666vw, 130.56px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-111 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(13px, min(6.4vw, 92.16px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -1.152px);
  font-weight: 400;
}

.emotion-111 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-111 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-111 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-111 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 230.39999999999998px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-111 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 153.6px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-111 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(3.733333333333334vw, 53.76px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-111 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(5.333333333333334vw, 76.8px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-111 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.8vw, 69.12px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-111 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.266666666666667vw, 61.44px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-112 {
  grid-area: bottom-right;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: end;
  justify-content: end;
}

.emotion-113 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  padding: 7px 10px 8px 0;
  padding: 0;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
}

.emotion-114 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 10px;
  line-height: 1.5;
  letter-spacing: 0;
  text-transform: none;
  font-weight: 500;
  min-height: auto;
  color: #FFFFFF;
  font-size: 11px;
  letter-spacing: 0.22px;
  line-height: normal;
  padding: 0;
}

.emotion-115 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
  letter-spacing: 1.05px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.2857142857142858;
  padding: 0;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #003764;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  text-transform: capitalize;
  font-weight: normal;
  text-transform: none;
  z-index: 1;
  color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  font-size: 10px;
  line-height: normal;
  min-height: 10px;
  letter-spacing: normal;
  padding: 0;
  margin-left: 8px;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  pointer-events: auto;
}

.emotion-115:focus {
  outline: none;
}

.emotion-115>span {
  padding: 1px 0;
}

.emotion-115 span span {
  padding-left: 1px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1rem * 0.7);
}

.emotion-115 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #003764;
}

.emotion-115 span span {
  padding-left: initial;
}

.emotion-115:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-115:focus-visible {
  outline: auto;
}

.emotion-116 {
  cursor: pointer;
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0px;
  left: 0px;
}

.emotion-117 {
  background: -webkit-linear-gradient(45deg, #DDDDFF,#3333FF);
  width: 100%;
}

.emotion-119 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  gap: 6.4vw;
}

.emotion-120 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  text-align: right;
  width: 100%;
}

.emotion-123 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: auto;
  -webkit-box-pack: right;
  -ms-flex-pack: right;
  -webkit-justify-content: right;
  justify-content: right;
  gap: 8px;
  -webkit-align-items: end;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: end;
  display: grid;
  grid-template-rows: auto;
  justify-items: right;
  grid-template-columns: repeat(1, 1fr);
  text-align: right;
}

.emotion-123>div {
  width: 100%;
  max-width: 100%;
}

.emotion-123>div:nth-child(1) {
  grid-column: span 2;
}

.emotion-123>div>div>ul {
  max-width: 100%;
  width: 100%;
}

.emotion-123>div>div>ul>li {
  max-width: 100%;
  width: 100%;
}

.emotion-123>div>div>ul>li>a {
  max-width: 100%;
  width: 100%;
}

.emotion-123>div .cta-label {
  text-align: center;
  max-width: 100%;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

<div>
  <div
    style="font-family: var(--font-family-font1),sans-serif;"
  >
    <div
      class="emotion-0"
    >
      <div
        class="emotion-1"
        height="0"
        width="0"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-3"
          >
            <div
              class="emotion-4"
            >
              <div
                class="emotion-5"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--headline-1"
                    >
                      Above section
                    </span>
                  </p>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Above section mobile override text
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <div
              class="emotion-4"
            >
              <div
                class="emotion-7"
              >
                <div
                  class="emotion-8"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-9"
                    color="dark"
                  >
                    <span
                      class="emotion-10"
                      data-id="cta-dropdown-label"
                    >
                      Summer Styles
                      <span
                        aria-hidden="true"
                        class="emotion-11"
                      >
                        <svg
                          viewBox="0 0 10.5 10.5"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                            fill="#003764"
                          />
                        </svg>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-12"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-13"
                    >
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-15"
                          href="/Jean Shorts"
                          target="_self"
                        >
                          Jean Shorts
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-15"
                          href="/Dresses"
                          target="_self"
                        >
                          Dresses
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-15"
                          href="/Tank Tops"
                          target="_self"
                        >
                          Tank Tops Tank Tops Tank Tops Tank Tops Tank Tops Tank
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div
                  class="emotion-8"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-9"
                    color="dark"
                  >
                    <span
                      class="emotion-10"
                      data-id="cta-dropdown-label"
                    >
                      Halloween Styles
                      <span
                        aria-hidden="true"
                        class="emotion-11"
                      >
                        <svg
                          viewBox="0 0 10.5 10.5"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                            fill="#003764"
                          />
                        </svg>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-12"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-13"
                    >
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-15"
                          href="/Pajamas"
                          target="_self"
                        >
                          Pajamas
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-15"
                          href="/Graphic Tees"
                          target="_self"
                        >
                          Graphic Tees
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div
                  class="emotion-8"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-9"
                    color="dark"
                  >
                    <span
                      class="emotion-10"
                      data-id="cta-dropdown-label"
                    >
                      Winter Styles
                      <span
                        aria-hidden="true"
                        class="emotion-11"
                      >
                        <svg
                          viewBox="0 0 10.5 10.5"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                            fill="#003764"
                          />
                        </svg>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-12"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-13"
                    >
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-15"
                          href="/Long Coats"
                          target="_self"
                        >
                          Long Coats
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-15"
                          href="/Sweaters"
                          target="_self"
                        >
                          Sweaters
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div
                  class="emotion-8"
                >
                  <a
                    class="cta-label emotion-41"
                    color="dark"
                    href="/Shop All"
                    target="_self"
                  >
                    Shop All
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="emotion-42"
        height="0"
        width="0"
      >
        <div
          class="emotion-43"
        >
          <div
            class="emotion-44"
          >
            <div
              class="emotion-45"
            >
              <div
                class="emotion-46"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--headline-3"
                      style="color:#FFFFFF"
                    >
                      Large Headline Goes Here.
                    </span>
                  </p>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--subhead-1"
                      style="color:#FFFFFF"
                    >
                      Subheading goes here.
                    </span>
                  </p>
                </div>
              </div>
              <div
                class="emotion-47"
              >
                <div
                  class="emotion-8"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-9"
                    color="dark"
                  >
                    <span
                      class="emotion-10"
                      data-id="cta-dropdown-label"
                    >
                      Shop All
                      <span
                        aria-hidden="true"
                        class="emotion-11"
                      >
                        <svg
                          viewBox="0 0 10.5 10.5"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                            fill="#003764"
                          />
                        </svg>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-12"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-13"
                    >
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-15"
                          href="/Women"
                          target="_self"
                        >
                          Women
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-15"
                          href="/Men"
                          target="_self"
                        >
                          Men
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-15"
                          href="/Girls"
                          target="_self"
                        >
                          Girls
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-15"
                          href="/Boys"
                          target="_self"
                        >
                          Boys
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-15"
                          href="/Toddler Girls"
                          target="_self"
                        >
                          Toddler Girls
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-15"
                          href="/Toddler Boys"
                          target="_self"
                        >
                          Toddler Boys
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-15"
                          href="/Baby Girls"
                          target="_self"
                        >
                          Baby Girls
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-15"
                          href="/Baby Boys"
                          target="_self"
                        >
                          Baby Boys
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div
                  class="emotion-8"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-9"
                    color="dark"
                  >
                    <span
                      class="emotion-10"
                      data-id="cta-dropdown-label"
                    >
                      Shop Pants
                      <span
                        aria-hidden="true"
                        class="emotion-11"
                      >
                        <svg
                          viewBox="0 0 10.5 10.5"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                            fill="#003764"
                          />
                        </svg>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-12"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-13"
                    >
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-15"
                          href="/jeans"
                          target="_self"
                        >
                          Jeans
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-15"
                          href="/khakis"
                          target="_self"
                        >
                          Khakis
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-15"
                          href="/sweatpants"
                          target="_self"
                        >
                          Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants Sweatpants
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div
                  class="emotion-8"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-9"
                    color="dark"
                  >
                    <span
                      class="emotion-10"
                      data-id="cta-dropdown-label"
                    >
                      Shop New
                      <span
                        aria-hidden="true"
                        class="emotion-11"
                      >
                        <svg
                          viewBox="0 0 10.5 10.5"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                            fill="#003764"
                          />
                        </svg>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-12"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-13"
                    >
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-15"
                          href="/Women"
                          target="_self"
                        >
                          Women
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-15"
                          href="/Men"
                          target="_self"
                        >
                          Men
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-15"
                          href="/Girls"
                          target="_self"
                        >
                          Girls
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-15"
                          href="/Boys"
                          target="_self"
                        >
                          Boys
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-15"
                          href="/Toddler Girls"
                          target="_self"
                        >
                          Toddler Girls
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-15"
                          href="/Toddler Boys"
                          target="_self"
                        >
                          Toddler Boys
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-15"
                          href="/Baby Girls"
                          target="_self"
                        >
                          Baby Girls
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-15"
                          href="/Maternity"
                          target="_self"
                        >
                          Maternity
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-15"
                          href="/Shop for the Fam Fam"
                          target="_self"
                        >
                          Shop for the Fam Fam
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="emotion-106"
          data-testid="product-card-image"
        >
          <img
            alt="Mobile image override"
            class="emotion-107"
            src="https://fmzlik0m0z6r1oifp8ex832rf.staging.bigcontent.io/i/oldnavy/230901_14-M5283_LaborDay_CatNav_Tops_HP_US_XL?fmt=webp"
          />
        </div>
        <div
          class="emotion-108"
        >
          <div
            class="emotion-109"
          >
            <div
              class="emotion-110"
            >
              <div
                class="emotion-111"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Handle - Mobile Text Override
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <div
              class="emotion-112"
            >
              <div
                class="emotion-113"
              >
                <span
                  class="emotion-114"
                >
                  Prefix Label
                </span>
              </div>
              <button
                class="emotion-115"
              >
                Details Link
              </button>
            </div>
          </div>
        </div>
        <a
          aria-label="link aria label"
          class="emotion-116"
          data-testid="conditional-link"
          href="/link"
          target="_self"
        />
      </div>
      <div
        class="emotion-117"
        height="0"
        width="0"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-119"
          >
            <div
              class="emotion-120"
            >
              <div
                class="emotion-5"
              >
                <div>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--headline-1"
                    >
                      Below section
                    </span>
                  </p>
                  <p
                    class="amp-cms--p"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Below section mobile override text
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <div
              class="emotion-120"
            >
              <div
                class="emotion-123"
              >
                <div
                  class="emotion-8"
                  data-testid="ctaDropdownWrapper"
                >
                  <button
                    aria-expanded="false"
                    class="emotion-9"
                    color="dark"
                  >
                    <span
                      class="emotion-10"
                      data-id="cta-dropdown-label"
                    >
                      Shop Sale
                      <span
                        aria-hidden="true"
                        class="emotion-11"
                      >
                        <svg
                          viewBox="0 0 10.5 10.5"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M9.75 4.125H6.375V.75a.75.75 0 00-.75-.75h-.75a.75.75 0 00-.75.75v3.375H.75a.75.75 0 00-.75.75v.75a.75.75 0 00.75.75h3.375V9.75a.75.75 0 00.75.75h.75a.75.75 0 00.75-.75V6.375H9.75a.75.75 0 00.75-.75v-.75a.75.75 0 00-.75-.75z"
                            fill="#003764"
                          />
                        </svg>
                      </span>
                    </span>
                  </button>
                  <div
                    class="emotion-12"
                  >
                    <ul
                      aria-hidden="true"
                      class="emotion-13"
                    >
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-15"
                          href="/Mens Sale"
                          target="_self"
                        >
                          Mens Sale
                        </a>
                      </li>
                      <li
                        class="emotion-14"
                      >
                        <a
                          breakpoint="mobile"
                          class="emotion-15"
                          href="/Womens Sale"
                          target="_self"
                        >
                          Womens Sale
                        </a>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
