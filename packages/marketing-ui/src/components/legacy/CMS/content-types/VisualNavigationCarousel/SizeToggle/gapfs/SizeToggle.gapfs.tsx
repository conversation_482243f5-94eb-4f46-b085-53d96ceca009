'use client';

import { CSSObject, css, useTheme } from '@ecom-next/core/react-stitch';
import React, { useMemo } from 'react'; // @ts-ignore
import { Size } from '../../../../../components/ComposableButton/types';
import { useScreenSize, useSelectedState, useViewportIsLarge, useViewportIsMobile } from '../../../../../hooks/index';
import VisualNavigationContainer from '../../../../components/visual-navigation/VisualNavigationContainer';
import useMinScale from '../../../../components/VisualNavigationCarousel/hooks/useMinScale';
import VisualNavigationCarousel from '../../../../components/VisualNavigationCarousel/index';
import { SLICK_CURRENT_CLASSNAME } from '../../../../global/constants/slick-carousel';
import { ComponentMaxWidth } from '../../../../subcomponents/ComponentMaxWidth/index';
import { ShowHideWrapper } from '../../../../subcomponents/ShowHideWrapper/index';
import { customCarouselStyles } from '../../../VisualNavigation/components/VisNavToggleCarouselStyles.gap';
import CategoryCard from '../../../VisualNavigation/components/VisualNavigationCard.gapfs';
import { useToggleCategoryCardState } from '../../../VisualNavigation/VisualNavigationSizeToggle/hooks/useToggleCategoryCardState';
import SizeToggleContainer from '../components/SizeTogglesContainer/index';
import { VisualNavigationSizeToggleCarouselProps } from '../types';

const VisualNavigationSizeToggleCarousel = ({ headline, ctaDropdownList, toggles, categoryCards, webAppearance }: VisualNavigationSizeToggleCarouselProps) => {
  const { chevronColor } = webAppearance;
  const isDesktop = useViewportIsLarge();
  const isLargeVP = useViewportIsLarge();
  const isMobile = useViewportIsMobile();
  const theme = useTheme();
  const screenSize = useScreenSize();
  const [cards, toggleState] = useToggleCategoryCardState(categoryCards, toggles);
  const selectedURL = useSelectedState(categoryCards?.map(card => ({ label: '', value: card.url?.value || '' })) || []); // single select
  const desktopSlidesToShow = 6;
  const mobileSlidesToShow = 2.13;
  const toggleCarouselGap = isLargeVP ? 30 : 8;

  const { width, height } = useMemo(() => {
    const screenWidth = screenSize?.width > 1280 ? 1280 : screenSize?.width || 0;
    const slidesToShow = isLargeVP ? desktopSlidesToShow : mobileSlidesToShow;
    const totalGapWidth = (slidesToShow - 1) * toggleCarouselGap;
    const totalPaddingWidth = isLargeVP ? 64 : 16;
    const availableWidth = screenWidth - totalGapWidth - totalPaddingWidth;
    const cardWidth = Math.floor(availableWidth / slidesToShow);

    const cardHeight = Math.floor(cardWidth * (4 / 3));
    return {
      width: cardWidth,
      height: cardHeight,
    };
  }, [screenSize?.width, cards?.length, desktopSlidesToShow, isLargeVP]);

  const arrowCss: CSSObject = {
    top: height / 2,
    ...(isLargeVP && {
      width: '28px !important',
      height: '28px !important',
      backgroundColor: '#1D1D1D4D',
      transition: 'background-color 0.3s ease',
      '&:hover, &:active': {
        backgroundColor: '#1D1D1D',
      },
      svg: {
        width: '12px !important',
        height: '12px !important',
      },
    }),
  };

  const ctaSize = isDesktop ? Size.large : Size.medium;
  const letterSpacing = isDesktop ? '.32px !important' : '.28px !important';
  const fontSize = isDesktop ? '16px !important' : '14px !important';
  const lineHeight = 1;
  const customCtaDropdownButtonStyles: CSSObject = {
    position: 'relative',
    minHeight: '42px',
    letterSpacing,
    'span[data-id="cta-dropdown-label"]': {
      fontSize,
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      lineHeight,
      letterSpacing,
      '& > span': {
        position: 'relative',
        transform: 'none',
        letterSpacing,
      },
    },
    button: {},
  };

  const customCtaDropdownItemsStyles: CSSObject = {
    position: 'absolute',
    width: undefined,
    li: {
      lineHeight,
      a: {
        letterSpacing,
        fontSize,
      },
    },
  };
  const getCardBorderStyle = (): CSSObject => {
    const CARD_FOOTER_SELECTOR = 'a > div:nth-of-type(2)';
    const firstVisibleCard = `.${SLICK_CURRENT_CLASSNAME} ${CARD_FOOTER_SELECTOR}`;

    return {
      [CARD_FOOTER_SELECTOR]: {},
      [firstVisibleCard]: {
        borderLeft: 'none',
      },
    };
  };

  return (
    <ShowHideWrapper breakpoint='large' showHideBasedOnScreenSize={webAppearance.showHideBasedOnScreenSize}>
      <ComponentMaxWidth>
        <VisualNavigationContainer
          headline={headline}
          headlineHorizontalPadding={!isLargeVP ? 13 : undefined}
          headlinePaddingBottom={isMobile ? 12 : 16}
          innerContainerCSS={{ flexDirection: 'column', gap: isMobile ? '16px' : '24px' }}
          css={{ padding: isLargeVP ? '24px 32px' : '16px 0 16px 16px' }}
          ctaDropdownList={ctaDropdownList}
          ctaSize={ctaSize}
          customCtaDropdownButtonStyles={customCtaDropdownButtonStyles}
          customCtaDropdownItemsStyles={customCtaDropdownItemsStyles}
        >
          <SizeToggleContainer
            onClick={toggleState.set}
            selected={toggleState.value}
            toggleCss={{
              padding: useMinScale(8),
              flex: `0 0 auto`,
            }}
            toggles={toggles}
          />
          <VisualNavigationCarousel
            arrowCss={arrowCss}
            chevronColor={chevronColor}
            infinite={false}
            containerCss={css(getCardBorderStyle())}
            css={customCarouselStyles(toggleCarouselGap, width)}
            cardsToScroll={1}
            slideWidth={width}
            overideDesktopSlidesToShow={desktopSlidesToShow}
            mobileSlidesToShow={mobileSlidesToShow}
            displayMobileArrows
            brand={theme.brand}
          >
            {cards.map(card => (
              <CategoryCard
                key={`category-card-${card.heading}`}
                card={card}
                css={{ height: '100%' }}
                imageAspectRatio={`${width}:${height}`}
                isImageFixedHeight
                selected={!!selectedURL && selectedURL === card.url?.value}
                showHover={isLargeVP}
                isToggleCarousel
              />
            ))}
          </VisualNavigationCarousel>
        </VisualNavigationContainer>
      </ComponentMaxWidth>
    </ShowHideWrapper>
  );
};

export default VisualNavigationSizeToggleCarousel;
