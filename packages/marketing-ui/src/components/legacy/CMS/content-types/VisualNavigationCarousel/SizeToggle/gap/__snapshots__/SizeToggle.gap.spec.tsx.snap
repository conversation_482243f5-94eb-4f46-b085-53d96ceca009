// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`VisualNavigationSizeToggleCarousel Category Cards should match snapshot when card is selected 1`] = `
.emotion-0 {
  outline: none;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 100%;
}

.emotion-1 {
  overflow: hidden;
  -webkit-transition: background-color .25s ease;
  transition: background-color .25s ease;
  background-color: transparent;
  position: relative;
  border-left: 1px solid var(--Interface-Black, #000);
  border-right: 1px solid var(--Interface-Black, #000);
}

.emotion-2 {
  box-sizing: border-box;
  aspect-ratio: 135/180;
  position: relative;
  -webkit-transition: -webkit-transform .25s ease,opacity .25s ease;
  transition: transform .25s ease,opacity .25s ease;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
  opacity: 1;
  width: 100%;
  z-index: 1;
}

.emotion-3 {
  width: 100%;
  aspect-ratio: 135/180;
  object-fit: cover;
}

.emotion-4 {
  background: transparent;
  position: relative;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  z-index: 1;
  box-sizing: border-box;
  position: relative;
  width: 100%;
  min-height: 44px;
  background-color: #2B2B2B;
  color: #FFFFFF;
}

.emotion-5 {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  letter-spacing: 0.32px;
  padding-top: 8px;
}

.emotion-6 {
  box-sizing: border-box;
  left: -1px;
  -webkit-transition: opacity 0.25s ease;
  transition: opacity 0.25s ease;
  position: absolute;
  background-color: #FFFFFF;
  bottom: calc(100%);
  width: calc(100% + 1px);
  opacity: 0;
  padding-block: 15px;
  padding-inline: 15px;
  width: 100%;
}

.emotion-7 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-7 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-7 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-7 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-7 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-7 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-7 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-7 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.40625vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.09375vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.125vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.734375vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.59375vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.46875vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.6875vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-7 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-7 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-7 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-7 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-7 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.953125vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-7 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

<a
  class="emotion-0"
  href="#first"
  target="_self"
>
  <div
    aria-live="polite"
    class="emotion-1"
  >
    <div
      aria-hidden="false"
      class="emotion-2"
    >
      <img
        alt="View"
        class="emotion-3"
        src=""
      />
    </div>
  </div>
  <div
    class="emotion-4"
  >
    <div
      class="emotion-5"
    >
      Category - 1
    </div>
    <div
      aria-hidden="true"
      class="emotion-6"
    >
      <div
        class="emotion-7"
      >
        <div>
          <p
            class="amp-cms--p"
            style="text-align:center;"
          >
            <span
              class="amp-cms--body-2"
            >
              sit amet consectetur adipiscing elit eiusmod.
            </span>
          </p>
        </div>
      </div>
    </div>
  </div>
</a>
`;

exports[`VisualNavigationSizeToggleCarousel in extra large viewports - extra large desktops should match snapshots for extra large viewports 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  aspect-ratio: 1280/365;
  padding: 24px 32px;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  padding-bottom: 16px;
  padding-inline: 0;
}

.emotion-3 {
  font-size: 32px;
  line-height: 38px;
  font-weight: 400;
}

.emotion-3 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-3 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-3 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-3 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-3 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-3 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-3 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.40625vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.09375vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.125vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.734375vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.59375vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.46875vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.6875vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-3 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.953125vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-4 {
  position: relative;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  text-transform: none;
  width: 100%;
  z-index: 2;
  position: relative;
  pointer-events: auto;
  min-height: 42px;
  letter-spacing: .32px!important;
  text-align: left;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5:hover,
.emotion-5:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-5:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-5>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-5 span[data-id="cta-dropdown-label"] {
  font-size: 16px!important;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  line-height: 1;
  letter-spacing: .32px!important;
}

.emotion-5 span[data-id="cta-dropdown-label"]>span {
  position: relative;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
  letter-spacing: .32px!important;
}

.emotion-6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 24px;
}

.emotion-7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 0;
  overflow: hidden;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: 24px;
}

.emotion-8 {
  -webkit-flex: 1 0 content;
  -ms-flex: 1 0 content;
  flex: 1 0 content;
  background-color: #FFFFFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #2B2B2B;
  padding: 6px 0px!important;
  gap: 8px;
  text-transform: capitalize;
  border-bottom: 2px solid transparent;
  outline: none;
  opacity: 1;
  border: 0px;
  border-bottom: 2px solid #000;
  padding: min(0.625vw, 8px);
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
}

.emotion-8 div>div {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 8px;
}

.emotion-8 div {
  text-align: center;
  font-style: normal;
  font-weight: 400;
  line-height: 21.6px;
  letter-spacing: 0.32px;
  line-height: 21.6px!important;
  letter-spacing: 0.32px!important;
  font-size: 16px!important;
  color: #595959!important;
  color: #000!important;
}

.emotion-8 div * {
  text-align: center;
  font-style: normal;
  font-weight: 400;
  line-height: 21.6px;
  letter-spacing: 0.32px;
  line-height: 21.6px!important;
  letter-spacing: 0.32px!important;
  font-size: 16px!important;
}

.emotion-8 div * {
  color: #595959!important;
}

.emotion-8 div * {
  color: #000!important;
}

.emotion-8:hover {
  outline: none;
  opacity: 1;
  border: 0px;
  border-bottom: 2px solid #000;
}

.emotion-8:hover:active {
  color: #000!important;
  border-color: #000!important;
}

.emotion-8:hover:active * {
  color: #000!important;
}

.emotion-8:hover:active {
  color: #000!important;
  border-color: #000!important;
}

.emotion-8:hover:active * {
  color: #000!important;
}

.emotion-8:hover div:active {
  color: #000!important;
  border-color: #000!important;
}

.emotion-8:hover div:active * {
  color: #000!important;
}

.emotion-8:hover div:active {
  color: #000!important;
  border-color: #000!important;
}

.emotion-8:hover div:active * {
  color: #000!important;
}

.emotion-8:hover:active div {
  color: #000!important;
}

.emotion-9 {
  width: 100%;
}

.emotion-9 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-9 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-9 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-9 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-9 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-9 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-9 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.40625vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.09375vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.125vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.734375vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.59375vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.46875vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.6875vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-9 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.953125vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-10 {
  -webkit-flex: 1 0 content;
  -ms-flex: 1 0 content;
  flex: 1 0 content;
  background-color: #FFFFFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #2B2B2B;
  padding: 6px 0px!important;
  gap: 8px;
  text-transform: capitalize;
  border-bottom: 2px solid transparent;
  padding: min(0.625vw, 8px);
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
}

.emotion-10 div>div {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 8px;
}

.emotion-10 div {
  text-align: center;
  font-style: normal;
  font-weight: 400;
  line-height: 21.6px;
  letter-spacing: 0.32px;
  line-height: 21.6px!important;
  letter-spacing: 0.32px!important;
  font-size: 16px!important;
  color: #595959!important;
}

.emotion-10 div * {
  text-align: center;
  font-style: normal;
  font-weight: 400;
  line-height: 21.6px;
  letter-spacing: 0.32px;
  line-height: 21.6px!important;
  letter-spacing: 0.32px!important;
  font-size: 16px!important;
}

.emotion-10 div * {
  color: #595959!important;
}

.emotion-10:hover {
  outline: none;
  opacity: 1;
  border: 0px;
  border-bottom: 2px solid #000;
  color: var(--Brand-Gap-Blue, #031ba1)!important;
  border-color: var(--Brand-Gap-Blue, #031ba1)!important;
  color: var(--Brand-Gap-Blue, #031ba1)!important;
  border-color: var(--Brand-Gap-Blue, #031ba1)!important;
}

.emotion-10:hover * {
  color: var(--Brand-Gap-Blue, #031ba1)!important;
}

.emotion-10:hover:active {
  color: #000!important;
  border-color: #000!important;
}

.emotion-10:hover:active * {
  color: #000!important;
}

.emotion-10:hover * {
  color: var(--Brand-Gap-Blue, #031ba1)!important;
}

.emotion-10:hover:active {
  color: #000!important;
  border-color: #000!important;
}

.emotion-10:hover:active * {
  color: #000!important;
}

.emotion-10:hover div {
  color: var(--Brand-Gap-Blue, #031ba1)!important;
  border-color: var(--Brand-Gap-Blue, #031ba1)!important;
  color: var(--Brand-Gap-Blue, #031ba1)!important;
  border-color: var(--Brand-Gap-Blue, #031ba1)!important;
}

.emotion-10:hover div * {
  color: var(--Brand-Gap-Blue, #031ba1)!important;
}

.emotion-10:hover div:active {
  color: #000!important;
  border-color: #000!important;
}

.emotion-10:hover div:active * {
  color: #000!important;
}

.emotion-10:hover div * {
  color: var(--Brand-Gap-Blue, #031ba1)!important;
}

.emotion-10:hover div:active {
  color: #000!important;
  border-color: #000!important;
}

.emotion-10:hover div:active * {
  color: #000!important;
}

.emotion-10:hover:active div {
  color: #000!important;
}

.emotion-14 {
  overflow: visible;
}

.emotion-14 div.slick-track {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: min(0vw, 0px);
  float: left;
}

.emotion-14 div.slick-track:before {
  display: none;
}

.emotion-14 div.slick-track:after {
  display: none;
}

.emotion-14 div.slick-slide {
  height: auto;
}

.emotion-14 div.slick-slide>div {
  height: 100%;
}

.emotion-14 .slick-current a>div:nth-of-type(2) {
  border-left: none;
}

.emotion-15 {
  position: relative;
}

.emotion-15 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-15 .slick-slider .slick-track,
.emotion-15 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-15 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-15 .slick-list:focus {
  outline: none;
}

.emotion-15 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-15 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-15 .slick-track:before,
.emotion-15 .slick-track:after {
  display: table;
  content: "";
}

.emotion-15 .slick-track:after {
  clear: both;
}

.emotion-15 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-15 .slick-slide img {
  display: block;
}

.emotion-15 .slick-slide.slick-loading img {
  display: none;
}

.emotion-15 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-15 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-15 .slick-initialized .slick-slide,
.emotion-15 .slick-vertical .slick-slide {
  display: block;
}

.emotion-15 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-15 .slick-loading .slick-track,
.emotion-15 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-15 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-15 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-15 .slick-prev,
.emotion-15 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-15 .slick-prev:hover,
.emotion-15 .slick-next:hover,
.emotion-15 .slick-prev:focus,
.emotion-15 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-15 .slick-prev.slick-disabled,
.emotion-15 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-15 .slick-prev {
  left: -11px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-15 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-15 [dir="rtl"] .slick-prev {
  right: -11px;
  left: auto;
}

.emotion-15 .slick-next {
  right: -11px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-15 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-15 [dir="rtl"] .slick-next {
  right: auto;
  left: -11px;
}

.emotion-15 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-15 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-15 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-15 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-15 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-15 .slick-dots li button:hover,
.emotion-15 .slick-dots li button:focus {
  outline: none;
}

.emotion-15 .slick-dots li button:hover:before,
.emotion-15 .slick-dots li button:focus:before,
.emotion-15 .slick-dots li button:hover:before,
.emotion-15 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-15 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-15 div.slick-list {
  margin: 0 -15px;
}

.emotion-15 div.slick-slide {
  margin: 0 15px!important;
  width: 135px!important;
}

.emotion-16.slick-prev.slick-arrow {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 28px!important;
  height: 28px!important;
  z-index: 2;
  top: 90px;
  background-color: #1D1D1D4D;
  -webkit-transition: background-color 0.3s ease;
  transition: background-color 0.3s ease;
}

.emotion-16.slick-prev.slick-arrow.slick-disabled {
  display: none;
}

.emotion-16.slick-prev.slick-arrow>span {
  margin: auto;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-16.slick-prev.slick-arrow svg {
  margin: auto;
  height: min(1.953125vw, 25px);
  width: min(1.40625vw, 18px);
}

.emotion-16.slick-prev.slick-arrow:hover,
.emotion-16.slick-prev.slick-arrow:active {
  background-color: #1D1D1D;
}

.emotion-16.slick-prev.slick-arrow svg {
  width: 12px!important;
  height: 12px!important;
}

.emotion-17 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-17 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-18 {
  outline: none;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 100%;
}

.emotion-19 {
  overflow: hidden;
  -webkit-transition: background-color .25s ease;
  transition: background-color .25s ease;
  background-color: transparent;
  position: relative;
}

.emotion-20 {
  box-sizing: border-box;
  aspect-ratio: 135/180;
  position: relative;
  -webkit-transition: -webkit-transform .25s ease,opacity .25s ease;
  transition: transform .25s ease,opacity .25s ease;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
  opacity: 1;
  width: 100%;
  z-index: 1;
}

.emotion-21 {
  width: 100%;
  aspect-ratio: 135/180;
  object-fit: cover;
}

.emotion-22 {
  background: transparent;
  position: relative;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  z-index: 1;
  box-sizing: border-box;
  position: relative;
  width: 100%;
  min-height: 44px;
  background-color: #FFFFFF;
}

.emotion-23 {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  letter-spacing: 0.32px;
  padding-top: 8px;
}

.emotion-60.slick-next.slick-arrow {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 28px!important;
  height: 28px!important;
  z-index: 2;
  top: 90px;
  background-color: #1D1D1D4D;
  -webkit-transition: background-color 0.3s ease;
  transition: background-color 0.3s ease;
}

.emotion-60.slick-next.slick-arrow.slick-disabled {
  display: none;
}

.emotion-60.slick-next.slick-arrow>span {
  margin: auto;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-60.slick-next.slick-arrow svg {
  margin: auto;
  height: min(1.953125vw, 25px);
  width: min(1.40625vw, 18px);
}

.emotion-60.slick-next.slick-arrow:hover,
.emotion-60.slick-next.slick-arrow:active {
  background-color: #1D1D1D;
}

.emotion-60.slick-next.slick-arrow svg {
  width: 12px!important;
  height: 12px!important;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="category-banner"
    >
      <article
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-3"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--headline-6"
                >
                  Lorem ipsum dolor
                </span>
                <span
                  class="amp-cms--headline-5"
                >
                   
                </span>
                <span
                  class="amp-cms--body-3"
                >
                  sit amet consectetur adipiscing elit sed do eiusmod.
                </span>
              </p>
            </div>
          </div>
          <div
            class="emotion-4"
          >
            <a
              class="cta-label emotion-5"
              color="dark"
              href="#"
              target="_self"
            >
              CTA
            </a>
          </div>
        </div>
        <div
          class="emotion-6"
        >
          <div
            class="emotion-7"
          >
            <button
              class="emotion-8"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Toggle 1
                    </span>
                  </p>
                </div>
              </div>
            </button>
            <button
              class="emotion-10"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Toggle 2
                    </span>
                  </p>
                </div>
              </div>
            </button>
            <button
              class="emotion-10"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Toggle 3
                    </span>
                  </p>
                </div>
              </div>
            </button>
          </div>
          <div
            class="emotion-14"
          >
            <div
              class="emotion-15"
            >
              <div
                class="slick-slider slick-initialized"
                dir="ltr"
              >
                <button
                  aria-label="Previous"
                  class="slick-prev slick-arrow slick-prev slick-disabled emotion-16"
                >
                  <span
                    aria-hidden="true"
                    class="emotion-17"
                  >
                    <svg
                      viewBox="0 0 18 7.742"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                        fill="#FFFFFF"
                        transform="translate(18) rotate(90)"
                      />
                    </svg>
                  </span>
                </button>
                <div
                  class="slick-list"
                >
                  <div
                    class="slick-track"
                    style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                  >
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active slick-current"
                      data-index="0"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <a
                          class="emotion-18"
                          href="https://www.A.com"
                          target="_self"
                        >
                          <div
                            aria-live="polite"
                            class="emotion-19"
                          >
                            <div
                              aria-hidden="false"
                              class="emotion-20"
                            >
                              <img
                                alt="Jeans"
                                class="emotion-21"
                                src=""
                              />
                            </div>
                          </div>
                          <div
                            class="emotion-22"
                          >
                            <div
                              class="emotion-23"
                            >
                              &lt;p class="amp-cms--p" style="text-align:left;"&gt;&lt;span class="amp-cms--body-1"&gt;Category 1&lt;/span&gt;&lt;/p&gt;
                            </div>
                          </div>
                        </a>
                      </div>
                    </div>
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active"
                      data-index="1"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <a
                          class="emotion-18"
                          href="https://www.B.com"
                          target="_self"
                        >
                          <div
                            aria-live="polite"
                            class="emotion-19"
                          >
                            <div
                              aria-hidden="false"
                              class="emotion-20"
                            >
                              <img
                                alt="Man"
                                class="emotion-21"
                                src=""
                              />
                            </div>
                          </div>
                          <div
                            class="emotion-22"
                          >
                            <div
                              class="emotion-23"
                            >
                              &lt;p class="amp-cms--p" style="text-align:left;"&gt;&lt;span class="amp-cms--body-1"&gt;Category 2&lt;/span&gt;&lt;/p&gt;
                            </div>
                          </div>
                        </a>
                      </div>
                    </div>
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active"
                      data-index="2"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <a
                          class="emotion-18"
                          href="https://www.C.com"
                          target="_self"
                        >
                          <div
                            aria-live="polite"
                            class="emotion-19"
                          >
                            <div
                              aria-hidden="false"
                              class="emotion-20"
                            >
                              <img
                                alt="Woman"
                                class="emotion-21"
                                src=""
                              />
                            </div>
                          </div>
                          <div
                            class="emotion-22"
                          >
                            <div
                              class="emotion-23"
                            >
                              &lt;p class="amp-cms--p" style="text-align:left;"&gt;&lt;span class="amp-cms--body-1"&gt;Category 3&lt;/span&gt;&lt;/p&gt;
                            </div>
                          </div>
                        </a>
                      </div>
                    </div>
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active"
                      data-index="3"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <a
                          class="emotion-18"
                          href="https://www.gap.com"
                          target="_self"
                        >
                          <div
                            aria-live="polite"
                            class="emotion-19"
                          >
                            <div
                              aria-hidden="false"
                              class="emotion-20"
                            >
                              <img
                                alt="Another Man"
                                class="emotion-21"
                                src=""
                              />
                            </div>
                          </div>
                          <div
                            class="emotion-22"
                          >
                            <div
                              class="emotion-23"
                            >
                              &lt;p class="amp-cms--p" style="text-align:left;"&gt;&lt;span class="amp-cms--body-1"&gt;Category 4&lt;/span&gt;&lt;/p&gt;
                            </div>
                          </div>
                        </a>
                      </div>
                    </div>
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active"
                      data-index="4"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <a
                          class="emotion-18"
                          href="https://www.gap.com"
                          target="_self"
                        >
                          <div
                            aria-live="polite"
                            class="emotion-19"
                          >
                            <div
                              aria-hidden="false"
                              class="emotion-20"
                            >
                              <img
                                alt="Posing"
                                class="emotion-21"
                                src=""
                              />
                            </div>
                          </div>
                          <div
                            class="emotion-22"
                          >
                            <div
                              class="emotion-23"
                            >
                              &lt;p class="amp-cms--p" style="text-align:left;"&gt;&lt;span class="amp-cms--body-1"&gt;Category 5&lt;/span&gt;&lt;/p&gt;
                            </div>
                          </div>
                        </a>
                      </div>
                    </div>
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active"
                      data-index="5"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <a
                          class="emotion-18"
                          href="https://www.gap.com"
                          target="_self"
                        >
                          <div
                            aria-live="polite"
                            class="emotion-19"
                          >
                            <div
                              aria-hidden="false"
                              class="emotion-20"
                            >
                              <img
                                alt="People"
                                class="emotion-21"
                                src=""
                              />
                            </div>
                          </div>
                          <div
                            class="emotion-22"
                          >
                            <div
                              class="emotion-23"
                            >
                              &lt;p class="amp-cms--p" style="text-align:left;"&gt;&lt;span class="amp-cms--body-1"&gt;Category 6&lt;/span&gt;&lt;/p&gt;
                            </div>
                          </div>
                        </a>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="6"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <a
                          class="emotion-18"
                          href="https://www.gap.com"
                          target="_self"
                        >
                          <div
                            aria-live="polite"
                            class="emotion-19"
                          >
                            <div
                              aria-hidden="false"
                              class="emotion-20"
                            >
                              <img
                                alt="Yoga"
                                class="emotion-21"
                                src=""
                              />
                            </div>
                          </div>
                          <div
                            class="emotion-22"
                          >
                            <div
                              class="emotion-23"
                            >
                              &lt;p class="amp-cms--p" style="text-align:left;"&gt;&lt;span class="amp-cms--body-1"&gt;Category 7&lt;/span&gt;&lt;/p&gt;
                            </div>
                          </div>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                <button
                  aria-label="Next"
                  class="slick-next slick-arrow slick-next emotion-60"
                >
                  <span
                    aria-hidden="true"
                    class="emotion-17"
                  >
                    <svg
                      viewBox="0 0 18 7.742"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                        fill="#FFFFFF"
                        transform="translate(18) rotate(90)"
                      />
                    </svg>
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </article>
    </div>
  </div>
</div>
`;

exports[`VisualNavigationSizeToggleCarousel in large viewports - Desktop should match snapshots 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  aspect-ratio: 1280/365;
  padding: 24px 32px;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  padding-bottom: 16px;
  padding-inline: 0;
}

.emotion-3 {
  font-size: 32px;
  line-height: 38px;
  font-weight: 400;
}

.emotion-3 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-3 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-3 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-3 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-3 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-3 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-3 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.40625vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.09375vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.125vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.734375vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.59375vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.46875vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.6875vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-3 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.953125vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-4 {
  position: relative;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  max-width: 380px;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 16px;
  padding: 12px;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  background: transparent;
  border-color: #000000;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  text-transform: none;
  width: 100%;
  z-index: 2;
  position: relative;
  pointer-events: auto;
  min-height: 42px;
  letter-spacing: .32px!important;
  text-align: left;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5:hover,
.emotion-5:focus {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-5:active {
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-5>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-5 span[data-id="cta-dropdown-label"] {
  font-size: 16px!important;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  line-height: 1;
  letter-spacing: .32px!important;
}

.emotion-5 span[data-id="cta-dropdown-label"]>span {
  position: relative;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
  letter-spacing: .32px!important;
}

.emotion-6 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 24px;
}

.emotion-7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 0;
  overflow: hidden;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: 24px;
}

.emotion-8 {
  -webkit-flex: 1 0 content;
  -ms-flex: 1 0 content;
  flex: 1 0 content;
  background-color: #FFFFFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #2B2B2B;
  padding: 6px 0px!important;
  gap: 8px;
  text-transform: capitalize;
  border-bottom: 2px solid transparent;
  outline: none;
  opacity: 1;
  border: 0px;
  border-bottom: 2px solid #000;
  padding: min(0.625vw, 8px);
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
}

.emotion-8 div>div {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 8px;
}

.emotion-8 div {
  text-align: center;
  font-style: normal;
  font-weight: 400;
  line-height: 21.6px;
  letter-spacing: 0.32px;
  line-height: 21.6px!important;
  letter-spacing: 0.32px!important;
  font-size: 16px!important;
  color: #595959!important;
  color: #000!important;
}

.emotion-8 div * {
  text-align: center;
  font-style: normal;
  font-weight: 400;
  line-height: 21.6px;
  letter-spacing: 0.32px;
  line-height: 21.6px!important;
  letter-spacing: 0.32px!important;
  font-size: 16px!important;
}

.emotion-8 div * {
  color: #595959!important;
}

.emotion-8 div * {
  color: #000!important;
}

.emotion-8:hover {
  outline: none;
  opacity: 1;
  border: 0px;
  border-bottom: 2px solid #000;
}

.emotion-8:hover:active {
  color: #000!important;
  border-color: #000!important;
}

.emotion-8:hover:active * {
  color: #000!important;
}

.emotion-8:hover:active {
  color: #000!important;
  border-color: #000!important;
}

.emotion-8:hover:active * {
  color: #000!important;
}

.emotion-8:hover div:active {
  color: #000!important;
  border-color: #000!important;
}

.emotion-8:hover div:active * {
  color: #000!important;
}

.emotion-8:hover div:active {
  color: #000!important;
  border-color: #000!important;
}

.emotion-8:hover div:active * {
  color: #000!important;
}

.emotion-8:hover:active div {
  color: #000!important;
}

.emotion-9 {
  width: 100%;
}

.emotion-9 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-9 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-9 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-9 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-9 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-9 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-9 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.9375vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.5625vw, 20px));
  line-height: 1.3;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.40625vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.25vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.09375vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.9375vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.90625vw, 50px));
  line-height: 0.92;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(3.125vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(35px, min(2.734375vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.375vw, 120px));
  line-height: 0.8333333333333334;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(8.59375vw, 110px));
  line-height: 0.8636363636363636;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 0.85;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.8125vw, 100px));
  line-height: 1.05;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.25vw, 80px));
  line-height: 0.9375;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.46875vw, 70px));
  line-height: 0.9285714285714286;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(4.6875vw, 60px));
  line-height: 0.9166666666666666;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(14px, min(8.90625vw, 114px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(14px, min(6.5625vw, 84px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(14px, min(5vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(14px, min(2.65625vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.03125vw, -0.4px);
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-9 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(7.8125vw, 100px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.25vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-9 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(1.25vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-9 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(2.34375vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.953125vw, 25px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-9 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5625vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-10 {
  -webkit-flex: 1 0 content;
  -ms-flex: 1 0 content;
  flex: 1 0 content;
  background-color: #FFFFFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #2B2B2B;
  padding: 6px 0px!important;
  gap: 8px;
  text-transform: capitalize;
  border-bottom: 2px solid transparent;
  padding: min(0.625vw, 8px);
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
}

.emotion-10 div>div {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 8px;
}

.emotion-10 div {
  text-align: center;
  font-style: normal;
  font-weight: 400;
  line-height: 21.6px;
  letter-spacing: 0.32px;
  line-height: 21.6px!important;
  letter-spacing: 0.32px!important;
  font-size: 16px!important;
  color: #595959!important;
}

.emotion-10 div * {
  text-align: center;
  font-style: normal;
  font-weight: 400;
  line-height: 21.6px;
  letter-spacing: 0.32px;
  line-height: 21.6px!important;
  letter-spacing: 0.32px!important;
  font-size: 16px!important;
}

.emotion-10 div * {
  color: #595959!important;
}

.emotion-10:hover {
  outline: none;
  opacity: 1;
  border: 0px;
  border-bottom: 2px solid #000;
  color: var(--Brand-Gap-Blue, #031ba1)!important;
  border-color: var(--Brand-Gap-Blue, #031ba1)!important;
  color: var(--Brand-Gap-Blue, #031ba1)!important;
  border-color: var(--Brand-Gap-Blue, #031ba1)!important;
}

.emotion-10:hover * {
  color: var(--Brand-Gap-Blue, #031ba1)!important;
}

.emotion-10:hover:active {
  color: #000!important;
  border-color: #000!important;
}

.emotion-10:hover:active * {
  color: #000!important;
}

.emotion-10:hover * {
  color: var(--Brand-Gap-Blue, #031ba1)!important;
}

.emotion-10:hover:active {
  color: #000!important;
  border-color: #000!important;
}

.emotion-10:hover:active * {
  color: #000!important;
}

.emotion-10:hover div {
  color: var(--Brand-Gap-Blue, #031ba1)!important;
  border-color: var(--Brand-Gap-Blue, #031ba1)!important;
  color: var(--Brand-Gap-Blue, #031ba1)!important;
  border-color: var(--Brand-Gap-Blue, #031ba1)!important;
}

.emotion-10:hover div * {
  color: var(--Brand-Gap-Blue, #031ba1)!important;
}

.emotion-10:hover div:active {
  color: #000!important;
  border-color: #000!important;
}

.emotion-10:hover div:active * {
  color: #000!important;
}

.emotion-10:hover div * {
  color: var(--Brand-Gap-Blue, #031ba1)!important;
}

.emotion-10:hover div:active {
  color: #000!important;
  border-color: #000!important;
}

.emotion-10:hover div:active * {
  color: #000!important;
}

.emotion-10:hover:active div {
  color: #000!important;
}

.emotion-14 {
  overflow: visible;
}

.emotion-14 div.slick-track {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: min(0vw, 0px);
  float: left;
}

.emotion-14 div.slick-track:before {
  display: none;
}

.emotion-14 div.slick-track:after {
  display: none;
}

.emotion-14 div.slick-slide {
  height: auto;
}

.emotion-14 div.slick-slide>div {
  height: 100%;
}

.emotion-14 .slick-current a>div:nth-of-type(2) {
  border-left: none;
}

.emotion-15 {
  position: relative;
}

.emotion-15 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-15 .slick-slider .slick-track,
.emotion-15 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-15 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-15 .slick-list:focus {
  outline: none;
}

.emotion-15 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-15 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-15 .slick-track:before,
.emotion-15 .slick-track:after {
  display: table;
  content: "";
}

.emotion-15 .slick-track:after {
  clear: both;
}

.emotion-15 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-15 .slick-slide img {
  display: block;
}

.emotion-15 .slick-slide.slick-loading img {
  display: none;
}

.emotion-15 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-15 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-15 .slick-initialized .slick-slide,
.emotion-15 .slick-vertical .slick-slide {
  display: block;
}

.emotion-15 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-15 .slick-loading .slick-track,
.emotion-15 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-15 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-15 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-15 .slick-prev,
.emotion-15 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-15 .slick-prev:hover,
.emotion-15 .slick-next:hover,
.emotion-15 .slick-prev:focus,
.emotion-15 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-15 .slick-prev.slick-disabled,
.emotion-15 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-15 .slick-prev {
  left: -11px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-15 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-15 [dir="rtl"] .slick-prev {
  right: -11px;
  left: auto;
}

.emotion-15 .slick-next {
  right: -11px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-15 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-15 [dir="rtl"] .slick-next {
  right: auto;
  left: -11px;
}

.emotion-15 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-15 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-15 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-15 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-15 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-15 .slick-dots li button:hover,
.emotion-15 .slick-dots li button:focus {
  outline: none;
}

.emotion-15 .slick-dots li button:hover:before,
.emotion-15 .slick-dots li button:focus:before,
.emotion-15 .slick-dots li button:hover:before,
.emotion-15 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-15 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-15 div.slick-list {
  margin: 0 -15px;
}

.emotion-15 div.slick-slide {
  margin: 0 15px!important;
  width: 135px!important;
}

.emotion-16.slick-prev.slick-arrow {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 28px!important;
  height: 28px!important;
  z-index: 2;
  top: 90px;
  background-color: #1D1D1D4D;
  -webkit-transition: background-color 0.3s ease;
  transition: background-color 0.3s ease;
}

.emotion-16.slick-prev.slick-arrow.slick-disabled {
  display: none;
}

.emotion-16.slick-prev.slick-arrow>span {
  margin: auto;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-16.slick-prev.slick-arrow svg {
  margin: auto;
  height: min(1.953125vw, 25px);
  width: min(1.40625vw, 18px);
}

.emotion-16.slick-prev.slick-arrow:hover,
.emotion-16.slick-prev.slick-arrow:active {
  background-color: #1D1D1D;
}

.emotion-16.slick-prev.slick-arrow svg {
  width: 12px!important;
  height: 12px!important;
}

.emotion-17 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-17 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-18 {
  outline: none;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 100%;
}

.emotion-19 {
  overflow: hidden;
  -webkit-transition: background-color .25s ease;
  transition: background-color .25s ease;
  background-color: transparent;
  position: relative;
}

.emotion-20 {
  box-sizing: border-box;
  aspect-ratio: 135/180;
  position: relative;
  -webkit-transition: -webkit-transform .25s ease,opacity .25s ease;
  transition: transform .25s ease,opacity .25s ease;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
  opacity: 1;
  width: 100%;
  z-index: 1;
}

.emotion-21 {
  width: 100%;
  aspect-ratio: 135/180;
  object-fit: cover;
}

.emotion-22 {
  background: transparent;
  position: relative;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  z-index: 1;
  box-sizing: border-box;
  position: relative;
  width: 100%;
  min-height: 44px;
  background-color: #FFFFFF;
}

.emotion-23 {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  letter-spacing: 0.32px;
  padding-top: 8px;
}

.emotion-60.slick-next.slick-arrow {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 28px!important;
  height: 28px!important;
  z-index: 2;
  top: 90px;
  background-color: #1D1D1D4D;
  -webkit-transition: background-color 0.3s ease;
  transition: background-color 0.3s ease;
}

.emotion-60.slick-next.slick-arrow.slick-disabled {
  display: none;
}

.emotion-60.slick-next.slick-arrow>span {
  margin: auto;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-60.slick-next.slick-arrow svg {
  margin: auto;
  height: min(1.953125vw, 25px);
  width: min(1.40625vw, 18px);
}

.emotion-60.slick-next.slick-arrow:hover,
.emotion-60.slick-next.slick-arrow:active {
  background-color: #1D1D1D;
}

.emotion-60.slick-next.slick-arrow svg {
  width: 12px!important;
  height: 12px!important;
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="category-banner"
    >
      <article
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-3"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--headline-6"
                >
                  Lorem ipsum dolor
                </span>
                <span
                  class="amp-cms--headline-5"
                >
                   
                </span>
                <span
                  class="amp-cms--body-3"
                >
                  sit amet consectetur adipiscing elit sed do eiusmod.
                </span>
              </p>
            </div>
          </div>
          <div
            class="emotion-4"
          >
            <a
              class="cta-label emotion-5"
              color="dark"
              href="#"
              target="_self"
            >
              CTA
            </a>
          </div>
        </div>
        <div
          class="emotion-6"
        >
          <div
            class="emotion-7"
          >
            <button
              class="emotion-8"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Toggle 1
                    </span>
                  </p>
                </div>
              </div>
            </button>
            <button
              class="emotion-10"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Toggle 2
                    </span>
                  </p>
                </div>
              </div>
            </button>
            <button
              class="emotion-10"
            >
              <div
                class="emotion-9"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Toggle 3
                    </span>
                  </p>
                </div>
              </div>
            </button>
          </div>
          <div
            class="emotion-14"
          >
            <div
              class="emotion-15"
            >
              <div
                class="slick-slider slick-initialized"
                dir="ltr"
              >
                <button
                  aria-label="Previous"
                  class="slick-prev slick-arrow slick-prev slick-disabled emotion-16"
                >
                  <span
                    aria-hidden="true"
                    class="emotion-17"
                  >
                    <svg
                      viewBox="0 0 18 7.742"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                        fill="#FFFFFF"
                        transform="translate(18) rotate(90)"
                      />
                    </svg>
                  </span>
                </button>
                <div
                  class="slick-list"
                >
                  <div
                    class="slick-track"
                    style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                  >
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active slick-current"
                      data-index="0"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <a
                          class="emotion-18"
                          href="https://www.A.com"
                          target="_self"
                        >
                          <div
                            aria-live="polite"
                            class="emotion-19"
                          >
                            <div
                              aria-hidden="false"
                              class="emotion-20"
                            >
                              <img
                                alt="Jeans"
                                class="emotion-21"
                                src=""
                              />
                            </div>
                          </div>
                          <div
                            class="emotion-22"
                          >
                            <div
                              class="emotion-23"
                            >
                              &lt;p class="amp-cms--p" style="text-align:left;"&gt;&lt;span class="amp-cms--body-1"&gt;Category 1&lt;/span&gt;&lt;/p&gt;
                            </div>
                          </div>
                        </a>
                      </div>
                    </div>
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active"
                      data-index="1"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <a
                          class="emotion-18"
                          href="https://www.B.com"
                          target="_self"
                        >
                          <div
                            aria-live="polite"
                            class="emotion-19"
                          >
                            <div
                              aria-hidden="false"
                              class="emotion-20"
                            >
                              <img
                                alt="Man"
                                class="emotion-21"
                                src=""
                              />
                            </div>
                          </div>
                          <div
                            class="emotion-22"
                          >
                            <div
                              class="emotion-23"
                            >
                              &lt;p class="amp-cms--p" style="text-align:left;"&gt;&lt;span class="amp-cms--body-1"&gt;Category 2&lt;/span&gt;&lt;/p&gt;
                            </div>
                          </div>
                        </a>
                      </div>
                    </div>
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active"
                      data-index="2"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <a
                          class="emotion-18"
                          href="https://www.C.com"
                          target="_self"
                        >
                          <div
                            aria-live="polite"
                            class="emotion-19"
                          >
                            <div
                              aria-hidden="false"
                              class="emotion-20"
                            >
                              <img
                                alt="Woman"
                                class="emotion-21"
                                src=""
                              />
                            </div>
                          </div>
                          <div
                            class="emotion-22"
                          >
                            <div
                              class="emotion-23"
                            >
                              &lt;p class="amp-cms--p" style="text-align:left;"&gt;&lt;span class="amp-cms--body-1"&gt;Category 3&lt;/span&gt;&lt;/p&gt;
                            </div>
                          </div>
                        </a>
                      </div>
                    </div>
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active"
                      data-index="3"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <a
                          class="emotion-18"
                          href="https://www.gap.com"
                          target="_self"
                        >
                          <div
                            aria-live="polite"
                            class="emotion-19"
                          >
                            <div
                              aria-hidden="false"
                              class="emotion-20"
                            >
                              <img
                                alt="Another Man"
                                class="emotion-21"
                                src=""
                              />
                            </div>
                          </div>
                          <div
                            class="emotion-22"
                          >
                            <div
                              class="emotion-23"
                            >
                              &lt;p class="amp-cms--p" style="text-align:left;"&gt;&lt;span class="amp-cms--body-1"&gt;Category 4&lt;/span&gt;&lt;/p&gt;
                            </div>
                          </div>
                        </a>
                      </div>
                    </div>
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active"
                      data-index="4"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <a
                          class="emotion-18"
                          href="https://www.gap.com"
                          target="_self"
                        >
                          <div
                            aria-live="polite"
                            class="emotion-19"
                          >
                            <div
                              aria-hidden="false"
                              class="emotion-20"
                            >
                              <img
                                alt="Posing"
                                class="emotion-21"
                                src=""
                              />
                            </div>
                          </div>
                          <div
                            class="emotion-22"
                          >
                            <div
                              class="emotion-23"
                            >
                              &lt;p class="amp-cms--p" style="text-align:left;"&gt;&lt;span class="amp-cms--body-1"&gt;Category 5&lt;/span&gt;&lt;/p&gt;
                            </div>
                          </div>
                        </a>
                      </div>
                    </div>
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active"
                      data-index="5"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <a
                          class="emotion-18"
                          href="https://www.gap.com"
                          target="_self"
                        >
                          <div
                            aria-live="polite"
                            class="emotion-19"
                          >
                            <div
                              aria-hidden="false"
                              class="emotion-20"
                            >
                              <img
                                alt="People"
                                class="emotion-21"
                                src=""
                              />
                            </div>
                          </div>
                          <div
                            class="emotion-22"
                          >
                            <div
                              class="emotion-23"
                            >
                              &lt;p class="amp-cms--p" style="text-align:left;"&gt;&lt;span class="amp-cms--body-1"&gt;Category 6&lt;/span&gt;&lt;/p&gt;
                            </div>
                          </div>
                        </a>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="6"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <a
                          class="emotion-18"
                          href="https://www.gap.com"
                          target="_self"
                        >
                          <div
                            aria-live="polite"
                            class="emotion-19"
                          >
                            <div
                              aria-hidden="false"
                              class="emotion-20"
                            >
                              <img
                                alt="Yoga"
                                class="emotion-21"
                                src=""
                              />
                            </div>
                          </div>
                          <div
                            class="emotion-22"
                          >
                            <div
                              class="emotion-23"
                            >
                              &lt;p class="amp-cms--p" style="text-align:left;"&gt;&lt;span class="amp-cms--body-1"&gt;Category 7&lt;/span&gt;&lt;/p&gt;
                            </div>
                          </div>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                <button
                  aria-label="Next"
                  class="slick-next slick-arrow slick-next emotion-60"
                >
                  <span
                    aria-hidden="true"
                    class="emotion-17"
                  >
                    <svg
                      viewBox="0 0 18 7.742"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                        fill="#FFFFFF"
                        transform="translate(18) rotate(90)"
                      />
                    </svg>
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </article>
    </div>
  </div>
</div>
`;

exports[`VisualNavigationSizeToggleCarousel in small viewports - mobile should match snapshots for mobile 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  min-height: 235px;
  padding: 16px 0 16px 16px;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  padding-bottom: 12px;
  padding-inline: 0;
}

.emotion-3 {
  font-size: 24px;
  line-height: 30px;
  font-weight: 400;
}

.emotion-3 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-3 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-3 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-3 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-3 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-3 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-3 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-3 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.266666666666667vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.933333333333333vw, 26px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.4vw, 24px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 55px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 55px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(13.333333333333334vw, 50px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(12vw, 45px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(9.333333333333334vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(13px, min(17.066666666666666vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(13px, min(14.399999999999999vw, 54px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(13px, min(9.066666666666666vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(13px, min(6.4vw, 24px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-3 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-3 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-3 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 22px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.333333333333334vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-3 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(4.8vw, 18px));
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-4 {
  position: relative;
}

.emotion-5 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  display: inline-block;
  text-transform: none;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 1px solid transparent;
  border-radius: 0;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: .02em;
  min-height: auto;
  max-height: auto;
  line-height: 18px;
  padding: 0;
  width: auto;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 0;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  color: #000000;
  height: auto;
  padding-bottom: 1.8px;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 1.8px;
  text-decoration-thickness: 2px;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  text-transform: none;
  width: 100%;
  z-index: 2;
  position: relative;
  pointer-events: auto;
  min-height: 42px;
  letter-spacing: .28px!important;
  text-align: left;
}

.emotion-5:focus {
  outline: none;
}

.emotion-5:hover,
.emotion-5:focus {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-5:active {
  -webkit-text-decoration: unset;
  text-decoration: unset;
}

.emotion-5>* {
  width: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

.emotion-5 span[data-id="cta-dropdown-label"] {
  font-size: 14px!important;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  line-height: 1;
  letter-spacing: .28px!important;
}

.emotion-5 span[data-id="cta-dropdown-label"]>span {
  position: relative;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
  letter-spacing: .28px!important;
}

.emotion-6 {
  box-sizing: border-box;
}

.emotion-7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 16px;
}

.emotion-8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 0;
  overflow: hidden;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: flex-start;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  gap: 20px;
}

.emotion-9 {
  -webkit-flex: 1 0 content;
  -ms-flex: 1 0 content;
  flex: 1 0 content;
  background-color: #FFFFFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #2B2B2B;
  padding: 6px 0px!important;
  gap: 8px;
  text-transform: capitalize;
  border-bottom: 2px solid transparent;
  outline: none;
  opacity: 1;
  border: 0px;
  border-bottom: 2px solid #000;
  padding: min(2.1333333333333333vw, 8px);
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
}

.emotion-9 div>div {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 8px;
}

.emotion-9 div {
  text-align: center;
  font-style: normal;
  font-weight: 400;
  line-height: 21.6px;
  letter-spacing: 0.32px;
  line-height: 21.6px!important;
  letter-spacing: 0.28px;
  font-size: 14px!important;
  color: #595959!important;
  color: #000!important;
}

.emotion-9 div * {
  text-align: center;
  font-style: normal;
  font-weight: 400;
  line-height: 21.6px;
  letter-spacing: 0.32px;
  line-height: 21.6px!important;
  letter-spacing: 0.32px!important;
  font-size: 14px!important;
}

.emotion-9 div * {
  color: #595959!important;
}

.emotion-9 div * {
  color: #000!important;
}

.emotion-9:hover {
  outline: none;
  opacity: 1;
  border: 0px;
  border-bottom: 2px solid #000;
}

.emotion-9:hover:active {
  color: #000!important;
  border-color: #000!important;
}

.emotion-9:hover:active * {
  color: #000!important;
}

.emotion-9:hover:active {
  color: #000!important;
  border-color: #000!important;
}

.emotion-9:hover:active * {
  color: #000!important;
}

.emotion-9:hover div:active {
  color: #000!important;
  border-color: #000!important;
}

.emotion-9:hover div:active * {
  color: #000!important;
}

.emotion-9:hover div:active {
  color: #000!important;
  border-color: #000!important;
}

.emotion-9:hover div:active * {
  color: #000!important;
}

.emotion-9:hover:active div {
  color: #000!important;
}

.emotion-10 {
  width: 100%;
}

.emotion-10 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-10 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-10 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-10 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-10 .amp-cms--legal-copy {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-10 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-10 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-10 .amp-cms--f-0 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--f-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--f-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--fn-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.8vw, 18px));
  line-height: 1.2777777777777777;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(4.266666666666667vw, 16px));
  line-height: 1.25;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1.4;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--eyebrow-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8vw, 30px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--eyebrow-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.933333333333333vw, 26px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--eyebrow-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(6.4vw, 24px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 55px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(14.666666666666666vw, 55px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-4 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(13.333333333333334vw, 50px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-5 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(12vw, 45px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-6 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headline-7 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(9.333333333333334vw, 35px));
  line-height: 1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--headlineAlt-1 {
  color: #2B2B2B;
  font-size: max(13px, min(17.066666666666666vw, 64px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-2 {
  color: #2B2B2B;
  font-size: max(13px, min(14.399999999999999vw, 54px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-3 {
  color: #2B2B2B;
  font-size: max(13px, min(9.066666666666666vw, 34px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-4 {
  color: #2B2B2B;
  font-size: max(13px, min(6.4vw, 24px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -0.3px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-5 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-6 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-7 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--promo-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-10 .amp-cms--promo-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-10 .amp-cms--promoAlt-1 {
  color: #2B2B2B;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-10 .amp-cms--subhead-1 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.866666666666666vw, 22px));
  line-height: 1.0909090909090908;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--subhead-2 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(5.333333333333334vw, 20px));
  line-height: 1.1;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--subhead-3 {
  color: #2B2B2B;
  font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(4.8vw, 18px));
  line-height: 1.1111111111111112;
  letter-spacing: 0;
}

.emotion-11 {
  -webkit-flex: 1 0 content;
  -ms-flex: 1 0 content;
  flex: 1 0 content;
  background-color: #FFFFFF;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #2B2B2B;
  padding: 6px 0px!important;
  gap: 8px;
  text-transform: capitalize;
  border-bottom: 2px solid transparent;
  padding: min(2.1333333333333333vw, 8px);
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
}

.emotion-11 div>div {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  gap: 8px;
}

.emotion-11 div {
  text-align: center;
  font-style: normal;
  font-weight: 400;
  line-height: 21.6px;
  letter-spacing: 0.32px;
  line-height: 21.6px!important;
  letter-spacing: 0.28px;
  font-size: 14px!important;
  color: #595959!important;
}

.emotion-11 div * {
  text-align: center;
  font-style: normal;
  font-weight: 400;
  line-height: 21.6px;
  letter-spacing: 0.32px;
  line-height: 21.6px!important;
  letter-spacing: 0.32px!important;
  font-size: 14px!important;
}

.emotion-11 div * {
  color: #595959!important;
}

.emotion-11:hover {
  outline: none;
  opacity: 1;
  border: 0px;
  border-bottom: 2px solid #000;
  color: var(--Brand-Gap-Blue, #031ba1)!important;
  border-color: var(--Brand-Gap-Blue, #031ba1)!important;
  color: var(--Brand-Gap-Blue, #031ba1)!important;
  border-color: var(--Brand-Gap-Blue, #031ba1)!important;
}

.emotion-11:hover * {
  color: var(--Brand-Gap-Blue, #031ba1)!important;
}

.emotion-11:hover:active {
  color: #000!important;
  border-color: #000!important;
}

.emotion-11:hover:active * {
  color: #000!important;
}

.emotion-11:hover * {
  color: var(--Brand-Gap-Blue, #031ba1)!important;
}

.emotion-11:hover:active {
  color: #000!important;
  border-color: #000!important;
}

.emotion-11:hover:active * {
  color: #000!important;
}

.emotion-11:hover div {
  color: var(--Brand-Gap-Blue, #031ba1)!important;
  border-color: var(--Brand-Gap-Blue, #031ba1)!important;
  color: var(--Brand-Gap-Blue, #031ba1)!important;
  border-color: var(--Brand-Gap-Blue, #031ba1)!important;
}

.emotion-11:hover div * {
  color: var(--Brand-Gap-Blue, #031ba1)!important;
}

.emotion-11:hover div:active {
  color: #000!important;
  border-color: #000!important;
}

.emotion-11:hover div:active * {
  color: #000!important;
}

.emotion-11:hover div * {
  color: var(--Brand-Gap-Blue, #031ba1)!important;
}

.emotion-11:hover div:active {
  color: #000!important;
  border-color: #000!important;
}

.emotion-11:hover div:active * {
  color: #000!important;
}

.emotion-11:hover:active div {
  color: #000!important;
}

.emotion-15 {
  overflow: hidden;
}

.emotion-15 div.slick-track {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: min(0vw, 0px);
}

.emotion-15 div.slick-track:before {
  display: none;
}

.emotion-15 div.slick-track:after {
  display: none;
}

.emotion-15 div.slick-slide {
  height: auto;
}

.emotion-15 div.slick-slide>div {
  height: 100%;
}

.emotion-15 .slick-current a>div:nth-of-type(2) {
  border-left: none;
}

.emotion-16 {
  position: relative;
}

.emotion-16 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-16 .slick-slider .slick-track,
.emotion-16 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-16 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-16 .slick-list:focus {
  outline: none;
}

.emotion-16 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-16 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-16 .slick-track:before,
.emotion-16 .slick-track:after {
  display: table;
  content: "";
}

.emotion-16 .slick-track:after {
  clear: both;
}

.emotion-16 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-16 .slick-slide img {
  display: block;
}

.emotion-16 .slick-slide.slick-loading img {
  display: none;
}

.emotion-16 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-16 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-16 .slick-initialized .slick-slide,
.emotion-16 .slick-vertical .slick-slide {
  display: block;
}

.emotion-16 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-16 .slick-loading .slick-track,
.emotion-16 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-16 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-16 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-16 .slick-prev,
.emotion-16 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-16 .slick-prev:hover,
.emotion-16 .slick-next:hover,
.emotion-16 .slick-prev:focus,
.emotion-16 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-16 .slick-prev.slick-disabled,
.emotion-16 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-16 .slick-prev {
  left: -0px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-16 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-16 [dir="rtl"] .slick-prev {
  right: -0px;
  left: auto;
}

.emotion-16 .slick-next {
  right: -0px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-16 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-16 [dir="rtl"] .slick-next {
  right: auto;
  left: -0px;
}

.emotion-16 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-16 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-16 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-16 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-16 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-16 .slick-dots li button:hover,
.emotion-16 .slick-dots li button:focus {
  outline: none;
}

.emotion-16 .slick-dots li button:hover:before,
.emotion-16 .slick-dots li button:focus:before,
.emotion-16 .slick-dots li button:hover:before,
.emotion-16 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-16 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-16 div.slick-list {
  margin: 0 -4px;
}

.emotion-16 div.slick-slide {
  margin: 0 4px!important;
  width: 140px!important;
}

.emotion-17.slick-prev.slick-arrow {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: min(11.733333333333333vw, 44px);
  height: min(11.733333333333333vw, 44px);
  z-index: 2;
  top: 98.5px;
}

.emotion-17.slick-prev.slick-arrow.slick-disabled {
  display: none;
}

.emotion-17.slick-prev.slick-arrow>span {
  margin: auto;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-17.slick-prev.slick-arrow svg {
  margin: auto;
  height: min(6.666666666666667vw, 25px);
  width: min(4.8vw, 18px);
}

.emotion-18 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-18 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-19 {
  outline: none;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 100%;
}

.emotion-20 {
  overflow: hidden;
  -webkit-transition: background-color .25s ease;
  transition: background-color .25s ease;
  background-color: transparent;
  min-width: 140px;
  position: relative;
}

.emotion-21 {
  box-sizing: border-box;
  aspect-ratio: 140/197;
  position: relative;
  -webkit-transition: -webkit-transform .25s ease,opacity .25s ease;
  transition: transform .25s ease,opacity .25s ease;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
  opacity: 1;
  width: 100%;
  z-index: 1;
}

.emotion-22 {
  width: 100%;
  aspect-ratio: 140/197;
  object-fit: cover;
}

.emotion-23 {
  background: transparent;
  position: relative;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  z-index: 1;
  box-sizing: border-box;
  position: relative;
  width: 100%;
  min-height: 44px;
  background-color: #FFFFFF;
}

.emotion-24 {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  letter-spacing: 0.28px;
  padding-top: 8px;
}

.emotion-61.slick-next.slick-arrow {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: min(11.733333333333333vw, 44px);
  height: min(11.733333333333333vw, 44px);
  z-index: 2;
  top: 98.5px;
}

.emotion-61.slick-next.slick-arrow.slick-disabled {
  display: none;
}

.emotion-61.slick-next.slick-arrow>span {
  margin: auto;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-61.slick-next.slick-arrow svg {
  margin: auto;
  height: min(6.666666666666667vw, 25px);
  width: min(4.8vw, 18px);
}

<div>
  <div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="category-banner"
    >
      <article
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div
            class="emotion-3"
          >
            <div>
              <p
                class="amp-cms--p"
                style="text-align:left;"
              >
                <span
                  class="amp-cms--headline-6"
                >
                  Lorem ipsum dolor
                </span>
                <span
                  class="amp-cms--headline-5"
                >
                   
                </span>
                <span
                  class="amp-cms--body-3"
                >
                  sit amet consectetur adipiscing elit sed do eiusmod.
                </span>
              </p>
            </div>
          </div>
          <div
            class="emotion-4"
          >
            <a
              class="cta-label emotion-5"
              color="dark"
              href="#"
              target="_self"
            >
              <span
                class="emotion-6"
              >
                CTA
              </span>
            </a>
          </div>
        </div>
        <div
          class="emotion-7"
        >
          <div
            class="emotion-8"
          >
            <button
              class="emotion-9"
            >
              <div
                class="emotion-10"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Toggle 1
                    </span>
                  </p>
                </div>
              </div>
            </button>
            <button
              class="emotion-11"
            >
              <div
                class="emotion-10"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Toggle 2
                    </span>
                  </p>
                </div>
              </div>
            </button>
            <button
              class="emotion-11"
            >
              <div
                class="emotion-10"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Toggle 3
                    </span>
                  </p>
                </div>
              </div>
            </button>
          </div>
          <div
            class="emotion-15"
          >
            <div
              class="emotion-16"
            >
              <div
                class="slick-slider slick-initialized"
                dir="ltr"
              >
                <button
                  aria-label="Previous"
                  class="slick-prev slick-arrow slick-prev slick-disabled emotion-17"
                >
                  <span
                    aria-hidden="true"
                    class="emotion-18"
                  >
                    <svg
                      viewBox="0 0 18 7.742"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                        fill="#FFFFFF"
                        transform="translate(18) rotate(90)"
                      />
                    </svg>
                  </span>
                </button>
                <div
                  class="slick-list"
                >
                  <div
                    class="slick-track"
                    style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                  >
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active slick-current"
                      data-index="0"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <a
                          class="emotion-19"
                          href="https://www.A.com"
                          target="_self"
                        >
                          <div
                            aria-live="polite"
                            class="emotion-20"
                          >
                            <div
                              aria-hidden="false"
                              class="emotion-21"
                            >
                              <img
                                alt="Jeans"
                                class="emotion-22"
                                src=""
                              />
                            </div>
                          </div>
                          <div
                            class="emotion-23"
                          >
                            <div
                              class="emotion-24"
                            >
                              &lt;p class="amp-cms--p" style="text-align:left;"&gt;&lt;span class="amp-cms--body-1"&gt;Category 1&lt;/span&gt;&lt;/p&gt;
                            </div>
                          </div>
                        </a>
                      </div>
                    </div>
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active"
                      data-index="1"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <a
                          class="emotion-19"
                          href="https://www.B.com"
                          target="_self"
                        >
                          <div
                            aria-live="polite"
                            class="emotion-20"
                          >
                            <div
                              aria-hidden="false"
                              class="emotion-21"
                            >
                              <img
                                alt="Man"
                                class="emotion-22"
                                src=""
                              />
                            </div>
                          </div>
                          <div
                            class="emotion-23"
                          >
                            <div
                              class="emotion-24"
                            >
                              &lt;p class="amp-cms--p" style="text-align:left;"&gt;&lt;span class="amp-cms--body-1"&gt;Category 2&lt;/span&gt;&lt;/p&gt;
                            </div>
                          </div>
                        </a>
                      </div>
                    </div>
                    <div
                      aria-hidden="false"
                      class="slick-slide slick-active"
                      data-index="2"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <a
                          class="emotion-19"
                          href="https://www.C.com"
                          target="_self"
                        >
                          <div
                            aria-live="polite"
                            class="emotion-20"
                          >
                            <div
                              aria-hidden="false"
                              class="emotion-21"
                            >
                              <img
                                alt="Woman"
                                class="emotion-22"
                                src=""
                              />
                            </div>
                          </div>
                          <div
                            class="emotion-23"
                          >
                            <div
                              class="emotion-24"
                            >
                              &lt;p class="amp-cms--p" style="text-align:left;"&gt;&lt;span class="amp-cms--body-1"&gt;Category 3&lt;/span&gt;&lt;/p&gt;
                            </div>
                          </div>
                        </a>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="3"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <a
                          class="emotion-19"
                          href="https://www.gap.com"
                          target="_self"
                        >
                          <div
                            aria-live="polite"
                            class="emotion-20"
                          >
                            <div
                              aria-hidden="false"
                              class="emotion-21"
                            >
                              <img
                                alt="Another Man"
                                class="emotion-22"
                                src=""
                              />
                            </div>
                          </div>
                          <div
                            class="emotion-23"
                          >
                            <div
                              class="emotion-24"
                            >
                              &lt;p class="amp-cms--p" style="text-align:left;"&gt;&lt;span class="amp-cms--body-1"&gt;Category 4&lt;/span&gt;&lt;/p&gt;
                            </div>
                          </div>
                        </a>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="4"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <a
                          class="emotion-19"
                          href="https://www.gap.com"
                          target="_self"
                        >
                          <div
                            aria-live="polite"
                            class="emotion-20"
                          >
                            <div
                              aria-hidden="false"
                              class="emotion-21"
                            >
                              <img
                                alt="Posing"
                                class="emotion-22"
                                src=""
                              />
                            </div>
                          </div>
                          <div
                            class="emotion-23"
                          >
                            <div
                              class="emotion-24"
                            >
                              &lt;p class="amp-cms--p" style="text-align:left;"&gt;&lt;span class="amp-cms--body-1"&gt;Category 5&lt;/span&gt;&lt;/p&gt;
                            </div>
                          </div>
                        </a>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="5"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <a
                          class="emotion-19"
                          href="https://www.gap.com"
                          target="_self"
                        >
                          <div
                            aria-live="polite"
                            class="emotion-20"
                          >
                            <div
                              aria-hidden="false"
                              class="emotion-21"
                            >
                              <img
                                alt="People"
                                class="emotion-22"
                                src=""
                              />
                            </div>
                          </div>
                          <div
                            class="emotion-23"
                          >
                            <div
                              class="emotion-24"
                            >
                              &lt;p class="amp-cms--p" style="text-align:left;"&gt;&lt;span class="amp-cms--body-1"&gt;Category 6&lt;/span&gt;&lt;/p&gt;
                            </div>
                          </div>
                        </a>
                      </div>
                    </div>
                    <div
                      aria-hidden="true"
                      class="slick-slide"
                      data-index="6"
                      style="outline: none; width: 0px;"
                      tabindex="-1"
                    >
                      <div>
                        <a
                          class="emotion-19"
                          href="https://www.gap.com"
                          target="_self"
                        >
                          <div
                            aria-live="polite"
                            class="emotion-20"
                          >
                            <div
                              aria-hidden="false"
                              class="emotion-21"
                            >
                              <img
                                alt="Yoga"
                                class="emotion-22"
                                src=""
                              />
                            </div>
                          </div>
                          <div
                            class="emotion-23"
                          >
                            <div
                              class="emotion-24"
                            >
                              &lt;p class="amp-cms--p" style="text-align:left;"&gt;&lt;span class="amp-cms--body-1"&gt;Category 7&lt;/span&gt;&lt;/p&gt;
                            </div>
                          </div>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                <button
                  aria-label="Next"
                  class="slick-next slick-arrow slick-next emotion-61"
                >
                  <span
                    aria-hidden="true"
                    class="emotion-18"
                  >
                    <svg
                      viewBox="0 0 18 7.742"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M0,.839,6.44,9,0,17.162.818,18,7.743,9.225,7.522,9l.22-.225L.818,0Z"
                        fill="#FFFFFF"
                        transform="translate(18) rotate(90)"
                      />
                    </svg>
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </article>
    </div>
  </div>
</div>
`;
