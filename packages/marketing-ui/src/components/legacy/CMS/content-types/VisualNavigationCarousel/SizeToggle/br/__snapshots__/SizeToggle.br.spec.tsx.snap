// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`VisualNavigationSizeToggleCarousel in extra large viewports - extra large desktops should match snapshots for desktop with introcard hideondesktop 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  aspect-ratio: 1280/365;
}

.emotion-2 {
  padding-bottom: 0;
  padding-inline: 0;
}

.emotion-2 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-2 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-2 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-2 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-2 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,<PERSON><PERSON>,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-2 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-2 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(0.9375vw, 18px));
  line-height: 1.4444444444444444;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.8333333333333334vw, 16px));
  line-height: 1.375;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.7291666666666666vw, 14px));
  line-height: 1.4285714285714286;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 16px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(48px, min(5vw, 96px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(40px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(28px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(1.6666666666666667vw, 32px));
  line-height: 1.125;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(1.25vw, 24px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1.4;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(60px, min(6.25vw, 120px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(0.5208333333333333vw, 10px));
  line-height: 1.6;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-2 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.9375vw, 18px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(1.25vw, 24px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 0;
  overflow: hidden;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 20px;
  margin: 20px 0;
}

.emotion-5 {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  border: none;
  background-color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  -webkit-transition: all 250ms ease-in-out 0s;
  transition: all 250ms ease-in-out 0s;
  border-top: 1.5px solid transparent;
  letter-spacing: 0px;
  min-height: 44px;
  width: auto;
  height: auto;
  -webkit-text-decoration: underline 1px;
  text-decoration: underline 1px;
  text-underline-offset: 5px;
  font-weight: bold;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 4px;
}

.emotion-5:not(:first-of-type) {
  margin-top: -1px;
}

.emotion-5:focus {
  font-weight: bold;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-5:hover {
  outline: none;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-6 {
  width: 100%;
}

.emotion-6 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-6 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-6 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-6 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-6 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-6 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-6 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(0.9375vw, 18px));
  line-height: 1.4444444444444444;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.8333333333333334vw, 16px));
  line-height: 1.375;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.7291666666666666vw, 14px));
  line-height: 1.4285714285714286;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-6 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 16px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-6 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-6 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(48px, min(5vw, 96px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(40px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(28px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(1.6666666666666667vw, 32px));
  line-height: 1.125;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(1.25vw, 24px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1.4;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(60px, min(6.25vw, 120px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(0.5208333333333333vw, 10px));
  line-height: 1.6;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-6 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.9375vw, 18px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(1.25vw, 24px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-7 {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  border: none;
  background-color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  -webkit-transition: all 250ms ease-in-out 0s;
  transition: all 250ms ease-in-out 0s;
  border-top: 1.5px solid transparent;
  letter-spacing: 0px;
  min-height: 44px;
  width: auto;
  height: auto;
  -webkit-text-decoration: underline 1px;
  text-decoration: underline 1px;
  text-underline-offset: 5px;
  text-underline-offset: 4px;
}

.emotion-7:not(:first-of-type) {
  margin-top: -1px;
}

.emotion-7:focus {
  font-weight: bold;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-7:hover {
  outline: none;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-11 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
}

.emotion-12 {
  padding: 0 12px 0 0;
  max-width: 333.3333333333333px;
}

.emotion-13 {
  overflow: hidden;
}

.emotion-13 div.slick-track {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 0;
}

.emotion-13 div.slick-track:before {
  display: none;
}

.emotion-13 div.slick-track:after {
  display: none;
}

.emotion-13 div.slick-slide {
  height: auto;
}

.emotion-13 div.slick-slide>div {
  height: 100%;
}

.emotion-14 {
  position: relative;
}

.emotion-14 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-14 .slick-slider .slick-track,
.emotion-14 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-14 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-14 .slick-list:focus {
  outline: none;
}

.emotion-14 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-14 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-14 .slick-track:before,
.emotion-14 .slick-track:after {
  display: table;
  content: "";
}

.emotion-14 .slick-track:after {
  clear: both;
}

.emotion-14 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-14 .slick-slide img {
  display: block;
}

.emotion-14 .slick-slide.slick-loading img {
  display: none;
}

.emotion-14 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-14 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-14 .slick-initialized .slick-slide,
.emotion-14 .slick-vertical .slick-slide {
  display: block;
}

.emotion-14 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-14 .slick-loading .slick-track,
.emotion-14 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-14 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-14 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-14 .slick-prev,
.emotion-14 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-14 .slick-prev:hover,
.emotion-14 .slick-next:hover,
.emotion-14 .slick-prev:focus,
.emotion-14 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-14 .slick-prev.slick-disabled,
.emotion-14 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-14 .slick-prev {
  left: -0px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-14 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-14 [dir="rtl"] .slick-prev {
  right: -0px;
  left: auto;
}

.emotion-14 .slick-next {
  right: -0px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-14 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-14 [dir="rtl"] .slick-next {
  right: auto;
  left: -0px;
}

.emotion-14 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-14 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-14 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-14 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-14 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-14 .slick-dots li button:hover,
.emotion-14 .slick-dots li button:focus {
  outline: none;
}

.emotion-14 .slick-dots li button:hover:before,
.emotion-14 .slick-dots li button:focus:before,
.emotion-14 .slick-dots li button:hover:before,
.emotion-14 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-14 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-14 .slick-list {
  margin: 0 -6px;
}

.emotion-14 .slick-track {
  margin-left: 0!important;
  margin-right: 0!important;
}

.emotion-14 .category-card {
  margin: 0 6px!important;
  overflow: hidden;
}

.emotion-15.slick-prev.slick-arrow {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: min(3.4375000000000004vw, 44px);
  height: min(3.4375000000000004vw, 44px);
  z-index: 2;
  top: 125px;
  background: #000000;
}

.emotion-15.slick-prev.slick-arrow.slick-disabled {
  display: none;
}

.emotion-15.slick-prev.slick-arrow>span {
  margin: auto;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-15.slick-prev.slick-arrow svg {
  margin: auto;
  height: min(1.953125vw, 25px);
  width: min(1.40625vw, 18px);
}

.emotion-15.slick-prev.slick-arrow:hover {
  background: #000000;
}

.emotion-16 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-16 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-17 {
  outline: none;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-17>div:nth-of-type(2) {
  border-top: 1px solid #000000;
}

.emotion-17:not(:first-of-type)>div:nth-of-type(2) {
  border-left: 1px solid #000000;
}

.emotion-18 {
  overflow: hidden;
  -webkit-transition: background-color .25s ease;
  transition: background-color .25s ease;
  background-color: transparent;
  position: relative;
  width: 333.3333333333333px;
}

.emotion-19 {
  box-sizing: border-box;
  aspect-ratio: 333.3333333333333/250;
  position: relative;
  -webkit-transition: -webkit-transform .25s ease,opacity .25s ease;
  transition: transform .25s ease,opacity .25s ease;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
  opacity: 1;
  width: 100%;
  z-index: 1;
}

.emotion-20 {
  width: 100%;
  aspect-ratio: 333.3333333333333/250;
  object-fit: cover;
}

.emotion-21 {
  background: transparent;
  position: relative;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  z-index: 1;
  box-sizing: border-box;
  position: relative;
  width: 100%;
  min-height: 44px;
  background-color: #FFFFFF;
}

.emotion-22 {
  opacity: 1;
  box-sizing: border-box;
  padding: 11px 15px 9px;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-22 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-22 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-22 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-22 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-22 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-22 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-22 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-22 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-22 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-22 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-22 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-22 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-22 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(0.9375vw, 18px));
  line-height: 1.4444444444444444;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-22 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.8333333333333334vw, 16px));
  line-height: 1.375;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-22 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.7291666666666666vw, 14px));
  line-height: 1.4285714285714286;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-22 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-22 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-22 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 16px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-22 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-22 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(48px, min(5vw, 96px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-22 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(40px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-22 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-22 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(28px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-22 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(1.6666666666666667vw, 32px));
  line-height: 1.125;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-22 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(1.25vw, 24px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-22 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1.4;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-22 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-22 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-22 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-22 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(60px, min(6.25vw, 120px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-22 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-22 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-22 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-22 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(0.5208333333333333vw, 10px));
  line-height: 1.6;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-22 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-22 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-22 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.9375vw, 18px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-22 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(1.25vw, 24px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-22 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-22>* {
  width: 100%;
}

.emotion-59.slick-next.slick-arrow {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: min(3.4375000000000004vw, 44px);
  height: min(3.4375000000000004vw, 44px);
  z-index: 2;
  top: 125px;
  background: #000000;
}

.emotion-59.slick-next.slick-arrow.slick-disabled {
  display: none;
}

.emotion-59.slick-next.slick-arrow>span {
  margin: auto;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-59.slick-next.slick-arrow svg {
  margin: auto;
  height: min(1.953125vw, 25px);
  width: min(1.40625vw, 18px);
}

.emotion-59.slick-next.slick-arrow:hover {
  background: #000000;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="category-banner"
    >
      <article
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div>
            <p
              class="amp-cms--p"
              style="text-align:left;"
            >
              <span
                class="amp-cms--headline-6"
              >
                Lorem ipsum dolor
              </span>
              <span
                class="amp-cms--headline-5"
              >
                 
              </span>
              <span
                class="amp-cms--body-3"
              >
                sit amet consectetur adipiscing elit sed do eiusmod.
              </span>
            </p>
          </div>
        </div>
        <div
          class="emotion-3"
        >
          <div
            class="emotion-4"
          >
            <button
              class="emotion-5"
            >
              <div
                class="emotion-6"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Toggle 1
                    </span>
                  </p>
                </div>
              </div>
            </button>
            <button
              class="emotion-7"
            >
              <div
                class="emotion-6"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Toggle 2
                    </span>
                  </p>
                </div>
              </div>
            </button>
            <button
              class="emotion-7"
            >
              <div
                class="emotion-6"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Toggle 3
                    </span>
                  </p>
                </div>
              </div>
            </button>
          </div>
          <div
            class="emotion-11"
          >
            <div
              class="emotion-12"
            />
            <div
              class="emotion-13"
            >
              <div
                class="emotion-14"
              >
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <button
                    aria-label="Previous"
                    class="slick-prev slick-arrow slick-prev slick-disabled emotion-15"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-16"
                    >
                      <svg
                        fill="none"
                        viewBox="0 0 28 13"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          clip-rule="evenodd"
                          d="M14.2211 12.2784L27.5 1.2161 26.8781.5 13.9918 11.2352 1.12192.513669.5 1.22977 13.8726 12.3701l.1001.1152.0191-.0159.0368.0306.1925-.2216z"
                          fill="#FFFFFF"
                          fill-rule="evenodd"
                        />
                      </svg>
                    </span>
                  </button>
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-17"
                            href="https://www.A.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-18"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-19"
                              >
                                <img
                                  alt="Jeans"
                                  class="emotion-20"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-21"
                            >
                              <div
                                class="emotion-22"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 1
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-17"
                            href="https://www.B.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-18"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-19"
                              >
                                <img
                                  alt="Man"
                                  class="emotion-20"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-21"
                            >
                              <div
                                class="emotion-22"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 2
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-17"
                            href="https://www.C.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-18"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-19"
                              >
                                <img
                                  alt="Woman"
                                  class="emotion-20"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-21"
                            >
                              <div
                                class="emotion-22"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 3
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="3"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-17"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-18"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-19"
                              >
                                <img
                                  alt="Another Man"
                                  class="emotion-20"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-21"
                            >
                              <div
                                class="emotion-22"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 4
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="4"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-17"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-18"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-19"
                              >
                                <img
                                  alt="Posing"
                                  class="emotion-20"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-21"
                            >
                              <div
                                class="emotion-22"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 5
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="5"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-17"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-18"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-19"
                              >
                                <img
                                  alt="People"
                                  class="emotion-20"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-21"
                            >
                              <div
                                class="emotion-22"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 6
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="6"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-17"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-18"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-19"
                              >
                                <img
                                  alt="Yoga"
                                  class="emotion-20"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-21"
                            >
                              <div
                                class="emotion-22"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 7
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    aria-label="Next"
                    class="slick-next slick-arrow slick-next emotion-59"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-16"
                    >
                      <svg
                        fill="none"
                        viewBox="0 0 28 13"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          clip-rule="evenodd"
                          d="M14.2211 12.2784L27.5 1.2161 26.8781.5 13.9918 11.2352 1.12192.513669.5 1.22977 13.8726 12.3701l.1001.1152.0191-.0159.0368.0306.1925-.2216z"
                          fill="#FFFFFF"
                          fill-rule="evenodd"
                        />
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </article>
    </div>
  </div>
</div>
`;

exports[`VisualNavigationSizeToggleCarousel in extra large viewports - extra large desktops should match snapshots for desktop with introcard hideonmobile 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  aspect-ratio: 1280/365;
}

.emotion-2 {
  padding-bottom: 0;
  padding-inline: 0;
}

.emotion-2 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-2 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-2 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-2 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-2 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-2 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-2 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(0.9375vw, 18px));
  line-height: 1.4444444444444444;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.8333333333333334vw, 16px));
  line-height: 1.375;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.7291666666666666vw, 14px));
  line-height: 1.4285714285714286;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 16px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(48px, min(5vw, 96px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(40px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(28px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(1.6666666666666667vw, 32px));
  line-height: 1.125;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(1.25vw, 24px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1.4;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(60px, min(6.25vw, 120px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(0.5208333333333333vw, 10px));
  line-height: 1.6;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-2 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.9375vw, 18px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(1.25vw, 24px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 0;
  overflow: hidden;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 20px;
  margin: 20px 0;
}

.emotion-5 {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  border: none;
  background-color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  -webkit-transition: all 250ms ease-in-out 0s;
  transition: all 250ms ease-in-out 0s;
  border-top: 1.5px solid transparent;
  letter-spacing: 0px;
  min-height: 44px;
  width: auto;
  height: auto;
  -webkit-text-decoration: underline 1px;
  text-decoration: underline 1px;
  text-underline-offset: 5px;
  font-weight: bold;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 4px;
}

.emotion-5:not(:first-of-type) {
  margin-top: -1px;
}

.emotion-5:focus {
  font-weight: bold;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-5:hover {
  outline: none;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-6 {
  width: 100%;
}

.emotion-6 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-6 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-6 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-6 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-6 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-6 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-6 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(0.9375vw, 18px));
  line-height: 1.4444444444444444;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.8333333333333334vw, 16px));
  line-height: 1.375;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.7291666666666666vw, 14px));
  line-height: 1.4285714285714286;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-6 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 16px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-6 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-6 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(48px, min(5vw, 96px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(40px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(28px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(1.6666666666666667vw, 32px));
  line-height: 1.125;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(1.25vw, 24px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1.4;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(60px, min(6.25vw, 120px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(0.5208333333333333vw, 10px));
  line-height: 1.6;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-6 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.9375vw, 18px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(1.25vw, 24px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-7 {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  border: none;
  background-color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  -webkit-transition: all 250ms ease-in-out 0s;
  transition: all 250ms ease-in-out 0s;
  border-top: 1.5px solid transparent;
  letter-spacing: 0px;
  min-height: 44px;
  width: auto;
  height: auto;
  -webkit-text-decoration: underline 1px;
  text-decoration: underline 1px;
  text-underline-offset: 5px;
  text-underline-offset: 4px;
}

.emotion-7:not(:first-of-type) {
  margin-top: -1px;
}

.emotion-7:focus {
  font-weight: bold;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-7:hover {
  outline: none;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-11 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
}

.emotion-12 {
  padding: 0 12px 0 0;
  max-width: 248px;
}

.emotion-13 {
  background: transparent;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  min-width: auto;
  -webkit-background-position: center;
  background-position: center;
  -webkit-background-size: cover;
  background-size: cover;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  height: 100%;
  min-width: 248px;
}

.emotion-14 {
  height: 100%;
  width: 100%;
  border: 0;
  padding: 0;
  margin: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-15 {
  padding: 0 8px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 20px;
}

.emotion-16 {
  -webkit-background-size: cover;
  background-size: cover;
  width: 24px;
}

.emotion-17 {
  width: 100%;
  object-fit: cover;
}

.emotion-18 {
  margin-block: 0;
}

.emotion-18 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-18 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-18 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-18 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-18 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-18 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-18 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-18 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(0.9375vw, 18px));
  line-height: 1.4444444444444444;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.8333333333333334vw, 16px));
  line-height: 1.375;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.7291666666666666vw, 14px));
  line-height: 1.4285714285714286;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-18 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 16px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-18 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-18 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(48px, min(5vw, 96px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(40px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(28px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(1.6666666666666667vw, 32px));
  line-height: 1.125;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(1.25vw, 24px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1.4;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(60px, min(6.25vw, 120px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-18 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(0.5208333333333333vw, 10px));
  line-height: 1.6;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-18 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-18 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.9375vw, 18px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(1.25vw, 24px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-19 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: left;
  padding: 0.5em 0.8em;
  font-size: 1.5rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 2px solid currentColor;
  color: #000000;
  padding-left: 0;
  padding-right: 0;
  height: auto;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  white-space: initial;
  padding: 0;
  margin: 0;
  display: inline;
  line-height: 1.5em;
  text-align: center;
  text-underline-offset: 4px;
  z-index: 10;
  font-weight: 400;
}

.emotion-19:focus {
  outline: none;
}

.emotion-19:hover,
.emotion-19:focus {
  color: #FFFFFF;
  border-bottom-color: #000000;
  background-color: #000000;
}

.emotion-20 {
  box-sizing: border-box;
}

.emotion-21 {
  overflow: hidden;
}

.emotion-21 div.slick-track {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 0;
}

.emotion-21 div.slick-track:before {
  display: none;
}

.emotion-21 div.slick-track:after {
  display: none;
}

.emotion-21 div.slick-slide {
  height: auto;
}

.emotion-21 div.slick-slide>div {
  height: 100%;
}

.emotion-22 {
  position: relative;
}

.emotion-22 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-22 .slick-slider .slick-track,
.emotion-22 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-22 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-22 .slick-list:focus {
  outline: none;
}

.emotion-22 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-22 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-22 .slick-track:before,
.emotion-22 .slick-track:after {
  display: table;
  content: "";
}

.emotion-22 .slick-track:after {
  clear: both;
}

.emotion-22 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-22 .slick-slide img {
  display: block;
}

.emotion-22 .slick-slide.slick-loading img {
  display: none;
}

.emotion-22 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-22 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-22 .slick-initialized .slick-slide,
.emotion-22 .slick-vertical .slick-slide {
  display: block;
}

.emotion-22 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-22 .slick-loading .slick-track,
.emotion-22 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-22 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-22 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-22 .slick-prev,
.emotion-22 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-22 .slick-prev:hover,
.emotion-22 .slick-next:hover,
.emotion-22 .slick-prev:focus,
.emotion-22 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-22 .slick-prev.slick-disabled,
.emotion-22 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-22 .slick-prev {
  left: -0px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-22 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-22 [dir="rtl"] .slick-prev {
  right: -0px;
  left: auto;
}

.emotion-22 .slick-next {
  right: -0px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-22 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-22 [dir="rtl"] .slick-next {
  right: auto;
  left: -0px;
}

.emotion-22 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-22 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-22 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-22 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-22 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-22 .slick-dots li button:hover,
.emotion-22 .slick-dots li button:focus {
  outline: none;
}

.emotion-22 .slick-dots li button:hover:before,
.emotion-22 .slick-dots li button:focus:before,
.emotion-22 .slick-dots li button:hover:before,
.emotion-22 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-22 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-22 .slick-list {
  margin: 0 -6px;
}

.emotion-22 .slick-track {
  margin-left: 0!important;
  margin-right: 0!important;
}

.emotion-22 .category-card {
  margin: 0 6px!important;
  overflow: hidden;
}

.emotion-23.slick-prev.slick-arrow {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: min(3.4375000000000004vw, 44px);
  height: min(3.4375000000000004vw, 44px);
  z-index: 2;
  top: 125px;
  background: #000000;
}

.emotion-23.slick-prev.slick-arrow.slick-disabled {
  display: none;
}

.emotion-23.slick-prev.slick-arrow>span {
  margin: auto;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-23.slick-prev.slick-arrow svg {
  margin: auto;
  height: min(1.953125vw, 25px);
  width: min(1.40625vw, 18px);
}

.emotion-23.slick-prev.slick-arrow:hover {
  background: #000000;
}

.emotion-24 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-24 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-25 {
  outline: none;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-25>div:nth-of-type(2) {
  border-top: 1px solid #000000;
}

.emotion-25:not(:first-of-type)>div:nth-of-type(2) {
  border-left: 1px solid #000000;
}

.emotion-26 {
  overflow: hidden;
  -webkit-transition: background-color .25s ease;
  transition: background-color .25s ease;
  background-color: transparent;
  position: relative;
  width: 248px;
}

.emotion-27 {
  box-sizing: border-box;
  aspect-ratio: 248/250;
  position: relative;
  -webkit-transition: -webkit-transform .25s ease,opacity .25s ease;
  transition: transform .25s ease,opacity .25s ease;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
  opacity: 1;
  width: 100%;
  z-index: 1;
}

.emotion-28 {
  width: 100%;
  aspect-ratio: 248/250;
  object-fit: cover;
}

.emotion-29 {
  background: transparent;
  position: relative;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  z-index: 1;
  box-sizing: border-box;
  position: relative;
  width: 100%;
  min-height: 44px;
  background-color: #FFFFFF;
}

.emotion-30 {
  opacity: 1;
  box-sizing: border-box;
  padding: 11px 15px 9px;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-30 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-30 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-30 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-30 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-30 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-30 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-30 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-30 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-30 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(0.9375vw, 18px));
  line-height: 1.4444444444444444;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-30 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.8333333333333334vw, 16px));
  line-height: 1.375;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-30 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.7291666666666666vw, 14px));
  line-height: 1.4285714285714286;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-30 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-30 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-30 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 16px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-30 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-30 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(48px, min(5vw, 96px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-30 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(40px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-30 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-30 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(28px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-30 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(1.6666666666666667vw, 32px));
  line-height: 1.125;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-30 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(1.25vw, 24px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-30 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1.4;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-30 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(60px, min(6.25vw, 120px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-30 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-30 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(0.5208333333333333vw, 10px));
  line-height: 1.6;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-30 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-30 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-30 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.9375vw, 18px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-30 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(1.25vw, 24px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-30 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-30>* {
  width: 100%;
}

.emotion-67.slick-next.slick-arrow {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: min(3.4375000000000004vw, 44px);
  height: min(3.4375000000000004vw, 44px);
  z-index: 2;
  top: 125px;
  background: #000000;
}

.emotion-67.slick-next.slick-arrow.slick-disabled {
  display: none;
}

.emotion-67.slick-next.slick-arrow>span {
  margin: auto;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-67.slick-next.slick-arrow svg {
  margin: auto;
  height: min(1.953125vw, 25px);
  width: min(1.40625vw, 18px);
}

.emotion-67.slick-next.slick-arrow:hover {
  background: #000000;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="category-banner"
    >
      <article
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div>
            <p
              class="amp-cms--p"
              style="text-align:left;"
            >
              <span
                class="amp-cms--headline-6"
              >
                Lorem ipsum dolor
              </span>
              <span
                class="amp-cms--headline-5"
              >
                 
              </span>
              <span
                class="amp-cms--body-3"
              >
                sit amet consectetur adipiscing elit sed do eiusmod.
              </span>
            </p>
          </div>
        </div>
        <div
          class="emotion-3"
        >
          <div
            class="emotion-4"
          >
            <button
              class="emotion-5"
            >
              <div
                class="emotion-6"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Toggle 1
                    </span>
                  </p>
                </div>
              </div>
            </button>
            <button
              class="emotion-7"
            >
              <div
                class="emotion-6"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Toggle 2
                    </span>
                  </p>
                </div>
              </div>
            </button>
            <button
              class="emotion-7"
            >
              <div
                class="emotion-6"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Toggle 3
                    </span>
                  </p>
                </div>
              </div>
            </button>
          </div>
          <div
            class="emotion-11"
          >
            <div
              class="emotion-12"
            >
              <div
                aria-label="Background"
                class="emotion-13"
                height="0"
                role="img"
                width="0"
              >
                <a
                  aria-label="Banner Title"
                  class="emotion-14"
                  href="www.c.com"
                >
                  <div
                    class="emotion-15"
                  >
                    <div
                      class="emotion-16"
                      data-testid="product-card-image"
                    >
                      <img
                        alt="Background"
                        class="emotion-17"
                        src=""
                      />
                    </div>
                    <div
                      class="emotion-18"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--headline-7"
                          >
                            Intro Headline
                          </span>
                        </p>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            Lorem ipsum dolor sit amet consectetur adipiscing elit
                          </span>
                        </p>
                      </div>
                    </div>
                    <a
                      class="emotion-19"
                      color="dark"
                      href="https://www.gap.com"
                    >
                      <span
                        class="emotion-20"
                      >
                        CTA label
                      </span>
                    </a>
                  </div>
                </a>
              </div>
            </div>
            <div
              class="emotion-21"
            >
              <div
                class="emotion-22"
              >
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <button
                    aria-label="Previous"
                    class="slick-prev slick-arrow slick-prev slick-disabled emotion-23"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-24"
                    >
                      <svg
                        fill="none"
                        viewBox="0 0 28 13"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          clip-rule="evenodd"
                          d="M14.2211 12.2784L27.5 1.2161 26.8781.5 13.9918 11.2352 1.12192.513669.5 1.22977 13.8726 12.3701l.1001.1152.0191-.0159.0368.0306.1925-.2216z"
                          fill="#FFFFFF"
                          fill-rule="evenodd"
                        />
                      </svg>
                    </span>
                  </button>
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-25"
                            href="https://www.A.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-26"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-27"
                              >
                                <img
                                  alt="Jeans"
                                  class="emotion-28"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-29"
                            >
                              <div
                                class="emotion-30"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 1
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-25"
                            href="https://www.B.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-26"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-27"
                              >
                                <img
                                  alt="Man"
                                  class="emotion-28"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-29"
                            >
                              <div
                                class="emotion-30"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 2
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-25"
                            href="https://www.C.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-26"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-27"
                              >
                                <img
                                  alt="Woman"
                                  class="emotion-28"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-29"
                            >
                              <div
                                class="emotion-30"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 3
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="3"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-25"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-26"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-27"
                              >
                                <img
                                  alt="Another Man"
                                  class="emotion-28"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-29"
                            >
                              <div
                                class="emotion-30"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 4
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="4"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-25"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-26"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-27"
                              >
                                <img
                                  alt="Posing"
                                  class="emotion-28"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-29"
                            >
                              <div
                                class="emotion-30"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 5
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="5"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-25"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-26"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-27"
                              >
                                <img
                                  alt="People"
                                  class="emotion-28"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-29"
                            >
                              <div
                                class="emotion-30"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 6
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="6"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-25"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-26"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-27"
                              >
                                <img
                                  alt="Yoga"
                                  class="emotion-28"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-29"
                            >
                              <div
                                class="emotion-30"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 7
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    aria-label="Next"
                    class="slick-next slick-arrow slick-next emotion-67"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-24"
                    >
                      <svg
                        fill="none"
                        viewBox="0 0 28 13"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          clip-rule="evenodd"
                          d="M14.2211 12.2784L27.5 1.2161 26.8781.5 13.9918 11.2352 1.12192.513669.5 1.22977 13.8726 12.3701l.1001.1152.0191-.0159.0368.0306.1925-.2216z"
                          fill="#FFFFFF"
                          fill-rule="evenodd"
                        />
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </article>
    </div>
  </div>
</div>
`;

exports[`VisualNavigationSizeToggleCarousel in extra large viewports - extra large desktops should match snapshots for extra large viewports 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  aspect-ratio: 1280/365;
}

.emotion-2 {
  padding-bottom: 0;
  padding-inline: 0;
}

.emotion-2 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-2 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-2 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-2 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-2 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-2 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-2 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(0.9375vw, 18px));
  line-height: 1.4444444444444444;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.8333333333333334vw, 16px));
  line-height: 1.375;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.7291666666666666vw, 14px));
  line-height: 1.4285714285714286;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 16px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(48px, min(5vw, 96px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(40px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(28px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(1.6666666666666667vw, 32px));
  line-height: 1.125;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(1.25vw, 24px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1.4;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(60px, min(6.25vw, 120px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(0.5208333333333333vw, 10px));
  line-height: 1.6;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-2 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.9375vw, 18px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(1.25vw, 24px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 0;
  overflow: hidden;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 20px;
  margin: 20px 0;
}

.emotion-5 {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  border: none;
  background-color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  -webkit-transition: all 250ms ease-in-out 0s;
  transition: all 250ms ease-in-out 0s;
  border-top: 1.5px solid transparent;
  letter-spacing: 0px;
  min-height: 44px;
  width: auto;
  height: auto;
  -webkit-text-decoration: underline 1px;
  text-decoration: underline 1px;
  text-underline-offset: 5px;
  font-weight: bold;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 4px;
}

.emotion-5:not(:first-of-type) {
  margin-top: -1px;
}

.emotion-5:focus {
  font-weight: bold;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-5:hover {
  outline: none;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-6 {
  width: 100%;
}

.emotion-6 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-6 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-6 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-6 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-6 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-6 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-6 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(0.9375vw, 18px));
  line-height: 1.4444444444444444;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.8333333333333334vw, 16px));
  line-height: 1.375;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.7291666666666666vw, 14px));
  line-height: 1.4285714285714286;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-6 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 16px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-6 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-6 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(48px, min(5vw, 96px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(40px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(28px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(1.6666666666666667vw, 32px));
  line-height: 1.125;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(1.25vw, 24px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1.4;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(60px, min(6.25vw, 120px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(0.5208333333333333vw, 10px));
  line-height: 1.6;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-6 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.9375vw, 18px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(1.25vw, 24px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-7 {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  border: none;
  background-color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  -webkit-transition: all 250ms ease-in-out 0s;
  transition: all 250ms ease-in-out 0s;
  border-top: 1.5px solid transparent;
  letter-spacing: 0px;
  min-height: 44px;
  width: auto;
  height: auto;
  -webkit-text-decoration: underline 1px;
  text-decoration: underline 1px;
  text-underline-offset: 5px;
  text-underline-offset: 4px;
}

.emotion-7:not(:first-of-type) {
  margin-top: -1px;
}

.emotion-7:focus {
  font-weight: bold;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-7:hover {
  outline: none;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-11 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
}

.emotion-12 {
  padding: 0 12px 0 0;
  max-width: 248px;
}

.emotion-13 {
  background: transparent;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  min-width: auto;
  -webkit-background-position: center;
  background-position: center;
  -webkit-background-size: cover;
  background-size: cover;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  height: 100%;
  min-width: 248px;
}

.emotion-14 {
  height: 100%;
  width: 100%;
  border: 0;
  padding: 0;
  margin: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-15 {
  padding: 0 8px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 20px;
}

.emotion-16 {
  -webkit-background-size: cover;
  background-size: cover;
  width: 24px;
}

.emotion-17 {
  width: 100%;
  object-fit: cover;
}

.emotion-18 {
  margin-block: 0;
}

.emotion-18 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-18 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-18 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-18 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-18 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-18 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-18 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-18 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(0.9375vw, 18px));
  line-height: 1.4444444444444444;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.8333333333333334vw, 16px));
  line-height: 1.375;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.7291666666666666vw, 14px));
  line-height: 1.4285714285714286;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-18 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 16px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-18 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-18 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(48px, min(5vw, 96px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(40px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(28px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(1.6666666666666667vw, 32px));
  line-height: 1.125;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(1.25vw, 24px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1.4;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(60px, min(6.25vw, 120px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-18 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(0.5208333333333333vw, 10px));
  line-height: 1.6;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-18 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-18 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.9375vw, 18px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(1.25vw, 24px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-19 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: left;
  padding: 0.5em 0.8em;
  font-size: 1.5rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 2px solid currentColor;
  color: #000000;
  padding-left: 0;
  padding-right: 0;
  height: auto;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  white-space: initial;
  padding: 0;
  margin: 0;
  display: inline;
  line-height: 1.5em;
  text-align: center;
  text-underline-offset: 4px;
  z-index: 10;
  font-weight: 400;
}

.emotion-19:focus {
  outline: none;
}

.emotion-19:hover,
.emotion-19:focus {
  color: #FFFFFF;
  border-bottom-color: #000000;
  background-color: #000000;
}

.emotion-20 {
  box-sizing: border-box;
}

.emotion-21 {
  overflow: hidden;
}

.emotion-21 div.slick-track {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 0;
}

.emotion-21 div.slick-track:before {
  display: none;
}

.emotion-21 div.slick-track:after {
  display: none;
}

.emotion-21 div.slick-slide {
  height: auto;
}

.emotion-21 div.slick-slide>div {
  height: 100%;
}

.emotion-22 {
  position: relative;
}

.emotion-22 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-22 .slick-slider .slick-track,
.emotion-22 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-22 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-22 .slick-list:focus {
  outline: none;
}

.emotion-22 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-22 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-22 .slick-track:before,
.emotion-22 .slick-track:after {
  display: table;
  content: "";
}

.emotion-22 .slick-track:after {
  clear: both;
}

.emotion-22 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-22 .slick-slide img {
  display: block;
}

.emotion-22 .slick-slide.slick-loading img {
  display: none;
}

.emotion-22 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-22 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-22 .slick-initialized .slick-slide,
.emotion-22 .slick-vertical .slick-slide {
  display: block;
}

.emotion-22 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-22 .slick-loading .slick-track,
.emotion-22 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-22 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-22 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-22 .slick-prev,
.emotion-22 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-22 .slick-prev:hover,
.emotion-22 .slick-next:hover,
.emotion-22 .slick-prev:focus,
.emotion-22 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-22 .slick-prev.slick-disabled,
.emotion-22 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-22 .slick-prev {
  left: -0px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-22 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-22 [dir="rtl"] .slick-prev {
  right: -0px;
  left: auto;
}

.emotion-22 .slick-next {
  right: -0px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-22 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-22 [dir="rtl"] .slick-next {
  right: auto;
  left: -0px;
}

.emotion-22 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-22 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-22 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-22 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-22 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-22 .slick-dots li button:hover,
.emotion-22 .slick-dots li button:focus {
  outline: none;
}

.emotion-22 .slick-dots li button:hover:before,
.emotion-22 .slick-dots li button:focus:before,
.emotion-22 .slick-dots li button:hover:before,
.emotion-22 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-22 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-22 .slick-list {
  margin: 0 -6px;
}

.emotion-22 .slick-track {
  margin-left: 0!important;
  margin-right: 0!important;
}

.emotion-22 .category-card {
  margin: 0 6px!important;
  overflow: hidden;
}

.emotion-23.slick-prev.slick-arrow {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: min(3.4375000000000004vw, 44px);
  height: min(3.4375000000000004vw, 44px);
  z-index: 2;
  top: 125px;
  background: #000000;
}

.emotion-23.slick-prev.slick-arrow.slick-disabled {
  display: none;
}

.emotion-23.slick-prev.slick-arrow>span {
  margin: auto;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-23.slick-prev.slick-arrow svg {
  margin: auto;
  height: min(1.953125vw, 25px);
  width: min(1.40625vw, 18px);
}

.emotion-23.slick-prev.slick-arrow:hover {
  background: #000000;
}

.emotion-24 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-24 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-25 {
  outline: none;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-25>div:nth-of-type(2) {
  border-top: 1px solid #000000;
}

.emotion-25:not(:first-of-type)>div:nth-of-type(2) {
  border-left: 1px solid #000000;
}

.emotion-26 {
  overflow: hidden;
  -webkit-transition: background-color .25s ease;
  transition: background-color .25s ease;
  background-color: transparent;
  position: relative;
  width: 248px;
}

.emotion-27 {
  box-sizing: border-box;
  aspect-ratio: 248/250;
  position: relative;
  -webkit-transition: -webkit-transform .25s ease,opacity .25s ease;
  transition: transform .25s ease,opacity .25s ease;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
  opacity: 1;
  width: 100%;
  z-index: 1;
}

.emotion-28 {
  width: 100%;
  aspect-ratio: 248/250;
  object-fit: cover;
}

.emotion-29 {
  background: transparent;
  position: relative;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  z-index: 1;
  box-sizing: border-box;
  position: relative;
  width: 100%;
  min-height: 44px;
  background-color: #FFFFFF;
}

.emotion-30 {
  opacity: 1;
  box-sizing: border-box;
  padding: 11px 15px 9px;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-30 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-30 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-30 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-30 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-30 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-30 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-30 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-30 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-30 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(0.9375vw, 18px));
  line-height: 1.4444444444444444;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-30 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.8333333333333334vw, 16px));
  line-height: 1.375;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-30 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.7291666666666666vw, 14px));
  line-height: 1.4285714285714286;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-30 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-30 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-30 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 16px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-30 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-30 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(48px, min(5vw, 96px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-30 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(40px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-30 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-30 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(28px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-30 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(1.6666666666666667vw, 32px));
  line-height: 1.125;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-30 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(1.25vw, 24px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-30 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1.4;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-30 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(60px, min(6.25vw, 120px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-30 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-30 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(0.5208333333333333vw, 10px));
  line-height: 1.6;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-30 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-30 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-30 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.9375vw, 18px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-30 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(1.25vw, 24px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-30 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-30>* {
  width: 100%;
}

.emotion-67.slick-next.slick-arrow {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: min(3.4375000000000004vw, 44px);
  height: min(3.4375000000000004vw, 44px);
  z-index: 2;
  top: 125px;
  background: #000000;
}

.emotion-67.slick-next.slick-arrow.slick-disabled {
  display: none;
}

.emotion-67.slick-next.slick-arrow>span {
  margin: auto;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-67.slick-next.slick-arrow svg {
  margin: auto;
  height: min(1.953125vw, 25px);
  width: min(1.40625vw, 18px);
}

.emotion-67.slick-next.slick-arrow:hover {
  background: #000000;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="category-banner"
    >
      <article
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div>
            <p
              class="amp-cms--p"
              style="text-align:left;"
            >
              <span
                class="amp-cms--headline-6"
              >
                Lorem ipsum dolor
              </span>
              <span
                class="amp-cms--headline-5"
              >
                 
              </span>
              <span
                class="amp-cms--body-3"
              >
                sit amet consectetur adipiscing elit sed do eiusmod.
              </span>
            </p>
          </div>
        </div>
        <div
          class="emotion-3"
        >
          <div
            class="emotion-4"
          >
            <button
              class="emotion-5"
            >
              <div
                class="emotion-6"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Toggle 1
                    </span>
                  </p>
                </div>
              </div>
            </button>
            <button
              class="emotion-7"
            >
              <div
                class="emotion-6"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Toggle 2
                    </span>
                  </p>
                </div>
              </div>
            </button>
            <button
              class="emotion-7"
            >
              <div
                class="emotion-6"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Toggle 3
                    </span>
                  </p>
                </div>
              </div>
            </button>
          </div>
          <div
            class="emotion-11"
          >
            <div
              class="emotion-12"
            >
              <div
                aria-label="Background"
                class="emotion-13"
                height="0"
                role="img"
                width="0"
              >
                <a
                  aria-label="Banner Title"
                  class="emotion-14"
                  href="www.c.com"
                >
                  <div
                    class="emotion-15"
                  >
                    <div
                      class="emotion-16"
                      data-testid="product-card-image"
                    >
                      <img
                        alt="Background"
                        class="emotion-17"
                        src=""
                      />
                    </div>
                    <div
                      class="emotion-18"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--headline-7"
                          >
                            Intro Headline
                          </span>
                        </p>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            Lorem ipsum dolor sit amet consectetur adipiscing elit
                          </span>
                        </p>
                      </div>
                    </div>
                    <a
                      class="emotion-19"
                      color="dark"
                      href="https://www.gap.com"
                    >
                      <span
                        class="emotion-20"
                      >
                        CTA label
                      </span>
                    </a>
                  </div>
                </a>
              </div>
            </div>
            <div
              class="emotion-21"
            >
              <div
                class="emotion-22"
              >
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <button
                    aria-label="Previous"
                    class="slick-prev slick-arrow slick-prev slick-disabled emotion-23"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-24"
                    >
                      <svg
                        fill="none"
                        viewBox="0 0 28 13"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          clip-rule="evenodd"
                          d="M14.2211 12.2784L27.5 1.2161 26.8781.5 13.9918 11.2352 1.12192.513669.5 1.22977 13.8726 12.3701l.1001.1152.0191-.0159.0368.0306.1925-.2216z"
                          fill="#FFFFFF"
                          fill-rule="evenodd"
                        />
                      </svg>
                    </span>
                  </button>
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-25"
                            href="https://www.A.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-26"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-27"
                              >
                                <img
                                  alt="Jeans"
                                  class="emotion-28"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-29"
                            >
                              <div
                                class="emotion-30"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 1
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-25"
                            href="https://www.B.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-26"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-27"
                              >
                                <img
                                  alt="Man"
                                  class="emotion-28"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-29"
                            >
                              <div
                                class="emotion-30"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 2
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-25"
                            href="https://www.C.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-26"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-27"
                              >
                                <img
                                  alt="Woman"
                                  class="emotion-28"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-29"
                            >
                              <div
                                class="emotion-30"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 3
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="3"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-25"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-26"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-27"
                              >
                                <img
                                  alt="Another Man"
                                  class="emotion-28"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-29"
                            >
                              <div
                                class="emotion-30"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 4
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="4"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-25"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-26"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-27"
                              >
                                <img
                                  alt="Posing"
                                  class="emotion-28"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-29"
                            >
                              <div
                                class="emotion-30"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 5
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="5"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-25"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-26"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-27"
                              >
                                <img
                                  alt="People"
                                  class="emotion-28"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-29"
                            >
                              <div
                                class="emotion-30"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 6
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="6"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-25"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-26"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-27"
                              >
                                <img
                                  alt="Yoga"
                                  class="emotion-28"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-29"
                            >
                              <div
                                class="emotion-30"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 7
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    aria-label="Next"
                    class="slick-next slick-arrow slick-next emotion-67"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-24"
                    >
                      <svg
                        fill="none"
                        viewBox="0 0 28 13"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          clip-rule="evenodd"
                          d="M14.2211 12.2784L27.5 1.2161 26.8781.5 13.9918 11.2352 1.12192.513669.5 1.22977 13.8726 12.3701l.1001.1152.0191-.0159.0368.0306.1925-.2216z"
                          fill="#FFFFFF"
                          fill-rule="evenodd"
                        />
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </article>
    </div>
  </div>
</div>
`;

exports[`VisualNavigationSizeToggleCarousel in large viewports - Desktop should match snapshots 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  aspect-ratio: 1280/365;
}

.emotion-2 {
  padding-bottom: 0;
  padding-inline: 0;
}

.emotion-2 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-2 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-2 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-2 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-2 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-2 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-2 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(0.9375vw, 18px));
  line-height: 1.4444444444444444;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.8333333333333334vw, 16px));
  line-height: 1.375;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.7291666666666666vw, 14px));
  line-height: 1.4285714285714286;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 16px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(48px, min(5vw, 96px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(40px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(28px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(1.6666666666666667vw, 32px));
  line-height: 1.125;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(1.25vw, 24px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1.4;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(60px, min(6.25vw, 120px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(0.5208333333333333vw, 10px));
  line-height: 1.6;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-2 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.9375vw, 18px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(1.25vw, 24px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 0;
  overflow: hidden;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 20px;
  margin: 20px 0;
}

.emotion-5 {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  border: none;
  background-color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  -webkit-transition: all 250ms ease-in-out 0s;
  transition: all 250ms ease-in-out 0s;
  border-top: 1.5px solid transparent;
  letter-spacing: 0px;
  min-height: 44px;
  width: auto;
  height: auto;
  -webkit-text-decoration: underline 1px;
  text-decoration: underline 1px;
  text-underline-offset: 5px;
  font-weight: bold;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 4px;
}

.emotion-5:not(:first-of-type) {
  margin-top: -1px;
}

.emotion-5:focus {
  font-weight: bold;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-5:hover {
  outline: none;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-6 {
  width: 100%;
}

.emotion-6 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-6 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-6 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-6 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-6 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-6 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-6 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(0.9375vw, 18px));
  line-height: 1.4444444444444444;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.8333333333333334vw, 16px));
  line-height: 1.375;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.7291666666666666vw, 14px));
  line-height: 1.4285714285714286;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-6 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 16px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-6 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-6 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(48px, min(5vw, 96px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(40px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(28px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(1.6666666666666667vw, 32px));
  line-height: 1.125;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(1.25vw, 24px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1.4;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(60px, min(6.25vw, 120px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(0.5208333333333333vw, 10px));
  line-height: 1.6;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-6 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.9375vw, 18px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(1.25vw, 24px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-7 {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  border: none;
  background-color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  -webkit-transition: all 250ms ease-in-out 0s;
  transition: all 250ms ease-in-out 0s;
  border-top: 1.5px solid transparent;
  letter-spacing: 0px;
  min-height: 44px;
  width: auto;
  height: auto;
  -webkit-text-decoration: underline 1px;
  text-decoration: underline 1px;
  text-underline-offset: 5px;
  text-underline-offset: 4px;
}

.emotion-7:not(:first-of-type) {
  margin-top: -1px;
}

.emotion-7:focus {
  font-weight: bold;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-7:hover {
  outline: none;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-11 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
}

.emotion-12 {
  padding: 0 12px 0 0;
  max-width: 248px;
}

.emotion-13 {
  background: transparent;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  min-width: auto;
  -webkit-background-position: center;
  background-position: center;
  -webkit-background-size: cover;
  background-size: cover;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  height: 100%;
  min-width: 248px;
}

.emotion-14 {
  height: 100%;
  width: 100%;
  border: 0;
  padding: 0;
  margin: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-15 {
  padding: 0 8px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 20px;
}

.emotion-16 {
  -webkit-background-size: cover;
  background-size: cover;
  width: 24px;
}

.emotion-17 {
  width: 100%;
  object-fit: cover;
}

.emotion-18 {
  margin-block: 0;
}

.emotion-18 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-18 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-18 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-18 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-18 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-18 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-18 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-18 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(0.9375vw, 18px));
  line-height: 1.4444444444444444;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.8333333333333334vw, 16px));
  line-height: 1.375;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.7291666666666666vw, 14px));
  line-height: 1.4285714285714286;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-18 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 16px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-18 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-18 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(48px, min(5vw, 96px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(40px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(28px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(1.6666666666666667vw, 32px));
  line-height: 1.125;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(1.25vw, 24px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1.4;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(60px, min(6.25vw, 120px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-18 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(0.5208333333333333vw, 10px));
  line-height: 1.6;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-18 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-18 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.9375vw, 18px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(1.25vw, 24px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-19 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: left;
  padding: 0.5em 0.8em;
  font-size: 1.5rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 2px solid currentColor;
  color: #000000;
  padding-left: 0;
  padding-right: 0;
  height: auto;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  white-space: initial;
  padding: 0;
  margin: 0;
  display: inline;
  line-height: 1.5em;
  text-align: center;
  text-underline-offset: 4px;
  z-index: 10;
  font-weight: 400;
}

.emotion-19:focus {
  outline: none;
}

.emotion-19:hover,
.emotion-19:focus {
  color: #FFFFFF;
  border-bottom-color: #000000;
  background-color: #000000;
}

.emotion-20 {
  box-sizing: border-box;
}

.emotion-21 {
  overflow: hidden;
}

.emotion-21 div.slick-track {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 0;
}

.emotion-21 div.slick-track:before {
  display: none;
}

.emotion-21 div.slick-track:after {
  display: none;
}

.emotion-21 div.slick-slide {
  height: auto;
}

.emotion-21 div.slick-slide>div {
  height: 100%;
}

.emotion-22 {
  position: relative;
}

.emotion-22 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-22 .slick-slider .slick-track,
.emotion-22 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-22 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-22 .slick-list:focus {
  outline: none;
}

.emotion-22 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-22 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-22 .slick-track:before,
.emotion-22 .slick-track:after {
  display: table;
  content: "";
}

.emotion-22 .slick-track:after {
  clear: both;
}

.emotion-22 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-22 .slick-slide img {
  display: block;
}

.emotion-22 .slick-slide.slick-loading img {
  display: none;
}

.emotion-22 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-22 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-22 .slick-initialized .slick-slide,
.emotion-22 .slick-vertical .slick-slide {
  display: block;
}

.emotion-22 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-22 .slick-loading .slick-track,
.emotion-22 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-22 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-22 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-22 .slick-prev,
.emotion-22 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-22 .slick-prev:hover,
.emotion-22 .slick-next:hover,
.emotion-22 .slick-prev:focus,
.emotion-22 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-22 .slick-prev.slick-disabled,
.emotion-22 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-22 .slick-prev {
  left: -0px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-22 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-22 [dir="rtl"] .slick-prev {
  right: -0px;
  left: auto;
}

.emotion-22 .slick-next {
  right: -0px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-22 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-22 [dir="rtl"] .slick-next {
  right: auto;
  left: -0px;
}

.emotion-22 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-22 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-22 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-22 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-22 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-22 .slick-dots li button:hover,
.emotion-22 .slick-dots li button:focus {
  outline: none;
}

.emotion-22 .slick-dots li button:hover:before,
.emotion-22 .slick-dots li button:focus:before,
.emotion-22 .slick-dots li button:hover:before,
.emotion-22 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-22 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-22 .slick-list {
  margin: 0 -6px;
}

.emotion-22 .slick-track {
  margin-left: 0!important;
  margin-right: 0!important;
}

.emotion-22 .category-card {
  margin: 0 6px!important;
  overflow: hidden;
}

.emotion-23.slick-prev.slick-arrow {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: min(3.4375000000000004vw, 44px);
  height: min(3.4375000000000004vw, 44px);
  z-index: 2;
  top: 125px;
  background: #000000;
}

.emotion-23.slick-prev.slick-arrow.slick-disabled {
  display: none;
}

.emotion-23.slick-prev.slick-arrow>span {
  margin: auto;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-23.slick-prev.slick-arrow svg {
  margin: auto;
  height: min(1.953125vw, 25px);
  width: min(1.40625vw, 18px);
}

.emotion-23.slick-prev.slick-arrow:hover {
  background: #000000;
}

.emotion-24 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-24 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-25 {
  outline: none;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-25>div:nth-of-type(2) {
  border-top: 1px solid #000000;
}

.emotion-25:not(:first-of-type)>div:nth-of-type(2) {
  border-left: 1px solid #000000;
}

.emotion-26 {
  overflow: hidden;
  -webkit-transition: background-color .25s ease;
  transition: background-color .25s ease;
  background-color: transparent;
  position: relative;
  width: 248px;
}

.emotion-27 {
  box-sizing: border-box;
  aspect-ratio: 248/250;
  position: relative;
  -webkit-transition: -webkit-transform .25s ease,opacity .25s ease;
  transition: transform .25s ease,opacity .25s ease;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
  opacity: 1;
  width: 100%;
  z-index: 1;
}

.emotion-28 {
  width: 100%;
  aspect-ratio: 248/250;
  object-fit: cover;
}

.emotion-29 {
  background: transparent;
  position: relative;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  z-index: 1;
  box-sizing: border-box;
  position: relative;
  width: 100%;
  min-height: 44px;
  background-color: #FFFFFF;
}

.emotion-30 {
  opacity: 1;
  box-sizing: border-box;
  padding: 11px 15px 9px;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-30 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-30 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-30 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-30 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-30 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-30 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-30 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-30 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.625vw, 12px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-30 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-30 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(0.9375vw, 18px));
  line-height: 1.4444444444444444;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-30 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.8333333333333334vw, 16px));
  line-height: 1.375;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-30 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.7291666666666666vw, 14px));
  line-height: 1.4285714285714286;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-30 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-30 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-30 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(0.8333333333333334vw, 16px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-30 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.625vw, 12px));
  line-height: 1;
  letter-spacing: min(0.078125vw, 1.5px);
  font-weight: 600;
}

.emotion-30 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(48px, min(5vw, 96px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-30 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(40px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-30 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-30 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(28px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-30 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(1.6666666666666667vw, 32px));
  line-height: 1.125;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-30 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(18px, min(1.25vw, 24px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-30 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(1.0416666666666665vw, 20px));
  line-height: 1.4;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-30 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(32px, min(3.3333333333333335vw, 64px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.5vw, 48px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(60px, min(6.25vw, 120px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-30 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-30 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-30 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(0.5208333333333333vw, 10px));
  line-height: 1.6;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-30 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(4.166666666666666vw, 80px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-30 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 16px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-30 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(0.9375vw, 18px));
  line-height: 1.3333333333333333;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 300;
}

.emotion-30 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(1.25vw, 24px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-30 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.0416666666666665vw, 20px));
  line-height: 1;
  letter-spacing: min(0.026041666666666668vw, 0.5px);
  font-weight: 350;
}

.emotion-30>* {
  width: 100%;
}

.emotion-67.slick-next.slick-arrow {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: min(3.4375000000000004vw, 44px);
  height: min(3.4375000000000004vw, 44px);
  z-index: 2;
  top: 125px;
  background: #000000;
}

.emotion-67.slick-next.slick-arrow.slick-disabled {
  display: none;
}

.emotion-67.slick-next.slick-arrow>span {
  margin: auto;
  height: auto;
  width: auto;
  min-height: auto;
  min-width: auto;
}

.emotion-67.slick-next.slick-arrow svg {
  margin: auto;
  height: min(1.953125vw, 25px);
  width: min(1.40625vw, 18px);
}

.emotion-67.slick-next.slick-arrow:hover {
  background: #000000;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="category-banner"
    >
      <article
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div>
            <p
              class="amp-cms--p"
              style="text-align:left;"
            >
              <span
                class="amp-cms--headline-6"
              >
                Lorem ipsum dolor
              </span>
              <span
                class="amp-cms--headline-5"
              >
                 
              </span>
              <span
                class="amp-cms--body-3"
              >
                sit amet consectetur adipiscing elit sed do eiusmod.
              </span>
            </p>
          </div>
        </div>
        <div
          class="emotion-3"
        >
          <div
            class="emotion-4"
          >
            <button
              class="emotion-5"
            >
              <div
                class="emotion-6"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Toggle 1
                    </span>
                  </p>
                </div>
              </div>
            </button>
            <button
              class="emotion-7"
            >
              <div
                class="emotion-6"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Toggle 2
                    </span>
                  </p>
                </div>
              </div>
            </button>
            <button
              class="emotion-7"
            >
              <div
                class="emotion-6"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Toggle 3
                    </span>
                  </p>
                </div>
              </div>
            </button>
          </div>
          <div
            class="emotion-11"
          >
            <div
              class="emotion-12"
            >
              <div
                aria-label="Background"
                class="emotion-13"
                height="0"
                role="img"
                width="0"
              >
                <a
                  aria-label="Banner Title"
                  class="emotion-14"
                  href="www.c.com"
                >
                  <div
                    class="emotion-15"
                  >
                    <div
                      class="emotion-16"
                      data-testid="product-card-image"
                    >
                      <img
                        alt="Background"
                        class="emotion-17"
                        src=""
                      />
                    </div>
                    <div
                      class="emotion-18"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--headline-7"
                          >
                            Intro Headline
                          </span>
                        </p>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            Lorem ipsum dolor sit amet consectetur adipiscing elit
                          </span>
                        </p>
                      </div>
                    </div>
                    <a
                      class="emotion-19"
                      color="dark"
                      href="https://www.gap.com"
                    >
                      <span
                        class="emotion-20"
                      >
                        CTA label
                      </span>
                    </a>
                  </div>
                </a>
              </div>
            </div>
            <div
              class="emotion-21"
            >
              <div
                class="emotion-22"
              >
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <button
                    aria-label="Previous"
                    class="slick-prev slick-arrow slick-prev slick-disabled emotion-23"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-24"
                    >
                      <svg
                        fill="none"
                        viewBox="0 0 28 13"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          clip-rule="evenodd"
                          d="M14.2211 12.2784L27.5 1.2161 26.8781.5 13.9918 11.2352 1.12192.513669.5 1.22977 13.8726 12.3701l.1001.1152.0191-.0159.0368.0306.1925-.2216z"
                          fill="#FFFFFF"
                          fill-rule="evenodd"
                        />
                      </svg>
                    </span>
                  </button>
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-25"
                            href="https://www.A.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-26"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-27"
                              >
                                <img
                                  alt="Jeans"
                                  class="emotion-28"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-29"
                            >
                              <div
                                class="emotion-30"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 1
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-25"
                            href="https://www.B.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-26"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-27"
                              >
                                <img
                                  alt="Man"
                                  class="emotion-28"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-29"
                            >
                              <div
                                class="emotion-30"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 2
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-25"
                            href="https://www.C.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-26"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-27"
                              >
                                <img
                                  alt="Woman"
                                  class="emotion-28"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-29"
                            >
                              <div
                                class="emotion-30"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 3
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="3"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-25"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-26"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-27"
                              >
                                <img
                                  alt="Another Man"
                                  class="emotion-28"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-29"
                            >
                              <div
                                class="emotion-30"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 4
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="4"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-25"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-26"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-27"
                              >
                                <img
                                  alt="Posing"
                                  class="emotion-28"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-29"
                            >
                              <div
                                class="emotion-30"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 5
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="5"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-25"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-26"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-27"
                              >
                                <img
                                  alt="People"
                                  class="emotion-28"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-29"
                            >
                              <div
                                class="emotion-30"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 6
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="6"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-25"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-26"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-27"
                              >
                                <img
                                  alt="Yoga"
                                  class="emotion-28"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-29"
                            >
                              <div
                                class="emotion-30"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 7
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    aria-label="Next"
                    class="slick-next slick-arrow slick-next emotion-67"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-24"
                    >
                      <svg
                        fill="none"
                        viewBox="0 0 28 13"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          clip-rule="evenodd"
                          d="M14.2211 12.2784L27.5 1.2161 26.8781.5 13.9918 11.2352 1.12192.513669.5 1.22977 13.8726 12.3701l.1001.1152.0191-.0159.0368.0306.1925-.2216z"
                          fill="#FFFFFF"
                          fill-rule="evenodd"
                        />
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </article>
    </div>
  </div>
</div>
`;

exports[`VisualNavigationSizeToggleCarousel in small viewports - mobile should match snapshots for mobile 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  min-height: 235px;
}

.emotion-2 {
  padding-bottom: 10px;
  padding-inline: 10px;
}

.emotion-2 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-2 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-2 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-2 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-2 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.666666666666667vw, 10px));
  line-height: 1.8;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-2 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-2 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.266666666666667vw, 16px));
  line-height: 1.625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.5714285714285714;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.6666666666666667;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.666666666666667vw, 10px));
  line-height: 1.8;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, min(3.2vw, 12px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(12.8vw, 48px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(8.533333333333333vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(7.466666666666668vw, 28px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.4vw, 24px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.8vw, 18px));
  line-height: 1.1111111111111112;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1.1428571428571428;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(8.533333333333333vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.4vw, 24px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.333333333333334vw, 20px));
  line-height: 1.1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.1333333333333333vw, 8px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-2 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.266666666666667vw, 16px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 12px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 0;
  overflow: hidden;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 20px;
  margin: 12px 0 16px;
}

.emotion-5 {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  border: none;
  background-color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  -webkit-transition: all 250ms ease-in-out 0s;
  transition: all 250ms ease-in-out 0s;
  border-top: 1.5px solid transparent;
  letter-spacing: 0px;
  min-height: 44px;
  width: auto;
  height: auto;
  -webkit-text-decoration: underline 1px;
  text-decoration: underline 1px;
  text-underline-offset: 5px;
  font-weight: bold;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 4px;
}

.emotion-5:not(:first-of-type) {
  margin-top: -1px;
}

.emotion-5:focus {
  font-weight: bold;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-5:hover {
  outline: none;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-6 {
  width: 100%;
}

.emotion-6 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-6 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-6 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-6 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-6 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.666666666666667vw, 10px));
  line-height: 1.8;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-6 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-6 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-6 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.266666666666667vw, 16px));
  line-height: 1.625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.5714285714285714;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.6666666666666667;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.666666666666667vw, 10px));
  line-height: 1.8;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-6 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, min(3.2vw, 12px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-6 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-6 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(12.8vw, 48px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(8.533333333333333vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(7.466666666666668vw, 28px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.4vw, 24px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.8vw, 18px));
  line-height: 1.1111111111111112;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1.1428571428571428;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(8.533333333333333vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.4vw, 24px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.333333333333334vw, 20px));
  line-height: 1.1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.1333333333333333vw, 8px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-6 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-6 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.266666666666667vw, 16px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 12px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-7 {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  border: none;
  background-color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  -webkit-transition: all 250ms ease-in-out 0s;
  transition: all 250ms ease-in-out 0s;
  border-top: 1.5px solid transparent;
  letter-spacing: 0px;
  min-height: 44px;
  width: auto;
  height: auto;
  -webkit-text-decoration: underline 1px;
  text-decoration: underline 1px;
  text-underline-offset: 5px;
  text-underline-offset: 4px;
}

.emotion-7:not(:first-of-type) {
  margin-top: -1px;
}

.emotion-7:focus {
  font-weight: bold;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-7:hover {
  outline: none;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-11 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
}

.emotion-12 {
  padding: 0 8px 0 0;
  max-width: 145px;
}

.emotion-13 {
  background: transparent;
  -webkit-flex: 0;
  -ms-flex: 0;
  flex: 0;
  min-width: auto;
  -webkit-background-position: center;
  background-position: center;
  -webkit-background-size: cover;
  background-size: cover;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  height: 100%;
  min-width: 145px;
}

.emotion-14 {
  height: 100%;
  width: 100%;
  border: 0;
  padding: 0;
  margin: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-15 {
  padding: 0 8px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 12px;
}

.emotion-16 {
  -webkit-background-size: cover;
  background-size: cover;
  width: 14px;
}

.emotion-17 {
  width: 100%;
  object-fit: cover;
}

.emotion-18 {
  margin-block: 0;
}

.emotion-18 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-18 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-18 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-18 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-18 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.666666666666667vw, 10px));
  line-height: 1.8;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-18 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-18 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-18 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.266666666666667vw, 16px));
  line-height: 1.625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.5714285714285714;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.6666666666666667;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.666666666666667vw, 10px));
  line-height: 1.8;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-18 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, min(3.2vw, 12px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-18 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-18 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(12.8vw, 48px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(8.533333333333333vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(7.466666666666668vw, 28px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.4vw, 24px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.8vw, 18px));
  line-height: 1.1111111111111112;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1.1428571428571428;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(8.533333333333333vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.4vw, 24px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.333333333333334vw, 20px));
  line-height: 1.1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-18 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.1333333333333333vw, 8px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-18 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-18 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-18 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.266666666666667vw, 16px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 12px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-19 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: left;
  padding: 0.5em 0.8em;
  font-size: 0.8rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 2px solid currentColor;
  color: #000000;
  padding-left: 0;
  padding-right: 0;
  height: auto;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  white-space: initial;
  padding: 0;
  margin: 0;
  display: inline;
  line-height: 1.5em;
  text-align: center;
  text-underline-offset: 4px;
  z-index: 10;
  font-weight: 400;
}

.emotion-19:focus {
  outline: none;
}

.emotion-19:hover,
.emotion-19:focus {
  color: #FFFFFF;
  border-bottom-color: #000000;
  background-color: #000000;
}

.emotion-20 {
  box-sizing: border-box;
}

.emotion-21 {
  overflow: hidden;
}

.emotion-21 div.slick-track {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 0;
}

.emotion-21 div.slick-track:before {
  display: none;
}

.emotion-21 div.slick-track:after {
  display: none;
}

.emotion-21 div.slick-slide {
  height: auto;
}

.emotion-21 div.slick-slide>div {
  height: 100%;
}

.emotion-22 {
  position: relative;
}

.emotion-22 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-22 .slick-slider .slick-track,
.emotion-22 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-22 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-22 .slick-list:focus {
  outline: none;
}

.emotion-22 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-22 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-22 .slick-track:before,
.emotion-22 .slick-track:after {
  display: table;
  content: "";
}

.emotion-22 .slick-track:after {
  clear: both;
}

.emotion-22 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-22 .slick-slide img {
  display: block;
}

.emotion-22 .slick-slide.slick-loading img {
  display: none;
}

.emotion-22 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-22 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-22 .slick-initialized .slick-slide,
.emotion-22 .slick-vertical .slick-slide {
  display: block;
}

.emotion-22 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-22 .slick-loading .slick-track,
.emotion-22 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-22 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-22 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-22 .slick-prev,
.emotion-22 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-22 .slick-prev:hover,
.emotion-22 .slick-next:hover,
.emotion-22 .slick-prev:focus,
.emotion-22 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-22 .slick-prev.slick-disabled,
.emotion-22 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-22 .slick-prev {
  left: -0px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-22 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-22 [dir="rtl"] .slick-prev {
  right: -0px;
  left: auto;
}

.emotion-22 .slick-next {
  right: -0px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-22 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-22 [dir="rtl"] .slick-next {
  right: auto;
  left: -0px;
}

.emotion-22 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-22 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-22 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-22 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-22 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-22 .slick-dots li button:hover,
.emotion-22 .slick-dots li button:focus {
  outline: none;
}

.emotion-22 .slick-dots li button:hover:before,
.emotion-22 .slick-dots li button:focus:before,
.emotion-22 .slick-dots li button:hover:before,
.emotion-22 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-22 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-22 .slick-list {
  margin: 0 -4px;
}

.emotion-22 .slick-track {
  margin-left: 0!important;
  margin-right: 0!important;
}

.emotion-22 .category-card {
  margin: 0 4px!important;
  overflow: hidden;
}

.emotion-23 {
  outline: none;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-23>div:nth-of-type(2) {
  border-top: 1px solid #000000;
}

.emotion-23:not(:first-of-type)>div:nth-of-type(2) {
  border-left: 1px solid #000000;
}

.emotion-24 {
  overflow: hidden;
  -webkit-transition: background-color .25s ease;
  transition: background-color .25s ease;
  background-color: transparent;
  min-width: 145px;
  position: relative;
  width: 145px;
}

.emotion-25 {
  box-sizing: border-box;
  aspect-ratio: 145/205;
  position: relative;
  -webkit-transition: -webkit-transform .25s ease,opacity .25s ease;
  transition: transform .25s ease,opacity .25s ease;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
  opacity: 1;
  width: 100%;
  z-index: 1;
}

.emotion-26 {
  width: 100%;
  aspect-ratio: 145/205;
  object-fit: cover;
}

.emotion-27 {
  background: transparent;
  position: relative;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  z-index: 1;
  box-sizing: border-box;
  position: relative;
  width: 100%;
  min-height: 44px;
  background-color: #FFFFFF;
}

.emotion-28 {
  opacity: 1;
  box-sizing: border-box;
  padding: 11px 15px 9px;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-28 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-28 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-28 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-28 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-28 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.666666666666667vw, 10px));
  line-height: 1.8;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-28 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-28 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-28 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-28 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-28 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-28 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-28 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-28 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.266666666666667vw, 16px));
  line-height: 1.625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-28 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.5714285714285714;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-28 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.6666666666666667;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-28 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.666666666666667vw, 10px));
  line-height: 1.8;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-28 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-28 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, min(3.2vw, 12px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-28 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-28 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(12.8vw, 48px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-28 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-28 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(8.533333333333333vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-28 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(7.466666666666668vw, 28px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-28 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.4vw, 24px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-28 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.8vw, 18px));
  line-height: 1.1111111111111112;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-28 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1.1428571428571428;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-28 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(8.533333333333333vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-28 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.4vw, 24px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-28 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.333333333333334vw, 20px));
  line-height: 1.1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-28 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-28 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-28 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-28 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-28 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.1333333333333333vw, 8px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-28 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-28 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-28 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-28 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.266666666666667vw, 16px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-28 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 12px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-28>* {
  width: 100%;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="category-banner"
    >
      <article
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div>
            <p
              class="amp-cms--p"
              style="text-align:left;"
            >
              <span
                class="amp-cms--headline-6"
              >
                Lorem ipsum dolor
              </span>
              <span
                class="amp-cms--headline-5"
              >
                 
              </span>
              <span
                class="amp-cms--body-3"
              >
                sit amet consectetur adipiscing elit sed do eiusmod.
              </span>
            </p>
          </div>
        </div>
        <div
          class="emotion-3"
        >
          <div
            class="emotion-4"
          >
            <button
              class="emotion-5"
            >
              <div
                class="emotion-6"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Toggle 1
                    </span>
                  </p>
                </div>
              </div>
            </button>
            <button
              class="emotion-7"
            >
              <div
                class="emotion-6"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Toggle 2
                    </span>
                  </p>
                </div>
              </div>
            </button>
            <button
              class="emotion-7"
            >
              <div
                class="emotion-6"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Toggle 3
                    </span>
                  </p>
                </div>
              </div>
            </button>
          </div>
          <div
            class="emotion-11"
          >
            <div
              class="emotion-12"
            >
              <div
                aria-label="Background"
                class="emotion-13"
                height="0"
                role="img"
                width="0"
              >
                <a
                  aria-label="Banner Title"
                  class="emotion-14"
                  href="www.c.com"
                >
                  <div
                    class="emotion-15"
                  >
                    <div
                      class="emotion-16"
                      data-testid="product-card-image"
                    >
                      <img
                        alt="Background"
                        class="emotion-17"
                        src=""
                      />
                    </div>
                    <div
                      class="emotion-18"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--headline-7"
                          >
                            Intro Headline
                          </span>
                        </p>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            Lorem ipsum dolor sit amet consectetur adipiscing elit
                          </span>
                        </p>
                      </div>
                    </div>
                    <a
                      class="emotion-19"
                      color="dark"
                      href="https://www.gap.com"
                    >
                      <span
                        class="emotion-20"
                      >
                        CTA label
                      </span>
                    </a>
                  </div>
                </a>
              </div>
            </div>
            <div
              class="emotion-21"
            >
              <div
                class="emotion-22"
              >
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-23"
                            href="https://www.A.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-24"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-25"
                              >
                                <img
                                  alt="Jeans"
                                  class="emotion-26"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-27"
                            >
                              <div
                                class="emotion-28"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 1
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-23"
                            href="https://www.B.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-24"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-25"
                              >
                                <img
                                  alt="Man"
                                  class="emotion-26"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-27"
                            >
                              <div
                                class="emotion-28"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 2
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-23"
                            href="https://www.C.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-24"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-25"
                              >
                                <img
                                  alt="Woman"
                                  class="emotion-26"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-27"
                            >
                              <div
                                class="emotion-28"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 3
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="3"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-23"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-24"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-25"
                              >
                                <img
                                  alt="Another Man"
                                  class="emotion-26"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-27"
                            >
                              <div
                                class="emotion-28"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 4
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="4"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-23"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-24"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-25"
                              >
                                <img
                                  alt="Posing"
                                  class="emotion-26"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-27"
                            >
                              <div
                                class="emotion-28"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 5
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="5"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-23"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-24"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-25"
                              >
                                <img
                                  alt="People"
                                  class="emotion-26"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-27"
                            >
                              <div
                                class="emotion-28"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 6
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="6"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-23"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-24"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-25"
                              >
                                <img
                                  alt="Yoga"
                                  class="emotion-26"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-27"
                            >
                              <div
                                class="emotion-28"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 7
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </article>
    </div>
  </div>
</div>
`;

exports[`VisualNavigationSizeToggleCarousel in small viewports - mobile should match snapshots for mobile with chevroncolor dark 1`] = `
.emotion-0 {
  margin-left: auto;
  margin-right: auto;
  max-width: 1280px;
}

.emotion-1 {
  min-height: 235px;
}

.emotion-2 {
  padding-bottom: 10px;
  padding-inline: 10px;
}

.emotion-2 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-2 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-2 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-2 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-2 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.666666666666667vw, 10px));
  line-height: 1.8;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-2 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-2 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-2 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.266666666666667vw, 16px));
  line-height: 1.625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.5714285714285714;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.6666666666666667;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.666666666666667vw, 10px));
  line-height: 1.8;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, min(3.2vw, 12px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-2 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(12.8vw, 48px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(8.533333333333333vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(7.466666666666668vw, 28px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.4vw, 24px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.8vw, 18px));
  line-height: 1.1111111111111112;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1.1428571428571428;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(8.533333333333333vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.4vw, 24px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.333333333333334vw, 20px));
  line-height: 1.1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-2 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.1333333333333333vw, 8px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-2 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-2 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-2 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-2 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.266666666666667vw, 16px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-2 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 12px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-3 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-4 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 0;
  overflow: hidden;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 20px;
  margin: 12px 0 16px;
}

.emotion-5 {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  border: none;
  background-color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  -webkit-transition: all 250ms ease-in-out 0s;
  transition: all 250ms ease-in-out 0s;
  border-top: 1.5px solid transparent;
  letter-spacing: 0px;
  min-height: 44px;
  width: auto;
  height: auto;
  -webkit-text-decoration: underline 1px;
  text-decoration: underline 1px;
  text-underline-offset: 5px;
  font-weight: bold;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  text-underline-offset: 4px;
}

.emotion-5:not(:first-of-type) {
  margin-top: -1px;
}

.emotion-5:focus {
  font-weight: bold;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-5:hover {
  outline: none;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-6 {
  width: 100%;
}

.emotion-6 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-6 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-6 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-6 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-6 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.666666666666667vw, 10px));
  line-height: 1.8;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-6 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-6 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-6 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-6 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.266666666666667vw, 16px));
  line-height: 1.625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.5714285714285714;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.6666666666666667;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.666666666666667vw, 10px));
  line-height: 1.8;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-6 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, min(3.2vw, 12px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-6 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-6 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(12.8vw, 48px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(8.533333333333333vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(7.466666666666668vw, 28px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.4vw, 24px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.8vw, 18px));
  line-height: 1.1111111111111112;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1.1428571428571428;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(8.533333333333333vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.4vw, 24px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.333333333333334vw, 20px));
  line-height: 1.1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-6 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.1333333333333333vw, 8px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-6 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-6 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-6 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-6 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.266666666666667vw, 16px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-6 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 12px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-7 {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  border: none;
  background-color: #FFFFFF;
  -webkit-text-decoration: underline;
  text-decoration: underline;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  color: #000000;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  cursor: pointer;
  -webkit-transition: all 250ms ease-in-out 0s;
  transition: all 250ms ease-in-out 0s;
  border-top: 1.5px solid transparent;
  letter-spacing: 0px;
  min-height: 44px;
  width: auto;
  height: auto;
  -webkit-text-decoration: underline 1px;
  text-decoration: underline 1px;
  text-underline-offset: 5px;
  text-underline-offset: 4px;
}

.emotion-7:not(:first-of-type) {
  margin-top: -1px;
}

.emotion-7:focus {
  font-weight: bold;
  -webkit-text-decoration: underline;
  text-decoration: underline;
}

.emotion-7:hover {
  outline: none;
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-11 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
}

.emotion-12 {
  padding: 0 8px 0 0;
  max-width: 145px;
}

.emotion-13 {
  background: transparent;
  -webkit-flex: 0;
  -ms-flex: 0;
  flex: 0;
  min-width: auto;
  -webkit-background-position: center;
  background-position: center;
  -webkit-background-size: cover;
  background-size: cover;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  height: 100%;
  min-width: 145px;
}

.emotion-14 {
  height: 100%;
  width: 100%;
  border: 0;
  padding: 0;
  margin: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-15 {
  padding: 0 8px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 12px;
}

.emotion-16 {
  -webkit-background-size: cover;
  background-size: cover;
  width: 14px;
}

.emotion-17 {
  width: 100%;
  object-fit: cover;
}

.emotion-18 {
  margin-block: 0;
}

.emotion-18 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-18 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-18 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-18 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-18 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.666666666666667vw, 10px));
  line-height: 1.8;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-18 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-18 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-18 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-18 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.266666666666667vw, 16px));
  line-height: 1.625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.5714285714285714;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.6666666666666667;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.666666666666667vw, 10px));
  line-height: 1.8;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-18 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, min(3.2vw, 12px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-18 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-18 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(12.8vw, 48px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(8.533333333333333vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(7.466666666666668vw, 28px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.4vw, 24px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.8vw, 18px));
  line-height: 1.1111111111111112;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1.1428571428571428;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(8.533333333333333vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.4vw, 24px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.333333333333334vw, 20px));
  line-height: 1.1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-18 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-18 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.1333333333333333vw, 8px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-18 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-18 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-18 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-18 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.266666666666667vw, 16px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-18 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 12px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-19 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 0;
  text-align: left;
  padding: 0.5em 0.8em;
  font-size: 0.8rem;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-weight: 600;
  border-left: none;
  border-right: none;
  border-radius: 0;
  background-color: transparent;
  border-bottom: 2px solid currentColor;
  color: #000000;
  padding-left: 0;
  padding-right: 0;
  height: auto;
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  white-space: normal;
  white-space: initial;
  padding: 0;
  margin: 0;
  display: inline;
  line-height: 1.5em;
  text-align: center;
  text-underline-offset: 4px;
  z-index: 10;
  font-weight: 400;
}

.emotion-19:focus {
  outline: none;
}

.emotion-19:hover,
.emotion-19:focus {
  color: #FFFFFF;
  border-bottom-color: #000000;
  background-color: #000000;
}

.emotion-20 {
  box-sizing: border-box;
}

.emotion-21 {
  overflow: hidden;
}

.emotion-21 div.slick-track {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 0;
}

.emotion-21 div.slick-track:before {
  display: none;
}

.emotion-21 div.slick-track:after {
  display: none;
}

.emotion-21 div.slick-slide {
  height: auto;
}

.emotion-21 div.slick-slide>div {
  height: 100%;
}

.emotion-22 {
  position: relative;
}

.emotion-22 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-22 .slick-slider .slick-track,
.emotion-22 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-22 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-22 .slick-list:focus {
  outline: none;
}

.emotion-22 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-22 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-22 .slick-track:before,
.emotion-22 .slick-track:after {
  display: table;
  content: "";
}

.emotion-22 .slick-track:after {
  clear: both;
}

.emotion-22 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-22 .slick-slide img {
  display: block;
}

.emotion-22 .slick-slide.slick-loading img {
  display: none;
}

.emotion-22 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-22 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-22 .slick-initialized .slick-slide,
.emotion-22 .slick-vertical .slick-slide {
  display: block;
}

.emotion-22 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-22 .slick-loading .slick-track,
.emotion-22 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-22 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-22 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-22 .slick-prev,
.emotion-22 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-22 .slick-prev:hover,
.emotion-22 .slick-next:hover,
.emotion-22 .slick-prev:focus,
.emotion-22 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-22 .slick-prev.slick-disabled,
.emotion-22 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-22 .slick-prev {
  left: -0px;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-22 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-22 [dir="rtl"] .slick-prev {
  right: -0px;
  left: auto;
}

.emotion-22 .slick-next {
  right: -0px;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-22 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-22 [dir="rtl"] .slick-next {
  right: auto;
  left: -0px;
}

.emotion-22 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-22 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-22 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-22 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-22 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-22 .slick-dots li button:hover,
.emotion-22 .slick-dots li button:focus {
  outline: none;
}

.emotion-22 .slick-dots li button:hover:before,
.emotion-22 .slick-dots li button:focus:before,
.emotion-22 .slick-dots li button:hover:before,
.emotion-22 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-22 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-22 .slick-list {
  margin: 0 -4px;
}

.emotion-22 .slick-track {
  margin-left: 0!important;
  margin-right: 0!important;
}

.emotion-22 .category-card {
  margin: 0 4px!important;
  overflow: hidden;
}

.emotion-23 {
  outline: none;
  position: relative;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.emotion-23>div:nth-of-type(2) {
  border-top: 1px solid #000000;
}

.emotion-23:not(:first-of-type)>div:nth-of-type(2) {
  border-left: 1px solid #000000;
}

.emotion-24 {
  overflow: hidden;
  -webkit-transition: background-color .25s ease;
  transition: background-color .25s ease;
  background-color: transparent;
  min-width: 145px;
  position: relative;
  width: 145px;
}

.emotion-25 {
  box-sizing: border-box;
  aspect-ratio: 145/205;
  position: relative;
  -webkit-transition: -webkit-transform .25s ease,opacity .25s ease;
  transition: transform .25s ease,opacity .25s ease;
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  transform: none;
  opacity: 1;
  width: 100%;
  z-index: 1;
}

.emotion-26 {
  width: 100%;
  aspect-ratio: 145/205;
  object-fit: cover;
}

.emotion-27 {
  background: transparent;
  position: relative;
  box-sizing: border-box;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  z-index: 1;
  box-sizing: border-box;
  position: relative;
  width: 100%;
  min-height: 44px;
  background-color: #FFFFFF;
}

.emotion-28 {
  opacity: 1;
  box-sizing: border-box;
  padding: 11px 15px 9px;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-28 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-28 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-28 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-28 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-28 .amp-cms--legal-copy {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.666666666666667vw, 10px));
  line-height: 1.8;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-28 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.25ex;
}

.emotion-28 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.25ex;
}

.emotion-28 .amp-cms--f-0 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-28 .amp-cms--f-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-28 .amp-cms--f-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-28 .amp-cms--fn-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 11px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-28 .amp-cms--body-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 12px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-28 .amp-cms--body-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.266666666666667vw, 16px));
  line-height: 1.625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-28 .amp-cms--body-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 14px));
  line-height: 1.5714285714285714;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-28 .amp-cms--body-4 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 12px));
  line-height: 1.6666666666666667;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-28 .amp-cms--body-5 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.666666666666667vw, 10px));
  line-height: 1.8;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-28 .amp-cms--eyebrow-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-28 .amp-cms--eyebrow-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(11px, min(3.2vw, 12px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-28 .amp-cms--eyebrow-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 10px));
  line-height: 1;
  letter-spacing: min(0.4vw, 1.5px);
  font-weight: 600;
}

.emotion-28 .amp-cms--headline-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(12.8vw, 48px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-28 .amp-cms--headline-2 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-28 .amp-cms--headline-3 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(8.533333333333333vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-28 .amp-cms--headline-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(7.466666666666668vw, 28px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-28 .amp-cms--headline-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.4vw, 24px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-28 .amp-cms--headline-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.8vw, 18px));
  line-height: 1.1111111111111112;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-28 .amp-cms--headline-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1.1428571428571428;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-28 .amp-cms--headlineAlt-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(8.533333333333333vw, 32px));
  line-height: 1.0625;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-28 .amp-cms--headlineAlt-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.4vw, 24px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-28 .amp-cms--headlineAlt-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.333333333333334vw, 20px));
  line-height: 1.1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-28 .amp-cms--headlineAlt-4 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(16vw, 60px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-28 .amp-cms--headlineAlt-5 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-28 .amp-cms--headlineAlt-6 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-28 .amp-cms--headlineAlt-7 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-28 .amp-cms--promo-1 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(8px, min(2.1333333333333333vw, 8px));
  line-height: 1.5;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 400;
}

.emotion-28 .amp-cms--promo-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 40px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-28 .amp-cms--promoAlt-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(3.733333333333334vw, 14px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-28 .amp-cms--subhead-1 {
  color: #000000;
  font-family: var(--font-banana-serif),Times New Roman,serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 14px));
  line-height: 1.2857142857142858;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 300;
}

.emotion-28 .amp-cms--subhead-2 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(4.266666666666667vw, 16px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-28 .amp-cms--subhead-3 {
  color: #000000;
  font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.2vw, 12px));
  line-height: 1;
  letter-spacing: min(0.13333333333333333vw, 0.5px);
  font-weight: 350;
}

.emotion-28>* {
  width: 100%;
}

<div>
  <div
    style="font-family: var(--font-lynstone),Helvetica,Arial,sans-serif;"
  >
    <div
      class="emotion-0"
      data-testid="category-banner"
    >
      <article
        class="emotion-1"
      >
        <div
          class="emotion-2"
        >
          <div>
            <p
              class="amp-cms--p"
              style="text-align:left;"
            >
              <span
                class="amp-cms--headline-6"
              >
                Lorem ipsum dolor
              </span>
              <span
                class="amp-cms--headline-5"
              >
                 
              </span>
              <span
                class="amp-cms--body-3"
              >
                sit amet consectetur adipiscing elit sed do eiusmod.
              </span>
            </p>
          </div>
        </div>
        <div
          class="emotion-3"
        >
          <div
            class="emotion-4"
          >
            <button
              class="emotion-5"
            >
              <div
                class="emotion-6"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Toggle 1
                    </span>
                  </p>
                </div>
              </div>
            </button>
            <button
              class="emotion-7"
            >
              <div
                class="emotion-6"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Toggle 2
                    </span>
                  </p>
                </div>
              </div>
            </button>
            <button
              class="emotion-7"
            >
              <div
                class="emotion-6"
              >
                <div>
                  <p
                    class="amp-cms--p"
                    style="text-align:left;"
                  >
                    <span
                      class="amp-cms--body-1"
                    >
                      Toggle 3
                    </span>
                  </p>
                </div>
              </div>
            </button>
          </div>
          <div
            class="emotion-11"
          >
            <div
              class="emotion-12"
            >
              <div
                aria-label="Background"
                class="emotion-13"
                height="0"
                role="img"
                width="0"
              >
                <a
                  aria-label="Banner Title"
                  class="emotion-14"
                  href="www.c.com"
                >
                  <div
                    class="emotion-15"
                  >
                    <div
                      class="emotion-16"
                      data-testid="product-card-image"
                    >
                      <img
                        alt="Background"
                        class="emotion-17"
                        src=""
                      />
                    </div>
                    <div
                      class="emotion-18"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--headline-7"
                          >
                            Intro Headline
                          </span>
                        </p>
                        <p
                          class="amp-cms--p"
                          style="text-align:center;"
                        >
                          <span
                            class="amp-cms--body-2"
                          >
                            Lorem ipsum dolor sit amet consectetur adipiscing elit
                          </span>
                        </p>
                      </div>
                    </div>
                    <a
                      class="emotion-19"
                      color="dark"
                      href="https://www.gap.com"
                    >
                      <span
                        class="emotion-20"
                      >
                        CTA label
                      </span>
                    </a>
                  </div>
                </a>
              </div>
            </div>
            <div
              class="emotion-21"
            >
              <div
                class="emotion-22"
              >
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-23"
                            href="https://www.A.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-24"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-25"
                              >
                                <img
                                  alt="Jeans"
                                  class="emotion-26"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-27"
                            >
                              <div
                                class="emotion-28"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 1
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-23"
                            href="https://www.B.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-24"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-25"
                              >
                                <img
                                  alt="Man"
                                  class="emotion-26"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-27"
                            >
                              <div
                                class="emotion-28"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 2
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-23"
                            href="https://www.C.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-24"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-25"
                              >
                                <img
                                  alt="Woman"
                                  class="emotion-26"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-27"
                            >
                              <div
                                class="emotion-28"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 3
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="3"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-23"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-24"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-25"
                              >
                                <img
                                  alt="Another Man"
                                  class="emotion-26"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-27"
                            >
                              <div
                                class="emotion-28"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 4
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="4"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-23"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-24"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-25"
                              >
                                <img
                                  alt="Posing"
                                  class="emotion-26"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-27"
                            >
                              <div
                                class="emotion-28"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 5
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="5"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-23"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-24"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-25"
                              >
                                <img
                                  alt="People"
                                  class="emotion-26"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-27"
                            >
                              <div
                                class="emotion-28"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 6
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="6"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <a
                            class="category-card emotion-23"
                            href="https://www.gap.com"
                            target="_self"
                          >
                            <div
                              aria-live="polite"
                              class="emotion-24"
                            >
                              <div
                                aria-hidden="false"
                                class="emotion-25"
                              >
                                <img
                                  alt="Yoga"
                                  class="emotion-26"
                                  src=""
                                />
                              </div>
                            </div>
                            <div
                              class="emotion-27"
                            >
                              <div
                                class="emotion-28"
                              >
                                <div>
                                  <p
                                    class="amp-cms--p"
                                    style="text-align:left;"
                                  >
                                    <span
                                      class="amp-cms--body-1"
                                    >
                                      Category 7
                                    </span>
                                  </p>
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </article>
    </div>
  </div>
</div>
`;
