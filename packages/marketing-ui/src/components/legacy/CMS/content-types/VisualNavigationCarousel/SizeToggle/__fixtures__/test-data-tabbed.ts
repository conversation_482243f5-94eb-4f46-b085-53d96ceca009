// @ts-nocheck
import { VisualNavigationIntroCard } from '../../../VisualNavigation/VisualNavigation/types';
import { VisualNavigationSizeToggleCarouselContentType } from '../types';

const background2Png = require('../../../../../assets/background-2.png').default?.src;
const jeansJpg = require('../../../../../assets/boyfriend-jeans.jpg').default?.src;
const peoplePng = require('../../../../../assets/gap-people-image.png').default?.src;
const manPng = require('../../../../../assets/man-on-casual-clothes.png').default?.src;
const yogaPng = require('../../../../../assets/person-doing-yoga.png').default?.src;
const posingPng = require('../../../../../assets/person-posing-or-fighting.png').default?.src;
const man2Png = require('../../../../../assets/man-walking.png').default?.src;
const womanPng = require('../../../../../assets/woman-cheering.png').default?.src;

export const MockVisualNavigationIntroCard: VisualNavigationIntroCard = {
  backgroundImage: [
    {
      svgPath: background2Png,
      altText: 'Background',
      variations: [
        {
          variation: 'desktop',
        },
        {
          variation: 'mobile',
        },
      ],
      fliph: false,
      flipv: false,
    },
  ],
  text: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--headline-7">Intro Headline</span></p><p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-2">Lorem ipsum dolor sit amet consectetur adipiscing elit</span></p>',
  cta: {
    label: 'CTA label',
    value: 'www.gap.com',
  },
  bannerLink: {
    display: 'Banner Title',
    url: 'www.c.com',
  },
  image: [
    {
      svgPath: background2Png,
      altText: 'Background',
      variations: [
        {
          variation: 'desktop',
        },
        {
          variation: 'mobile',
        },
      ],
      fliph: false,
      flipv: false,
      enableChroma: false,
      chromaQuality: 80,
    },
  ],
  introCardPlacement: 'beginning',
  showHideBasedOnScreenSize: 'alwaysShow',
};

export const MockVisualNavigationIntroCard1: VisualNavigationIntroCard = {
  backgroundImage: [
    {
      svgPath: background2Png,
      altText: 'Background',

      variations: [
        {
          variation: 'desktop',
        },
        {
          variation: 'mobile',
        },
      ],
      fliph: false,
      flipv: false,
    },
  ],
  text: '<p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--headline-7">Intro Headline</span></p><p class="amp-cms--p" style="text-align:center;"><span class="amp-cms--body-2">Lorem ipsum dolor sit amet consectetur adipiscing elit</span></p>',
  image: [
    {
      svgPath: background2Png,
      altText: 'Background',
      variations: [
        {
          variation: 'desktop',
        },
        {
          variation: 'mobile',
        },
      ],
      fliph: false,
      flipv: false,
      enableChroma: false,
      chromaQuality: 80,
    },
  ],
  introCardPlacement: 'end',
  showHideBasedOnScreenSize: 'hideOnMobile',
};

export const visualNavigationSizeToggleTabbedCarouselData: VisualNavigationSizeToggleCarouselContentType = {
  _meta: {
    name: 'Visual Navigation Size Toggle Carousel',
    schema: 'https://cms.gap.com/schema/content/v1/visual-navigation-size-toggle-carousel.json',
    deliveryId: 'f7759bc7-0849-4f5c-bf17-4ae8435a30a9',
  },
  headline:
    '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--headline-6">Lorem ipsum dolor</span><span class="amp-cms--headline-5"> </span><span class="amp-cms--body-3">sit amet consectetur adipiscing elit sed do eiusmod.</span></p>',
  numberCardsPerSlider: 3,
  ctaDropdownList: [
    {
      ctaDropdown: [
        {
          label: 'CTA',
          value: '#',
        },
      ],
      label: 'CTA Dropdown',
    },
  ],
  toggles: [
    {
      introCards: [MockVisualNavigationIntroCard],
      images: [
        {
          svgPath: jeansJpg,
          altText: 'Jeans',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
        },
        {
          svgPath: manPng,
          altText: 'Man',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
        },
        {
          svgPath: womanPng,
          altText: 'Woman',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
        },
        {
          svgPath: man2Png,
          altText: 'Another Man',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
        },
        {
          svgPath: posingPng,
          altText: 'Posing',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
        },
        {
          svgPath: peoplePng,
          altText: 'People',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
        },
        {
          svgPath: yogaPng,
          altText: 'Yoga',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
        },
      ],
      categoryCards: [
        {
          url: {
            value: 'www.A.com',
          },
          heading: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Category 1</span></p>',
        },
        {
          url: {
            value: 'www.B.com',
          },
          heading: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Category 2</span></p>',
        },
        {
          url: {
            value: 'www.C.com',
          },
          heading: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Category 3</span></p>',
        },
        {
          url: {
            value: 'www.gap.com',
          },
          heading: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Category 4</span></p>',
        },
        {
          url: {
            value: 'www.gap.com',
          },
          heading: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Category 5</span></p>',
        },
        {
          url: {
            value: 'www.gap.com',
          },
          heading: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Category 6</span></p>',
        },
        {
          url: {
            value: 'www.gap.com',
          },
          heading: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Category 7</span></p>',
        },
      ],
      title: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Toggle 1</span></p>',
    },
    {
      introCards: [MockVisualNavigationIntroCard1],
      images: [
        {
          svgPath: manPng,
          altText: 'Man',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
        {
          svgPath: manPng,
          altText: 'Man',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
        {
          svgPath: manPng,
          altText: 'Man',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
        {
          svgPath: manPng,
          altText: 'Man',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
          fliph: false,
          flipv: false,
          enableChroma: false,
          chromaQuality: 80,
        },
        {
          svgPath: manPng,
          altText: 'Man',
          variations: [
            {
              variation: 'desktop',
              crop: {
                x: 1330.56,
                y: 30.24,
                width: 1683.36,
                height: 2993.76,
                unit: 'px',
              },
            },
            {
              variation: 'mobile',
            },
          ],
        },
        {
          svgPath: manPng,
          altText: 'Man',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
        },
        {
          svgPath: manPng,
          altText: 'Man',
          variations: [
            {
              variation: 'desktop',
              crop: {
                x: 599.2600000000003,
                y: -1.7221441947565543,
                width: 729.3625,
                height: 1051,
                unit: 'px',
              },
            },
            {
              variation: 'mobile',
            },
          ],
        },
      ],
      categoryCards: [
        {
          url: {
            value: 'www.gap.com',
          },
          heading: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">T2 Category 1</span></p>',
        },
        {
          url: {
            value: 'www.gap.com',
          },
          heading: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">T2 Category 2</span></p>',
        },
        {
          url: {
            value: 'www.gap.com',
          },
          heading: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">T2 Category 3</span></p>',
        },
        {
          url: {
            value: 'www.gap.com',
          },
          heading: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">T2 Category 4</span></p>',
        },
        {
          url: {
            value: 'www.gap.com',
          },
          heading: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">T2 Category 5</span></p>',
        },
        {
          url: {
            value: 'www.gap.com',
          },
          heading: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">T2 Category 6</span></p>',
        },
        {
          url: {
            value: 'www.gap.com',
          },
          heading: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">T2 Category 7</span></p>',
        },
      ],
      title: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Toggle 2</span></p>',
    },

    {
      images: [
        {
          svgPath: manPng,
          altText: 'Man',
          variations: [
            {
              variation: 'desktop',
              crop: {
                x: 1330.56,
                y: 30.24,
                width: 1683.36,
                height: 2993.76,
                unit: 'px',
              },
            },
            {
              variation: 'mobile',
            },
          ],
        },
        {
          svgPath: manPng,
          altText: 'Man',
          variations: [
            {
              variation: 'desktop',
              crop: {
                x: 599.2600000000003,
                y: -1.7221441947565543,
                width: 729.3625,
                height: 1051,
                unit: 'px',
              },
            },
            {
              variation: 'mobile',
            },
          ],
        },
        {
          svgPath: manPng,
          altText: 'Man',
          variations: [
            {
              variation: 'desktop',
              crop: {
                x: 259.335,
                y: 1.2535112359550562,
                width: 364.905,
                height: 610.7464887640449,
                unit: 'px',
              },
            },
            {
              variation: 'mobile',
            },
          ],
        },
        {
          svgPath: manPng,
          altText: 'Man',
          variations: [
            {
              variation: 'desktop',
              crop: {
                x: 1330.56,
                y: 30.24,
                width: 1683.36,
                height: 2993.76,
                unit: 'px',
              },
            },
            {
              variation: 'mobile',
            },
          ],
        },
        {
          svgPath: posingPng,
          altText: 'Posing',
          variations: [
            {
              variation: 'desktop',
              crop: {
                x: 638.4,
                y: 2.1720217136150235,
                width: 648,
                height: 1018.827978286385,
                unit: 'px',
              },
            },
            {
              variation: 'mobile',
            },
          ],
        },
        {
          svgPath: peoplePng,
          altText: 'People',
          variations: [
            {
              variation: 'desktop',
            },
            {
              variation: 'mobile',
            },
          ],
        },
        {
          svgPath: yogaPng,
          altText: 'Yoga',
          variations: [
            {
              variation: 'desktop',
              crop: {
                x: 259.335,
                y: 1.2535112359550562,
                width: 364.905,
                height: 610.7464887640449,
                unit: 'px',
              },
            },
            {
              variation: 'mobile',
            },
          ],
        },
      ],
      categoryCards: [
        {
          url: {
            value: 'www.gap.com',
          },
          heading: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">T3 Category 1</span></p>',
        },
        {
          url: {
            value: 'www.gap.com',
          },
          heading: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">T3 Category 2</span></p>',
        },
        {
          url: {
            value: 'www.gap.com',
          },
          heading: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">T3 Category 3</span></p>',
        },
        {
          url: {
            value: 'www.gap.com',
          },
          heading: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">T3 Category 4</span></p>',
        },
        {
          url: {
            value: 'www.gap.com',
          },
          heading: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">T3 Category 5</span></p>',
        },
        {
          url: {
            value: 'www.gap.com',
          },
          heading: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">T2 Category 6</span></p>',
        },
        {
          url: {
            value: 'www.gap.com',
          },
          heading: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">T2 Category 7</span></p>',
        },
      ],
      title: '<p class="amp-cms--p" style="text-align:left;"><span class="amp-cms--body-1">Toggle 3</span></p>',
    },
  ],
  categoryCards: [
    {
      url: {
        value: '#style=123456,2312',
      },
      heading: '<span class="amp-cms--body-2 amp-cms--avenir-next-regular">Category1 Heading</span>',
      description: '<span class="amp-cms--body-2 amp-cms--avenir-next-regular">Category1 Description</span>',
      hoverOptions: [
        {
          zoom: false,
          color: '#bad8ee',
          transparency: 50,
        },
      ],
    },
    {
      url: {
        value: '#style=090909',
      },
      description: '<span class="amp-cms--body-2 amp-cms--avenir-next-regular">Category2 Description</span>',
      heading: '<span class="amp-cms--body-2 amp-cms--avenir-next-regular">Category2 Heading</span>',
      hoverOptions: [
        {
          zoom: false,
          color: '#ecd4ba',
          transparency: 50,
        },
      ],
    },
    {
      url: {
        value: 'www.google.com',
      },
      heading: '<span class="amp-cms--body-2 amp-cms--avenir-next-regular">Category3 Heading</span>',
      description: '<span class="amp-cms--body-2 amp-cms--avenir-next-regular">Category3 Description</span>',
      hoverOptions: [
        {
          zoom: false,
          color: '#e3ecf2',
          transparency: 50,
        },
      ],
    },
    {
      url: {
        value: 'https://google.com/search=testing-url-helper',
      },
      heading: '<span class="amp-cms--body-2 amp-cms--avenir-next-regular">Category4 Heading</span>',
      description: '<span class="amp-cms--body-2 amp-cms--avenir-next-regular">Category4 Description</span>',
      hoverOptions: [
        {
          zoom: false,
          image: [
            {
              svgPath: background2Png,
              altText: 'View',
              variations: [
                {
                  variation: 'desktop',
                },
                {
                  variation: 'mobile',
                },
              ],
              fliph: false,
              flipv: false,
            },
          ],
        },
      ],
    },
    {
      url: {
        value: 'https://google.com',
      },
      heading: '<span class="amp-cms--body-2 amp-cms--avenir-next-regular">Category5 Heading</span>',
      description: '<span class="amp-cms--body-2 amp-cms--avenir-next-regular">Category5 Description</span>',
      hoverOptions: [
        {
          zoom: false,
          color: '#bad8ee',
          transparency: 50,
        },
      ],
    },
    {
      url: {
        value: 'https://google.com',
      },
      heading: '<span class="amp-cms--body-2 amp-cms--avenir-next-regular">Category6 Heading</span>',
      description: '<span class="amp-cms--body-2 amp-cms--avenir-next-regular">Category6 Description</span>',
      hoverOptions: [
        {
          zoom: false,
          color: '#ffcc00',
          transparency: 50,
        },
      ],
    },
    {
      heading: '<span class="amp-cms--body-2 amp-cms--avenir-next-regular">Category7 Heading</span>',
      description: '<span class="amp-cms--body-2 amp-cms--avenir-next-regular">Category7 Description</span>',
      url: {
        value: 'https://google.com',
      },
      hoverOptions: [
        {
          zoom: true,
        },
      ],
    },
  ],
  webAppearance: {
    chevronColor: 'light',
    showHideBasedOnScreenSize: 'alwaysShow',
    imageOrIconPlacement: 'above',
    desktopImageOrIconSize: '24px',
    mobileImageOrIconSize: '14px',
  },
};

export const visualNavigationSizeToggleTabbedCarouselDataGAP: VisualNavigationSizeToggleCarouselContentType = {
  ...visualNavigationSizeToggleTabbedCarouselData,
  headline: 'Lorem ipsum dolor sit amet consectetur adipiscing elit sed do eiusmod tempor.',
  toggles: visualNavigationSizeToggleTabbedCarouselData.toggles.map(toggle => ({
    ...toggle,
    categoryCards: toggle.categoryCards.map((card, index) => ({
      ...card,
      heading: `Category - ${index + 1}`,
    })),
  })),
};
