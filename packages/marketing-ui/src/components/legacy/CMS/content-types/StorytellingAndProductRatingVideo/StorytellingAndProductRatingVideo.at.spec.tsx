/**
 * @deprecated StorytellingAndProductRatingVideo tests are deprecated and will be removed in a future release.
 * Please migrate to the recommended alternative component if available.
 */

// @ts-nocheck
import React from 'react';
import { render, act } from 'test-utils';
import { SMALL, LARGE, Size } from '@ecom-next/core/breakpoint-provider';
import { storytellingAndProductRatingVideoData } from './__fixtures__/test-data';
import AthletaStorytellingAndProductRating from '../StorytellingAndProductRating/StorytellingAndProductRating.at';

// to mock the non-deterministic IDs generated in @core-ui/core/review-ratings
jest.mock('random-uuid-v4', () => {
  let value = 0;
  return () => {
    value += 1;
    return value;
  };
});

describe('Athelta StorytellingAndProductRatingVideo', () => {
  [SMALL, LARGE].forEach(size => {
    it(`should render on ${size} viewport`, () => {
      const { container } = render(<AthletaStorytellingAndProductRating {...storytellingAndProductRatingVideoData} />, { breakpoint: size as Size });
      expect(container).toMatchSnapshot();
    });
  });
});
