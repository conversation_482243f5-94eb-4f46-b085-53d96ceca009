/**
 * @deprecated StorytellingAndProductRatingVideo is deprecated and will be removed in a future release.
 * Please migrate to a recommended alternative component if available.
 */

// @ts-nocheck
'use client';
import { AmplienceConfig } from '../../global/types';
import { StorytellingAndProductRatingSchemas } from '../StorytellingAndProductRating';

export const config: AmplienceConfig<StorytellingAndProductRatingSchemas> = {
  component: 'StorytellingAndProductRatingVideo',
  schemas: ['https://cms.gap.com/schema/content/v1/storytelling-product-rating-video.json'],
};

export default config;
