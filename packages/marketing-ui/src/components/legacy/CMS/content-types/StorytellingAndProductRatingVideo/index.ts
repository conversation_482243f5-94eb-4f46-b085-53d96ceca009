/**
 * @deprecated StorytellingAndProductRatingVideo is deprecated and will be removed in a future release.
 * Please migrate to a recommended alternative component if available.
 */

// @ts-nocheck
'use client';
import at from '../StorytellingAndProductRating/StorytellingAndProductRating.at';
import { StorytellingAndProductRatingContentType } from '../StorytellingAndProductRating';
import withBrandComponent from '../withBrandComponent';

// Deprecation runtime warning
if (process.env.NODE_ENV !== 'production') {
  // eslint-disable-next-line no-console
  console.warn(
    'StorytellingAndProductRatingVideo is deprecated and will be removed in a future release. Please migrate to a recommended alternative component if available.'
  );
}

export default withBrandComponent<StorytellingAndProductRatingContentType>({
  at,
});
