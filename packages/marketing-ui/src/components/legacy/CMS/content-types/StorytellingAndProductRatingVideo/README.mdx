> **⚠️ DEPRECATED:**  
> `StorytellingAndProductRatingVideo` is deprecated and will be removed in a future release.  
> Please migrate to a recommended alternative component if available.

# StorytellingAndProductRatingVideo (CMS Configurable)

What is `StorytellingAndProductRatingVideo`?

- It is a wrapper for `StorytellingAndProductRating` that displays a video instead of an image in the "hero" section of the component. All functionality is the same, except that there is no rich text handle under the video like there is for the hero image.

`StorytellingAndProductRatingVideo` is intended for use on the Athleta Homepage.

## Default Behavior

`StorytellingAndProductRatingVideo` contains a background, Rich Text 1, Rich Text 2, CTA button, Star Rating, Rich Text Quote, Video, Product Card image and Product Cards RTE.

The video is required and comes from Vimeo. There is a fall back image for the video in desktop and mobile. There is an optional video mobile override, if not present the Desktop video will carry over to mobile.

There is an optional mobile background override, if not provided it will use the default background style.

The Rich Text 1 is optional. If present, it should be on top of the Video. The text should scale when the viewport changes size, and there is an optional mobile override.

The Rich Text 2 is optional. If present, it should be under Rich Text 1 and on the left of the Video. The text should scale when the viewport changes size and there is an optional mobile override.

The CTA Button is also optional. If present, it will be under Rich Text 2 and on the left side of the Video in Desktop.

The quote text is optional. If present, the text is bottom aligned to the video. The text should scale when the viewport changes size, and there is an option for mobile override quote text.

The star rating is optional. If present, it will be above Quote Text. The default star color is black. The author will be able to choose star ratings at an interval of 0.25 (4, 4.25, 4.5, 4.75, 5 are all valid options). There is no mobile override for the star rating. Right now, there are two sizes for the star size: large on desktop, and medium on mobile.

The Product Card is optional. If present, on desktop, the card's RTE handle text is right aligned and right justified to the card's image, and top aligned with the Main Image text. The card's image bottom is aligned to the Video. On mobile, it should be placed below the Video.

## Limitations

`StorytellingAndProductRatingVideo` is currently only styled for Athleta.

## Technical Notes

- The styling in the `StorytellingAndProductRatingVideo` package uses [`react-stitch`](https://github.gapinc.com/ecomfrontend/core-ui/blob/main/packages/react-stitch/README.md).

  ### API

You can see the props [here](https://github.gapinc.com/ecomfrontend/marketing-ui/blob/main/src/CMS/components/storytelling-and-product-rating/types.ts)

## Testing the Component in Storybook

- Changes in the Storybook controls JSON should be reflected in the visual example above.
- A Playground story has been added to enable/disable optional elements and change background style.

## Breaking Changes Information

To view information regarding BREAKING CHANGES, please view the [Marketing UI MIGRATION.md file](/src/MIGRATION.md).
