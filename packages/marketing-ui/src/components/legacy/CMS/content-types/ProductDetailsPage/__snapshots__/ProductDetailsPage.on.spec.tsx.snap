// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ProductDetailsPage ON carousel should match snapshot at small breakpoint 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  max-width: 1400px;
  box-sizing: border-box;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-0 .slick-slider ul.slick-dots {
  bottom: 0;
  display: -webkit-box!important;
  display: -webkit-flex!important;
  display: -ms-flexbox!important;
  display: flex!important;
  height: 44px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-0 .slick-slider .slick-dots li.slick-active button:before {
  background-color: #003764;
}

.emotion-0 .slick-slider ul.slick-dots button:before {
  width: 10px;
  height: 10px;
  opacity: 1;
  background-color: #FFFFFF;
}

.emotion-1 {
  background: transparent;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  box-sizing: border-box;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  height: auto;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: auto;
}

.emotion-2 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
}

.emotion-3 {
  height: auto;
  max-width: 100%;
}

.emotion-4 {
  min-height: 21.333333333333336vw;
  max-height: 21.333333333333336vw;
}

.emotion-5 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-5 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-5 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-5 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-5 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 37.333333333333336px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-5 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-5 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-5 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 41.06666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 41.06666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 41.06666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 41.06666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 52.266666666666666px));
  line-height: 1.5;
  letter-spacing: min(0.08960000000000001vw, 1.2544000000000002px);
  font-weight: 500;
}

.emotion-5 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 44.8px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 1.0752px);
  font-weight: 500;
}

.emotion-5 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 44.8px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 1.0752px);
}

.emotion-5 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 37.333333333333336px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 37.333333333333336px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-5 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.133333333333333vw, 85.86666666666667px));
  line-height: 1;
  letter-spacing: min(0.24533333333333335vw, 3.4346666666666668px);
  font-weight: 500;
}

.emotion-5 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.066666666666666vw, 70.93333333333334px));
  line-height: 1;
  letter-spacing: min(0.20266666666666666vw, 2.8373333333333335px);
  font-weight: 500;
}

.emotion-5 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 52.266666666666666px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.14933333333333335vw, 2.090666666666667px);
  font-weight: 500;
}

.emotion-5 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(18.133333333333333vw, 253.86666666666667px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-5 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(13.333333333333334vw, 186.66666666666666px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-5 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(11.466666666666667vw, 160.53333333333333px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-5 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.6vw, 134.4px));
  line-height: 1;
  letter-spacing: min(0.19199999999999998vw, 2.6879999999999997px);
  font-weight: 700;
}

.emotion-5 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.466666666666668vw, 104.53333333333333px));
  line-height: 1;
  letter-spacing: min(0.2986666666666667vw, 4.181333333333334px);
  font-weight: 700;
}

.emotion-5 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.4vw, 89.6px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.256vw, 3.584px);
  font-weight: 700;
}

.emotion-5 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.333333333333334vw, 74.66666666666667px));
  line-height: 1;
  letter-spacing: min(0.128vw, 1.792px);
  font-weight: 700;
}

.emotion-5 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(13px, min(17.066666666666666vw, 238.93333333333334px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.1199999999999999px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(13px, min(14.399999999999999vw, 201.6px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.1199999999999999px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(13px, min(9.066666666666666vw, 126.93333333333334px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.1199999999999999px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(13px, min(6.4vw, 89.6px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -1.1199999999999999px);
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(3.733333333333334vw, 52.266666666666666px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(3.733333333333334vw, 52.266666666666666px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(3.733333333333334vw, 52.266666666666666px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-5 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 224px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 149.33333333333334px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-5 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(3.733333333333334vw, 52.266666666666666px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-5 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(5.333333333333334vw, 74.66666666666667px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-5 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.8vw, 67.2px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-5 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.266666666666667vw, 59.733333333333334px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-column-gap: 20px;
  column-gap: 20px;
}

.emotion-8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  text-align: center;
  min-width: 10.666666666666668vw;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-9 {
  height: 10.666666666666668vw;
}

.emotion-10 {
  font-size: 2.933333333333333vw;
  padding-top: 1.866666666666667vw;
  line-height: 145%;
  max-width: 20vw;
  font-weight: 500;
  text-transform: uppercase;
  color: #111;
}

.emotion-18 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 700;
  letter-spacing: 1.6px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.25;
  padding: 0;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #003764;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  pointer-events: all;
}

.emotion-18:focus {
  outline: none;
}

.emotion-18>span span {
  height: calc(16px * 0.7);
}

.emotion-18 span span {
  padding-left: 1px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.2rem * 0.7);
}

.emotion-18 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #003764;
}

.emotion-18:hover,
.emotion-18:focus {
  color: #FFFFFF;
  background-color: #003764;
}

.emotion-18:hover span svg,
.emotion-18:focus span svg {
  fill: #FFFFFF;
}

.emotion-18 span:active {
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-18 span:active span svg {
  fill: #FFFFFF;
}

.emotion-19 {
  box-sizing: border-box;
}

.emotion-20 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-20 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-21 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 15px;
  box-sizing: border-box;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  aspect-ratio: 4/3;
  height: auto;
  position: relative;
  width: 100%;
  overflow: clip;
}

.emotion-22 {
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-22 .slick-list {
  overflow: hidden;
}

.emotion-22 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-22 button.slick-next.slick-arrow.slick-next,
.emotion-22 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: undefined;
  height: 44px;
  width: 44px;
  background: #003764;
  opacity: 75%;
}

.emotion-22 button.slick-next.slick-arrow.slick-next>span,
.emotion-22 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-22 button.slick-next.slick-arrow.slick-next svg,
.emotion-22 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-22 button.slick-next.slick-arrow.slick-next span svg path,
.emotion-22 button.slick-prev.slick-arrow.slick-prev span svg path {
  fill: #ffffff;
}

.emotion-22 button.slick-next.slick-arrow.slick-next:hover,
.emotion-22 button.slick-prev.slick-arrow.slick-prev:hover {
  opacity: 100%;
}

.emotion-22 button.slick-next.slick-arrow.slick-next:focus,
.emotion-22 button.slick-prev.slick-arrow.slick-prev:focus {
  background: #003764;
}

.emotion-22 button.slick-next.slick-arrow.slick-next:focus-visible,
.emotion-22 button.slick-prev.slick-arrow.slick-prev:focus-visible {
  outline: auto;
}

.emotion-22 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-22 .slick-disabled {
  display: none!important;
}

.emotion-22 .slick-next {
  left: calc(100% - 44px);
}

.emotion-22 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-22 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-22 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-23 {
  position: relative;
}

.emotion-23 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-23 .slick-slider .slick-track,
.emotion-23 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-23 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-23 .slick-list:focus {
  outline: none;
}

.emotion-23 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-23 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-23 .slick-track:before,
.emotion-23 .slick-track:after {
  display: table;
  content: "";
}

.emotion-23 .slick-track:after {
  clear: both;
}

.emotion-23 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-23 .slick-slide img {
  display: block;
}

.emotion-23 .slick-slide.slick-loading img {
  display: none;
}

.emotion-23 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-23 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-23 .slick-initialized .slick-slide,
.emotion-23 .slick-vertical .slick-slide {
  display: block;
}

.emotion-23 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-23 .slick-loading .slick-track,
.emotion-23 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-23 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-23 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-23 .slick-prev,
.emotion-23 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-23 .slick-prev:hover,
.emotion-23 .slick-next:hover,
.emotion-23 .slick-prev:focus,
.emotion-23 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-23 .slick-prev.slick-disabled,
.emotion-23 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-23 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-23 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-23 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-23 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-23 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-23 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-23 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-23 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-23 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-23 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-23 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-23 .slick-dots li button:hover,
.emotion-23 .slick-dots li button:focus {
  outline: none;
}

.emotion-23 .slick-dots li button:hover:before,
.emotion-23 .slick-dots li button:focus:before,
.emotion-23 .slick-dots li button:hover:before,
.emotion-23 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-23 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-25 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 4/3;
}

.emotion-26 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-27 {
  width: 100%;
  aspect-ratio: 4/3;
  object-fit: cover;
}

.emotion-28 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-29:focus-visible {
  outline-offset: -5px;
  outline: auto;
}

.emotion-29:focus-visible video {
  outline-offset: -5px;
  outline: auto;
  z-index: -1;
}

.emotion-30 {
  width: 100%;
  background: none;
  height: 100%;
}

.emotion-33 {
  width: 100%;
  height: 100%;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <article
        class="emotion-0"
      >
        <div
          style="background-color: rgb(255, 255, 0);"
        >
          <div
            class="emotion-1"
            height="0"
            width="0"
          >
            <div
              style="display: flex; flex-direction: column; gap: 16px; padding: 24px; height: 100%; justify-content: flex-end; box-sizing: border-box;"
            >
              <div
                class="emotion-2"
              >
                <div
                  class="emotion-3"
                  data-testid="advance-image-container-test-id"
                >
                  <img
                    alt="Mobile Icon"
                    class="emotion-4"
                    maxminheight="21.333333333333336vw"
                    src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_bag-icon?fmt=auto"
                  />
                </div>
              </div>
              <div
                style="text-align: left;"
              >
                <div
                  class="emotion-5"
                >
                  <div>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-2"
                      >
                        Mobile Introducing
                      </span>
                    </p>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--headline-5"
                      >
                        THE ULTIMATE II TIGHT
                      </span>
                    </p>
                    <p
                      class="amp-cms--p"
                    >
                      <span
                        class="amp-cms--body-3"
                      >
                        Our #1 sweat ready style just got a major upgrade. Lorem ipsum dolor sit amet.
                      </span>
                    </p>
                  </div>
                </div>
              </div>
              <div
                class="emotion-2"
              >
                <div
                  class="emotion-7"
                >
                  <div
                    class="emotion-8"
                  >
                    <img
                      alt=""
                      class="emotion-9"
                      src="https://athleta.a.bigcontent.io/v1/static/BREATHABLE_dark"
                    />
                    <span
                      class="emotion-10"
                    >
                      Breathable
                    </span>
                  </div>
                  <div
                    class="emotion-8"
                  >
                    <img
                      alt=""
                      class="emotion-9"
                      src="https://athleta.a.bigcontent.io/v1/static/WRINKLE-RESISTANT_dark"
                    />
                    <span
                      class="emotion-10"
                    >
                      Wrinkle-Resistant
                    </span>
                  </div>
                  <div
                    class="emotion-8"
                  >
                    <img
                      alt=""
                      class="emotion-9"
                      src="https://athleta.a.bigcontent.io/v1/static/ALL-AROUND-STRETCH_dark"
                    />
                    <span
                      class="emotion-10"
                    >
                      All-Around Stretch
                    </span>
                  </div>
                </div>
              </div>
              <div
                class="emotion-2"
              >
                <a
                  class="emotion-18"
                  color="dark"
                  href="/VALUE"
                >
                  <span
                    class="emotion-19"
                  >
                    Shop The Ultimate Collection
                    <span
                      aria-hidden="true"
                      class="emotion-20"
                    />
                  </span>
                </a>
              </div>
            </div>
          </div>
        </div>
        <section
          class="emotion-21"
        >
          <div
            style="width: 100%;"
          >
            <nav
              class="emotion-22"
            >
              <div
                class="emotion-23"
              >
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <button
                    aria-label="Previous"
                    class="slick-prev slick-arrow slick-prev slick-disabled"
                    data-role="none"
                    disabled=""
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-20"
                    >
                      <svg
                        viewBox="0 0 13.29 8.07"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                          fill="#003764"
                        />
                      </svg>
                    </span>
                  </button>
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            style="width: 100%; display: inline-block;"
                            tabindex="-1"
                          >
                            <div
                              class="emotion-25"
                              height="3"
                              width="4"
                            >
                              <div
                                class="emotion-26"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt=""
                                  class="emotion-27"
                                  src="https://1d9xxafvh577y12766tt2karod.staging.bigcontent.io/i/athleta/100_540x960_00?fmt=webp"
                                />
                              </div>
                              <div
                                class="emotion-28"
                              >
                                <a
                                  aria-label="frame 1 test banner"
                                  class="emotion-29"
                                  data-testid="conditional-link"
                                  href="#linkwrapper1"
                                  style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                  target="_self"
                                >
                                  <div
                                    class="emotion-30"
                                    data-testid="gradient-wrapper"
                                  />
                                </a>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            style="width: 100%; display: inline-block;"
                            tabindex="-1"
                          >
                            <div
                              class="emotion-25"
                              height="3"
                              width="4"
                            >
                              <div
                                class="emotion-26"
                                data-testid="product-card-image"
                              >
                                <canvas
                                  aria-label=""
                                  class="emotion-33"
                                  role="img"
                                />
                              </div>
                              <div
                                class="emotion-28"
                              >
                                <a
                                  aria-label="frame 2 test banner"
                                  class="emotion-29"
                                  data-testid="conditional-link"
                                  href="#linkwrapper2"
                                  style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                  target="_self"
                                >
                                  <div
                                    class="emotion-30"
                                    data-testid="gradient-wrapper"
                                  />
                                </a>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            style="width: 100%; display: inline-block;"
                            tabindex="-1"
                          >
                            <div
                              class="emotion-25"
                              height="3"
                              width="4"
                            >
                              <div
                                class="emotion-26"
                                data-testid="product-card-image"
                              >
                                <canvas
                                  aria-label=""
                                  class="emotion-33"
                                  role="img"
                                />
                              </div>
                              <div
                                class="emotion-28"
                              >
                                <a
                                  aria-label="frame 3 test banner"
                                  class="emotion-29"
                                  data-testid="conditional-link"
                                  href="#linkwrapper3"
                                  style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                  target="_self"
                                >
                                  <div
                                    class="emotion-30"
                                    data-testid="gradient-wrapper"
                                  />
                                </a>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    aria-label="Next"
                    class="slick-next slick-arrow slick-next"
                    data-role="none"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-20"
                    >
                      <svg
                        viewBox="0 0 13.29 8.07"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                          fill="#003764"
                        />
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </nav>
          </div>
        </section>
      </article>
    </div>
  </div>
</DocumentFragment>
`;

exports[`ProductDetailsPage ON carousel with unique links per frame in bannerLink and linkWrapper format should match snapshot at large breakpoint 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  max-width: 1400px;
  box-sizing: border-box;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  aspect-ratio: 4/1;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 20px;
  box-sizing: border-box;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  aspect-ratio: 4/1;
  height: 100%;
  position: relative;
  width: 100%;
  overflow: clip;
}

.emotion-2 {
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-2 .slick-list {
  overflow: hidden;
}

.emotion-2 .slick-track {
  width: -webkit-max-content!important;
  width: -moz-max-content!important;
  width: max-content!important;
}

.emotion-2 button.slick-next.slick-arrow.slick-next,
.emotion-2 button.slick-prev.slick-arrow.slick-prev {
  z-index: 2;
  top: undefined;
  height: 44px;
  width: 44px;
  background: #003764;
  opacity: 75%;
}

.emotion-2 button.slick-next.slick-arrow.slick-next>span,
.emotion-2 button.slick-prev.slick-arrow.slick-prev>span {
  margin: auto;
}

.emotion-2 button.slick-next.slick-arrow.slick-next svg,
.emotion-2 button.slick-prev.slick-arrow.slick-prev svg {
  margin: auto;
  height: 25px;
  width: 14px;
}

.emotion-2 button.slick-next.slick-arrow.slick-next span svg path,
.emotion-2 button.slick-prev.slick-arrow.slick-prev span svg path {
  fill: #ffffff;
}

.emotion-2 button.slick-next.slick-arrow.slick-next:hover,
.emotion-2 button.slick-prev.slick-arrow.slick-prev:hover {
  opacity: 100%;
}

.emotion-2 button.slick-next.slick-arrow.slick-next:focus,
.emotion-2 button.slick-prev.slick-arrow.slick-prev:focus {
  background: #003764;
}

.emotion-2 button.slick-next.slick-arrow.slick-next:focus-visible,
.emotion-2 button.slick-prev.slick-arrow.slick-prev:focus-visible {
  outline: auto;
}

.emotion-2 .slick-slide {
  margin: 0;
  width: auto;
}

.emotion-2 .slick-disabled {
  display: none!important;
}

.emotion-2 .slick-next {
  left: calc(100% - 44px);
}

.emotion-2 .slick-slide:first-of-type a>div:nth-of-type(2)>div>div:nth-of-type(2) {
  border-left: none;
}

.emotion-2 .slick-slide>div:first-of-type {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-2 .slick-slide>div:first-of-type>div:first-of-type {
  width: 100%;
}

.emotion-3 {
  position: relative;
}

.emotion-3 .slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  scroll-behavior: smooth;
}

.emotion-3 .slick-slider .slick-track,
.emotion-3 .slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.emotion-3 .slick-list {
  position: relative;
  display: block;
  overflow-x: hidden;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.emotion-3 .slick-list:focus {
  outline: none;
}

.emotion-3 .slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.emotion-3 .slick-track {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.emotion-3 .slick-track:before,
.emotion-3 .slick-track:after {
  display: table;
  content: "";
}

.emotion-3 .slick-track:after {
  clear: both;
}

.emotion-3 .slick-slide {
  display: none;
  float: left;
  height: 100%;
  min-height: 1px;
}

.emotion-3 .slick-slide img {
  display: block;
}

.emotion-3 .slick-slide.slick-loading img {
  display: none;
}

.emotion-3 .slick-slide.dragging img {
  pointer-events: none;
}

.emotion-3 [dir="rtl"] .slick-slide {
  float: right;
}

.emotion-3 .slick-initialized .slick-slide,
.emotion-3 .slick-vertical .slick-slide {
  display: block;
}

.emotion-3 .slick-vertical .slick-slide {
  border: 1px solid transparent;
  height: auto;
}

.emotion-3 .slick-loading .slick-track,
.emotion-3 .slick-loading .slick-slide {
  visibility: hidden;
}

.emotion-3 .slick-loading .slick-list {
  background: #fff url("./ajax-loader.gif") center center no-repeat;
}

.emotion-3 .slick-arrow.slick-hidden {
  display: none;
}

.emotion-3 .slick-prev,
.emotion-3 .slick-next {
  font-size: 0;
  line-height: 0;
  position: absolute;
  top: 50%;
  display: block;
  padding: 0;
  cursor: pointer;
  color: transparent;
  border: none;
  outline: none;
  background: transparent;
}

.emotion-3 .slick-prev:hover,
.emotion-3 .slick-next:hover,
.emotion-3 .slick-prev:focus,
.emotion-3 .slick-next:focus {
  color: transparent;
  outline: none;
  background: transparent;
  opacity: 1;
}

.emotion-3 .slick-prev.slick-disabled,
.emotion-3 .slick-next.slick-disabled {
  opacity: 0.25;
}

.emotion-3 .slick-prev {
  left: -0;
  -webkit-transform: translate(0, -50%) rotate(90deg);
  -moz-transform: translate(0, -50%) rotate(90deg);
  -ms-transform: translate(0, -50%) rotate(90deg);
  transform: translate(0, -50%) rotate(90deg);
}

.emotion-3 .slick-prev span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 [dir="rtl"] .slick-prev {
  right: -0;
  left: auto;
}

.emotion-3 .slick-next {
  right: -0;
  -webkit-transform: translate(0, -50%) rotate(-90deg);
  -moz-transform: translate(0, -50%) rotate(-90deg);
  -ms-transform: translate(0, -50%) rotate(-90deg);
  transform: translate(0, -50%) rotate(-90deg);
}

.emotion-3 .slick-next span {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
}

.emotion-3 [dir="rtl"] .slick-next {
  right: auto;
  left: -0;
}

.emotion-3 .slick-dotted.slick-slider {
  margin-bottom: 30px;
}

.emotion-3 .slick-dots {
  position: absolute;
  bottom: -25px;
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
  list-style: none;
  text-align: center;
}

.emotion-3 .slick-dots li {
  position: relative;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 20px;
  height: 20px;
  margin: 0 5px;
  padding: 0;
  cursor: pointer;
}

.emotion-3 .slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: black;
}

.emotion-3 .slick-dots li button {
  font-size: 0;
  line-height: 0;
  display: block;
  padding: 5px;
  cursor: pointer;
  color: transparent;
  border: 0;
  outline: none;
  background: transparent;
}

.emotion-3 .slick-dots li button:hover,
.emotion-3 .slick-dots li button:focus {
  outline: none;
}

.emotion-3 .slick-dots li button:hover:before,
.emotion-3 .slick-dots li button:focus:before,
.emotion-3 .slick-dots li button:hover:before,
.emotion-3 .slick-dots li button:focus:before {
  opacity: 1;
}

.emotion-3 .slick-dots li button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  opacity: 0.25;
  background-color: black;
  border-radius: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-4 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-4 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

.emotion-5 {
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  aspect-ratio: 4/1;
}

.emotion-6 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-7 {
  width: 100%;
  aspect-ratio: 4/1;
  object-fit: cover;
}

.emotion-8 {
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.emotion-9:focus-visible {
  outline-offset: -5px;
  outline: auto;
}

.emotion-9:focus-visible video {
  outline-offset: -5px;
  outline: auto;
  z-index: -1;
}

.emotion-10 {
  width: 100%;
  background: none;
  height: 100%;
}

.emotion-12 {
  height: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: 0px;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  text-align: center;
}

.emotion-14 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  height: 100%;
  padding: 16px 120px;
  gap: 16px;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: flex-end;
  justify-content: flex-end;
  box-sizing: border-box;
}

.emotion-16 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-17 {
  height: auto;
  max-width: 100%;
}

.emotion-18 {
  min-height: min(
              1.6666666666666667vw, 
              23.333333333333332px
            );
  max-height: min(
              1.6666666666666667vw, 
              23.333333333333332px
            );
}

.emotion-19 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-19 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-19 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-19 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-19 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 9.722222222222221px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-19 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-19 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-19 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 11.666666666666666px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-19 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 11.666666666666666px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-19 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 11.666666666666666px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-19 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 11.666666666666666px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-19 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 17.5px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.7px);
  font-weight: 500;
}

.emotion-19 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.1111111111111112vw, 15.555555555555555px));
  line-height: 1.5;
  letter-spacing: min(0.044444444444444446vw, 0.6222222222222222px);
  font-weight: 500;
}

.emotion-19 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9722222222222222vw, 13.61111111111111px));
  line-height: 1.5;
  letter-spacing: min(0.03888888888888889vw, 0.5444444444444445px);
}

.emotion-19 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.8333333333333334vw, 11.666666666666666px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-19 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 9.722222222222221px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-19 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.9444444444444444vw, 27.22222222222222px));
  line-height: 1;
  letter-spacing: min(0.07777777777777778vw, 1.088888888888889px);
  font-weight: 500;
}

.emotion-19 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.597222222222222vw, 22.36111111111111px));
  line-height: 1;
  letter-spacing: min(0.0638888888888889vw, 0.8944444444444445px);
  font-weight: 500;
}

.emotion-19 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1805555555555556vw, 16.52777777777778px));
  line-height: 1;
  letter-spacing: min(0.04722222222222222vw, 0.6611111111111112px);
  font-weight: 500;
}

.emotion-19 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8.194444444444445vw, 114.72222222222221px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-19 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.555555555555555vw, 77.77777777777777px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-19 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 70px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-19 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(4.444444444444445vw, 62.22222222222222px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.2444444444444445px);
  font-weight: 700;
}

.emotion-19 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.3333333333333335vw, 46.666666666666664px));
  line-height: 1;
  letter-spacing: min(0.2vw, 2.8px);
  font-weight: 700;
}

.emotion-19 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.7777777777777777vw, 38.888888888888886px));
  line-height: 1.1;
  letter-spacing: min(0.16666666666666666vw, 2.333333333333333px);
  font-weight: 700;
}

.emotion-19 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.2222222222222223vw, 31.11111111111111px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.2444444444444445px);
  font-weight: 700;
}

.emotion-19 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(7.916666666666666vw, 110.83333333333333px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.3888888888888889px);
  font-weight: 400;
}

.emotion-19 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(5.833333333333333vw, 81.66666666666667px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.3888888888888889px);
  font-weight: 400;
}

.emotion-19 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(4.444444444444445vw, 62.22222222222222px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.3888888888888889px);
  font-weight: 400;
}

.emotion-19 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.361111111111111vw, 33.05555555555556px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.3888888888888889px);
  font-weight: 400;
}

.emotion-19 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 15.555555555555555px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-19 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 15.555555555555555px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-19 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 15.555555555555555px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-19 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 97.22222222222221px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-19 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 77.77777777777777px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-19 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 15.555555555555555px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-19 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 23.333333333333332px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-19 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5277777777777777vw, 21.38888888888889px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-19 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 19.444444444444443px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-21 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-column-gap: 24px;
  column-gap: 24px;
}

.emotion-22 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  text-align: center;
  min-width: 3.4722222222222223vw;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-23 {
  height: 3.4722222222222223vw;
  max-height: 48.611111111111114px;
}

.emotion-24 {
  line-height: 150%;
  font-size: max(10px, min(0.8333333333333334vw, 11.666666666666666px));
  padding-top: min(0.5555555555555556vw, 7.777777777777778px);
  max-width: min(5.902777777777778vw, 82.63888888888889px);
  font-weight: 500;
  text-transform: uppercase;
  color: #111;
}

.emotion-33 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 18px;
  font-weight: 700;
  letter-spacing: 2.1599999999999997px;
  min-height: 52px;
  max-height: auto;
  line-height: 1.1111111111111112;
  padding: 14px 22px;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  background-color: #FFFFFF;
  color: #003764;
  border-color: #003764;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  pointer-events: all;
  min-height: auto;
}

.emotion-33:focus {
  outline: none;
}

.emotion-33>span {
  padding: 1px 0;
}

.emotion-33:hover,
.emotion-33:focus {
  color: #FFFFFF;
  background-color: #003764;
  border-color: #003764;
}

.emotion-33:active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #FFFFFF;
  background-color: #000000;
  border-color: #000000;
}

.emotion-36 {
  width: 100%;
  height: 100%;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <article
        class="emotion-0"
      >
        <section
          class="emotion-1"
        >
          <div
            style="width: 100%;"
          >
            <nav
              class="emotion-2"
            >
              <div
                class="emotion-3"
              >
                <div
                  class="slick-slider slick-initialized"
                  dir="ltr"
                >
                  <button
                    aria-label="Previous"
                    class="slick-prev slick-arrow slick-prev slick-disabled"
                    data-role="none"
                    disabled=""
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-4"
                    >
                      <svg
                        viewBox="0 0 13.29 8.07"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                          fill="#003764"
                        />
                      </svg>
                    </span>
                  </button>
                  <div
                    class="slick-list"
                  >
                    <div
                      class="slick-track"
                      style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
                    >
                      <div
                        aria-hidden="false"
                        class="slick-slide slick-active slick-current"
                        data-index="0"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            style="width: 100%; display: inline-block;"
                            tabindex="-1"
                          >
                            <div
                              class="emotion-5"
                              height="1"
                              width="4"
                            >
                              <div
                                class="emotion-6"
                                data-testid="product-card-image"
                              >
                                <img
                                  alt=""
                                  class="emotion-7"
                                  src="https://1d9xxafvh577y12766tt2karod.staging.bigcontent.io/i/athleta/ATG_DP_XL_Testing2?fmt=webp"
                                />
                              </div>
                              <div
                                class="emotion-8"
                              >
                                <a
                                  aria-label="frame 1 banner link"
                                  class="emotion-9"
                                  data-testid="conditional-link"
                                  href="/frame1bannerlink"
                                  style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                  target="_self"
                                />
                                <div
                                  class="emotion-10"
                                  data-testid="gradient-wrapper"
                                >
                                  <a
                                    aria-label="frame 1 banner link"
                                    class="emotion-9"
                                    data-testid="conditional-link"
                                    href="/frame1bannerlink"
                                    style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                    target="_self"
                                  />
                                  <div
                                    class="emotion-12"
                                  >
                                    <a
                                      aria-label="frame 1 banner link"
                                      class="emotion-9"
                                      data-testid="conditional-link"
                                      href="/frame1bannerlink"
                                      style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                      target="_self"
                                    />
                                    <div
                                      class="emotion-14"
                                    >
                                      <a
                                        aria-label="frame 1 banner link"
                                        class="emotion-9"
                                        data-testid="conditional-link"
                                        href="/frame1bannerlink"
                                        style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                        target="_self"
                                      >
                                        <div
                                          class="emotion-16"
                                        >
                                          <div
                                            class="emotion-17"
                                            data-testid="advance-image-container-test-id"
                                          >
                                            <img
                                              alt="Default Icon"
                                              class="emotion-18"
                                              maxminheight="min(
            1.6666666666666667vw, 
            23.333333333333332px
          )"
                                              src="https://1d9xxafvh577y12766tt2karod.staging.bigcontent.io/i/athleta/ATHLETA_logo@2x?fmt=auto"
                                            />
                                          </div>
                                        </div>
                                        <div
                                          style="text-align: center;"
                                        >
                                          <div
                                            class="emotion-19"
                                          >
                                            <div>
                                              <p
                                                class="amp-cms--p"
                                              >
                                                <span
                                                  class="amp-cms--eyebrow-1"
                                                >
                                                  Introducing
                                                </span>
                                              </p>
                                              <p
                                                class="amp-cms--p"
                                              >
                                                <span
                                                  class="amp-cms--headline-3"
                                                >
                                                  THE ULTIMATE II TIGHT
                                                </span>
                                              </p>
                                              <p
                                                class="amp-cms--p"
                                              >
                                                <span
                                                  class="amp-cms--body-1"
                                                >
                                                  Our #1 sweat ready style just got a major upgrade.
                                                </span>
                                              </p>
                                              <hr
                                                aria-hidden="true"
                                                style="display:block;border:0;height:8px;margin:0;background:transparent;"
                                              />
                                            </div>
                                          </div>
                                        </div>
                                        <div
                                          class="emotion-16"
                                        >
                                          <div
                                            class="emotion-21"
                                          >
                                            <div
                                              class="emotion-22"
                                            >
                                              <img
                                                alt=""
                                                class="emotion-23"
                                                src="https://athleta.a.bigcontent.io/v1/static/BREATHABLE_dark"
                                              />
                                              <span
                                                class="emotion-24"
                                              >
                                                Breathable
                                              </span>
                                            </div>
                                            <div
                                              class="emotion-22"
                                            >
                                              <img
                                                alt=""
                                                class="emotion-23"
                                                src="https://athleta.a.bigcontent.io/v1/static/WRINKLE-RESISTANT_dark"
                                              />
                                              <span
                                                class="emotion-24"
                                              >
                                                Wrinkle-Resistant
                                              </span>
                                            </div>
                                            <div
                                              class="emotion-22"
                                            >
                                              <img
                                                alt=""
                                                class="emotion-23"
                                                src="https://athleta.a.bigcontent.io/v1/static/ALL-AROUND-STRETCH_dark"
                                              />
                                              <span
                                                class="emotion-24"
                                              >
                                                All-Around Stretch
                                              </span>
                                            </div>
                                          </div>
                                        </div>
                                      </a>
                                      <div
                                        class="emotion-16"
                                      >
                                        <a
                                          aria-label="frame 1 banner link"
                                          class="emotion-9"
                                          data-testid="conditional-link"
                                          href="/frame1bannerlink"
                                          style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                          target="_self"
                                        />
                                        <a
                                          class="emotion-33"
                                          color="dark"
                                          href="#button1"
                                        >
                                          Frame 1 CTA
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="1"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            style="width: 100%; display: inline-block;"
                            tabindex="-1"
                          >
                            <div
                              class="emotion-5"
                              height="1"
                              width="4"
                            >
                              <div
                                class="emotion-6"
                                data-testid="product-card-image"
                              >
                                <canvas
                                  aria-label=""
                                  class="emotion-36"
                                  role="img"
                                />
                              </div>
                              <div
                                class="emotion-8"
                              >
                                <a
                                  aria-label="frame 2 link wrapper"
                                  class="emotion-9"
                                  data-testid="conditional-link"
                                  href="#frame2linkwrapper2"
                                  style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                  target="_self"
                                />
                                <div
                                  class="emotion-10"
                                  data-testid="gradient-wrapper"
                                >
                                  <a
                                    aria-label="frame 2 link wrapper"
                                    class="emotion-9"
                                    data-testid="conditional-link"
                                    href="#frame2linkwrapper2"
                                    style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                    target="_self"
                                  />
                                  <div
                                    class="emotion-12"
                                  >
                                    <a
                                      aria-label="frame 2 link wrapper"
                                      class="emotion-9"
                                      data-testid="conditional-link"
                                      href="#frame2linkwrapper2"
                                      style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                      target="_self"
                                    />
                                    <div
                                      class="emotion-14"
                                    >
                                      <a
                                        aria-label="frame 2 link wrapper"
                                        class="emotion-9"
                                        data-testid="conditional-link"
                                        href="#frame2linkwrapper2"
                                        style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                        target="_self"
                                      >
                                        <div
                                          class="emotion-16"
                                        >
                                          <div
                                            class="emotion-17"
                                            data-testid="advance-image-container-test-id"
                                          >
                                            <img
                                              alt="Default Icon"
                                              class="emotion-18"
                                              maxminheight="min(
            1.6666666666666667vw, 
            23.333333333333332px
          )"
                                              src="https://1d9xxafvh577y12766tt2karod.staging.bigcontent.io/i/athleta/ATHLETA_logo@2x?fmt=auto"
                                            />
                                          </div>
                                        </div>
                                        <div
                                          style="text-align: center;"
                                        >
                                          <div
                                            class="emotion-19"
                                          >
                                            <div>
                                              <p
                                                class="amp-cms--p"
                                              >
                                                <span
                                                  class="amp-cms--eyebrow-1"
                                                >
                                                  Introducing
                                                </span>
                                              </p>
                                              <p
                                                class="amp-cms--p"
                                              >
                                                <span
                                                  class="amp-cms--headline-3"
                                                >
                                                  THE ULTIMATE II TIGHT
                                                </span>
                                              </p>
                                              <p
                                                class="amp-cms--p"
                                              >
                                                <span
                                                  class="amp-cms--body-1"
                                                >
                                                  Our #1 sweat ready style just got a major upgrade.
                                                </span>
                                              </p>
                                              <hr
                                                aria-hidden="true"
                                                style="display:block;border:0;height:8px;margin:0;background:transparent;"
                                              />
                                            </div>
                                          </div>
                                        </div>
                                        <div
                                          class="emotion-16"
                                        >
                                          <div
                                            class="emotion-21"
                                          >
                                            <div
                                              class="emotion-22"
                                            >
                                              <img
                                                alt=""
                                                class="emotion-23"
                                                src="https://athleta.a.bigcontent.io/v1/static/BREATHABLE_dark"
                                              />
                                              <span
                                                class="emotion-24"
                                              >
                                                Breathable
                                              </span>
                                            </div>
                                            <div
                                              class="emotion-22"
                                            >
                                              <img
                                                alt=""
                                                class="emotion-23"
                                                src="https://athleta.a.bigcontent.io/v1/static/WRINKLE-RESISTANT_dark"
                                              />
                                              <span
                                                class="emotion-24"
                                              >
                                                Wrinkle-Resistant
                                              </span>
                                            </div>
                                            <div
                                              class="emotion-22"
                                            >
                                              <img
                                                alt=""
                                                class="emotion-23"
                                                src="https://athleta.a.bigcontent.io/v1/static/ALL-AROUND-STRETCH_dark"
                                              />
                                              <span
                                                class="emotion-24"
                                              >
                                                All-Around Stretch
                                              </span>
                                            </div>
                                          </div>
                                        </div>
                                      </a>
                                      <div
                                        class="emotion-16"
                                      >
                                        <a
                                          aria-label="frame 2 link wrapper"
                                          class="emotion-9"
                                          data-testid="conditional-link"
                                          href="#frame2linkwrapper2"
                                          style="cursor: pointer; height: 100%; width: 100%; left: 0px; top: 0px; position: absolute;"
                                          target="_self"
                                        />
                                        <a
                                          class="emotion-33"
                                          color="dark"
                                          href="#button2"
                                        >
                                          Frame 2 CTA
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div
                        aria-hidden="true"
                        class="slick-slide"
                        data-index="2"
                        style="outline: none; width: 0px;"
                        tabindex="-1"
                      >
                        <div>
                          <div
                            style="width: 100%; display: inline-block;"
                            tabindex="-1"
                          >
                            <div
                              class="emotion-5"
                              height="1"
                              width="4"
                            >
                              <div
                                class="emotion-6"
                                data-testid="product-card-image"
                              >
                                <canvas
                                  aria-label=""
                                  class="emotion-36"
                                  role="img"
                                />
                              </div>
                              <div
                                class="emotion-8"
                              >
                                <div
                                  class="emotion-10"
                                  data-testid="gradient-wrapper"
                                >
                                  <div
                                    class="emotion-12"
                                  >
                                    <div
                                      class="emotion-14"
                                    >
                                      <div
                                        class="emotion-16"
                                      >
                                        <div
                                          class="emotion-17"
                                          data-testid="advance-image-container-test-id"
                                        >
                                          <img
                                            alt="Default Icon"
                                            class="emotion-18"
                                            maxminheight="min(
            1.6666666666666667vw, 
            23.333333333333332px
          )"
                                            src="https://1d9xxafvh577y12766tt2karod.staging.bigcontent.io/i/athleta/ATHLETA_logo@2x?fmt=auto"
                                          />
                                        </div>
                                      </div>
                                      <div
                                        style="text-align: center;"
                                      >
                                        <div
                                          class="emotion-19"
                                        >
                                          <div>
                                            <p
                                              class="amp-cms--p"
                                            >
                                              <span
                                                class="amp-cms--eyebrow-1"
                                              >
                                                Introducing
                                              </span>
                                            </p>
                                            <p
                                              class="amp-cms--p"
                                            >
                                              <span
                                                class="amp-cms--headline-3"
                                              >
                                                THE ULTIMATE II TIGHT
                                              </span>
                                            </p>
                                            <p
                                              class="amp-cms--p"
                                            >
                                              <span
                                                class="amp-cms--body-1"
                                              >
                                                Our #1 sweat ready style just got a major upgrade.
                                              </span>
                                            </p>
                                            <hr
                                              aria-hidden="true"
                                              style="display:block;border:0;height:8px;margin:0;background:transparent;"
                                            />
                                          </div>
                                        </div>
                                      </div>
                                      <div
                                        class="emotion-16"
                                      >
                                        <div
                                          class="emotion-21"
                                        >
                                          <div
                                            class="emotion-22"
                                          >
                                            <img
                                              alt=""
                                              class="emotion-23"
                                              src="https://athleta.a.bigcontent.io/v1/static/BREATHABLE_dark"
                                            />
                                            <span
                                              class="emotion-24"
                                            >
                                              Breathable
                                            </span>
                                          </div>
                                          <div
                                            class="emotion-22"
                                          >
                                            <img
                                              alt=""
                                              class="emotion-23"
                                              src="https://athleta.a.bigcontent.io/v1/static/WRINKLE-RESISTANT_dark"
                                            />
                                            <span
                                              class="emotion-24"
                                            >
                                              Wrinkle-Resistant
                                            </span>
                                          </div>
                                          <div
                                            class="emotion-22"
                                          >
                                            <img
                                              alt=""
                                              class="emotion-23"
                                              src="https://athleta.a.bigcontent.io/v1/static/ALL-AROUND-STRETCH_dark"
                                            />
                                            <span
                                              class="emotion-24"
                                            >
                                              All-Around Stretch
                                            </span>
                                          </div>
                                        </div>
                                      </div>
                                      <div
                                        class="emotion-16"
                                      >
                                        <a
                                          class="emotion-33"
                                          color="dark"
                                          href="#button3"
                                        >
                                          Frame 3 CTA
                                        </a>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    aria-label="Next"
                    class="slick-next slick-arrow slick-next"
                    data-role="none"
                  >
                    <span
                      aria-hidden="true"
                      class="emotion-4"
                    >
                      <svg
                        viewBox="0 0 13.29 8.07"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M6.13 7.86a.73.73 0 001.04 0l5.92-5.92a.73.73 0 000-1.03l-.7-.7a.73.73 0 00-1.03 0l-4.71 4.7L1.94.2A.73.73 0 00.9.21l-.69.7a.73.73 0 000 1.03z"
                          fill="#003764"
                        />
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            </nav>
          </div>
        </section>
      </article>
    </div>
  </div>
</DocumentFragment>
`;

exports[`ProductDetailsPage ON image should match snapshot with a large breakpoint 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  max-width: 1400px;
  box-sizing: border-box;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  aspect-ratio: 4/1;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 20px;
  box-sizing: border-box;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  aspect-ratio: 4/1;
  height: 100%;
  position: absolute;
  width: 100%;
  overflow: clip;
}

.emotion-2 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-3 {
  width: 100%;
  aspect-ratio: 4/1;
  object-fit: cover;
}

.emotion-4 {
  background: transparent;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  box-sizing: border-box;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  height: 100%;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  pointer-events: none;
  width: 100%;
  aspect-ratio: 4/1;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  text-align: center;
}

.emotion-5 {
  width: 100%;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0) 100%);
}

.emotion-6 {
  pointerevents: auto;
}

.emotion-7 {
  height: auto;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding: 0px;
  -webkit-align-items: flex-end;
  -webkit-box-align: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  text-align: center;
}

.emotion-8 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  height: initial;
  padding: 16px 120px;
  gap: 16px;
}

.emotion-9 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-10 {
  height: auto;
  max-width: 100%;
}

.emotion-11 {
  min-height: min(
              1.6666666666666667vw, 
              23.333333333333332px
            );
  max-height: min(
              1.6666666666666667vw, 
              23.333333333333332px
            );
}

.emotion-12 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-12 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-12 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-12 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-12 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 9.722222222222221px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-12 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-12 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-12 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 11.666666666666666px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 11.666666666666666px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 11.666666666666666px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(0.8333333333333334vw, 11.666666666666666px);
  line-height: 1.3333333333333333;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.25vw, 17.5px));
  line-height: 1.5;
  letter-spacing: min(0.05vw, 0.7px);
  font-weight: 500;
}

.emotion-12 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(1.1111111111111112vw, 15.555555555555555px));
  line-height: 1.5;
  letter-spacing: min(0.044444444444444446vw, 0.6222222222222222px);
  font-weight: 500;
}

.emotion-12 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.9722222222222222vw, 13.61111111111111px));
  line-height: 1.5;
  letter-spacing: min(0.03888888888888889vw, 0.5444444444444445px);
}

.emotion-12 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.8333333333333334vw, 11.666666666666666px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(0.6944444444444444vw, 9.722222222222221px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-12 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.9444444444444444vw, 27.22222222222222px));
  line-height: 1;
  letter-spacing: min(0.07777777777777778vw, 1.088888888888889px);
  font-weight: 500;
}

.emotion-12 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.597222222222222vw, 22.36111111111111px));
  line-height: 1;
  letter-spacing: min(0.0638888888888889vw, 0.8944444444444445px);
  font-weight: 500;
}

.emotion-12 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(1.1805555555555556vw, 16.52777777777778px));
  line-height: 1;
  letter-spacing: min(0.04722222222222222vw, 0.6611111111111112px);
  font-weight: 500;
}

.emotion-12 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(8.194444444444445vw, 114.72222222222221px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-12 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5.555555555555555vw, 77.77777777777777px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-12 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(5vw, 70px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-12 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(4.444444444444445vw, 62.22222222222222px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.2444444444444445px);
  font-weight: 700;
}

.emotion-12 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(3.3333333333333335vw, 46.666666666666664px));
  line-height: 1;
  letter-spacing: min(0.2vw, 2.8px);
  font-weight: 700;
}

.emotion-12 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.7777777777777777vw, 38.888888888888886px));
  line-height: 1.1;
  letter-spacing: min(0.16666666666666666vw, 2.333333333333333px);
  font-weight: 700;
}

.emotion-12 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(24px, min(2.2222222222222223vw, 31.11111111111111px));
  line-height: 1;
  letter-spacing: min(0.08888888888888889vw, 1.2444444444444445px);
  font-weight: 700;
}

.emotion-12 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(14px, min(7.916666666666666vw, 110.83333333333333px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.3888888888888889px);
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(14px, min(5.833333333333333vw, 81.66666666666667px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.3888888888888889px);
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(14px, min(4.444444444444445vw, 62.22222222222222px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.3888888888888889px);
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(14px, min(2.361111111111111vw, 33.05555555555556px));
  line-height: 1;
  letter-spacing: min(-0.027777777777777776vw, -0.3888888888888889px);
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 15.555555555555555px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 15.555555555555555px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 15.555555555555555px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-12 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(6.944444444444445vw, 97.22222222222221px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-12 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(14px, min(5.555555555555555vw, 77.77777777777777px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-12 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(1.1111111111111112vw, 15.555555555555555px);
  line-height: 0.0625;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-12 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.6666666666666667vw, 23.333333333333332px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-12 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.5277777777777777vw, 21.38888888888889px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-12 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(1.3888888888888888vw, 19.444444444444443px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-14 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-column-gap: 24px;
  column-gap: 24px;
}

.emotion-15 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  text-align: center;
  min-width: 3.4722222222222223vw;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-16 {
  height: 3.4722222222222223vw;
  max-height: 48.611111111111114px;
}

.emotion-17 {
  line-height: 150%;
  font-size: max(10px, min(0.8333333333333334vw, 11.666666666666666px));
  padding-top: min(0.5555555555555556vw, 7.777777777777778px);
  max-width: min(5.902777777777778vw, 82.63888888888889px);
  font-weight: 500;
  text-transform: uppercase;
  color: #111;
}

.emotion-25 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 18px;
  font-weight: 700;
  letter-spacing: 1.8px;
  min-height: auto;
  max-height: auto;
  line-height: 1.2222222222222223;
  padding: 0;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #003764;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  pointer-events: all;
  min-height: auto;
}

.emotion-25:focus {
  outline: none;
}

.emotion-25>span span {
  height: calc(18px * 0.7);
}

.emotion-25 span span {
  padding-left: 1px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.5rem * 0.7);
}

.emotion-25 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #003764;
}

.emotion-25:hover,
.emotion-25:focus {
  color: #FFFFFF;
  background-color: #003764;
}

.emotion-25:hover span svg,
.emotion-25:focus span svg {
  fill: #FFFFFF;
}

.emotion-25 span:active {
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-25 span:active span svg {
  fill: #FFFFFF;
}

.emotion-26 {
  box-sizing: border-box;
}

.emotion-27 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-27 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <article
        class="emotion-0"
      >
        <section
          class="emotion-1"
        >
          <div
            class="emotion-2"
            data-testid="product-card-image"
          >
            <img
              alt="image showing a person"
              class="emotion-3"
              src="https://1puomodm9vwdc1sabswsbq9607.staging.bigcontent.io/i/oldnavy/Nav4?fmt=webp"
            />
          </div>
        </section>
        <div
          class="emotion-4"
          height="0"
          width="0"
        >
          <div
            class="emotion-5"
            data-testid="gradient-wrapper"
          >
            <div
              class="emotion-6"
            >
              <div
                class="emotion-7"
              >
                <div
                  class="emotion-8"
                >
                  <div
                    class="emotion-9"
                  >
                    <div
                      class="emotion-10"
                      data-testid="advance-image-container-test-id"
                    >
                      <img
                        alt="Default Icon"
                        class="emotion-11"
                        maxminheight="min(
            1.6666666666666667vw, 
            23.333333333333332px
          )"
                        src="https://1d9xxafvh577y12766tt2karod.staging.bigcontent.io/i/athleta/ATHLETA_logo@2x?fmt=auto"
                      />
                    </div>
                  </div>
                  <div
                    style="text-align: center;"
                  >
                    <div
                      class="emotion-12"
                    >
                      <div>
                        <p
                          class="amp-cms--p"
                        >
                          <span
                            class="amp-cms--eyebrow-1"
                          >
                            Introducing
                          </span>
                        </p>
                        <p
                          class="amp-cms--p"
                        >
                          <span
                            class="amp-cms--headline-3"
                          >
                            THE ULTIMATE II TIGHT
                          </span>
                        </p>
                        <p
                          class="amp-cms--p"
                        >
                          <span
                            class="amp-cms--body-1"
                          >
                            Our #1 sweat ready style just got a major upgrade.
                          </span>
                        </p>
                        <hr
                          aria-hidden="true"
                          style="display:block;border:0;height:8px;margin:0;background:transparent;"
                        />
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-9"
                  >
                    <div
                      class="emotion-14"
                    >
                      <div
                        class="emotion-15"
                      >
                        <img
                          alt=""
                          class="emotion-16"
                          src="https://athleta.a.bigcontent.io/v1/static/BREATHABLE_dark"
                        />
                        <span
                          class="emotion-17"
                        >
                          Breathable
                        </span>
                      </div>
                      <div
                        class="emotion-15"
                      >
                        <img
                          alt=""
                          class="emotion-16"
                          src="https://athleta.a.bigcontent.io/v1/static/WRINKLE-RESISTANT_dark"
                        />
                        <span
                          class="emotion-17"
                        >
                          Wrinkle-Resistant
                        </span>
                      </div>
                      <div
                        class="emotion-15"
                      >
                        <img
                          alt=""
                          class="emotion-16"
                          src="https://athleta.a.bigcontent.io/v1/static/ALL-AROUND-STRETCH_dark"
                        />
                        <span
                          class="emotion-17"
                        >
                          All-Around Stretch
                        </span>
                      </div>
                    </div>
                  </div>
                  <div
                    class="emotion-9"
                  >
                    <a
                      class="emotion-25"
                      color="dark"
                      href="/VALUE"
                    >
                      <span
                        class="emotion-26"
                      >
                        Shop The Ultimate Collection
                        <span
                          aria-hidden="true"
                          class="emotion-27"
                        />
                      </span>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </article>
    </div>
  </div>
</DocumentFragment>
`;

exports[`ProductDetailsPage ON image should match snapshot with a small breakpoint 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  position: relative;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  max-width: 1400px;
  box-sizing: border-box;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.emotion-0 .slick-slider ul.slick-dots {
  bottom: 0;
  display: -webkit-box!important;
  display: -webkit-flex!important;
  display: -ms-flexbox!important;
  display: flex!important;
  height: 44px;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

.emotion-0 .slick-slider .slick-dots li.slick-active button:before {
  background-color: #003764;
}

.emotion-0 .slick-slider ul.slick-dots button:before {
  width: 10px;
  height: 10px;
  opacity: 1;
  background-color: #FFFFFF;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 15px;
  box-sizing: border-box;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  aspect-ratio: 9/16;
  height: 100%;
  position: absolute;
  width: 100%;
  overflow: clip;
}

.emotion-2 {
  -webkit-background-size: cover;
  background-size: cover;
}

.emotion-3 {
  width: 100%;
  height: 100%;
}

.emotion-4 {
  background: transparent;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  box-sizing: border-box;
  -webkit-flex: 1;
  -ms-flex: 1;
  flex: 1;
  height: 100%;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  pointer-events: none;
  width: 100%;
  aspect-ratio: 9/16;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: flex-end;
  justify-content: flex-end;
  text-align: left;
}

.emotion-5 {
  width: 100%;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0) 100%);
}

.emotion-6 {
  pointerevents: auto;
}

.emotion-7 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
}

.emotion-8 {
  height: auto;
  max-width: 100%;
}

.emotion-9 {
  min-height: 21.333333333333336vw;
  max-height: 21.333333333333336vw;
}

.emotion-10 .amp-cms--p {
  padding: 0;
  margin: 0;
  line-height: 0;
}

.emotion-10 .amp-cms--p>span {
  display: inline;
  white-space: break-spaces;
}

.emotion-10 .amp-cms--p a {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  position: relative;
  z-index: 2;
  pointer-events: auto;
}

.emotion-10 .amp-cms--p a:hover {
  -webkit-text-decoration: none;
  text-decoration: none;
}

.emotion-10 .amp-cms--legal-copy {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 37.333333333333336px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-10 sup {
  vertical-align: top;
  display: inline-block;
  margin-top: -0.20ex;
}

.emotion-10 sub {
  vertical-align: bottom;
  display: inline-block;
  margin-bottom: -0.20ex;
}

.emotion-10 .amp-cms--f-0 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 41.06666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--f-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 41.06666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--f-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 41.06666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--fn-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: min(2.933333333333333vw, 41.06666666666667px);
  line-height: 1.2727272727272727;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.733333333333334vw, 52.266666666666666px));
  line-height: 1.5;
  letter-spacing: min(0.08960000000000001vw, 1.2544000000000002px);
  font-weight: 500;
}

.emotion-10 .amp-cms--body-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 44.8px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 1.0752px);
  font-weight: 500;
}

.emotion-10 .amp-cms--body-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(3.2vw, 44.8px));
  line-height: 1.5;
  letter-spacing: min(0.0768vw, 1.0752px);
}

.emotion-10 .amp-cms--body-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 37.333333333333336px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--body-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(10px, min(2.666666666666667vw, 37.333333333333336px));
  line-height: 1.5;
  letter-spacing: 0;
}

.emotion-10 .amp-cms--eyebrow-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(6.133333333333333vw, 85.86666666666667px));
  line-height: 1;
  letter-spacing: min(0.24533333333333335vw, 3.4346666666666668px);
  font-weight: 500;
}

.emotion-10 .amp-cms--eyebrow-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(5.066666666666666vw, 70.93333333333334px));
  line-height: 1;
  letter-spacing: min(0.20266666666666666vw, 2.8373333333333335px);
  font-weight: 500;
}

.emotion-10 .amp-cms--eyebrow-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(12px, min(3.733333333333334vw, 52.266666666666666px));
  line-height: 1.0714285714285714;
  letter-spacing: min(0.14933333333333335vw, 2.090666666666667px);
  font-weight: 500;
}

.emotion-10 .amp-cms--headline-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(18.133333333333333vw, 253.86666666666667px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(13.333333333333334vw, 186.66666666666666px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(11.466666666666667vw, 160.53333333333333px));
  line-height: 1;
  letter-spacing: 0;
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-4 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(9.6vw, 134.4px));
  line-height: 1;
  letter-spacing: min(0.19199999999999998vw, 2.6879999999999997px);
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-5 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(7.466666666666668vw, 104.53333333333333px));
  line-height: 1;
  letter-spacing: min(0.2986666666666667vw, 4.181333333333334px);
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-6 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(6.4vw, 89.6px));
  line-height: 1.0833333333333333;
  letter-spacing: min(0.256vw, 3.584px);
  font-weight: 700;
}

.emotion-10 .amp-cms--headline-7 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(20px, min(5.333333333333334vw, 74.66666666666667px));
  line-height: 1;
  letter-spacing: min(0.128vw, 1.792px);
  font-weight: 700;
}

.emotion-10 .amp-cms--headlineAlt-1 {
  color: #003764;
  font-size: max(13px, min(17.066666666666666vw, 238.93333333333334px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.1199999999999999px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-2 {
  color: #003764;
  font-size: max(13px, min(14.399999999999999vw, 201.6px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.1199999999999999px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-3 {
  color: #003764;
  font-size: max(13px, min(9.066666666666666vw, 126.93333333333334px));
  line-height: 1;
  letter-spacing: min(-0.07999999999999999vw, -1.1199999999999999px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-4 {
  color: #003764;
  font-size: max(13px, min(6.4vw, 89.6px));
  line-height: 1.4166666666666667;
  letter-spacing: min(-0.07999999999999999vw, -1.1199999999999999px);
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-5 {
  color: #003764;
  font-size: min(3.733333333333334vw, 52.266666666666666px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-6 {
  color: #003764;
  font-size: min(3.733333333333334vw, 52.266666666666666px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--headlineAlt-7 {
  color: #003764;
  font-size: min(3.733333333333334vw, 52.266666666666666px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 400;
}

.emotion-10 .amp-cms--promo-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(16vw, 224px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-10 .amp-cms--promo-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(13px, min(10.666666666666668vw, 149.33333333333334px));
  line-height: 1;
  letter-spacing: min(0vw, 0px);
  font-weight: 600;
  text-transform: uppercase;
}

.emotion-10 .amp-cms--promoAlt-1 {
  color: #003764;
  font-size: min(3.733333333333334vw, 52.266666666666666px);
  line-height: 0.07142857142857142;
  letter-spacing: 0;
  font-weight: 600;
}

.emotion-10 .amp-cms--subhead-1 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(5.333333333333334vw, 74.66666666666667px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-10 .amp-cms--subhead-2 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.8vw, 67.2px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-10 .amp-cms--subhead-3 {
  color: #003764;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: max(16px, min(4.266666666666667vw, 59.733333333333334px));
  line-height: 1.5;
  letter-spacing: 0;
  font-weight: 500;
}

.emotion-12 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  flex-direction: row;
  -webkit-column-gap: 20px;
  column-gap: 20px;
}

.emotion-13 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  text-align: center;
  min-width: 10.666666666666668vw;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: start;
  justify-content: start;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-14 {
  height: 10.666666666666668vw;
}

.emotion-15 {
  font-size: 2.933333333333333vw;
  padding-top: 1.866666666666667vw;
  line-height: 145%;
  max-width: 20vw;
  font-weight: 500;
  text-transform: uppercase;
  color: #111;
}

.emotion-23 {
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  line-height: normal;
  text-transform: uppercase;
  cursor: pointer;
  vertical-align: middle;
  -webkit-transition: all 250ms ease-in-out;
  transition: all 250ms ease-in-out;
  box-sizing: border-box;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 700;
  letter-spacing: 1.6px;
  min-height: 44px;
  max-height: auto;
  line-height: 1.25;
  padding: 0;
  width: auto;
  font-family: var(--font-family-font1),sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: left;
  border-radius: 0;
  border: none;
  background-color: transparent;
  color: #003764;
  height: auto;
  -webkit-box-pack: left;
  -ms-flex-pack: left;
  -webkit-justify-content: left;
  justify-content: left;
  -webkit-align-self: flex-start;
  -ms-flex-item-align: flex-start;
  align-self: flex-start;
  white-space: normal;
  pointer-events: all;
}

.emotion-23:focus {
  outline: none;
}

.emotion-23>span span {
  height: calc(16px * 0.7);
}

.emotion-23 span span {
  padding-left: 1px;
  min-width: auto;
  min-height: auto;
  position: relative;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 250ms ease-in-out;
  transition: 250ms ease-in-out;
  transition-property: height,width,margin;
  top: 0;
  width: calc(1.2rem * 0.7);
}

.emotion-23 span span svg {
  -webkit-transition: fill 250ms ease-in-out;
  transition: fill 250ms ease-in-out;
  fill: #003764;
}

.emotion-23:hover,
.emotion-23:focus {
  color: #FFFFFF;
  background-color: #003764;
}

.emotion-23:hover span svg,
.emotion-23:focus span svg {
  fill: #FFFFFF;
}

.emotion-23 span:active {
  color: #FFFFFF;
  background-color: #000000;
}

.emotion-23 span:active span svg {
  fill: #FFFFFF;
}

.emotion-24 {
  box-sizing: border-box;
}

.emotion-25 {
  display: inline-block;
  height: 2rem;
  width: 2rem;
  min-height: 2rem;
  min-width: 2rem;
}

.emotion-25 svg {
  display: block;
  max-height: 100%;
  max-width: 100%;
  position: relative;
  width: 100%;
}

<div
    style="font-family: var(--font-gapsans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="font-family: var(--font-family-font1),sans-serif;"
    >
      <article
        class="emotion-0"
      >
        <section
          class="emotion-1"
        >
          <div
            class="emotion-2"
            data-testid="product-card-image"
          >
            <canvas
              class="emotion-3"
              role="img"
            />
          </div>
        </section>
        <div
          class="emotion-4"
          height="0"
          width="0"
        >
          <div
            class="emotion-5"
            data-testid="gradient-wrapper"
          >
            <div
              class="emotion-6"
            >
              <div
                style="display: flex; flex-direction: column; gap: 16px; padding: 24px 24px 68px;"
              >
                <div
                  class="emotion-7"
                >
                  <div
                    class="emotion-8"
                    data-testid="advance-image-container-test-id"
                  >
                    <img
                      alt="Mobile Icon"
                      class="emotion-9"
                      maxminheight="21.333333333333336vw"
                      src="https://pqm4o81ro4dn1332lt5a8eas8.staging.bigcontent.io/i/gap/Gap_BOPIS_bag-icon?fmt=auto"
                    />
                  </div>
                </div>
                <div
                  style="text-align: left;"
                >
                  <div
                    class="emotion-10"
                  >
                    <div>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-2"
                        >
                          Mobile Introducing
                        </span>
                      </p>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--headline-5"
                        >
                          THE ULTIMATE II TIGHT
                        </span>
                      </p>
                      <p
                        class="amp-cms--p"
                      >
                        <span
                          class="amp-cms--body-3"
                        >
                          Our #1 sweat ready style just got a major upgrade. Lorem ipsum dolor sit amet.
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-7"
                >
                  <div
                    class="emotion-12"
                  >
                    <div
                      class="emotion-13"
                    >
                      <img
                        alt=""
                        class="emotion-14"
                        src="https://athleta.a.bigcontent.io/v1/static/BREATHABLE_dark"
                      />
                      <span
                        class="emotion-15"
                      >
                        Breathable
                      </span>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <img
                        alt=""
                        class="emotion-14"
                        src="https://athleta.a.bigcontent.io/v1/static/WRINKLE-RESISTANT_dark"
                      />
                      <span
                        class="emotion-15"
                      >
                        Wrinkle-Resistant
                      </span>
                    </div>
                    <div
                      class="emotion-13"
                    >
                      <img
                        alt=""
                        class="emotion-14"
                        src="https://athleta.a.bigcontent.io/v1/static/ALL-AROUND-STRETCH_dark"
                      />
                      <span
                        class="emotion-15"
                      >
                        All-Around Stretch
                      </span>
                    </div>
                  </div>
                </div>
                <div
                  class="emotion-7"
                >
                  <a
                    class="emotion-23"
                    color="dark"
                    href="/VALUE"
                  >
                    <span
                      class="emotion-24"
                    >
                      Shop The Ultimate Collection
                      <span
                        aria-hidden="true"
                        class="emotion-25"
                      />
                    </span>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </article>
    </div>
  </div>
</DocumentFragment>
`;
