# CreditCardOffer

> **⚠️ DEPRECATED (2025-08-05)**  
> CreditCardOffer is deprecated and will be removed in a future release.  
> This component will no longer receive updates or support.  
> If you have any questions or need assistance, please reach out to the FUI support slack channel: https://gapinc.enterprise.slack.com/archives/C03R0QG9JM6.  
> **Component scheduled for removal. Do not use in new development.**

## General Description

The `CreditCardOffer` component creates credit card promotions banner that displays savings if the user is approved for a credit card.

## How to Use

The `CreditCardOffer` component requires a developer to pass in five props: `brandName`, `data`, `itemsTotalForBrand`, `shoppingBagTotal` and `currencySymbol`.
There are also four optional props: `submitButtonText`, `prescreenApiHost`, `returnUrl` and `onClickApplyNow`.

### Required Prop: `brandName`

- Type: `String` REQUIRED
- Description: The brand that the `CreditCardOffer` is associated with

### Required Prop: `itemsTotalForBrand`

- Type: `Number` REQUIRED
- Description: Total dollar amount of items for the specific brand.

### Required Prop: `shoppingBagTotal`

- Type: `Number` REQUIRED
- Description: Total dollar amount of user's shopping bag

### Required Prop: `data`

- Type: `Object` REQUIRED
- Description: This object is parsed from a JSON object. It contains all of the necessary information to render the `CreditCardOffer` component

### Required Prop: `currencySymbol`

- Type: `String` REQUIRED
- Description: Market specific currency symbol.

### Optional Prop: `submitButtonText`

- Type: `String` OPTIONAL
- Description: Custom text to show on the submit button for this component. By default, 'apply now' text will be used.

### Optional Prop: `prescreenApiHost`

- Type: `String` OPTIONAL
- Description: Api host to make prescreen request. If not passed, will use relative url instead.

### Optional Prop: `returnUrl`

- Type: `String` OPTIONAL
- Description: Url to be added as a retUrl query param in apply link url(required for barclays).

### Optional Prop: `onClickApplyNow`

- Type: `Function` OPTIONAL
- Description: Optional function to be executed, instead of the default tealium one, when user clicks apply now.

#### Quick Look at the `data` Object

| Key                        |   Type    | Required? |
| -------------------------- | :-------: | :-------: |
| `showSavingsDetails`       | `boolean` |    Yes    |
| `offerDescriptionEmphasis` | `String`  |    Yes    |
| `offerDescription`         | `String`  |    Yes    |
| `applyLinkURL`             | `String`  |    Yes    |
| `applyLinkURLMobile`       | `String`  |    Yes    |
| `cardImg`                  | `String`  |    Yes    |
| `legalDetails`             | `String`  |    Yes    |
| `savingsThreshold`         | `Number`  |    Yes    |
| `discountPercentage`       | `Number`  |    Yes    |

#### Key Definitions

##### `showSavingsDetails`

- Type: `Boolean` REQUIRED
- Description: Option to show card savings details

##### `offerDescriptionEmphasis`

- Type: `String` REQUIRED
- Description: Emphasized portion of offer description

##### `offerDescription`

- Type: `String` REQUIRED
- Description: The rest of the offer description i.e '<offerDescriptionEmphasis /><offerDescription />'

##### `applyLinkURL`

- Type: `String` REQUIRED
- Description: Desktop view URL link for credit card application

##### `applyLinkURLMobile`

- Type: `String` REQUIRED
- Description: Mobile view URL link for credit card application

##### `cardImg`

- Type: `String` REQUIRED
- Description: Image URL for brand specific credit card

##### `legalDetails`

- Type: `String` REQUIRED
- Description: Legal details for modal window. Modal can display a new page if passed an absolute or relative url or a paragraph if passed raw text

##### `savingsThreshold`

- Type: `Number` REQUIRED
- Description: Minimum savings amount before component is shown.

##### `discountPercentage`

- Type: `Number` REQUIRED
- Description: Discount offered to user.
