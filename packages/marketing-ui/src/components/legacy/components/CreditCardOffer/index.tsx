// @ts-nocheck
'use client';

/**
 * @deprecated 2025-08-05
 * CreditCardOffer is deprecated and will be removed in a future release.
 * This component will no longer receive updates or support.
 * If you have any questions or need assistance, please reach out to the FUI support slack channel: https://gapinc.enterprise.slack.com/archives/C03R0QG9JM6.
 * @todo Component scheduled for removal. Do not use in new development.
 */

import React, { useEffect, useState } from 'react';
import { withAnalytics } from '@ecom-next/core/legacy/analytics';
import CardOfferLayout, { CardOfferLayoutProps } from './CardOfferLayout';
import { getCardSavings } from './getCardSavings';
import { getContentType } from './getContentType';

type Data = {
  discountPercentage: number;
  savingsThreshold: number;
};

export interface CreditCardOfferProps extends Pick<CardOfferLayoutProps, 'onOfferRender' | 'onOfferUnmount'> {
  brandAbbr: ValidBrand;
  data: Data;
  isFeatureEnabled: boolean;
  itemsTotalForBrand: number;
  market: Market;
  prescreenApiHost: string;
  returnUrl: string;
  shoppingBagTotal: number;
}

export type ValidBrand = 'GPFS' | 'BRFS' | 'GP' | 'BR' | 'ON' | 'AT';

export type MarketOptions = 'US' | 'CA';
export type Market = MarketOptions | Lowercase<MarketOptions>;
export type ClientId = 'bag-ui-xapi' | 'checkout-xapi';

export type OIDCHeaders = {
  'Content-Type': string;
  'X-Gap-ApiMode': string;
  brand: ValidBrand;
  channel: string;
  clientId: ClientId;
  market: Market;
};

type OIDCHeadersWithPreviewType = OIDCHeaders & { previewType: 'WIP' };

export type CardSavingsInfoType = typeof defaultCardSavings;

const defaultCardSavings = {
  promoCode: '',
  promoID: '',
  currencyCode: '',
  currentSubTotal: 0,
  estimatedSavings: 0,
  estimatedSubTotal: 0,
  failedCall: false,
};

const CreditCardOffer = ({
                           itemsTotalForBrand,
                           shoppingBagTotal,
                           data,
                           prescreenApiHost,
                           isFeatureEnabled = false,
                           market,
                           brandAbbr,
                           returnUrl,
                           ...props
                         }: CreditCardOfferProps) => {
  const [cardSavingsInfo, setCardSavingsInfo] = useState<CardSavingsInfoType>(defaultCardSavings);

  const { savingsThreshold, discountPercentage } = data;

  const discountAmount: number = isFeatureEnabled ? cardSavingsInfo.estimatedSavings : itemsTotalForBrand * (discountPercentage / 100);

  const totalAmount: number = isFeatureEnabled ? cardSavingsInfo.estimatedSubTotal : shoppingBagTotal - discountAmount;

  const isBagTotalGreaterThanThreshold = shoppingBagTotal > savingsThreshold;

  const previewType = getContentType(prescreenApiHost);

  let clientName = '';
  if (returnUrl) {
    const clientReturnUrl = new URL(returnUrl);
    clientName = clientReturnUrl.pathname.split('/')[1];
  }

  const clientMap = {
    'shopping-bag': 'bag-ui-xapi',
    checkout: 'checkout-xapi',
  } as const satisfies Record<string, ClientId>;

  const clientId = clientName && clientMap[clientName] ? clientMap[clientName] : '';

  useEffect(() => {
    let headers: OIDCHeaders = {
      'Content-Type': 'application/json',
      'X-Gap-ApiMode': 'leapfrog',
      market,
      brand: brandAbbr,
      channel: 'WEB',
      clientId,
    };

    if (previewType === 'WIP') {
      headers = { ...headers, previewType } as OIDCHeadersWithPreviewType;
    }

    const fetchCardSavings = async () => {
      try {
        const response = await getCardSavings(headers, shoppingBagTotal, prescreenApiHost);
        setCardSavingsInfo({ ...response, failedCall: false });
      } catch {
        setCardSavingsInfo({ ...defaultCardSavings, failedCall: true });
      }
    };

    if (isFeatureEnabled && isBagTotalGreaterThanThreshold) {
      fetchCardSavings();
    }
  }, [shoppingBagTotal, isFeatureEnabled, prescreenApiHost, brandAbbr, market, isBagTotalGreaterThanThreshold, previewType]);

  if (!isBagTotalGreaterThanThreshold || !discountAmount || discountAmount < savingsThreshold || cardSavingsInfo.failedCall) {
    return null;
  }

  const marketingProps = {
    shoppingBagTotal: shoppingBagTotal.toFixed(2),
    discountAmount: discountAmount.toFixed(2),
    totalAmount: totalAmount.toFixed(2),
    prescreenApiHost,
    market,
    brandAbbr,
    data,
    returnUrl,
    ...props,
  };

  return <CardOfferLayout {...marketingProps} />;
};

const defaultProps = {
  itemsTotalForBrand: 0.0,
  shoppingBagTotal: 0.0,
  data: {
    savingsThreshold: 0.0,
    discountPercentage: 0.0,
  },
};

CreditCardOffer.defaultProps = defaultProps;

/**
 * @deprecated 2025-08-05
 */
export default withAnalytics(CreditCardOffer);

export { CreditCardOffer as PureCreditCardOffer };
