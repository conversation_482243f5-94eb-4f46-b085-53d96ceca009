// @ts-nocheck
'use client';
import { AppStateProvider } from '@ecom-next/sitewide/app-state-provider';
import { ComponentMeta, ComponentStory, DecoratorFn } from '@storybook/react';
import React from 'react';
import BasicHtml from '..';
import JsonDynamicMarketing from '../../../../json-marketing';
import CmsDynamicMarketing from '../../../../legacy-mui-entry';
import { MarketingProvider } from '../../../../marketing-provider';
import README from '../README.mdx';
import {
  aTagExample,
  divExample,
  headerTagsExample,
  imgExample,
  jumplinkExample,
  mediaQueryExample,
  nestedTagsExample,
  spanExample,
  viewportOverridesExample,
} from './exampleJSONs';

const withPadding: DecoratorFn = Story => (
  <div css={{ padding: '1rem' }}>
    <Story />
  </div>
);

const marketingData = {
  contentData: {
    contentItems: [],
  },
};

const withMarketingProvider: DecoratorFn = (Story, context) => (
  <AppStateProvider value={{ ...context.globals.appState }}>
    <MarketingProvider value={marketingData} jsonMarketingComponent={JsonDynamicMarketing} cmsMarketingComponent={CmsDynamicMarketing}>
      <Story />
    </MarketingProvider>
  </AppStateProvider>
);

const BasicHtmlComponent = BasicHtml as React.ComponentType;

export default {
  title: 'Common/JSON Components (Marketing)/BasicHtml (builtin)',
  decorators: [withPadding, withMarketingProvider],
  parameters: {
    docs: {
      page: README,
    },
    knobs: { escapeHTML: false },
    eyes: { include: false },
  },
  tags: ['exclude'],
} as ComponentMeta<typeof BasicHtmlComponent>;

const BasicHtmlTemplate: ComponentStory<typeof BasicHtmlComponent> = args => <BasicHtmlComponent {...args} />;

export const anchor = BasicHtmlTemplate.bind({});
anchor.args = {
  ...aTagExample,
  deprecated: true,
};

export const div = BasicHtmlTemplate.bind({});
div.args = {
  ...divExample,
  deprecated: true,
};

export const headerTags = BasicHtmlTemplate.bind({});
headerTags.args = {
  ...headerTagsExample,
  deprecated: true,
};

export const img = BasicHtmlTemplate.bind({});
img.args = {
  ...imgExample,
  deprecated: true,
};

export const jumplink = BasicHtmlTemplate.bind({});
jumplink.args = {
  ...jumplinkExample,
  deprecated: true,
};

export const mediaQueries = BasicHtmlTemplate.bind({});
mediaQueries.args = {
  ...mediaQueryExample,
  deprecated: true,
};

export const nestedTags = BasicHtmlTemplate.bind({});
nestedTags.args = {
  ...nestedTagsExample,
  deprecated: true,
};

export const span = BasicHtmlTemplate.bind({});
span.args = {
  ...spanExample,
  deprecated: true,
};

export const viewportOverrides = BasicHtmlTemplate.bind({});
viewportOverrides.args = {
  ...viewportOverridesExample,
  deprecated: true,
};
