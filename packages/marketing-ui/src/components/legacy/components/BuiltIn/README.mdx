# BasicHtml

> **⚠️ DEPRECATED (2025-08-05)**  
> BuiltIn is deprecated and will be removed in a future release.  
> This component will no longer receive updates or support.  
> If you have any questions or need assistance, please reach out to the FUI support slack channel: https://gapinc.enterprise.slack.com/archives/C03R0QG9JM6.  
> **Component scheduled for removal. Do not use in new development.**

The `BasicHtml` component allows Web Content Developers to put HTML `div`, `p`, `span`, `a`, `h1`, `h2`, `h3`, `h4`, `h5`, `h6`, and `img` tags directly into a JSON configuration. HTML attributes are available.

## Basic Example using `"span"`

```json
{
  "type": "builtin",
  "name": "span",
  "data": {
    "style": {
      "background": "#d4edda",
      "display": "flex",
      "padding": "20px",
      "justifyContent": "center"
    },
    "components": ["Here is a <span> tag. Use the \"knobs\" tab to adjust the JSON."]
  }
}
```

## API

`BasicHtml` has the following basic structure:

```json5
{
  type: 'builtin',
  name: '[tagName]', // specifies the tag name of the React element to be created
  // "div", "span", "a", and "img" are available
  data: {
    style: {}, // optional object to allow styling for the element
    props: {}, // object is spread as attributes in the React HTML element
    components: [], // renders each array element as a child
  },
}
```

The `style` object allows styles to be assigned to the element. See [Applying CSS Styles to your HTML element](#applying-css-styles-to-your-html-element) for more details.

The `props` object spreads the key-value pairs as attributes to the React HTML element. See the [React Documentation](https://reactjs.org/docs/dom-elements.html) for the difference between these attributes and standard HTML attributes.

The `components` array renders each array element as a child of the React element. If the element is an object, it is as rendered as a nested React element.

These three parameters can be overridden by a desktop or tablet version. See [Tablet/Desktop Overrides](#overrides) for more details.

### `img` Requirements

The `img` tag has two extra requirements:

1. An `alt` tag is required in the `props` object. To mark an image as decorative, all that is required is that you supply an empty ALT attribute.

To do this, simply provide `alt: ""` to indicate an empty ALT attribute. This will signal a screen reader to skip over the image, taking it out of the reading flow.

2. The `components` array must be `null`.

If either of these requirements are not met, an `img` element will not be created.

### Documentation for Available Attributes

The [differences](https://reactjs.org/docs/dom-elements.html) between the React attributes and the standard HTML attributes require some syntactical changes (for example, `textAlign` instead of `text-align`), but otherwise, all the attributes available for each HTML tag are also available for the corresponding React element.

- [Global attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes)

- `<h1>`, `<h2>`, `<h3>`, `<h4>`, `<h5>`, `<h6>`, `<div>`, `<p>`, and `<span>`: Only uses the global attributes

- [`<a>` attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/a#Attributes)

- [`img` attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#Attributes)

## More examples

### Simple example of using `"h2"` tag

```json
{
  "type": "builtin",
  "name": "h2",
  "data": {
    "props": {
      "className": "sds_display-a"
    },
    "components": ["Shop Our Greatest Sale"]
  }
}
```

**IMPORTANT NOTE:** If using an `h1` tag, please be aware that it could have a major impact on SEO. Please use this tag only when it is absolutely necessary and ensure the rendered page does not already have one.<br/><br/>**Example scenario:** When adding an EBB/VI Banner on a category page, the `h1` tag is usually no longer present. To ensure the page has an `h1` tag you can include the builtin as a part of the EBB/VI Banner feature. Be sure the tag includes keywords of the pages's subject matter.

### Simple example using `"div"`

```json
{
  "type": "builtin",
  "name": "div",
  "data": {
    "style": {
      "background": "#cce5ff",
      "display": "flex",
      "padding": "20px",
      "justifyContent": "center"
    },
    "components": ["Here is a <div> tag. Use the \"knobs\" tab to adjust the JSON."]
  }
}
```

### Simple example using `"a"`

```json
{
  "type": "builtin",
  "name": "a",
  "data": {
    "style": {
      "background": "#d5d4ed",
      "padding": "0.5rem",
      "display": "flex",
      "justifyContent": "center"
    },
    "props": {
      "href": "https://www.duckduckgo.com",
      "target": "_blank"
    },
    "components": ["Here is an <a> tag. Use the \"knobs\" tab to adjust the JSON."]
  }
}
```

The `a` tag also accepts an optional `tid` attribute in the props object which will append the appropriate `mlink` and referring `cid`. This relies on the cid, market and brandName props provided by `appState`. _Note: If the href already has an "mlink" appended in the URL, the tid attribute will be ignored. Be sure to remove the mlink parameter if your intent is to use the tid feature_

### Example using `"a"` with tracking

```json
{
  "type": "builtin",
  "name": "a",
  "data": {
    "props": {
      "href": "/browse/category.do?cid=1180064",
      "tid": "HP_Prim_2_a"
    },
    "components": ["Here is an <a> tag with tracking.  \"mlink\" parameter will be appended automatically with CID of page."]
  }
}
```

### Simple example using `"img"`

```json
{
  "type": "builtin",
  "name": "img",
  "data": {
    "props": {
      "src": "https://www.gap.com/Asset_Archive/GPWeb/content/0014/460/630/assets/gapcards-4x-blue-silver.png",
      "alt": "Gap Cards"
    }
  }
}
```

### Example with nested components

```json
{
  "type": "builtin",
  "name": "div",
  "data": {
    "style": {
      "fontFamily": "Helvetica, sans-serif",
      "display": "flex",
      "maxWidth": "24rem"
    },
    "components": [
      {
        "type": "builtin",
        "name": "a",
        "data": {
          "style": {
            "textDecoration": "none",
            "color": "black",
            "display": "inherit"
          },
          "props": {
            "href": "https://www.gap.com",
            "target": "_blank"
          },
          "components": [
            {
              "type": "builtin",
              "name": "img",
              "data": {
                "style": {
                  "flex": "0 0 50px",
                  "marginRight": "150px"
                },
                "props": {
                  "src": "https://www.gap.com/Asset_Archive/GPWeb/content/0017/901/437/assets/VCN_PZ/unrec/VCN_BG_7.jpg",
                  "alt": "Baby Girl"
                }
              }
            }
          ]
        }
      },
      {
        "type": "builtin",
        "name": "div",
        "data": {
          "components": [
            "Look at this! It's a \"div\" containing an \"img\" and some text using another \"div\". And why not ",
            {
              "type": "builtin",
              "name": "span",
              "data": {
                "style": {
                  "fontWeight": "bold"
                },
                "components": ["throw in a \"span\" "]
              }
            },
            "while we're at it? Oh, and the pic is a link to Gap. Brought to you by `BasicHtml`. Use the \"knobs\" tab to adjust the JSON."
          ]
        }
      }
    ]
  }
}
```

## Jumplinking

The `BasicHtml` component allows the ability to flag if it should be a jumplink or not.
Simply specify `isAJumplink` to be true and `jumplinkCSSSelector` to be the CSS selector of the target element.

```json
{
  "type": "builtin",
  "name": "a",
  "data": {
    "isAJumplink": true,
    "jumplinkCSSSelector": "#sitewide-footer",
    "style": {
      "margin": "1rem auto",
      "display": "block",
      "width": "200px",
      "fontWeight": "bold"
    },
    "props": {
      "href": "#",
      "className": "sds_btn"
    },
    "components": ["Go to footer"]
  }
}
```

**IMPORTANT:** Be sure to only to use this functionality with the `a` tag for accessibility purposes.

## Applying CSS Styles to your HTML element

The `BasicHtml` component uses [Emotion](https://emotion.sh/), like the `LayeredContentModule` does, allowing you to use psuedo-classes and media queries for different breakpoints.

```json
{
  "name": "div",
  "type": "builtin",
  "data": {
    "style": {
      "display": "inline-block",
      "whiteSpace": "pre-line",
      "color": "#cc0000",
      "transition": "color .5s ease-out",
      "&:hover": {
        "color": "#cccc00"
      },
      "fontSize": "3.911vw",
      "@media (min-width: 768px)": {
        "color": "#0000cc",
        "&:hover": {
          "color": "#00cccc"
        },
        "fontSize": "2.143vw",
        "@media (min-width: 1400px)": {
          "fontSize": "30px"
        }
      }
    },
    "components": ["MOBILES ARE RED\nDESKTOPS ARE BLUE\nIF YOU HOVER OVER ME\nI CHANGE COLOR TOO"]
  }
}
```

The style parameter goes directly into the data object. Please note: due to the nature of the BuiltIn being able to take in attributes, you would still be able to specify a `style` attribute in the `props` object (not shown in example). This effectively is an inline-style that will override what is defined in the style props. We highly recommend only using the "style object" method for more flexibility and avoid defining the "style attribute" in the props object.

## Tablet/Desktop Overrides

The `BasicHtml` component now allows you to specify optional breakpoint/viewport specific parameters. The three parameters this is available for are the `style` object, the `props` object, and the `components` array. Please be sure to always assign a default/mobile version before assigning a desktop or tablet override version.

If `desktopStyle` and/or `tabletStyle` objects are defined, this will merge with the default/mobile style object. This could be used to better organize your CSS styles and make it more readable.

```json
{
  "name": "div",
  "type": "builtin",
  "data": {
    "style": {
      "color": "red",
      "fontWeight": "bold"
    },
    "tabletStyle": {
      "color": "green"
    },
    "desktopStyle": {
      "color": "blue"
    }
  }
}
```

In the example above, when you are viewing at desktop breakpoint the text will become blue, but the font-weight will be bold (inherited from the default/mobile style parameter).

If `desktopProps` and/or `tabletProps` objects are defined, this will merge with the default/mobile props object. This could be used to assign different alt tags, images, links, tracking for different viewports.

```json
{
  "name": "div",
  "type": "builtin",
  "data": {
    "props": {
      "data-testid": "default-or-mobile-props",
      "title": "This title should be for all viewports"
    },
    "tabletProps": {
      "data-testid": "tablet-override-props"
    },
    "desktopProps": {
      "data-testid": "desktop-override-props"
    }
  }
}
```

In the example above, when you are viewing at desktop breakpoint the data-testid value will become "desktop-override-props", but the title will remain "This title should be for all viewports" (inherited from the default/mobile props parameter).

If `desktopComponents` and/or `tabletComponents` arrays are defined, they will completely override the objects in the components array when it reaches that specific breakpoint. This could be used to completely change the layout between specific viewports. For instance, there would be a button dropdown for desktop, but on mobile it would be exposed CTAs.

```json
{
  "name": "div",
  "type": "builtin",
  "data": {
    "components": ["Mobile or Default View"],
    "tabletComponents": ["Tablet View"],
    "desktopComponents": ["Desktop View"]
  }
}
```

In the example above, when you are viewing at desktop breakpoint, the html text will become "Desktop View".  
When viewing at tablet breakpoint, the html text will become "Tablet View".  
When viewing at mobile breakpoint, the html text will become "Mobile or Default View".

**NOTE:** There is no need to specify a desktop or tablet version if the element will share the exact same styles, properties or components as the default/mobile version. It is also unnecessary to define them if the element will only appear for a specific breakpoint, like when using in a LayoutComponent set to desktop or mobile.
