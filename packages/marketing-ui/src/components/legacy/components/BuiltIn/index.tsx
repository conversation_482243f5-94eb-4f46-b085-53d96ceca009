// @ts-nocheck
'use client';

/**
 * @deprecated 2025-08-05
 * BuiltIn is deprecated and will be removed in a future release.
 * This component will no longer receive updates or support.
 * If you have any questions or need assistance, please reach out to the FUI support slack channel: https://gapinc.enterprise.slack.com/archives/C03R0QG9JM6.
 * @todo Component scheduled for removal. Do not use in new development.
 */

import React, { useContext } from 'react';
import { LARGE, XLARGE, BreakpointContext } from '@ecom-next/core/breakpoint-provider';
import { useAppState } from '@ecom-next/sitewide/app-state-provider';
import { jsx } from '@emotion/react';
import { safeWindow } from '../../helper/safeWindow';
import { DynamicMarketing as Marketing } from '../../../json-marketing.client';
import validate from './Validators';
import { jumpToSection } from '../../helper/jumpToSection';
import { retrieveHomeCid } from './homeCids';
import { BasicHtmlProps } from './types';

const BasicHtml = (componentJson: BasicHtmlProps) => {
  const { minWidth } = useContext(BreakpointContext);
  let viewportSize = 'mobile';
  if (minWidth(XLARGE)) {
    viewportSize = 'desktop';
  } else if (minWidth(LARGE)) {
    viewportSize = 'tablet';
  }

  const { cid, brandName, market } = useAppState();
  const errorLoggerDefault = (errors: string, json: string) => {
    console.error(`error found when attempting to render ${JSON.stringify(json)}:`);
    console.error(errors[0]);
  };
  const {
    data: { props, desktopProps, tabletProps, components, desktopComponents, tabletComponents, style, desktopStyle, tabletStyle },
    name,
    animationCss,
    errorLogger = errorLoggerDefault,
  } = componentJson;

  const desktopOrTabletStyle = () => {
    if (viewportSize === 'tablet' && tabletStyle) {
      return tabletStyle;
    }
    if (desktopStyle) {
      return desktopStyle;
    }
    return null;
  };

  const desktopOrTabletProps = () => {
    if (viewportSize === 'tablet' && tabletProps) {
      return tabletProps;
    }
    if (desktopProps) {
      return desktopProps;
    }
    return null;
  };

  const desktopOrTabletComponents = () => {
    if (viewportSize === 'tablet' && tabletComponents) {
      return tabletComponents;
    }
    if (desktopComponents) {
      return desktopComponents;
    }
    return null;
  };

  const cssStyles = [style, viewportSize !== 'mobile' && desktopOrTabletStyle()];
  const finalProps = viewportSize !== 'mobile' && desktopOrTabletProps() ? { ...props, ...desktopOrTabletProps() } : props;
  const finalComponents = viewportSize !== 'mobile' && desktopOrTabletComponents() ? desktopOrTabletComponents() : components;

  let finalLink = finalProps?.href;
  if (finalProps?.tid && finalLink?.indexOf('mlink') < 0) {
    const queryParamStart = finalProps.href.indexOf('?') >= 0 ? '&' : '?';
    const useCID = cid === 'home' ? retrieveHomeCid(market, brandName) : cid;
    if (useCID) {
      finalLink = `${finalLink}${queryParamStart}mlink=${useCID},1,${finalProps?.tid}`;
    }
  }

  const getFullProps = (partialProps: any) => ({
    ...partialProps,
  });

  const isComponentJson = (component: React.FunctionComponent) => typeof component === 'object';

  const children = finalComponents?.map((component: any, i: number) =>
    isComponentJson(component) ? (
      /* eslint-disable-next-line react/no-array-index-key */
      <Marketing key={`${component.name}_${i}`} {...getFullProps(component)} />
    ) : (
      component
    )
  );

  const handleClick = (e: Event) => {
    const { data } = componentJson;
    const sWindow = safeWindow();
    if (data && data.isAJumplink && data.jumplinkCSSSelector) {
      jumpToSection(data.isAJumplink, data.jumplinkCSSSelector);
    } else if (finalLink) {
      e.preventDefault();
      finalProps?.target ? sWindow?.open(finalLink, finalProps.target) : sWindow?.location.assign(finalLink);
    }
  };

  return validate(componentJson, errorLogger)?.length
    ? null
    : jsx(
        name,
        {
          ...finalProps,
          css: [cssStyles, animationCss],
          onClick: handleClick,
          href: finalLink,
        },
        children
      );
};

/**
 * @deprecated 2025-08-05
 */
export default BasicHtml;
