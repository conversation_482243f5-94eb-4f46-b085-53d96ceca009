// @ts-nocheck
'use client';
import { StoryFn } from '@storybook/react';
import React from 'react';
import JsonDynamicMarketing from '../../../json-marketing';
import CmsDynamicMarketing from '../../../legacy-mui-entry';
import { MarketingProvider } from '../../../marketing-provider';
import { defaultIconsData, horizontalLayoutData, multipleAnimations, verticalLayoutData } from './__fixtures__/story-data';
import README from './README.mdx';
import CSSAnimation from '.';

const CSSAnimationComponent = CSSAnimation as React.ComponentType;

const marketingData = {
  contentData: {
    contentItems: [],
  },
};

const withProviders = (story, context) => (
  <MarketingProvider value={marketingData} jsonMarketingComponent={JsonDynamicMarketing} cmsMarketingComponent={CmsDynamicMarketing}>
    {story(context)}
  </MarketingProvider>
);

export default {
  title: 'Common/JSON Components (Marketing)/CSSAnimation',
  decorators: [withProviders],
  parameters: {
    docs: {
      page: README,
    },
    knobs: { escapeHTML: false },
    eyes: { include: false },
  },
  tags: ['exclude'],
};

const CSSAnimationTemplate: StoryFn<typeof CSSAnimationComponent> = args => (
  <div>
    <CSSAnimationComponent {...args} />
  </div>
);

export const Horizontal = CSSAnimationTemplate.bind({});
Horizontal.args = {
  ...horizontalLayoutData,
  deprecated: true,
};

export const Vertical = CSSAnimationTemplate.bind({});
Vertical.args = {
  ...verticalLayoutData,
  deprecated: true,
};

export const DefaultIcons = CSSAnimationTemplate.bind({});
DefaultIcons.args = {
  ...defaultIconsData,
  deprecated: true,
};

export const MultipleAnimations = CSSAnimationTemplate.bind({});
MultipleAnimations.args = {
  ...multipleAnimations,
  deprecated: true,
};
