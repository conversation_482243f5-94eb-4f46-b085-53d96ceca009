// @ts-nocheck
import React, { useState, useEffect, useContext } from 'react';
import { BreakpointContext, XLARGE } from '@ecom-next/core/breakpoint-provider';
import { PlayActive, PauseActive } from '../../legacy/components/VideoComponent/components/icons/index';

import { ImageTileContent } from './utils';
import NewTile from './ImageTile';

const ImageTileCarousel = (serverResponse: ImageTileContent) => {
  const [currentSlideIdx, setCurrentSlideIdx] = useState(0);
  const { smallerThan } = useContext(BreakpointContext);
  const isMobile = smallerThan(XLARGE);
  const [isPlaying, setIsPlaying] = useState(true);
  const carouselSlides = isMobile ? serverResponse.imageTabs.mobileCarousel : serverResponse.imageTabs.desktopCarousel;
  const { carouselSettings = {}, hasUniqueHotzones } = serverResponse;

  function getImageData(carouselSlides) {
    const finalSlides = [];
    if (!carouselSlides) return [];
    carouselSlides?.forEach(slide => {
      const serverResponseClone = structuredClone(serverResponse);
      if (isMobile) {
        serverResponseClone.imageTabs.image.url = slide?.mobileImage?.url;
        serverResponseClone.imageTabs.image.dimensions = slide?.mobileImage?.dimensions;
        serverResponseClone.imageTabs.accessibilityAltText = slide.mobileCarouselAltText as string;
        serverResponseClone.imageTabs.image.hotspots = hasUniqueHotzones ? slide?.mobileImage?.hotspots : serverResponseClone.imageTabs.image.hotspots || [];
      } else {
        serverResponseClone.imageTabs.desktopImage.url = slide?.desktopImage?.url;
        serverResponseClone.imageTabs.desktopImage.dimensions = slide?.desktopImage?.dimensions;
        serverResponseClone.imageTabs.desktopAccessibilityAltText = slide.desktopCarouselAltText as string;
        serverResponseClone.imageTabs.desktopImage.hotspots = hasUniqueHotzones
          ? slide?.desktopImage?.hotspots
          : serverResponseClone.imageTabs.desktopImage.hotspots || [];
      }

      finalSlides.push(serverResponseClone);
    });
    return finalSlides;
  }
  const slides = [serverResponse, ...getImageData(carouselSlides)];

  useEffect(() => {
    if (!slides?.length || slides?.length <= 1) {
      return;
    }

    const interval = setInterval(() => {
      setCurrentSlideIdx(prevSlideIdx => (prevSlideIdx === slides.length - 1 ? 0 : prevSlideIdx + 1));
    }, carouselSettings.rotationTime);

    if (!isPlaying) {
      clearInterval(interval);
      return;
    }
    return () => {
      clearInterval(interval);
    };
  }, [slides.length, isPlaying, carouselSettings.rotationTime]);

  if (!slides || slides.length === 0) return null;

  return slides.length === 1 ? (
    <NewTile {...serverResponse} />
  ) : (
    <div className='bg-color-background-default--white relative mx-auto'>
      {slides.map((slide, slideIdx) => (
        <div
          key={slideIdx}
          className={`bg-color-background-default--white inset-0 ${
            carouselSettings?.transitionType === 'fade'
              ? `transition-opacity ${
                  slideIdx === currentSlideIdx ? 'z-2 pointer-events-auto static opacity-100' : 'z-1 pointer-events-none absolute opacity-0'
                }`
              : ''
          }`}
          style={carouselSettings?.transitionType === 'fade' ? { transitionDuration: `${carouselSettings?.transitionTime}ms` } : undefined}
        >
          <NewTile {...slide} />
          <button
            aria-label={isPlaying ? 'Pause' : 'Play'}
            onClick={() => setIsPlaying(!isPlaying)}
            style={{
              position: 'absolute',
              bottom: isMobile ? 20 : 30,
              left: isMobile ? 'auto' : 15,
              right: isMobile ? 15 : 'auto',
              background: 'transparent',
              border: 'none',
              cursor: 'pointer',
              zIndex: 3,
              display: 'block',
              opacity: 0.64,
            }}
          >
            {carouselSettings?.autoplay && carouselSettings?.looping === 'infinite' && (isPlaying ? <PauseActive /> : <PlayActive />)}
          </button>
        </div>
      ))}
    </div>
  );
};

export default ImageTileCarousel;
