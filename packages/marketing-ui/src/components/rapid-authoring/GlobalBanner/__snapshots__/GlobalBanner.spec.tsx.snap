// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`GlobalBanner matches snapshot for the stores page on desktop 1`] = `
<div>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="w-full"
    >
      <section
        class="relative inset-0 flex w-full flex-col items-center justify-center gap-[3px] px-4 py-[10px] text-center text-white lg:grid lg:grid-cols-[1fr_minmax(auto,150px)] lg:gap-0 lg:gap-[5px] lg:px-[15px]"
        style="color: rgb(255, 255, 255); display: grid; grid-template-columns: 1fr minmax(auto, 150px); gap: 0; padding-left: 15px; padding-right: 15px;"
      >
        <div
          class=""
        >
          <div
            class="flex flex-wrap justify-center lg:pl-6 lg:pr-[8px]"
            style="padding-left: 0px;"
          >
            <span
              class="ml-1 mr-[15px] text-sm leading-[16px] lg:text-xs lg:leading-[20px]"
            >
              <span
                class="font-bold"
              >
                00:00:00
              </span>
            </span>
            <h2
              class="font-normal text-sm leading-[16px] lg:text-xs lg:leading-[20px]"
            >
              Welcome to Our Store
            </h2>
            <span
              class="font-normal text-sm lg:ml-1 lg:text-xs lg:leading-[20px]"
            >
              Discover our latest collections
            </span>
            <div
              class="flex flex-nowrap gap-[15px] lg:ml-[15px]"
            >
              <div
                class="flex flex-nowrap gap-[15px]"
              >
                <a
                  aria-hidden="false"
                  class="z-[1] cursor-pointer text-sm underline hover:no-underline lg:text-xs lg:leading-[20px]"
                  href="/shop"
                  tabindex="0"
                  title="Shop Now"
                >
                  Shop Now
                </a>
                <a
                  aria-hidden="false"
                  class="z-[1] cursor-pointer text-sm underline hover:no-underline lg:text-xs lg:leading-[20px]"
                  href="/learn"
                  tabindex="0"
                  title="Learn More"
                >
                  Learn More
                </a>
              </div>
            </div>
          </div>
        </div>
        <div
          class="flex w-full items-center justify-end"
        >
          <div
            class="flex"
          >
            <div
              class="ml-[12px] flex flex-row flex-wrap items-baseline justify-center gap-x-[5px] gap-y-0 lg:ml-[5px] lg:mt-[5px] lg:justify-end"
            >
              <div
                class="inline-flex flex-wrap justify-center gap-x-[5px]"
                style="color: rgb(255, 255, 255);"
              >
                <span
                  class="lg:text-xs"
                >
                  Terms apply
                </span>
                <button
                  aria-hidden="false"
                  aria-label="See details"
                  class="z-[1] m-0 inline-flex border-0 bg-transparent p-0 text-sm lg:text-xs"
                  tabindex="0"
                  title="See details"
                  type="button"
                >
                  <span
                    class="underline hover:no-underline"
                  >
                    See details
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>
`;

exports[`GlobalBanner matches snapshots 1`] = `
<div
  style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
>
  <div
    class="w-full"
  >
    <section
      class="relative inset-0 flex w-full flex-col items-center justify-center gap-[3px] px-4 py-[10px] text-center text-white lg:grid lg:grid-cols-[1fr_minmax(auto,150px)] lg:gap-0 lg:gap-[5px] lg:px-[15px]"
      style="color: rgb(255, 255, 255);"
    >
      <div
        class=""
      >
        <div
          class="flex flex-wrap justify-center lg:pl-6 lg:pr-[8px]"
          style="padding-left: 0px;"
        >
          <span
            class="ml-1 mr-[15px] text-sm leading-[16px] lg:text-xs lg:leading-[20px]"
          >
            <span
              class="font-bold"
            >
              00:00:00
            </span>
          </span>
          <h2
            class="font-normal text-sm leading-[16px] lg:text-xs lg:leading-[20px]"
          >
            Welcome to Our Store
          </h2>
          <span
            class="font-normal text-sm lg:ml-1 lg:text-xs lg:leading-[20px]"
          >
            Discover our latest collections
          </span>
          <div
            class="flex flex-nowrap gap-[15px] lg:ml-[15px]"
          >
            <div
              class="flex flex-nowrap gap-[15px]"
            >
              <a
                aria-hidden="false"
                class="z-[1] cursor-pointer text-sm underline hover:no-underline lg:text-xs lg:leading-[20px]"
                href="/shop"
                tabindex="0"
                title="Shop Now"
              >
                Shop Now
              </a>
              <a
                aria-hidden="false"
                class="z-[1] cursor-pointer text-sm underline hover:no-underline lg:text-xs lg:leading-[20px]"
                href="/learn"
                tabindex="0"
                title="Learn More"
              >
                Learn More
              </a>
            </div>
          </div>
        </div>
      </div>
      <div
        class="flex w-full items-center justify-end"
      >
        <div
          class="flex"
        >
          <div
            class="ml-[12px] flex flex-row flex-wrap items-baseline justify-center gap-x-[5px] gap-y-0 lg:ml-[5px] lg:mt-[5px] lg:justify-end"
          >
            <div
              class="inline-flex flex-wrap justify-center gap-x-[5px]"
              style="color: rgb(255, 255, 255);"
            >
              <span
                class="lg:text-xs"
              >
                Terms apply
              </span>
              <button
                aria-hidden="false"
                aria-label="See details"
                class="z-[1] m-0 inline-flex border-0 bg-transparent p-0 text-sm lg:text-xs"
                tabindex="0"
                title="See details"
                type="button"
              >
                <span
                  class="underline hover:no-underline"
                >
                  See details
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</div>
`;

exports[`GlobalBanner mobile should render the expected snapshot 1`] = `
<div>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      aria-label="Carousel of promotional text banners"
      aria-roledescription="carousel"
      class="relative h-full w-full overflow-x-hidden overflow-y-hidden scroll-smooth"
      role="region"
      style="background-color: rgb(59, 144, 255);"
      tabindex="0"
    >
      <style>
        
        div {
          -ms-overflow-style: none; /* IE and Edge */
        }
        div::-webkit-scrollbar {
          display: none;
        }
      
      </style>
      <div
        aria-atomic="true"
        aria-live="polite"
        class="sr-only"
      >
        undefined undefined
      </div>
      <div
        aria-atomic="true"
        aria-live="off"
        class="sr-only"
      >
        On slide 1 of 3.
      </div>
      <div
        aria-hidden="false"
        class="absolute left-0 top-0 w-full transition-opacity duration-500 pointer-events-auto relative z-10 opacity-100"
        style="background-color: rgb(59, 144, 255); position: relative; width: 100%; pointer-events: auto;"
      >
        <section
          class="relative inset-0 flex w-full flex-col items-center justify-center gap-[3px] px-4 py-[10px] text-center text-white lg:grid lg:grid-cols-[1fr_minmax(auto,150px)] lg:gap-0 lg:gap-[5px] lg:px-[15px]"
          style="background-color: rgb(59, 144, 255); color: rgb(255, 255, 255);"
        >
          <div
            class="flex w-full flex-col text-left"
          >
            <div
              class="flex flex-wrap justify-start lg:pl-6 lg:pr-[8px]"
              style="padding-left: 0px;"
            >
              <h2
                class="font-bold text-sm leading-[16px] lg:text-xs lg:leading-[20px]"
              >
                Limited Time Only: Open and use your Athleta Rewards 
              </h2>
            </div>
          </div>
          <div
            class="flex w-full items-center justify-between"
          >
            <div
              aria-hidden="true"
              class="flex gap-[6px]"
              data-testid="mobile-pagination-dots"
            >
              <div
                data-testid="active-dot"
              >
                <svg
                  fill="none"
                  height="4"
                  viewBox="0 0 4 4"
                  width="4"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g>
                    <circle
                      cx="50%"
                      cy="50%"
                      fill="white"
                      r="2"
                    />
                  </g>
                </svg>
              </div>
              <div
                data-testid="inactive-dot"
              >
                <svg
                  fill="none"
                  height="4"
                  viewBox="0 0 4 4"
                  width="4"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g>
                    <circle
                      cx="50%"
                      cy="50%"
                      fill="#767676"
                      r="2"
                    />
                  </g>
                </svg>
              </div>
              <div
                data-testid="inactive-dot"
              >
                <svg
                  fill="none"
                  height="4"
                  viewBox="0 0 4 4"
                  width="4"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g>
                    <circle
                      cx="50%"
                      cy="50%"
                      fill="#767676"
                      r="2"
                    />
                  </g>
                </svg>
              </div>
            </div>
            <div
              class="flex"
            >
              <div
                class="flex flex-wrap gap-[15px]"
              >
                <div
                  class="flex flex-nowrap gap-[15px]"
                >
                  <a
                    aria-hidden="false"
                    class="z-[1] cursor-pointer text-sm underline hover:no-underline lg:text-xs lg:leading-[20px]"
                    href="/browse/athleta-girl/all-sale?cid=1073226&mlink=1,1,Site_SW_SWB_Promo_40offredwhiteblue_062425"
                    tabindex="0"
                    title="Apply Now"
                  >
                    Apply Now
                  </a>
                </div>
              </div>
              <div
                class="ml-[12px] flex flex-row flex-wrap items-baseline justify-center gap-x-[5px] gap-y-0 lg:ml-[5px] lg:mt-[5px] lg:justify-end"
              >
                <div
                  class="inline-flex flex-wrap justify-center gap-x-[5px]"
                  style="color: rgb(255, 255, 255);"
                >
                  <button
                    aria-hidden="false"
                    aria-label="*Details"
                    class="z-[1] m-0 inline-flex border-0 bg-transparent p-0 text-sm lg:text-xs"
                    tabindex="0"
                    title="*Details"
                    type="button"
                  >
                    *
                    <span
                      class="underline hover:no-underline"
                    >
                      Details
                    </span>
                  </button>
                </div>
              </div>
              <a
                aria-hidden="false"
                aria-label="Shop Now"
                class="absolute left-0 top-0 h-full w-full cursor-pointer"
                href="/browse/sale/all-sale?cid=1023728&mlink=1,1,Site_SW_SWB_Promo_40offredwhiteblue_062425"
                tabindex="0"
                title="Shop Now"
              />
            </div>
          </div>
        </section>
      </div>
      <div
        aria-hidden="true"
        class="absolute left-0 top-0 w-full transition-opacity duration-500 pointer-events-none z-0 opacity-0"
        style="background-color: rgb(48, 90, 207); position: absolute; width: 100%; pointer-events: none;"
      >
        <section
          class="relative inset-0 flex w-full flex-col items-center justify-center gap-[3px] px-4 py-[10px] text-center text-white lg:grid lg:grid-cols-[1fr_minmax(auto,150px)] lg:gap-0 lg:gap-[5px] lg:px-[15px]"
          style="background-color: rgb(48, 90, 207); color: rgb(255, 255, 255);"
        >
          <div
            class="flex w-full flex-col text-left"
          >
            <div
              class="flex flex-wrap justify-start lg:pl-6 lg:pr-[8px]"
              style="padding-left: 0px;"
            >
              <h2
                class="font-bold text-sm leading-[16px] lg:text-xs lg:leading-[20px]"
              >
                25% Off
              </h2>
            </div>
          </div>
          <div
            class="flex w-full items-center justify-between"
          >
            <div
              aria-hidden="true"
              class="flex gap-[6px]"
              data-testid="mobile-pagination-dots"
            >
              <div
                data-testid="inactive-dot"
              >
                <svg
                  fill="none"
                  height="4"
                  viewBox="0 0 4 4"
                  width="4"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g>
                    <circle
                      cx="50%"
                      cy="50%"
                      fill="#767676"
                      r="2"
                    />
                  </g>
                </svg>
              </div>
              <div
                data-testid="active-dot"
              >
                <svg
                  fill="none"
                  height="4"
                  viewBox="0 0 4 4"
                  width="4"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g>
                    <circle
                      cx="50%"
                      cy="50%"
                      fill="white"
                      r="2"
                    />
                  </g>
                </svg>
              </div>
              <div
                data-testid="inactive-dot"
              >
                <svg
                  fill="none"
                  height="4"
                  viewBox="0 0 4 4"
                  width="4"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g>
                    <circle
                      cx="50%"
                      cy="50%"
                      fill="#767676"
                      r="2"
                    />
                  </g>
                </svg>
              </div>
            </div>
            <div
              class="flex"
            >
              <div
                class="ml-[12px] flex flex-row flex-wrap items-baseline justify-center gap-x-[5px] gap-y-0 lg:ml-[5px] lg:mt-[5px] lg:justify-end"
              >
                <div
                  class="inline-flex flex-wrap justify-center gap-x-[5px]"
                  style="color: rgb(255, 255, 255);"
                />
              </div>
            </div>
          </div>
        </section>
      </div>
      <div
        aria-hidden="true"
        class="absolute left-0 top-0 w-full transition-opacity duration-500 pointer-events-none z-0 opacity-0"
        style="background-color: rgb(18, 17, 17); position: absolute; width: 100%; pointer-events: none;"
      >
        <section
          class="relative inset-0 flex w-full flex-col items-center justify-center gap-[3px] px-4 py-[10px] text-center text-white lg:grid lg:grid-cols-[1fr_minmax(auto,150px)] lg:gap-0 lg:gap-[5px] lg:px-[15px]"
          style="background-color: rgb(18, 17, 17); color: rgb(237, 219, 19);"
        >
          <div
            class="flex w-full flex-col text-left"
          >
            <div
              class="flex flex-wrap justify-start lg:pl-6 lg:pr-[8px]"
              style="padding-left: 0px;"
            >
              <h2
                class="font-normal text-sm leading-[16px] lg:text-xs lg:leading-[20px]"
              >
                third banner
              </h2>
            </div>
            <div
              class="flex justify-start"
            >
              <span
                class="font-bold text-sm leading-[16px] lg:text-xs lg:leading-[20px]"
              >
                never gonna give you up, never gonna let you down - ohhhh waaaaaa
              </span>
            </div>
          </div>
          <div
            class="flex w-full items-center justify-between"
          >
            <div
              aria-hidden="true"
              class="flex gap-[6px]"
              data-testid="mobile-pagination-dots"
            >
              <div
                data-testid="inactive-dot"
              >
                <svg
                  fill="none"
                  height="4"
                  viewBox="0 0 4 4"
                  width="4"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g>
                    <circle
                      cx="50%"
                      cy="50%"
                      fill="#767676"
                      r="2"
                    />
                  </g>
                </svg>
              </div>
              <div
                data-testid="inactive-dot"
              >
                <svg
                  fill="none"
                  height="4"
                  viewBox="0 0 4 4"
                  width="4"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g>
                    <circle
                      cx="50%"
                      cy="50%"
                      fill="#767676"
                      r="2"
                    />
                  </g>
                </svg>
              </div>
              <div
                data-testid="active-dot"
              >
                <svg
                  fill="none"
                  height="4"
                  viewBox="0 0 4 4"
                  width="4"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g>
                    <circle
                      cx="50%"
                      cy="50%"
                      fill="white"
                      r="2"
                    />
                  </g>
                </svg>
              </div>
            </div>
            <div
              class="flex"
            >
              <div
                class="ml-[12px] flex flex-row flex-wrap items-baseline justify-center gap-x-[5px] gap-y-0 lg:ml-[5px] lg:mt-[5px] lg:justify-end"
              >
                <div
                  class="inline-flex flex-wrap justify-center gap-x-[5px]"
                  style="color: rgb(237, 219, 19);"
                >
                  <button
                    aria-hidden="true"
                    aria-label="legal text"
                    class="z-[1] m-0 inline-flex border-0 bg-transparent p-0 text-sm lg:text-xs"
                    tabindex="-1"
                    title="legal text"
                    type="button"
                  >
                    <span
                      class="underline hover:no-underline"
                    >
                      legal text
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  </div>
</div>
`;
