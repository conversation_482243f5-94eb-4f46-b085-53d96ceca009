'use client';
import React, { useState, useEffect, useContext, useRef, useMemo, ReactNode } from 'react';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { BreakpointContext, XLARGE, XL_MIN } from '@ecom-next/core/breakpoint-provider';
import { Modal } from '@ecom-next/core/components/fabric/modal';
// @ts-ignore - file not listed in project
import { renderLeadingCharUnderline, getDetailsContent, Brands } from '../common-utils';
import { NO_COUNTDOWN_CLOCK, RESUMED_ANNOUNCEMENT, PAUSED_ANNOUNCEMENT, DESKTOP_INSTRUCTIONS, MOBILE_INSTRUCTIONS } from './utils';
import { GlobalBannerData, Banner } from './types';
import { useCountdownList } from './hooks';
import { ActiveBanner, InActiveBanner } from './svgs';

/**
 * GlobalBanner displays a responsive, optionally-carousel banner at the top of the page.
 *
 * Key features:
 * - Responsive: Adjusts layout and CTA visibility for mobile and desktop breakpoints.
 * - Carousel: Supports multiple banners with auto-advance and looping.
 * - Countdown timer: Shows a live countdown if configured per banner.
 * - Headline/subheadline: Supports bold styling and dynamic content.
 * - CTAs: Renders one or more call-to-action links, with mobile/desktop targeting.
 * - Legal details: Shows legal text and opens a modal for additional details if available.
 * - Overlay link: Optionally makes the entire banner clickable.
 *
 * @param serverResponse - Banner data and configuration, including carousel items and styling.
 * @returns React element for the global banner, or null if no data is provided.
 */
export default function GlobalBanner(serverResponse: GlobalBannerData) {
  const { brandAbbr, pageType, locale } = usePageContext();
  const { smallerThan } = useContext(BreakpointContext);
  const isMobile = smallerThan(XLARGE);
  const [isMobileState, setIsMobileState] = useState(isMobile);
  const [modal, setModal] = useState({
    isOpen: false,
    pemoleCode: '',
  });
  const [detailsSectionWidth, setDetailsSectionWidth] = useState(0);
  const [currentIndex, setCurrentCarouselIdx] = useState(0);
  const [touchStart, setTouchStart] = useState(0);
  const [touchEnd, setTouchEnd] = useState(0);
  const [isFocused, setIsFocused] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const minSwipeDistance = 50;

  const { siteCarousel } = serverResponse;

  const totalSlides = siteCarousel?.length;
  let paginationDots: ReactNode[] = [];
  const refillPaginationPillsArr = () => {
    paginationDots = [];
    for (let i = 0; i < totalSlides; i++) {
      paginationDots.push(<InActiveBanner key={i} />);
    }
  };

  // memoize these values to prevent recomputation on every render which can cause an infinite
  // max depth error for the endDateList useEffect as it runs every time the endDateList changes
  const endDateList = useMemo(
    () =>
      siteCarousel.map(banner =>
        banner?.banner?.countdown?.countdownTimer === 'full' ? NO_COUNTDOWN_CLOCK : banner?.banner?.countdown?.countdownTimer || NO_COUNTDOWN_CLOCK
      ),
    [siteCarousel]
  );

  const countdownList = useCountdownList(endDateList);

  const [isPlaying, setIsPlaying] = useState(true);
  const [hasMounted, setHasMounted] = useState(false);

  const sliderTrackRef = useRef<HTMLDivElement>(null);
  const detailsSectionRef = useRef<HTMLDivElement>(null);

  // this is used to only CSR the countdown timer while still using SSR for the rest of the component
  useEffect(() => {
    setHasMounted(true);
  }, []);

  useEffect(() => {
    if (!siteCarousel?.length || siteCarousel?.length <= 1) {
      return;
    }

    const interval = setInterval(() => {
      const itemCount = siteCarousel.length;
      if (isPlaying) {
        setCurrentCarouselIdx(prevIdx => (prevIdx === itemCount - 1 ? 0 : prevIdx + 1));
      }
    }, 3000);
    return () => clearInterval(interval);
  }, [isPlaying, siteCarousel.length]);

  // These useEffects below are being needed since the changes to the breakpoint context isn't triggering re-render
  // This is based on the window size directly
  useEffect(() => {
    const handleResize = () => {
      const isCurrentlyMobile = window.innerWidth < XL_MIN;
      setIsMobileState(isCurrentlyMobile);
    };

    window.addEventListener('resize', handleResize);

    handleResize();

    return () => window.removeEventListener('resize', handleResize);
  }, []); // Empty dependency array means this runs once on mount

  useEffect(() => {
    const updateWidth = () => {
      setDetailsSectionWidth(detailsSectionRef?.current?.clientWidth || 0);
    };

    updateWidth();
    window.addEventListener('resize', updateWidth);
    return () => window.removeEventListener('resize', updateWidth);
  }, []);

  if (!serverResponse) {
    return null; // Return null if no data is provided
  }

  const formatCountdown = (secs: number | null) => {
    if (secs === undefined || secs === null || isNaN(secs)) return null;
    const d = Math.floor(secs / 86400);
    const h = Math.floor((secs % 86400) / 3600);
    const m = Math.floor((secs % 3600) / 60);
    const s = secs % 60;
    const pad = (n: number) => n.toString().padStart(2, '0');
    return `${pad(h + d * 24)}:${pad(m)}:${pad(s)}`;
  };

  const updateCarouselIdx = (direction: 'prev' | 'next') => {
    const itemCount = siteCarousel?.length || 0;
    if (direction === 'next') {
      return setCurrentCarouselIdx(prevIdx => (prevIdx === itemCount - 1 ? 0 : prevIdx + 1));
    }
    return setCurrentCarouselIdx(prevIdx => (prevIdx === 0 ? itemCount - 1 : prevIdx - 1));
  };

  const sectionFrame = (
    { link, bannerColor, textColor, headline, boldHeadline, subHeadline, boldSubHeadline, ctas, legalDetails }: Banner['banner'],
    i: number
  ) => {
    refillPaginationPillsArr();
    paginationDots[i] = <ActiveBanner key={i} />;

    const headlineTitleWeight = boldHeadline ? 'font-bold' : 'font-normal';
    const subHeadlineTitleWeight = boldSubHeadline ? 'font-bold' : 'font-normal';
    const overlayLinkHref = link?.url;

    const endDateTimeStamp = endDateList[i] && endDateList[i] !== NO_COUNTDOWN_CLOCK ? new Date(endDateList[i]).getTime() : null;
    const formattedCountdown = endDateList[i] && !isNaN(endDateTimeStamp as number) ? formatCountdown(countdownList[i]) : null;

    const showCtasForMobile = isMobileState && ctas && ctas.length > 0 && ctas.map(cta => cta.showOnMobile).some(bool => bool);
    const showCtasForDesktop = !isMobileState && ctas && ctas.length > 0 && ctas.map(cta => cta.url).some(bool => bool);

    const renderedCtas =
      showCtasForDesktop || showCtasForMobile ? (
        <div className='flex flex-nowrap gap-[15px]'>
          {ctas?.map(({ title, url, mobileUrl, showOnMobile }, ctaIdx) => {
            if (isMobileState && !showOnMobile) return null;
            const ctaLinkHref = isMobileState && showOnMobile && mobileUrl ? mobileUrl : url;
            return (
              <a
                key={ctaIdx}
                className='z-[1] cursor-pointer text-sm underline hover:no-underline lg:text-xs lg:leading-[20px]'
                href={ctaLinkHref}
                title={title}
                tabIndex={i === currentIndex ? 0 : -1}
                aria-hidden={i !== currentIndex}
              >
                {title}
              </a>
            );
          })}
        </div>
      ) : null;

    return (
      <section
        className='relative inset-0 flex w-full flex-col items-center justify-center gap-[3px] px-4 py-[10px] text-center text-white lg:grid lg:grid-cols-[1fr_minmax(auto,150px)] lg:gap-0 lg:gap-[5px] lg:px-[15px]'
        key={i}
        style={{
          backgroundColor: bannerColor,
          color: textColor,
          // TODO: this hack is needed for the stores page since that was built by a third party vendor
          ...(pageType === 'store' &&
            !isMobileState && {
              display: 'grid',
              gridTemplateColumns: '1fr minmax(auto, 150px)',
              gap: 0,
              paddingLeft: '15px',
              paddingRight: '15px',
            }),
        }}
      >
        <div className={isMobileState ? 'flex w-full flex-col text-left' : ''}>
          <div
            className={`flex flex-wrap ${isMobileState ? 'justify-start' : 'justify-center'} lg:pl-6 lg:pr-[8px]`}
            style={{ paddingLeft: !isMobileState ? detailsSectionWidth : 0 }}
          >
            {formattedCountdown && (
              <span className={`${!isMobileState ? 'ml-1' : ''} mr-[15px] text-sm leading-[16px] lg:text-xs lg:leading-[20px]`}>
                <span className='font-bold'>
                  {/* \u00A0 is a non-breaking space character primarily to prevent layout shifts between SSR and CSR */}
                  {hasMounted && formattedCountdown ? formattedCountdown : '\u00A0'}
                </span>
              </span>
            )}
            {headline && <h2 className={`${headlineTitleWeight} text-sm leading-[16px] lg:text-xs lg:leading-[20px]`}>{headline}</h2>}
            {!isMobileState && subHeadline && <span className={`${subHeadlineTitleWeight} text-sm lg:ml-1 lg:text-xs lg:leading-[20px]`}>{subHeadline}</span>}
            {!isMobileState && showCtasForDesktop && <div className='flex flex-nowrap gap-[15px] lg:ml-[15px]'>{renderedCtas}</div>}
          </div>
          {isMobileState && subHeadline && (
            <div className='flex justify-start'>
              {subHeadline && <span className={`${subHeadlineTitleWeight} text-sm leading-[16px] lg:text-xs lg:leading-[20px]`}>{subHeadline}</span>}
            </div>
          )}
        </div>
        <div className={`flex w-full items-center ${siteCarousel.length > 1 && isMobileState ? 'justify-between' : 'justify-end'}`}>
          {siteCarousel.length > 1 && isMobileState && (
            <div aria-hidden='true' className='flex gap-[6px]' data-testid='mobile-pagination-dots'>
              {paginationDots}
            </div>
          )}
          <div className='flex'>
            {isMobileState && showCtasForMobile && <div className='flex flex-wrap gap-[15px]'>{renderedCtas}</div>}
            <div
              className='ml-[12px] flex flex-row flex-wrap items-baseline justify-center gap-x-[5px] gap-y-0 lg:ml-[5px] lg:mt-[5px] lg:justify-end'
              ref={detailsSectionRef}
            >
              <div className='inline-flex flex-wrap justify-center gap-x-[5px]' style={{ color: textColor }}>
                {!isMobileState && <span className='lg:text-xs'>{legalDetails?.legal?.title}</span>}
                {legalDetails?.legal?.detailsText && (
                  <>
                    <button
                      className='z-[1] m-0 inline-flex border-0 bg-transparent p-0 text-sm lg:text-xs'
                      onClick={e => {
                        e.preventDefault();

                        setModal({
                          isOpen: true,
                          pemoleCode: legalDetails?.legal?.pemoleCode || '',
                        });
                      }}
                      aria-label={legalDetails?.legal?.detailsText}
                      type='button'
                      title={legalDetails?.legal?.detailsText}
                      aria-hidden={i !== currentIndex}
                      tabIndex={i === currentIndex ? 0 : -1}
                    >
                      {renderLeadingCharUnderline(legalDetails?.legal?.detailsText, true, brandAbbr as Brands)}
                    </button>
                    <Modal
                      closeButtonAriaLabel={'close'}
                      data-testid='global-banner-iframe-modal'
                      isOpen={modal.isOpen}
                      callbackFn={() => {
                        setModal({
                          isOpen: false,
                          pemoleCode: '',
                        });
                        setIsPlaying(true);
                      }}
                      className='mui-new-video-tile__modal'
                    >
                      <iframe
                        data-testid='global-banner-modal-iframe'
                        src={getDetailsContent(brandAbbr as Brands, legalDetails?.legal?.pemoleCode, undefined, locale)}
                        title={legalDetails?.legal?.detailsText}
                      />
                    </Modal>
                  </>
                )}
              </div>
            </div>
            {overlayLinkHref && (
              <a
                href={overlayLinkHref}
                aria-label={link?.linkTitle || `a banner link that goes to ${overlayLinkHref}`}
                title={link?.linkTitle || 'banner link'}
                className='absolute left-0 top-0 h-full w-full cursor-pointer'
                aria-hidden={i !== currentIndex}
                tabIndex={i === currentIndex ? 0 : -1}
              ></a>
            )}
          </div>
        </div>
      </section>
    );
  };

  if (siteCarousel.length <= 1) {
    return siteCarousel.length === 1 ? <div className='w-full'>{sectionFrame(siteCarousel[0].banner, 0)}</div> : null;
  }

  return (
    <div
      className='relative h-full w-full overflow-x-hidden overflow-y-hidden scroll-smooth'
      onMouseEnter={() => {
        setIsPlaying(false);
        setIsHovered(true);
      }}
      onMouseLeave={() => {
        setIsPlaying(true);
        setIsHovered(false);
      }}
      onFocus={() => {
        setIsPlaying(false);
        setIsFocused(true);
      }}
      onBlur={() => {
        setIsPlaying(true);
        setIsFocused(false);
      }}
      // Add keyboard navigation
      onKeyDown={e => {
        if (e.key === 'ArrowLeft') {
          updateCarouselIdx('prev');
        } else if (e.key === 'ArrowRight') {
          updateCarouselIdx('next');
        }
      }}
      // Add touch handlers for swipe
      onTouchStart={e => setTouchStart(e.targetTouches[0].clientX)}
      onTouchMove={e => setTouchEnd(e.targetTouches[0].clientX)}
      onTouchEnd={() => {
        if (!touchStart || !touchEnd) return;
        const distance = touchStart - touchEnd;
        const isSwipe = Math.abs(distance) > minSwipeDistance;

        if (isSwipe) {
          if (distance > 0) {
            // Swiped left -> next slide
            updateCarouselIdx('next');
          } else {
            // Swiped right -> previous slide
            updateCarouselIdx('prev');
          }
        }

        // Reset values
        setTouchEnd(0);
        setTouchStart(0);
      }}
      tabIndex={0} // Add tabIndex to make container focusable for keyboard events
      role='region'
      aria-roledescription='carousel'
      aria-label='Carousel of promotional text banners'
      ref={sliderTrackRef}
      style={{
        minHeight: sliderTrackRef.current?.style.minHeight || undefined,
        backgroundColor: siteCarousel[currentIndex]?.banner?.bannerColor,
        scrollbarWidth: 'none',
      }}
    >
      {/* Add this style tag for WebKit browsers (Chrome, Safari) */}
      <style>{`
        div {
          -ms-overflow-style: none; /* IE and Edge */
        }
        div::-webkit-scrollbar {
          display: none;
        }
      `}</style>
      <div className='sr-only' aria-live='polite' aria-atomic='true'>
        {isPlaying
          ? `${RESUMED_ANNOUNCEMENT} ${isMobile ? MOBILE_INSTRUCTIONS : DESKTOP_INSTRUCTIONS}`
          : `${PAUSED_ANNOUNCEMENT} ${isMobile ? MOBILE_INSTRUCTIONS : DESKTOP_INSTRUCTIONS}`}
      </div>
      <div className='sr-only' aria-live={isFocused || isHovered ? 'polite' : 'off'} aria-atomic='true'>
        {`On slide ${currentIndex + 1} of ${siteCarousel?.length}.`}
      </div>
      {siteCarousel.map((bannerData, i) => (
        <div
          key={i}
          aria-hidden={i !== currentIndex}
          className={`absolute left-0 top-0 w-full transition-opacity duration-500 ${
            i === currentIndex ? 'pointer-events-auto relative z-10 opacity-100' : 'pointer-events-none z-0 opacity-0'
          }`}
          style={{
            backgroundColor: bannerData.banner.bannerColor,
            position: i === currentIndex ? 'relative' : 'absolute',
            width: '100%',
            pointerEvents: i === currentIndex ? 'auto' : 'none',
          }}
        >
          {sectionFrame(bannerData.banner, i)}
        </div>
      ))}
    </div>
  );
}
