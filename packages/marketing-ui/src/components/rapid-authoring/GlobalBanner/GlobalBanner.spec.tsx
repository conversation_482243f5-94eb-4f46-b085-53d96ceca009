import React from 'react';
import { AppState } from '@ecom-next/sitewide/app-state-provider';
import { render as renderWithProviders, screen, act, within } from 'test-utils';
import { Brands } from '@ecom-next/core/react-stitch';
import GlobalBanner from './GlobalBanner';
import { GlobalBannerData } from './types';
import { baseProps, mobileBannerData } from './__fixtures__/test-data';

// Mock the descriptionSchemaToHtml util
jest.mock('./utils', () => ({
  descriptionSchemaToHtml: (desc: string) => `<p>${desc}</p>`,
}));

const desktopRenderOptions = {
  appState: {
    brandName: Brands.Athleta,
    brandAbbr: Brands.Athleta,
  } as unknown as AppState,
  breakpoint: 'large',
} as any;

const desktopStorePageRenderOptions = {
  appState: {
    brandName: Brands.Athleta,
    brandAbbr: Brands.Athleta,
    pageType: 'store',
  } as unknown as AppState,
  breakpoint: 'large',
} as any;

const mobileRenderOptions = {
  appState: {
    brandName: Brands.Athleta,
    brandAbbr: Brands.Athleta,
  } as unknown as AppState,
  breakpoint: 'small',
} as any;

describe('GlobalBanner', () => {
  beforeEach(() => {
    // Mock timers for countdown functionality
    jest.useFakeTimers();
    act(() => {
      jest.advanceTimersByTime(1000); // Fast-forward time to ensure countdown starts correctly
    });
  });

  afterEach(() => {
    jest.clearAllTimers();
    jest.clearAllMocks();
    jest.useRealTimers();
  });

  it('matches snapshots', () => {
    const { container } = renderWithProviders(<GlobalBanner {...baseProps} />, desktopRenderOptions);
    expect(container.firstChild).toMatchSnapshot();
  });

  it('matches snapshot for the stores page on desktop', () => {
    const { container } = renderWithProviders(<GlobalBanner {...baseProps} />, desktopStorePageRenderOptions);
    expect(container).toMatchSnapshot();
    const section = container.querySelector('section');
    expect(section).toHaveStyle({
      display: 'grid',
      gridTemplateColumns: '1fr minmax(auto, 150px)',
      gap: 0,
      paddingLeft: '15px',
      paddingRight: '15px',
    });
  });

  it('renders headline and subheadline', () => {
    renderWithProviders(<GlobalBanner {...baseProps} />);
    expect(screen.getByText('Welcome to Our Store')).toBeInTheDocument();
  });

  it('renders call-to-action buttons', () => {
    renderWithProviders(<GlobalBanner {...baseProps} />);
    expect(screen.getByText('Shop Now')).toBeInTheDocument();
    expect(screen.getByText('Learn More')).toBeInTheDocument();
  });

  it('renders legal details if provided', () => {
    renderWithProviders(<GlobalBanner {...baseProps} />);
    expect(screen.getByText('Terms apply')).toBeInTheDocument();
    expect(screen.getByText('See details')).toBeInTheDocument();
  });

  it('does not render legal details if not provided', () => {
    const propsWithoutLegal = {
      ...baseProps,
      siteCarousel: [
        {
          banner: {
            ...baseProps.siteCarousel[0].banner,
            legalDetails: undefined, // No legal details
          },
        },
      ],
    };
    renderWithProviders(<GlobalBanner {...propsWithoutLegal} />);
    expect(screen.queryByText('Terms apply')).not.toBeInTheDocument();
    expect(screen.queryByText('See details')).not.toBeInTheDocument();
  });

  it('renders countdown timer if provided', () => {
    renderWithProviders(<GlobalBanner {...baseProps} />);
    expect(screen.getByText('00:00:00')).toBeInTheDocument(); // Initial countdown display
  });

  it('does not render countdown timer if not provided', () => {
    const propsWithoutCountdown = {
      ...baseProps,
      siteCarousel: [
        {
          banner: {
            ...baseProps.siteCarousel[0].banner,
            countdown: undefined, // No countdown
          },
        },
      ],
    };
    renderWithProviders(<GlobalBanner {...propsWithoutCountdown} />);
    expect(screen.queryByText('00:00:00')).not.toBeInTheDocument();
  });

  it('renders nothing if siteCarousel is empty', () => {
    const propsWithEmptyCarousel = {
      ...baseProps,
      siteCarousel: [],
    };
    const { container } = renderWithProviders(<GlobalBanner {...propsWithEmptyCarousel} />);
    expect(container.firstChild?.firstChild).toBeNull();
  });

  it('renders only the first banner if siteCarousel has one item', () => {
    const { container } = renderWithProviders(<GlobalBanner {...baseProps} />);
    expect(container.querySelectorAll('section').length).toBe(1);
    expect(screen.getByText('Welcome to Our Store')).toBeInTheDocument();
  });

  it('renders carousel with multiple banners', () => {
    const multiBannerProps = {
      ...baseProps,
      siteCarousel: [
        ...baseProps.siteCarousel,
        {
          banner: {
            ...baseProps.siteCarousel[0].banner,
            headline: 'Second Banner',
            subHeadline: 'Second subheadline',
            ctas: [{ title: 'Second CTA', url: '/second', mobileUrl: '/second', showOnMobile: true }],
          },
        },
      ],
    };
    renderWithProviders(<GlobalBanner {...multiBannerProps} />);
    expect(screen.getByText('Welcome to Our Store')).toBeInTheDocument();
  });

  it('does not render the pagination dots for desktop', () => {
    const multiBannerProps = {
      ...baseProps,
      siteCarousel: [
        ...baseProps.siteCarousel,
        {
          banner: {
            ...baseProps.siteCarousel[0].banner,
            headline: 'Second Banner',
            subHeadline: 'Second subheadline',
            ctas: [{ title: 'Second CTA', url: '/second', mobileUrl: '/second', showOnMobile: true }],
          },
        },
      ],
    };

    renderWithProviders(<GlobalBanner {...multiBannerProps} />, desktopRenderOptions);

    const pagDots = screen.queryByTestId('mobile-pagination-dots');
    expect(pagDots).not.toBeInTheDocument();
  });

  it('shows overlay link if link is provided', () => {
    const propsWithLink = {
      ...baseProps,
      siteCarousel: [
        {
          banner: {
            ...baseProps.siteCarousel[0].banner,
            link: { url: '/promo', linkTitle: 'Promo Link' },
          },
        },
      ],
    };
    renderWithProviders(<GlobalBanner {...propsWithLink} />);
    const overlayLink = screen.getByTitle('Promo Link');
    expect(overlayLink).toBeInTheDocument();
    expect(overlayLink).toHaveAttribute('href', '/promo');
  });

  it('does not render overlay link if link is not provided', () => {
    renderWithProviders(<GlobalBanner {...baseProps} />);
    expect(screen.queryByRole('link', { name: /banner link/i })).not.toBeInTheDocument();
  });

  it('opens modal with iframe when legal details button is clicked', () => {
    renderWithProviders(<GlobalBanner {...baseProps} />);
    const detailsButton = screen.getByRole('button', { name: 'See details' });
    expect(detailsButton).toBeInTheDocument();
    act(() => {
      detailsButton.click();
    });
    expect(document.getElementById('modal-header-container')).toBeInTheDocument();
  });

  it('closes modal when callbackFn is called', () => {
    renderWithProviders(<GlobalBanner {...baseProps} />);
    const detailsButton = screen.getByRole('button', { name: 'See details' });
    act(() => {
      detailsButton.click();
    });
    const closeButton = screen.getByLabelText('close');
    act(() => {
      closeButton.dispatchEvent(new MouseEvent('click', { bubbles: true }));
    });
    expect(document.getElementById('modal-header-container')).not.toBeInTheDocument();
  });

  it('renders bold headline and subheadline when specified', () => {
    const propsWithBold = {
      ...baseProps,
      siteCarousel: [
        {
          banner: {
            ...baseProps.siteCarousel[0].banner,
            boldHeadline: true,
            boldSubHeadline: true,
          },
        },
      ],
    };
    renderWithProviders(<GlobalBanner {...propsWithBold} />);
    const headline = screen.getByText('Welcome to Our Store');
    expect(headline).toHaveClass('font-bold');
  });

  it('renders correct CTA links for mobile', () => {
    // Simulate mobile
    global.innerWidth = 500;
    act(() => {
      global.dispatchEvent(new Event('resize'));
    });
    renderWithProviders(<GlobalBanner {...baseProps} />);
    expect(screen.getByText('Shop Now')).toBeInTheDocument();
  });

  it('does not render mobile CTA if showOnMobile is false', () => {
    const propsWithoutMobileCTA = {
      ...baseProps,
      siteCarousel: [
        {
          banner: {
            ...baseProps.siteCarousel[0].banner,
            ctas: [{ title: 'Shop Now', url: '/shop', mobileUrl: '/shop', showOnMobile: false }],
          },
        },
      ],
    };
    // Simulate mobile
    global.innerWidth = 500;
    act(() => {
      global.dispatchEvent(new Event('resize'));
    });
    renderWithProviders(<GlobalBanner {...propsWithoutMobileCTA} />);
    expect(screen.queryByText('Shop Now')).not.toBeInTheDocument();
  });

  it('renders correct text color and background color', () => {
    const propsWithColors = {
      ...baseProps,
      siteCarousel: [
        {
          banner: {
            ...baseProps.siteCarousel[0].banner,
            bannerColor: '#123456',
            textColor: '#abcdef',
          },
        },
      ],
    };
    renderWithProviders(<GlobalBanner {...propsWithColors} />);
    const section = screen.getByText('Welcome to Our Store').closest('section');
    expect(section).toHaveStyle({ backgroundColor: '#123456', color: '#abcdef' });
  });

  it('renders no CTAs if ctas is undefined', () => {
    const propsNoCtas = {
      ...baseProps,
      siteCarousel: [
        {
          banner: {
            ...baseProps.siteCarousel[0].banner,
            ctas: undefined,
          },
        },
      ],
    };
    renderWithProviders(<GlobalBanner {...propsNoCtas} />);
    expect(screen.queryByText('Shop Now')).not.toBeInTheDocument();
    expect(screen.queryByText('Learn More')).not.toBeInTheDocument();
  });

  it('renders subheadline if provided', () => {
    renderWithProviders(<GlobalBanner {...baseProps} />);
    expect(screen.getByText('Discover our latest collections')).toBeInTheDocument();
  });

  it('does not render subheadline if not provided', () => {
    const propsNoSub = {
      ...baseProps,
      siteCarousel: [
        {
          banner: {
            ...baseProps.siteCarousel[0].banner,
            subHeadline: undefined,
          },
        },
      ],
    };
    renderWithProviders(<GlobalBanner {...propsNoSub} />);
    expect(screen.queryByText('Discover our latest collections')).not.toBeInTheDocument();
  });

  describe('mobile', () => {
    it('should render the expected snapshot', () => {
      const { container } = renderWithProviders(<GlobalBanner {...mobileBannerData} />, mobileRenderOptions);
      // Simulate mobile
      global.innerWidth = 500;
      act(() => {
        global.dispatchEvent(new Event('resize'));
      });

      expect(container).toMatchSnapshot();
    });

    it('does not render the pagination dots for a single frame', () => {
      const clonedMobileData: GlobalBannerData = JSON.parse(JSON.stringify(mobileBannerData));
      clonedMobileData.siteCarousel.splice(1, 2);
      // Simulate mobile
      global.innerWidth = 500;
      act(() => {
        global.dispatchEvent(new Event('resize'));
      });
      renderWithProviders(<GlobalBanner {...clonedMobileData} />, mobileRenderOptions);

      const pagDots = screen.queryByTestId('mobile-pagination-dots');
      expect(pagDots).not.toBeInTheDocument();
    });

    it('does render the pagination dots for each banner when there are multiple banners', () => {
      renderWithProviders(<GlobalBanner {...mobileBannerData} />, mobileRenderOptions);
      // Simulate mobile
      global.innerWidth = 500;
      act(() => {
        global.dispatchEvent(new Event('resize'));
      });

      const pagDots = screen.getAllByTestId('mobile-pagination-dots');
      expect(pagDots.length).toBe(mobileBannerData.siteCarousel.length);
    });

    it('renders the same number of dots for each banner as there are banners', async () => {
      renderWithProviders(<GlobalBanner {...mobileBannerData} />, mobileRenderOptions);
      // Simulate mobile
      global.innerWidth = 500;
      act(() => {
        global.dispatchEvent(new Event('resize'));
      });

      const pagDotContainers = screen.getAllByTestId('mobile-pagination-dots');

      for (const container of pagDotContainers) {
        const svgs = await within(container).findAllByTestId(/^(active-dot|inactive-dot)$/);
        expect(svgs.length).toBe(3);
      }
    });

    it('initially only renders one active pagination dot at any one point and its position is the same position as the banner being currently displayed', async () => {
      renderWithProviders(<GlobalBanner {...mobileBannerData} />, mobileRenderOptions);
      // Simulate mobile
      global.innerWidth = 500;
      act(() => {
        global.dispatchEvent(new Event('resize'));
      });

      const expectedCurrentBannerIndex = 0;

      const currentBannerPagDotContainer = screen.getAllByTestId('mobile-pagination-dots')[expectedCurrentBannerIndex];
      expect(currentBannerPagDotContainer).toBeVisible();

      // Find all dots (both active and inactive)
      const allDots = await within(currentBannerPagDotContainer).findAllByTestId(/dot/i);
      expect(allDots.length).toBe(3);
      const activeIndex = allDots.findIndex(dot => dot.dataset.testid === 'active-dot');
      expect(activeIndex).toBe(expectedCurrentBannerIndex);
    });

    it('renders the expected pagination dots state based after the carousel transitioned to a different banner', async () => {
      renderWithProviders(<GlobalBanner {...mobileBannerData} />, mobileRenderOptions);
      // Simulate mobile
      global.innerWidth = 500;
      act(() => {
        global.dispatchEvent(new Event('resize'));
      });

      jest.useFakeTimers();
      // Advance timers by 3 seconds to let the carousel transition to the second banner
      jest.advanceTimersByTime(3000);
      // Wait for any pending promises to resolve
      await act(async () => {
        await Promise.resolve();
      });

      const expectedCurrentBannerIndex = 1;

      const currentBannerPagDotContainer = screen.getAllByTestId('mobile-pagination-dots')[expectedCurrentBannerIndex];
      expect(currentBannerPagDotContainer).toBeVisible();

      // Find all dots (both active and inactive)
      const allDots = await within(currentBannerPagDotContainer).findAllByTestId(/dot/i);
      expect(allDots.length).toBe(3);
      // Get the index of the active dot (should match current banner index)
      const activeIndex = allDots.findIndex(dot => dot.dataset.testid === 'active-dot');
      expect(activeIndex).toBe(expectedCurrentBannerIndex);
    });
  });
});
