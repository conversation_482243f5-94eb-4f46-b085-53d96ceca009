// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`NewVideoTile should render the component 1`] = `
<div>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      style="padding-bottom: 20px; width: 100%;"
    >
      <div
        class="relative grid w-full cursor-pointer"
        style="grid-template-columns: 1fr; grid-template-rows: 1fr;"
      >
        <img
          alt="a large brick house with a black roof and white trim"
          class="col-start-1 row-start-1 w-full"
          fetchpriority="high"
          src="https://dam-test.gap.orangelogic.com/AssetLink/7mj287dj28dal8160s8knwnwp5ow0qho.webp"
        />
        <div
          class="absolute left-0 top-0 col-start-1 row-start-1 h-full w-full"
        >
          <video
            aria-label="a large brick house with a black roof and white trim"
            autoplay=""
            class="h-full w-full object-cover"
            loop=""
            playsinline=""
            poster="https://dam-test.gap.orangelogic.com/AssetLink/7mj287dj28dal8160s8knwnwp5ow0qho.webp"
            preload="auto"
            tabindex="0"
          >
            <source
              src="https://dam-test.gap.orangelogic.com/AssetLink/v5445hb8hyowy08550ysm67634cn14kw.mp4"
              type="video/mp4"
            />
            Your browser does not support the video tag.
          </video>
          <div
            class="pointer-events-none absolute bottom-0 left-0 h-full w-full"
          >
            <button
              aria-label="Pause"
              class="pointer-events-auto absolute z-30 block cursor-pointer border-none bg-transparent opacity-65"
              style="bottom: 10px; left: 15px;"
            >
              <svg
                aria-label="pause-active-button"
                fill="none"
                height="24"
                viewBox="0 0 24 24"
                width="24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                  fill="#FFFFFF"
                />
                <path
                  d="M7.5 7.5C7.5 7.10218 7.65804 6.72064 7.93934 6.43934C8.22064 6.15804 8.60218 6 9 6C9.39782 6 9.77936 6.15804 10.0607 6.43934C10.342 6.72064 10.5 7.10218 10.5 7.5V16.5C10.5 16.8978 10.342 17.2794 10.0607 17.5607C9.77936 17.842 9.39782 18 9 18C8.60218 18 8.22064 17.842 7.93934 17.5607C7.65804 17.2794 7.5 16.8978 7.5 16.5V7.5ZM13.5 7.5C13.5 7.10218 13.658 6.72064 13.9393 6.43934C14.2206 6.15804 14.6022 6 15 6C15.3978 6 15.7794 6.15804 16.0607 6.43934C16.342 6.72064 16.5 7.10218 16.5 7.5V16.5C16.5 16.8978 16.342 17.2794 16.0607 17.5607C15.7794 17.842 15.3978 18 15 18C14.6022 18 14.2206 17.842 13.9393 17.5607C13.658 17.2794 13.5 16.8978 13.5 16.5V7.5Z"
                  fill="#2C2824"
                />
              </svg>
            </button>
            <div
              class="pointer-events-none absolute bottom-[10px] flex w-full justify-center"
            >
              <button
                aria-label="*Details"
                class="block  pointer-events-auto text-wh border-none shadow-none"
                style="width: 137px; height: 16px;"
                title="*Details"
                type="button"
              >
                *
                <span
                  class="underline hover:no-underline"
                >
                  Details
                </span>
              </button>
            </div>
          </div>
        </div>
        <img
          class="z-[2] col-start-1 row-start-1 w-full"
          src="https://dam-test.gap.orangelogic.com/AssetLink/85438f6jkyb3uwvlf7rnfwor3fsm1516.svg"
        />
      </div>
    </div>
  </div>
</div>
`;
