'use client';
import React, { useState, useContext, useRef, useEffect } from 'react';
import { BreakpointContext, XLARGE } from '@ecom-next/core/breakpoint-provider';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { Modal } from '@ecom-next/core/components/fabric/modal';
import { renderLeadingCharUnderline, getDetailsContent, setAuthorableBottomPadding } from '../common-utils';
import { PauseActive, UnmuteIcon, MuteIcon, PlayActive } from './icons';
import { VideoTileContent, Brands } from './utils';

/**
 * A responsive video tile component for marketing use, supporting autoplay, mute/unmute, play/pause,
 * and legal modal overlays. Displays a fallback image, SVG overlay, and handles both desktop and mobile layouts.
 *
 * @param serverResponse - The video tile content and configuration, including video sources, styling, links, and feature flags.
 * @returns A React component rendering a video tile with interactive controls and optional legal modal.
 *
 * @remarks
 * - Uses context from `BreakpointContext` to determine mobile/desktop rendering.
 * - Handles video playback, mute state, and overlays for legal information.
 * - Integrates with legacy modal and icon components.
 * - Supports feature flag for debug grid overlay.
 */
const NewVideoTile = (serverResponse: VideoTileContent) => {
  const { brandAbbr, locale } = usePageContext();
  const {
    video,
    styling,
    link,
    // @ts-ignore
    featureFlags,
    rightAlignControls = false,
  } = serverResponse;

  const [isSafari, setIsSafari] = useState(false);
  const [videoLoaded, setVideoLoaded] = useState(false);
  const [isMuted, setIsMuted] = useState(true);
  const [displaySoundIcon, setDisplaySoundIcon] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [modal, setModal] = useState({
    isOpen: false,
    pemoleCode: '',
  });
  const [showDebug] = useState(featureFlags?.['omni-show-hotzone-grid'] || false);

  const videoRef = useRef<HTMLVideoElement | null>(null);

  const { smallerThan } = useContext(BreakpointContext);
  const isMobile = smallerThan(XLARGE);
  const autoplay = true; // Autoplay is always true, but muted is required for mobile
  const [isPlaying, setIsPlaying] = useState(autoplay);

  const [isIntersecting, setIsIntersecting] = useState(false);

  const videoData = isMobile ? video?.mobile : video?.desktop;
  const videoUrl = videoData?.video?.url;

  const fallbackImage = videoData?.fallbackImage;
  const svgOverlay = videoData?.svgOverlay;
  const altText = videoData?.altText;
  const svgAltText = videoData?.overlayAltText;
  const fallbackUrl = `${fallbackImage?.url}`;
  const imgWidth = fallbackImage?.dimensions?.[0]?.width;
  const imgHeight = fallbackImage?.dimensions?.[0]?.height;
  const legal = link?.legal || {};

  useEffect(() => {
    // Safari detection (client-side only)
    const ua = typeof window !== 'undefined' ? window.navigator.userAgent : '';
    const safari = /^((?!chrome|android).)*safari/i.test(ua);
    setIsSafari(safari);
  }, []);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);

    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);

    return () => {
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
    };
  }, [videoRef, setIsPlaying]);

  useEffect(() => {
    const observer = new window.IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
        if (entry.isIntersecting && videoRef.current) {
          videoRef.current.play();
        }
        if (!entry.isIntersecting && videoRef.current) {
          videoRef.current.pause();
        }
      },
      { threshold: 0.25 }
    );
    if (videoRef.current) {
      observer.observe(videoRef.current);
    }
    return () => {
      if (videoRef.current) {
        observer.unobserve(videoRef.current);
      }
    };
  }, [videoRef]);

  useEffect(() => {
    setVideoLoaded(true);
  }, []);

  // Handle play/pause
  const togglePlay = () => {
    const video = videoRef.current;
    if (!video) return;
    if (video.paused) {
      video.play();
      setIsPlaying(true);
    } else {
      video.pause();
      setIsPlaying(false);
    }
  };

  const toggleMute = () => {
    const video = videoRef.current;
    if (!video) return;
    video.muted = !isMuted;
    setIsMuted(!isMuted);
  };

  const handleVideoClick = () => {
    if (!link?.linkTypeValue) return;
    const isAbsolute = /^https?:\/\//i.test(link.linkTypeValue);
    const url = isAbsolute ? link.linkTypeValue : `${window.location.origin}${link.linkTypeValue}`;
    if (isAbsolute) {
      window.open(url, '_blank', 'noopener,noreferrer');
    } else {
      window.location.href = url;
    }
  };

  useEffect(() => {
    if (!isMobile || !videoRef.current) return;

    const video = videoRef.current;
    const handleLoadedData = () => {
      video.play();
      setVideoLoaded(true);
    };

    video.addEventListener('loadeddata', handleLoadedData);

    return () => {
      video.removeEventListener('loadeddata', handleLoadedData);
    };
  }, [isMobile, videoRef]);

  return videoData && videoUrl ? (
    <div
      style={{
        ...setAuthorableBottomPadding(styling, isMobile),
        width: '100%',
      }}
    >
      <div
        className='relative grid w-full cursor-pointer'
        style={{
          gridTemplateColumns: '1fr',
          gridTemplateRows: '1fr',
          aspectRatio: `${imgWidth} / ${imgHeight}`,
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <img src={fallbackUrl} alt={altText} fetchPriority='high' className='col-start-1 row-start-1 w-full' />
        <div
          className='absolute left-0 top-0 col-start-1 row-start-1 h-full w-full'
          style={{
            aspectRatio: `${imgWidth} / ${imgHeight}`,
          }}
        >
          <video
            key={videoUrl}
            ref={videoRef}
            {...(!isSafari ? { controls: false } : {})}
            className='h-full w-full object-cover'
            style={{
              aspectRatio: `${imgWidth} / ${imgHeight}`,
            }}
            onClick={handleVideoClick}
            preload='auto'
            poster={fallbackUrl}
            autoPlay={autoplay}
            loop
            playsInline
            muted={isMuted}
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
            tabIndex={0}
            aria-label={altText || 'Video player'}
          >
            <source src={videoUrl} type='video/mp4' />
            Your browser does not support the video tag.
          </video>
          {/* Controls container */}
          <div className='pointer-events-none absolute bottom-0 left-0 h-full w-full'>
            {videoUrl && videoLoaded && (
              <button
                aria-label={isPlaying ? 'Pause' : 'Play'}
                onClick={togglePlay}
                className='pointer-events-auto absolute z-30 block cursor-pointer border-none bg-transparent opacity-65'
                style={{
                  bottom: 10,
                  ...(!rightAlignControls && { left: isMobile ? 'auto' : 15 }),
                  right: isMobile || rightAlignControls ? 15 : 'auto',
                }}
              >
                {isPlaying ? <PauseActive /> : <PlayActive />}
              </button>
            )}
            {videoUrl && videoLoaded && displaySoundIcon && (
              <button
                aria-label={isMuted ? 'Unmute' : 'Mute'}
                onClick={toggleMute}
                className='pointer-events-auto absolute z-30 cursor-pointer border-none bg-transparent'
                style={{
                  bottom: 30,
                  ...(!rightAlignControls && { left: isMobile ? 'auto' : 60 }),
                  right: isMobile || rightAlignControls ? 50 : 'auto',
                }}
              >
                {isMuted ? <MuteIcon /> : <UnmuteIcon />}
              </button>
            )}
            {legal?.pemoleCode && (
              <>
                <div className='pointer-events-none absolute bottom-[10px] flex w-full justify-center'>
                  <button
                    className={`block ${showDebug ? 'border border-black' : ''} pointer-events-auto ${legal?.textColor === 'dark' ? 'text-bk' : 'text-wh'} border-none shadow-none`}
                    style={{
                      width: 137,
                      height: 16,
                    }}
                    onClick={e => {
                      e.preventDefault();

                      setModal({
                        isOpen: true,
                        pemoleCode: legal?.pemoleCode || '',
                      });
                    }}
                    aria-label={legal?.detailsText}
                    type='button'
                    title={legal?.detailsText}
                  >
                    {renderLeadingCharUnderline(legal?.detailsText, undefined, brandAbbr as Brands)}
                  </button>
                </div>
                <Modal
                  closeButtonAriaLabel={'close'}
                  data-testid='default-modal'
                  isOpen={modal.isOpen}
                  callbackFn={() => {
                    setModal({
                      isOpen: false,
                      pemoleCode: '',
                    });
                  }}
                  className='mui-new-video-tile__modal'
                >
                  <iframe
                    data-testid='iframeModal'
                    src={getDetailsContent(brandAbbr as Brands, legal?.pemoleCode, undefined, locale)}
                    title={legal?.detailsText}
                  />
                </Modal>
              </>
            )}
          </div>
        </div>
        <img src={svgOverlay?.url} alt={svgAltText} onClick={handleVideoClick} className='z-[2] col-start-1 row-start-1 w-full' />
      </div>
    </div>
  ) : null;
};

export default NewVideoTile;
