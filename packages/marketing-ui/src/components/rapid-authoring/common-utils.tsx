import React from 'react';

// This function renders a string with its leading non-alphanumeric character unstyled
// and the rest underlined, or returns the text as-is if no such character exists.
export function renderLeadingCharUnderline(text?: string, activeUnderlineEffect?: boolean, brand?: Brands) {
  if (!text) return null;

  const specialCharMatch = text.match(/^[^a-zA-Z0-9]/);
  if (!specialCharMatch && brand !== Brands.Athleta) {
    return text;
  }

  const specialChar = specialCharMatch ? specialCharMatch[0] : undefined;
  const detailsText = specialCharMatch ? text.slice(1).trimStart() : text;

  const className = `underline ${activeUnderlineEffect || brand === Brands.Athleta ? 'hover:no-underline' : ''}`;

  return (
    <>
      {specialChar}
      <span className={className}>{detailsText}</span>
    </>
  );
}

export function getDetailsContent(brand: Brands, pemoleCode?: string, htmlModalUrl?: string, locale?: 'en_US' | 'en_CA' | 'fr_CA'): string {
  if (htmlModalUrl) return htmlModalUrl;

  const isCA = locale ? locale.includes('CA') : false;
  const isUS = locale === 'en_US';

  const branded = [Brands.Athleta, Brands.BananaRepublic, Brands.OldNavy].includes(brand);
  const urlSuffix: Partial<Record<Brands, string>> = {
    [Brands.Athleta]: 'athleta',
    [Brands.BananaRepublic]: 'bananarepublic',
    [Brands.OldNavy]: 'oldnavy',
  };

  if (branded && (isUS || isCA)) {
    const domain = isCA ? 'gapcanada.ca' : 'gap.com';
    return `https://secure-${urlSuffix[brand]}.${domain}/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=${pemoleCode}`;
  }

  if (isCA) {
    return `https://www.gapcanada.ca/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=${pemoleCode}`;
  }

  // Default to US
  return `https://www.gap.com/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=${pemoleCode}`;
}

export enum Brands {
  Athleta = 'at',
  BananaRepublic = 'br',
  BananaRepublicFactoryStore = 'brfs',
  Gap = 'gap',
  GapFactoryStore = 'gapfs',
  OldNavy = 'on',
}

interface StyleData {
  padding: {
    desktop: number;
    mobile: number;
  };
}

export const setAuthorableBottomPadding = (styleData: StyleData, isMobile?: boolean) => {
  const paddingBottom = styleData?.padding?.[isMobile ? 'mobile' : 'desktop'];
  return {
    paddingBottom: `${paddingBottom}px`,
  };
};
