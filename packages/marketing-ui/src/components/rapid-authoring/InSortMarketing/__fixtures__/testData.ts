import { InSortMarketingData } from '../types';

export const sampleInSortMarketingData: InSortMarketingData = {
  _meta: {
    name: 'Spring Campaign',
    schema: 'inSortMarketingSchema',
    deliveryId: 'abc123',
  },
  desktop: {
    content: [
      {
        title: 'Spring Collection Launch',
        type: 'imageTile',
        carouselSettings: {
          autoplay: true,
          looping: 'infinite',
          rotationTime: 2000,
          transitionTime: 0,
          transitionType: 'fade',
        },
        imageTabs: {
          desktopImage: {
            url: 'https://dam-test.gap.orangelogic.com/IIIF3/Image/0gvgd466035075b1gqc7372pjl6ua70e/full/max/0.0/default.jpg',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/IIIF3/Image/0gvgd466035075b1gqc7372pjl6ua70e/full/max/0.0/default.jpg',
                width: 344,
                height: 615,
              },
            ],
            hotspots: [
              {
                type: 'url',
                coordinates: {
                  x1: 0,
                  x2: 100,
                  y1: 2,
                  y2: 100,
                },
                url: 'https://athleta.gap.com/browse/new/resort?cid=1022034&mlink=1,1,HP_Spotlight_CTA1',
                name: 'Go Short',
              },
            ],
          },
          image: {
            url: 'https://cdn.media.amplience.net/i/athleta/2d1_must_have_links_with_ease_v2_mobile?fmt=auto',
            dimensions: [
              {
                usage: 'image',
                type: 'https://cdn.media.amplience.net/i/athleta/2d1_must_have_links_with_ease_v2_mobile?fmt=auto',
                width: 336,
                height: 408,
              },
            ],
          },
          desktopAccessibilityAltText: 'Desktop image alt text',
          accessibilityAltText: 'Mobile image alt text',
          desktopCarousel: [
            {
              desktopImage: {
                url: 'https://dam-test.gap.orangelogic.com/IIIF3/Image/0gvgd466035075b1gqc7372pjl6ua70e/full/max/0.0/default.jpg',
                dimensions: [
                  {
                    usage: 'image',
                    type: 'https://dam-test.gap.orangelogic.com/IIIF3/Image/0gvgd466035075b1gqc7372pjl6ua70e/full/max/0.0/default.jpg',
                    width: 344,
                    height: 615,
                  },
                ],
                hotspots: [
                  {
                    type: 'url',
                    coordinates: {
                      x1: 0,
                      x2: 100,
                      y1: 2,
                      y2: 100,
                    },
                    url: 'https://athleta.gap.com/browse/new/resort?cid=1022034&mlink=1,1,HP_Spotlight_CTA1',
                    name: 'Go Short',
                  },
                ],
              },
              desktopCarouselAltText: 'Carousel desktop image 1',
            },
            {
              desktopImage: {
                url: 'https://cdn.media.amplience.net/i/athleta/2d1_must_have_links_with_ease_v2_mobile?fmt=auto',
                dimensions: [
                  {
                    usage: 'image',
                    type: 'https://cdn.media.amplience.net/i/athleta/2d1_must_have_links_with_ease_v2_mobile?fmt=auto',
                    width: 344,
                    height: 615,
                  },
                ],
                hotspots: [
                  {
                    type: 'url',
                    coordinates: {
                      x1: 0,
                      x2: 100,
                      y1: 2,
                      y2: 100,
                    },
                    url: 'https://athleta.gap.com/browse/new/resort?cid=1022034&mlink=1,1,HP_Spotlight_CTA1',
                    name: 'Go Short',
                  },
                ],
              },
              desktopCarouselAltText: 'Carousel desktop image 2',
            },
          ],
          mobileCarousel: [
            {
              mobileImage: {
                url: 'https://cdn.media.amplience.net/i/athleta/2d1_must_have_links_with_ease_v2_mobile?fmt=auto',
                dimensions: [
                  {
                    usage: 'image',
                    type: 'https://cdn.media.amplience.net/i/athleta/2d1_must_have_links_with_ease_v2_mobile?fmt=auto',
                    width: 336,
                    height: 408,
                  },
                ],
              },
              mobileCarouselAltText: 'a woman in a blue shirt is doing a yoga pose',
            },
            {
              mobileImage: {
                url: 'https://cdn.media.amplience.net/i/athleta/HOL1_S_TEST_F2?fmt=auto',
                dimensions: [
                  {
                    usage: 'image',
                    type: 'https://cdn.media.amplience.net/i/athleta/HOL1_S_TEST_F2?fmt=auto',
                    width: 750,
                    height: 1334,
                  },
                ],
              },
              mobileCarouselAltText: 'two women walking down a street together, one with a bag',
            },
            {
              mobileImage: {
                url: 'https://cdn.media.amplience.net/i/athleta/S%20D1_F1_HP_Outerwear_S?fmt=auto',
                dimensions: [
                  {
                    usage: 'image',
                    type: 'https://cdn.media.amplience.net/i/athleta/S%20D1_F1_HP_Outerwear_S?fmt=auto',
                    width: 750,
                    height: 1334,
                  },
                ],
              },
              mobileCarouselAltText: 'a woman in a vest and pants standing in front of a wall',
            },
          ],
        },
        video: {
          desktop: {
            fallbackImage: {
              url: 'https://dam-test.gap.orangelogic.com/AssetLink/7mj287dj28dal8160s8knwnwp5ow0qho.webp',
              dimensions: [
                {
                  usage: 'image',
                  type: 'https://dam-test.gap.orangelogic.com/AssetLink/7mj287dj28dal8160s8knwnwp5ow0qho.webp',
                  width: 2880,
                  height: 1300,
                },
              ],
            },
            video: {
              url: 'https://dam-test.gap.orangelogic.com/AssetLink/v5445hb8hyowy08550ysm67634cn14kw.mp4',
            },
            altText: 'a large brick house with a black roof and white trim',
            svgOverlay: {
              url: 'https://dam-test.gap.orangelogic.com/AssetLink/85438f6jkyb3uwvlf7rnfwor3fsm1516.svg',
              dimensions: [
                {
                  usage: 'image',
                  type: 'https://dam-test.gap.orangelogic.com/AssetLink/85438f6jkyb3uwvlf7rnfwor3fsm1516.svg',
                  width: 1440,
                  height: 635,
                },
              ],
            },
          },
          mobile: {
            fallbackImage: {
              url: 'https://dam-test.gap.orangelogic.com/AssetLink/k3c5cd7ykfqjaf8l4cj100ckf330m7l2.webp',
              dimensions: [
                {
                  usage: 'image',
                  type: 'https://dam-test.gap.orangelogic.com/AssetLink/k3c5cd7ykfqjaf8l4cj100ckf330m7l2.webp',
                  width: 750,
                  height: 1000,
                },
              ],
            },
            svgOverlay: {
              url: 'https://dam-test.gap.orangelogic.com/AssetLink/5c4x3jfi483nk4218708033bre3owp46.svg',
              dimensions: [
                {
                  usage: 'image',
                  type: 'https://dam-test.gap.orangelogic.com/AssetLink/5c4x3jfi483nk4218708033bre3owp46.svg',
                  width: 375,
                  height: 485,
                },
              ],
            },
            video: {
              url: 'https://dam-test.gap.orangelogic.com/AssetLink/3lv5ybh0c2jp61iexj22ag32ly1o0o33.mp4',
            },
            overlayAltText: 'Transparent SVG Text',
            altText: 'a woman in a purple crop top posing for a picture',
          },
        },
      },
    ],
  },
  mobile: {
    content: [
      {
        title: 'Spring Collection Launch',
        type: 'imageTile',
        carouselSettings: {
          autoplay: true,
          looping: 'infinite',
          rotationTime: 2000,
          transitionTime: 0,
          transitionType: 'fade',
        },
        imageTabs: {
          desktopImage: {
            url: 'https://dam-test.gap.orangelogic.com/IIIF3/Image/0gvgd466035075b1gqc7372pjl6ua70e/full/max/0.0/default.jpg',
            dimensions: [
              {
                usage: 'image',
                type: 'https://dam-test.gap.orangelogic.com/IIIF3/Image/0gvgd466035075b1gqc7372pjl6ua70e/full/max/0.0/default.jpg',
                width: 344,
                height: 615,
              },
            ],
            hotspots: [
              {
                type: 'url',
                coordinates: {
                  x1: 0,
                  x2: 100,
                  y1: 2,
                  y2: 100,
                },
                url: 'https://athleta.gap.com/browse/new/resort?cid=1022034&mlink=1,1,HP_Spotlight_CTA1',
                name: 'Go Short',
              },
            ],
          },
          image: {
            url: 'https://cdn.media.amplience.net/i/athleta/2d1_must_have_links_with_ease_v2_mobile?fmt=auto',
            dimensions: [
              {
                usage: 'image',
                type: 'https://cdn.media.amplience.net/i/athleta/2d1_must_have_links_with_ease_v2_mobile?fmt=auto',
                width: 336,
                height: 408,
              },
            ],
          },
          desktopAccessibilityAltText: 'Desktop image alt text',
          accessibilityAltText: 'Mobile image alt text',
          desktopCarousel: [
            {
              desktopImage: {
                url: 'https://dam-test.gap.orangelogic.com/IIIF3/Image/0gvgd466035075b1gqc7372pjl6ua70e/full/max/0.0/default.jpg',
                dimensions: [
                  {
                    usage: 'image',
                    type: 'https://dam-test.gap.orangelogic.com/IIIF3/Image/0gvgd466035075b1gqc7372pjl6ua70e/full/max/0.0/default.jpg',
                    width: 344,
                    height: 615,
                  },
                ],
                hotspots: [
                  {
                    type: 'url',
                    coordinates: {
                      x1: 0,
                      x2: 100,
                      y1: 2,
                      y2: 100,
                    },
                    url: 'https://athleta.gap.com/browse/new/resort?cid=1022034&mlink=1,1,HP_Spotlight_CTA1',
                    name: 'Go Short',
                  },
                ],
              },
              desktopCarouselAltText: 'Carousel desktop image 1',
            },
            {
              desktopImage: {
                url: 'https://cdn.media.amplience.net/i/athleta/2d1_must_have_links_with_ease_v2_mobile?fmt=auto',
                dimensions: [
                  {
                    usage: 'image',
                    type: 'https://cdn.media.amplience.net/i/athleta/2d1_must_have_links_with_ease_v2_mobile?fmt=auto',
                    width: 344,
                    height: 615,
                  },
                ],
                hotspots: [
                  {
                    type: 'url',
                    coordinates: {
                      x1: 0,
                      x2: 100,
                      y1: 2,
                      y2: 100,
                    },
                    url: 'https://athleta.gap.com/browse/new/resort?cid=1022034&mlink=1,1,HP_Spotlight_CTA1',
                    name: 'Go Short',
                  },
                ],
              },
              desktopCarouselAltText: 'Carousel desktop image 2',
            },
          ],
          mobileCarousel: [
            {
              mobileImage: {
                url: 'https://cdn.media.amplience.net/i/athleta/2d1_must_have_links_with_ease_v2_mobile?fmt=auto',
                dimensions: [
                  {
                    usage: 'image',
                    type: 'https://cdn.media.amplience.net/i/athleta/2d1_must_have_links_with_ease_v2_mobile?fmt=auto',
                    width: 336,
                    height: 408,
                  },
                ],
              },
              mobileCarouselAltText: 'a woman in a blue shirt is doing a yoga pose',
            },
            {
              mobileImage: {
                url: 'https://cdn.media.amplience.net/i/athleta/HOL1_S_TEST_F2?fmt=auto',
                dimensions: [
                  {
                    usage: 'image',
                    type: 'https://cdn.media.amplience.net/i/athleta/HOL1_S_TEST_F2?fmt=auto',
                    width: 750,
                    height: 1334,
                  },
                ],
              },
              mobileCarouselAltText: 'two women walking down a street together, one with a bag',
            },
            {
              mobileImage: {
                url: 'https://cdn.media.amplience.net/i/athleta/S%20D1_F1_HP_Outerwear_S?fmt=auto',
                dimensions: [
                  {
                    usage: 'image',
                    type: 'https://cdn.media.amplience.net/i/athleta/S%20D1_F1_HP_Outerwear_S?fmt=auto',
                    width: 750,
                    height: 1334,
                  },
                ],
              },
              mobileCarouselAltText: 'a woman in a vest and pants standing in front of a wall',
            },
          ],
        },
        video: {
          desktop: {
            fallbackImage: {
              url: 'https://dam-test.gap.orangelogic.com/AssetLink/7mj287dj28dal8160s8knwnwp5ow0qho.webp',
              dimensions: [
                {
                  usage: 'image',
                  type: 'https://dam-test.gap.orangelogic.com/AssetLink/7mj287dj28dal8160s8knwnwp5ow0qho.webp',
                  width: 2880,
                  height: 1300,
                },
              ],
            },
            video: {
              url: 'https://dam-test.gap.orangelogic.com/AssetLink/v5445hb8hyowy08550ysm67634cn14kw.mp4',
            },
            altText: 'a large brick house with a black roof and white trim',
            svgOverlay: {
              url: 'https://dam-test.gap.orangelogic.com/AssetLink/85438f6jkyb3uwvlf7rnfwor3fsm1516.svg',
              dimensions: [
                {
                  usage: 'image',
                  type: 'https://dam-test.gap.orangelogic.com/AssetLink/85438f6jkyb3uwvlf7rnfwor3fsm1516.svg',
                  width: 1440,
                  height: 635,
                },
              ],
            },
          },
          mobile: {
            fallbackImage: {
              url: 'https://dam-test.gap.orangelogic.com/AssetLink/k3c5cd7ykfqjaf8l4cj100ckf330m7l2.webp',
              dimensions: [
                {
                  usage: 'image',
                  type: 'https://dam-test.gap.orangelogic.com/AssetLink/k3c5cd7ykfqjaf8l4cj100ckf330m7l2.webp',
                  width: 750,
                  height: 1000,
                },
              ],
            },
            svgOverlay: {
              url: 'https://dam-test.gap.orangelogic.com/AssetLink/5c4x3jfi483nk4218708033bre3owp46.svg',
              dimensions: [
                {
                  usage: 'image',
                  type: 'https://dam-test.gap.orangelogic.com/AssetLink/5c4x3jfi483nk4218708033bre3owp46.svg',
                  width: 375,
                  height: 485,
                },
              ],
            },
            video: {
              url: 'https://dam-test.gap.orangelogic.com/AssetLink/3lv5ybh0c2jp61iexj22ag32ly1o0o33.mp4',
            },
            overlayAltText: 'Transparent SVG Text',
            altText: 'a woman in a purple crop top posing for a picture',
          },
        },
      },
    ],
  },
  title: 'Spring Campaign',
  type: 'imageTile',
  platforms: {
    android: true,
    ios: true,
  },
  tags: ['spring', 'campaign'],
};

export const sampleInSortMarketingDataWithVideo: InSortMarketingData = {
  ...sampleInSortMarketingData,
  desktop: {
    content: [
      ...(sampleInSortMarketingData.desktop?.content || []),
      {
        title: 'Spring Video Feature',
        type: 'videoTile',
        video: {
          desktop: {
            fallbackImage: {
              url: 'https://dam-test.gap.orangelogic.com/AssetLink/7mj287dj28dal8160s8knwnwp5ow0qho.webp',
              dimensions: [
                {
                  usage: 'image',
                  type: 'https://dam-test.gap.orangelogic.com/AssetLink/7mj287dj28dal8160s8knwnwp5ow0qho.webp',
                  width: 2880,
                  height: 1300,
                },
              ],
            },
            video: {
              url: 'https://dam-test.gap.orangelogic.com/AssetLink/v5445hb8hyowy08550ysm67634cn14kw.mp4',
            },
            altText: 'a large brick house with a black roof and white trim',
            svgOverlay: {
              url: 'https://dam-test.gap.orangelogic.com/AssetLink/85438f6jkyb3uwvlf7rnfwor3fsm1516.svg',
              dimensions: [
                {
                  usage: 'image',
                  type: 'https://dam-test.gap.orangelogic.com/AssetLink/85438f6jkyb3uwvlf7rnfwor3fsm1516.svg',
                  width: 1440,
                  height: 635,
                },
              ],
            },
          },
          mobile: {
            fallbackImage: {
              url: 'https://dam-test.gap.orangelogic.com/AssetLink/k3c5cd7ykfqjaf8l4cj100ckf330m7l2.webp',
              dimensions: [
                {
                  usage: 'image',
                  type: 'https://dam-test.gap.orangelogic.com/AssetLink/k3c5cd7ykfqjaf8l4cj100ckf330m7l2.webp',
                  width: 750,
                  height: 1000,
                },
              ],
            },
            svgOverlay: {
              url: 'https://dam-test.gap.orangelogic.com/AssetLink/5c4x3jfi483nk4218708033bre3owp46.svg',
              dimensions: [
                {
                  usage: 'image',
                  type: 'https://dam-test.gap.orangelogic.com/AssetLink/5c4x3jfi483nk4218708033bre3owp46.svg',
                  width: 375,
                  height: 485,
                },
              ],
            },
            video: {
              url: 'https://dam-test.gap.orangelogic.com/AssetLink/3lv5ybh0c2jp61iexj22ag32ly1o0o33.mp4',
            },
            overlayAltText: 'Transparent SVG Text',
            altText: 'a woman in a purple crop top posing for a picture',
          },
        },
      },
    ],
  },
  mobile: {
    content: [
      ...(sampleInSortMarketingData.mobile?.content || []),
      {
        title: 'Spring Video Feature',
        type: 'videoTile',
        video: {
          desktop: {
            fallbackImage: {
              url: 'https://dam-test.gap.orangelogic.com/AssetLink/7mj287dj28dal8160s8knwnwp5ow0qho.webp',
              dimensions: [
                {
                  usage: 'image',
                  type: 'https://dam-test.gap.orangelogic.com/AssetLink/7mj287dj28dal8160s8knwnwp5ow0qho.webp',
                  width: 2880,
                  height: 1300,
                },
              ],
            },
            video: {
              url: 'https://dam-test.gap.orangelogic.com/AssetLink/v5445hb8hyowy08550ysm67634cn14kw.mp4',
            },
            altText: 'a large brick house with a black roof and white trim',
            svgOverlay: {
              url: 'https://dam-test.gap.orangelogic.com/AssetLink/85438f6jkyb3uwvlf7rnfwor3fsm1516.svg',
              dimensions: [
                {
                  usage: 'image',
                  type: 'https://dam-test.gap.orangelogic.com/AssetLink/85438f6jkyb3uwvlf7rnfwor3fsm1516.svg',
                  width: 1440,
                  height: 635,
                },
              ],
            },
          },
          mobile: {
            fallbackImage: {
              url: 'https://dam-test.gap.orangelogic.com/AssetLink/k3c5cd7ykfqjaf8l4cj100ckf330m7l2.webp',
              dimensions: [
                {
                  usage: 'image',
                  type: 'https://dam-test.gap.orangelogic.com/AssetLink/k3c5cd7ykfqjaf8l4cj100ckf330m7l2.webp',
                  width: 750,
                  height: 1000,
                },
              ],
            },
            svgOverlay: {
              url: 'https://dam-test.gap.orangelogic.com/AssetLink/5c4x3jfi483nk4218708033bre3owp46.svg',
              dimensions: [
                {
                  usage: 'image',
                  type: 'https://dam-test.gap.orangelogic.com/AssetLink/5c4x3jfi483nk4218708033bre3owp46.svg',
                  width: 375,
                  height: 485,
                },
              ],
            },
            video: {
              url: 'https://dam-test.gap.orangelogic.com/AssetLink/3lv5ybh0c2jp61iexj22ag32ly1o0o33.mp4',
            },
            overlayAltText: 'Transparent SVG Text',
            altText: 'a woman in a purple crop top posing for a picture',
          },
        },
      },
    ],
  },
};
