export interface ImageDimensions {
  usage: string;
  type: string;
  width: number;
  height: number;
}

export interface HotspotCoordinates {
  x1: number;
  x2: number;
  y1: number;
  y2: number;
}

export interface Hotspot {
  type?: string;
  coordinates?: HotspotCoordinates;
  name?: string;
  url?: string;
}

export interface InSortMarketingImage {
  url: string;
  dimensions: ImageDimensions[];
  hotspots?: Hotspot[];
}

export interface VideoTileFallbackImage {
  dimensions: ImageDimensions[];
  url: string;
}

export interface VideoTileSVGOverlay {
  url: string;
  dimensions: ImageDimensions[];
}

export interface VideoTileVideoSource {
  fallbackImage?: VideoTileFallbackImage;
  video?: {
    url: string;
  };
  altText?: string;
  svgOverlay?: VideoTileSVGOverlay;
  overlayAltText?: string;
}

export interface VideoTileVideo {
  desktop?: VideoTileVideoSource;
  mobile?: VideoTileVideoSource;
}

export interface VideoTileStyling {
  padding: {
    desktop: number;
    mobile: number;
  };
}

export interface VideoTilePlatforms {
  android: boolean;
  ios: boolean;
}

export interface VideoTileMeta {
  deliveryId: string;
  name: string;
  schema: string;
}

export interface LinkRedirectUrl {
  legal?: LegalBannerType;
  linkType: string;
  linkTypeValue?: string;
}

export interface LegalBannerType {
  detailsText?: string;
  pemoleCode?: string;
  textColor?: string;
}
export interface VideoTileContent {
  _meta: VideoTileMeta;
  link: LinkRedirectUrl;
  platforms: VideoTilePlatforms;
  styling: VideoTileStyling;
  title: string;
  type: string;
  video?: VideoTileVideo;
  videoControlSettings?: {
    controlsIconsColor: string;
  };
  rightAlignControls?: boolean;
}

export interface DesktopCarouselItem {
  desktopImage: InSortMarketingImage;
  desktopCarouselAltText: string;
}

export interface MobileCarouselItem {
  mobileImage: InSortMarketingImage;
  mobileCarouselAltText: string;
}

export type ismType = 'imageTile' | 'videoTile';

export interface ImageTab {
  desktopImage?: InSortMarketingImage;
  image?: InSortMarketingImage;
  desktopAccessibilityAltText?: string;
  accessibilityAltText?: string;
  desktopCarousel: DesktopCarouselItem[];
  mobileCarousel: MobileCarouselItem[];
}
export interface InSortMarketingMeta {
  name: string;
  schema: string;
  deliveryId: string;
}

export interface InSortMarketingDataItem {
  title?: string;
  type?: ismType;
  carouselSettings?: {
    autoplay: boolean;
    looping: string;
    rotationTime: number;
    transitionTime: number;
    transitionType: string;
  };
  imageTabs?: {
    desktopImage?: InSortMarketingImage;
    image?: InSortMarketingImage;
    desktopAccessibilityAltText?: string;
    desktopCarousel?: DesktopCarouselItem[];
    mobileCarousel?: MobileCarouselItem[];
  };
  image?: InSortMarketingImage;
  desktopImage?: InSortMarketingImage;
  desktopAccessibilityAltText?: string;
  video?: {
    desktop?: VideoTileContent;
    mobile?: VideoTileContent;
  };
}

export interface InSortMarketingData {
  _meta: InSortMarketingMeta;
  output?: {
    rowPosition?: {
      desktop?: number;
      mobile?: number;
    };
  };
  desktop?: {
    content: InSortMarketingDataItem[];
  };
  mobile?: {
    content: InSortMarketingDataItem[];
  };
}
