import React from 'react';
import { render as renderWithProviders, screen } from 'test-utils';
import { Brands } from '@ecom-next/core/react-stitch';
import { AppState } from '@ecom-next/sitewide/app-state-provider';
import { sampleInSortMarketingData as carouselData, sampleInSortMarketingDataWithVideo as newVideoStoryData } from './__fixtures__/testData';
import InSortMarketing from '.';

const desktopRenderOptions = {
  appState: {
    brandName: Brands.Athleta,
  } as unknown as AppState,
  breakpoint: 'large',
} as any;

const mobileRenderOptions = {
  appState: {
    brandName: Brands.Athleta,
  } as unknown as AppState,
  breakpoint: 'small',
} as any;

describe('InSortMarketing Component', () => {
  it('renders nothing if type is undefined', () => {
    const data = { ...carouselData, desktop: { content: [{}] } };
    renderWithProviders(<InSortMarketing {...data} />, desktopRenderOptions);
    expect(screen.queryByTestId('in-sort-marketing')).toBeNull();
  });

  it('renders image carousel on desktop when type is imageTile', () => {
    renderWithProviders(<InSortMarketing {...carouselData} />, desktopRenderOptions);
    const figure = screen.getByTestId('in-sort-marketing');
    expect(figure).toBeInTheDocument();
    expect(figure).toHaveClass('flex');
  });

  it('renders image carousel on mobile when type is imageTile', () => {
    renderWithProviders(<InSortMarketing {...carouselData} />, mobileRenderOptions);
    const figure = screen.getByTestId('in-sort-marketing');
    expect(figure).toBeInTheDocument();
    expect(figure).toHaveClass('flex');
  });

  it('renders video tile on desktop when type is videoTile', () => {
    renderWithProviders(<InSortMarketing {...newVideoStoryData} />, desktopRenderOptions);
    const figure = screen.getByTestId('in-sort-marketing');
    expect(figure).toBeInTheDocument();
    expect(figure).toHaveClass('flex');
  });

  it('matches snapshot for image carousel on desktop', () => {
    const { asFragment } = renderWithProviders(<InSortMarketing {...carouselData} />, desktopRenderOptions);
    expect(asFragment()).toMatchSnapshot();
  });

  it('matches snapshot for image carousel on mobile', () => {
    const { asFragment } = renderWithProviders(<InSortMarketing {...carouselData} />, mobileRenderOptions);
    expect(asFragment()).toMatchSnapshot();
  });

  it('matches snapshot for video tile on desktop', () => {
    const { asFragment } = renderWithProviders(<InSortMarketing {...newVideoStoryData} />, desktopRenderOptions);
    expect(asFragment()).toMatchSnapshot();
  });

  it('matches snapshot for video tile on mobile', () => {
    const { asFragment } = renderWithProviders(<InSortMarketing {...newVideoStoryData} />, mobileRenderOptions);
    expect(asFragment()).toMatchSnapshot();
  });

  it('matches snapshot when nothing is rendered (type undefined)', () => {
    const data = { ...carouselData, desktop: { content: [{}] } };
    const { asFragment } = renderWithProviders(<InSortMarketing {...data} />, desktopRenderOptions);
    expect(asFragment()).toMatchSnapshot();
  });
});
