// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`InSortMarketing Component matches snapshot for image carousel on desktop 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <figure
      aria-label="Spring Collection Launch"
      class="flex"
      data-testid="in-sort-marketing"
    >
      <div
        class="relative mx-auto bg-white"
      >
        <div
          class="absolute inset-0 transition-opacity"
          style="background-color: rgb(255, 255, 255); opacity: 1; pointer-events: auto; z-index: 2; transition-duration: 0ms; position: static;"
        >
          <div
            class="w-full"
            style="padding-bottom: 0px;"
          >
            <div
              class="relative w-full"
            >
              <img
                alt="Carousel desktop image 2"
                class="w-full"
                fetchpriority="auto"
                src="https://cdn.media.amplience.net/i/athleta/2d1_must_have_links_with_ease_v2_mobile?fmt=auto"
                usemap="#hotspotMap-:ru:"
              />
              <map
                name="hotspotMap-:ru:"
                role="group"
              >
                <area
                  alt="Go Short"
                  coords="0,0,0,0"
                  data-hotspot-type="url"
                  data-testid="hotspot-Go Short"
                  href="https://athleta.gap.com/browse/new/resort?cid=1022034&mlink=1,1,HP_Spotlight_CTA1"
                  rel="noopener noreferrer"
                  shape="rect"
                  tabindex="0"
                  target="_blank"
                  title="Go Short"
                />
              </map>
            </div>
          </div>
          <button
            aria-label="Pause"
            style="position: absolute; bottom: 30px; left: 15px; background: transparent; cursor: pointer; z-index: 3; display: block; opacity: 0.64;"
          >
            <svg
              aria-label="pause-active-button"
              fill="none"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                fill="#FFFFFF"
              />
              <path
                d="M7.5 7.5C7.5 7.10218 7.65804 6.72064 7.93934 6.43934C8.22064 6.15804 8.60218 6 9 6C9.39782 6 9.77936 6.15804 10.0607 6.43934C10.342 6.72064 10.5 7.10218 10.5 7.5V16.5C10.5 16.8978 10.342 17.2794 10.0607 17.5607C9.77936 17.842 9.39782 18 9 18C8.60218 18 8.22064 17.842 7.93934 17.5607C7.65804 17.2794 7.5 16.8978 7.5 16.5V7.5ZM13.5 7.5C13.5 7.10218 13.658 6.72064 13.9393 6.43934C14.2206 6.15804 14.6022 6 15 6C15.3978 6 15.7794 6.15804 16.0607 6.43934C16.342 6.72064 16.5 7.10218 16.5 7.5V16.5C16.5 16.8978 16.342 17.2794 16.0607 17.5607C15.7794 17.842 15.3978 18 15 18C14.6022 18 14.2206 17.842 13.9393 17.5607C13.658 17.2794 13.5 16.8978 13.5 16.5V7.5Z"
                fill="#2C2824"
              />
            </svg>
          </button>
        </div>
        <div
          class="absolute inset-0 transition-opacity"
          style="background-color: rgb(255, 255, 255); opacity: 0; pointer-events: none; z-index: 1; transition-duration: 0ms; position: absolute;"
        >
          <div
            class="w-full"
            style="padding-bottom: 0px;"
          >
            <div
              class="relative w-full"
            >
              <img
                alt="Carousel desktop image 2"
                class="w-full"
                fetchpriority="auto"
                src="https://cdn.media.amplience.net/i/athleta/2d1_must_have_links_with_ease_v2_mobile?fmt=auto"
                usemap="#hotspotMap-:r11:"
              />
              <map
                name="hotspotMap-:r11:"
                role="group"
              >
                <area
                  alt="Go Short"
                  coords="0,0,0,0"
                  data-hotspot-type="url"
                  data-testid="hotspot-Go Short"
                  href="https://athleta.gap.com/browse/new/resort?cid=1022034&mlink=1,1,HP_Spotlight_CTA1"
                  rel="noopener noreferrer"
                  shape="rect"
                  tabindex="0"
                  target="_blank"
                  title="Go Short"
                />
              </map>
            </div>
          </div>
          <button
            aria-label="Pause"
            style="position: absolute; bottom: 30px; left: 15px; background: transparent; cursor: pointer; z-index: 3; display: block; opacity: 0.64;"
          >
            <svg
              aria-label="pause-active-button"
              fill="none"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                fill="#FFFFFF"
              />
              <path
                d="M7.5 7.5C7.5 7.10218 7.65804 6.72064 7.93934 6.43934C8.22064 6.15804 8.60218 6 9 6C9.39782 6 9.77936 6.15804 10.0607 6.43934C10.342 6.72064 10.5 7.10218 10.5 7.5V16.5C10.5 16.8978 10.342 17.2794 10.0607 17.5607C9.77936 17.842 9.39782 18 9 18C8.60218 18 8.22064 17.842 7.93934 17.5607C7.65804 17.2794 7.5 16.8978 7.5 16.5V7.5ZM13.5 7.5C13.5 7.10218 13.658 6.72064 13.9393 6.43934C14.2206 6.15804 14.6022 6 15 6C15.3978 6 15.7794 6.15804 16.0607 6.43934C16.342 6.72064 16.5 7.10218 16.5 7.5V16.5C16.5 16.8978 16.342 17.2794 16.0607 17.5607C15.7794 17.842 15.3978 18 15 18C14.6022 18 14.2206 17.842 13.9393 17.5607C13.658 17.2794 13.5 16.8978 13.5 16.5V7.5Z"
                fill="#2C2824"
              />
            </svg>
          </button>
        </div>
        <div
          class="absolute inset-0 transition-opacity"
          style="background-color: rgb(255, 255, 255); opacity: 0; pointer-events: none; z-index: 1; transition-duration: 0ms; position: absolute;"
        >
          <div
            class="w-full"
            style="padding-bottom: 0px;"
          >
            <div
              class="relative w-full"
            >
              <img
                alt="Carousel desktop image 2"
                class="w-full"
                fetchpriority="auto"
                src="https://cdn.media.amplience.net/i/athleta/2d1_must_have_links_with_ease_v2_mobile?fmt=auto"
                usemap="#hotspotMap-:r14:"
              />
              <map
                name="hotspotMap-:r14:"
                role="group"
              >
                <area
                  alt="Go Short"
                  coords="0,0,0,0"
                  data-hotspot-type="url"
                  data-testid="hotspot-Go Short"
                  href="https://athleta.gap.com/browse/new/resort?cid=1022034&mlink=1,1,HP_Spotlight_CTA1"
                  rel="noopener noreferrer"
                  shape="rect"
                  tabindex="0"
                  target="_blank"
                  title="Go Short"
                />
              </map>
            </div>
          </div>
          <button
            aria-label="Pause"
            style="position: absolute; bottom: 30px; left: 15px; background: transparent; cursor: pointer; z-index: 3; display: block; opacity: 0.64;"
          >
            <svg
              aria-label="pause-active-button"
              fill="none"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                fill="#FFFFFF"
              />
              <path
                d="M7.5 7.5C7.5 7.10218 7.65804 6.72064 7.93934 6.43934C8.22064 6.15804 8.60218 6 9 6C9.39782 6 9.77936 6.15804 10.0607 6.43934C10.342 6.72064 10.5 7.10218 10.5 7.5V16.5C10.5 16.8978 10.342 17.2794 10.0607 17.5607C9.77936 17.842 9.39782 18 9 18C8.60218 18 8.22064 17.842 7.93934 17.5607C7.65804 17.2794 7.5 16.8978 7.5 16.5V7.5ZM13.5 7.5C13.5 7.10218 13.658 6.72064 13.9393 6.43934C14.2206 6.15804 14.6022 6 15 6C15.3978 6 15.7794 6.15804 16.0607 6.43934C16.342 6.72064 16.5 7.10218 16.5 7.5V16.5C16.5 16.8978 16.342 17.2794 16.0607 17.5607C15.7794 17.842 15.3978 18 15 18C14.6022 18 14.2206 17.842 13.9393 17.5607C13.658 17.2794 13.5 16.8978 13.5 16.5V7.5Z"
                fill="#2C2824"
              />
            </svg>
          </button>
        </div>
      </div>
    </figure>
  </div>
</DocumentFragment>
`;

exports[`InSortMarketing Component matches snapshot for image carousel on mobile 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <figure
      aria-label="Spring Collection Launch"
      class="flex"
      data-testid="in-sort-marketing"
    >
      <div
        class="relative mx-auto bg-white"
      >
        <div
          class="absolute inset-0 transition-opacity"
          style="background-color: rgb(255, 255, 255); opacity: 1; pointer-events: auto; z-index: 2; transition-duration: 0ms; position: static;"
        >
          <div
            class="w-full"
            style="padding-bottom: 0px;"
          >
            <div
              class="relative w-full"
            >
              <img
                alt="a woman in a vest and pants standing in front of a wall"
                class="w-full"
                fetchpriority="auto"
                src="https://cdn.media.amplience.net/i/athleta/S%20D1_F1_HP_Outerwear_S?fmt=auto"
              />
            </div>
          </div>
          <button
            aria-label="Pause"
            style="position: absolute; bottom: 20px; right: 15px; background: transparent; cursor: pointer; z-index: 3; display: block; opacity: 0.64;"
          >
            <svg
              aria-label="pause-active-button"
              fill="none"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                fill="#FFFFFF"
              />
              <path
                d="M7.5 7.5C7.5 7.10218 7.65804 6.72064 7.93934 6.43934C8.22064 6.15804 8.60218 6 9 6C9.39782 6 9.77936 6.15804 10.0607 6.43934C10.342 6.72064 10.5 7.10218 10.5 7.5V16.5C10.5 16.8978 10.342 17.2794 10.0607 17.5607C9.77936 17.842 9.39782 18 9 18C8.60218 18 8.22064 17.842 7.93934 17.5607C7.65804 17.2794 7.5 16.8978 7.5 16.5V7.5ZM13.5 7.5C13.5 7.10218 13.658 6.72064 13.9393 6.43934C14.2206 6.15804 14.6022 6 15 6C15.3978 6 15.7794 6.15804 16.0607 6.43934C16.342 6.72064 16.5 7.10218 16.5 7.5V16.5C16.5 16.8978 16.342 17.2794 16.0607 17.5607C15.7794 17.842 15.3978 18 15 18C14.6022 18 14.2206 17.842 13.9393 17.5607C13.658 17.2794 13.5 16.8978 13.5 16.5V7.5Z"
                fill="#2C2824"
              />
            </svg>
          </button>
        </div>
        <div
          class="absolute inset-0 transition-opacity"
          style="background-color: rgb(255, 255, 255); opacity: 0; pointer-events: none; z-index: 1; transition-duration: 0ms; position: absolute;"
        >
          <div
            class="w-full"
            style="padding-bottom: 0px;"
          >
            <div
              class="relative w-full"
            >
              <img
                alt="a woman in a vest and pants standing in front of a wall"
                class="w-full"
                fetchpriority="auto"
                src="https://cdn.media.amplience.net/i/athleta/S%20D1_F1_HP_Outerwear_S?fmt=auto"
              />
            </div>
          </div>
          <button
            aria-label="Pause"
            style="position: absolute; bottom: 20px; right: 15px; background: transparent; cursor: pointer; z-index: 3; display: block; opacity: 0.64;"
          >
            <svg
              aria-label="pause-active-button"
              fill="none"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                fill="#FFFFFF"
              />
              <path
                d="M7.5 7.5C7.5 7.10218 7.65804 6.72064 7.93934 6.43934C8.22064 6.15804 8.60218 6 9 6C9.39782 6 9.77936 6.15804 10.0607 6.43934C10.342 6.72064 10.5 7.10218 10.5 7.5V16.5C10.5 16.8978 10.342 17.2794 10.0607 17.5607C9.77936 17.842 9.39782 18 9 18C8.60218 18 8.22064 17.842 7.93934 17.5607C7.65804 17.2794 7.5 16.8978 7.5 16.5V7.5ZM13.5 7.5C13.5 7.10218 13.658 6.72064 13.9393 6.43934C14.2206 6.15804 14.6022 6 15 6C15.3978 6 15.7794 6.15804 16.0607 6.43934C16.342 6.72064 16.5 7.10218 16.5 7.5V16.5C16.5 16.8978 16.342 17.2794 16.0607 17.5607C15.7794 17.842 15.3978 18 15 18C14.6022 18 14.2206 17.842 13.9393 17.5607C13.658 17.2794 13.5 16.8978 13.5 16.5V7.5Z"
                fill="#2C2824"
              />
            </svg>
          </button>
        </div>
        <div
          class="absolute inset-0 transition-opacity"
          style="background-color: rgb(255, 255, 255); opacity: 0; pointer-events: none; z-index: 1; transition-duration: 0ms; position: absolute;"
        >
          <div
            class="w-full"
            style="padding-bottom: 0px;"
          >
            <div
              class="relative w-full"
            >
              <img
                alt="a woman in a vest and pants standing in front of a wall"
                class="w-full"
                fetchpriority="auto"
                src="https://cdn.media.amplience.net/i/athleta/S%20D1_F1_HP_Outerwear_S?fmt=auto"
              />
            </div>
          </div>
          <button
            aria-label="Pause"
            style="position: absolute; bottom: 20px; right: 15px; background: transparent; cursor: pointer; z-index: 3; display: block; opacity: 0.64;"
          >
            <svg
              aria-label="pause-active-button"
              fill="none"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                fill="#FFFFFF"
              />
              <path
                d="M7.5 7.5C7.5 7.10218 7.65804 6.72064 7.93934 6.43934C8.22064 6.15804 8.60218 6 9 6C9.39782 6 9.77936 6.15804 10.0607 6.43934C10.342 6.72064 10.5 7.10218 10.5 7.5V16.5C10.5 16.8978 10.342 17.2794 10.0607 17.5607C9.77936 17.842 9.39782 18 9 18C8.60218 18 8.22064 17.842 7.93934 17.5607C7.65804 17.2794 7.5 16.8978 7.5 16.5V7.5ZM13.5 7.5C13.5 7.10218 13.658 6.72064 13.9393 6.43934C14.2206 6.15804 14.6022 6 15 6C15.3978 6 15.7794 6.15804 16.0607 6.43934C16.342 6.72064 16.5 7.10218 16.5 7.5V16.5C16.5 16.8978 16.342 17.2794 16.0607 17.5607C15.7794 17.842 15.3978 18 15 18C14.6022 18 14.2206 17.842 13.9393 17.5607C13.658 17.2794 13.5 16.8978 13.5 16.5V7.5Z"
                fill="#2C2824"
              />
            </svg>
          </button>
        </div>
        <div
          class="absolute inset-0 transition-opacity"
          style="background-color: rgb(255, 255, 255); opacity: 0; pointer-events: none; z-index: 1; transition-duration: 0ms; position: absolute;"
        >
          <div
            class="w-full"
            style="padding-bottom: 0px;"
          >
            <div
              class="relative w-full"
            >
              <img
                alt="a woman in a vest and pants standing in front of a wall"
                class="w-full"
                fetchpriority="auto"
                src="https://cdn.media.amplience.net/i/athleta/S%20D1_F1_HP_Outerwear_S?fmt=auto"
              />
            </div>
          </div>
          <button
            aria-label="Pause"
            style="position: absolute; bottom: 20px; right: 15px; background: transparent; cursor: pointer; z-index: 3; display: block; opacity: 0.64;"
          >
            <svg
              aria-label="pause-active-button"
              fill="none"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                fill="#FFFFFF"
              />
              <path
                d="M7.5 7.5C7.5 7.10218 7.65804 6.72064 7.93934 6.43934C8.22064 6.15804 8.60218 6 9 6C9.39782 6 9.77936 6.15804 10.0607 6.43934C10.342 6.72064 10.5 7.10218 10.5 7.5V16.5C10.5 16.8978 10.342 17.2794 10.0607 17.5607C9.77936 17.842 9.39782 18 9 18C8.60218 18 8.22064 17.842 7.93934 17.5607C7.65804 17.2794 7.5 16.8978 7.5 16.5V7.5ZM13.5 7.5C13.5 7.10218 13.658 6.72064 13.9393 6.43934C14.2206 6.15804 14.6022 6 15 6C15.3978 6 15.7794 6.15804 16.0607 6.43934C16.342 6.72064 16.5 7.10218 16.5 7.5V16.5C16.5 16.8978 16.342 17.2794 16.0607 17.5607C15.7794 17.842 15.3978 18 15 18C14.6022 18 14.2206 17.842 13.9393 17.5607C13.658 17.2794 13.5 16.8978 13.5 16.5V7.5Z"
                fill="#2C2824"
              />
            </svg>
          </button>
        </div>
      </div>
    </figure>
  </div>
</DocumentFragment>
`;

exports[`InSortMarketing Component matches snapshot for video tile on desktop 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <figure
      aria-label="Spring Collection Launch"
      class="flex"
      data-testid="in-sort-marketing"
    >
      <div
        class="relative mx-auto bg-white"
      >
        <div
          class="absolute inset-0 transition-opacity"
          style="background-color: rgb(255, 255, 255); opacity: 1; pointer-events: auto; z-index: 2; transition-duration: 0ms; position: static;"
        >
          <div
            class="w-full"
            style="padding-bottom: 0px;"
          >
            <div
              class="relative w-full"
            >
              <img
                alt="Carousel desktop image 2"
                class="w-full"
                fetchpriority="auto"
                src="https://cdn.media.amplience.net/i/athleta/2d1_must_have_links_with_ease_v2_mobile?fmt=auto"
                usemap="#hotspotMap-:r1j:"
              />
              <map
                name="hotspotMap-:r1j:"
                role="group"
              >
                <area
                  alt="Go Short"
                  coords="0,0,0,0"
                  data-hotspot-type="url"
                  data-testid="hotspot-Go Short"
                  href="https://athleta.gap.com/browse/new/resort?cid=1022034&mlink=1,1,HP_Spotlight_CTA1"
                  rel="noopener noreferrer"
                  shape="rect"
                  tabindex="0"
                  target="_blank"
                  title="Go Short"
                />
              </map>
            </div>
          </div>
          <button
            aria-label="Pause"
            style="position: absolute; bottom: 30px; left: 15px; background: transparent; cursor: pointer; z-index: 3; display: block; opacity: 0.64;"
          >
            <svg
              aria-label="pause-active-button"
              fill="none"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                fill="#FFFFFF"
              />
              <path
                d="M7.5 7.5C7.5 7.10218 7.65804 6.72064 7.93934 6.43934C8.22064 6.15804 8.60218 6 9 6C9.39782 6 9.77936 6.15804 10.0607 6.43934C10.342 6.72064 10.5 7.10218 10.5 7.5V16.5C10.5 16.8978 10.342 17.2794 10.0607 17.5607C9.77936 17.842 9.39782 18 9 18C8.60218 18 8.22064 17.842 7.93934 17.5607C7.65804 17.2794 7.5 16.8978 7.5 16.5V7.5ZM13.5 7.5C13.5 7.10218 13.658 6.72064 13.9393 6.43934C14.2206 6.15804 14.6022 6 15 6C15.3978 6 15.7794 6.15804 16.0607 6.43934C16.342 6.72064 16.5 7.10218 16.5 7.5V16.5C16.5 16.8978 16.342 17.2794 16.0607 17.5607C15.7794 17.842 15.3978 18 15 18C14.6022 18 14.2206 17.842 13.9393 17.5607C13.658 17.2794 13.5 16.8978 13.5 16.5V7.5Z"
                fill="#2C2824"
              />
            </svg>
          </button>
        </div>
        <div
          class="absolute inset-0 transition-opacity"
          style="background-color: rgb(255, 255, 255); opacity: 0; pointer-events: none; z-index: 1; transition-duration: 0ms; position: absolute;"
        >
          <div
            class="w-full"
            style="padding-bottom: 0px;"
          >
            <div
              class="relative w-full"
            >
              <img
                alt="Carousel desktop image 2"
                class="w-full"
                fetchpriority="auto"
                src="https://cdn.media.amplience.net/i/athleta/2d1_must_have_links_with_ease_v2_mobile?fmt=auto"
                usemap="#hotspotMap-:r1m:"
              />
              <map
                name="hotspotMap-:r1m:"
                role="group"
              >
                <area
                  alt="Go Short"
                  coords="0,0,0,0"
                  data-hotspot-type="url"
                  data-testid="hotspot-Go Short"
                  href="https://athleta.gap.com/browse/new/resort?cid=1022034&mlink=1,1,HP_Spotlight_CTA1"
                  rel="noopener noreferrer"
                  shape="rect"
                  tabindex="0"
                  target="_blank"
                  title="Go Short"
                />
              </map>
            </div>
          </div>
          <button
            aria-label="Pause"
            style="position: absolute; bottom: 30px; left: 15px; background: transparent; cursor: pointer; z-index: 3; display: block; opacity: 0.64;"
          >
            <svg
              aria-label="pause-active-button"
              fill="none"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                fill="#FFFFFF"
              />
              <path
                d="M7.5 7.5C7.5 7.10218 7.65804 6.72064 7.93934 6.43934C8.22064 6.15804 8.60218 6 9 6C9.39782 6 9.77936 6.15804 10.0607 6.43934C10.342 6.72064 10.5 7.10218 10.5 7.5V16.5C10.5 16.8978 10.342 17.2794 10.0607 17.5607C9.77936 17.842 9.39782 18 9 18C8.60218 18 8.22064 17.842 7.93934 17.5607C7.65804 17.2794 7.5 16.8978 7.5 16.5V7.5ZM13.5 7.5C13.5 7.10218 13.658 6.72064 13.9393 6.43934C14.2206 6.15804 14.6022 6 15 6C15.3978 6 15.7794 6.15804 16.0607 6.43934C16.342 6.72064 16.5 7.10218 16.5 7.5V16.5C16.5 16.8978 16.342 17.2794 16.0607 17.5607C15.7794 17.842 15.3978 18 15 18C14.6022 18 14.2206 17.842 13.9393 17.5607C13.658 17.2794 13.5 16.8978 13.5 16.5V7.5Z"
                fill="#2C2824"
              />
            </svg>
          </button>
        </div>
        <div
          class="absolute inset-0 transition-opacity"
          style="background-color: rgb(255, 255, 255); opacity: 0; pointer-events: none; z-index: 1; transition-duration: 0ms; position: absolute;"
        >
          <div
            class="w-full"
            style="padding-bottom: 0px;"
          >
            <div
              class="relative w-full"
            >
              <img
                alt="Carousel desktop image 2"
                class="w-full"
                fetchpriority="auto"
                src="https://cdn.media.amplience.net/i/athleta/2d1_must_have_links_with_ease_v2_mobile?fmt=auto"
                usemap="#hotspotMap-:r1p:"
              />
              <map
                name="hotspotMap-:r1p:"
                role="group"
              >
                <area
                  alt="Go Short"
                  coords="0,0,0,0"
                  data-hotspot-type="url"
                  data-testid="hotspot-Go Short"
                  href="https://athleta.gap.com/browse/new/resort?cid=1022034&mlink=1,1,HP_Spotlight_CTA1"
                  rel="noopener noreferrer"
                  shape="rect"
                  tabindex="0"
                  target="_blank"
                  title="Go Short"
                />
              </map>
            </div>
          </div>
          <button
            aria-label="Pause"
            style="position: absolute; bottom: 30px; left: 15px; background: transparent; cursor: pointer; z-index: 3; display: block; opacity: 0.64;"
          >
            <svg
              aria-label="pause-active-button"
              fill="none"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                fill="#FFFFFF"
              />
              <path
                d="M7.5 7.5C7.5 7.10218 7.65804 6.72064 7.93934 6.43934C8.22064 6.15804 8.60218 6 9 6C9.39782 6 9.77936 6.15804 10.0607 6.43934C10.342 6.72064 10.5 7.10218 10.5 7.5V16.5C10.5 16.8978 10.342 17.2794 10.0607 17.5607C9.77936 17.842 9.39782 18 9 18C8.60218 18 8.22064 17.842 7.93934 17.5607C7.65804 17.2794 7.5 16.8978 7.5 16.5V7.5ZM13.5 7.5C13.5 7.10218 13.658 6.72064 13.9393 6.43934C14.2206 6.15804 14.6022 6 15 6C15.3978 6 15.7794 6.15804 16.0607 6.43934C16.342 6.72064 16.5 7.10218 16.5 7.5V16.5C16.5 16.8978 16.342 17.2794 16.0607 17.5607C15.7794 17.842 15.3978 18 15 18C14.6022 18 14.2206 17.842 13.9393 17.5607C13.658 17.2794 13.5 16.8978 13.5 16.5V7.5Z"
                fill="#2C2824"
              />
            </svg>
          </button>
        </div>
      </div>
    </figure>
  </div>
</DocumentFragment>
`;

exports[`InSortMarketing Component matches snapshot for video tile on mobile 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <figure
      aria-label="Spring Collection Launch"
      class="flex"
      data-testid="in-sort-marketing"
    >
      <div
        class="relative mx-auto bg-white"
      >
        <div
          class="absolute inset-0 transition-opacity"
          style="background-color: rgb(255, 255, 255); opacity: 1; pointer-events: auto; z-index: 2; transition-duration: 0ms; position: static;"
        >
          <div
            class="w-full"
            style="padding-bottom: 0px;"
          >
            <div
              class="relative w-full"
            >
              <img
                alt="a woman in a vest and pants standing in front of a wall"
                class="w-full"
                fetchpriority="auto"
                src="https://cdn.media.amplience.net/i/athleta/S%20D1_F1_HP_Outerwear_S?fmt=auto"
              />
            </div>
          </div>
          <button
            aria-label="Pause"
            style="position: absolute; bottom: 20px; right: 15px; background: transparent; cursor: pointer; z-index: 3; display: block; opacity: 0.64;"
          >
            <svg
              aria-label="pause-active-button"
              fill="none"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                fill="#FFFFFF"
              />
              <path
                d="M7.5 7.5C7.5 7.10218 7.65804 6.72064 7.93934 6.43934C8.22064 6.15804 8.60218 6 9 6C9.39782 6 9.77936 6.15804 10.0607 6.43934C10.342 6.72064 10.5 7.10218 10.5 7.5V16.5C10.5 16.8978 10.342 17.2794 10.0607 17.5607C9.77936 17.842 9.39782 18 9 18C8.60218 18 8.22064 17.842 7.93934 17.5607C7.65804 17.2794 7.5 16.8978 7.5 16.5V7.5ZM13.5 7.5C13.5 7.10218 13.658 6.72064 13.9393 6.43934C14.2206 6.15804 14.6022 6 15 6C15.3978 6 15.7794 6.15804 16.0607 6.43934C16.342 6.72064 16.5 7.10218 16.5 7.5V16.5C16.5 16.8978 16.342 17.2794 16.0607 17.5607C15.7794 17.842 15.3978 18 15 18C14.6022 18 14.2206 17.842 13.9393 17.5607C13.658 17.2794 13.5 16.8978 13.5 16.5V7.5Z"
                fill="#2C2824"
              />
            </svg>
          </button>
        </div>
        <div
          class="absolute inset-0 transition-opacity"
          style="background-color: rgb(255, 255, 255); opacity: 0; pointer-events: none; z-index: 1; transition-duration: 0ms; position: absolute;"
        >
          <div
            class="w-full"
            style="padding-bottom: 0px;"
          >
            <div
              class="relative w-full"
            >
              <img
                alt="a woman in a vest and pants standing in front of a wall"
                class="w-full"
                fetchpriority="auto"
                src="https://cdn.media.amplience.net/i/athleta/S%20D1_F1_HP_Outerwear_S?fmt=auto"
              />
            </div>
          </div>
          <button
            aria-label="Pause"
            style="position: absolute; bottom: 20px; right: 15px; background: transparent; cursor: pointer; z-index: 3; display: block; opacity: 0.64;"
          >
            <svg
              aria-label="pause-active-button"
              fill="none"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                fill="#FFFFFF"
              />
              <path
                d="M7.5 7.5C7.5 7.10218 7.65804 6.72064 7.93934 6.43934C8.22064 6.15804 8.60218 6 9 6C9.39782 6 9.77936 6.15804 10.0607 6.43934C10.342 6.72064 10.5 7.10218 10.5 7.5V16.5C10.5 16.8978 10.342 17.2794 10.0607 17.5607C9.77936 17.842 9.39782 18 9 18C8.60218 18 8.22064 17.842 7.93934 17.5607C7.65804 17.2794 7.5 16.8978 7.5 16.5V7.5ZM13.5 7.5C13.5 7.10218 13.658 6.72064 13.9393 6.43934C14.2206 6.15804 14.6022 6 15 6C15.3978 6 15.7794 6.15804 16.0607 6.43934C16.342 6.72064 16.5 7.10218 16.5 7.5V16.5C16.5 16.8978 16.342 17.2794 16.0607 17.5607C15.7794 17.842 15.3978 18 15 18C14.6022 18 14.2206 17.842 13.9393 17.5607C13.658 17.2794 13.5 16.8978 13.5 16.5V7.5Z"
                fill="#2C2824"
              />
            </svg>
          </button>
        </div>
        <div
          class="absolute inset-0 transition-opacity"
          style="background-color: rgb(255, 255, 255); opacity: 0; pointer-events: none; z-index: 1; transition-duration: 0ms; position: absolute;"
        >
          <div
            class="w-full"
            style="padding-bottom: 0px;"
          >
            <div
              class="relative w-full"
            >
              <img
                alt="a woman in a vest and pants standing in front of a wall"
                class="w-full"
                fetchpriority="auto"
                src="https://cdn.media.amplience.net/i/athleta/S%20D1_F1_HP_Outerwear_S?fmt=auto"
              />
            </div>
          </div>
          <button
            aria-label="Pause"
            style="position: absolute; bottom: 20px; right: 15px; background: transparent; cursor: pointer; z-index: 3; display: block; opacity: 0.64;"
          >
            <svg
              aria-label="pause-active-button"
              fill="none"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                fill="#FFFFFF"
              />
              <path
                d="M7.5 7.5C7.5 7.10218 7.65804 6.72064 7.93934 6.43934C8.22064 6.15804 8.60218 6 9 6C9.39782 6 9.77936 6.15804 10.0607 6.43934C10.342 6.72064 10.5 7.10218 10.5 7.5V16.5C10.5 16.8978 10.342 17.2794 10.0607 17.5607C9.77936 17.842 9.39782 18 9 18C8.60218 18 8.22064 17.842 7.93934 17.5607C7.65804 17.2794 7.5 16.8978 7.5 16.5V7.5ZM13.5 7.5C13.5 7.10218 13.658 6.72064 13.9393 6.43934C14.2206 6.15804 14.6022 6 15 6C15.3978 6 15.7794 6.15804 16.0607 6.43934C16.342 6.72064 16.5 7.10218 16.5 7.5V16.5C16.5 16.8978 16.342 17.2794 16.0607 17.5607C15.7794 17.842 15.3978 18 15 18C14.6022 18 14.2206 17.842 13.9393 17.5607C13.658 17.2794 13.5 16.8978 13.5 16.5V7.5Z"
                fill="#2C2824"
              />
            </svg>
          </button>
        </div>
        <div
          class="absolute inset-0 transition-opacity"
          style="background-color: rgb(255, 255, 255); opacity: 0; pointer-events: none; z-index: 1; transition-duration: 0ms; position: absolute;"
        >
          <div
            class="w-full"
            style="padding-bottom: 0px;"
          >
            <div
              class="relative w-full"
            >
              <img
                alt="a woman in a vest and pants standing in front of a wall"
                class="w-full"
                fetchpriority="auto"
                src="https://cdn.media.amplience.net/i/athleta/S%20D1_F1_HP_Outerwear_S?fmt=auto"
              />
            </div>
          </div>
          <button
            aria-label="Pause"
            style="position: absolute; bottom: 20px; right: 15px; background: transparent; cursor: pointer; z-index: 3; display: block; opacity: 0.64;"
          >
            <svg
              aria-label="pause-active-button"
              fill="none"
              height="24"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
                fill="#FFFFFF"
              />
              <path
                d="M7.5 7.5C7.5 7.10218 7.65804 6.72064 7.93934 6.43934C8.22064 6.15804 8.60218 6 9 6C9.39782 6 9.77936 6.15804 10.0607 6.43934C10.342 6.72064 10.5 7.10218 10.5 7.5V16.5C10.5 16.8978 10.342 17.2794 10.0607 17.5607C9.77936 17.842 9.39782 18 9 18C8.60218 18 8.22064 17.842 7.93934 17.5607C7.65804 17.2794 7.5 16.8978 7.5 16.5V7.5ZM13.5 7.5C13.5 7.10218 13.658 6.72064 13.9393 6.43934C14.2206 6.15804 14.6022 6 15 6C15.3978 6 15.7794 6.15804 16.0607 6.43934C16.342 6.72064 16.5 7.10218 16.5 7.5V16.5C16.5 16.8978 16.342 17.2794 16.0607 17.5607C15.7794 17.842 15.3978 18 15 18C14.6022 18 14.2206 17.842 13.9393 17.5607C13.658 17.2794 13.5 16.8978 13.5 16.5V7.5Z"
                fill="#2C2824"
              />
            </svg>
          </button>
        </div>
      </div>
    </figure>
  </div>
</DocumentFragment>
`;

exports[`InSortMarketing Component matches snapshot when nothing is rendered (type undefined) 1`] = `
<DocumentFragment>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  />
</DocumentFragment>
`;
