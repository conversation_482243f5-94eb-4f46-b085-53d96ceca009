import React, { useContext } from 'react';
import { InSortMarketingData } from './types';
import { BreakpointContext, XLARGE } from '@ecom-next/core/breakpoint-provider';
import ImageTileCarousel from '../NewImageTile/index';
import NewVideoTile from '../NewVideoTile';

/**
 * Renders a marketing component that displays either an image carousel or a video tile,
 * depending on the provided server response data.
 *
 * The component determines its visibility and layout based on the presence of desktop and mobile assets,
 * and the specified type (`imageTile` or `videoTile`).
 *
 * @param serverResponse - The data object containing marketing content, including title, type, image tabs, and video information.
 * @returns A React element rendering either an image carousel or a video tile, with responsive visibility classes.
 */
const InSortMarketing = (serverResponse: InSortMarketingData) => {
  const { smallerThan } = useContext(BreakpointContext);
  const isMobile = smallerThan(XLARGE);
  const deviceData = isMobile ? serverResponse.mobile : serverResponse.desktop;
  const data = Array.isArray(deviceData?.content) ? deviceData.content[0] : undefined;
  const { title = '', type = undefined, imageTabs = {}, video = {} } = data || {};

  if (!data || !type) {
    return null;
  }

  const visibility = (() => {
    const isVideo = type === 'videoTile';

    const hasDesktopImages = () => !!(imageTabs?.desktopCarousel?.length || imageTabs?.desktopImage || serverResponse?.desktop?.content?.length);

    const hasMobileImages = () => !!(imageTabs?.mobileCarousel?.length || imageTabs?.image || serverResponse?.mobile?.content?.length);

    const hasDesktop = isVideo ? Boolean(video?.desktop) : hasDesktopImages();

    const hasMobile = isVideo ? Boolean(video?.mobile) : hasMobileImages();

    if (hasDesktop && hasMobile) return 'flex';
    if (hasDesktop) return 'hidden xl:flex';
    if (hasMobile) return 'flex xl:hidden';
    return 'hidden';
  })();

  return (
    <figure className={visibility} aria-label={title} data-testid='in-sort-marketing'>
      {type === 'imageTile' ? <ImageTileCarousel {...(data as any)} /> : <NewVideoTile {...(data as any)} />}
    </figure>
  );
};

export default InSortMarketing;
