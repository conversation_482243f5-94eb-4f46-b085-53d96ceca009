import React from 'react';
import { render, fireEvent } from 'test-utils';
import { renderLeadingCharUnderline, getDetailsContent, <PERSON>s } from './common-utils';

describe('renderLeadingCharUnderline', () => {
  describe('Athleta brand', () => {
    it('returns null if text is undefined', () => {
      expect(renderLeadingCharUnderline(undefined, undefined, Brands.Athleta)).toBeNull();
    });

    it('returns null if text is empty', () => {
      expect(renderLeadingCharUnderline('', undefined, Brands.Athleta)).toBeNull();
    });

    it('renders text underlined if no leading special character', () => {
      const { container } = render(<>{renderLeadingCharUnderline('Hello', undefined, Brands.Athleta)}</>);
      expect(container.textContent).toBe('Hello');
      const span = container.querySelector('span');
      expect(span).not.toBeNull();
      expect(span?.className).toContain('underline');
    });

    it('renders text underlined if there is a leading special character', () => {
      const { container } = render(<>{renderLeadingCharUnderline('*Hello', undefined, Brands.Athleta)}</>);
      expect(container.textContent).toBe('*Hello');
      const span = container.querySelector('span.underline');
      expect(span).not.toBeNull();
      expect(span.textContent).toBe('Hello');
      expect(span).toHaveClass('underline');
    });

    it('renders leading special character unstyled and rest underlined', () => {
      const { container } = render(<>{renderLeadingCharUnderline('#Hello', undefined, Brands.Athleta)}</>);
      expect(container.textContent).toBe('#Hello');
      const span = container.querySelector('span');
      expect(span).not.toBeNull();
      expect(span).toHaveClass('underline');
      expect(span?.textContent).toBe('Hello');
    });

    it('applies hover:no-underline class when hovered on', () => {
      const { container } = render(<>{renderLeadingCharUnderline('#Hello', true, Brands.Athleta)}</>);
      const span = container.querySelector('span');
      fireEvent.mouseEnter(span);
      expect(span).toHaveClass('hover:no-underline');
    });

    it('trims leading whitespace after special character', () => {
      const { container } = render(<>{renderLeadingCharUnderline('!   Hello', undefined, Brands.Athleta)}</>);
      expect(container.textContent).toBe('!Hello');
      const span = container.querySelector('span');
      expect(span?.textContent).toBe('Hello');
    });
  });

  describe('non-Athleta brand', () => {
    it('returns null if text is undefined', () => {
      expect(renderLeadingCharUnderline(undefined, undefined)).toBeNull();
    });

    it('returns null if text is empty', () => {
      expect(renderLeadingCharUnderline('', undefined)).toBeNull();
    });

    it('does not render underlined text if no leading special characters', () => {
      const { container } = render(<>{renderLeadingCharUnderline('Hello', undefined)}</>);
      expect(container.textContent).toBe('Hello');
      const span = container.querySelector('span');
      expect(span).toBeNull();
    });

    it('renders text underlined if there is a leading special character', () => {
      const { container } = render(<>{renderLeadingCharUnderline('*Hello', undefined)}</>);
      expect(container.textContent).toBe('*Hello');
      const span = container.querySelector('span.underline');
      expect(span).not.toBeNull();
      expect(span.textContent).toBe('Hello');
      expect(span).toHaveClass('underline');
    });

    it('renders leading special character unstyled and rest underlined', () => {
      const { container } = render(<>{renderLeadingCharUnderline('#Hello', undefined)}</>);
      expect(container.textContent).toBe('#Hello');
      const span = container.querySelector('span');
      expect(span).not.toBeNull();
      expect(span).toHaveClass('underline');
      expect(span?.textContent).toBe('Hello');
    });

    it('applies hover:no-underline class when hovered on', () => {
      const { container } = render(<>{renderLeadingCharUnderline('#Hello', true)}</>);
      const span = container.querySelector('span');
      fireEvent.mouseEnter(span);
      expect(span).toHaveClass('hover:no-underline');
    });

    it('trims leading whitespace after special character', () => {
      const { container } = render(<>{renderLeadingCharUnderline('!   Hello', undefined)}</>);
      expect(container.textContent).toBe('!Hello');
      const span = container.querySelector('span');
      expect(span?.textContent).toBe('Hello');
    });
  });
});

describe('getDetailsContent', () => {
  const pemoleCode = 'PROMO123';

  it('returns htmlModalUrl if provided', () => {
    expect(getDetailsContent(Brands.Athleta, pemoleCode, 'https://custom.url')).toBe('https://custom.url');
  });

  it('returns Athleta US URL', () => {
    const url = getDetailsContent(Brands.Athleta, pemoleCode, undefined, 'en_US');
    expect(url).toContain('https://secure-athleta.gap.com');
    expect(url).toContain(`promoId=${pemoleCode}`);
  });

  it('returns Athleta CA URL', () => {
    const url = getDetailsContent(Brands.Athleta, pemoleCode, undefined, 'en_CA');
    expect(url).toContain('https://secure-athleta.gapcanada.ca');
    expect(url).toContain(`promoId=${pemoleCode}`);
  });

  it('returns BananaRepublic US URL', () => {
    const url = getDetailsContent(Brands.BananaRepublic, pemoleCode, undefined, 'en_US');
    expect(url).toContain('https://secure-bananarepublic.gap.com');
    expect(url).toContain(`promoId=${pemoleCode}`);
  });

  it('returns BananaRepublic CA URL', () => {
    const url = getDetailsContent(Brands.BananaRepublic, pemoleCode, undefined, 'fr_CA');
    expect(url).toContain('https://secure-bananarepublic.gapcanada.ca');
    expect(url).toContain(`promoId=${pemoleCode}`);
  });

  it('returns OldNavy US URL', () => {
    const url = getDetailsContent(Brands.OldNavy, pemoleCode, undefined, 'en_US');
    expect(url).toContain('https://secure-oldnavy.gap.com');
    expect(url).toContain(`promoId=${pemoleCode}`);
  });

  it('returns OldNavy CA URL', () => {
    const url = getDetailsContent(Brands.OldNavy, pemoleCode, undefined, 'en_CA');
    expect(url).toContain('https://secure-oldnavy.gapcanada.ca');
    expect(url).toContain(`promoId=${pemoleCode}`);
  });

  it('returns Gap CA URL', () => {
    const url = getDetailsContent(Brands.Gap, pemoleCode, undefined, 'en_CA');
    expect(url).toBe(`https://www.gapcanada.ca/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=${pemoleCode}`);
  });

  it('returns Gap US URL by default', () => {
    const url = getDetailsContent(Brands.Gap, pemoleCode);
    expect(url).toBe(`https://www.gap.com/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=${pemoleCode}`);
  });

  it('returns Gap US URL if locale is en_US', () => {
    const url = getDetailsContent(Brands.Gap, pemoleCode, undefined, 'en_US');
    expect(url).toBe(`https://www.gap.com/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=${pemoleCode}`);
  });

  it('returns Gap US URL for unknown locale', () => {
    const url = getDetailsContent(Brands.Gap, pemoleCode, undefined, 'es_MX');
    expect(url).toBe(`https://www.gap.com/Asset_Archive/AllBrands/promoAPI/promo_lookup_details.html?promoId=${pemoleCode}`);
  });
});

describe('Brands enum', () => {
  it('should have correct values', () => {
    expect(Brands.Athleta).toBe('at');
    expect(Brands.BananaRepublic).toBe('br');
    expect(Brands.BananaRepublicFactoryStore).toBe('brfs');
    expect(Brands.Gap).toBe('gap');
    expect(Brands.GapFactoryStore).toBe('gapfs');
    expect(Brands.OldNavy).toBe('on');
  });
});
