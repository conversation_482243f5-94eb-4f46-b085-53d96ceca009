import React from 'react';
import { ImageData, ImageTileContent } from '../NewImageTile/utils';
import { CategoryItem, FeaturedCategoriesData, FeaturedCategoryResponseType } from './types';

export type ArrowButtonProps = React.ButtonHTMLAttributes<HTMLButtonElement> & {
  direction: 'left' | 'right';
  disabled: boolean;
  onClick: () => void;
};

const PREFIX = 'Featured categories';
const CAROUSEL = `${PREFIX} carousel`;
const GRID = `${PREFIX} grid`;
const COLLECTION = `${PREFIX} collection`;

export const getSectionLabel = (isMobile: boolean, isCarousel: boolean, showArrows: boolean) => {
  if (isMobile) {
    return isCarousel ? CAROUSEL : GRID;
  }
  return isCarousel && showArrows ? CAROUSEL : COLLECTION;
};

const defaultCarouselSettings = {
  autoplay: true,
  looping: 'infinite',
  rotationTime: 2000,
  transitionTime: 0,
  transitionType: 'fade',
};

export const transformFCDataToImageTileCarousel = ({
  styling,
  desktopCarousel,
  mobileCarousel,
  featureFlags,
  buildFeaturedCategoryResponse,
}: Pick<FeaturedCategoriesData, 'styling' | 'desktopCarousel' | 'mobileCarousel' | 'featureFlags'> & {
  buildFeaturedCategoryResponse: (featureCategoryItem: CategoryItem) => FeaturedCategoryResponseType;
}) => {
  const imageTileDesktopCarousel = desktopCarousel
    .filter(item => item.type === 'image' && !!item.image?.desktopImage)
    .slice(1)
    .map(item => ({
      desktopImage: item?.image?.desktopImage as unknown as ImageData,
      desktopCarouselAltText: item.image?.desktopAccessibilityAltText || '',
    }));

  const imageTileMobileCarousel = mobileCarousel
    .filter(item => item.type === 'image' && !!item.image?.image)
    .slice(1)
    .map(item => ({
      mobileImage: item?.image?.image as unknown as ImageData,
      mobileCarouselAltText: item.image?.accessibilityAltText || '',
    }));

  const imageTileCarouselData: ImageTileContent = {
    styling,
    carouselSettings: defaultCarouselSettings,
    imageTabs: {
      desktopImage: buildFeaturedCategoryResponse(desktopCarousel?.[0]).imageTabs?.desktopImage as any,
      desktopAccessibilityAltText: desktopCarousel?.[0]?.accessibilityAltText,
      desktopCarousel: imageTileDesktopCarousel,
      image: buildFeaturedCategoryResponse(mobileCarousel?.[0]).imageTabs?.image as any,
      mobileCarousel: imageTileMobileCarousel,
      accessibilityAltText: mobileCarousel?.[0]?.accessibilityAltText,
    },
    featureFlags,
    hasUniqueHotzones: true,
  };

  return imageTileCarouselData;
};
