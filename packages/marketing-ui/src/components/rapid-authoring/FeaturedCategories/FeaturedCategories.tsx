'use client';
import React, { useContext } from 'react';
import { BreakpointContext, XLARGE } from '@ecom-next/core/breakpoint-provider';
import NewTile from '../NewImageTile/ImageTile';
import NewVideoTile from '../NewVideoTile';
import ImageTileCarousel from '../NewImageTile';
import { FeaturedCategoriesData, CategoryItem } from './types';
import { getSectionLabel, transformFCDataToImageTileCarousel } from './utils';
import { getStyles } from './styles';
import ArrowButton from './ArrowButton';

/**
 * Renders a featured categories section with a responsive carousel or stacked layout,
 * depending on the current breakpoint and configuration.
 *
 * @param serverResponse - The data object containing featured categories, layout, styling, and feature flags.
 * @returns A React component displaying featured categories as a carousel or stacked cards,
 *          with optional navigation arrows for carousel mode.
 *
 * @remarks
 * - Uses `BreakpointContext` to determine if the view is mobile or desktop.
 * - Displays navigation arrows if the carousel is enabled and there are more than a threshold number of items.
 * - Handles smooth scrolling and carousel index management for navigation.
 * - Each category item is rendered using the `NewTile` component.
 *
 * @example
 * ```tsx
 * <FeaturedCategories {...featuredCategoriesData} />
 * ```
 */
export default function FeaturedCategories(serverResponse: FeaturedCategoriesData) {
  const { smallerThan } = useContext(BreakpointContext);
  const isMobile = smallerThan(XLARGE);
  const { headline, desktopCarousel, mobileCarousel, layout, styling, carouselLayout = { layout: 'partial' }, featureFlags } = serverResponse;
  const minIndex = 3;
  const [carouselIndex, setCarouselIndex] = React.useState(minIndex);

  const sliderTrackRef = React.useRef<HTMLDivElement>(null);

  const featuredCatItems = isMobile ? mobileCarousel : desktopCarousel;

  const itemCount = featuredCatItems ? featuredCatItems.length : 0;

  if (!featuredCatItems || featuredCatItems.length === 0) {
    return null; // Render nothing if no items are available
  }
  if (featuredCatItems && !featuredCatItems.some((item: CategoryItem) => item?.video || item?.image)) return null; // Render nothing if no valid items are available

  const isCarousel = (() => {
    if (!isMobile) return true;
    const stacked = layout?.mobile?.stacked ?? [];
    return !stacked.includes(itemCount);
  })();

  const arrowDisplayThreshold = 4;
  const maxIndex = itemCount - 1;
  const showArrows = isCarousel && itemCount > arrowDisplayThreshold;
  const rightAlignControls = true;

  const updateCarouselIndex = (direction: 'left' | 'right') => {
    setCarouselIndex(prevIndex => {
      const newIndex = direction === 'left' ? Math.max(prevIndex - 1, minIndex) : Math.min(prevIndex + 1, maxIndex);
      return newIndex;
    });
  };

  const handleScroll = (direction: 'left' | 'right') => {
    if (sliderTrackRef.current) {
      const childWidth = sliderTrackRef.current?.firstElementChild?.clientWidth || 0;
      const scrollAmount = direction === 'left' ? -childWidth : childWidth;
      sliderTrackRef.current?.scrollBy({ left: scrollAmount, behavior: 'smooth' });
      updateCarouselIndex(direction);
    }
  };

  const styles = getStyles(isCarousel, itemCount);

  const buildFeaturedCategoryResponse = (featureCategoryItem: CategoryItem) => {
    if (featureCategoryItem.type === 'image') {
      return {
        type: featureCategoryItem.type,
        imageTabs: {
          desktopImage: featureCategoryItem.image?.desktopImage,
          image: featureCategoryItem.image?.image,
          desktopAccessibilityAltText: featureCategoryItem.image?.desktopAccessibilityAltText,
          accessibilityAltText: featureCategoryItem.image?.accessibilityAltText,
        },
        featureFlags,
      };
    }
    return {
      ...featureCategoryItem,
      link: featureCategoryItem?.video?.[isMobile ? 'mobile' : 'desktop']?.link,
      styling,
      featureFlags,
      rightAlignControls,
    };
  };

  const sectionLabel = getSectionLabel(isMobile, isCarousel, showArrows);

  const viewport = isMobile ? 'mobile' : 'desktop';
  const authorableContainerSpacing = {
    paddingBottom: `${styling?.padding?.[viewport] ?? 0}px`,
  } as const;

  if (carouselLayout.layout === 'fullWidth' || featuredCatItems.length === 1) {
    const imageTileCarouselData = transformFCDataToImageTileCarousel({ desktopCarousel, mobileCarousel, featureFlags, styling, buildFeaturedCategoryResponse });
    return <ImageTileCarousel {...imageTileCarouselData} />;
  }

  return (
    <section className={styles.featuredCategoriesContainer} style={authorableContainerSpacing} aria-label={headline}>
      {headline && <header className={styles.header}>{headline}</header>}
      <section aria-label={sectionLabel} className={styles.slider}>
        {showArrows && <ArrowButton direction='left' disabled={carouselIndex === minIndex} onClick={() => handleScroll('left')} />}
        <article className={styles.sliderTrack} ref={sliderTrackRef}>
          {featuredCatItems.map((featuredCatItem, index) => (
            <figure key={index} tabIndex={0} className={styles.featuredCategoryCard} role='group'>
              {featuredCatItem?.type === 'image' ? (
                <NewTile {...(buildFeaturedCategoryResponse(featuredCatItem) as any)} />
              ) : (
                <NewVideoTile {...(buildFeaturedCategoryResponse(featuredCatItem) as any)} />
              )}
            </figure>
          ))}
        </article>
        {showArrows && <ArrowButton direction='right' disabled={carouselIndex === maxIndex} onClick={() => handleScroll('right')} />}
      </section>
    </section>
  );
}
