import React from 'react';
import { render as renderWithProviders } from 'test-utils';
import { Brands } from '@ecom-next/core/react-stitch';
import { AppState } from '@ecom-next/sitewide/app-state-provider';
import { screen, fireEvent } from '@testing-library/react';
import FeaturedCategories from './FeaturedCategories';
import { twoFramesData, fourFramesData, eightFramesData } from './fixtures/test-data';
import { FeaturedCategoriesData } from './types';

const desktopRenderOptions = {
  appState: {
    brandName: Brands.Athleta,
  } as unknown as AppState,
  breakpoint: 'large',
} as any;

const mobileRenderOptions = {
  appState: {
    brandName: Brands.Athleta,
  } as unknown as AppState,
  breakpoint: 'small',
} as any;

describe('Rapid authoring - FeaturedCategories', () => {
  describe('desktop', () => {
    it('should render a snapshot for static 2 way split screen when there are two frames', () => {
      const { container } = renderWithProviders(<FeaturedCategories {...twoFramesData} />, desktopRenderOptions);
      expect(container).toMatchSnapshot();
    });

    it('should render a snapshot for static 4 way split screen when there are four frames', () => {
      const { container } = renderWithProviders(<FeaturedCategories {...fourFramesData} />, desktopRenderOptions);
      expect(container).toMatchSnapshot();
    });

    it('should render a snapshot for a carousel when there are eight frames', () => {
      const { container } = renderWithProviders(<FeaturedCategories {...eightFramesData} />, desktopRenderOptions);
      expect(container).toMatchSnapshot();
    });

    it('should render a snapshot for a full width carousel when it is fullBleed', () => {
      const fourFramesDataWithFullBleed: FeaturedCategoriesData = JSON.parse(JSON.stringify(fourFramesData));
      fourFramesDataWithFullBleed.carouselLayout = {
        layout: 'fullWidth',
      };
      const { container } = renderWithProviders(<FeaturedCategories {...fourFramesDataWithFullBleed} />, desktopRenderOptions);
      expect(container).toMatchSnapshot();
      const pauseBtn = screen.getAllByLabelText('Pause')[0];
      expect(pauseBtn).toBeVisible();
    });

    it('should render a single image tile and not a carousel when it is fullBleed with a single image', () => {
      const singleFrameDataWithFullBleed: FeaturedCategoriesData = JSON.parse(JSON.stringify(twoFramesData));
      singleFrameDataWithFullBleed.desktopCarousel = [singleFrameDataWithFullBleed.desktopCarousel[0]];
      singleFrameDataWithFullBleed.carouselLayout = {
        layout: 'fullWidth',
      };
      const { container } = renderWithProviders(<FeaturedCategories {...singleFrameDataWithFullBleed} />, desktopRenderOptions);
      expect(container).toMatchSnapshot();
      const pauseBtn = screen.queryByLabelText('Pause');
      expect(pauseBtn).not.toBeInTheDocument();
    });

    it('should have appropriate roles and accessible labels for accessibility', () => {
      renderWithProviders(<FeaturedCategories {...fourFramesData} />, desktopRenderOptions);
      expect(screen.getByRole('region', { name: /4 frames Image/i })).toBeInTheDocument();
      const cards = screen.getAllByRole('group').filter(card => card.tagName === 'FIGURE');
      expect(cards.length).toBe(fourFramesData.desktopCarousel.length);
    });

    it('should have collection aria label for 2 frames', () => {
      renderWithProviders(<FeaturedCategories {...twoFramesData} />, desktopRenderOptions);
      expect(screen.getByLabelText('Featured categories collection')).toBeInTheDocument();
    });

    it('should have collection aria label for 4 frames', () => {
      renderWithProviders(<FeaturedCategories {...fourFramesData} />, desktopRenderOptions);
      expect(screen.getByLabelText('Featured categories collection')).toBeInTheDocument();
    });

    it('should have carousel aria label for more than 4 frames', () => {
      renderWithProviders(<FeaturedCategories {...eightFramesData} />, desktopRenderOptions);
      expect(screen.getByLabelText('Featured categories carousel')).toBeInTheDocument();
    });

    it('should disable left arrow at min index and right arrow at max index in carousel', () => {
      // Mock scrollBy on the HTMLElement prototype to avoid assignment errors
      if (!HTMLElement.prototype.scrollBy) {
        // @ts-ignore
        HTMLElement.prototype.scrollBy = jest.fn();
      } else {
        jest.spyOn(HTMLElement.prototype, 'scrollBy').mockImplementation(() => {});
      }

      renderWithProviders(<FeaturedCategories {...eightFramesData} />, desktopRenderOptions);
      const leftArrow = screen.getByRole('button', { name: /Previous/i });
      const rightArrow = screen.getByRole('button', { name: /Next/i });

      expect(leftArrow).toBeDisabled();
      expect(rightArrow).not.toBeDisabled();
      fireEvent.click(rightArrow);
      // // After one click, left arrow should be enabled
      expect(leftArrow).not.toBeDisabled();
    });

    it('should scroll carousel when arrow buttons are clicked', () => {
      if (!HTMLElement.prototype.scrollBy) {
        // @ts-ignore
        HTMLElement.prototype.scrollBy = jest.fn();
      } else {
        jest.spyOn(HTMLElement.prototype, 'scrollBy').mockImplementation(() => {});
      }

      renderWithProviders(<FeaturedCategories {...eightFramesData} />, desktopRenderOptions);
      const rightArrow = screen.getByRole('button', { name: /Next/i });
      fireEvent.click(rightArrow);
      // No error thrown, carousel index should update (not directly observable, but no crash)
    });

    it('should allow keyboard navigation to cards', () => {
      renderWithProviders(<FeaturedCategories {...fourFramesData} />, desktopRenderOptions);
      const cards = screen.getAllByRole('group').filter(card => card.tagName === 'FIGURE');
      cards[0].focus();
      expect(cards[0]).toHaveFocus();
      cards[1].focus();
      expect(cards[1]).toHaveFocus();
    });
  });

  describe('mobile', () => {
    it('should render a snapshot for a carousel when there are two frames', () => {
      const { container } = renderWithProviders(<FeaturedCategories {...twoFramesData} />, mobileRenderOptions);
      expect(container).toMatchSnapshot();
    });

    it('should render a snapshot for a 2 x 2 grid when there are four frames', () => {
      const { container } = renderWithProviders(<FeaturedCategories {...fourFramesData} />, mobileRenderOptions);
      expect(container).toMatchSnapshot();
    });

    it('should render a snapshot for a carousel when there are eight frames', () => {
      const { container } = renderWithProviders(<FeaturedCategories {...fourFramesData} />, mobileRenderOptions);
      expect(container).toMatchSnapshot();
    });

    it('should render a snapshot for a full width carousel when it is fullBleed', () => {
      const fourFramesDataWithFullBleed: FeaturedCategoriesData = JSON.parse(JSON.stringify(fourFramesData));
      fourFramesDataWithFullBleed.carouselLayout = {
        layout: 'fullWidth',
      };
      const { container } = renderWithProviders(<FeaturedCategories {...fourFramesDataWithFullBleed} />, mobileRenderOptions);
      expect(container).toMatchSnapshot();
      const pauseBtn = screen.getAllByLabelText('Pause')[0];
      expect(pauseBtn).toBeVisible();
    });

    it('should render a single image tile and not a carousel when it is fullBleed with a single image', () => {
      const singleFrameDataWithFullBleed: FeaturedCategoriesData = JSON.parse(JSON.stringify(twoFramesData));
      singleFrameDataWithFullBleed.desktopCarousel = [singleFrameDataWithFullBleed.mobileCarousel[0]];
      singleFrameDataWithFullBleed.carouselLayout = {
        layout: 'fullWidth',
      };
      const { container } = renderWithProviders(<FeaturedCategories {...singleFrameDataWithFullBleed} />, desktopRenderOptions);
      expect(container).toMatchSnapshot();
      const pauseBtn = screen.queryByLabelText('Pause');
      expect(pauseBtn).not.toBeInTheDocument();
    });

    it('should have accessible carousel and cards', () => {
      renderWithProviders(<FeaturedCategories {...twoFramesData} />, mobileRenderOptions);
      const cards = screen.getAllByRole('group');
      expect(cards.some(card => card.getAttribute('tabindex') === '0')).toBe(true);
    });

    it('should have carousel aria label for 2 frames', () => {
      renderWithProviders(<FeaturedCategories {...twoFramesData} />, mobileRenderOptions);
      expect(screen.getByLabelText('Featured categories carousel')).toBeInTheDocument();
    });

    it('should have grid aria label for 4 frames', () => {
      renderWithProviders(<FeaturedCategories {...fourFramesData} />, mobileRenderOptions);
      expect(screen.getByLabelText('Featured categories grid')).toBeInTheDocument();
    });

    it('should have carousel aria label for more than 4 frames', () => {
      renderWithProviders(<FeaturedCategories {...eightFramesData} />, mobileRenderOptions);
      expect(screen.getByLabelText('Featured categories carousel')).toBeInTheDocument();
    });

    it('should handle arrow button clicks if carousel is shown', () => {
      if (!HTMLElement.prototype.scrollBy) {
        // @ts-ignore
        HTMLElement.prototype.scrollBy = jest.fn();
      } else {
        jest.spyOn(HTMLElement.prototype, 'scrollBy').mockImplementation(() => {});
      }

      renderWithProviders(<FeaturedCategories {...eightFramesData} />, mobileRenderOptions);
      const rightArrow = screen.queryByRole('button', { name: /Next/i });
      if (rightArrow) {
        fireEvent.click(rightArrow);
      }
    });
  });
});
