export interface ImageDimension {
  height: number;
  type: string;
  usage: string;
  width: number;
}

export interface HotspotCoordinates {
  x1: number;
  x2: number;
  y1: number;
  y2: number;
}

export interface Hotspot {
  coordinates?: HotspotCoordinates;
  name?: string;
  type?: string;
  url?: string;
}

export interface CategoryImage {
  dimensions: ImageDimension[];
  hotspots?: Hotspot[];
  url: string;
}

export interface CategoryItem {
  accessibilityAltText?: string;
  image?: { accessibilityAltText?: string; desktopAccessibilityAltText?: string; desktopImage?: CategoryImage; image?: CategoryImage };
  title?: string;
  type?: string;
  video?: VideoTileVideo;
}

export interface FeaturedCategoriesStyling {
  padding: {
    desktop: number;
    mobile: number;
  };
}

export interface FeaturedCategoriesPlatforms {
  android: boolean;
  ios: boolean;
}

export interface FeaturedCategoriesMeta {
  deliveryId: string;
  name: string;
  schema: string;
}

export interface FeaturedCategoriesLayout {
  desktop: {
    linear: number[];
  };
  mobile: {
    linear: number[];
    stacked: number[];
  };
}

export interface FeaturedCategoriesData {
  _meta: FeaturedCategoriesMeta;
  carouselLayout: {
    layout: 'partial' | 'fullWidth';
  };
  desktopCarousel: CategoryItem[];
  featureFlags?: Record<string, boolean>;
  headline?: string;
  layout?: FeaturedCategoriesLayout;
  mobileCarousel: CategoryItem[];
  platforms: FeaturedCategoriesPlatforms;
  styling: FeaturedCategoriesStyling;
  title: string;
  type: string;
}

export interface ImageDimensions {
  height: number;
  type: string;
  usage: string;
  width: number;
}

export interface VideoTileFallbackImage {
  dimensions: ImageDimensions[];
  url: string;
}

export interface VideoTileSVGOverlay {
  dimensions: ImageDimensions[];
  url: string;
}

export interface VideoTileVideoSource {
  altText?: string;
  fallbackImage: VideoTileFallbackImage;
  link?: LinkRedirectUrl;
  svgOverlay?: VideoTileSVGOverlay;
  video: {
    url: string;
  };
}

export interface VideoTileVideo {
  desktop?: VideoTileVideoSource;
  mobile?: VideoTileVideoSource;
}

export interface VideoTileStyling {
  padding: {
    desktop: number;
    mobile: number;
  };
}

export interface VideoTilePlatforms {
  android: boolean;
  ios: boolean;
}

export interface VideoTileMeta {
  deliveryId: string;
  name: string;
  schema: string;
}

export interface LinkRedirectUrl {
  legal?: LegalBannerType;
  linkType: string;
  linkTypeValue?: string;
}

export interface LegalBannerType {
  detailsText?: string;
  pemoleCode?: string;
  textColor?: string;
}
export interface VideoTileContent {
  _meta: VideoTileMeta;
  link: LinkRedirectUrl;
  platforms: VideoTilePlatforms;
  styling: VideoTileStyling;
  title: string;
  type: string;
  video: VideoTileVideo;
  videoControlSettings: {
    controlsIconsColor: string;
  };
}

export type FeaturedCategoryResponseType = CategoryItem & {
  featureFlags?: Record<string, boolean>;
  imageTabs?: {
    accessibilityAltText?: string;
    desktopAccessibilityAltText?: string;
    desktopImage?: CategoryImage | ImageData;
    image?: CategoryImage | ImageData;
  };
  link?: string | LinkRedirectUrl;
  rightAlignControls?: boolean;
  styling?: FeaturedCategoriesData['styling'];
  type?: string;
};
