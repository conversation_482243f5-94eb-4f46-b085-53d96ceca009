// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Rapid authoring - FeaturedCategories desktop should render a single image tile and not a carousel when it is fullBleed with a single image 1`] = `
<div>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="w-full"
      style="padding-bottom: 20px;"
    >
      <div
        class="relative w-full"
      >
        <img
          class="w-full"
          fetchpriority="auto"
          src="https://dam-test.gap.orangelogic.com/AssetLink/2d0tp28i8kcqo35v7mrafq58ut2x555d.jpg"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`Rapid authoring - FeaturedCategories desktop should render a snapshot for a carousel when there are eight frames 1`] = `
<div>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <section
      aria-label="8 frames Image"
      class="flex flex-col items-start stretch lg:pl-0 gap-4 lg:gap-[30px]"
      style="padding-bottom: 20px;"
    >
      <header
        class="pl-2 lg:pl-[40px] text-[max(13px,5.333vw)] lg:text-[max(14px,3.125vw)] font-weight-400"
      >
        8 frames Image
      </header>
      <section
        aria-label="Featured categories carousel"
        class="relative w-full"
      >
        <button
          aria-label="Previous"
          class="invisible lg:visible ch-auto max-w-[50px] w-full z-[1] bottom-0 mb-auto mt-auto top-0 transform-none absolute left-0 opacity-[0.125] cursor-default"
          disabled=""
          type="button"
        >
          <img
            alt="Previous"
            src="https://athletaprod.a.bigcontent.io/v1/static/Carousel_Arrow_Black_Left_1"
          />
        </button>
        <article
          class="flex overflow-x-auto overflow-y-hidden lg:overflow-x-hidden snap-x snap-mandatory scroll-smooth h-auto max-w-full [scrollbar-width:none] [&::-webkit-scrollbar]:hidden lg:m-[0px_32px_40px]"
        >
          <figure
            class="flex-shrink-0 snap-start snap-always w-3/4 md:w-[300px] lg:w-1/4 box-border flex px-2 first:ml-[-8px] first:lg:ml-0"
            role="group"
            tabindex="0"
          >
            <div
              class="w-full"
              style="padding-bottom: 0px;"
            >
              <div
                class="relative w-full"
              >
                <img
                  alt="a woman in a purple top posing for a picture"
                  class="w-full"
                  fetchpriority="auto"
                  src="https://dam-test.gap.orangelogic.com/AssetLink/566s4w7532x2mqu1w3k5j82pnvl1u8b4.jpg"
                />
              </div>
            </div>
          </figure>
          <figure
            class="flex-shrink-0 snap-start snap-always w-3/4 md:w-[300px] lg:w-1/4 box-border flex px-2 first:ml-[-8px] first:lg:ml-0"
            role="group"
            tabindex="0"
          >
            <div
              class="w-full"
              style="padding-bottom: 0px;"
            >
              <div
                class="relative w-full"
              >
                <img
                  alt="a woman in a purple top posing for a picture"
                  class="w-full"
                  fetchpriority="auto"
                  src="https://dam-test.gap.orangelogic.com/AssetLink/h37v472th2876g31sbs3kui1452ifmnw.jpg"
                />
              </div>
            </div>
          </figure>
          <figure
            class="flex-shrink-0 snap-start snap-always w-3/4 md:w-[300px] lg:w-1/4 box-border flex px-2 first:ml-[-8px] first:lg:ml-0"
            role="group"
            tabindex="0"
          >
            <div
              class="w-full"
              style="padding-bottom: 0px;"
            >
              <div
                class="relative w-full"
              >
                <img
                  alt="a woman walking down a sidewalk with a tennis racket"
                  class="w-full"
                  fetchpriority="auto"
                  src="https://dam-test.gap.orangelogic.com/AssetLink/ltoa5100tq5t0a230o2625760il0v31m.jpg"
                />
              </div>
            </div>
          </figure>
          <figure
            class="flex-shrink-0 snap-start snap-always w-3/4 md:w-[300px] lg:w-1/4 box-border flex px-2 first:ml-[-8px] first:lg:ml-0"
            role="group"
            tabindex="0"
          >
            <div
              class="w-full"
              style="padding-bottom: 0px;"
            >
              <div
                class="relative w-full"
              >
                <img
                  alt="a woman in white pants sitting on a bench with a caption that reads pants"
                  class="w-full"
                  fetchpriority="auto"
                  src="https://dam-test.gap.orangelogic.com/AssetLink/13c64rf50f02q2p5652je6hs26y0r43e.jpg"
                />
              </div>
            </div>
          </figure>
          <figure
            class="flex-shrink-0 snap-start snap-always w-3/4 md:w-[300px] lg:w-1/4 box-border flex px-2 first:ml-[-8px] first:lg:ml-0"
            role="group"
            tabindex="0"
          >
            <div
              class="w-full"
              style="padding-bottom: 0px;"
            >
              <div
                class="relative w-full"
              >
                <img
                  alt="a woman in a blue outfit posing for a photo"
                  class="w-full"
                  fetchpriority="auto"
                  src="https://dam-test.gap.orangelogic.com/AssetLink/a1k0fepyxy5405ff23n47dy6620q1m77.jpg"
                />
              </div>
            </div>
          </figure>
          <figure
            class="flex-shrink-0 snap-start snap-always w-3/4 md:w-[300px] lg:w-1/4 box-border flex px-2 first:ml-[-8px] first:lg:ml-0"
            role="group"
            tabindex="0"
          >
            <div
              class="w-full"
              style="padding-bottom: 0px;"
            >
              <div
                class="relative w-full"
              >
                <img
                  alt="a woman in a pink sweatshirt and shorts posing for a photo"
                  class="w-full"
                  fetchpriority="auto"
                  src="https://dam-test.gap.orangelogic.com/AssetLink/p07325phsju0m5lsp3136kip28s75f7a.jpg"
                />
              </div>
            </div>
          </figure>
          <figure
            class="flex-shrink-0 snap-start snap-always w-3/4 md:w-[300px] lg:w-1/4 box-border flex px-2 first:ml-[-8px] first:lg:ml-0"
            role="group"
            tabindex="0"
          >
            <div
              class="w-full"
              style="padding-bottom: 0px;"
            >
              <div
                class="relative w-full"
              >
                <img
                  alt="a woman in a sports bra holding a large stick"
                  class="w-full"
                  fetchpriority="auto"
                  src="https://dam-test.gap.orangelogic.com/AssetLink/dfpl653wfxcb8021wcq47124ok1040ue.jpg"
                />
              </div>
            </div>
          </figure>
          <figure
            class="flex-shrink-0 snap-start snap-always w-3/4 md:w-[300px] lg:w-1/4 box-border flex px-2 first:ml-[-8px] first:lg:ml-0"
            role="group"
            tabindex="0"
          >
            <div
              class="w-full"
              style="padding-bottom: 0px;"
            >
              <div
                class="relative w-full"
              >
                <img
                  alt="a woman in red pants and a red top posing for a magazine"
                  class="w-full"
                  fetchpriority="auto"
                  src="https://dam-test.gap.orangelogic.com/AssetLink/x6s3ou1kc856ct3cerp2uy2srcvs77de.jpg"
                />
              </div>
            </div>
          </figure>
        </article>
        <button
          aria-label="Next"
          class="invisible lg:visible ch-auto max-w-[50px] w-full z-[1] bottom-0 mb-auto mt-auto top-0 transform-none absolute right-0 "
          type="button"
        >
          <img
            alt="Next"
            src="https://athletaprod.a.bigcontent.io/v1/static/Carousel_Arrow_Black_Right_1"
          />
        </button>
      </section>
    </section>
  </div>
</div>
`;

exports[`Rapid authoring - FeaturedCategories desktop should render a snapshot for a full width carousel when it is fullBleed 1`] = `
<div>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="relative mx-auto bg-white"
    >
      <div
        class="absolute inset-0 transition-opacity"
        style="background-color: rgb(255, 255, 255); opacity: 1; pointer-events: auto; z-index: 2; transition-duration: 0ms; position: static;"
      >
        <div
          class="w-full"
          style="padding-bottom: 20px;"
        >
          <div
            class="relative w-full"
          >
            <img
              alt="a woman in white pants sitting on a bench with a caption that reads pants"
              class="w-full"
              fetchpriority="auto"
              src="https://dam-test.gap.orangelogic.com/AssetLink/go487e16406r6i180a0jqc5y3sbp4vif.jpg"
            />
          </div>
        </div>
        <button
          aria-label="Pause"
          style="position: absolute; bottom: 30px; left: 15px; background: transparent; cursor: pointer; z-index: 3; display: block; opacity: 0.64;"
        >
          <svg
            aria-label="pause-active-button"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
              fill="#FFFFFF"
            />
            <path
              d="M7.5 7.5C7.5 7.10218 7.65804 6.72064 7.93934 6.43934C8.22064 6.15804 8.60218 6 9 6C9.39782 6 9.77936 6.15804 10.0607 6.43934C10.342 6.72064 10.5 7.10218 10.5 7.5V16.5C10.5 16.8978 10.342 17.2794 10.0607 17.5607C9.77936 17.842 9.39782 18 9 18C8.60218 18 8.22064 17.842 7.93934 17.5607C7.65804 17.2794 7.5 16.8978 7.5 16.5V7.5ZM13.5 7.5C13.5 7.10218 13.658 6.72064 13.9393 6.43934C14.2206 6.15804 14.6022 6 15 6C15.3978 6 15.7794 6.15804 16.0607 6.43934C16.342 6.72064 16.5 7.10218 16.5 7.5V16.5C16.5 16.8978 16.342 17.2794 16.0607 17.5607C15.7794 17.842 15.3978 18 15 18C14.6022 18 14.2206 17.842 13.9393 17.5607C13.658 17.2794 13.5 16.8978 13.5 16.5V7.5Z"
              fill="#2C2824"
            />
          </svg>
        </button>
      </div>
      <div
        class="absolute inset-0 transition-opacity"
        style="background-color: rgb(255, 255, 255); opacity: 0; pointer-events: none; z-index: 1; transition-duration: 0ms; position: absolute;"
      >
        <div
          class="w-full"
          style="padding-bottom: 20px;"
        >
          <div
            class="relative w-full"
          >
            <img
              alt="a woman in white pants sitting on a bench with a caption that reads pants"
              class="w-full"
              fetchpriority="auto"
              src="https://dam-test.gap.orangelogic.com/AssetLink/go487e16406r6i180a0jqc5y3sbp4vif.jpg"
            />
          </div>
        </div>
        <button
          aria-label="Pause"
          style="position: absolute; bottom: 30px; left: 15px; background: transparent; cursor: pointer; z-index: 3; display: block; opacity: 0.64;"
        >
          <svg
            aria-label="pause-active-button"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
              fill="#FFFFFF"
            />
            <path
              d="M7.5 7.5C7.5 7.10218 7.65804 6.72064 7.93934 6.43934C8.22064 6.15804 8.60218 6 9 6C9.39782 6 9.77936 6.15804 10.0607 6.43934C10.342 6.72064 10.5 7.10218 10.5 7.5V16.5C10.5 16.8978 10.342 17.2794 10.0607 17.5607C9.77936 17.842 9.39782 18 9 18C8.60218 18 8.22064 17.842 7.93934 17.5607C7.65804 17.2794 7.5 16.8978 7.5 16.5V7.5ZM13.5 7.5C13.5 7.10218 13.658 6.72064 13.9393 6.43934C14.2206 6.15804 14.6022 6 15 6C15.3978 6 15.7794 6.15804 16.0607 6.43934C16.342 6.72064 16.5 7.10218 16.5 7.5V16.5C16.5 16.8978 16.342 17.2794 16.0607 17.5607C15.7794 17.842 15.3978 18 15 18C14.6022 18 14.2206 17.842 13.9393 17.5607C13.658 17.2794 13.5 16.8978 13.5 16.5V7.5Z"
              fill="#2C2824"
            />
          </svg>
        </button>
      </div>
      <div
        class="absolute inset-0 transition-opacity"
        style="background-color: rgb(255, 255, 255); opacity: 0; pointer-events: none; z-index: 1; transition-duration: 0ms; position: absolute;"
      >
        <div
          class="w-full"
          style="padding-bottom: 20px;"
        >
          <div
            class="relative w-full"
          >
            <img
              alt="a woman in white pants sitting on a bench with a caption that reads pants"
              class="w-full"
              fetchpriority="auto"
              src="https://dam-test.gap.orangelogic.com/AssetLink/go487e16406r6i180a0jqc5y3sbp4vif.jpg"
            />
          </div>
        </div>
        <button
          aria-label="Pause"
          style="position: absolute; bottom: 30px; left: 15px; background: transparent; cursor: pointer; z-index: 3; display: block; opacity: 0.64;"
        >
          <svg
            aria-label="pause-active-button"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
              fill="#FFFFFF"
            />
            <path
              d="M7.5 7.5C7.5 7.10218 7.65804 6.72064 7.93934 6.43934C8.22064 6.15804 8.60218 6 9 6C9.39782 6 9.77936 6.15804 10.0607 6.43934C10.342 6.72064 10.5 7.10218 10.5 7.5V16.5C10.5 16.8978 10.342 17.2794 10.0607 17.5607C9.77936 17.842 9.39782 18 9 18C8.60218 18 8.22064 17.842 7.93934 17.5607C7.65804 17.2794 7.5 16.8978 7.5 16.5V7.5ZM13.5 7.5C13.5 7.10218 13.658 6.72064 13.9393 6.43934C14.2206 6.15804 14.6022 6 15 6C15.3978 6 15.7794 6.15804 16.0607 6.43934C16.342 6.72064 16.5 7.10218 16.5 7.5V16.5C16.5 16.8978 16.342 17.2794 16.0607 17.5607C15.7794 17.842 15.3978 18 15 18C14.6022 18 14.2206 17.842 13.9393 17.5607C13.658 17.2794 13.5 16.8978 13.5 16.5V7.5Z"
              fill="#2C2824"
            />
          </svg>
        </button>
      </div>
      <div
        class="absolute inset-0 transition-opacity"
        style="background-color: rgb(255, 255, 255); opacity: 0; pointer-events: none; z-index: 1; transition-duration: 0ms; position: absolute;"
      >
        <div
          class="w-full"
          style="padding-bottom: 20px;"
        >
          <div
            class="relative w-full"
          >
            <img
              alt="a woman in white pants sitting on a bench with a caption that reads pants"
              class="w-full"
              fetchpriority="auto"
              src="https://dam-test.gap.orangelogic.com/AssetLink/go487e16406r6i180a0jqc5y3sbp4vif.jpg"
            />
          </div>
        </div>
        <button
          aria-label="Pause"
          style="position: absolute; bottom: 30px; left: 15px; background: transparent; cursor: pointer; z-index: 3; display: block; opacity: 0.64;"
        >
          <svg
            aria-label="pause-active-button"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
              fill="#FFFFFF"
            />
            <path
              d="M7.5 7.5C7.5 7.10218 7.65804 6.72064 7.93934 6.43934C8.22064 6.15804 8.60218 6 9 6C9.39782 6 9.77936 6.15804 10.0607 6.43934C10.342 6.72064 10.5 7.10218 10.5 7.5V16.5C10.5 16.8978 10.342 17.2794 10.0607 17.5607C9.77936 17.842 9.39782 18 9 18C8.60218 18 8.22064 17.842 7.93934 17.5607C7.65804 17.2794 7.5 16.8978 7.5 16.5V7.5ZM13.5 7.5C13.5 7.10218 13.658 6.72064 13.9393 6.43934C14.2206 6.15804 14.6022 6 15 6C15.3978 6 15.7794 6.15804 16.0607 6.43934C16.342 6.72064 16.5 7.10218 16.5 7.5V16.5C16.5 16.8978 16.342 17.2794 16.0607 17.5607C15.7794 17.842 15.3978 18 15 18C14.6022 18 14.2206 17.842 13.9393 17.5607C13.658 17.2794 13.5 16.8978 13.5 16.5V7.5Z"
              fill="#2C2824"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</div>
`;

exports[`Rapid authoring - FeaturedCategories desktop should render a snapshot for static 2 way split screen when there are two frames 1`] = `
<div>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <section
      aria-label="2 frames Image"
      class="flex flex-col items-start stretch lg:pl-0 gap-4 lg:gap-[30px]"
      style="padding-bottom: 20px;"
    >
      <header
        class="pl-2 lg:pl-[40px] text-[max(13px,5.333vw)] lg:text-[max(14px,3.125vw)] font-weight-400"
      >
        2 frames Image
      </header>
      <section
        aria-label="Featured categories collection"
        class="relative w-full"
      >
        <article
          class="flex overflow-x-auto overflow-y-hidden lg:overflow-x-hidden snap-x snap-mandatory scroll-smooth h-auto max-w-full [scrollbar-width:none] [&::-webkit-scrollbar]:hidden lg:m-[0px_32px_40px] lg:[&>*]:flex-1"
        >
          <figure
            class="flex-shrink-0 snap-start snap-always w-3/4 lg:w-auto box-border flex px-2 first:ml-[-8px] first:lg:ml-0"
            role="group"
            tabindex="0"
          >
            <div
              class="w-full"
              style="padding-bottom: 0px;"
            >
              <div
                class="relative w-full"
              >
                <img
                  alt="a woman in a purple top posing for a picture"
                  class="w-full"
                  fetchpriority="auto"
                  src="https://dam-test.gap.orangelogic.com/AssetLink/2d0tp28i8kcqo35v7mrafq58ut2x555d.jpg"
                />
              </div>
            </div>
          </figure>
          <figure
            class="flex-shrink-0 snap-start snap-always w-3/4 lg:w-auto box-border flex px-2 first:ml-[-8px] first:lg:ml-0"
            role="group"
            tabindex="0"
          >
            <div
              class="w-full"
              style="padding-bottom: 0px;"
            >
              <div
                class="relative w-full"
              >
                <img
                  alt="a woman in a purple top posing for a picture"
                  class="w-full"
                  fetchpriority="auto"
                  src="https://dam-test.gap.orangelogic.com/AssetLink/i64736t2f6k7dluwn0g882c6v7y70cry.jpg"
                />
              </div>
            </div>
          </figure>
        </article>
      </section>
    </section>
  </div>
</div>
`;

exports[`Rapid authoring - FeaturedCategories desktop should render a snapshot for static 4 way split screen when there are four frames 1`] = `
<div>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <section
      aria-label="4 frames Image"
      class="flex flex-col items-start stretch lg:pl-0 gap-4 lg:gap-[30px]"
      style="padding-bottom: 20px;"
    >
      <header
        class="pl-2 lg:pl-[40px] text-[max(13px,5.333vw)] lg:text-[max(14px,3.125vw)] font-weight-400"
      >
        4 frames Image
      </header>
      <section
        aria-label="Featured categories collection"
        class="relative w-full"
      >
        <article
          class="flex overflow-x-auto overflow-y-hidden lg:overflow-x-hidden snap-x snap-mandatory scroll-smooth h-auto max-w-full [scrollbar-width:none] [&::-webkit-scrollbar]:hidden lg:m-[0px_32px_40px]"
        >
          <figure
            class="flex-shrink-0 snap-start snap-always md:w-1/4 box-border flex px-2 first:ml-[-8px] first:lg:ml-0"
            role="group"
            tabindex="0"
          >
            <div
              class="w-full"
              style="padding-bottom: 0px;"
            >
              <div
                class="relative w-full"
              >
                <img
                  alt="a woman in a purple top posing for a picture"
                  class="w-full"
                  fetchpriority="auto"
                  src="https://dam-test.gap.orangelogic.com/AssetLink/838d5u37u24125va752ipx8qv38riq84.jpg"
                />
              </div>
            </div>
          </figure>
          <figure
            class="flex-shrink-0 snap-start snap-always md:w-1/4 box-border flex px-2 first:ml-[-8px] first:lg:ml-0"
            role="group"
            tabindex="0"
          >
            <div
              class="w-full"
              style="padding-bottom: 0px;"
            >
              <div
                class="relative w-full"
              >
                <img
                  alt="a woman in a purple top posing for a picture"
                  class="w-full"
                  fetchpriority="auto"
                  src="https://dam-test.gap.orangelogic.com/AssetLink/2jb2g7132c88206wm2yacn22m0857e71.jpg"
                />
              </div>
            </div>
          </figure>
          <figure
            class="flex-shrink-0 snap-start snap-always md:w-1/4 box-border flex px-2 first:ml-[-8px] first:lg:ml-0"
            role="group"
            tabindex="0"
          >
            <div
              class="w-full"
              style="padding-bottom: 0px;"
            >
              <div
                class="relative w-full"
              >
                <img
                  alt="a woman walking down a sidewalk with a tennis racket"
                  class="w-full"
                  fetchpriority="auto"
                  src="https://dam-test.gap.orangelogic.com/AssetLink/e12bd4jal5o83wc313g3j4is01076as0.jpg"
                />
              </div>
            </div>
          </figure>
          <figure
            class="flex-shrink-0 snap-start snap-always md:w-1/4 box-border flex px-2 first:ml-[-8px] first:lg:ml-0"
            role="group"
            tabindex="0"
          >
            <div
              class="w-full"
              style="padding-bottom: 0px;"
            >
              <div
                class="relative w-full"
              >
                <img
                  alt="a woman in white pants sitting on a bench with a caption that reads pants"
                  class="w-full"
                  fetchpriority="auto"
                  src="https://dam-test.gap.orangelogic.com/AssetLink/go487e16406r6i180a0jqc5y3sbp4vif.jpg"
                />
              </div>
            </div>
          </figure>
        </article>
      </section>
    </section>
  </div>
</div>
`;

exports[`Rapid authoring - FeaturedCategories mobile should render a single image tile and not a carousel when it is fullBleed with a single image 1`] = `
<div>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  />
</div>
`;

exports[`Rapid authoring - FeaturedCategories mobile should render a snapshot for a 2 x 2 grid when there are four frames 1`] = `
<div>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <section
      aria-label="4 frames Image"
      class="flex flex-col items-start stretch gap-4"
      style="padding-bottom: 10px;"
    >
      <header
        class="pl-2 lg:pl-[40px] text-[max(13px,5.333vw)] lg:text-[max(14px,3.125vw)] font-weight-400"
      >
        4 frames Image
      </header>
      <section
        aria-label="Featured categories grid"
        class="relative w-full"
      >
        <article
          class="grid grid-cols-2 gap-4"
        >
          <figure
            class="flex flex-col box-border"
            role="group"
            tabindex="0"
          >
            <div
              class="w-full"
              style="padding-bottom: 0px;"
            >
              <div
                class="relative w-full"
              >
                <img
                  alt="a woman in a purple crop top posing for a picture"
                  class="w-full"
                  fetchpriority="auto"
                  src="https://dam-test.gap.orangelogic.com/AssetLink/838d5u37u24125va752ipx8qv38riq84.jpg"
                  usemap="#hotspotMap-:r5h:"
                />
                <map
                  name="hotspotMap-:r5h:"
                  role="group"
                >
                  <area
                    alt="zone1"
                    coords="0,0,0,0"
                    data-hotspot-type="text"
                    data-testid="hotspot-zone1"
                    href="#"
                    shape="rect"
                    tabindex="0"
                    title="zone1"
                  />
                </map>
              </div>
            </div>
          </figure>
          <figure
            class="flex flex-col box-border"
            role="group"
            tabindex="0"
          >
            <div
              class="w-full"
              style="padding-bottom: 0px;"
            >
              <div
                class="relative w-full"
              >
                <img
                  alt="a woman in a purple crop top posing for a picture"
                  class="w-full"
                  fetchpriority="auto"
                  src="https://dam-test.gap.orangelogic.com/AssetLink/2jb2g7132c88206wm2yacn22m0857e71.jpg"
                />
              </div>
            </div>
          </figure>
          <figure
            class="flex flex-col box-border"
            role="group"
            tabindex="0"
          >
            <div
              class="w-full"
              style="padding-bottom: 0px;"
            >
              <div
                class="relative w-full"
              >
                <img
                  alt="a woman walking down a sidewalk with a tennis racket"
                  class="w-full"
                  fetchpriority="auto"
                  src="https://dam-test.gap.orangelogic.com/AssetLink/e12bd4jal5o83wc313g3j4is01076as0.jpg"
                />
              </div>
            </div>
          </figure>
          <figure
            class="flex flex-col box-border"
            role="group"
            tabindex="0"
          >
            <div
              class="w-full"
              style="padding-bottom: 0px;"
            >
              <div
                class="relative w-full"
              >
                <img
                  alt="a woman in white pants sitting on a bench with a caption that reads pants"
                  class="w-full"
                  fetchpriority="auto"
                  src="https://dam-test.gap.orangelogic.com/AssetLink/go487e16406r6i180a0jqc5y3sbp4vif.jpg"
                />
              </div>
            </div>
          </figure>
        </article>
      </section>
    </section>
  </div>
</div>
`;

exports[`Rapid authoring - FeaturedCategories mobile should render a snapshot for a carousel when there are eight frames 1`] = `
<div>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <section
      aria-label="4 frames Image"
      class="flex flex-col items-start stretch gap-4"
      style="padding-bottom: 10px;"
    >
      <header
        class="pl-2 lg:pl-[40px] text-[max(13px,5.333vw)] lg:text-[max(14px,3.125vw)] font-weight-400"
      >
        4 frames Image
      </header>
      <section
        aria-label="Featured categories grid"
        class="relative w-full"
      >
        <article
          class="grid grid-cols-2 gap-4"
        >
          <figure
            class="flex flex-col box-border"
            role="group"
            tabindex="0"
          >
            <div
              class="w-full"
              style="padding-bottom: 0px;"
            >
              <div
                class="relative w-full"
              >
                <img
                  alt="a woman in a purple crop top posing for a picture"
                  class="w-full"
                  fetchpriority="auto"
                  src="https://dam-test.gap.orangelogic.com/AssetLink/838d5u37u24125va752ipx8qv38riq84.jpg"
                  usemap="#hotspotMap-:r5t:"
                />
                <map
                  name="hotspotMap-:r5t:"
                  role="group"
                >
                  <area
                    alt="zone1"
                    coords="0,0,0,0"
                    data-hotspot-type="text"
                    data-testid="hotspot-zone1"
                    href="#"
                    shape="rect"
                    tabindex="0"
                    title="zone1"
                  />
                </map>
              </div>
            </div>
          </figure>
          <figure
            class="flex flex-col box-border"
            role="group"
            tabindex="0"
          >
            <div
              class="w-full"
              style="padding-bottom: 0px;"
            >
              <div
                class="relative w-full"
              >
                <img
                  alt="a woman in a purple crop top posing for a picture"
                  class="w-full"
                  fetchpriority="auto"
                  src="https://dam-test.gap.orangelogic.com/AssetLink/2jb2g7132c88206wm2yacn22m0857e71.jpg"
                />
              </div>
            </div>
          </figure>
          <figure
            class="flex flex-col box-border"
            role="group"
            tabindex="0"
          >
            <div
              class="w-full"
              style="padding-bottom: 0px;"
            >
              <div
                class="relative w-full"
              >
                <img
                  alt="a woman walking down a sidewalk with a tennis racket"
                  class="w-full"
                  fetchpriority="auto"
                  src="https://dam-test.gap.orangelogic.com/AssetLink/e12bd4jal5o83wc313g3j4is01076as0.jpg"
                />
              </div>
            </div>
          </figure>
          <figure
            class="flex flex-col box-border"
            role="group"
            tabindex="0"
          >
            <div
              class="w-full"
              style="padding-bottom: 0px;"
            >
              <div
                class="relative w-full"
              >
                <img
                  alt="a woman in white pants sitting on a bench with a caption that reads pants"
                  class="w-full"
                  fetchpriority="auto"
                  src="https://dam-test.gap.orangelogic.com/AssetLink/go487e16406r6i180a0jqc5y3sbp4vif.jpg"
                />
              </div>
            </div>
          </figure>
        </article>
      </section>
    </section>
  </div>
</div>
`;

exports[`Rapid authoring - FeaturedCategories mobile should render a snapshot for a carousel when there are two frames 1`] = `
<div>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <section
      aria-label="2 frames Image"
      class="flex flex-col items-start stretch lg:pl-0 gap-4 lg:gap-[30px]"
      style="padding-bottom: 10px;"
    >
      <header
        class="pl-2 lg:pl-[40px] text-[max(13px,5.333vw)] lg:text-[max(14px,3.125vw)] font-weight-400"
      >
        2 frames Image
      </header>
      <section
        aria-label="Featured categories carousel"
        class="relative w-full"
      >
        <article
          class="flex overflow-x-auto overflow-y-hidden lg:overflow-x-hidden snap-x snap-mandatory scroll-smooth h-auto max-w-full [scrollbar-width:none] [&::-webkit-scrollbar]:hidden lg:m-[0px_32px_40px] lg:[&>*]:flex-1"
        >
          <figure
            class="flex-shrink-0 snap-start snap-always w-3/4 lg:w-auto box-border flex px-2 first:ml-[-8px] first:lg:ml-0"
            role="group"
            tabindex="0"
          >
            <div
              class="w-full"
              style="padding-bottom: 0px;"
            >
              <div
                class="relative w-full"
              >
                <img
                  alt="a woman in a purple crop top posing for a picture"
                  class="w-full"
                  fetchpriority="auto"
                  src="https://dam-test.gap.orangelogic.com/AssetLink/2d0tp28i8kcqo35v7mrafq58ut2x555d.jpg"
                  usemap="#hotspotMap-:r5b:"
                />
                <map
                  name="hotspotMap-:r5b:"
                  role="group"
                >
                  <area
                    alt="Textzone2"
                    coords="0,0,0,0"
                    data-hotspot-type="text"
                    data-testid="hotspot-Textzone2"
                    href="#"
                    shape="rect"
                    tabindex="0"
                    title="Textzone2"
                  />
                </map>
              </div>
            </div>
          </figure>
          <figure
            class="flex-shrink-0 snap-start snap-always w-3/4 lg:w-auto box-border flex px-2 first:ml-[-8px] first:lg:ml-0"
            role="group"
            tabindex="0"
          >
            <div
              class="w-full"
              style="padding-bottom: 0px;"
            >
              <div
                class="relative w-full"
              >
                <img
                  alt="a woman in a purple crop top posing for a picture"
                  class="w-full"
                  fetchpriority="auto"
                  src="https://dam-test.gap.orangelogic.com/AssetLink/i64736t2f6k7dluwn0g882c6v7y70cry.jpg"
                />
              </div>
            </div>
          </figure>
        </article>
      </section>
    </section>
  </div>
</div>
`;

exports[`Rapid authoring - FeaturedCategories mobile should render a snapshot for a full width carousel when it is fullBleed 1`] = `
<div>
  <div
    style="font-family: var(--font-phantom-sans),Helvetica,Arial,Roboto,sans-serif;"
  >
    <div
      class="relative mx-auto bg-white"
    >
      <div
        class="absolute inset-0 transition-opacity"
        style="background-color: rgb(255, 255, 255); opacity: 1; pointer-events: auto; z-index: 2; transition-duration: 0ms; position: static;"
      >
        <div
          class="w-full"
          style="padding-bottom: 10px;"
        >
          <div
            class="relative w-full"
          >
            <img
              alt="a woman in white pants sitting on a bench with a caption that reads pants"
              class="w-full"
              fetchpriority="auto"
              src="https://dam-test.gap.orangelogic.com/AssetLink/go487e16406r6i180a0jqc5y3sbp4vif.jpg"
            />
          </div>
        </div>
        <button
          aria-label="Pause"
          style="position: absolute; bottom: 20px; right: 15px; background: transparent; cursor: pointer; z-index: 3; display: block; opacity: 0.64;"
        >
          <svg
            aria-label="pause-active-button"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
              fill="#FFFFFF"
            />
            <path
              d="M7.5 7.5C7.5 7.10218 7.65804 6.72064 7.93934 6.43934C8.22064 6.15804 8.60218 6 9 6C9.39782 6 9.77936 6.15804 10.0607 6.43934C10.342 6.72064 10.5 7.10218 10.5 7.5V16.5C10.5 16.8978 10.342 17.2794 10.0607 17.5607C9.77936 17.842 9.39782 18 9 18C8.60218 18 8.22064 17.842 7.93934 17.5607C7.65804 17.2794 7.5 16.8978 7.5 16.5V7.5ZM13.5 7.5C13.5 7.10218 13.658 6.72064 13.9393 6.43934C14.2206 6.15804 14.6022 6 15 6C15.3978 6 15.7794 6.15804 16.0607 6.43934C16.342 6.72064 16.5 7.10218 16.5 7.5V16.5C16.5 16.8978 16.342 17.2794 16.0607 17.5607C15.7794 17.842 15.3978 18 15 18C14.6022 18 14.2206 17.842 13.9393 17.5607C13.658 17.2794 13.5 16.8978 13.5 16.5V7.5Z"
              fill="#2C2824"
            />
          </svg>
        </button>
      </div>
      <div
        class="absolute inset-0 transition-opacity"
        style="background-color: rgb(255, 255, 255); opacity: 0; pointer-events: none; z-index: 1; transition-duration: 0ms; position: absolute;"
      >
        <div
          class="w-full"
          style="padding-bottom: 10px;"
        >
          <div
            class="relative w-full"
          >
            <img
              alt="a woman in white pants sitting on a bench with a caption that reads pants"
              class="w-full"
              fetchpriority="auto"
              src="https://dam-test.gap.orangelogic.com/AssetLink/go487e16406r6i180a0jqc5y3sbp4vif.jpg"
            />
          </div>
        </div>
        <button
          aria-label="Pause"
          style="position: absolute; bottom: 20px; right: 15px; background: transparent; cursor: pointer; z-index: 3; display: block; opacity: 0.64;"
        >
          <svg
            aria-label="pause-active-button"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
              fill="#FFFFFF"
            />
            <path
              d="M7.5 7.5C7.5 7.10218 7.65804 6.72064 7.93934 6.43934C8.22064 6.15804 8.60218 6 9 6C9.39782 6 9.77936 6.15804 10.0607 6.43934C10.342 6.72064 10.5 7.10218 10.5 7.5V16.5C10.5 16.8978 10.342 17.2794 10.0607 17.5607C9.77936 17.842 9.39782 18 9 18C8.60218 18 8.22064 17.842 7.93934 17.5607C7.65804 17.2794 7.5 16.8978 7.5 16.5V7.5ZM13.5 7.5C13.5 7.10218 13.658 6.72064 13.9393 6.43934C14.2206 6.15804 14.6022 6 15 6C15.3978 6 15.7794 6.15804 16.0607 6.43934C16.342 6.72064 16.5 7.10218 16.5 7.5V16.5C16.5 16.8978 16.342 17.2794 16.0607 17.5607C15.7794 17.842 15.3978 18 15 18C14.6022 18 14.2206 17.842 13.9393 17.5607C13.658 17.2794 13.5 16.8978 13.5 16.5V7.5Z"
              fill="#2C2824"
            />
          </svg>
        </button>
      </div>
      <div
        class="absolute inset-0 transition-opacity"
        style="background-color: rgb(255, 255, 255); opacity: 0; pointer-events: none; z-index: 1; transition-duration: 0ms; position: absolute;"
      >
        <div
          class="w-full"
          style="padding-bottom: 10px;"
        >
          <div
            class="relative w-full"
          >
            <img
              alt="a woman in white pants sitting on a bench with a caption that reads pants"
              class="w-full"
              fetchpriority="auto"
              src="https://dam-test.gap.orangelogic.com/AssetLink/go487e16406r6i180a0jqc5y3sbp4vif.jpg"
            />
          </div>
        </div>
        <button
          aria-label="Pause"
          style="position: absolute; bottom: 20px; right: 15px; background: transparent; cursor: pointer; z-index: 3; display: block; opacity: 0.64;"
        >
          <svg
            aria-label="pause-active-button"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
              fill="#FFFFFF"
            />
            <path
              d="M7.5 7.5C7.5 7.10218 7.65804 6.72064 7.93934 6.43934C8.22064 6.15804 8.60218 6 9 6C9.39782 6 9.77936 6.15804 10.0607 6.43934C10.342 6.72064 10.5 7.10218 10.5 7.5V16.5C10.5 16.8978 10.342 17.2794 10.0607 17.5607C9.77936 17.842 9.39782 18 9 18C8.60218 18 8.22064 17.842 7.93934 17.5607C7.65804 17.2794 7.5 16.8978 7.5 16.5V7.5ZM13.5 7.5C13.5 7.10218 13.658 6.72064 13.9393 6.43934C14.2206 6.15804 14.6022 6 15 6C15.3978 6 15.7794 6.15804 16.0607 6.43934C16.342 6.72064 16.5 7.10218 16.5 7.5V16.5C16.5 16.8978 16.342 17.2794 16.0607 17.5607C15.7794 17.842 15.3978 18 15 18C14.6022 18 14.2206 17.842 13.9393 17.5607C13.658 17.2794 13.5 16.8978 13.5 16.5V7.5Z"
              fill="#2C2824"
            />
          </svg>
        </button>
      </div>
      <div
        class="absolute inset-0 transition-opacity"
        style="background-color: rgb(255, 255, 255); opacity: 0; pointer-events: none; z-index: 1; transition-duration: 0ms; position: absolute;"
      >
        <div
          class="w-full"
          style="padding-bottom: 10px;"
        >
          <div
            class="relative w-full"
          >
            <img
              alt="a woman in white pants sitting on a bench with a caption that reads pants"
              class="w-full"
              fetchpriority="auto"
              src="https://dam-test.gap.orangelogic.com/AssetLink/go487e16406r6i180a0jqc5y3sbp4vif.jpg"
            />
          </div>
        </div>
        <button
          aria-label="Pause"
          style="position: absolute; bottom: 20px; right: 15px; background: transparent; cursor: pointer; z-index: 3; display: block; opacity: 0.64;"
        >
          <svg
            aria-label="pause-active-button"
            fill="none"
            height="24"
            viewBox="0 0 24 24"
            width="24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M24 12C24 18.6275 18.6275 24 12 24C5.3725 24 0 18.6275 0 12C0 5.3725 5.3725 0 12 0C18.6275 0 24 5.3725 24 12Z"
              fill="#FFFFFF"
            />
            <path
              d="M7.5 7.5C7.5 7.10218 7.65804 6.72064 7.93934 6.43934C8.22064 6.15804 8.60218 6 9 6C9.39782 6 9.77936 6.15804 10.0607 6.43934C10.342 6.72064 10.5 7.10218 10.5 7.5V16.5C10.5 16.8978 10.342 17.2794 10.0607 17.5607C9.77936 17.842 9.39782 18 9 18C8.60218 18 8.22064 17.842 7.93934 17.5607C7.65804 17.2794 7.5 16.8978 7.5 16.5V7.5ZM13.5 7.5C13.5 7.10218 13.658 6.72064 13.9393 6.43934C14.2206 6.15804 14.6022 6 15 6C15.3978 6 15.7794 6.15804 16.0607 6.43934C16.342 6.72064 16.5 7.10218 16.5 7.5V16.5C16.5 16.8978 16.342 17.2794 16.0607 17.5607C15.7794 17.842 15.3978 18 15 18C14.6022 18 14.2206 17.842 13.9393 17.5607C13.658 17.2794 13.5 16.8978 13.5 16.5V7.5Z"
              fill="#2C2824"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</div>
`;
