// AUTO-GENERATED, DO NOT MODIFIED!!! generated using generate-components-map.js.

export const cmsComponentSchemaMap = {
  'https://cms.gap.com/schema/content/v1/cta-or-dropdown.json': 'CTADropdown',
  'https://cms.gap.com/schema/content/v2/category-banner.json': 'CategoryBanner',
  'https://cms.gap.com/schema/content/v2/category-banner-shop-by-size.json': 'CategoryBanner',
  'https://cms.gap.com/schema/v1/content/category-banner-price-cards.json': 'CategoryBannerPriceCards',
  'https://cms.gap.com/schema/v1/content/category-banner-price-cards-carousel.json': 'CategoryBannerPriceCardsCarousel',
  'https://cms.gap.com/schema/content/v1/category-banner.json': 'CategoryBannerV1',
  'https://cms.gap.com/schema/content/v1/category-banner-shop-by-size.json': 'CategoryBannerV1',
  'https://cms.gap.com/schema/content/v1/category-banner-variable-height-carousel.json': 'CategoryBannerVariableHeightCarousel',
  'https://cms.gap.com/schema/content/v1/category-card-variable-grid.json': 'CategoryCardVariableGrid',
  'https://cms.gap.com/schema/content/v1/category-cards.json': 'CategoryCards',
  'https://cms.gap.com/schema/content/v1/circle-navigation.json': 'CircleNavigation',
  'https://cms.gap.com/schema/content/v1/community-feed.json': 'CommunityFeed',
  'https://cms.gap.com/schema/content/v1/community-spotlight.json': 'CommunitySpotlight',
  'https://cms.gap.com/schema/content/v1/edfs.json': 'EveryDayFreeShipping',
  'https://cms.gap.com/schema/content/v1/featured-categories.json': 'FeaturedCategories',
  'https://cms.gap.com/schema/content/v1/flexible-banner-hp.json': 'FlexibleBannerHP',
  'https://cms.gap.com/schema/content/v1/headline.json': 'Headline',
  'https://cms.gap.com/schema/content/v1/ism-double-full-image.json': 'ISMBannerFullImage',
  'https://cms.gap.com/schema/content/v1/ism-single-full-image.json': 'ISMBannerFullImage',
  'https://cms.gap.com/schema/content/v1/ism-double-full-video.json': 'ISMBannerFullVideo',
  'https://cms.gap.com/schema/content/v1/ism-single-full-video.json': 'ISMBannerFullVideo',
  'https://cms.gap.com/schema/content/v1/ism-double-with-icons.json': 'ISMBannerPartialIcons',
  'https://cms.gap.com/schema/content/v1/ism-double-partial-image.json': 'ISMBannerPartialImage',
  'https://cms.gap.com/schema/content/v1/ism-single-partial-image.json': 'ISMBannerPartialImage',
  'https://cms.gap.com/schema/content/v1/ism-double-partial-video.json': 'ISMBannerPartialVideo',
  'https://cms.gap.com/schema/content/v1/ism-single-partial-video.json': 'ISMBannerPartialVideo',
  'https://cms.gap.com/schema/content/v1/ism-double-partial-image-carousel.json': 'ISMCarouselDoublePartialImage',
  'https://cms.gap.com/schema/content/v1/ism-single-full-image-carousel.json': 'ISMCarouselFullImage',
  'https://cms.gap.com/schema/content/v1/ism-double-full-image-carousel.json': 'ISMCarouselFullImage',
  'https://cms.gap.com/schema/content/v1/ism-single-partial-image-carousel.json': 'ISMCarouselSinglePartialImage',
  'https://cms.gap.com/schema/content/v1/left-nav-tile.json': 'LeftNavTile',
  'https://cms.gap.com/schema/content/v1/overlapping-2-image-banner.json': 'Overlapping2ImageBanner',
  'https://cms.gap.com/schema/content/v1/overlapping-3-image-banner.json': 'Overlapping3ImageBanner',
  'https://cms.gap.com/schema/content/v1/partnerships-brand-stories.json': 'PartnershipsAndBrandStories',
  'https://cms.gap.com/schema/content/v1/partnerships-brand-stories-video.json': 'PartnershipsAndBrandStoriesVideo',
  'https://cms.gap.com/schema/content/v1/product-detail-page.json': 'ProductDetailsPage',
  'https://cms.gap.com/schema/content/v1/promo-drawer.json': 'PromoDrawer',
  'https://cms.gap.com/schema/content/v1/promo-sticker.json': 'PromoSticker',
  'https://cms.gap.com/schema/content/v1/rich-text.json': 'RichTextBanner',
  'https://cms.gap.com/schema/content/v1/seo-rich-text.json': 'SEORichText',
  'https://cms.gap.com/schema/content/v1/shopping-bag.json': 'ShoppingBag',
  'https://cms.gap.com/schema/content/v1/sitewide-banner.json': 'SitewideBanner',
  'https://cms.gap.com/schema/content/v1/sitewide-banner-carousel.json': 'SitewideBannerCarousel',
  'https://cms.gap.com/schema/content/v2/sitewide-banner-countdown-clock.json': 'SitewideBannerCountdownClock',
  'https://cms.gap.com/schema/content/v1/spotlight-carousel.json': 'SpotlightCarousel',
  'https://cms.gap.com/schema/content/v1/spotlight.json': 'SpotlightImage',
  'https://cms.gap.com/schema/content/v1/spotlight-split-view.json': 'SpotlightSplitView',
  'https://cms.gap.com/schema/content/v1/spotlight-variable-height.json': 'SpotlightVariableHeight',
  'https://cms.gap.com/schema/content/v1/spotlight-variable-height-video.json': 'SpotlightVariableHeightVideo',
  'https://cms.gap.com/schema/content/v1/spotlight-video.json': 'SpotlightVideo',
  'https://cms.gap.com/schema/content/v1/storytelling-product-collection.json': 'StorytellingAndProductCollection',
  'https://cms.gap.com/schema/content/v1/storytelling-product-collection-video.json': 'StorytellingAndProductCollectionVideo',
  'https://cms.gap.com/schema/content/v1/storytelling-product-rating.json': 'StorytellingAndProductRating',
  'https://cms.gap.com/schema/content/v1/storytelling-product-rating-video.json': 'StorytellingAndProductRatingVideo',
  'https://cms.gap.com/schema/content/v1/sub-category-banner-image-with-text.json': 'SubCategoryBanner',
  'https://cms.gap.com/schema/content/v1/sub-category-banner-variable-height-carousel.json': 'SubCategoryBannerVariableHeightCarousel',
  'https://cms.gap.com/schema/content/v1/sub-category-banner-with-icons.json': 'SubCategoryBannerWithIcons',
  'https://cms.gap.com/schema/content/v1/text-navigation-carousel.json': 'TextNavigation',
  'https://cms.gap.com/schema/content/v1/text-navigation-exposed.json': 'TextNavigation',
  'https://cms.gap.com/schema/content/v1/text-navigation-dropdown.json': 'TextNavigation',
  'https://cms.gap.com/schema/content/v1/vertical-spacer.json': 'VerticalSpacer',
  'https://cms.gap.com/schema/content/v2/visual-navigation-carousel-v2.json': 'VisualNavigationCarouselV2',
  'https://cms.gap.com/schema/content/v1/wayfinding-and-product-cards.json': 'WayfindingAndProductCards',
  'https://cms.gap.com/schema/content/v1/visual-navigation.json': 'VisualNavigation/VisualNavigation',
  'https://cms.gap.com/schema/content/v1/visual-navigation-size-toggle.json': 'VisualNavigation/VisualNavigationSizeToggle',
  'https://cms.gap.com/schema/content/v1/visual-navigation-size-toggle-carousel.json': 'VisualNavigationCarousel/SizeToggle',
  'https://cms.gap.com/schema/content/v1/visual-navigation-carousel.json': 'VisualNavigationCarousel/VisualNavigationCarousel',
  'https://cms.gap.com/schema/content/v1/visual-navigation-carousel-with-price.json': 'VisualNavigationCarousel/WithPrice',
  'https://cms.gap.com/schema/content/v1/category-banner-more-to-shop.json': 'CategoryBannerMoreToShop',
  'https://cms.gap.com/schema/content/v1/category-banner-variable-height.json': 'CategoryBannerVariableHeight',
  'https://cms.gap.com/schema/content/v1/sub-category-banner-variable-height.json': 'CategoryBannerVariableHeight',
  'https://cms.gap.com/schema/content/v1/category-link-ribbon-banner.json': 'CategoryLinkRibbonBanner',
  'https://cms.gap.com/schema/content/v1/seo-category-description.json': 'CategorySeoDescription',
  'https://cms.gap.com/schema/content/v1/visual-navigation-with-price.json': 'VisualNavigation',
  'https://cms.gap.com/schema/content/v1/mobileapp-category-carousel.json': 'FeaturedCategories',
  'https://cms.gap.com/schema/content/v1/mobileapp-promo-prominence.json': 'GlobalBanner',
  'https://cms.gap.com/schema/content/v1/mobileapp-image-tile.json': 'NewImageTile',
  'https://cms.gap.com/schema/content/v1/mobileapp-video-tile.json': 'NewVideoTile',
  'https://cms.gap.com/schema/content/v1/mobileapp-product-carousel.json': 'ProductFeed',
  'https://cms.gap.com/schema/content/v1/northstar-global-header-banner.json': 'GlobalHeaderBanner',
  'https://cms.gap.com/schema/content/v1/multi-select-banner.json': 'MultiSelectBanner',
  'https://cms.gap.com/schema/content/v1/multi-select-banner-modular.json': 'MultiSelectBannerXC',
  'https://cms.gap.com/schema/content/v1/xc-image-card.json': 'NewImageTile',
  'https://cms.gap.com/schema/content/v1/xc-video-card.json': 'NewVideoTile',
  'https://cms.gap.com/schema/content/v1/xc-featured-category-carousel.json': 'FeaturedCategories',
  'https://cms.gap.com/schema/content/v1/xc-global-banner.json': 'GlobalBanner',
  'https://cms.gap.com/schema/content/v1/xc-product-feed.json': 'ProductFeed',
  'https://cms.gap.com/schema/content/v1/xc-ism.json': 'InSortMarketing',
} as const;

export const jsonComponentsMap = {
  Animation: 'Animation',
  ButtonDropdown: 'ButtonDropdown',
  ButtonList: 'ButtonList',
  CSSAnimation: 'CSSAnimation',
  CTALinksContainer: 'CTALinksContainer',
  Carousel: 'Carousel',
  ComposableButton: 'ComposableButton',
  ComposableButtonBR: 'ComposableButtonBR',
  CreditCardOffer: 'CreditCardOffer',
  DetailsLink: 'DetailsLink',
  DetailsLinkWithPrefix: 'DetailsLinkWithPrefix',
  Dismissible: 'Dismissible',
  EmbeddedVideo: 'EmbeddedVideo',
  HoverImage: 'HoverImage',
  HoverImageWithTransition: 'HoverImageWithTransition',
  InSortMarketingBanner: 'InSortMarketingBanner',
  JumpLink: 'JumpLink',
  LayeredContentModule: 'LayeredContentModule',
  LinkWithModal: 'LinkWithModal',
  MarketingBanner: 'MarketingBanner',
  ModalTrigger: 'ModalTrigger',
  PixleeModule: 'PixleeModule',
  Recommendations: 'Recommendations',
  RedpointPlaceholder: 'RedpointPlaceholder',
  SVGImage: 'SVGImage',
  SVGOverlay: 'SVGOverlay',
  SignInCta: 'SignInCta',
  Tabs: 'Tabs',
  TapToApply: 'TapToApply',
  TextHeadline: 'TextHeadline',
  TextOverlay: 'TextOverlay',
  TriggerableModal: 'TriggerableModal',
  VideoComponent: 'VideoComponent',
} as const;
export type JsonComponentsMap = typeof jsonComponentsMap;
export type CmsComponentsSchemaMap = typeof cmsComponentSchemaMap;
