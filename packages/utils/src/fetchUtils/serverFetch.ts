import 'server-only';
import logger from '@ecom-next/app/logger';
import { headers, cookies } from 'next/headers';
import { mergeHeaders } from './common';
import { MAX_RETRIES, DEFAULT_RETRIES, DEFAULT_TIMEOUT, DEFAULT_REVALIDATE, DEFAULT_SERVER_HEADERS } from './constants';

interface IncomingReqOpts extends RequestInit {
  next?: {
    revalidate?: number;
  };
  retries?: number;
  timeout?: number;
}

type RequestOpts = IncomingReqOpts & {
  headers: HeadersInit;
  next: {
    revalidate: number;
  };
  retries: number;
  timeout: number;
};

type ApiError = Record<string, unknown>;

const DEFAULT_OPTS: RequestOpts = {
  method: 'GET',
  keepalive: true,
  retries: DEFAULT_RETRIES,
  timeout: DEFAULT_TIMEOUT,
  next: {
    revalidate: DEFAULT_REVALIDATE,
  },
  headers: DEFAULT_SERVER_HEADERS,
} as const;

const NR_TRACE_ID = 'X-B3-TraceId';
const NR_SPAN_ID = 'X-B3-SpanId';
const NR_PARENT_SPAN_ID = 'X-B3-ParentSpanId';

const request = <T>(url: string, opts: RequestOpts, isJson: boolean = true): Promise<T> => {
  let retryCount = 0;
  const trigger = async <T>(url: string, opts: RequestOpts): Promise<T> => {
    const options = { ...opts };
    try {
      const signal = AbortSignal.timeout(opts.timeout);
      options.signal = signal;
      const res = await fetch(url, options);
      if (res.ok) {
        return isJson ? await res.json() : ((await res.text()) as T);
      }
      const error: ApiError = isJson ? await res.json() : {};
      error.status = res.status;
      throw error;
    } catch (err: unknown) {
      const mergedContext = {
        req: {
          headers: {
            ...options.headers,
            unknownShopperId: cookies().get('unknownShopperId')?.value?.replaceAll(/\|/g, '') || '',
          },
          url,
        },
        res: err,
        err,
      };
      const message = `Error fetching url: ${url} with retry count: ${retryCount} and status: ${(err as ApiError)?.status}`;

      logger.error(mergedContext, message);

      if (retryCount >= opts.retries || (err as ApiError)?.status === 404 || (err as ApiError)?.status === 400) {
        throw err;
      } else {
        retryCount++;
        return trigger(url, opts);
      }
    }
  };
  return trigger(url, opts);
};

const prepareOpts = (opts: IncomingReqOpts): RequestOpts => {
  const reqOpts = { ...DEFAULT_OPTS, ...opts } as RequestOpts;
  const headersList = headers();

  return {
    ...reqOpts,
    headers: {
      ...mergeHeaders(reqOpts.headers),
      [NR_TRACE_ID]: headersList.get(NR_TRACE_ID) || '',
      [NR_SPAN_ID]: headersList.get(NR_SPAN_ID) || '',
      [NR_PARENT_SPAN_ID]: headersList.get(NR_PARENT_SPAN_ID) || '',
    },
    retries: reqOpts.retries > MAX_RETRIES ? MAX_RETRIES : reqOpts.retries,
  } as RequestOpts;
};

const serverFetch = <T>(url: string, opts: IncomingReqOpts = {}): Promise<T> => {
  return request(url, prepareOpts(opts), true);
};

const serverFetchText = (url: string, opts: IncomingReqOpts = {}): Promise<string> => {
  return request(url, prepareOpts(opts), false);
};

export { serverFetch, serverFetchText, type ApiError };
