import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import datalayer from '@mfe/data-layer';
import { ViewTag } from '@ecom-next/checkout/contexts/types';
import { useOCP } from '../../contexts/GlobalProvider';
import { useDatalayer } from '../useDatalayer';
import { EventData } from '../../../../checkout/src/types';
import { OCPCompositeApiResponse } from '../../utils/compositeAPI/types';

jest.mock('@ecom-next/sitewide/hooks/usePageContext');
jest.mock('../../contexts/GlobalProvider');

jest.mock('@mfe/data-layer', () => {
  return {
    async link(_: EventData) { },
    async view(_: EventData) { },
    async add(_: EventData) { },
    async build() {
      return {
        channel: 'gp:buy',
        page_type: 'OCP',
      };
    },
  };
});

const compositeData = {
  type: 'CUSTOMER-ORDER',
  customer: {
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON><PERSON>',
    email: 'P*****@gap.com',
    phone: '**********',
  },
  order: {
    order_number: '11KNM2C',
    order_date: '2025-08-07T10:20:40-0400',
    brand: 'GP',
    market: 'US',
    currency: 'USD',
    locale: 'en_US',
    express_checkout: false,
  },
  order_summary: {
    subtotal: 194.85,
    savings_summary: {
      markdown_savings: 24.98,
      promotional_savings: 27.95,
      rewards_savings: 0,
      markdown_promo_savings: 52.93,
      discount_pricing_savings: 52.93,
      promo_savings: 0,
      total_savings: 52.93,
    },
    rewards_sub_total: 0,
    merchandise_subtotal_after_savings: 141.92,
    subtotal_after_savings: 141.92,
    shipping_price: 0,
    estimated_tax: 0,
    retail_delivery_fee: 0,
    subtotal_before_gift_cards: 141.92,
    donations_total: 0,
    total_price: 141.92,
    promos_count: 0,
    shipping_method: '3-5 business days',
    shipping_items_count: 2,
  },
  order_discounts: {
    single_unit_price_discount: 27.95,
    product_line_and_brand_discount: 0,
    multiple_unit_price_discount: 0,
    order_level_discount_product_level: 0,
    plcc_discounts_product_level: 0,
    pwp_discount_product_level: 0,
  },
  payments: {
    credit_card_payments: [
      {
        card_type: 'VISA',
        card_type_id: '1',
        display_code: '1111',
        card_brand: null,
      },
    ],
    gift_card_payments: [],
  },
  fulfillment_details: [
    {
      fulfillment_type: 'BOPIS',
      pickup_details: {
        pickup_person: {
          first_name: 'Lucas',
          last_name: 'Reinoso',
        },
        pickup_locations: [
          {
            store_number: '220',
            store_name: 'Glendale Galleria-CA',
            store_timezone: 'America/Los_Angeles',
            phone: '7472970330',
            store_address: {
              address_line_1: '2122 Glendale Galleria',
              address_line_2: 'Space 1161',
              city: 'Glendale',
              state: 'California',
              state_province_code: 'CA',
              country: 'US',
              postal_code: '91210',
            },
          },
        ],
      },
      order_items: [
        {
          item_id: '2',
          shipping_group_id: '9ba5891f-fb16-4cf9-a70d-ddf954c57e10',
          product_style_id: '794603',
          product_sku_id: '7946030023202',
          brand: {
            id: '1',
            abbr_name: 'GP',
          },
          product_name: 'Mid Rise Girlfriend Jeans',
          product_url: 'browse/product.do?pid=7946030023202',
          color: 'dark wash',
          size: '32',
          quantity: 1,
          image_path: 'webcontent/0055/670/495/cn55670495.jpg',
          back_order_date: '',
          web_vendor_name: 'CRYSTAL APPAREL LTD',
          pickup_store: '220',
          is_back_order: false,
          price: {
            regular_price: 69.95,
            sale_price: '69.95',
            discounted_price: '69.95',
            total_price: '69.95',
            savings: '0.00',
            percentage_off: 0,
          },
        },
      ],
      total_items: 1,
      instructions: [
        {
          instruction_type: 'Pickup',
          instruction_text: 'Lucas Reinoso',
        },
      ],
    },
    {
      fulfillment_type: 'SHIP',
      shipping_details: {
        shipping_preferences: [
          {
            shipping_group_id: '9ba5891f-fb16-4cf9-a70d-ddf954c57e10',
            shipping_method: 1,
            min_days_to_deliver: 3,
            max_days_to_deliver: 5,
            delivery_date: '2025-08-14T23:59:59-0400',
            delivery_week_day: 'Fri',
            shipping_description: 'Standard',
            shipping_charge: 0,
          },
        ],
        shipping_address: {
          address_line_1: '54 E Main St',
          address_line_2: null,
          city: 'Columbus',
          state: 'Ohio',
          state_province_code: 'OH',
          country: 'US',
          postal_code: '43081-1502',
        },
        delivery_date: '2025-08-15T03:59:59.000Z',
      },
      order_items: [
        {
          item_id: '1',
          shipping_group_id: '9ba5891f-fb16-4cf9-a70d-ddf954c57e10',
          product_style_id: '891508',
          product_sku_id: '8915080020601',
          brand: {
            id: '1',
            abbr_name: 'GP',
          },
          product_name: 'Kids Low Rise Stride Wide-Leg Jeans',
          product_url: 'browse/product.do?pid=8915080020601',
          color: 'medium wash',
          size: '6  Slim',
          quantity: 1,
          image_path: 'webcontent/0054/751/895/cn54751895.jpg',
          back_order_date: '',
          web_vendor_name: 'THE CIVIL ENGINEERS WOVEN LIMITED',
          is_back_order: false,
          price: {
            regular_price: 54.95,
            sale_price: '54.95',
            discounted_price: '27.00',
            total_price: '54.95',
            savings: '27.95',
            percentage_off: 50.86,
          },
        },
        {
          item_id: '3',
          shipping_group_id: '9ba5891f-fb16-4cf9-a70d-ddf954c57e10',
          product_style_id: '647678',
          product_sku_id: '6476780022001',
          brand: {
            id: '1',
            abbr_name: 'GP',
          },
          product_name: 'High Rise Culotte Pants',
          product_url: 'browse/product.do?pid=6476780022001',
          color: 'true black',
          size: '20',
          quantity: 1,
          image_path: 'webcontent/0019/445/796/cn19445796.jpg',
          back_order_date: '',
          is_back_order: false,
          price: {
            regular_price: 69.95,
            sale_price: '44.97',
            discounted_price: '44.97',
            total_price: '69.95',
            savings: '24.98',
            percentage_off: 35.71,
          },
        },
      ],
      total_items: 2,
      instructions: [],
    },
  ],
  product_analytics: [
    {
      quantity: 1,
      name: 'Kids Low Rise Stride Wide-Leg Jeans',
      category: 'girls jeans',
      bopis: false,
      product_size: '6  Slim',
      sku: '8915080020601',
      price: 27,
      metadata: {
        variant_group: false,
        variant_key: 'SLIM',
      },
      product_id: '891508002',
      product_color: 'medium wash',
      product_fit: '6  Slim',
      style_id: 'Kids Low Rise Stride Wide-Leg Jeans',
      primary_category_name: 'Jeans',
      product_page_type: 'STYLE',
      product_order_level_discount: 0,
      product_line_and_brand_discount: 27.95,
      product_plcc_level_discount: 0,
      product_pwp_level_discount: 0,
      multiple_unit_price_discount: 0,
    },
    {
      quantity: 1,
      name: 'Mid Rise Girlfriend Jeans',
      category: 'petite jeans',
      bopis: true,
      product_size: '32',
      sku: '7946030023202',
      price: 69.95,
      metadata: {
        variant_group: false,
        variant_key: 'REGULAR',
      },
      product_id: '794603002',
      product_color: 'dark wash',
      product_fit: '32',
      style_id: 'Mid Rise Girlfriend Jeans',
      product_page_type: 'STYLE',
      product_order_level_discount: 0,
      product_line_and_brand_discount: 0,
      product_plcc_level_discount: 0,
      product_pwp_level_discount: 0,
      multiple_unit_price_discount: 0,
    },
    {
      quantity: 1,
      name: 'High Rise Culotte Pants',
      category: 'petite pants',
      bopis: false,
      product_size: '20',
      sku: '6476780022001',
      price: 44.97,
      metadata: {
        variant_group: false,
        variant_key: 'REGULAR',
      },
      product_id: '647678002',
      product_color: 'true black',
      product_fit: '20',
      style_id: 'High Rise Culotte Pants',
      product_page_type: 'NONE',
      product_order_level_discount: 0,
      product_line_and_brand_discount: 0,
      product_plcc_level_discount: 0,
      product_pwp_level_discount: 0,
      multiple_unit_price_discount: 0,
    },
  ],
} as unknown as OCPCompositeApiResponse;

describe('useDatalayer', () => {
  (usePageContext as jest.Mock).mockReturnValue({
    brandCode: 1,
    brand: 'gap',
    market: 'us',
    brandName: 'GAP',
    brandAbbr: 'gap',
    locale: 'en_US',
  });
  (useOCP as jest.Mock).mockReturnValue({
    orderNumber: 'ORD123',
  });

  const {
    firePageViewTag,
    getEvergreenTags,
    getBooleanValue,
    getRecognizationStatus,
    getPersonalizationViewTagData,
    formatted,
    getSumOfStringArray,
    buildBrandData,
    getLineItemData,
    getOCPViewTagData,
  } = useDatalayer();

  const addSpy = jest.spyOn(datalayer, 'add');
  const viewSpy = jest.spyOn(datalayer, 'view');

  describe('firePageViewTag', () => {
    it('calls Datalayer.add for pageLoad', async () => {
      const viewTag = { brand_short_name: 'TB', event_name: 'test' } as ViewTag;
      await firePageViewTag(viewTag);
      expect(addSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'test',
          data: expect.objectContaining({
            page_name: 'TB:checkout:Checkout:module:Order Confirmation',
          }),
        })
      );
    });

    it('calls Datalayer.view for other pages', async () => {
      const viewTag = { brand_short_name: 'TB', event_name: 'test' } as ViewTag;
      await firePageViewTag(viewTag);
      expect(viewSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'test',
          data: expect.objectContaining({
            page_name: 'TB:checkout:Checkout:module:Order Confirmation',
          }),
        })
      );
    });
  });

  describe('getEvergreenTags', () => {
    it('returns correct tags for array', () => {
      const evergreens = [
        { AttributeName: 'CARDSTATUS', AttributeValue: 'Gold' },
        { AttributeName: 'CATPREF', AttributeValue: 'Shoes' },
        { AttributeName: 'DIVPREF', AttributeValue: 'Men' },
        { AttributeName: 'BRDS', AttributeValue: 'Low' },
      ];
      expect(getEvergreenTags(evergreens)).toEqual({
        cardholder_status: 'Gold',
        category_preference: 'Shoes',
        division_preference: 'Men',
        discount_sensitivity: 'Low',
      });
    });

    it('returns correct tags for single object', () => {
      const evergreen = { AttributeName: 'CARDSTATUS', AttributeValue: 'Silver' };
      expect(getEvergreenTags(evergreen)).toEqual({
        cardholder_status: 'Silver',
        category_preference: '',
        division_preference: '',
        discount_sensitivity: '',
      });
    });

    it('returns empty tags for undefined', () => {
      expect(getEvergreenTags(undefined)).toEqual({
        cardholder_status: '',
        category_preference: '',
        division_preference: '',
        discount_sensitivity: '',
      });
    });
  });

  describe('getBooleanValue', () => {
    it('returns true for true or "true"', () => {
      expect(getBooleanValue(true)).toBe(true);
      expect(getBooleanValue('true')).toBe(true);
    });
    it('returns false for false or "false"', () => {
      expect(getBooleanValue(false)).toBe(false);
      expect(getBooleanValue('false')).toBe(false);
    });
  });

  describe('getRecognizationStatus', () => {
    it('returns authenticated', () => {
      expect(getRecognizationStatus(true, false, false)).toBe('authenticated');
    });
    it('returns recognized', () => {
      expect(getRecognizationStatus(false, true, false)).toBe('recognized');
    });
    it('returns guest', () => {
      expect(getRecognizationStatus(false, false, true)).toBe('guest');
    });
    it('returns unrecognized', () => {
      expect(getRecognizationStatus(false, false, false)).toBe('unrecognized');
    });
  });

  describe('getPersonalizationViewTagData', () => {
    it('returns correct data for guest', () => {
      const data = getPersonalizationViewTagData({
        customerAttributes: { mtlMember: 'Y', tier: 'Gold' },
        featureSelections: { Evergreens: [{ AttributeName: 'CARDSTATUS', AttributeValue: 'Gold' }] },
        customerUUID: 'uuid',
        userContext: {
          userEmailEncrypt: 'enc',
          userEmailMMEncrypt: 'mm',
          userEmailEncrypt256: '256',
          isLoggedInUser: false,
          isRecognizedUser: false,
          guest: true,
        },
      });
      expect(data.checkout_type).toBe('Guest Customer');
      expect(data.recognition_status).toBe('guest');
      expect(data.mtl_member_status).toBe('Y');
      expect(data.tier_status).toBe('Gold');
      expect(data.cardholder_status).toBe('Gold');
    });

    it('returns correct data for returning customer', () => {
      const data = getPersonalizationViewTagData({
        customerAttributes: { mtlMember: '', tier: '' },
        featureSelections: { Evergreens: [] },
        customerUUID: 'uuid',
        userContext: {
          userEmailEncrypt: '',
          userEmailMMEncrypt: '',
          userEmailEncrypt256: '',
          isLoggedInUser: true,
          isRecognizedUser: false,
          guest: false,
        },
      });
      expect(data.checkout_type).toBe('Returning Customer');
      expect(data.recognition_status).toBe('authenticated');
    });
  });

  describe('formatted', () => {
    it('formats number to 2 decimals', () => {
      expect(formatted(12)).toBe('12.00');
      expect(formatted(12.345)).toBe('12.35');
    });
    it('returns empty string for undefined', () => {
      expect(formatted(undefined)).toBe('');
    });
  });

  describe('getSumOfStringArray', () => {
    it('returns sum of string array', () => {
      expect(getSumOfStringArray(['1.11', '2.22'])).toBeCloseTo(3.33, 2);
    });
    it('returns 0 for undefined', () => {
      expect(getSumOfStringArray(undefined)).toBe(0);
    });
  });

  describe('buildBrandData', () => {
    it('returns brand data', () => {
      expect(buildBrandData('gap', 'us')).toEqual({
        brand_code: "GAP",
        brand_name: "Gap",
        brand_number: '1',
        business_unit_id: 1,
        brand_short_name: 'gp',
        business_unit_abbr_name: "GAP_US_OL",
        business_unit_description: "Gap",
        channel: 'gp:checkout',
        checkout_version: 'Checkout_MVP',
      });
    });
  });

  describe('getLineItemData', () => {
    it('returns undefined for empty input', () => {
      expect(getLineItemData(undefined)).toBeUndefined();
      expect(getLineItemData([])).toBeUndefined();
    });

    it('returns correct data for line items', () => {
      const data = getLineItemData(compositeData);
      expect(data).toBeDefined();
      expect(data && data.product_id && data.product_id[0]).toBe('794603');
      expect(data && data.product_brand && data.product_brand[0]).toBe('GAP');
      // expect(data && data.product_category && data.product_category[0]).toBe('Cat1');
      expect(data && data.product_name && data.product_name[0]).toBe('Mid Rise Girlfriend Jeans');
      expect(data && data.product_quantity && data.product_quantity[0]).toBe(1);
      expect(data && data.product_dropship && data.product_dropship[0]).toBe('false');
      expect(data && data.product_seller_id && data.product_seller_id[0]).toBe('1');
      expect(data && data.product_seller_name && data.product_seller_name[0]).toBe('GAP');
      // expect(data && data.product_markdown_amount && data.product_markdown_amount[0]).toBe('2.00');
      expect(data && data.product_gross_merchandise && data.product_gross_merchandise[0]).toBe('69.95');
      expect(data && data.product_gross_retail && data.product_gross_retail[0]).toBe('69.95');
      expect(data && data.product_net_demand && data.product_net_demand[0]).toBe('69.95');
      expect(data && data.brand_mix).toBe('GAP');
      expect(data && data.order_gross_merchandise).toBe(169.87);
    });
  });

  describe('getOCPViewTagData', () => {
    it('returns correct OCP view tag data', () => {
      const personalizationData = {
        customerAttributes: { mtlMember: 'Y', tier: 'Gold' },
        featureSelections: { Evergreens: [{ AttributeName: 'CARDSTATUS', AttributeValue: 'Gold' }] },
        customerUUID: 'uuid',
        userContext: {
          userEmailEncrypt: 'enc',
          userEmailMMEncrypt: 'mm',
          userEmailEncrypt256: '256',
          isLoggedInUser: false,
          isRecognizedUser: false,
          guest: true,
        },
      };
      const result = getOCPViewTagData({
        personalizationData,
        compositeResponse: compositeData,
      });
      expect(result.event_name).toBe('order confirmation');
      expect(result.page_type).toBe('OrderConfirmation');
      expect(result.brand_number).toBe('1');
      expect(result.country_code).toBe('US');
      expect(result.language_code).toBe('en_US');
      expect(result.id).toBe('ORD123');
      expect(result.order_id).toBe('ORD123');
      expect(result.customer_type).toBe('Guest Customer');
      expect(result.tier_status).toBe('Gold');
    });

    it('returns correct OCP view tag data without personalization', () => {
      const result = getOCPViewTagData({
        compositeResponse: compositeData,
      });
      expect(result.event_name).toBe('order confirmation');
      expect(result.page_type).toBe('OrderConfirmation');
      expect(result.brand_number).toBe('1');
      expect(result.country_code).toBe('US');
      expect(result.language_code).toBe('en_US');
      expect(result.id).toBe('ORD123');
      expect(result.order_id).toBe('ORD123');
      expect(result.customer_type).toBe('');
    });
  });
});
