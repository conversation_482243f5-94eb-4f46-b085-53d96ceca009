import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { default as datalayer } from '@mfe/data-layer';
import { ViewTag, EvergreenType, PersonalizationData } from '@ecom-next/checkout/contexts/types';
import { bizUnitLookup, brandLookupTable, lookupTable } from '@ecom-next/checkout/utils/brandLookUpUtil';
import { useOCP } from '../contexts/GlobalProvider';
import { logNewRelicError, Feature } from '../../../checkout/src/utils/newrelic-logger';
import { DatalayerType } from '../../../checkout/global';
import { FulfillmentOrderItem, FullfillmentDetails, OCPCompositeApiResponse, ProductAnalytics } from '../utils/compositeAPI/types';
import {
  formatted,
  getAllOrderItems,
  getBooleanValue,
  getFulfillmentTypeFlags,
  getOrderPaymentType,
  getOrderSellerType,
  getRecognizationStatus,
  getSumOfStringArray
} from '../utils/datalayerUtils';

interface OCPViewTagDataProps {
  compositeResponse: OCPCompositeApiResponse;
  personalizationData?: PersonalizationData | Record<string, unknown>;
}

export const useDatalayer = () => {
  const { brandCode, locale = 'en_US', market, brandName, brandAbbr } = usePageContext();
  const { orderNumber } = useOCP();

  // @ts-ignore
  const Datalayer: DatalayerType = datalayer;

  const firePageViewTag = (viewTagData: ViewTag): Promise<void> => {
    const data = {
      data: {
        ...viewTagData,
        page_name: `${viewTagData.brand_short_name}:checkout:Checkout:module:Order Confirmation`,
      },
      name: viewTagData?.event_name || '',
    };

    Datalayer.add(data);
    // @ts-ignore
    const { error_message } = Datalayer.view(data);
    // @ts-ignore
    if (typeof newrelic === 'object') newrelic.addPageAction('tealiumEventFired', { orderNumber });

    if (error_message) {
      logNewRelicError(error_message as Error, {
        feature: Feature.OCP_TEALIUM_VIEW_TAG_ERROR,
        caller: 'firePageViewTag()',
        message: `${error_message.toString()}orderNumber:${orderNumber}`,
      });
    }
    // @ts-ignore
    return Datalayer.view(data);
  };

  const getEvergreenTags = (evergreens: EvergreenType | EvergreenType[]) => {
    const evergreenTagList = {
      cardholder_status: '',
      category_preference: '',
      division_preference: '',
      discount_sensitivity: '',
    };
    const processItem = (item: EvergreenType) => {
      switch (item.AttributeName) {
        case 'CARDSTATUS':
          evergreenTagList.cardholder_status = item.AttributeValue;
          break;
        case 'CATPREF':
          evergreenTagList.category_preference = item.AttributeValue;
          break;
        case 'DIVPREF':
          evergreenTagList.division_preference = item.AttributeValue;
          break;
        case 'BRDS':
          evergreenTagList.discount_sensitivity = item.AttributeValue;
          break;
      }
    };
    if (evergreens) {
      if (Array.isArray(evergreens)) {
        evergreens.forEach(processItem);
      } else {
        processItem(evergreens);
      }
    }
    return evergreenTagList;
  };

  const getPersonalizationViewTagData = (personalizationData: PersonalizationData) => {
    const {
      customerAttributes: { mtlMember = '', tier = '' } = {},
      featureSelections: { Evergreens = [] } = {},
      customerUUID,
      userContext: {
        userEmailEncrypt = '',
        userEmailMMEncrypt = '',
        userEmailEncrypt256 = '',
        isLoggedInUser = false,
        isRecognizedUser = false,
        guest = false,
      } = {},
    } = personalizationData || {};
    const isGuestUser = getBooleanValue(guest);
    const recognition_status = getRecognizationStatus(getBooleanValue(isLoggedInUser), getBooleanValue(isRecognizedUser), isGuestUser);
    const checkout_type = isGuestUser ? 'Guest Customer' : 'Returning Customer';
    const { cardholder_status, category_preference, division_preference } = getEvergreenTags(Evergreens);
    const data = {
      tier_status: tier || '',
      checkout_type,
      customer_uuid: customerUUID || '',
      encrypted_customer_email: userEmailEncrypt || '',
      encrypted_customer_email_mm: userEmailMMEncrypt || '',
      hashed_customer_email: userEmailEncrypt256 || '',
      cardholder_status,
      category_preference,
      division_preference,
      recognition_status,
      mtl_member_status: mtlMember || '',
    };
    return data;
  };

  const buildBrandData = (brandCode: string, marketCode: string) => {
    const bizUnitId = bizUnitLookup(brandCode, marketCode);
    const bizUnitData = lookupTable[bizUnitId]();
    const brandData = brandLookupTable[bizUnitData.BRD_CD]();
    const data = {
      brand_code: brandData.ABBR_BRD_NM,
      brand_name: brandData.BRD_NM,
      brand_number: brandData.BRD_CD,
      business_unit_abbr_name: bizUnitData.ABBR_BUS_UN_NM,
      business_unit_description: bizUnitData.BUS_UN_DESC,
      business_unit_id: bizUnitData.BUS_UN_ID,
      brand_short_name: brandData.BRD_SHORT_NM,
      channel: `${brandData.BRD_SHORT_NM}:checkout`,
      checkout_version: 'Checkout_MVP',
    };
    return data;
  };

  const getLineItemData = (compositeResponse: OCPCompositeApiResponse): ViewTag | undefined => {
    const { fulfillment_details, product_analytics } = compositeResponse || {};
    const order_items = getAllOrderItems(fulfillment_details);

    if (!order_items || order_items.length === 0) return undefined;

    const productId: string[] = [];
    const productBrand: string[] = [];
    const productCategory: string[] = [];
    const productName: string[] = [];
    const productQuantity: number[] = [];
    const productSku: string[] = [];
    const productSellerId: string[] = [];
    const productSellerName: string[] = [];
    const productDropship: string[] = [];
    const productCustomerChoiceId: string[] = [];
    const productMarkDown: string[] = [];
    const productGrossMerchandise: string[] = [];
    const productGrossRetail: string[] = [];
    const productNetDemand: string[] = [];
    const productMergeType: string[] = [];
    const productLineAndBrandDiscount: string[] = [];
    const productPWPLevelDiscount: string[] = [];
    const productShippingCost: string[] = [];
    const productShippingOptions: string[] = [];
    const productMultipleUnitPriceDiscount: string[] = [];
    const productShortSku: string[] = [];

    order_items.forEach((item: FulfillmentOrderItem) => {
      const {
        quantity,
        product_style_id,
        product_type_name,
        brand: { abbr_name },
        product_name,
        vendor_id,
        web_vendor_name,
        customer_choice_number,
        primary_category_name,
        product_sku_id,
        price: { regular_price, discounted_price, sale_price },
      } = item || {};
      const productStyleIdItem = product_style_id ? product_style_id : '';
      productId.push(productStyleIdItem);
      const itemBrand = abbr_name === 'GP' ? 'GAP' : abbr_name;
      productBrand.push(itemBrand);
      if (primary_category_name) {
        productCategory.push(primary_category_name);
      }
      productName.push(product_name);
      const itemQuantity = parseInt(String(quantity), 10);
      productQuantity.push(itemQuantity);
      productSku.push(product_sku_id);
      if (product_type_name && product_type_name?.toLowerCase() === 'dropship') {
        productDropship.push('true');
        productSellerId.push(vendor_id);
        productSellerName.push(web_vendor_name);
      } else {
        productDropship.push('false');
        productSellerId.push(String(brandCode));
        productSellerName.push(itemBrand);
      }
      const priceItem = regular_price;
      const markdownPriceItem = discounted_price;
      const effectivePriceItem = sale_price;
      const markdown: number = priceItem - effectivePriceItem;
      productMarkDown.push(formatted(markdown * itemQuantity));
      productGrossMerchandise.push(formatted(effectivePriceItem * itemQuantity));
      productGrossRetail.push(formatted(priceItem * itemQuantity));
      productNetDemand.push(formatted(markdownPriceItem * itemQuantity));

      const customerCId = customer_choice_number ? customer_choice_number : '';
      productCustomerChoiceId.push(customerCId);
    });

    product_analytics.forEach((item: ProductAnalytics) => {
      const {
        multiple_unit_price_discount,
        product_page_type,
        product_line_and_brand_discount,
        product_pwp_level_discount,
        product_id,
      } = item || {};

      productMergeType.push(product_page_type || '');
      productLineAndBrandDiscount.push(formatted(product_line_and_brand_discount));
      productPWPLevelDiscount.push(formatted(product_pwp_level_discount));
      productMultipleUnitPriceDiscount.push(formatted(multiple_unit_price_discount));
      productShortSku.push(product_id);
    });

    fulfillment_details.forEach(detail => {
      const preferences = detail.shipping_details?.shipping_preferences || [];

      preferences.forEach(pref => {
        if (pref.shipping_charge !== undefined) {
          productShippingCost.push(String(pref.shipping_charge));
        }
        if (pref.shipping_description) {
          productShippingOptions.push(pref.shipping_description);
        }
      });
    });

    const brandMixArray: string[] = [];
    productBrand.forEach(brand => {
      if (!brandMixArray.includes(brand)) {
        brandMixArray.push(brand);
      }
    });
    const brandMix = brandMixArray.join(':');
    const orderGrossMerchandise = getSumOfStringArray(productGrossMerchandise) || 0;

    const data = {
      product_id: productId,
      product_cc_id: productCustomerChoiceId,
      product_brand: productBrand,
      brand_mix: brandMix,
      // product_category: productCategory, // not marked as required
      product_name: productName,
      product_quantity: productQuantity,
      product_sku: productSku,
      // product_markdown_amount: productMarkDown, // not marked as required
      product_gross_retail: productGrossRetail,
      product_gross_merchandise: productGrossMerchandise,
      product_net_demand: productNetDemand,
      order_gross_merchandise: orderGrossMerchandise,
      product_page_type: productMergeType,
      product_dropship: productDropship,
      product_seller_id: productSellerId,
      product_seller_name: productSellerName,
      product_short_sku: productShortSku,
      product_line_and_brand_discount: productLineAndBrandDiscount,
      product_pwp_level_discount: productPWPLevelDiscount,
      product_shipping_cost: productShippingCost,
      product_shipping_options: productShippingOptions,
      multiple_unit_price_discount: productMultipleUnitPriceDiscount,
    };
    return data;
  };

  const getOCPViewTagData = ({ personalizationData, compositeResponse }: OCPViewTagDataProps) => {
    const { order, fulfillment_details, payments, order_summary, order_discounts } = compositeResponse;
    const brandData = buildBrandData(brandAbbr, market);
    const personalizationViewTagData = personalizationData ? getPersonalizationViewTagData(personalizationData) : undefined;
    const isBOPISOrOmni = (fulfillment_details: FullfillmentDetails[]): boolean => {
      return fulfillment_details.some(fd =>
        fd.fulfillment_type === 'BOPIS'
      );
    };

    const viewTagData = {
      bopis_order_type: getFulfillmentTypeFlags(fulfillment_details),
      bopis_enabled: isBOPISOrOmni(fulfillment_details),
      checkout_brand: brandName?.toUpperCase(),
      event_name: 'order confirmation',
      page_type: 'OrderConfirmation',
      ...brandData,
      country_code: market.toUpperCase(),
      currency_code: order.currency,
      express_checkout: order.express_checkout,
      language_code: locale,
      id: orderNumber?.toString(),
      order_id: orderNumber?.toString(),
      order_level_discount_incl_line: String(order_discounts.order_level_discount_product_level),
      order_payment_type: getOrderPaymentType(payments),
      order_seller_type: getOrderSellerType(getAllOrderItems(fulfillment_details)),
      order_shipping: String(order_summary.shipping_price),
      order_tax: String(order_summary.estimated_tax),
      order_total: String(order_summary.total_price),
      shipping_method: `${order_summary.shipping_method} - $${order_summary.shipping_price}`,
      state_regulatory_fee: String(order_summary.retail_delivery_fee),
      customer_type: personalizationViewTagData?.checkout_type ?? '',
      ...getLineItemData(compositeResponse),
    };

    Object.assign(viewTagData, personalizationViewTagData);
    return viewTagData;
  };
  return {
    firePageViewTag,
    getOCPViewTagData,
    getEvergreenTags,
    getBooleanValue,
    getRecognizationStatus,
    getPersonalizationViewTagData,
    formatted,
    getSumOfStringArray,
    buildBrandData,
    getLineItemData,
  };
};
