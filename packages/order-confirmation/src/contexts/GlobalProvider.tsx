import { useContext, createContext, useState, useEffect } from 'react';
import { useClientFetch } from '@ecom-next/utils/useClientFetch';
import { clientFetch } from '@ecom-next/utils/clientFetch';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { ApiError } from '@ecom-next/checkout/contexts/types';
import { FeatureFlagsContext } from '@ecom-next/core/components/legacy/feature-flags';
import { makeCompositeAPIRequest } from '../utils/compositeAPI/makeCompositeAPIRequest';
import { ORDER_CONFIRMATION_XAPI_ENDPOINTS } from './constants';
import { GlobalContext, OrderConfirmationDetails, OrderConfirmationXapiEndpoint, GlobalProviderProps } from './types';

export const OCPContext = createContext<GlobalContext | null>(null);
OCPContext.displayName = 'OrderConfirmationContext';

export const useOCP = (): GlobalContext => {
  const value = useContext(OCPContext);

  if (value === null) {
    throw new Error('Component must be wrapped in OCP Provider');
  }
  return value;
};

const invalidateGuestSession = async () => {
  const apiEndpoint = ORDER_CONFIRMATION_XAPI_ENDPOINTS['invalidateGuestSession' as OrderConfirmationXapiEndpoint];
  const { path, method } = apiEndpoint;

  clientFetch<OrderConfirmationDetails>(`/checkout/place-order/xapi${path}`, {
    method,
  }) || {};
};

export const GlobalProvider = ({ children, orderNumber, isAccountCreationSuccess = false }: GlobalProviderProps) => {
  const apiEndpoint = ORDER_CONFIRMATION_XAPI_ENDPOINTS['orderConfirmationDetails' as OrderConfirmationXapiEndpoint];
  const { path, method } = apiEndpoint;
  const { locale, market, brand, ecomApiBaseUrl, abSeg } = usePageContext();
  const { enabledFeatures } = useContext(FeatureFlagsContext);
  const isOCPCompositeAPIEnabled = enabledFeatures['buy-ui-ocp-composite'] && abSeg?.['xb247'] === 'a';
  const [response, setResponse] = useState<{ data: OrderConfirmationDetails | null; error: ApiError | null }>({
    data: null,
    error: null,
  });

  const xAPIResponse =
    (!isOCPCompositeAPIEnabled &&
      // Need to dsiable the rule here. isOCPCompositeAPIEnabled will not change during the component's lifecycle
      // eslint-disable-next-line react-hooks/rules-of-hooks
      useClientFetch<OrderConfirmationDetails>(`/checkout/place-order/xapi${path}/${orderNumber}`, {
        method,
        headers: {
          locale,
        },
      })) || { data: null, error: null };

  useEffect(() => {
    const fetchData = async () => {
      if (isOCPCompositeAPIEnabled) {
        const updatedResponse = await makeCompositeAPIRequest(ecomApiBaseUrl, { brand, locale, market }, orderNumber);
        setResponse(prev => ({ ...prev, data: updatedResponse }));
      } else {
        setResponse({ data: xAPIResponse.data, error: xAPIResponse.error });
      }
    };
    fetchData();
  }, [xAPIResponse.data, xAPIResponse.error]);

  const { error, data } = response;

  const apiError = error !== null;
  const ocpResponse = data;
  const { panels: { orderSummaryPanel: { isGuest = false } = {} } = {} } = data || {};
  isGuest && invalidateGuestSession();
  return (
    <OCPContext.Provider
      value={{
        ocpResponse,
        apiError,
        orderNumber,
        isAccountCreationSuccess,
      }}
    >
      {children}
    </OCPContext.Provider>
  );
};
