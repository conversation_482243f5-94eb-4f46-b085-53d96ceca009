import { ReactNode } from 'react';
import { ViewTag } from '@ecom-next/checkout/contexts/types';
import { ItemBrand } from '@ecom-next/core/components/migration/product-image/types';
import { Locale } from '@ecom-next/utils/server';
import type { OCPCompositeApiResponse } from '../utils/compositeAPI/types';
import { ORDER_CONFIRMATION_XAPI_ENDPOINTS } from './constants';

export type OrderSummaryPanel = {
  customerEmail: string;
  customerFirstName: string;
  customerLastName: string;
  customerPhoneNumber: string;
  donationsTotal: number;
  estimatedTax: number;
  giftCardsTotal: number;
  hasDonations: boolean;
  hasGiftCards: boolean;
  isDropship: boolean;
  isGuest: boolean;
  markdownPromoSavings: number;
  marketCode: string;
  orderNumber: string;
  retailDeliveryFee: number;
  rewardsSavings: number;
  rewardsSubTotal: number;
  shippingItemCount: number;
  shippingMessage: string;
  shippingPrice: number;
  subTotal: number;
  totalPrice: number;
  totalSavings: number;
};

export interface Instruction {
  instructionText: string;
  instructionType: string;
}

export type PaymentPanel = {
  creditCardPayment: Array<CreditCard>;
  digitalWalletPayment: 'PAYPAL' | 'AFTERPAY' | 'APPLE_PAY' | 'KLARNA';
  giftCardPayment: Array<GiftCard>;
};

export type GiftCard = {
  amount: number;
  displayCode: string;
};

export type CreditCard = {
  cardBrand: string;
  creditCardType: string;
  displayCode: string;
};

interface ShippingGroupId {
  shippingGroupId: string;
}

export interface LineItems extends ShippingGroupId {
  backOrderDate: string;
  brand: ItemBrand;
  color: string;
  excludedFromPromotion: boolean;
  imagePath: string;
  isBackOrderItem: boolean;
  isDropship: boolean;
  itemId: string;
  madeToOrder: boolean;
  price: {
    discountedPrice: string;
    percentageOff?: number;
    regularPrice: number;
    salePrice: string;
    savings: number;
  };
  productName: string;
  productTypeName?: string;
  productUrl: string;
  quantity: number;
  size: string;
  storeId?: string;
  totalPrice: number;
  twoCharBrandCode: string;
  webVendorName?: string;
}

interface LineItemList {
  lineItemList: LineItems[];
}

export interface ShipmentPreference extends ShippingGroupId {
  deliveryDate: string;
  deliveryWeekDay: string;
  maxDaysToDeliver: number;
  minDaysToDeliver: number;
}

interface ShipmentPreferences {
  shipmentPreferences: ShipmentPreference[];
}

export interface ShipmentStandard extends ShippingGroupId {
  chargeAmount: number;
  chargeCategory?: string;
  chargeDescription: string;
  chargeName?: string;
}

interface ShipmentStandards {
  shipmentStandards: ShipmentStandard[];
}

export type ShippingAddress = {
  city: string;
  country: string;
  firstName: string;
  lastName: string;
  state: string;
  streetName: string;
  unitNo: string | null;
  zip: string;
};

export type ShippingAddressPanel = LineItemList &
  ShipmentPreferences &
  ShipmentStandards & {
    instruction: Instruction[];
    isUPS: boolean;
    shippingAddress: ShippingAddress;
    shippingMethod: string;
  };

export type UpsPanel = {
  shippingAddress: {
    city: string;
    country: string;
    firstName: string;
    lastName: string;
    state: string;
    streetName: string;
    unitNo: string | null;
    zip: string;
  };
  shippingMethod: string;
};

export type BopisItemsInfoList = {
  brandCode: string;
  itemCount: number;
  storeId: string;
};

export type BopisPanel = {
  bopisItemCount: number;
  bopisItemsInfoList: Array<BopisItemsInfoList>;
  pickupPerson: {
    firstName: string;
    lastName: string;
    mobileNumber?: string;
  };
};
export type AccountPanel = {
  getBackAmount: string;
};

export type PickupPanel = LineItemList & {
  firstName: string;
  lastName: string;
  lineItemList: LineItems[];
  mobileNumber?: string;
  storeInfo: Array<StoreInfo>;
};

interface Panels {
  accountPanel?: AccountPanel;
  bopisPanel?: BopisPanel;
  orderSummaryPanel: OrderSummaryPanel;
  payment?: PaymentPanel;
  pickupPanel?: PickupPanel;
  shippingAddressPanel: ShippingAddressPanel;
  upsPanel?: UpsPanel;
}

export type StoreInfo = {
  address: {
    addressLine1: string;
    cityName: string;
    postalCode: string;
    stateProvinceCode: string;
  };
  closeHours?: string;
  openHours?: string;
  phoneNumber: string;
  storeName: string;
  storeNumber: string;
};

export type Flags = { eligibleForNewAccount: boolean };

export interface OrderConfirmationDetails {
  compositeResponse?: OCPCompositeApiResponse;
  features?: object;
  flags: Flags;
  panels: Panels;
  viewTag?: ViewTag;
}

export interface GlobalContext {
  apiError: boolean;
  isAccountCreationSuccess?: boolean;
  ocpResponse: OrderConfirmationDetails | null;
  orderNumber?: Array<string> | string;
}

export interface OCPAppProps {
  orderNumber: Array<string> | string;
}

export interface GlobalProviderProps extends OCPAppProps {
  children: ReactNode;
  isAccountCreationSuccess?: boolean;
}

export type OrderConfirmationXapiEndpoint = keyof typeof ORDER_CONFIRMATION_XAPI_ENDPOINTS;

export type OCPSkeletonProps = {
  loadingComplete?: boolean;
};

export interface DeliveryGroups extends ShippingGroupId, LineItemList, ShipmentPreferences, ShipmentStandards {}

export interface StoreItemList extends LineItemList {}

export interface ProductsProps extends LineItemList {
  displayBrandLogos: boolean;
  locale: Locale;
  shipmentPreferences?: ShipmentPreference[];
  shipmentStandards?: ShipmentStandard[];
}
