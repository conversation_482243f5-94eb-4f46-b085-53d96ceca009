'use client';
import { useEffect, useContext, useState } from 'react';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { Button } from '@ecom-next/core/migration/button';
import { FeatureFlagsContext } from '@ecom-next/core/legacy/feature-flags';
import { useRouter } from 'next/navigation';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { firePageViewTag as firePageViewTagOld, getOCPViewTagData as getOCPViewTagDataOld } from '@ecom-next/checkout/tealium-utils';
import { PersonalizationContext } from '@ecom-next/sitewide/personalization-provider';
import { PageContextData } from '@ecom-next/checkout/contexts/types';
import { useDatalayer } from '../../hooks/useDatalayer';
import PickupIcon from '../../icons/PickupIcon';
import { useOCP } from '../../contexts/GlobalProvider';
import { deleteQueryParams } from '../../utils/OrderConfirmationUtil';
import type { OCPCompositeApiResponse } from '../../utils/compositeAPI/types';

export const Summary = () => {
  const { ocpResponse, apiError, orderNumber, isAccountCreationSuccess = false } = useOCP();
  const personalizationData = useContext(PersonalizationContext);
  const { brandAbbr, market, locale, abSeg } = usePageContext();
  const { search, pathname } = location;
  const searchParams = new URLSearchParams(search);
  const { push } = useRouter();
  const initialLoad = searchParams.get('initialLoad') === 'true';
  const { customerFirstName = '', customerEmail = '', isGuest = false } = ocpResponse?.panels?.orderSummaryPanel ?? {};
  const isBopisAvailable = ocpResponse?.panels?.bopisPanel?.bopisItemCount || false;

  const { localize } = useLocalize();
  const previousOrderNo = sessionStorage.getItem('previousOrderNo') || '';

  const onButtonClick = () => {
    window.location.href = `/my-account/order-details/${orderNumber}`;
  };

  const [isTealiumFiredOnPageLoad, setIsTealiumFiredOnPageLoad] = useState(false);
  const { enabledFeatures } = useContext(FeatureFlagsContext);
  const isTealiumMigrationEnabled = Boolean(enabledFeatures?.['buy-ui-ocp-composite']) && abSeg?.['xb247'] === 'a';
  const { firePageViewTag: firePageViewTagNew, getOCPViewTagData: getOCPViewTagDataNew } = useDatalayer();

  useEffect(() => {
    if (!initialLoad || !orderNumber || !ocpResponse?.viewTag || isTealiumFiredOnPageLoad || orderNumber?.toString() === previousOrderNo) return;
    const viewTag = ocpResponse?.viewTag || {};
    const compositeResponse = ocpResponse?.compositeResponse as OCPCompositeApiResponse;
    const pageContextData: PageContextData = {
      locale,
      market,
      brandAbbr,
    };
    sessionStorage.setItem('previousOrderNo', orderNumber.toString());
    const viewTagData = isTealiumMigrationEnabled
      ? getOCPViewTagDataNew({
          personalizationData,
          compositeResponse,
        })
      : getOCPViewTagDataOld({
          personalizationData,
          pageContextData,
          viewTag,
          orderNumber: orderNumber.toString(),
        });
    isTealiumMigrationEnabled ? firePageViewTagNew(viewTagData) : firePageViewTagOld('Order Confirmation', viewTagData);
    setIsTealiumFiredOnPageLoad(true);
    const validQueryParams = deleteQueryParams(['initialLoad=true'], search);
    const newPath = validQueryParams ? `${pathname}?${validQueryParams}` : `${pathname}`;
    push(newPath);
  }, [initialLoad, orderNumber, personalizationData.customerUUID, ocpResponse?.viewTag, previousOrderNo, isTealiumFiredOnPageLoad]);

  return (
    <section className='cb-base-compact' data-testid='ocp-summary-section'>
      <div>
        {!apiError && (
          <p className='cb-special-emphasis leading-135 truncate pb-2' data-testid='ocp-summary-greeting'>
            {localize('ocp.greet').toUpperCase()} {customerFirstName?.toUpperCase()}
          </p>
        )}
        <p className='cb-display-lg-emphasis pb-2'>{isAccountCreationSuccess ? localize('ocp.orderUpdate') : localize('ocp.thankyou')}</p>
        <p className='cb-base-default'>
          {localize('ocp.orderNumber')} <span className='cb-base-default-emphasis'>{orderNumber}</span>
        </p>
      </div>
      {!apiError && isBopisAvailable && !isAccountCreationSuccess && (
        <div className='bg-cb-alert-info-muted mt-6 flex rounded p-4'>
          <div>
            <PickupIcon />
          </div>
          <div className='pl-3'>
            <p className='cb-base-compact-emphasis pb-1'>{localize('ocp.pickupHeader')}</p>
            <p className='cb-base-note'>
              {localize('ocp.notifyWhenReady')} {localize('ocp.after2pm')}
            </p>
          </div>
        </div>
      )}
      <div className='cb-base-compact py-6'>
        <p>
          {!apiError ? (
            <>
              {localize('ocp.willEmailTo')}{' '}
              <span className='cb-base-compact-emphasis' data-testid='ocp-customer-email'>
                {customerEmail}
              </span>
              .
            </>
          ) : (
            <> {localize('ocp.willEmailToFallback')} </>
          )}{' '}
          {localize('ocp.emailArrivalcheckSpam')}
        </p>
        {!isGuest && <p className='pt-4'>{localize('ocp.emailNotifyMessageAuth')}</p>}
      </div>
      <Button kind='primary' className={`w-full px-[50px] text-base sm:w-auto`} onClick={onButtonClick}>
        {localize('ocp.orderDetailsButton')}
      </Button>
    </section>
  );
};
