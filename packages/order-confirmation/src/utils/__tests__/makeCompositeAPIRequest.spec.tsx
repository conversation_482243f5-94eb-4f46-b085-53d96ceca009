import { clientFetch } from '@ecom-next/utils/clientFetch';
import { getCompositeApiHeaders } from '@ecom-next/checkout/compositeAPI/headers';
import type { RequestHeaders } from '@ecom-next/checkout/compositeAPI/types';
import { makeCompositeAPIRequest } from '../compositeAPI/makeCompositeAPIRequest';
import type { OrderConfirmationDetails } from '../../contexts/types';

jest.mock('@ecom-next/utils/clientFetch');
jest.mock('@ecom-next/checkout/compositeAPI/headers');

const mockClientFetch = clientFetch as jest.Mock;
const mockGetCompositeApiHeaders = getCompositeApiHeaders as jest.Mock;

describe('makeOCPCompositeAPIRequest', () => {
  const requestHeaders: RequestHeaders = {
    brand: 'gap',
    locale: 'en_US',
    market: 'us',
  };

  const orderNumber = 'ORDER123';

  const mockCompositeResponse = {
    customer: {
      email: '<EMAIL>',
      first_name: '<PERSON>',
      is_guest: true,
      last_name: '<PERSON><PERSON>',
      phone: '1234567890',
    },
    fulfillment_details: [{
      fulfillment_type: 'BOPIS',
      pickup_details: {
        pickup_person: {
          first_name: 'Jane',
          last_name: 'Smith',
          phone: '0987654321',
        },
      },
      pickup_location: {
        store_id: '001',
        name: 'Store 001',
        address: {
          line1: '123 Main St',
          line2: '',
          city: 'San Francisco',
          state: 'CA',
          postal_code: '94105',
        },
        phone: '4155551234',
      },
      order_items: [
        {
          item_id: "1",
          shipping_group_id: "dummy-id",
          product_style_id: "740140",
          product_sku_id: "7401400220001",
          brand: {
            id: "1",
            abbr_name: "GP"
          },
          product_name: "Organic Cotton VintageSoft V-Neck T-Shirt",
          product_url: "browse/product.do?pid=7401400220001",
          pickup_store: "001",
          color: "FRESH WHITE",
          size: "S",
          quantity: 3,
          image_path: "webcontent/0056/041/410/cn56041410.jpg",
          back_order_date: "",
          web_vendor_name: "PRATIBHA SYNTEX LTD",
          is_back_order: false,
          price: {
            regular_price: 24.95,
            sale_price: 24.95,
            discounted_price: 7.52,
            total_price: 74.85,
            savings: 17.43,
            percentage_off: 69.86
          }
        }
      ],
    }],
    order: {
      brand: 'gap',
      currency: 'USD',
      locale: 'en_US',
      market: 'us',
      order_date: '2024-06-01T00:00:00Z',
      order_number: 'ORDER123',
    },
    order_discounts: {
      multiple_unit_price_discount: 10,
      order_level_discount_product_level: 5,
      plcc_discounts_product_level: 3,
      product_line_and_brand_discount: 4,
      pwp_discount_product_level: 2,
      single_unit_price_discount: 1,
    },
    order_summary: {
      rewards_sub_total: 0,
      subtotal: 100,
      savings: 10,
      savings_summary: {
        markdown_savings: 5,
        promotional_savings: 5,
        promo_count: 1,
        rewards_savings: 0,
        markdown_promo_savings: 0,
        discount_pricing_savings: 0,
        promo_savings: 0,
        total_savings: 10,
      },
      shipping_price: 0,
      subtotal_after_savings: 90,
      shipping: 0,
      estimated_tax: 8,
      state_regulatory_fee: 0,
      shipping_items_count: 0,
      subtotal_before_gift_cards: 90,
      gift_cards: 0,
      donations_total: 0,
      total: 98,
    },
    payments: {
      credit_card_payments: [
        {
          card_brand: 'VISA',
          card_type: 'Credit',
          card_type_id: '1',
          display_code: '****1234',
        },
      ],
      digital_wallet: {
        payment_type: 'APPLE_PAY',
      },
      gift_card_payments: [
        {
          amount: 10,
          brand: 'Gap',
          display_code: '****5678',
        },
      ],
    },
    product_analytics: {
      bopis: true,
      category: 'Shirts',
      color: 'Blue',
      fit: 'Slim',
      id: 'prod123',
      line_and_brand_discount: 5,
      markdown_amount: 5,
      metadata: {
        variant_group: true,
        variant_group_id: 'vg123',
        variant_key: 'size_color',
      },
      multiple_unit_price_discount: 5,
      name: 'Oxford Shirt',
      order_level_discount: 3,
      page_type: 'ProductDetail',
      plcc_level_discount: 2,
      price: 50,
      pwp_level_discount: 1,
      quantity: 2,
      size: 'M',
      style_id: 'style123',
    },
    type: 'ORDER_CONFIRMATION',
  };

  const formattedResponse = {
    compositeResponse: mockCompositeResponse,
    flags: {
      eligibleForNewAccount: false
    },
    panels: {
      shippingAddressPanel: {
        shippingMethod: "undefined-undefined",
        shippingAddress: {
          firstName: "John",
          lastName: "Doe"
        },
        lineItemList: [],
        shipmentPreferences: [],
        shipmentStandards: [],
        isUPS: false,
        instruction: []
      },
      payment: {
        creditCardPayment: [
          {
            displayCode: "****1234",
            creditCardType: "1",
            cardBrand: "VISA"
          }
        ],
        giftCardPayment: [
          {
            displayCode: "****5678",
            amount: 10
          }
        ],
        digitalWalletPayment: "APPLE_PAY"
      },
      bopisPanel: {
        bopisItemsInfoList: [
          {
            brandCode: "1",
            storeId: "001",
            itemCount: 3
          }
        ],
        pickupPerson: {
          firstName: "Jane",
          lastName: "Smith"
        },
        bopisItemCount: 0
      },
      pickupPanel: {
        firstName: "Jane",
        lastName: "Smith",
        mobileNumber: "0987654321",
        storeInfo: [],
        lineItemList: [
          {
            shippingGroupId: "dummy-id",
            brand: "gp",
            twoCharBrandCode: "1",
            productName: "Organic Cotton VintageSoft V-Neck T-Shirt",
            productUrl: "browse/product.do?pid=7401400220001",
            productTypeName: undefined,
            color: "FRESH WHITE",
            size: "S",
            imagePath: "webcontent/0056/041/410/cn56041410.jpg",
            isBackOrderItem: false,
            backOrderDate: "",
            isDropship: true,
            itemId: "1",
            storeId: "001",
            price: {
              regularPrice: 24.95,
              salePrice: "24.95",
              discountedPrice: "7.52",
              savings: 17.43,
              percentageOff: 69.86
            },
            totalPrice: 74.85,
            quantity: 3,
            webVendorName: "PRATIBHA SYNTEX LTD",
            excludedFromPromotion: false,
            madeToOrder: false
          }
        ]
      },
      orderSummaryPanel: {
        orderNumber: "ORDER123",
        marketCode: "us",
        customerEmail: "<EMAIL>",
        customerFirstName: "John",
        customerLastName: "Doe",
        customerPhoneNumber: "1234567890",
        isGuest: true,
        shippingItemCount: 0,
        isDropship: false,
        totalPrice: 0,
        totalSavings: 10,
        subTotal: 100,
        shippingPrice: 0,
        shippingMessage: "",
        rewardsSubTotal: 0,
        rewardsSavings: 0,
        estimatedTax: 8,
        giftCardsTotal: 0,
        hasGiftCards: false,
        donationsTotal: 0,
        hasDonations: false,
        retailDeliveryFee: 0,
        markdownPromoSavings: 0
      }
    },
  } as unknown as OrderConfirmationDetails;

  beforeEach(() => {
    jest.clearAllMocks();
    mockClientFetch.mockResolvedValue(mockCompositeResponse);
    mockGetCompositeApiHeaders.mockReturnValue({
      brand: 'GP',
      locale: 'en_US',
      market: 'US',
      channel: 'WEB',
    });
  });

  it('should call clientFetch with correct arguments and return updated response', async () => {
    const result = await makeCompositeAPIRequest('https://ecom-api.stage.gaptechol.com', requestHeaders, orderNumber);

    expect(clientFetch).toHaveBeenCalledWith(
      'https://ecom-api.stage.gaptechol.com/ui_composite_checkouts/v1/orders/ORDER123',
      expect.objectContaining({
        method: 'GET',
        credentials: 'include',
        headers: expect.objectContaining({
          brand: 'GP',
          channel: 'WEB',
          locale: 'en_US',
          market: 'US',
        }),
      })
    );

    expect(result).toEqual(formattedResponse);
  });

  it('should support orderNumber passed as an array', async () => {
    const result = await makeCompositeAPIRequest('https://ecom-api.stage.gaptechol.com', requestHeaders, ['ORDER456']);

    expect(clientFetch).toHaveBeenCalledWith('https://ecom-api.stage.gaptechol.com/ui_composite_checkouts/v1/orders/ORDER456', expect.any(Object));

    expect(result).toEqual(formattedResponse);
  });
});
