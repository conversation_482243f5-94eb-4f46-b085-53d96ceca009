import type { FulfillmentType, OrderSummary, StorePickUpInfo } from '@ecom-next/checkout/compositeAPI/types';
import type { BrandCode } from '@mfe/brand-info';
import { Brand } from '@ecom-next/utils/server';
import { Instruction } from '../../contexts/types';

export interface OCPCompositeApiResponse {
  customer: {
    email: string;
    first_name: string;
    is_guest: boolean;
    last_name: string;
    phone: string;
  };
  fulfillment_details: FullfillmentDetails[];
  order: {
    brand: Uppercase<Brand>;
    currency: 'USD' | 'CAD';
    express_checkout: boolean;
    locale: string;
    market: string;
    order_date: string;
    order_number: string;
  };
  order_discounts: {
    multiple_unit_price_discount: number;
    order_level_discount_product_level: number;
    plcc_discounts_product_level: number;
    product_line_and_brand_discount: number;
    pwp_discount_product_level: number;
    single_unit_price_discount: number;
  };
  order_summary: OrderSummary;
  payments: Payments;
  product_analytics: ProductAnalytics[];
  type: string;
}

export type CardInfo = [
  {
    card_brand: string;
    card_type: string;
    card_type_id: string;
    credit_card_tier?: string; // Optional, does not exist in composite API but kept for Tealium's sake
    display_code: string;
  },
]

export type Payments = {
  credit_card_payments?: CardInfo;
  digital_wallet?: {
    payment_type: 'PAYPAL' | 'APPLE_PAY' | 'AFTERPAY' | 'KLARNA';
  };
  gift_card_payments?: [
    {
      amount: number;
      brand: string;
      display_code: string;
    },
  ];
}

export type ProductAnalytics = {
  bopis: boolean;
  category: string;
  color: string;
  fit: string;
  id: string;
  line_and_brand_discount: number;
  markdown_amount: number;
  metadata: {
    variant_group: boolean;
    variant_group_id: string;
    variant_key: string;
  };
  multiple_unit_price_discount: number;
  name: string;
  order_level_discount: number;
  page_type: string;
  plcc_level_discount: number;
  price: number;
  product_id: string;
  product_line_and_brand_discount: number;
  product_page_type: string;
  product_pwp_level_discount: number;
  pwp_level_discount: number;
  quantity: number;
  size: string;
  style_id: string;
};

export interface FulfillmentOrderItem {
  back_order_date: string;
  brand: {
    abbr_name: string; // Composite services do not have an enum. They're passing this down from the draft-order.
    id: `${BrandCode}`;
  };
  color: string;
  customer_choice_number: string;
  excluded_from_promotion: boolean;
  image_path: string;
  item_id: string;
  made_to_order: boolean;
  pickup_store: string;
  price: {
    discounted_price: number;
    percentage_off: number;
    regular_price: number;
    sale_price: number;
    savings: number;
    total_price: number;
  };
  primary_category_name: string;
  product_name: string;
  product_sku_id: string;
  product_style_id: string;
  product_type_name?: string; // Only available for dropship items.
  product_url: string;
  quantity: number;
  shipping_group_id: string;
  size: string;
  store_id: string;
  vendor_id: string;
  web_vendor_name: string;
}

export interface ShippingGroupId {
  shipping_group_id: string;
}

export interface FulfillmentShippingPreferences {
  delivery_date: string;
  delivery_week_day: string;
  max_days_to_deliver: number;
  min_days_to_deliver: number;
  shipping_method: string;
}

export interface FulfillmentShippingDescription {
  shipping_charge: number;
  shipping_description: string;
  shipping_group_id: string;
}
export interface ShippingAddressComposite {
  address_line_1: string;
  address_line_2: string;
  city: string;
  country: string;
  firstName?: string;
  lastName?: string;
  postal_code: string;
  state: string;
  state_province_code: string;
  streetName?: string;
  unitNo?: string;
  zip?: string;
}
export interface ShippingDetailsComposite {
  delivery_date: string;
  shipping_address: ShippingAddressComposite;
  shipping_preferences: (FulfillmentShippingPreferences & FulfillmentShippingDescription)[];
}

export interface FullfillmentDetails {
  fulfillment_type: FulfillmentType;
  instruction: Instruction[];
  order_items: FulfillmentOrderItem[];
  pickup_details: {
    pickup_locations: StorePickUpInfo[];
    pickup_person: {
      first_name: string;
      last_name: string;
      phone: string;
    };
  };
  shipping_details: ShippingDetailsComposite;
  total_items: number;
}
