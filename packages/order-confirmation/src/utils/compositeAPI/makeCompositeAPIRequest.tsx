import { clientFetch } from '@ecom-next/utils/clientFetch';
import { getCompositeApiHeaders } from '@ecom-next/checkout/compositeAPI/headers';
import { RequestHeaders } from '@ecom-next/checkout/compositeAPI/types';
import { OrderConfirmationDetails } from '../../contexts/types';
import { buildShippingAddress } from '../../builders/buildShippingAddress';
import { buildOrderSummaryPanel } from '../../builders/buildOrderSummaryPanel';
import { buildPayments } from '../../builders/buildPayments';
import type { ShippingAddressPanel, PaymentPanel, BopisPanel, PickupPanel } from '../../contexts/types';
import { buildBopis } from '../../builders/buildBopis';
import { buildPickup } from '../../builders/buildPickup';
import { OCPCompositeApiResponse } from './types';

export const makeCompositeAPIRequest = async (
  ecomApiBaseUrl: string,
  requestHeaders: RequestHeaders,
  orderNumber: Array<string> | string
): Promise<OrderConfirmationDetails | null> => {
  try {
    const data = await clientFetch<OCPCompositeApiResponse>(`${ecomApiBaseUrl}/ui_composite_checkouts/v1/orders/${orderNumber}`, {
      method: 'GET',
      headers: getCompositeApiHeaders(requestHeaders),
      credentials: 'include',
    });

    return {
      compositeResponse: { ...data },
      flags: { eligibleForNewAccount: false }, // TODO: Implement flags (PB-733)
      panels: {
        // accountPanel: { getBackAmount: '' }, // TODO: Implement account panel (PB-733)
        shippingAddressPanel: buildShippingAddress({ data }) as ShippingAddressPanel,
        payment: buildPayments({ data }) as PaymentPanel,
        bopisPanel: buildBopis({ data }) as BopisPanel,
        pickupPanel: buildPickup({ data }) as PickupPanel,
        orderSummaryPanel: buildOrderSummaryPanel({ data }),
        // upsPanel: { }, // TODO: Implement UPS panel (PB-733)
      },
    };
  } catch (error) {
    //TODO: Set up proper error handling
    // eslint-disable-next-line no-console
    console.error('Error fetching composite API:', error);
    throw error;
  }
};
