import { brandLookupTable } from '@ecom-next/checkout/utils/brandLookUpUtil';
import { CardInfo, FulfillmentOrderItem, FullfillmentDetails, Payments } from "./compositeAPI/types";

export const getBooleanValue = (data: string | boolean) => data === true || data === 'true';

export const getRecognizationStatus = (isLoggedInUser: boolean, isRecognizedUser: boolean, isGuestUser: boolean) => {
  const recognition_status = isLoggedInUser ? 'authenticated' : isRecognizedUser ? 'recognized' : isGuestUser ? 'guest' : 'unrecognized';
  return recognition_status;
};

export const formatted = (value: number): string => {
  if (value === undefined) {
    return '';
  }
  return value.toFixed(2);
};

const cardIdNumber = {
  VISAID: 1,
  MASTERCARDID: 2,
  AMEXID: 3,
  DISCOVERID: 4,
  BRPLCCID: 5,
  JCBID: 6,
  GAPLCCID: 7,
  ONPLCCID: 8,
  DINERSID: 9,
  MAESTROID: 10,
  ATPLCCID: 11,
  PAYPALID: 1001,
  AFTERPAYID: 1002,
  BR_BCS_PLCC_ID: 30,
  ON_BCS_PLCC_ID: 31,
  GAP_BCS_PLCC_ID: 32,
  AT_BCS_PLCC_ID: 33,
  BR_BCS_CBCC_ID: 34,
  ON_BCS_CBCC_ID: 35,
  GAP_BCS_CBCC_ID: 36,
  AT_BCS_CBCC_ID: 37
};

const getPaymentType = (cardInfo: CardInfo) => {
  const {
    BR_BCS_CBCC_ID, BR_BCS_PLCC_ID, GAP_BCS_CBCC_ID, GAP_BCS_PLCC_ID, ON_BCS_CBCC_ID,
    ON_BCS_PLCC_ID, AT_BCS_PLCC_ID, AT_BCS_CBCC_ID, VISAID, MASTERCARDID, AMEXID,
    DISCOVERID, JCBID, DINERSID, MAESTROID, ATPLCCID, BRPLCCID, GAPLCCID, ONPLCCID, PAYPALID, AFTERPAYID
  } = cardIdNumber;
  const getCCTypeName = (ccTypeId: number) => {
    switch (ccTypeId) {
      case VISAID:
        return 'Visa';
      case MASTERCARDID:
        return 'MasterCard';
      case AMEXID:
        return 'American Express';
      case DISCOVERID:
        return 'Discover';
      case BRPLCCID:
      case BR_BCS_CBCC_ID:
      case BR_BCS_PLCC_ID:
        return 'Banana Republic';
      case JCBID:
        return 'JCB';
      case GAPLCCID:
      case GAP_BCS_CBCC_ID:
      case GAP_BCS_PLCC_ID:
        return 'GapCard';
      case ONPLCCID:
      case ON_BCS_CBCC_ID:
      case ON_BCS_PLCC_ID:
        return 'Old Navy';
      case DINERSID:
        return 'Diners';
      case MAESTROID:
        return 'Maestro';
      case ATPLCCID:
      case AT_BCS_CBCC_ID:
      case AT_BCS_PLCC_ID:
        return 'Athleta';
      default:
        return 'Unspecified';
    }
  };

  if (!cardInfo) {
    return 'Unspecified';
  }
  if (cardInfo[0].card_type_id === PAYPALID.toString()) {
    return 'Paypal';
  }
  if (cardInfo[0].card_type_id === AFTERPAYID.toString()) {
    return 'Afterpay';
  }
  // According to composite services, credit_card_tier is deprecated and not available in the response.
  // However, we are keeping this logic if we want to revisit it later.
  let PLCC = false;
  const cardTypeData = [];
  const cardType = parseInt(cardInfo[0].card_type_id, 10);
  const cardBrandNumber = parseInt(cardInfo[0].card_brand, 10);
  const creditCardTier = parseInt(cardInfo[0].credit_card_tier || '', 10);

  PLCC = [BRPLCCID, GAPLCCID, ONPLCCID, ATPLCCID].includes(cardType);

  //get the name, add it to cardData array
  cardTypeData.push(getCCTypeName(cardType));

  // Report card brand if its a visa/masterCard
  if ([VISAID, MASTERCARDID].includes(cardType) && cardBrandNumber) {
    if (brandLookupTable[cardBrandNumber]().BRD_NM) {
      cardTypeData.push(brandLookupTable[cardBrandNumber]().BRD_NM);
    }
  }

  //report brand if its branded Visa/masterCard or PLCC
  if (([VISAID, MASTERCARDID].includes(cardType) && cardBrandNumber) || PLCC) {
    if (creditCardTier === 0) {
      cardTypeData.push('Base');
    } else if ([1, 2, 3].includes(creditCardTier)) {
      cardTypeData.push('Tier');
    }
  }
  const cardTypeName = cardTypeData.join(':');

  return cardTypeName;
};

export const getOrderPaymentType = (payments: Payments) => {
  let order_payment_type;
  const isGiftCardPayment = payments.gift_card_payments && payments.gift_card_payments.length > 0;
  const isCreditCardPayment = payments.credit_card_payments && payments.credit_card_payments.length > 0;
  const isApplePay = payments.digital_wallet && payments.digital_wallet.payment_type === 'APPLE_PAY';
  const isPaypal =
    payments.digital_wallet && payments.digital_wallet.payment_type === 'PAYPAL';
  const isAfterPay =
    payments.digital_wallet && payments.digital_wallet.payment_type === 'AFTERPAY';
  const isKlarna =
    payments.digital_wallet && payments.digital_wallet.payment_type === 'KLARNA';
  const cardInfo = isCreditCardPayment && payments.credit_card_payments?.[0]
    ? [payments.credit_card_payments[0]]
    : undefined;
  const paymentType = getPaymentType(cardInfo as CardInfo);
  if (isGiftCardPayment) {
    order_payment_type = isCreditCardPayment ? `${paymentType}:GiftCard` : 'GiftCard';
  } else if (isPaypal) {
    order_payment_type = 'PAYPAL';
  } else if (isAfterPay) {
    order_payment_type = 'AFTERPAY';
  } else if (isKlarna) {
    order_payment_type = 'KLARNA';
  } else if (isApplePay) {
    order_payment_type = `${paymentType}:ApplePay`;
  } else {
    order_payment_type = paymentType;
  }

  return order_payment_type;
};

export const getAllOrderItems = (fulfillmentArray: FullfillmentDetails[]): FulfillmentOrderItem[] => {
  if (!Array.isArray(fulfillmentArray)) return [];
  return fulfillmentArray.flatMap(f => (Array.isArray(f.order_items) ? f.order_items : []));
};

export const getOrderSellerType = (items: FulfillmentOrderItem[]): 'dropship' | 'brand' | 'omni' => {
  if (!items || items.length === 0) return 'brand';
  const dropshipCount = items.filter(item => item?.product_type_name?.toLowerCase() === 'dropship').length;
  if (dropshipCount === items.length) return 'dropship';
  if (dropshipCount === 0) return 'brand';
  return 'omni';
};

export const getFulfillmentTypeFlags = (fulfillment_details: FullfillmentDetails[]): string => {
  const types = new Set(fulfillment_details.map(fd => fd.fulfillment_type));

  // Alphabetically sorted key for consistent matching
  const key = Array.from(types).sort().join('+');

  const fulfillmentTypeMap: Record<string, string> = {
    'BOPIS': 'BOPISonly',
    'UPS': 'UPSOnly',
    'SHIP': 'Shipitemsonly',
    'BOPIS+UPS': 'Omni – UPS and BOPIS',
    'BOPIS+SHIP': 'Omni – BOPIS and shipItems'
  };

  return fulfillmentTypeMap[key];
};

export const getSumOfStringArray = (arr: string[]): number => {
  if (arr === undefined) {
    return 0;
  }
  const numArray = arr.reduce((acc, val) => acc + parseFloat(val), 0);
  return Number.parseFloat(numArray.toFixed(2));
};
