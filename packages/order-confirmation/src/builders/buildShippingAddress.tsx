import type {
  OCPCompositeApiResponse,
  FulfillmentOrderItem,
  FulfillmentShippingPreferences,
  FullfillmentDetails,
  FulfillmentShippingDescription,
  ShippingGroupId,
  ShippingDetailsComposite,
  ShippingAddressComposite,
} from '../utils/compositeAPI/types';
import type { LineItems, ShipmentPreference, ShipmentStandard, ShippingAddress, Instruction } from '../contexts/types';
import { buildLineItems } from './buildLineItems';

type BuildShippingAddressReturn = {
  instruction: Instruction[];
  isUPS: boolean;
  lineItemList: LineItems[];
  shipmentPreferences: ShipmentPreference[];
  shipmentStandards: ShipmentStandard[];
  shippingAddress: ShippingAddress;
  shippingMethod: string;
};

export const buildShippingAddress = ({ data }: { data: OCPCompositeApiResponse }): BuildShippingAddressReturn => {
  const { fulfillment_details, customer } = data;

  const shippingDetails =
    (fulfillment_details?.find(({ fulfillment_type }) => ['SHIP', 'UPS'].includes(fulfillment_type?.toUpperCase() || '')) as FullfillmentDetails) || {};

  const { shipping_details = {} as ShippingDetailsComposite, order_items = [] as FulfillmentOrderItem[] } = shippingDetails;
  const {
    shipping_address = {} as ShippingAddressComposite,
    shipping_preferences = [] as (FulfillmentShippingPreferences & FulfillmentShippingDescription)[],
  } = shipping_details;

  const lineItemList: LineItems[] = buildLineItems(order_items);

  const shipmentPreferences: ShipmentPreference[] = shipping_preferences.map((item: FulfillmentShippingPreferences) => ({
    shippingGroupId: (item as FulfillmentShippingPreferences & ShippingGroupId).shipping_group_id,
    maxDaysToDeliver: item.max_days_to_deliver,
    minDaysToDeliver: item.min_days_to_deliver,
    deliveryDate: item.delivery_date,
    deliveryWeekDay: item.delivery_week_day,
  }));

  const shipmentStandards: ShipmentStandard[] = shipping_preferences.map((item: FulfillmentShippingDescription) => ({
    chargeAmount: item.shipping_charge,
    chargeDescription: item.shipping_description,
    shippingGroupId: item.shipping_group_id,
  }));

  return {
    shippingMethod: `${shipping_preferences?.[0]?.min_days_to_deliver}-${shipping_preferences?.[0]?.max_days_to_deliver}`,
    shippingAddress: {
      firstName: customer.first_name,
      lastName: customer.last_name,
      streetName: shipping_address.address_line_1,
      unitNo: shipping_address.address_line_2,
      city: shipping_address.city,
      state: shipping_address.state_province_code,
      country: shipping_address.country,
      zip: shipping_address.postal_code,
    },
    lineItemList,
    shipmentPreferences,
    shipmentStandards,
    isUPS: shippingDetails?.fulfillment_type === 'UPS',
    instruction: shippingDetails?.instruction || [],
  };
};
