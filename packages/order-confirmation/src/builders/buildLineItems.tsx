import { ItemBrand } from '@ecom-next/core/components/migration/product-image/types';
import type { FulfillmentOrderItem } from '../utils/compositeAPI/types';
import type { LineItems } from '../contexts/types';

export const buildLineItems = (order_items: FulfillmentOrderItem[]): LineItems[] =>
  order_items?.map((item: FulfillmentOrderItem) => ({
    shippingGroupId: item.shipping_group_id,
    brand: item.brand.abbr_name?.toLowerCase() as ItemBrand,
    twoCharBrandCode: item.brand.id,
    productName: item.product_name,
    productUrl: item.product_url,
    productTypeName: item.product_type_name,
    color: item.color,
    size: item.size,
    imagePath: item.image_path,
    isBackOrderItem: item.back_order_date !== '',
    backOrderDate: item.back_order_date, // TODO: Format this date if needed
    isDropship: item.web_vendor_name !== '',
    itemId: item.item_id,
    storeId: item.pickup_store,
    price: {
      regularPrice: item.price.regular_price,
      salePrice: String(item.price.sale_price),
      discountedPrice: String(item.price.discounted_price),
      savings: item.price.savings,
      percentageOff: item.price.percentage_off,
    },
    totalPrice: item.price.total_price,
    quantity: item.quantity,
    webVendorName: item.web_vendor_name || '',
    excludedFromPromotion: item.excluded_from_promotion || false,
    madeToOrder: item.made_to_order || false,
  }));
