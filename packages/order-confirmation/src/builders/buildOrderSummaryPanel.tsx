import type { OCPCompositeApiResponse } from '../utils/compositeAPI/types';
import type { OrderSummaryPanel } from '../contexts/types';

export const buildOrderSummaryPanel = ({ data }: { data: OCPCompositeApiResponse }): OrderSummaryPanel => {
  const { order_summary, order, customer, fulfillment_details } = data;
  const hasGiftCards = order_summary.gift_cards_total > 0;
  const hasDonations = order_summary.donations_total > 0;
  const isDropship = fulfillment_details.some(detail => detail.order_items.some(item => item.product_type_name?.toLowerCase() === 'dropship'));

  return {
    orderNumber: order.order_number,
    marketCode: order.market,
    customerEmail: customer.email,
    customerFirstName: customer.first_name,
    customerLastName: customer.last_name,
    customerPhoneNumber: customer.phone,
    isGuest: customer.is_guest,
    shippingItemCount: Number(order_summary.shipping_items_count),
    isDropship: isDropship,
    totalPrice: order_summary.total_price || 0,
    totalSavings: order_summary.savings_summary.total_savings,
    subTotal: order_summary.subtotal,
    shippingPrice: order_summary.shipping_price,
    shippingMessage: order_summary.shipping_method ? `(${order_summary.shipping_method})` : '',
    rewardsSubTotal: order_summary.rewards_sub_total,
    rewardsSavings: order_summary.savings_summary.rewards_savings,
    estimatedTax: order_summary.estimated_tax,
    giftCardsTotal: order_summary.gift_cards_total || 0,
    hasGiftCards: hasGiftCards,
    donationsTotal: order_summary.donations_total,
    hasDonations: hasDonations,
    retailDeliveryFee: order_summary.retail_delivery_fee || 0,
    markdownPromoSavings: order_summary.savings_summary.markdown_promo_savings,
  };
};
