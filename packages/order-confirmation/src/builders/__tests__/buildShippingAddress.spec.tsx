import { buildShippingAddress } from '../buildShippingAddress';
import type { OCPCompositeApiResponse, ShippingAddressComposite } from '../../utils/compositeAPI/types';

describe('buildShippingAddress', () => {
  const baseData: OCPCompositeApiResponse = {
    fulfillment_details: [
      {
        fulfillment_type: 'SHIP',
        instruction: [
          {
            instructionType: 'GIFT',
            instructionText: 'This is a gift',
          },
        ],
        shipping_details: {
          shipping_address: {
            address_line_1: '123 Main St',
            address_line_2: 'Apt 4',
            city: 'New York',
            state: 'New York',
            state_province_code: 'NY',
            country: 'USA',
            postal_code: '10001',
          },
          shipping_preferences: [
            {
              shipping_group_id: 'sg1',
              max_days_to_deliver: 5,
              min_days_to_deliver: 2,
              delivery_date: '2024-06-01',
              delivery_week_day: 'Monday',
              shipping_charge: 10,
              shipping_description: 'Standard Shipping',
              shipping_method: 'Ground',
            },
          ],
          delivery_date: '',
        },
        order_items: [
          {
            shipping_group_id: 'sg1',
            brand: { abbr_name: 'br', id: 'BR01' },
            product_name: 'Product 1',
            product_url: '/product-1',
            color: 'Red',
            size: 'M',
            image_path: '/img1.jpg',
            back_order_date: '',
            web_vendor_name: '',
            item_id: 'item1',
            price: {
              regular_price: 100,
              discounted_price: 70,
              total_price: 70,
              sale_price: '',
            },
            quantity: 1,
            excluded_from_promotion: false,
            made_to_order: false,
            product_sku_id: '',
            product_style_id: '',
            product_type_name: '',
          },
        ],
        pickup_details: {
          pickup_person: {
            first_name: '',
            last_name: '',
            phone: '',
          },
          pickup_location: {
            store_name: '',
            phone: '',
            store_address: {
              address_line_1: '',
              address_line_2: '',
              city: '',
              country: '',
              phone: '',
              postal_code: '',
              state: '',
            },
            store_number: '123',
            store_timezone: '',
          },
        },
        total_items: 0,
      },
    ],
    customer: {
      first_name: 'John',
      last_name: 'Doe',
      email: '',
      is_guest: false,
      phone: '',
    },
    order: {
      brand: '',
      currency: '',
      locale: '',
      market: '',
      order_date: '',
      order_number: '',
    },
    order_discounts: {
      multiple_unit_price_discount: 0,
      order_level_discount_product_level: 0,
      plcc_discounts_product_level: 0,
      product_line_and_brand_discount: 0,
      pwp_discount_product_level: 0,
      single_unit_price_discount: 0,
    },
    order_summary: {
      subtotal: 0,
      donations_total: 0,
      estimated_tax: 0,
      gift_cards_total: 0,
      shipping_price: 0,
      total_price: 0,
      savings_summary: {
        markdown_promo_savings: 0,
        rewards_savings: 0,
        total_savings: 0,
      },
      shipping_message: '',
      retail_delivery_fee: 0,
      subtotal_after_savings: 0,
    },
    payments: {
      credit_card_payments: [
        {
          card_brand: '',
          card_type: '',
          card_type_id: '',
          display_code: '',
        },
      ],
      digital_wallet: {
        payment_type: '',
      },
      gift_card_payments: [{ amount: 0, brand: '', display_code: '' }],
    },
    product_analytics: [{
      bopis: false,
      category: '',
      color: '',
      fit: '',
      id: '',
      line_and_brand_discount: 0,
      markdown_amount: 0,
      metadata: {
        variant_group: false,
        variant_group_id: '',
        variant_key: '',
      },
      multiple_unit_price_discount: 0,
      name: '',
      order_level_discount: 0,
      page_type: '',
      plcc_level_discount: 0,
      price: 0,
      pwp_level_discount: 0,
      quantity: 0,
      size: '',
      style_id: '',
    }],
    type: '',
  };

  it('should build shipping address with correct values', () => {
    const result = buildShippingAddress({ data: baseData });
    expect(result.shippingAddress).toEqual({
      firstName: 'John',
      lastName: 'Doe',
      streetName: '123 Main St',
      unitNo: 'Apt 4',
      city: 'New York',
      state: 'NY',
      country: 'USA',
      zip: '10001',
    });
    expect(result.lineItemList.length).toBe(1);
    expect(result.shipmentPreferences.length).toBe(1);
    expect(result.shipmentStandards.length).toBe(1);
    expect(result.isUPS).toBe(false);
    expect(result.shippingMethod).toBe('2-5');
    expect(Array.isArray(result.instruction)).toBe(true);
  });

  it('should handle UPS fulfillment type', () => {
    const data: OCPCompositeApiResponse = {
      ...baseData,
      fulfillment_details: [
        {
          ...baseData.fulfillment_details[0],
          fulfillment_type: 'UPS',
        },
      ],
    };
    const result = buildShippingAddress({ data });
    expect(result.isUPS).toBe(true);
  });

  it('should handle missing shipping_preferences gracefully', () => {
    const data: OCPCompositeApiResponse = {
      ...baseData,
      fulfillment_details: [
        {
          ...baseData.fulfillment_details[0],
          shipping_details: {
            ...baseData.fulfillment_details[0].shipping_details,
            shipping_preferences: [],
          },
        },
      ],
    };
    const result = buildShippingAddress({ data });
    expect(result.shipmentPreferences).toEqual([]);
    expect(result.shipmentStandards).toEqual([]);
    expect(result.shippingMethod).toBe('undefined-undefined');
  });

  it('should handle missing order_items gracefully', () => {
    const data: OCPCompositeApiResponse = {
      ...baseData,
      fulfillment_details: [
        {
          ...baseData.fulfillment_details[0],
          order_items: [],
        },
      ],
    };
    const result = buildShippingAddress({ data });
    expect(result.lineItemList).toEqual([]);
  });

  it('should handle missing shipping_address fields', () => {
    const data: OCPCompositeApiResponse = {
      ...baseData,
      fulfillment_details: [
        {
          ...baseData.fulfillment_details[0],
          shipping_details: {
            ...baseData.fulfillment_details[0].shipping_details,
            shipping_address: {} as ShippingAddressComposite,
          },
        },
      ],
    };
    const result = buildShippingAddress({ data });
    expect(result.shippingAddress).toEqual({
      firstName: 'John',
      lastName: 'Doe',
      streetName: undefined,
      unitNo: undefined,
      city: undefined,
      state: undefined,
      country: undefined,
      zip: undefined,
    });
  });

  it('should return default values if fulfillment_details is missing', () => {
    const data: OCPCompositeApiResponse = {
      ...baseData,
      fulfillment_details: [],
    };
    const result = buildShippingAddress({ data });
    expect(result.lineItemList).toEqual([]);
    expect(result.shipmentPreferences).toEqual([]);
    expect(result.shipmentStandards).toEqual([]);
    expect(result.isUPS).toBe(false);
    expect(result.shippingMethod).toBe('undefined-undefined');
  });
});
