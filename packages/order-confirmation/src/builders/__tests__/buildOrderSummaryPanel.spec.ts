import { buildOrderSummaryPanel } from '../buildOrderSummaryPanel';

describe('buildOrderSummaryPanel', () => {
  const baseData = {
    order_summary: {
      gift_cards_total: 0,
      donations_total: 0,
      total_price: 100,
      savings_summary: {
        total_savings: 10,
        rewards_savings: 5,
        markdown_promo_savings: 2,
      },
      subtotal: 80,
      shipping_price: 5,
      shipping_method: '3-5 business days',
      rewards_sub_total: 3,
      estimated_tax: 7,
      shipping_items_count: '2',
      retail_delivery_fee: 1,
    },
    order: {
      order_number: '12345',
      market: 'US',
    },
    customer: {
      email: '<EMAIL>',
      first_name: '<PERSON>',
      last_name: '<PERSON><PERSON>',
      phone: '1234567890',
      is_guest: false,
    },
    fulfillment_details: [
      {
        order_items: [{ product_type_name: 'Regular' }],
      },
    ],
  };

  it('should build the order summary panel with default values', () => {
    const result = buildOrderSummaryPanel({ data: baseData });
    expect(result).toEqual({
      orderNumber: '12345',
      marketCode: 'US',
      customerEmail: '<EMAIL>',
      customerFirstName: 'John',
      customerLastName: 'Doe',
      customerPhoneNumber: '1234567890',
      isGuest: false,
      shippingItemCount: 2,
      isDropship: false,
      totalPrice: 100,
      totalSavings: 10,
      subTotal: 80,
      shippingPrice: 5,
      shippingMessage: '(3-5 business days)',
      rewardsSubTotal: 3,
      rewardsSavings: 5,
      estimatedTax: 7,
      giftCardsTotal: 0,
      hasGiftCards: false,
      donationsTotal: 0,
      hasDonations: false,
      retailDeliveryFee: 1,
      markdownPromoSavings: 2,
    });
  });

  it('should set hasGiftCards to true if gift_cards_total > 0', () => {
    const data = {
      ...baseData,
      order_summary: { ...baseData.order_summary, gift_cards_total: 20 },
    };
    const result = buildOrderSummaryPanel({ data });
    expect(result.hasGiftCards).toBe(true);
    expect(result.giftCardsTotal).toBe(20);
  });

  it('should set hasDonations to true if donations_total > 0', () => {
    const data = {
      ...baseData,
      order_summary: { ...baseData.order_summary, donations_total: 15 },
    };
    const result = buildOrderSummaryPanel({ data });
    expect(result.hasDonations).toBe(true);
    expect(result.donationsTotal).toBe(15);
  });

  it('should set isDropship to true if any order item is Dropship', () => {
    const data = {
      ...baseData,
      fulfillment_details: [
        {
          order_items: [{ product_type_name: 'Dropship' }, { product_type_name: 'Regular' }],
        },
      ],
    };
    const result = buildOrderSummaryPanel({ data });
    expect(result.isDropship).toBe(true);
  });

  it('should handle shipping_items_count as string', () => {
    const data = {
      ...baseData,
      order_summary: { ...baseData.order_summary, shipping_items_count: '3' },
    };
    const result = buildOrderSummaryPanel({ data });
    expect(result.shippingItemCount).toBe(3);
  });

  it('should handle missing optional fields gracefully', () => {
    const data = {
      ...baseData,
      order_summary: {
        ...baseData.order_summary,
        shipping_method: undefined,
        rewards_sub_total: undefined,
        estimated_tax: undefined,
        retail_delivery_fee: undefined,
      },
    };
    const result = buildOrderSummaryPanel({ data });
    expect(result.shippingMessage).toBe('');
    expect(result.rewardsSubTotal).toBeUndefined();
    expect(result.estimatedTax).toBeUndefined();
    expect(result.retailDeliveryFee).toBe(0);
  });
});
