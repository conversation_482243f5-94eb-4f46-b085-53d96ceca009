import { fireEvent, waitFor, screen, within } from '@testing-library/react';
import { mockAllIsIntersecting } from 'react-intersection-observer/test-utils';
import { MediaZoomOverlay } from '../MediaZoomOverlay';
import { wrapInTestApp } from '../../../../test-utils/appWrapper';
import { mediaSequencerResponseMock } from '../../../../pages/helpers/__tests__/mediaSequencer/mediaSequencerDataMock';
import { AdaptedImage, AdaptedVideo } from '../../../../pages/helpers/mediaSequencer';

window.HTMLElement.prototype.scrollIntoView = function () {};

const media = mediaSequencerResponseMock as (AdaptedImage | AdaptedVideo)[];

describe('MediaZoomOverlay', () => {
  let container: HTMLElement;
  beforeEach(async () => {
    jest.spyOn(console, 'error').mockImplementation();
    jest.spyOn(console, 'warn').mockImplementation();
    mockAllIsIntersecting(true);
    await waitFor(() => {
      ({ container } = wrapInTestApp(<MediaZoomOverlay media={media} isOpen={true} closeModal={() => false} />));
      const video = screen.getByTestId('main-video-3');
      fireEvent.canPlayThrough(video);
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('snapshot', async () => {
    expect(container).toMatchSnapshot();
  });

  test('changes main image when thumbnail is clicked', async () => {
    const imageThumbnailButton = await screen.findByTestId('pdp-zoom-modal-image-thumbnail-0');
    fireEvent.click(imageThumbnailButton);

    const [, updatedImage] = screen.queryAllByTestId('pdp-zoom-modal-main-image-0');

    waitFor(() => {
      expect(updatedImage).toBeInTheDocument();
    });
  });

  test('switches to video when video thumbnail is clicked', async () => {
    const videoThumbnailButton = await screen.findByTestId('pdp-zoom-modal-video-thumbnail-3');
    fireEvent.click(videoThumbnailButton);
    const videoElement = await screen.findByTestId('main-video-3');
    const videoSrc = await screen.findByTestId('video-source');
    expect(videoElement).toBeInTheDocument();
    expect(videoSrc).toHaveAttribute('src', '/webcontent/0056/258/877/cn56258877.mp4');
  });

  test('render placeholder until image is fully loaded', async () => {
    const imageThumbnailButton = await screen.findByTestId('pdp-zoom-modal-image-thumbnail-0');
    fireEvent.click(imageThumbnailButton);
    const imageLoaded = await screen.getAllByTestId('pdp-zoom-modal-main-image-0')[0];
    expect(imageLoaded).toBeInTheDocument();

    const placeholder = await screen.getAllByTestId('pdp-zoom-modal-image-skeleton-0')[0];
    expect(placeholder).toBeInTheDocument();

    const mainImage = await screen.getAllByTestId('pdp-zoom-modal-main-image-0')[0];
    expect(mainImage).toBeInTheDocument();

    waitFor(() => {
      fireEvent.load(imageLoaded);
      expect(placeholder).toBeVisible();
    });
  });

  test('render image zoomed when you click on the image', async () => {
    const imageThumbnailButton = await screen.findByTestId('pdp-zoom-modal-image-thumbnail-1');
    fireEvent.click(imageThumbnailButton);
    const imageLoaded = await screen.getByTestId('pdp-zoom-modal-main-wrapper-1');
    fireEvent.click(imageLoaded);
    fireEvent.keyDown(imageLoaded);
    expect(imageLoaded).toBeInTheDocument();
  });
  test('execute onload when handleImageLoad is called', async () => {
    const imageThumbnailButton = await screen.findByTestId('pdp-zoom-modal-image-thumbnail-2');
    fireEvent.click(imageThumbnailButton);

    const allModals = screen.getAllByTestId('pdp-zoom-modal-main-image-2');

    const targetModal = allModals.find(element => element.classList.contains('pdp_zoom-modal__main-image'));
    const image = within(targetModal).getByRole('img', { name: 'Zoom in 3' });

    fireEvent.load(image);
    expect(targetModal).toBeInTheDocument();
  });
});
