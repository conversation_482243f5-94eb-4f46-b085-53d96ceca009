import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { AdaptedImage, AdaptedVideo } from '../../pages/helpers/mediaSequencer';
import { ImageCropTypeKeys } from '../../pages/helpers/mediaSequencer/types';
import { Image } from './Image';
import { getProductImageAltText, getPhotoBricksSizes, getPhotoBricksSrcSet } from './helpers/util';

export const OutOfStockImageLayout = ({
  media,
  productTitle,
  imageSize,
  isDesktop,
  isSpecialOOSEnabled,
}: {
  imageSize: ImageCropTypeKeys;
  isDesktop: boolean;
  isSpecialOOSEnabled: boolean;
  media: (AdaptedImage | AdaptedVideo)[];
  productTitle: string;
}) => {
  const { localize } = useLocalize();
  const soldOutLabel = localize('pdp.OOS.addToBag.label');
  const specialOOSLabel = localize('pdp.specialOOS.addToBag.label');

  return (
    <div className={`pdp-oos-image-layout-wrapper ${isDesktop ? 'grid grid-cols-2' : ''}`} data-testid='pdp-oos-image-layout-wrapper'>
      {media?.map((item, index: number) => {
        const imageUrl = item?.[imageSize] ?? '';

        return (
          <div
            className='pdp-oos-image'
            data-testid='pdp-oos-image'
            data-imageurl={imageUrl}
            key={`${index}-Sequence-${item.sequenceNumber}-${imageUrl}`}
            role='button'
            tabIndex={0}
          >
            {(!isSpecialOOSEnabled || (isDesktop && !imageUrl)) && <div className='pdp-oos-image__brand-logo'></div>}
            {index === 0 && <div className='pdp-oos-image__sold-out-label'>{isSpecialOOSEnabled ? specialOOSLabel : soldOutLabel}</div>}

            {isSpecialOOSEnabled && imageUrl && (
              <>
                <Image
                  alt={getProductImageAltText(localize, productTitle, index)}
                  fill
                  image={item as AdaptedImage}
                  priority={true}
                  src={imageUrl}
                  sizes={getPhotoBricksSizes()}
                  customSrcSet={getPhotoBricksSrcSet(item as AdaptedImage)}
                />
              </>
            )}
          </div>
        );
      })}
    </div>
  );
};
