import React from 'react';
import { render } from '@ecom-next/core/test/test-helper';
import { OutOfStockImageLayout } from '../OutOfStockImageLayout';
import { AdaptedImage } from '../../../pages/helpers/mediaSequencer';

describe('OutOfStockImageLayout', () => {
  const media: AdaptedImage[] = [
    { id: '1', isVideo: false, ZOOM: '/image1.jpg' },
    { id: '2', isVideo: false, ZOOM: '/image3.jpg' },
  ];

  const productTitle = 'Test Product';

  it('renders the correct number of media items', () => {
    const { getAllByTestId } = render(
      <OutOfStockImageLayout media={media} productTitle={productTitle} isDesktop={true} isSpecialOOSEnabled={false} imageSize='ZOOM' />
    );
    const items = getAllByTestId('pdp-oos-image-layout-wrapper')[0].querySelectorAll('.pdp-oos-image');
    expect(items.length).toBe(media.length);
  });

  it('sets the data-imageurl attribute correctly', () => {
    const { getAllByTestId } = render(
      <OutOfStockImageLayout media={media} productTitle={productTitle} isDesktop={true} isSpecialOOSEnabled={false} imageSize='ZOOM' />
    );
    const items = getAllByTestId('pdp-oos-image-layout-wrapper')[0].querySelectorAll('.pdp-oos-image');
    items.forEach((item, index) => {
      expect(item).toHaveAttribute('data-imageurl', media[index]?.['ZOOM'] ?? '');
    });
  });

  it('renders img tag when isSpecialOOSEnabled is true', () => {
    const { getAllByTestId } = render(
      <OutOfStockImageLayout media={media} productTitle={productTitle} isDesktop={true} isSpecialOOSEnabled={true} imageSize='ZOOM' />
    );
    const items = getAllByTestId('pdp-oos-image-layout-wrapper')[0].querySelectorAll('.pdp-oos-image img');
    expect(items.length).toBe(media.length);
  });

  it('does not render img tag when isSpecialOOSEnabled is false', () => {
    const { getAllByTestId } = render(
      <OutOfStockImageLayout media={media} productTitle={productTitle} isDesktop={true} isSpecialOOSEnabled={false} imageSize='ZOOM' />
    );
    const items = getAllByTestId('pdp-oos-image-layout-wrapper')[0].querySelectorAll('.pdp-oos-image img');
    expect(items.length).toBe(0);
  });

  it('does not render img tag when media item does not have imageUrl', () => {
    const mediaWithoutImageUrl: AdaptedImage[] = [media[0], { id: '2', isVideo: false }];
    const { getAllByTestId } = render(
      <OutOfStockImageLayout media={mediaWithoutImageUrl} productTitle={productTitle} isDesktop={true} isSpecialOOSEnabled={true} imageSize='ZOOM' />
    );
    const items = getAllByTestId('pdp-oos-image-layout-wrapper')[0].querySelectorAll('.pdp-oos-image img');
    expect(items.length).toBe(1);
  });
});
