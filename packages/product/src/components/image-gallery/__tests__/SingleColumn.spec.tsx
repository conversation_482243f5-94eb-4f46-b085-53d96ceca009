import React from 'react';
import { fireEvent, render, waitFor } from '@ecom-next/core/test/test-helper';
import { SingleColumn } from '../SingleColumn';
import { AdaptedImage, AdaptedVideo } from '../../../pages/helpers/mediaSequencer';

jest.mock('../VideoPlayer/VideoPlayer', () => jest.fn(() => <div>VideoPlayer</div>));

describe('SingleColumn', () => {
  const media: (AdaptedImage | AdaptedVideo)[] = [
    { isVideo: false, ZOOM: '/image1.jpg' } as AdaptedImage,
    { id: '2', isVideo: true, thumbnailImageUrl: '/thumb2.jpg', url: '/video2.mp4' } as AdaptedVideo,
    { isVideo: false, ZOOM: '/image3.jpg' } as AdaptedImage,
  ];

  const productTitle = 'Test Product';

  it('renders the correct number of media items', () => {
    const { getAllByTestId } = render(<SingleColumn media={media} productTitle={productTitle} />);
    const items = getAllByTestId('grid')[0].querySelectorAll('.pdp-photo-single-column-image');
    expect(items.length).toBe(media.length);
  });

  it('renders VideoPlayer for video items', () => {
    const { getAllByText } = render(<SingleColumn media={media} productTitle={productTitle} />);
    const videoPlayers = getAllByText('VideoPlayer');
    expect(videoPlayers.length).toBe(1);
  });

  it('renders ImageWrapper for image items', () => {
    const { getAllByAltText } = render(<SingleColumn media={media} productTitle={productTitle} />);
    const imageWrappers = getAllByAltText('pdp.photoBrick.photoBrickAltText');
    expect(imageWrappers.length).toBe(2);
  });

  it('sets the correct height for image wrappers', () => {
    const { getAllByTestId } = render(<SingleColumn media={media} productTitle={productTitle} />);
    const wrappers = getAllByTestId('grid')[0].querySelectorAll('.pdp-photo-single-column-image');
    wrappers.forEach(wrapper => {
      const img = wrapper.querySelector('img') || ({} as HTMLImageElement);
      const aspectRatio = img.naturalHeight / img.naturalWidth;
      const wrapperWidth = img.offsetWidth;
      const calculatedHeight = wrapperWidth * aspectRatio;
      expect(wrapper).toHaveStyle(`height: ${calculatedHeight}px`);
    });
  });

  it('sets correct height for image wrappers on resize', () => {
    const { getAllByTestId } = render(<SingleColumn media={media} productTitle={productTitle} />);
    const wrappers = getAllByTestId('grid')[0].querySelectorAll('.pdp-photo-single-column-image');
    const resizeEvent = new Event('resize');
    window.dispatchEvent(resizeEvent);
    wrappers.forEach(wrapper => {
      const img = wrapper.querySelector('img') || ({} as HTMLImageElement);
      const aspectRatio = img.naturalHeight / img.naturalWidth;
      const wrapperWidth = img.offsetWidth;
      const calculatedHeight = wrapperWidth * aspectRatio;
      expect(wrapper).toHaveStyle(`height: ${calculatedHeight}px`);
    });
  });

  it('sets the data-imageurl attribute correctly', () => {
    const { getAllByTestId } = render(<SingleColumn media={media} productTitle={productTitle} />);
    const items = getAllByTestId('grid')[0].querySelectorAll('.pdp-photo-single-column-image');
    items.forEach((item, index) => {
      expect(item).toHaveAttribute('data-imageurl', media[index]?.['ZOOM'] ?? '');
    });
  });

  it('renders placeholder until image is fully loaded', async () => {
    const { getAllByTestId } = render(<SingleColumn media={media} productTitle={productTitle} />);
    const placeholder = getAllByTestId('pdp-photo-single-column-image-skeleton-0')[0];
    expect(placeholder).toBeInTheDocument();
    const mainImage = getAllByTestId('grid')[0].querySelectorAll('.pdp-photo-single-column-image img')[0];
    expect(mainImage).toBeInTheDocument();
    waitFor(() => {
      expect(placeholder).not.toBeInTheDocument();
    });
  });

  it('execute setImageWrapperHeight when onLoad is triggered', async () => {
    const { getAllByTestId } = render(<SingleColumn media={media} productTitle={productTitle} />);
    const placeholder = getAllByTestId('pdp-photo-single-column-image-skeleton-0')[0];
    expect(placeholder).toBeInTheDocument();
    const mainImage = getAllByTestId('grid')[0].querySelectorAll('.pdp-photo-single-column-image ')[0];
    fireEvent.keyDown(mainImage, { key: 'Enter', code: 'Enter' });
    expect(mainImage).toBeInTheDocument();
    const image = mainImage.querySelector('img');
    fireEvent.load(image as HTMLImageElement);
    expect(image).toBeInTheDocument();
  });

  it('should hide images if previous are still loading', async () => {
    const { container } = render(<SingleColumn media={media} productTitle={productTitle} />);

    expect(container.querySelector(`[data-imageurl="/image3.jpg"]`)).toHaveStyle({ display: 'none' });
  });
});
