'use client';

import classNames from 'classnames';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { addPageAction } from '@ecom-next/core/reporting';
import React, { MouseEvent, useEffect, useRef, useState } from 'react';
import { ButtonIconPlay } from '@ecom-next/core/components/fabric/button-icon-play';
import { ButtonIconPause } from '@ecom-next/core/components/fabric/button-icon-pause';

type VideoPlayerProps = {
  activeIndex: number;
  dataTestId?: string;
  image?: string;
  index: number;
  onLoadedVideo?: () => void;
  onVideoClick?: () => void;
  productTitle: string;
  showControlButton?: boolean;
  src?: string;
  videoAltText: string;
};

const getVideoplayerAriaLabel = ({ isPlaying = false }: { isPlaying: boolean }): string => {
  return isPlaying ? 'pdp.photo.pauseVideoAriaLabel' : 'pdp.photo.playVideoAriaLabel';
};

const VideoPlayer = (props: VideoPlayerProps) => {
  const { src, image, index, showControlButton = false, activeIndex, onVideoClick, onLoadedVideo, productTitle, videoAltText, dataTestId } = props;

  const { localize } = useLocalize();
  const [playing, setPlaying] = useState(false);
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);
  const video = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    setPlaying(index === activeIndex);
  }, [index, activeIndex]);

  useEffect(() => {
    if (!video.current) {
      return;
    }
    try {
      if (playing) {
        video?.current?.play().catch(() => {});
        if (onVideoClick) {
          onVideoClick();
        }
      } else {
        video?.current?.pause();
      }
    } catch (e) {
      addPageAction('VideoPlayerError', { errorMessage: e, video });
      // eslint-disable-next-line no-console
      console.error('VideoPlayer - Play/Pause Error: ', e);
    }
  }, [playing, onVideoClick, activeIndex, video?.current]);

  function togglePlayState(e: MouseEvent) {
    e.stopPropagation();
    setPlaying(isPlaying => !isPlaying);
  }

  const handleShowControlButton = () => setIsVideoLoaded(true);

  const playAriaLabel = localize(getVideoplayerAriaLabel({ isPlaying: playing }), { productTitle });
  const wrapperClasses = classNames('product-photo--video-wrapper');
  const buttonClasses = classNames('product-photo--video-wrapper--inner', { 'is-playing': playing });
  const buttonIconPauseId = 'pdp-video-player-button-icon-pause';
  const playIconPauseId = 'pdp-video-player-button-icon-play';

  return (
    <>
      <div className={wrapperClasses}>
        <button aria-label={playAriaLabel} className={buttonClasses} onClick={togglePlayState} type='button'>
          <video
            ref={video}
            poster={image}
            className='product-photo--video'
            muted
            loop
            playsInline
            aria-label={videoAltText}
            data-testid={dataTestId}
            onCanPlayThrough={() => {
              handleShowControlButton();
              if (onLoadedVideo) {
                onLoadedVideo();
              }
            }}
          >
            <source data-testid='video-source' src={src} type='video/mp4' />
          </video>
        </button>
        {showControlButton && isVideoLoaded ? (
          <div className='pdp_video-player__control-button'>
            {playing ? (
              <ButtonIconPause data-testid={buttonIconPauseId} id={buttonIconPauseId} aria-hidden={true} tabIndex={-1} onClick={togglePlayState} />
            ) : (
              <ButtonIconPlay data-testid={playIconPauseId} id={playIconPauseId} aria-hidden={true} tabIndex={-1} onClick={togglePlayState} />
            )}
          </div>
        ) : (
          <></>
        )}
      </div>
    </>
  );
};

export default VideoPlayer;
