'use client';

import React, { useEffect, useLayoutEffect, useRef, useState } from 'react';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { LoadingPlaceholder } from '@ecom-next/core/components/fabric/loading-placeholder';
import { AdaptedImage, AdaptedVideo } from '../../pages/helpers/mediaSequencer';
import VideoPlayer from './VideoPlayer/VideoPlayer';
import { getProductImageAltText, getProductVideoAltText } from './helpers/util';
import { useZoomContext } from './zoom/ZoomContext';
import { Image } from './Image';

export const SingleColumn = ({ media, productTitle }: { media: (AdaptedImage | AdaptedVideo)[]; productTitle: string; topImagesLazy?: boolean }) => {
  const { localize } = useLocalize();
  const { setSelectedMediaIndex } = useZoomContext();

  const handleKeyDown = (event: React.KeyboardEvent<HTMLDivElement>, index: number) => {
    if (['Enter', 'Space'].includes(event.code)) {
      setSelectedMediaIndex(index);
    }
  };

  const imageWrapperRefs = useRef<(HTMLDivElement | null)[]>([]);
  const imageAspectRatiosRef = useRef<number[]>([]);
  const imageRefs = useRef<Record<string, HTMLImageElement | null>>({});

  const [imageLoadingStates, setImageLoadingStates] = useState<{ [key: string]: boolean }>({});

  const setImageWrapperHeight = (imageWrapper: HTMLDivElement | null, aspectRatio: number) => {
    if (imageWrapper) {
      const wrapperWidth = imageWrapper.offsetWidth;
      const calculatedHeight = wrapperWidth * aspectRatio;
      imageWrapper.style.height = `${calculatedHeight}px`;
    }
  };

  const handleImageLoad = (imageKey: string) => {
    setImageLoadingStates(prev => ({
      ...prev,
      [imageKey]: true,
    }));
  };

  const handleImageRef = (imageKey: string) => (element: HTMLImageElement | null) => {
    imageRefs.current[imageKey] = element;
  };

  const isImageLoaded = (imageKey: string) => {
    return imageLoadingStates[imageKey] || false;
  };

  useEffect(() => {
    const handleResize = () => {
      imageWrapperRefs.current.forEach((wrapper, index) => {
        const aspectRatio = imageAspectRatiosRef.current[index];
        setImageWrapperHeight(wrapper, aspectRatio);
      });
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    const initialLoadingStates: { [key: string]: boolean } = {};
    media.forEach((image, index) => {
      const imageKey = image?.['ZOOM'] ?? index.toString();
      initialLoadingStates[imageKey] = false;
    });
    setImageLoadingStates(initialLoadingStates);
  }, [media]);

  useLayoutEffect(() => {
    for (const imageKey in imageRefs.current) {
      const image = imageRefs.current[imageKey];
      if (image && image.complete && image.naturalHeight !== 0 && !isImageLoaded(imageKey)) {
        handleImageLoad(imageKey);
      }
    }
  }, [media, imageLoadingStates]);

  return (
    <div data-testid='grid'>
      <div className='pdp-photo-single-column-wrapper grid'>
        {media?.map((item, index: number) => {
          const imageUrl = item?.['ZOOM'] ?? '';
          const imageKey = imageUrl === '' ? index.toString() : imageUrl;
          const isLoadOnPriority = index === 0;
          const isCurrentImageLoaded = isImageLoaded(imageKey);
          const arePreviousImagesLoaded = () =>
            media.slice(0, index).every(mediaItem => {
              const mediaItemUrl = mediaItem?.['ZOOM'] ?? '';
              return isImageLoaded(mediaItemUrl === '' ? index.toString() : mediaItemUrl);
            });

          return (
            <div
              ref={wrapper => (imageWrapperRefs.current[index] = wrapper)}
              className='pdp-photo-single-column-image relative w-full'
              data-imageurl={imageUrl}
              key={`${index}-Sequence-${item.sequenceNumber}-${imageUrl}`}
              onClick={() => setSelectedMediaIndex(index)}
              onKeyDown={event => handleKeyDown(event, index)}
              role='button'
              tabIndex={0}
              style={item.isVideo || arePreviousImagesLoaded() ? {} : { display: 'none' }}
            >
              {!item.isVideo ? (
                <>
                  {!isCurrentImageLoaded && (
                    <div
                      className='pdp-photo-single-column-image-skeleton absolute left-0 top-0 h-full w-full'
                      data-testid={`pdp-photo-single-column-image-skeleton-${index}`}
                    >
                      <LoadingPlaceholder fixedSize={{ height: '100%', width: 'auto' }} />
                    </div>
                  )}
                  <Image
                    alt={getProductImageAltText(localize, productTitle, index)}
                    fill
                    image={item}
                    priority={isLoadOnPriority}
                    src={imageUrl}
                    ref={handleImageRef(imageKey)}
                    onLoad={e => {
                      const img = e.target as HTMLImageElement;
                      const aspectRatio = img.naturalHeight / img.naturalWidth;
                      imageAspectRatiosRef.current[index] = aspectRatio;
                      setImageWrapperHeight(imageWrapperRefs.current[index], aspectRatio);
                      handleImageLoad(imageKey);
                    }}
                  />
                </>
              ) : (
                <VideoPlayer
                  activeIndex={index}
                  index={index}
                  key={`single-column-productTitle-${item.id}-${index}`}
                  image={imageUrl}
                  onVideoClick={() => {}}
                  productTitle={productTitle}
                  src={item.url}
                  videoAltText={`${getProductVideoAltText(localize, '')} ${productTitle}`}
                  showControlButton
                />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};
