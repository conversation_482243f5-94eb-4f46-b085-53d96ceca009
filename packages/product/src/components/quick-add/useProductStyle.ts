import { useMemo, useRef } from 'react';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { Brand } from '@ecom-next/utils/server';
import { ProductData } from './QuickAddContainer';
import { QuickAddState } from './reducer';
import useDimension, { AugmentedSku } from './useDimension';

export default function useProductStyle(product: ProductData | null, state: QuickAddState, brand: Brand) {
  const { localize } = useLocalize();
  const { viewDetailsLabel, sizeLabel, amountLabel, quickAddSuccessText, addToBagText, variantLabel, fitLabel } = useRef({
    viewDetailsLabel: localize('pdp.lpo.fullDetailsLink'),
    sizeLabel: localize('pdp.sizeLabel'),
    amountLabel: localize('pdp.giftCardAmountLabel'),
    quickAddSuccessText: localize('quickAdd.addToBagSuccess'),
    addToBagText: localize('quickAdd.addToBag'),
    variantLabel: localize('pdp.variantGroupLabel'),
    fitLabel: localize('pdp.fitLabel'),
  }).current;

  const { styles = [] } = product || {};

  const { variants = [], styleDescription: productName = '', rating, isGiftCard, styleNumber, mergeType, primaryCategoryName } = styles[0] || {};

  const ratingsLabel = localize('fui.ReviewRatings.new-ariaLabel', {
    starRating: rating?.averageRating || 0,
    ratingCount: rating?.ratingCount || 0,
  });

  const { selectedVariantId, selectedSizeDimensions, isAddToBagSuccess } = state;

  const sortedVariants = useMemo(() => {
    return variants.sort((a, b) => a.variantSortOrder - b.variantSortOrder);
  }, [productName]);

  const addToBagButtonText = isAddToBagSuccess ? quickAddSuccessText : addToBagText;

  const variant = useMemo(() => sortedVariants.find(v => v.variantId === selectedVariantId), [selectedVariantId, productName]);

  const [selectedDim1, selectedDim2] = selectedSizeDimensions;

  const {
    customerChoices: colors = [],
    defaultCustomerChoiceId: defaultColorId,

    sizeDimension2LabelName,

    nonSizeSelectorLabelName,
  } = variant || {};

  const selectedColorId = defaultColorId;
  const selectedColor = useMemo(() => colors.find(c => c.customerChoiceNumber === selectedColorId), [selectedVariantId, productName]);

  const {
    images = [],
    skus = [],
    price,
    colorDescription: rawColorDescription,
    marketingFlags = [],
    seasonCode = '',
    seasonYear = '',
    vendorName,
    vendorNumber,
  } = selectedColor || {};

  const colorDescription = useMemo(() => {
    if (brand === 'gapfs' && rawColorDescription) {
      return rawColorDescription.charAt(0).toUpperCase() + rawColorDescription.slice(1);
    }
    return rawColorDescription;
  }, [brand, rawColorDescription]);

  const marketingFlag = marketingFlags && marketingFlags.length > 0 ? marketingFlags[0].flagContent : null;

  const mainImageData = useMemo(
    () =>
      images.find(data => {
        return data.legacyPlacement === 'P01';
      }),
    [selectedColorId]
  );

  const { url = '', width = 300, height = 400 } = useMemo(() => mainImageData?.crops.find(crop => crop.legacyType === 'VLI'), [selectedColorId]) || {};

  const { sizeDimension1Data, sizeDimension2Data, skusData, isOneSize } = useDimension(skus as AugmentedSku[], selectedVariantId, localize);

  const selectedSku = useMemo(() => {
    const { isInStock = false, index = -1 } = skusData.get(`${selectedDim1},${selectedDim2 || ''}`) || {};

    return isInStock && index >= 0 ? skus[index] : undefined;
  }, [selectedSizeDimensions, productName]);

  const mappedDim1LabelName = isGiftCard ? amountLabel : sizeLabel;
  const variantDimensions = useMemo(
    () => ({
      dimensions: [
        {
          label: mappedDim1LabelName,
          dimensions: sizeDimension1Data,
          selectedDimension: selectedDim1,
        },
        ...(sizeDimension2Data.length
          ? [
              {
                label: sizeDimension2LabelName || '',
                dimensions: sizeDimension2Data,
                selectedDimension: selectedDim2,
              },
            ]
          : []),
      ],
    }),
    [selectedVariantId, selectedSizeDimensions, productName]
  );

  const isDisabled =
    selectedDim1 && (!sizeDimension2Data.length || selectedDim2) && (skusData.get(`${selectedDim1},${selectedDim2 || ''}`)?.isInStock || false) === false;

  const commonQuickAddReportingData = useMemo(
    () => ({
      brand: brand as Brand,
      selectedColorId: selectedColorId || '',
      styleNumber,
      productName,
      vendorNumber: vendorNumber || '',
      vendorName,
      skus,
      mergeType,
      marketingFlag,
      primaryCategoryName,
      colorPaletteSeasonCode: seasonCode + seasonYear,
    }),
    [selectedColorId]
  );

  return {
    selectedColorId,
    selectedColor,
    selectedVariantId,
    selectedSku,
    selectedSizeDimensions,

    productName,
    styleNumber,
    rating,
    ratingsLabel,
    mainImageData,
    url,
    width,
    height,
    price,
    colorDescription,
    vendorName,
    vendorNumber,
    nonSizeSelectorLabelName,
    variantDimensions,
    sizeDimension1Data,
    sizeDimension2Data,
    sizeDimension2LabelName,
    skusData,
    marketingFlag,
    sortedVariants,
    mappedDim1LabelName,
    viewDetailsLabel,
    variantLabel,
    fitLabel,
    isOneSize,
    commonQuickAddReportingData,
    addToBagButtonText,
    isDisabled,
    isGiftCard,
  };
}
