import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import type { Brand, Locale, Market } from '@ecom-next/utils/server';
import { useState } from 'react';
import { clientFetch } from '@ecom-next/utils/clientFetch';
import { DataBuilder } from '@mfe/data-layer';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import dynamic from 'next/dynamic';
import { ButtonIconQuickAdd } from '@ecom-next/core/fabric/button-icon-quick-add';
import type { ProductData } from '@product-page/capi-utils';
import type { QuickAddContainerProps, ErrorResponse } from './QuickAddContainer';

const QuickAddContainer = dynamic<QuickAddContainerProps>(() => import('./QuickAddContainer'), {
  ssr: false,
});

interface QuickAddQuery {
  apiDomain?: string;
  brand: Brand;
  locale: Locale;
  market: Market;
  pid: string;
}

interface ProductError {
  error: {
    details?: string[];
    message: string;
  };
}

function fetchProductData(query: QuickAddQuery) {
  const { pid, market, locale, brand, apiDomain = '' } = query;

  return clientFetch(
    `${apiDomain}/commerce/catalog/aggregation/v2/products/customer-choice/${pid}?channel=ONL&market=${market.toUpperCase()}&brand=${brand.toUpperCase()}&locale=${locale}`,
    {
      headers: {
        'X-Client-Application-Name': 'hui-product',
      },
      signal: AbortSignal.timeout(5000),
    }
  )
    .then(data => {
      const { error } = data as ProductError;
      if (error) {
        throw new Error(error.message);
      }

      return data as ProductData;
    })
    .catch((error: unknown) => {
      const maybeError = error as Error;
      const maybeResponse = error as Response;

      const errorMessage = maybeError?.message || `An error occurred while fetching product data for pid: ${pid}, market: ${market}, locale: ${locale}`;

      const moreInfo = {
        stack: maybeError?.stack || '',
        status: maybeResponse?.status || 'unknown',
      };

      return {
        errors: [
          {
            developerMessage: errorMessage,
            moreInfo,
          },
        ],
        status: 'ERROR',
      } as ErrorResponse;
    });
}

export interface QuickAddProps {
  apiDomain?: string;
  isMultiVariantProduct?: boolean;
  onReportingAddToBag?: (dataBuilders: DataBuilder[]) => DataBuilder[];
  onReportingOpen?: (dataBuilders: DataBuilder[]) => DataBuilder[];
  pid: string;
  showPercentageOff?: boolean;
}

export default function QuickAdd(props: QuickAddProps) {
  const [productResponse, setProductResponse] = useState(null as ProductData | ErrorResponse | null);
  const { pid, showPercentageOff = false, apiDomain = '', onReportingOpen, onReportingAddToBag, isMultiVariantProduct } = props;

  const [isOpened, setIsOpened] = useState(false);
  const [isDataLoaded, setIsDataLoaded] = useState(false);
  const { brand, market, locale } = usePageContext();
  const { localize } = useLocalize();
  const ariaLabel = localize('quickAdd.openModal');

  const openQuickAdd = async () => {
    setIsOpened(true);

    try {
      const query: QuickAddQuery = { pid, market, brand, locale, apiDomain };
      const data = await fetchProductData(query);
      setProductResponse(data);
    } catch (error: unknown) {
      // eslint-disable-next-line no-console
      console.error('Failed to load product data:', error);
    } finally {
      setIsDataLoaded(true);
    }
  };

  return (
    <>
      {isOpened && (
        <QuickAddContainer
          productResponse={productResponse}
          locale={locale}
          market={market}
          brand={brand}
          onClose={() => {
            setIsOpened(false);
            setProductResponse(null);
            setIsDataLoaded(false);
          }}
          showPercentageOff={showPercentageOff}
          apiDomain={apiDomain}
          onReportingOpen={onReportingOpen}
          onReportingAddToBag={onReportingAddToBag}
          isDataLoaded={isDataLoaded}
          isMultiVariantProduct={isMultiVariantProduct}
        />
      )}
      <ButtonIconQuickAdd id={`quick-add-button-${pid}`} className='pdp-hui-quick-add absolute bottom-4 right-4' ariaLabel={ariaLabel} onClick={openQuickAdd} />
    </>
  );
}
