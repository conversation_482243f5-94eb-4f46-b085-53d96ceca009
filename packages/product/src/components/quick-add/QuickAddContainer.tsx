'use client';
import { useMemo, useRef } from 'react';
import { clsx } from 'clsx';
import { LoadingPlaceholder } from '@ecom-next/core/fabric/loading-placeholder';
import { Selector } from '@ecom-next/core/fabric/selector';
import { StarRatings } from '@ecom-next/core/fabric/star-ratings';
import { Link } from '@ecom-next/core/fabric/link';
import { addPageAction } from '@ecom-next/core/components/reporting';
import { useMediaQuery } from '@ecom-next/core/breakpoint-provider';
import { Modal } from '@ecom-next/core/fabric/modal';
import { Button } from '@ecom-next/core/fabric/button';
import { Brand } from '@ecom-next/utils/server';
import { brandToEnum } from '@ecom-next/core/react-stitch';
import { StyleRaw } from '@product-page/capi-utils';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { UserFeedbackAlert } from '@product-page/providers/buybox-provider/BuyBoxContext';
import { getBrandInformation } from '@mfe/brand-info';
import { addToBagService } from '../buy-box/components/add-to-bag/service/addToBagService';
import type { AddToBagErrorResponse, AdaptedAddToBagResponse } from '../buy-box/components/add-to-bag/types';
import { updateBagCount } from '../buy-box/components/add-to-bag/helpers/updateBagCount';
import { UserFeedbackComponent as UserFeedback } from '../buy-box/components/user-feedback/UserFeedbackNoStore';
import SizeDimensionGroup from './SizeDimensionGroup';
import { ProductPrice } from './ProductPrice';
import useQuickAddState from './useQuickAddState';
import useTealiumReporting from './useTealiumReporting';
import { QuickAddProps } from '.';

export interface ErrorResponse {
  errors: {
    developerMessage: string;
    moreInfo: Record<string, string>;
  }[];
  status: 'ERROR';
}

export interface ProductData {
  styles: StyleRaw[];
}
export interface QuickAddContainerProps {
  apiDomain?: string;
  brand: Brand;
  isDataLoaded?: boolean;
  isMultiVariantProduct?: boolean;
  locale: Locale;
  market: Extract<Market, 'us' | 'ca'>;
  onClose: () => void;
  onReportingAddToBag?: QuickAddProps['onReportingAddToBag'];
  onReportingOpen?: QuickAddProps['onReportingOpen'];
  productResponse: ProductData | ErrorResponse | null;
  showPercentageOff?: boolean;
}

interface ProductImage {
  alt: string;
  height: number;
  src: string;
  width: number;
}

function ProductImage(props: ProductImage) {
  const { src, width, height, alt } = props;
  return <img src={src} alt={alt} className='w-full' style={{ aspectRatio: `${width}/${height}` }} data-testid='quickadd-product-image' />;
}

function QuickAddLoadingModal({ onClose, isDesktop }: { isDesktop: boolean; onClose: () => void }) {
  const quickAddHeaderLoading = (
    <div className='gap-utk-spacing-s flex flex-col '>
      <LoadingPlaceholder className='h-5 w-11/12' fixedSize={{ width: '80%', height: '1.25rem' }} />
      <LoadingPlaceholder className='h-4 w-11/12' fixedSize={{ width: '50%', height: '1rem' }} />
      <LoadingPlaceholder className='h-3 w-11/12' fixedSize={{ width: '40%', height: '0.75rem' }} />
    </div>
  );

  const productImageLoading = <LoadingPlaceholder className='aspect-[3/4] w-full' fixedSize={{ width: '100%', height: '100%' }} />;

  return (
    <Modal withCloseIcon isOpen withHeader={false} callbackFn={onClose} className='pdp_quick-add w-full sm:w-2/3'>
      <div className='font-brand-base text-font-size-0 w-full'>
        <section className={clsx('gap-utk-spacing-l w-full bg-white', isDesktop ? 'grid grid-cols-2' : 'flex flex-col')}>
          {isDesktop ? productImageLoading : quickAddHeaderLoading}
          <div className='gap-utk-spacing-l flex flex-col'>
            {isDesktop ? quickAddHeaderLoading : productImageLoading}

            <div className='gap-utk-spacing-xl flex flex-col items-start self-stretch'>
              <LoadingPlaceholder className='w-full' fixedSize={{ width: '100%', height: '1rem' }} />

              <div className='gap-utk-spacing-s flex flex-col'>
                <LoadingPlaceholder className='w-full' fixedSize={{ width: '100%', height: '1rem' }} />
                <LoadingPlaceholder className='w-full' fixedSize={{ width: '100%', height: '2.5rem' }} />
              </div>
              <div className='gap-utk-spacing-s flex flex-col'>
                <LoadingPlaceholder className='w-full' fixedSize={{ width: '100%', height: '1rem' }} />
                <LoadingPlaceholder className='w-full' fixedSize={{ width: '100%', height: '5.5rem' }} />
              </div>
            </div>

            <div className='gap-utk-spacing-l flex flex-col items-center'>
              <LoadingPlaceholder className='w-full' fixedSize={{ width: '100%', height: '2.5rem' }} />
              <LoadingPlaceholder className='w-full' fixedSize={{ width: '30%', height: '1rem' }} />
            </div>
          </div>
        </section>
      </div>
    </Modal>
  );
}

function QuickAddProduct(props: Omit<QuickAddContainerProps, 'productResponse'> & { isDesktop: boolean; productResponse: ProductData | null }) {
  const {
    productResponse,
    locale,
    onClose,
    market,
    brand,
    showPercentageOff = false,
    apiDomain = '',
    onReportingOpen,
    onReportingAddToBag,
    isDesktop,
    isMultiVariantProduct,
  } = props;

  const { state, data, dispatch } = useQuickAddState(productResponse, brand);

  const { selectedVariantId, isAddToBagSuccess, isAddingToBag, isCautionState, error, alert } = state;

  const {
    selectedSku,
    productName,
    price,
    rating,
    ratingsLabel,
    marketingFlag,
    colorDescription,
    nonSizeSelectorLabelName,
    sizeDimension1Data,
    sizeDimension2Data,
    sortedVariants,
    selectedSizeDimensions,
    mappedDim1LabelName,
    skusData,
    sizeDimension2LabelName,
    isDisabled,
    selectedColorId,
    addToBagButtonText,
    viewDetailsLabel,
    variantLabel,
    fitLabel,
    url,
    width,
    height,
    commonQuickAddReportingData,
  } = data;

  const [selectedDim1, selectedDim2] = selectedSizeDimensions;
  const businessUnitId = getBrandInformation(brand, market).marketAwareBrandCode.toString();

  const adjustedViewDetailsLabel = ['gap', 'gapfs', 'br', 'brfs'].includes(brand)
    ? `${viewDetailsLabel.charAt(0).toUpperCase()}${viewDetailsLabel.slice(1).toLowerCase()}`
    : viewDetailsLabel;

  const isDropShip = !!(productResponse?.styles[0].webProductType?.toLowerCase() === 'dropship');

  const tealiumParams = useMemo(() => {
    return {
      ...commonQuickAddReportingData,
      businessUnitId,
      isDropShip,
      isMultiVariantProduct,
    };
  }, [
    commonQuickAddReportingData?.brand,
    commonQuickAddReportingData?.selectedColorId,
    commonQuickAddReportingData?.styleNumber,
    commonQuickAddReportingData?.productName,
    commonQuickAddReportingData?.vendorNumber,
    commonQuickAddReportingData?.vendorName,
    commonQuickAddReportingData?.skus,
    commonQuickAddReportingData?.mergeType,
    commonQuickAddReportingData?.marketingFlag,
    commonQuickAddReportingData?.primaryCategoryName,
    commonQuickAddReportingData?.colorPaletteSeasonCode,
    businessUnitId,
    isDropShip,
    isMultiVariantProduct,
  ]);
  const { reportAddToBag } = useTealiumReporting(tealiumParams, {
    onAddToBag: onReportingAddToBag,
    onStart: onReportingOpen,
  });

  const handleClick = async () => {
    if (!selectedSku) {
      dispatch({ type: 'INVALID_SKU_SELECTION' });
      return;
    }

    if (isAddingToBag || isAddToBagSuccess) {
      return;
    }

    dispatch({ type: 'ADDING_TO_BAG' });

    const response = await addToBagService({
      brandName: brandToEnum[brand],
      deliveryLocationId: '',
      locale,
      market,
      name: productName,
      quantity: 1,
      sku: selectedSku.skuNumber,
      atbOidcUrl: apiDomain,
    });

    if (!response || response?.status === 'ERROR') {
      const apiError = (response as AddToBagErrorResponse).errors?.[0]?.moreInfo || '';
      const { errorCode, developerMessage } = (response as AddToBagErrorResponse).errors?.[0] || {};

      buttonRef && buttonRef.current?.focus();

      addPageAction('addToBagActionLeapFrog', {
        addToBagDeveloperMessage: developerMessage,
        addToBagResponseCode: errorCode,
        addToBagResponseMessage: apiError,
        rawResponse: { ...response },
        skuId: selectedSku.skuId,
        isHuiRedesign: true,
      });

      dispatch({
        type: 'ADD_TO_BAG_ERROR',
        payload: {
          message: apiError,
          kind: 'apiError',
        },
      });

      return;
    }

    updateBagCount((response as AdaptedAddToBagResponse)?.totalBagQuantity || 1);

    addPageAction('addToBagActionLeapFrog', {
      numberOfItemsAdded: 1,
      skuId: selectedSku?.skuId,
      isHuiRedesign: true,
    });

    reportAddToBag(selectedSku);

    dispatch({ type: 'ADD_TO_BAG_SUCCESS' });
  };

  const buttonRef = useRef<HTMLButtonElement>(null);

  const quickAddHeader = productName && (
    <div className='tracking-font-letter-spacing-base font-font-weight-base-default flex flex-col gap-2'>
      <h2
        className='text-font-size-1 font-font-weight-base-heavier br:uppercase w-11/12'
        dangerouslySetInnerHTML={{ __html: productName }}
        data-testid='quickadd-product-name'
      />
      <ProductPrice price={price} locale={locale} brand={brand} showPercentageOff={showPercentageOff} />
      {marketingFlag && (
        <p className='text-style-product-card-marketing-flag text-style-product-card-font-size flex gap-2'>
          <span data-testid='quickadd-marketing-flag'>{marketingFlag}</span>
        </p>
      )}
      <StarRatings
        ratingValue={rating?.averageRating || 0}
        ratingSize='small'
        showRatingValue={false}
        postText={rating?.ratingCount ? `${rating?.ratingCount}` : ''}
        ariaLabel={ratingsLabel}
      />
    </div>
  );

  const productImage = url ? (
    <ProductImage src={`/${url}`} width={width} height={height} alt={productName} data-testid='quickadd-main-image' />
  ) : (
    <LoadingPlaceholder className='aspect-[3/4] w-full' fixedSize={{ width: '100%', height: '100%' }} />
  );

  return (
    <Modal withCloseIcon isOpen withHeader={false} callbackFn={onClose} className='pdp_quick-add'>
      <div className='font-brand-base text-font-size-0 w-full'>
        <section className={clsx('gap-utk-spacing-l w-full bg-white', isDesktop ? 'grid grid-cols-2' : 'flex flex-col')}>
          {isDesktop ? productImage : quickAddHeader}
          <div className='gap-utk-spacing-l flex flex-col'>
            {isDesktop ? quickAddHeader : productImage}

            {nonSizeSelectorLabelName && (
              <div className='gap-x-utk-spacing-2xs flex'>
                <span className='font-font-weight-base-heavier'>{nonSizeSelectorLabelName}</span>
                <span data-testid='quickadd-color'>{colorDescription}</span>
              </div>
            )}

            <div className='gap-utk-spacing-xl flex flex-col items-start self-stretch'>
              {sortedVariants.length > 1 ? (
                <section className='gap-utk-spacing-s flex w-full flex-col'>
                  <span className='font-font-weight-base-heavier'>{fitLabel}</span>
                  <div className='flex gap-2'>
                    {sortedVariants.map(variant => {
                      const { variantId, variantName } = variant;

                      return (
                        <Selector
                          key={variantId}
                          singleSelect
                          group={'sizeVariant'}
                          id={`variant-${variantId}`}
                          className='pdp_quick-add-variant-selector'
                          aria-label={`${variantLabel} ${variantName}`}
                          data-testid={`quickadd-variant${variantId === selectedVariantId ? '-selected' : ''}`}
                          onChange={() =>
                            dispatch({
                              type: 'SELECT_VARIANT',
                              variantId,
                            })
                          }
                          checked={variantId === selectedVariantId}
                        >
                          {variantName}
                        </Selector>
                      );
                    })}
                  </div>
                </section>
              ) : (
                !sortedVariants || (sortedVariants.length === 0 && <LoadingPlaceholder className='w-full' fixedSize={{ width: '100%', height: '1rem' }} />)
              )}

              {sizeDimension1Data.length > 0 ? (
                <SizeDimensionGroup
                  dimensionLabel={mappedDim1LabelName}
                  selectedSizeName={selectedDim1}
                  selectedAlternateSizeName={selectedDim2}
                  sizeDimensionData={sizeDimension1Data}
                  skusData={skusData}
                  groupName='dim1'
                  onChange={(dim1: string) =>
                    dispatch({
                      type: 'SELECT_DIM1',
                      dim1,
                      skusData,
                    })
                  }
                />
              ) : (
                <>
                  <div className='gap-utk-spacing-s flex flex-col'>
                    <LoadingPlaceholder className='w-full' fixedSize={{ width: '100%', height: '1rem' }} />
                    <LoadingPlaceholder className='w-full' fixedSize={{ width: '100%', height: '2.5rem' }} />
                  </div>
                  <div className='gap-utk-spacing-s flex flex-col'>
                    <LoadingPlaceholder className='w-full' fixedSize={{ width: '100%', height: '1rem' }} />
                    <LoadingPlaceholder className='w-full' fixedSize={{ width: '100%', height: '5.5rem' }} />
                  </div>
                </>
              )}

              {sizeDimension2Data.length > 0 && (
                <SizeDimensionGroup
                  dimensionLabel={sizeDimension2LabelName || ''}
                  selectedSizeName={selectedDim2}
                  selectedAlternateSizeName={selectedDim1}
                  sizeDimensionData={sizeDimension2Data}
                  skusData={skusData}
                  groupName='dim2'
                  onChange={(dim2: string) =>
                    dispatch({
                      type: 'SELECT_DIM2',
                      dim2,
                      skusData,
                    })
                  }
                />
              )}
            </div>
            {sizeDimension1Data.length > 0 && (
              <div className='flex flex-col'>
                <UserFeedback brandName={brandToEnum[brand]} alert={alert || null} />

                <Button
                  ref={buttonRef}
                  className='pdp_add-to-bag-button'
                  data-testid='quickadd-atb-btn'
                  data-iscaution={isCautionState}
                  data-isdisabled={isDisabled}
                  data-isloading={isAddingToBag}
                  kind='critical'
                  onClick={handleClick}
                  isLoading={isAddingToBag}
                  isDisabled={!!isDisabled}
                  isCaution={isCautionState}
                  cautionMessage={error || ''}
                >
                  <span className='br:uppercase'>{addToBagButtonText || ''}</span>
                </Button>
              </div>
            )}

            {selectedColorId && (
              <p className='flex w-full justify-center'>
                <Link href={`/browse/product.do?pid=${selectedColorId}`}>{adjustedViewDetailsLabel}</Link>
              </p>
            )}
          </div>
        </section>
      </div>
    </Modal>
  );
}

export default function QuickAddContainer(props: QuickAddContainerProps) {
  const {
    productResponse,
    locale,
    onClose,
    market,
    brand,
    showPercentageOff = false,
    onReportingAddToBag,
    onReportingOpen,
    apiDomain,
    isDataLoaded,
    isMultiVariantProduct,
  } = props;

  const { localize } = useLocalize();
  const isDesktop = useMediaQuery({
    range: '>',
    type: 'sm',
  });

  // Show loading modal when data is not loaded
  if (isDataLoaded === false) {
    return <QuickAddLoadingModal onClose={onClose} isDesktop={isDesktop} />;
  }

  const maybeErrorResponse = productResponse as ErrorResponse;
  const { errors } = maybeErrorResponse || {};

  let productError: UserFeedbackAlert | null = null;

  if (errors) {
    const { developerMessage: errorMessage, moreInfo = {} } = errors[0].moreInfo || {};
    productError = {
      kind: 'negative',
      message: localize('quickAdd.error.general'),
    };

    addPageAction('quickaddHUIOpen', {
      errors: errorMessage,
      info: moreInfo,
      isHuiRedesign: true,
    });
  }

  const maybeProductData = productResponse as ProductData;
  const isOutOfStock = maybeProductData?.styles?.[0]?.inventoryStatus === 'OUT_OF_STOCK';

  if (isOutOfStock) {
    productError = {
      kind: 'negative',
      message: localize('quickAdd.error.itemOutOfStock'),
    };

    addPageAction('quickaddHUIOpen', {
      errors: productError.message,
      isHuiRedesign: true,
    });
  }

  if (productError || (productResponse && 'errors' in productResponse)) {
    return (
      <Modal withCloseIcon isOpen withHeader={false} callbackFn={onClose} className='w-11/12 lg:w-1/3'>
        <div className='h-[33vh] w-10/12'>
          <UserFeedback brandName={brandToEnum[brand]} alert={productError || null} />
        </div>
      </Modal>
    );
  }

  return (
    <QuickAddProduct
      productResponse={productResponse}
      brand={brand}
      locale={locale}
      market={market}
      onClose={onClose}
      showPercentageOff={showPercentageOff}
      onReportingAddToBag={onReportingAddToBag}
      onReportingOpen={onReportingOpen}
      apiDomain={apiDomain}
      isDesktop={isDesktop}
      isMultiVariantProduct={isMultiVariantProduct}
    />
  );
}
