import React from 'react';
import { render, screen, fireEvent, act } from 'test-utils';
import datalayer from '@mfe/data-layer';
import fetchMock from 'fetch-mock-jest';
import QuickAddContainer from '../QuickAddContainer';
import QuickAdd from '../';
import { addToBagService } from '../../buy-box/components/add-to-bag/service/addToBagService';
import addToBagResponse from './add-to-bag-response.json';
import productWith2Dims from './product-data-response.json';
import productWith1Dim1Variant from './single-variant-single-dim.json';
import regularPriceProduct from './regular-price-product.json';
import backOrderProduct from './back-order-product.json';
import commonBuilderData from './common-builder.json';
import addToBagBuilderData from './add-to-bag-builder.json';
import errorProductData from './product-data-error.json';
import outOfStockData from './out-of-stock-product.json';
import oneSizeProduct from './one-size-product.json';

jest.mock('../../buy-box/components/add-to-bag/service/addToBagService', () => ({
  addToBagService: jest.fn(),
}));

describe('QuickAdd', () => {
  beforeEach(() => {
    (addToBagService as jest.Mock).mockImplementation((...args) => {
      const originalFunction = jest.requireActual('../../buy-box/components/add-to-bag/service/addToBagService').addToBagService;
      return originalFunction(...args);
    });
  });

  afterEach(() => {
    fetchMock.mockReset();
  });

  it('renders quick add button', () => {
    render(<QuickAdd pid={'1234546'} />);

    const quickAddBtn = screen.getByLabelText('quickAdd.openModal');
    expect(quickAddBtn).toBeInTheDocument();
  });

  it('should be able to open quickadd modal', async () => {
    fetchMock.get('glob:/commerce/catalog/aggregation/v2/products/customer-choice/*', productWith2Dims);

    render(<QuickAdd pid={'1234546'} />);

    const quickAddBtn = screen.getByLabelText('quickAdd.openModal');
    expect(quickAddBtn).toBeInTheDocument();

    await act(() => {
      fireEvent.click(quickAddBtn);
    });

    const modal = screen.queryByTestId('modal');
    expect(modal).toBeVisible();

    const addToBagButton = screen.getByRole('button', { name: /quickAdd.addToBag/i });
    expect(addToBagButton).toBeInTheDocument();

    const productImage = screen.getByTestId<HTMLImageElement>('quickadd-product-image');
    expect(productImage).toBeInTheDocument();
    expect(productImage.src).toContain('webcontent/0056/114/668/cn56114668.jpg');

    const productName = screen.getByTestId<HTMLHeadingElement>('quickadd-product-name');
    expect(productName).toBeInTheDocument();
    expect(productName).toHaveTextContent('Straight Five-Pocket Built-In Flex Jeans');
  });

  it('should use apiDomain prop correctly', async () => {
    const apiDomain = 'https://api.example.com';
    const mockProductData = { id: '1234546', name: 'Mock Product', styles: [{ webProductType: 'regular' }] };

    fetchMock.get(
      `${apiDomain}/commerce/catalog/aggregation/v2/products/customer-choice/1234546?channel=ONL&market=US&brand=GAP&locale=en_US`,
      mockProductData
    );

    render(<QuickAdd pid={'1234546'} apiDomain={apiDomain} />);

    const quickAddBtn = screen.getByLabelText('quickAdd.openModal');
    expect(quickAddBtn).toBeInTheDocument();

    await act(() => {
      fireEvent.click(quickAddBtn);
    });

    const modal = screen.queryByTestId('modal');
    expect(modal).toBeVisible();

    expect(fetchMock.called(`${apiDomain}/commerce/catalog/aggregation/v2/products/customer-choice/1234546?channel=ONL&market=US&brand=GAP&locale=en_US`)).toBe(
      true
    );
  });

  it('renders modal with error message when api call fails', async () => {
    fetchMock.get('glob:/commerce/catalog/aggregation/v2/products/customer-choice/*', 500);

    render(<QuickAdd pid={'1234546'} />);

    const quickAddBtn = screen.getByLabelText('quickAdd.openModal');
    expect(quickAddBtn).toBeInTheDocument();

    await act(() => {
      fireEvent.click(quickAddBtn);
    });

    const errorNotification = screen.getByTestId('buybox-user-feedback');
    expect(errorNotification).toBeInTheDocument();

    expect(errorNotification).toHaveTextContent(/quickAdd\.error\.general/);
  });

  it('renders error message for error response', async () => {
    fetchMock.get('glob:/commerce/catalog/aggregation/v2/products/customer-choice/*', errorProductData);

    render(<QuickAdd pid={'1234546'} />);

    const quickAddBtn = screen.getByLabelText('quickAdd.openModal');
    expect(quickAddBtn).toBeInTheDocument();

    await act(() => {
      fireEvent.click(quickAddBtn);
    });

    const errorNotification = screen.getByTestId('buybox-user-feedback');
    expect(errorNotification).toBeInTheDocument();

    expect(errorNotification).toHaveTextContent(/quickAdd\.error\.general/);
  });

  it('renders out-of-stock message for out-of-stock response', async () => {
    fetchMock.get('glob:/commerce/catalog/aggregation/v2/products/customer-choice/*', outOfStockData);

    render(<QuickAdd pid={'1234546'} />);

    const quickAddBtn = screen.getByLabelText('quickAdd.openModal');
    expect(quickAddBtn).toBeInTheDocument();

    await act(() => {
      fireEvent.click(quickAddBtn);
    });

    const errorNotification = screen.getByTestId('buybox-user-feedback');
    expect(errorNotification).toBeInTheDocument();

    expect(errorNotification).toHaveTextContent(/quickAdd\.error\.itemOutOfStock/);
  });

  it('should open modal with placeholder first, then render data', async () => {
    jest.useFakeTimers();

    fetchMock.get(
      'glob:/commerce/catalog/aggregation/v2/products/customer-choice/*',
      () =>
        new Promise(resolve => {
          setTimeout(() => {
            resolve(productWith2Dims);
          }, 3000);
        })
    );

    render(<QuickAdd pid={'1234546'} showPercentageOff={true} />);

    const quickAddBtn = screen.getByLabelText('quickAdd.openModal');
    expect(quickAddBtn).toBeInTheDocument();

    await act(() => {
      fireEvent.click(quickAddBtn);
    });

    let placeHolders = screen.queryAllByTestId<HTMLDivElement>('loading-placeholder');
    expect(placeHolders.length > 0).toEqual(true);

    let productImage = screen.queryByTestId<HTMLImageElement>('quickadd-product-image');
    expect(productImage).not.toBeInTheDocument();

    await act(() => {
      jest.advanceTimersByTime(4000);
    });

    placeHolders = screen.queryAllByTestId<HTMLDivElement>('loading-placeholder');
    expect(placeHolders.length === 0).toEqual(true);

    productImage = screen.queryByTestId<HTMLImageElement>('quickadd-product-image');
    expect(productImage).toBeInTheDocument();
  });

  it('should show percentage off', async () => {
    fetchMock.get('glob:/commerce/catalog/aggregation/v2/products/customer-choice/*', productWith2Dims);

    render(<QuickAdd pid={'1234546'} showPercentageOff={true} />);

    const quickAddBtn = screen.getByLabelText('quickAdd.openModal');
    expect(quickAddBtn).toBeInTheDocument();

    await act(() => {
      fireEvent.click(quickAddBtn);
    });

    const priceSpan = screen.getByTestId('quickadd-price');
    expect(priceSpan).toBeInTheDocument();

    expect(priceSpan).toHaveTextContent(/pdp\.price\.percentageOff/);
  });

  it('should default select one size product', async () => {
    fetchMock.get('glob:/commerce/catalog/aggregation/v2/products/customer-choice/*', oneSizeProduct);

    render(<QuickAdd pid={'1234546'} showPercentageOff={true} />);

    const quickAddBtn = screen.getByLabelText('quickAdd.openModal');
    expect(quickAddBtn).toBeInTheDocument();

    await act(() => {
      fireEvent.click(quickAddBtn);
    });

    const selectedVariantName = screen.queryByTestId<HTMLButtonElement>('quickadd-variant-selected');
    expect(selectedVariantName).not.toBeInTheDocument();

    const sizeDim1Group = screen.getByTestId<HTMLDivElement>('quickadd-sizegroup-dim1');
    expect(sizeDim1Group).toBeInTheDocument();

    const dim1Label = sizeDim1Group.querySelectorAll('p')?.[0];
    expect(dim1Label).toBeInTheDocument();

    const selectedDim1 = sizeDim1Group.querySelectorAll('.fds_selector__input:checked');
    expect(selectedDim1.length).toBe(1);

    const selected = selectedDim1[0].nextSibling;
    expect(selected).toHaveTextContent('One Size');
  });

  it('should render regular price, with no percentage off', async () => {
    fetchMock.get('glob:/commerce/catalog/aggregation/v2/products/customer-choice/*', regularPriceProduct);

    render(<QuickAdd pid={'1234546'} showPercentageOff={true} />);

    const quickAddBtn = screen.getByLabelText('quickAdd.openModal');
    expect(quickAddBtn).toBeInTheDocument();

    await act(() => {
      fireEvent.click(quickAddBtn);
    });

    const priceSpan = screen.getByTestId('quickadd-price');
    expect(priceSpan).toBeInTheDocument();

    expect(priceSpan).toHaveTextContent('$26.99');
  });

  it('renders 2 dimensions, multi variant product', () => {
    render(
      <QuickAddContainer
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        productResponse={productWith2Dims as any}
        locale='en_US'
        market='us'
        brand='on'
        onClose={() => {}}
      />
    );
    const addToBagButton = screen.getByRole('button', { name: /quickAdd.addToBag/i });
    expect(addToBagButton).toBeInTheDocument();

    const productImage = screen.getByTestId<HTMLImageElement>('quickadd-product-image');
    expect(productImage).toBeInTheDocument();
    expect(productImage.src).toContain('webcontent/0056/114/668/cn56114668.jpg');

    const productName = screen.getByTestId<HTMLHeadingElement>('quickadd-product-name');
    expect(productName).toBeInTheDocument();
    expect(productName).toHaveTextContent('Straight Five-Pocket Built-In Flex Jeans');

    const marketingFlag = screen.getByTestId<HTMLHeadingElement>('quickadd-marketing-flag');
    expect(marketingFlag).toBeInTheDocument();
    expect(marketingFlag).toHaveTextContent('Hot Deal');

    const priceSpan = screen.getByTestId('quickadd-price');
    expect(priceSpan).toBeInTheDocument();

    expect(priceSpan).toHaveTextContent(/\$44\.99\$30\.99/);

    const starRatings = screen.getByTestId<HTMLDivElement>('fds_star-ratings');
    expect(starRatings).toBeInTheDocument();
    expect(starRatings).toHaveTextContent(/13570/);

    const selectedColorName = screen.getByTestId<HTMLSpanElement>('quickadd-color');
    expect(selectedColorName).toBeInTheDocument();
    expect(selectedColorName).toHaveTextContent('Medium Wash');

    const selectedVariantName = screen.getByTestId<HTMLButtonElement>('quickadd-variant-selected').parentElement;
    expect(selectedVariantName).toBeInTheDocument();
    expect(selectedVariantName).toHaveTextContent('Regular');

    const sizeDim1Group = screen.getByTestId<HTMLDivElement>('quickadd-sizegroup-dim1');
    expect(sizeDim1Group).toBeInTheDocument();

    const dim1Label = sizeDim1Group.querySelectorAll('p')?.[0];
    expect(dim1Label).toBeInTheDocument();
    expect(dim1Label).toHaveTextContent(/pdp\.sizeLabel/);

    const dim1Options = sizeDim1Group.querySelectorAll('label');
    expect(dim1Options.length).toBe(18);

    const sizeDim2Group = screen.getByTestId<HTMLDivElement>('quickadd-sizegroup-dim2');
    expect(sizeDim2Group).toBeInTheDocument();

    const dim2Label = sizeDim2Group.querySelectorAll('p')?.[0];
    expect(dim2Label).toBeInTheDocument();
    expect(dim2Label).toHaveTextContent(/Length/);

    const dim2Options = sizeDim2Group.querySelectorAll('label');
    expect(dim2Options.length).toBe(5);

    expect(screen.queryByTestId('buybox-user-feedback')).not.toBeInTheDocument();

    expect(addToBagButton).toHaveAttribute('data-iscaution', 'false');
  });

  it('renders 1 dimension, 1 variant product', () => {
    render(
      <QuickAddContainer
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        productResponse={productWith1Dim1Variant as any}
        locale='en_US'
        market='us'
        brand='on'
        onClose={() => {}}
      />
    );
    const addToBagButton = screen.getByRole('button', { name: /quickAdd.addToBag/i });
    expect(addToBagButton).toBeInTheDocument();

    const productImage = screen.getByTestId<HTMLImageElement>('quickadd-product-image');
    expect(productImage).toBeInTheDocument();
    expect(productImage.src).toContain('webcontent/0057/319/204/cn57319204.jpg');

    const productName = screen.getByTestId<HTMLHeadingElement>('quickadd-product-name');
    expect(productName).toBeInTheDocument();
    expect(productName).toHaveTextContent('Printed Rashguard Top and Ruffled Bikini Swim Set for Toddler & Baby');

    const marketingFlag = screen.getByTestId<HTMLHeadingElement>('quickadd-marketing-flag');
    expect(marketingFlag).toBeInTheDocument();
    expect(marketingFlag).toHaveTextContent('Hot Deal');

    const starRatings = screen.getByTestId<HTMLDivElement>('fds_star-ratings');
    expect(starRatings).toBeInTheDocument();
    expect(starRatings).toHaveTextContent(/257/);

    const selectedColorName = screen.getByTestId<HTMLSpanElement>('quickadd-color');
    expect(selectedColorName).toBeInTheDocument();
    expect(selectedColorName).toHaveTextContent('Pink Multi Tie-Dye');

    const selectedVariantName = screen.queryByTestId<HTMLButtonElement>('quickadd-variant-selected');
    expect(selectedVariantName).not.toBeInTheDocument();

    const sizeDim1Group = screen.getByTestId<HTMLDivElement>('quickadd-sizegroup-dim1');
    expect(sizeDim1Group).toBeInTheDocument();

    const dim1Label = sizeDim1Group.querySelectorAll('p')?.[0];
    expect(dim1Label).toBeInTheDocument();

    const dim1Options = sizeDim1Group.querySelectorAll('label');
    expect(dim1Options.length).toBe(6);

    const sizeDim2Group = screen.queryByTestId<HTMLDivElement>('quickadd-sizegroup-dim2');
    expect(sizeDim2Group).not.toBeInTheDocument();

    expect(screen.queryByTestId('buybox-user-feedback')).not.toBeInTheDocument();

    expect(addToBagButton).toHaveAttribute('data-iscaution', 'false');
  });

  it('shows error notification when no size is selected', async () => {
    render(
      <QuickAddContainer
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        productResponse={productWith2Dims as any}
        locale='en_US'
        market='us'
        brand='on'
        onClose={() => {}}
      />
    );

    const addToBagButton = screen.getByRole('button', { name: /quickAdd.addToBag/i });

    fireEvent.click(addToBagButton);

    const errorNotification = screen.getByTestId<HTMLDivElement>('buybox-user-feedback');
    expect(errorNotification).toBeInTheDocument();
    expect(errorNotification).toHaveTextContent(/pdp\.addToBag\.selectionErrorAlternate/);

    expect(addToBagButton).toHaveAttribute('data-iscaution', 'true');
  });

  it('shows error notification when only 1 size is selected', () => {
    render(
      <QuickAddContainer
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        productResponse={productWith2Dims as any}
        locale='en_US'
        market='us'
        brand='on'
        onClose={() => {}}
      />
    );

    const addToBagButton = screen.getByRole('button', { name: /quickAdd.addToBag/i });
    fireEvent.click(screen.getByText('26W'));
    fireEvent.click(addToBagButton);

    const errorNotification = screen.getByTestId<HTMLDivElement>('buybox-user-feedback');
    expect(errorNotification).toBeInTheDocument();
    expect(errorNotification).toHaveTextContent('pdp.addToBag.selectionErrorAlternate');

    expect(addToBagButton).toHaveAttribute('data-iscaution', 'true');
  });

  it('no error notification when size selection changes', () => {
    render(
      <QuickAddContainer
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        productResponse={productWith2Dims as any}
        locale='en_US'
        market='us'
        brand='on'
        onClose={() => {}}
      />
    );

    const addToBagButton = screen.getByRole('button', { name: /quickAdd.addToBag/i });

    fireEvent.click(addToBagButton);
    const errorNotification = screen.getByTestId<HTMLDivElement>('buybox-user-feedback');
    expect(errorNotification).toBeInTheDocument();
    expect(errorNotification).toHaveTextContent('pdp.addToBag.selectionErrorAlternate');

    fireEvent.click(screen.getByText('26W'));

    expect(errorNotification).not.toBeInTheDocument();
    expect(addToBagButton).toHaveAttribute('data-iscaution', 'false');
  });

  it('disables add to bag button when OOS sku selected', () => {
    render(
      <QuickAddContainer
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        productResponse={productWith2Dims as any}
        locale='en_US'
        market='us'
        brand='on'
        onClose={() => {}}
      />
    );

    const addToBagButton = screen.getByRole('button', { name: /quickAdd.addToBag/i });
    fireEvent.click(screen.getByText('40W'));
    fireEvent.click(screen.getByText('31L'));

    expect(addToBagButton).toBeDisabled();
  });

  it('adds item to bag when valid sku is selected', async () => {
    const addToBagUrl = `/commerce/shopping-bags/items/summary?locale=en_US`;

    fetchMock.post(addToBagUrl, addToBagResponse);

    render(
      <QuickAddContainer
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        productResponse={productWith2Dims as any}
        locale='en_US'
        market='us'
        brand='on'
        onClose={() => {}}
      />
    );

    const addToBagButton = screen.getByRole('button', { name: /quickAdd.addToBag/i });
    fireEvent.click(screen.getByText('36W'));
    fireEvent.click(screen.getByText('32L'));

    await act(async () => {
      fireEvent.click(addToBagButton);
    });

    expect(addToBagButton).toHaveTextContent(/quickAdd\.addToBagSuccess/);
    expect(addToBagButton).toHaveAttribute('data-iscaution', 'true');
  });

  it('calls add to bag only once, even when clicked multiple times', async () => {
    const addToBagUrl = `/commerce/shopping-bags/items/summary?locale=en_US`;

    fetchMock.post(addToBagUrl, addToBagResponse);

    render(
      <QuickAddContainer
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        productResponse={productWith2Dims as any}
        locale='en_US'
        market='us'
        brand='on'
        onClose={() => {}}
      />
    );

    const addToBagButton = screen.getByRole('button', { name: /quickAdd.addToBag/i });
    fireEvent.click(screen.getByText('36W'));
    fireEvent.click(screen.getByText('32L'));

    await act(async () => {
      fireEvent.click(addToBagButton);
    });

    await act(async () => {
      fireEvent.click(addToBagButton);
    });

    await act(async () => {
      fireEvent.click(addToBagButton);
    });

    expect(addToBagService).toHaveBeenCalledTimes(1);
  });

  it('shows error when add to bag fails', async () => {
    const addToBagUrl = `/commerce/shopping-bags/items/summary?locale=en_US`;

    fetchMock.post(addToBagUrl, 500);

    render(
      <QuickAddContainer
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        productResponse={productWith2Dims as any}
        locale='en_US'
        market='us'
        brand='on'
        onClose={() => {}}
      />
    );

    const addToBagButton = screen.getByRole('button', { name: /quickAdd.addToBag/i });
    fireEvent.click(screen.getByText('36W'));
    fireEvent.click(screen.getByText('32L'));

    await act(async () => {
      fireEvent.click(addToBagButton);
    });

    const errorNotification = screen.getByTestId<HTMLDivElement>('buybox-user-feedback');
    expect(errorNotification).toBeInTheDocument();
    expect(errorNotification).toHaveTextContent("We're sorry, there's been an error adding this item to bag.");

    expect(addToBagButton).toHaveAttribute('data-iscaution', 'true');
  });

  it('able to change variant', () => {
    render(
      <QuickAddContainer
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        productResponse={productWith2Dims as any}
        locale='en_US'
        market='us'
        brand='on'
        onClose={() => {}}
      />
    );

    fireEvent.click(screen.getByText('36W'));
    fireEvent.click(screen.getByText('32L'));

    let sizeDim1Group = screen.getByTestId<HTMLDivElement>('quickadd-sizegroup-dim1');
    expect(sizeDim1Group).toBeInTheDocument();

    let selectedDim1 = sizeDim1Group.querySelector('.fds_selector__input:checked')?.nextSibling;

    expect(selectedDim1).toHaveTextContent('36W');

    let sizeDim2Group = screen.getByTestId<HTMLDivElement>('quickadd-sizegroup-dim2');
    expect(sizeDim2Group).toBeInTheDocument();

    let selectedDim2 = sizeDim2Group.querySelector('.fds_selector__input:checked')?.nextSibling;

    expect(selectedDim2).toHaveTextContent('32L');

    fireEvent.click(screen.getByText('Tall'));

    sizeDim1Group = screen.getByTestId<HTMLDivElement>('quickadd-sizegroup-dim1');
    expect(sizeDim1Group).toBeInTheDocument();

    selectedDim1 = sizeDim1Group.querySelector('.fds_selector__input:checked')?.nextSibling;

    expect(selectedDim1).toHaveTextContent('36W');

    sizeDim2Group = screen.getByTestId<HTMLDivElement>('quickadd-sizegroup-dim2');
    expect(sizeDim2Group).toBeInTheDocument();

    selectedDim2 = sizeDim2Group.querySelector('.fds_selector__input:checked');

    expect(selectedDim2).not.toBeInTheDocument();
  });

  it('persist size selection when possible, after change variant', () => {
    render(
      <QuickAddContainer
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        productResponse={productWith2Dims as any}
        locale='en_US'
        market='us'
        brand='on'
        onClose={() => {}}
      />
    );

    fireEvent.click(screen.getByText('Tall'));

    const selectedVariantName = screen.getByTestId<HTMLButtonElement>('quickadd-variant-selected').parentElement;

    expect(selectedVariantName).toBeInTheDocument();
    expect(selectedVariantName).toHaveTextContent('Tall');

    const sizeDim1Group = screen.getByTestId<HTMLDivElement>('quickadd-sizegroup-dim1');
    expect(sizeDim1Group).toBeInTheDocument();

    const dim1Options = sizeDim1Group.querySelectorAll('label');
    expect(dim1Options.length).toBe(9);

    const sizeDim2Group = screen.getByTestId<HTMLDivElement>('quickadd-sizegroup-dim2');
    expect(sizeDim2Group).toBeInTheDocument();

    const dim2Options = sizeDim2Group.querySelectorAll('label');
    expect(dim2Options.length).toBe(2);
  });

  it('shows backorder notification when backorder sku selected', () => {
    render(
      <QuickAddContainer
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        productResponse={backOrderProduct as any}
        locale='en_CA'
        market='ca'
        brand='at'
        onClose={() => {}}
      />
    );

    fireEvent.click(screen.getByText('XS'));

    const backOrderNotification = screen.getByTestId<HTMLDivElement>('buybox-user-feedback');
    expect(backOrderNotification).toBeInTheDocument();
    expect(backOrderNotification).toHaveTextContent('quickAdd.onBackOrderMessage');
  });

  it('report datalayer on modal open', () => {
    render(
      <QuickAddContainer
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        productResponse={productWith2Dims as any}
        locale='en_US'
        market='us'
        brand='on'
        onClose={() => {}}
      />
    );

    const dataBuilders = (datalayer.viewWith as jest.Mock).mock.calls[0][0];

    expect(dataBuilders.length).toEqual(2);

    const [commonBuilder, quickAddStartBuilder] = dataBuilders;
    expect(commonBuilder.name).toEqual('quickadd_common');
    expect(commonBuilder.data).toEqual(commonBuilderData);

    expect(quickAddStartBuilder.name).toEqual('quickadd_start');
    expect(quickAddStartBuilder.data).toEqual({
      event_name: 'quickadd_start',
      page_type: 'QuickAddStart',
      page_name: 'on:browse:QuickAddStart',
    });
  });

  it('report with custom datalayer builder on modal open', () => {
    render(
      <QuickAddContainer
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        productResponse={productWith2Dims as any}
        locale='en_US'
        market='us'
        brand='on'
        onClose={() => {}}
        onReportingOpen={builders => {
          const data = builders.reduce((result, builder) => {
            return { ...result, ...builder.data };
          }, {}) as any; // eslint-disable-line @typescript-eslint/no-explicit-any

          return [
            {
              name: 'custom_view',
              data: {
                ...data,
                page_type: `${data.page_type}_custom`,
              },
            },
          ];
        }}
      />
    );

    const dataBuilders = (datalayer.viewWith as jest.Mock).mock.calls[0][0];

    expect(dataBuilders.length).toEqual(1);

    const [customBuilder] = dataBuilders;
    expect(customBuilder.name).toEqual('custom_view');
    expect(customBuilder.data.page_type).toEqual('QuickAddStart_custom');
  });

  it('report datalayer on quickadd to bag', async () => {
    const addToBagUrl = `/commerce/shopping-bags/items/summary?locale=en_US`;

    fetchMock.post(addToBagUrl, addToBagResponse);

    render(
      <QuickAddContainer
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        productResponse={productWith2Dims as any}
        locale='en_US'
        market='us'
        brand='on'
        onClose={() => {}}
      />
    );

    const addToBagButton = screen.getByRole('button', { name: /quickAdd.addToBag/i });
    fireEvent.click(screen.getByText('36W'));
    fireEvent.click(screen.getByText('32L'));

    await act(async () => {
      fireEvent.click(addToBagButton);
    });

    expect(addToBagButton).toHaveTextContent(/quickAdd\.addToBagSuccess/);
    expect(addToBagButton).toHaveAttribute('data-iscaution', 'true');

    const dataBuilders = (datalayer.viewWith as jest.Mock).mock.calls[1][0];

    expect(dataBuilders.length).toEqual(2);

    const [commonBuilder, addToBagBuilder] = dataBuilders;
    expect(commonBuilder.name).toEqual('quickadd_common');
    expect(commonBuilder.data).toEqual(commonBuilderData);

    expect(addToBagBuilder.name).toEqual('cart_add');
    expect(addToBagBuilder.data).toEqual(addToBagBuilderData);
  });

  it('use custom datalayer builder on quickadd to bag', async () => {
    const addToBagUrl = `/commerce/shopping-bags/items/summary?locale=en_US`;

    fetchMock.post(addToBagUrl, addToBagResponse);

    const customBuilder = {
      data: {
        event_name: 'quickadd_start',
        page_type: 'QuickAddStart',
      },
      name: 'quickadd_start',
    };
    render(
      <QuickAddContainer
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        productResponse={productWith2Dims as any}
        locale='en_US'
        market='us'
        brand='on'
        onClose={() => {}}
        onReportingAddToBag={builders => {
          return [...builders, customBuilder];
        }}
      />
    );

    const addToBagButton = screen.getByRole('button', { name: /quickAdd.addToBag/i });
    fireEvent.click(screen.getByText('36W'));
    fireEvent.click(screen.getByText('32L'));

    await act(async () => {
      fireEvent.click(addToBagButton);
    });

    expect(addToBagButton).toHaveTextContent(/quickAdd\.addToBagSuccess/);
    expect(addToBagButton).toHaveAttribute('data-iscaution', 'true');

    const dataBuilders = (datalayer.viewWith as jest.Mock).mock.calls[1][0];

    expect(dataBuilders.length).toEqual(3);

    const [commonBuilder, addToBagBuilder, customDataBuilder] = dataBuilders;
    expect(commonBuilder.name).toEqual('quickadd_common');
    expect(commonBuilder.data).toEqual(commonBuilderData);

    expect(addToBagBuilder.name).toEqual('cart_add');
    expect(addToBagBuilder.data).toEqual(addToBagBuilderData);

    expect(customDataBuilder?.name).toEqual(customBuilder.name);
    expect(customDataBuilder.data).toEqual(customBuilder.data);
  });

  it('capitalizes first letter of colorDescription for gapfs brand only', () => {
    const gapfsProductData = {
      ...productWith2Dims,
      styles: [
        {
          ...productWith2Dims.styles[0],
          variants: [
            {
              ...productWith2Dims.styles[0].variants[0],
              customerChoices: [
                {
                  ...productWith2Dims.styles[0].variants[0].customerChoices[0],
                  colorDescription: 'disney family blue ridge',
                },
              ],
            },
          ],
        },
      ],
    };

    render(
      <QuickAddContainer
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        productResponse={gapfsProductData as any}
        locale='en_US'
        market='us'
        brand='gapfs'
        onClose={() => {}}
      />
    );

    const selectedColorName = screen.getByTestId<HTMLSpanElement>('quickadd-color');
    expect(selectedColorName).toBeInTheDocument();
    expect(selectedColorName).toHaveTextContent('Disney family blue ridge');
  });

  it('does not capitalize colorDescription for non-gapfs brands', () => {
    const nonGapfsProductData = {
      ...productWith2Dims,
      styles: [
        {
          ...productWith2Dims.styles[0],
          variants: [
            {
              ...productWith2Dims.styles[0].variants[0],
              customerChoices: [
                {
                  ...productWith2Dims.styles[0].variants[0].customerChoices[0],
                  colorDescription: 'disney family blue ridge',
                },
              ],
            },
          ],
        },
      ],
    };

    render(
      <QuickAddContainer
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        productResponse={nonGapfsProductData as any}
        locale='en_US'
        market='us'
        brand='gap'
        onClose={() => {}}
      />
    );

    const selectedColorName = screen.getByTestId<HTMLSpanElement>('quickadd-color');
    expect(selectedColorName).toBeInTheDocument();
    expect(selectedColorName).toHaveTextContent('disney family blue ridge');
  });
});
