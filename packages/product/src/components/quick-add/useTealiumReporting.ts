import { Brand } from '@ecom-next/utils/server';
import { LocalizationContextT, useLocalize } from '@ecom-next/sitewide/localization-provider';
import { useCallback, useEffect } from 'react';
import datalayer from '@mfe/data-layer';
import { SKURaw, StyleRaw } from '@product-page/capi-utils';
import { getMergeType } from '@product-page/util/get-merge-type';
import { PRODUCT_INVENTORY_STATUS_MAP } from '@product-page/providers/pdp-reporter-provider/v3/get-inventory-status';
import { AugmentedSku } from './useDimension';
import { QuickAddProps } from '.';

interface DataBuilderParams {
  brand: Brand;
  businessUnitId?: string;
  colorPaletteSeasonCode?: string;
  isDropShip: boolean;
  isMultiVariantProduct?: boolean;
  marketingFlag: string | null;
  mergeType: StyleRaw['mergeType'];
  primaryCategoryName?: string;
  productName: string;
  selectedColorId: string;
  skus: SKURaw[];
  styleNumber: string;
  vendorName?: string;
  vendorNumber: string;
}

interface ReportingOptions {
  onAddToBag?: QuickAddProps['onReportingAddToBag'];
  onStart?: QuickAddProps['onReportingOpen'];
}

const InventoryStatusToLegacy = {
  IN_STOCK: 0,
  LIMITED_STOCK: 1,
  LOW_STOCK: 1,
  OUT_OF_STOCK: 3,
  BACK_ORDER: 4,
  HIGH_STOCK: 0,
  ON_ORDER: 4,
} as const;

function formatSkuId(skuId: string): string {
  const formattedSkuIdSize = 4;
  const maxLeadingZeros = 3;
  const formattedSkuId = skuId.slice(-formattedSkuIdSize);
  for (let index = maxLeadingZeros; index >= 1; index -= 1) {
    const regexp = new RegExp(`0{${index}}[0-9]{${formattedSkuIdSize - index}}`);
    if (formattedSkuId.match(regexp)) {
      return formattedSkuId.slice(-formattedSkuIdSize + index);
    }
  }
  return formattedSkuId;
}

function getMvgPropsToOverride(isMultiVariantProduct: boolean) {
  return isMultiVariantProduct
    ? {
        product_mvg_id: '',
        product_variant_selected: '',
        product_mvg_price: '',
      }
    : {};
}

function commonDataBuilder(params: DataBuilderParams) {
  const {
    brand,
    selectedColorId,
    styleNumber,
    productName,
    vendorNumber,
    vendorName,
    skus,
    mergeType,
    marketingFlag,
    primaryCategoryName,
    colorPaletteSeasonCode,
    businessUnitId,
    isDropShip,
    isMultiVariantProduct,
  } = params;

  return {
    data: {
      page_type: 'QuickAdd',
      product_brand: [brand.toUpperCase()],
      product_cc_id: [selectedColorId],
      product_dropship: [isDropShip.toString()],
      product_id: [styleNumber],
      product_name: [productName],
      product_seller_id: [`${isDropShip ? vendorNumber : businessUnitId}`],
      product_seller_name: [isDropShip ? vendorName : brand.toUpperCase()],
      size_inventory_status: skus.map(size => `${formatSkuId(size.skuNumber)}-${InventoryStatusToLegacy[size.inventoryStatus.status]}`).join(':'),
      product_page_type: [getMergeType(mergeType)],
      product_marketing_flag: marketingFlag ? marketingFlag : 'No flag',
      product_primary_category: [primaryCategoryName as string],
      is_react: true,
      ...(colorPaletteSeasonCode
        ? {
            product_season_code: colorPaletteSeasonCode,
          }
        : {}),
      ...getMvgPropsToOverride(!!isMultiVariantProduct),
    },
    name: 'quickadd_common',
  };
}

function startDataBuilder(brand: string) {
  return {
    data: {
      event_name: 'quickadd_start',
      page_type: 'QuickAddStart',
      page_name: `${brand}:browse:QuickAddStart`,
    } as any, // eslint-disable-line @typescript-eslint/no-explicit-any
    name: 'quickadd_start',
  };
}

function addToBagBuilder(selectedSku: AugmentedSku, formatCurrency: LocalizationContextT['formatCurrency'], brand: string) {
  const {
    skuNumber,
    price,
    sizeDimension1,
    inventoryStatus: { status },
  } = selectedSku;
  const { regularPrice, effectivePrice } = price;

  return {
    data: {
      event_name: 'cart_add',
      page_name: `${brand}:browse:QuickAdd`,
      page_type: 'QuickAdd',
      product_sku: [skuNumber],
      product_price: [effectivePrice.toFixed(2).toString()],
      product_gross_merchandise: [effectivePrice.toFixed(2).toString()],
      product_markdown_amount: [`${(Math.round((regularPrice - effectivePrice) * 100) / 100).toFixed(2)}`],
      product_quantity: ['1'],
      product_inventory_conversion: PRODUCT_INVENTORY_STATUS_MAP[status],
      product_selected_size: [sizeDimension1],
    } as any, // eslint-disable-line @typescript-eslint/no-explicit-any
    name: 'cart_add',
  };
}

export default function useTealiumReporting(params: DataBuilderParams | undefined, options: ReportingOptions = {}) {
  const { formatCurrency } = useLocalize();
  const { onAddToBag, onStart } = options;

  const addToBagBuilderWithCurrency = useCallback(
    (selectedSku: SKURaw) => {
      if (!params) {
        return;
      }
      const dataBuilders = [commonDataBuilder(params), addToBagBuilder(selectedSku as AugmentedSku, formatCurrency, params.brand)];
      datalayer.viewWith(onAddToBag ? onAddToBag(dataBuilders) : dataBuilders, null);
    },
    [params]
  );

  useEffect(() => {
    if (!params || !params?.productName || !params?.selectedColorId || !params?.styleNumber) {
      return;
    }

    const dataBuilders = [commonDataBuilder(params), startDataBuilder(params?.brand)];
    datalayer.viewWith(onStart ? onStart(dataBuilders) : dataBuilders, null);
  }, [params]);

  return {
    reportAddToBag: addToBagBuilderWithCurrency,
  };
}
