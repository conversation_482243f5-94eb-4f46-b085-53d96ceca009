'use client';

import { useShallow } from 'zustand/react/shallow';
import { PDPRecsCarouselWrapper } from '..';
import type { FeaturesConfig } from '../../../pages/getFeatureConfig';
import { useMVGBuyBoxStore } from '../../../providers/mvg-buybox-provider';

type MvgPDPRecsCarouselWrapperProps = {
  isMultiVariantProduct: boolean;
  isPercentageEnabled: boolean;
  layoutType?: 'grid' | 'carousel';
  recommendationsConfig: FeaturesConfig['recommendationsConfig'];
  reportCertonaLoad?: boolean;
};

export const MvgPDPRecsCarouselWrapper = ({
  isPercentageEnabled,
  recommendationsConfig,
  reportCertonaLoad = false,
  layoutType,
  isMultiVariantProduct,
}: MvgPDPRecsCarouselWrapperProps) => {
  const { selectedCustomerChoiceId } = useMVGBuyBoxStore(
    useShallow(state => ({
      selectedCustomerChoiceId: state?.selectedCustomerChoice?.customer_choice_id,
    }))
  );

  return (
    <PDPRecsCarouselWrapper
      isPercentageEnabled={isPercentageEnabled}
      recommendationsConfig={recommendationsConfig}
      reportCertonaLoad={reportCertonaLoad}
      selectedCustomerChoiceId={selectedCustomerChoiceId}
      layoutType={layoutType}
      isMvg
      isMultiVariantProduct={isMultiVariantProduct}
    />
  );
};
