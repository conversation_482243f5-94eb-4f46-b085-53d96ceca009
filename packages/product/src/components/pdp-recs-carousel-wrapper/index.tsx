'use client';

import { ProductRecommendationsProviderData, useProductRecommendations } from '@ecom-next/sitewide/product-recs-provider';
import { useEffect, useState, useContext } from 'react';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { ModalContext } from '@ecom-next/core/components/fabric/modal';
import ProductCarousel from '../product-recs-carousel';
import type { FeaturesConfig } from '../../pages/getFeatureConfig';
import { ProductRecsGrid } from '../product-recs-carousel/ProductRecsGrid';
import { recommendationsUrlBuilder } from './recommendations-url-builder';

export const PDPRecsCarouselWrapper = ({
  isPercentageEnabled,
  recommendationsConfig,
  reportCertonaLoad = false,
  selectedCustomerChoiceId,
  layoutType = 'carousel',
  pageType = 'PRODUCT',
  isMvg = false,
  isMultiVariantProduct = false,
}: {
  isMultiVariantProduct?: boolean;
  isMvg?: boolean;
  isPercentageEnabled: boolean;
  layoutType?: 'grid' | 'carousel';
  pageType?: 'PRODUCT' | 'ADDTOCART' | 'PRODUCTLPO';
  recommendationsConfig: FeaturesConfig['recommendationsConfig'];
  reportCertonaLoad?: boolean;
  selectedCustomerChoiceId: string;
}) => {
  const [isCertonaLoadTracked, setIsCertonaLoadTracked] = useState(false);
  const { brand } = usePageContext();
  const { getAiRecommendations, certonaRecommendationsData } = useProductRecommendations();
  const { requestFocusReload } = useContext(ModalContext);
  const ALLOWED_PAGE_TYPES = ['PRODUCT'];
  const RECS_IN_MODALS = ['ADDTOCART'];

  const {
    aiRecommendations: { enabled: isAiRecommendationsFeatureEnabled, model = '', recommendedItemSize, recommendedItemSizeATB },
    isCertonaRecommendationsEnabled,
    isAthletaHUIChanges,
    pdpAtbAiRecommendationsEnabled,
  } = recommendationsConfig;

  const isAiRecommendationsEnabled =
    (isAiRecommendationsFeatureEnabled && ALLOWED_PAGE_TYPES.includes(pageType)) || (pdpAtbAiRecommendationsEnabled && RECS_IN_MODALS.includes(pageType));
  const useReporter = isMvg
    ? require('../../providers/pdp-reporter-provider/v3/mvg-reporter-provider').useMvgPDPReporter
    : require('../../providers/pdp-reporter-provider').usePDPReporter;

  const pdpReporter = useReporter();

  const aiRecommendationsConfig = {
    anchor_type: 'customer_choice',
    anchor_ids: selectedCustomerChoiceId,
    page_type: pageType,
    segment_id: model ?? '',
    recommended_item_size: pageType === 'ADDTOCART' ? recommendedItemSizeATB : recommendedItemSize,
  };
  const [recsData, setRecsData] = useState<ProductRecommendationsProviderData | null>(null);

  const requestFocus = () => {
    if (RECS_IN_MODALS.includes(pageType)) {
      requestFocusReload?.();
    }
  };
  useEffect(() => {
    const fetchRecommendations = async () => {
      const data = await getAiRecommendations(aiRecommendationsConfig);
      setRecsData(data);
      requestFocus();
    };

    if (isAiRecommendationsEnabled) {
      fetchRecommendations();
    }
  }, [isAiRecommendationsEnabled]);

  useEffect(() => {
    /**
     * this is to ensure that the certona load event is reported only once for the main PDP
     * and not for the add to cart
     */
    if (reportCertonaLoad && !isCertonaLoadTracked) {
      pdpReporter?.reportCertonaRecommendationsLoad();
      setIsCertonaLoadTracked(true);
    }
  }, [certonaRecommendationsData, isCertonaLoadTracked]);

  useEffect(() => {
    if (isCertonaRecommendationsEnabled && !isAiRecommendationsEnabled) {
      setRecsData(certonaRecommendationsData);
      requestFocus();
    }
  }, [certonaRecommendationsData]);

  const adaptedRecsDataWithTrackingUrls = recsData?.recommendationsData.schemes?.map((scheme, index) => ({
    ...scheme,
    items: scheme.items.map(item => ({
      ...item,
      DetailURL: recommendationsUrlBuilder({
        componentOrigin: pageType,
        brand,
        detailUrl: item.DetailURL,
        scheme: scheme.scheme,
        index,
      }),
    })),
  }));

  const shouldShowGrid = layoutType === 'grid';

  return (
    <div className={`recs-carousel-wrapper ${isAthletaHUIChanges ? 'recs-carousel-wrapper-athleta-hui-changes' : ''}`} data-test-id='recs-carousel-wrapper'>
      {shouldShowGrid ? (
        <ProductRecsGrid
          schemes={adaptedRecsDataWithTrackingUrls?.filter(scheme => scheme.items.length > 0) || []}
          recommendationsConfig={recommendationsConfig}
          isPercentageEnabled={isPercentageEnabled}
          id='product-recs-grid'
          isMultiVariantProduct={isMultiVariantProduct}
        />
      ) : (
        adaptedRecsDataWithTrackingUrls
          ?.filter(scheme => scheme.items.length > 0)
          .map((scheme, i) => (
            <ProductCarousel
              isMvg={isMvg}
              index={i}
              key={scheme.explanation}
              headline={scheme.explanation}
              products={scheme.items}
              recommendationsConfig={recommendationsConfig}
              pageType={pageType}
              id={`${scheme.explanation}-${i}`}
              isAthletaHUIChanges={isAthletaHUIChanges}
              showPercentageOff={isPercentageEnabled}
              isMultiVariantProduct={isMultiVariantProduct}
            />
          ))
      )}
    </div>
  );
};
