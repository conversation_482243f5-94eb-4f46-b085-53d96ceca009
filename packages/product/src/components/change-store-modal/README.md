# @product-page/change-store-modal

# ./change-store-modal/ChangeStoreModalRedesign.tsx

This is the most recent version--created July 2025--of a React component to allow customers to search for nearby stores using a zip code, and select a store as preference. The update includes replacing React Stitch with components from the Fabric Design System (FDS).
To view the new version locally, add the `&fffs=["pdp-hui-changestoremodal"]` query parameter to the URL.

# ./change-store-modal/index.tsx

This is the legacy version that preceded the `ChangeStoreModalRedesign.tsx` rewrite. This version uses React Stitch.

```sh
$ npm install @product-page/change-store-modal
```

<br/>
<br/>
## Usage
<br/>
### Example 
```js
import { ChangeStoreModal } from '@product-page/change-store-modal';

<ChangeStoreModal
container=".package-modal"
id="change-store-modal"
onClose={() => {
console.log('close');
}}
onDone={() => {
console.log('done');
}}
onOpen={() => {
console.log('open');
}}
openButtonClassName="your-custom-class"
PASConfig={{
    url: 'URL',
  }}
selectedStoreId="180"
sku="6605240022402"
translation={{
    'changeStoreModal.title': 'changeStoreModal.doneButton',
  }}
zipCode="92123"
useStorage
curbsideEnabled={true}

> { /_ children: SOME REACT NODE _/ }
> </ChangeStoreModal>

````
### Optional children content
You can add content inside the Change Store Modal. For that, add your component as children in the space specified on the example above.

### Open modal from outside component scope
You can actually open the modal from outside component scope by providing a reference to the ChangeStoreModal component.

Note: You need to use the default import version of the component for this to work

Example:

```js
import ChangeStoreModal from '@product-page/change-store-modal';
import { useRef } from 'react';
import type { ChangeStoreModalRefType } from '../types';

const changeStoreModalRef = useRef<ChangeStoreModalRefType>();

<React.Fragment>
  <button onClick={() => {
    console.log('open from ref');
    changeStoreModalRef.current.openModal();
  }}
  type="button">
  <ChangeStoreModal
    container=".package-modal"
    LASConfig={{
      query: {
        size: 20,
        radius: 150,
      }
    }}
    onClose={() => {
      console.log('close');
    }}
    onDone={() => {
      console.log('done');
    }}
    onOpen={() => {
      console.log('open');
    }}
    openButtonClassName="your-custom-class"
    selectedStoreId="180"
    sku="6605240022402"
    zipCode="92123"
    curbsideEnabled={true}
  >
    { /* children: SOME REACT NODE */ }
  </ChangeStoreModal>
</React.Fragment>
````

### Dependencies

`ChangeStoreModal` is built for usage in MFE applications. In an effort to keep this package as small as possible, we have specified all `core-ui` and major dependencies as `peerDependencies`. You will be required to install them as needed. To view the list of required dependencies run:
<br/>

```sh
$ npm info @product-page/change-store-modal peerDependencies
```

For the React providers listed in `peerDependencies` it will be required that these are included in your main application entry component, above the `ChangeStoreModal` component.

- [`AppStateProvider`][app-state-provider]
- [`BreakpointProvider`][breakpoint-provider]
- [`LocalizationProvider`][localization-provider]
- [`StitchStyleProvider`][stitch-style-provider]

```html
<AppStateProvider {...}>
  <BreakpointProvider {...}>
    <LocalizationProvider {...}>
      <StitchStyleProvider {...}>
        <YourAppThatUsesChangeStoreModal />
      </StitchStyleProvider>
    </LocalizationProvider>
  </BreakpointProvider>
</AppStateProvider>
```

### Localization

This component requires that the following keys be present in your localization JSON files for each required language:

```
"changeStoreModal.title"
"changeStoreModal.oneStoreFound"
"changeStoreModal.storesFound"
"changeStoreModal.inStockFilter"
"changeStoreModal.inStockFilter.on"
"changeStoreModal.inStockFilter.off"
"changeStoreModal.itemNotFoundMessage"
"changeStoreModal.changeStoreUnavailable"
"changeStoreModal.zipCodeNotFound"
"changeStoreModal.closeModal"
"changeStoreModal.doneButton"
"changeStoreModal.toggleButtonStoreDetails"
"changeStoreModal.bothAvailable"
"changeStoreModal.curbsideAvailable"
"changeStoreModal.inStoreAvailable"
"changeStoreModal.storeDistance"
"changeStoreModal.findAStore"
"changeStoreModal.selectedStore"
"changeStoreModal.noStoreFound"
"changeStoreModal.outOfStock"
"changeStoreModal.lowStock"
"changeStoreModal.inStock"
"changeStoreModal.openModalButton"
"changeStoreModal.lowStockTooltipForPickup"
"changeStoreModal.lowStockOpenTooltipAriaLabel"
"changeStoreModal.lowStockCloseTooltipAriaLabel"
```

PDP versions of these keys for English and French can be found at https://github.gapinc.com/ecomfrontend/product-page/tree/main/state-builder/static-data/i18n

### Custom CSS

- The `openButtonClassName` property lets you assign a custom CSS class to the open modal unstyled button (modal closed, initial state).

### Notes

> **Style Deduplication:** This package relies on `@core-ui/react-stitch` (`emotion`) for styling. If you're app uses the basic server rendering that works automatically with `emotion`, you may need to add a [`CacheProvider`][cache-provider] to deduplicate injected style tags.

> **Services:** apikeys are no longer needed. Instead, target environment `env` and `contentType` must be exposed in app State To access environment-specific urls.

> **Polyfills**: Make sure you are using the latest `@mfe/browse-polyfill-loader`.

> **Properties**:
> [curbsideEnabled] - must be true by default to not impact other teams, but now we can hide PickupTypeFilter if we don't have curbside enable.

### Local Development from this MFE

To access the quick-add component in the browser, run the app with the standard build process and navigate to [localhost:3000/package/changestoremodal][local-dev-route].

[pss]: https://github.gapinc.com/onlinebrowse/productstyle-service
[sbs]: https://github.gapinc.com/ecom-buy/ecombuy/blob/master/ShoppingBagService/swagger.yaml
[app-state-provider]: https://github.gapinc.com/ecomfrontend/core-ui/tree/main/packages/app-state-provider
[localization-provider]: https://github.gapinc.com/ecomfrontend/core-ui/tree/main/packages/localization-provider
[personalization-provider]: https://github.gapinc.com/ecomfrontend/core-ui/tree/main/packages/personalization-provider
[stitch-style-provider]: https://github.gapinc.com/ecomfrontend/core-ui/tree/main/packages/react-stitch
[cache-provider]: https://emotion.sh/docs/ssr#advanced-approach
[local-dev-route]: http://localhost:3000/package/changestoremodal

<!-- pss: https://confluence.gapinc.com/display/EVAL/Product+Style+Service+Urls -->
<!-- atb: https://confluence.gapinc.com/display/EVAL/Add+to+Bag+url+details -->
