export const translations = {
  applyButtonText: 'Apply',
  availabilityInstockText: 'In Stock',
  availabilityLowstockText: 'Low Stock',
  availabilityOutOfStockText: 'Out of Stock',
  changeStoreUnavailableMessageText: 'Change Store is unavailable. Please try again later.',
  closeModalText: 'Close Change Store Modal',
  getStoreDistanceText: (storeDistance: string) => `Store Distance: ${storeDistance}`,
  inStockMessageText: 'In Stock',
  itemNotFoundErrorText: "We're sorry but we can't find this item in stock nearby. Try another size, color, or zip code.",
  modalTitleText: 'Select Store',
  openTodayText: (storeHours: string) => `Open Today: ${storeHours}`,
  pickupAllTypesText: 'All Types',
  pickupCurbsideText: 'Curbside',
  pickupInStoreText: 'In-Store',
  storeDetailsTitleText: 'Store Details',
  storeHoursText: 'Store Hours',
  selectStoreCheckAvailabilityText: 'Select a store to check availability',
  validZipCodeMessageText: 'Please enter a valid zip code.',
  zipCodeNotFoundMessageText: "Zip code can't be found. Try again.",
  zipcodeLabelText: 'Zip Code',
};

export const SKU_ID = '6810370120002';
export const SKU_ID_CANADA = '6930490430002'; // SKU_ID in Canada

export const ZIP_CODE_CHICAGO = '60608';
export const ZIP_CODE_SAN_FRANCISCO = '94102';
export const CANADA_POSTAL_CODE = 'V5C6N5'; // Vancouver, BC. Canada does not use ZIP codes.

export const STORE_ID_CANADA = '3458'; // store with inventory for SKU_ID in Canada
export const STORE_ID_IN_STOCK = '5520';
export const STORE_ID_OUT_OF_STOCK = '1862'; // store with no inventory for SKU_ID
