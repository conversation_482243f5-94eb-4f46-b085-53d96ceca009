import { useCallback, useState } from 'react';
import type { ProductStore } from '@ecom-next/core/components/migration/bopis';
import { ChangeStoreModalProps, SkuAwareChangeStoreModal } from '../ChangeStoreModalRedesign';
import { STORE_ID_IN_STOCK, ZIP_CODE_SAN_FRANCISCO } from './fixtures';

type PropsForTestingPurposes = {
  forceOpen?: boolean;
};
export const ChangeStoreWrapper = (props: ChangeStoreModalProps & PropsForTestingPurposes) => {
  const {
    onOpen: onOpenCall,
    onDone: onDoneCall,
    onClose: onCloseCall,
    sku,
    stores: storesProp = [],
    selectedStoreId: selectedStoreIdProp = STORE_ID_IN_STOCK,
    zipCode = ZIP_CODE_SAN_FRANCISCO,
    forceOpen = false,
    ...rest
  } = props || {};

  const [isOpen, setIsOpen] = useState(forceOpen);
  const [selectedStoreId, setSelectedStoreId] = useState(selectedStoreIdProp);
  const [zipcode, setZipCode] = useState(zipCode);
  const [_, setSelectedStore] = useState<ProductStore | null>(null);
  const [stores, setStores] = useState<ProductStore[]>(storesProp);

  const onOpen = useCallback(() => {
    setIsOpen(true);
    if (onOpenCall) {
      onOpenCall();
    }
  }, [onOpenCall]);

  const onClose = useCallback(() => {
    setIsOpen(false);
    if (onCloseCall) {
      onCloseCall();
    }
  }, [onCloseCall]);

  const onDone = useCallback(
    (...params: Parameters<NonNullable<ChangeStoreModalProps['onDone']>>) => {
      setIsOpen(false);
      const { selectedStore, zipCode: selectedZipCode, stores: loadedStores = [] } = params[0];

      if (selectedStore) {
        setSelectedStoreId(selectedStore.storeId);
        setSelectedStore(selectedStore);
      }

      if (selectedZipCode) {
        setZipCode(selectedZipCode);
      }

      if (loadedStores.length) {
        setStores(loadedStores);
      }

      if (onDoneCall) {
        onDoneCall(params[0]);
      }
    },
    [onDoneCall]
  );

  return (
    <div>
      <button data-testid='change-store-button' onClick={() => setIsOpen(true)}>
        Change Store
      </button>
      <span data-testid='selected-store-id'>{selectedStoreId}</span>
      <span data-testid='selected-zipcode'>{zipcode}</span>
      <span data-testid='stores-count'>{stores.length}</span>

      {isOpen && (
        <SkuAwareChangeStoreModal
          sku={sku}
          zipCode={zipcode}
          selectedStoreId={selectedStoreId}
          stores={stores}
          onDone={onDone}
          onOpen={onOpen}
          onClose={onClose}
          {...rest}
        />
      )}
      <div id='portal-root' />
    </div>
  );
};
