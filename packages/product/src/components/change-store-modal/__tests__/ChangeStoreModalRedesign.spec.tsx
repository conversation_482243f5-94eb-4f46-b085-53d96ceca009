import { fireEvent, renderAsync, act, screen, TestContextProps } from 'test-utils';
import fetchMock from 'fetch-mock-jest';
import { BreakpointProvider } from '@ecom-next/core/components/breakpoint-provider';
import { ChangeStoreModalProps } from '../ChangeStoreModalRedesign';
import stores94102 from './stores-for-94102.json';
import stores60608 from './stores-for-60608.json';
import storesInventory60608 from './stores60608-sku-inventory.json';
import storesInventory94102 from './stores94102-sku-inventory.json';
import storesV5C6N5 from './stores-for-v5c6n5.json';
import storesInventoryV5C6N5 from './store3458-sku-instock.json';
import {
  translations,
  SKU_ID,
  SKU_ID_CANADA,
  ZIP_CODE_CHICAGO,
  ZIP_CODE_SAN_FRANCISCO,
  CANADA_POSTAL_CODE,
  STORE_ID_CANADA,
  STORE_ID_IN_STOCK,
  STORE_ID_OUT_OF_STOCK,
} from './fixtures';
import { ChangeStoreWrapper } from './MockParentComponents';

describe('<ChangeStoreModal />', () => {
  const defaultRenderSpec = () => {
    const changeStoreModal = screen.getByTestId('change-store-modal-content');
    expect(changeStoreModal).toBeInTheDocument();

    const modalHeader = screen.getByText(translations.modalTitleText);
    expect(modalHeader).toBeInTheDocument();

    const prepopulatedZipCodeInput = screen.getByLabelText<HTMLInputElement>('Zip code', { selector: 'input' });
    expect(prepopulatedZipCodeInput.value).toEqual(ZIP_CODE_SAN_FRANCISCO);

    const storesList = screen.getByTestId<HTMLDivElement>('change-store-modal-stores-list');
    expect(storesList).toBeInTheDocument();
    expect(storesList.children.length).toBeGreaterThan(10);

    const selectedStoreInput = storesList.querySelector<HTMLInputElement>(`#store-${STORE_ID_IN_STOCK}`);
    expect(selectedStoreInput).toBeChecked();

    const selectedStore = selectedStoreInput?.nextSibling as HTMLLabelElement;
    const storeWithInventoryAndAddress = selectedStore.querySelector('a');
    expect(storeWithInventoryAndAddress).toHaveTextContent('280 METRO CENTER');

    const storeAddress = storeWithInventoryAndAddress?.nextSibling as HTMLParagraphElement;
    expect(storeAddress).toHaveTextContent('55 Colma Blvd Colma, CA 94014');

    const storeInfo = storeAddress?.nextSibling as HTMLSpanElement;
    expect(storeInfo).toHaveTextContent('Store Distance: 7.47•Open Today: 10:00 am - 9:00 pm');

    const storeInventory = selectedStore.lastChild as HTMLDivElement;
    expect(storeInventory).toHaveTextContent('In Stock');

    const enabledApplyButton = screen.getByTestId('change-store-modal-done-button');
    expect(enabledApplyButton).toBeEnabled();

    return {
      storesList,
      changeStoreModal,
    };
  };

  const defaultCanadaRenderSpec = () => {
    const changeStoreModal = screen.getByTestId('change-store-modal-content');
    expect(changeStoreModal).toBeInTheDocument();

    const modalHeader = screen.getByText(translations.modalTitleText);
    expect(modalHeader).toBeInTheDocument();

    const prepopulatedPostalCode = screen.getByLabelText<HTMLInputElement>('Zip code', { selector: 'input' });
    expect(prepopulatedPostalCode.value).toEqual(CANADA_POSTAL_CODE);

    const storesList = screen.getByTestId<HTMLDivElement>('change-store-modal-stores-list');
    expect(storesList).toBeInTheDocument();
    expect(storesList.children.length).toBeGreaterThan(10);

    const selectedStoreInput = storesList.querySelector<HTMLInputElement>(`#store-${STORE_ID_CANADA}`);
    expect(selectedStoreInput).toBeChecked();

    const selectedStoreWithInventoryAndInfo = selectedStoreInput?.nextSibling as HTMLLabelElement;
    const storeName = selectedStoreWithInventoryAndInfo.querySelector('a');
    expect(storeName).toHaveTextContent('GUILDFORD T/C');

    const storeAddress = storeName?.nextSibling as HTMLParagraphElement;
    expect(storeAddress).toHaveTextContent('10355 152th Street Surrey, BC V3R 7C1');

    const storeInfo = storeAddress?.nextSibling as HTMLSpanElement;
    expect(storeInfo).toHaveTextContent('Store Distance: 11.00•Open Today: 10:00 am - 9:00 pm');

    const storeInventory = selectedStoreWithInventoryAndInfo.lastChild as HTMLDivElement;
    expect(storeInventory).toHaveTextContent('In Stock');

    const applyButton = screen.getByTestId('change-store-modal-done-button');
    expect(applyButton).toBeEnabled();

    return {
      storesList,
      changeStoreModal,
    };
  };

  const allOutOfStockErrorSpec = async () => {
    fetchMock.get(
      `glob:*/commerce/catalog/inventory/v1/sku/store/status?channel=ONL&market=US&brand=ON&storeIds=1862,5520,1528,3607,5024,5955,5556,682,5585,7804&skuIds=${SKU_ID}`,
      500,
      { overwriteRoutes: true }
    );

    const consoleErrorMock = jest.fn();
    jest.spyOn(console, 'error').mockImplementation(consoleErrorMock);

    await renderChangeStoreModal();

    const storesList = screen.getByTestId('change-store-modal-stores-list');
    const badgeList = Array.from(storesList.querySelectorAll('.fds_status-badge span')).map(el => el.textContent || '');

    expect(badgeList.indexOf(translations.inStockMessageText)).toEqual(-1);
  };

  const changeToChicagoZipSpec = async () => {
    jest.useFakeTimers();

    fetchMock.get(
      `glob:*/commerce/locations/stores/geo?zipCode=${ZIP_CODE_CHICAGO}&marketCode=US&brandCode=ON&applicationsEnabled=BOPIS&includes=*`,
      stores60608
    );

    fetchMock.get(
      `glob:*/commerce/catalog/inventory/v1/sku/store/status?channel=ONL&market=US&brand=ON&storeIds=3058,5328,5768,6401,5721,5935,532,3973,183,5192,6179,5730,3070,6190,6471,1021,3120,6700,5838,5733,6537,5702,6574,6368,1651,5571,7198,5681,5267,5894,3321,5511,6393,5304,4665,5504,9289,6778,3421,5778&skuIds=${SKU_ID}`,
      storesInventory60608
    );

    // have zipcode prepopulated
    const zipCodeInput = screen.getByLabelText<HTMLInputElement>('Zip code', { selector: 'input' });

    await act(() => {
      fireEvent.change(zipCodeInput, { target: { value: ZIP_CODE_CHICAGO } });
    });

    // wait for debounce
    await act(() => {
      jest.advanceTimersByTime(700);
    });

    const storesList = screen.getByTestId<HTMLDivElement>('change-store-modal-stores-list');
    expect(storesList).toBeInTheDocument();

    // has Chicago store
    const chicagoStore = storesList.querySelector<HTMLInputElement>('#store-3058');
    expect(chicagoStore).toBeInTheDocument();

    // disables Apply button because selected store no longer exists
    const applyButton = screen.getByTestId('change-store-modal-done-button');
    expect(applyButton).toBeDisabled();

    return { storesList };
  };

  const changeToInvalidZipSpec = async () => {
    jest.useFakeTimers();

    // have zipcode prepopulated
    const zipCodeInput = screen.getByLabelText<HTMLInputElement>('Zip code', { selector: 'input' });

    await act(() => {
      fireEvent.change(zipCodeInput, { target: { value: '1234' } });
    });

    // wait for debounce
    await act(() => {
      jest.advanceTimersByTime(700);
    });

    const errorText = screen.getByText<HTMLElement>('Please enter a valid zip code.');
    expect(errorText).toBeInTheDocument();

    // change to valid zip
    return await changeToChicagoZipSpec();
  };

  const changeToInvalidCanadianPostalCodeSpec = async () => {
    jest.useFakeTimers();

    const zipCodeInput = screen.getByLabelText<HTMLInputElement>('Zip code', { selector: 'input' });

    await act(() => {
      fireEvent.change(zipCodeInput, { target: { value: ZIP_CODE_SAN_FRANCISCO } });
    });

    // wait for debounce
    await act(() => {
      jest.advanceTimersByTime(700);
    });

    const errorText = screen.getByText<HTMLElement>('Please enter a valid zip code.');
    expect(errorText).toBeInTheDocument();
  };

  const changeZipcodeAndPickStoreSpec = async (renderFunc: RenderFunc = renderChangeStoreModal) => {
    await renderFunc();

    defaultRenderSpec();

    const { storesList } = await changeToChicagoZipSpec();

    const chicagoStoreInput = storesList.querySelector<HTMLInputElement>('#store-3058');
    expect(chicagoStoreInput).not.toBeChecked();

    const chicagoStoreLabel = chicagoStoreInput?.nextSibling as HTMLLabelElement;
    expect(chicagoStoreLabel).toBeInTheDocument();

    await act(() => {
      fireEvent.click(chicagoStoreLabel);
    });

    const selectedStore = storesList.querySelector<HTMLInputElement>('#store-3058');
    expect(selectedStore).toBeChecked();

    const applyButton = screen.getByTestId('change-store-modal-done-button');
    expect(applyButton).toBeEnabled();

    return {
      applyButton,
      selectedStore,
    };
  };

  const openModalWithTriggerSpec = async () => {
    const changeStoreButton = screen.getByTestId('change-store-button');
    expect(changeStoreButton).toBeInTheDocument();

    await act(() => {
      fireEvent.click(changeStoreButton);
    });
  };

  const triggerInStockSwitchSpec = async () => {
    const changeStoreModal = screen.getByTestId('change-store-modal-content');

    const instockFilter = changeStoreModal.querySelector('#available-stores-filter-id')?.nextSibling as HTMLLabelElement;

    await act(() => {
      fireEvent.click(instockFilter);
    });
  };

  const switchToInStockFilterSpec = async () => {
    await triggerInStockSwitchSpec();

    const outOfStockNode = screen.queryByText(translations.availabilityOutOfStockText);

    expect(outOfStockNode).not.toBeInTheDocument();
  };

  beforeEach(() => {
    fetchMock.get(
      `glob:*/commerce/locations/stores/geo?zipCode=${ZIP_CODE_SAN_FRANCISCO}&marketCode=US&brandCode=ON&applicationsEnabled=BOPIS&includes=*`,
      stores94102
    );

    fetchMock.get(
      `glob:*/commerce/catalog/inventory/v1/sku/store/status?channel=ONL&market=US&brand=ON&storeIds=1862,5520,1528,3607,5024,5955,5556,682,5585,7804&skuIds=${SKU_ID}`,
      storesInventory94102
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
    fetchMock.mockReset();
    jest.useRealTimers();
  });

  describe('DISPLAY: Variations based on props on the displayed modal result', () => {
    describe('WHEN: viewed on a mobile device', () => {
      test('THEN: this component renders as a drawer', async () => {
        await renderChangeStoreModalForMobile();
        const changeStoreModal = document.querySelector('.fds_modal__container');
        const changeStoreDrawer = document.querySelector('.fds_drawer__container');

        expect(changeStoreModal).not.toBeInTheDocument();
        expect(changeStoreDrawer).toBeInTheDocument();
      });
    });
    describe('WHEN: viewed on a desktop viewport', () => {
      test('THEN: this component renders as a modal', async () => {
        await renderChangeStoreModalForDesktop();
        const changeStoreModal = document.querySelector('.fds_modal__container');
        const changeStoreDrawer = document.querySelector('.fds_drawer__container');

        expect(changeStoreModal).toBeInTheDocument();
        expect(changeStoreDrawer).not.toBeInTheDocument();
      });
    });
    describe('WHEN: the parent component is PLP', () => {
      test('THEN: the store availability toggle should NOT appear', async () => {
        await renderChangeStoreModalForPlp();

        const bopisModalContent = screen.getByTestId('change-store-modal-content');
        const availableStoresFilter = screen.queryByTestId('pdp__in-stock-stores-filter');

        expect(bopisModalContent).toBeInTheDocument();
        expect(availableStoresFilter).not.toBeInTheDocument();
      });
      test('THEN: the out-of-stock badge should NOT appear', async () => {
        await renderChangeStoreModalForPlp();

        const bopisModalContent = screen.getByTestId('change-store-modal-content');
        const outOfStockBadge = document.querySelector('.fds_status-badge');

        expect(bopisModalContent).toBeInTheDocument();
        expect(outOfStockBadge).toBeNull();
      });
    });
    describe('WHEN: the parent component is PDP', () => {
      test('THEN: the store availability toggle should appear', async () => {
        await renderChangeStoreModal();

        const bopisModalContent = screen.getByTestId('change-store-modal-content');
        const availableStoresFilter = screen.getByTestId('pdp__in-stock-stores-filter');

        expect(bopisModalContent).toBeInTheDocument();
        expect(availableStoresFilter).toBeInTheDocument();
      });
      test('THEN: the out-of-stock badge should appear', async () => {
        await renderChangeStoreModal();

        const bopisModalContent = screen.getByTestId('change-store-modal-content');
        const outOfStockBadge = document.querySelector('.fds_status-badge');

        expect(bopisModalContent).toBeInTheDocument();
        expect(outOfStockBadge).toBeInTheDocument();
      });
    });
    describe('WHEN: No `sku` string (stock-keeping unit) prop is passed to this component', () => {
      const [getSkuAvailability, sku] = [undefined, undefined];

      test('THEN: should have all stores be unselected.', async () => {
        await renderChangeStoreModal({ getSkuAvailability, sku });

        const storesList = screen.getByTestId<HTMLDivElement>('change-store-modal-stores-list');
        expect(storesList).toBeInTheDocument();

        const storeInputs = storesList.querySelectorAll<HTMLInputElement>('input[type="checkbox"][checked]');

        expect(storeInputs.length).toEqual(0);
      });
      test('THEN:: the Apply button is disabled', async () => {
        await renderChangeStoreModal({ getSkuAvailability, sku });

        const applyButton = screen.getByTestId('change-store-modal-done-button');

        expect(applyButton).toBeDisabled();
      });
    });
    describe('WHEN: it fails to fetch store data,', () => {
      test('THEN: should show an error message', async () => {
        fetchMock.get(
          `glob:*/commerce/locations/stores/geo?zipCode=${ZIP_CODE_SAN_FRANCISCO}&marketCode=US&brandCode=ON&applicationsEnabled=BOPIS&includes=*`,
          500,
          {
            overwriteRoutes: true,
          }
        );

        await renderChangeStoreModal();

        expect(screen.getByText(translations.changeStoreUnavailableMessageText)).toBeInTheDocument();
      });
    });
    describe('WHEN: `sku` string is passed to this component but indicates that the product is out of stock', () => {
      test('should unselect store if sku is out of stock', async () => {
        await renderChangeStoreModalWithTrigger({
          selectedStoreId: STORE_ID_OUT_OF_STOCK,
        });

        await openModalWithTriggerSpec();

        const storesList = screen.getByTestId<HTMLDivElement>('change-store-modal-stores-list');
        expect(storesList).toBeInTheDocument();

        // no store should be selected
        const storeInputs = storesList.querySelectorAll<HTMLInputElement>('input[type="checkbox"][checked]');

        expect(storeInputs.length).toEqual(0);

        // apply button should be disabled
        const applyButton = screen.getByTestId('change-store-modal-done-button');
        expect(applyButton).toBeDisabled();
      });
    });
    describe('WHEN: Canadian user inputs an American ZIP code instead of a Canadian postal code', () => {
      test('THEN: should return an error', async () => {
        fetchMock.get(
          `glob:*/commerce/locations/stores/geo?zipCode=${CANADA_POSTAL_CODE}&marketCode=CA&brandCode=ON&applicationsEnabled=BOPIS&includes=*`,
          storesV5C6N5
        );

        fetchMock.get(
          `glob:*/commerce/catalog/inventory/v1/sku/store/status?channel=ONL&market=CA&brand=ON&storeIds=3461,4218,8377,3908,8374,3462,8372,3458,7187,1201,3777,3979,6369,9618,3326,9501,3447,4224,3457,1478,6202,3382,2664,4213,3492,6793,3263,3463,5128,1076,846,1101,4222,2204,8670,3308,3312,3746,3311,3313&skuIds=${SKU_ID_CANADA}`,
          storesInventoryV5C6N5
        );

        await renderChangeStoreModal(
          { sku: SKU_ID_CANADA, zipCode: CANADA_POSTAL_CODE, selectedStoreId: STORE_ID_CANADA },
          {
            appState: {
              brand: 'on',
              market: 'ca',
              locale: 'en_CA',
              isDesktop: true,
            },
          }
        );

        defaultCanadaRenderSpec();

        await changeToInvalidCanadianPostalCodeSpec();
      });
    });
    describe('WHEN: the component fails to fetch inventory data,', () => {
      test('THEN: should show out-of-stock stores.', async () => {
        await allOutOfStockErrorSpec();
      });
    });
    describe('WHEN: the in-stock filter is enabled', () => {
      test('THEN: should show the error indicating that the item is not in stock nearby.', async () => {
        await allOutOfStockErrorSpec();

        await triggerInStockSwitchSpec();

        expect(screen.getByText(translations.itemNotFoundErrorText)).toBeInTheDocument();
      });
    });
    describe('WHEN: the filter is set to in-stock stores only', () => {
      test('THEN: the `Out of Stock` label should not be displayed.', async () => {
        await renderChangeStoreModal();

        defaultRenderSpec();

        await switchToInStockFilterSpec();
      });
    });
    describe('INTERACTIONS: The user journey from opening, selecting stores, to closing the modal.', () => {
      describe('WHEN: user opens the modal', () => {
        test('THEN: should call onOpen callback correctly', async () => {
          await renderChangeStoreModal({ onOpen: onOpenMock });

          expect(onOpenMock).toHaveBeenCalledTimes(1);
        });
      });
      describe('GIVEN: the test inviting the user to select a store should appear,', () => {
        describe('WHEN: user clicks the trigger', () => {
          test('THEN: should open the modal and show select to check availability message', async () => {
            await renderChangeStoreModalWithTrigger({
              showSelectStoreAvailabilityText: true,
            });

            await openModalWithTriggerSpec();

            defaultRenderSpec();

            const selectStoreAvailabilityText = screen.getByText(translations.selectStoreCheckAvailabilityText);
            expect(selectStoreAvailabilityText).toBeInTheDocument();
          });
        });
      });
      describe('GIVEN: the test inviting the user to select a store should NOT appear,', () => {
        describe('WHEN: user clicks trigger', () => {
          test('THEN: should open the modal but the check availability message should not display.', async () => {
            await renderChangeStoreModalWithTrigger();

            await openModalWithTriggerSpec();

            defaultRenderSpec();

            const selectStoreAvailabilityText = screen.queryByText(translations.selectStoreCheckAvailabilityText);
            expect(selectStoreAvailabilityText).not.toBeInTheDocument();
          });
        });
      });
      describe('GIVEN: the modal loads with a San Francisco ZIP code,', () => {
        describe('WHEN: user changes ZIP code to Chicago,', () => {
          test('THEN: should load Chicago stores.', async () => {
            await renderChangeStoreModal();

            defaultRenderSpec();

            await changeToChicagoZipSpec();
          });
        });
      });
      describe('WHEN: user enters an invalid ZIP code,', () => {
        test('THEN: should show an error message', async () => {
          await renderChangeStoreModal();

          defaultRenderSpec();

          await changeToInvalidZipSpec();
        });
      });
      describe('WHEN: the user clicks a store link (the blue underlined store name)', () => {
        test('THEN: should see store details', async () => {
          const { selectedStore } = await changeZipcodeAndPickStoreSpec();

          const storeLink = (selectedStore?.nextSibling as HTMLLabelElement)?.querySelector('a');
          expect(storeLink).toBeInTheDocument();

          await act(() => {
            fireEvent.click(storeLink!);
          });

          const storeDetailsHeader = screen.getByText(translations.storeDetailsTitleText);
          expect(storeDetailsHeader).toBeInTheDocument();

          const storeDetailName = screen.getByTestId('store-detail-name');
          expect(storeDetailName).toBeInTheDocument();
          expect(storeDetailName).toHaveTextContent('ROOSEVELT COLLECTIONStore Distance: 2.46');

          const storeDetailAddress = screen.getByTestId('store-detail-address');
          expect(storeDetailAddress).toBeInTheDocument();
          expect(storeDetailAddress).toHaveTextContent('150 Roosevelt Road Chicago, IL 60605');

          const storeDetailTodayHours = screen.getByTestId('store-detail-today-hours');
          expect(storeDetailTodayHours).toBeInTheDocument();
          expect(storeDetailTodayHours).toHaveTextContent('Open Today: 10:00 am - 9:00 pm');

          const storeDetailHours = screen.getByTestId('store-detail-hours');
          expect(storeDetailHours).toBeInTheDocument();
          expect(storeDetailHours).toHaveTextContent('Store HoursMonday - Saturday10:00 am - 9:00 pmSunday10:00 am - 7:00 pm');
        });
      });
      describe('GIVEN: the user selects a store', () => {
        describe('WHEN: user then presses the Apply button', () => {
          test('THEN: should change the store in the parent component', async () => {
            const { applyButton } = await changeZipcodeAndPickStoreSpec(async () => {
              await renderChangeStoreModalWithTrigger();

              await openModalWithTriggerSpec();
            });

            expect(screen.getByTestId('selected-zipcode')).toHaveTextContent(ZIP_CODE_SAN_FRANCISCO);
            expect(screen.getByTestId('selected-store-id')).toHaveTextContent(STORE_ID_IN_STOCK);
            expect(screen.getByTestId('stores-count')).toHaveTextContent('0');

            await act(() => {
              fireEvent.click(applyButton);
            });

            expect(screen.queryByTestId('change-store-modal-content')).not.toBeInTheDocument();

            expect(screen.getByTestId('selected-store-id')).toHaveTextContent('3058');

            expect(screen.getByTestId('selected-zipcode')).toHaveTextContent(ZIP_CODE_CHICAGO);

            expect(screen.getByTestId('stores-count')).toHaveTextContent('40');
          });
        });
      });
      describe('WHEN: user closes the modal', () => {
        test('THEN: should call onClose callback.', async () => {
          await renderChangeStoreModal({
            onOpen: onOpenMock,
            onClose: onCloseMock,
          });

          expect(screen.getByTestId('change-store-modal-content')).toBeInTheDocument();
          expect(onOpenMock).toHaveBeenCalledTimes(1);

          const modalCloseButton = screen.getByLabelText(translations.closeModalText);

          await act(() => {
            fireEvent.click(modalCloseButton);
          });

          expect(screen.queryByTestId('change-store-modal-content')).not.toBeInTheDocument();
          expect(onCloseMock).toHaveBeenCalledTimes(1);
        });
      });
    });
  });
});

const [onOpenMock, onCloseMock] = [jest.fn(), jest.fn()];

const renderChangeStoreModalWithTrigger = async (props: Partial<ChangeStoreModalProps> | null = null, options: TestContextProps | null = null) => {
  (window.matchMedia as jest.Mock).mockImplementation(() => {
    return {
      matches: options?.appState?.isDesktop ?? true,
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    };
  });

  const {
    sku = SKU_ID,
    stores = [],
    selectedStoreId: selectedStoreIdProp = STORE_ID_IN_STOCK,
    zipCode = ZIP_CODE_SAN_FRANCISCO,
    translations: translationsProp = translations,
    ...rest
  } = props || {};

  const newProps = {
    ...rest,
    translations: translationsProp,
    sku,
    stores,
    selectedStoreId: selectedStoreIdProp,
    zipCode,
  };

  await renderAsync(
    <ChangeStoreWrapper {...newProps} />,

    {
      appState: {
        brand: 'on',
        market: 'us',
        locale: 'en_US',
      },
      ...(options || {}),
    }
  );
};

const renderChangeStoreModal = async (props: Partial<ChangeStoreModalProps> = {}, options: Partial<TestContextProps> | null = null) => {
  (window.matchMedia as jest.Mock).mockImplementation(() => {
    return {
      matches: options?.appState?.isDesktop ?? true,
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    };
  });

  const { sku = SKU_ID, zipCode = ZIP_CODE_SAN_FRANCISCO, selectedStoreId = STORE_ID_IN_STOCK } = props || {};

  await renderAsync(
    <div>
      <ChangeStoreWrapper translations={translations} sku={sku} zipCode={zipCode} selectedStoreId={selectedStoreId} stores={[]} forceOpen={true} {...props} />
      <div id='portal-root' />
    </div>,

    {
      appState: {
        brand: 'on',
        market: 'us',
        locale: 'en_US',
      },
      ...(options || {}),
    }
  );
};

const renderChangeStoreModalForPlp = async (props: Partial<ChangeStoreModalProps> = {}, options: Partial<TestContextProps> | null = null) => {
  (window.matchMedia as jest.Mock).mockImplementation(() => {
    return {
      matches: options?.appState?.isDesktop ?? true,
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    };
  });

  const { sku = SKU_ID, zipCode = ZIP_CODE_SAN_FRANCISCO, selectedStoreId = STORE_ID_IN_STOCK } = props || {};

  await renderAsync(
    <div>
      <ChangeStoreWrapper
        hidePdpOnlyFeatures={true}
        translations={translations}
        sku={sku}
        zipCode={zipCode}
        selectedStoreId={selectedStoreId}
        stores={[]}
        forceOpen={true}
        {...props}
      />
      <div id='portal-root' />
    </div>,

    {
      appState: {
        brand: 'on',
        market: 'us',
        locale: 'en_US',
      },
      ...(options || {}),
    }
  );
};

// This is based on the renderChangeStoreModalForPlp arrow function above, but with an added BreakpointProvider
const renderChangeStoreModalForDesktop = async (props: Partial<ChangeStoreModalProps> = {}, options: Partial<TestContextProps> | null = null) => {
  (window.matchMedia as jest.Mock).mockImplementation(() => {
    return {
      matches: options?.appState?.isDesktop ?? true,
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    };
  });

  const { sku = SKU_ID, zipCode = ZIP_CODE_SAN_FRANCISCO, selectedStoreId = STORE_ID_IN_STOCK } = props || {};

  await renderAsync(
    <BreakpointProvider initialSizeClass={'Desktop'}>
      <ChangeStoreWrapper
        hidePdpOnlyFeatures={true}
        translations={translations}
        sku={sku}
        zipCode={zipCode}
        selectedStoreId={selectedStoreId}
        stores={[]}
        forceOpen={true}
        {...props}
      />
      <div id='portal-root' />
    </BreakpointProvider>,

    {
      appState: {
        brand: 'on',
        market: 'us',
        locale: 'en_US',
      },
      ...(options || {}),
    }
  );
};

const renderChangeStoreModalForMobile = async (props: Partial<ChangeStoreModalProps> = {}, options: Partial<TestContextProps> | null = null) => {
  const isDesktop = false;
  (window.matchMedia as jest.Mock).mockImplementation(() => {
    return {
      matches: isDesktop,
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    };
  });

  const { sku = SKU_ID, zipCode = ZIP_CODE_SAN_FRANCISCO, selectedStoreId = STORE_ID_IN_STOCK } = props || {};

  await renderAsync(
    <BreakpointProvider initialSizeClass={'Mobile'}>
      <ChangeStoreWrapper
        hidePdpOnlyFeatures={true}
        translations={translations}
        sku={sku}
        zipCode={zipCode}
        selectedStoreId={selectedStoreId}
        stores={[]}
        forceOpen={true}
        {...props}
      />
      <div id='portal-root' />
    </BreakpointProvider>,

    {
      appState: {
        brand: 'on',
        market: 'us',
        locale: 'en_US',
      },
      ...(options || {}),
    }
  );
};

type RenderFunc = typeof renderChangeStoreModal | typeof renderChangeStoreModalWithTrigger;
