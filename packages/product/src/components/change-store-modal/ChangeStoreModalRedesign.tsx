'use client';
import clsx from 'clsx';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import dynamic from 'next/dynamic';
import { ProductStore } from '@ecom-next/core/migration/bopis';
import { useMediaQuery } from '@ecom-next/core/breakpoint-provider';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { TextInput } from '@ecom-next/core/fabric/text-input';
import { Button } from '@ecom-next/core/fabric/button';
import { Link } from '@ecom-next/core/fabric/link';
import { Switch } from '@ecom-next/core/fabric/switch';
import { SelectorTile } from '@ecom-next/core/fabric/selector-tile';
import { StatusBadge } from '@ecom-next/core/fabric/status-badge';
import { Popover } from '@ecom-next/core/fabric/popover';
import { FetchStatusType } from '@ecom-next/core/components/migration/bopis/use-store-fetch';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { InventorySku } from '../../capi-utils';
import { useStoreSkuInventory } from './useStoreSkuInventory';
import { ApiConfigType } from './types';

type DoneCallbackParamsType = {
  selectedStore: ProductStore;
  stores: ProductStore[];
  zipCode: string;
};

export interface ChangeStoreModalProps {
  getSkuAvailability?: (storeIds: string[]) => Promise<InventorySku[]>;

  getStores?: (zipCode: string) => Promise<{
    errorMessage?: string;
    fetchStatus: FetchStatusType;
    stores: ProductStore[];
  }>;
  hidePdpOnlyFeatures?: boolean;
  onClose?: () => void;
  onDone?: (params: DoneCallbackParamsType) => void;
  onOpen?: () => void;
  portalRoot?: string;
  selectedStoreId: string;

  showSelectStoreAvailabilityText?: boolean;
  sku?: string;

  stores: ProductStore[];

  translations: {
    applyButtonText: string;
    availabilityInstockText: string;
    availabilityLowstockText: string;
    availabilityOutOfStockText: string;
    changeStoreUnavailableMessageText: string;
    closeModalText: string;
    getStoreDistanceText: (storeDistance: string) => string;
    inStockMessageText: string;
    itemNotFoundErrorText: string;
    modalTitleText: string;
    openTodayText: (todayHoursText: string) => string;
    pickupAllTypesText: string;
    pickupCurbsideText: string;
    pickupInStoreText: string;
    selectStoreCheckAvailabilityText: string;
    storeDetailsTitleText: string;
    storeHoursText: string;
    validZipCodeMessageText: string;
    zipCodeNotFoundMessageText: string;
    zipcodeLabelText: string;
  };
  zipCode: string;
}

const Modal = dynamic(() => import('@ecom-next/core/fabric/modal').then(mod => mod.Modal), {
  ssr: false,
});

const Drawer = dynamic(() => import('@ecom-next/core/fabric/drawer').then(mod => mod.Drawer), {
  ssr: false,
});

interface BadgeProps {
  badgeColor: string;
  badgeText: string;
  showInfoIcon?: boolean;
}

const getSentenceCaseText = (text: string) => {
  return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
};

function BadgePopover(props: BadgeProps) {
  const { badgeText, badgeColor, showInfoIcon = false } = props;
  const iconRef = useRef(null);
  const [isOpen, setIsOpen] = useState(false);
  const { localize } = useLocalize();
  const lowInventoryWarning = localize('pdp.fulfillmentMethod.lowStockTooltipForPickup');

  return (
    <>
      <StatusBadge
        badgeText={badgeText}
        badgeColor={badgeColor}
        showInfoIcon={showInfoIcon}
        onInfoIconClick={() => setIsOpen(!isOpen)}
        ref={iconRef}
        className='pdp_csm_status-badge'
      />
      {showInfoIcon && (
        <Popover anchorRef={iconRef} isOpen={isOpen} placement='top' onClose={() => setIsOpen(false)}>
          <span>{lowInventoryWarning}</span>
        </Popover>
      )}
    </>
  );
}

const filterCuberside = (stores: ProductStore[]) => Array.isArray(stores) && stores.filter(store => store?.activeFeatureToggles?.curbsidePickup);
const filterInStore = (stores: ProductStore[]) => Array.isArray(stores) && stores.filter(store => store?.activeFeatureToggles?.inStorePickup);

type StoreFetchResult = [ProductStore[], FetchStatusType];

const dayToId = {
  Sun: 0,
  Mon: 1,
  Tue: 2,
  Wed: 3,
  Thu: 4,
  Fri: 5,
  Sat: 6,
} as const;

type DayToId = typeof dayToId;

const idToDay = Object.fromEntries(Object.entries(dayToId).map(([day, id]) => [id, day]));

const fulldayToId = {
  Sun: 'Sunday',
  Mon: 'Monday',
  Tue: 'Tuesday',
  Wed: 'Wednesday',
  Thu: 'Thursday',
  Fri: 'Friday',
  Sat: 'Saturday',
} as const;

type FulldayToId = typeof fulldayToId;

const badgeColorClassName = {
  IN_STOCK: 'bg-color-icon-success',
  LOW_INVENTORY: 'bg-color-icon-warning',
  OUT_OF_STOCK: 'bg-color-gray-4',
  NOT_AVAILABLE: 'bg-color-gray-4',
};

function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const id = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(id);
    };
  }, [value, delay]);

  return debouncedValue;
}

const canadaPostalCodeRegex = /^([A-CEGHJ-NPR-TVXYa-ceghj-npr-tvxy]\d[A-CEGHJ-NPR-Za-ceghj-npr-z]) (\d[A-CEGHJ-NPR-Za-ceghj-npr-z]\d)?$/;
const usZipRegex = '^[0-9]{5}?$';
const isValidZipCode = (zipCode: string, market: string): boolean => {
  const regex = market === 'ca' ? canadaPostalCodeRegex : usZipRegex;
  return zipCode.match(regex) !== null;
};

export interface SkuAwareChangeStoreModalProps extends ChangeStoreModalProps {
  LASConfig?: ApiConfigType;
  clientId?: string;
  useStorage?: boolean;
}

export function SkuAwareChangeStoreModal(props: SkuAwareChangeStoreModalProps) {
  const { sku = '', useStorage = false, LASConfig, clientId, ...rest } = props;
  const { getSkuAvailability, getStores } = useStoreSkuInventory({
    sku,
    useStorage,
    LASConfig,
    clientId,
  });
  const newProps = {
    sku,
    getSkuAvailability,
    getStores,
    ...rest,
  };
  return <ChangeStoreModal {...newProps} />;
}

// eslint-disable-next-line react/display-name
export default function ChangeStoreModal(props: ChangeStoreModalProps) {
  const {
    getSkuAvailability,
    getStores,
    onClose = () => {},
    onDone = () => {},
    onOpen = () => {},
    selectedStoreId,
    translations,
    hidePdpOnlyFeatures = false,
    zipCode,
    stores: bopisStores = [],

    sku,
    portalRoot = '#portal-root',

    showSelectStoreAvailabilityText = false,
  } = props;

  const [storeDetail, setStoreDetail] = useState(null as ProductStore | null);
  const { market } = usePageContext();

  const storeDetailsLinkRef = useRef<HTMLAnchorElement | null>(null);

  // close store details panel
  const backHandler = useCallback(() => {
    setStoreDetail(null);
  }, [setStoreDetail]);

  // restore focus when closing store details panel
  useEffect(() => {
    if (storeDetail === null && storeDetailsLinkRef.current) {
      storeDetailsLinkRef.current.focus();
    }
  }, [storeDetail, storeDetailsLinkRef.current]);

  // constants
  const LAS_ZIP_CODE_ERROR_MESSAGE = 'zipCode could not be found';

  // states
  const [postalCodeError, setPostalCodeError] = useState('');
  const [postalCode, setPostalCode] = useState<string>(zipCode);
  const [outOfStockFiltered, setOutOfStockFilter] = useState<boolean>(false);

  const {
    applyButtonText,
    availabilityInstockText,
    availabilityLowstockText,
    availabilityOutOfStockText,
    closeModalText,
    inStockMessageText,
    modalTitleText,
    changeStoreUnavailableMessageText,
    getStoreDistanceText,
    openTodayText,
    pickupAllTypesText,
    pickupCurbsideText,
    pickupInStoreText,
    storeDetailsTitleText,
    storeHoursText,
    validZipCodeMessageText,
    zipCodeNotFoundMessageText,
    zipcodeLabelText,
    selectStoreCheckAvailabilityText,
  } = translations;

  const availabilityText = {
    IN_STOCK: availabilityInstockText,
    LOW_STOCK: availabilityLowstockText,
    LOW_INVENTORY: availabilityLowstockText,
    OUT_OF_STOCK: availabilityOutOfStockText,
    NOT_AVAILABLE: availabilityOutOfStockText,
  };

  const pickupFilterOptions = [pickupAllTypesText, pickupCurbsideText, pickupInStoreText];

  const [pickupSelected, _] = useState<string>(pickupFilterOptions[0]);
  const [stores, setStores] = useState<ProductStore[]>(bopisStores);

  const [selectedStore, setSelectedStore] = useState<ProductStore | undefined>(
    stores?.length > 0 && selectedStoreId ? stores.find(store => store.storeId === selectedStoreId) : undefined
  );
  const [storesAvailability, setStoresAvailability] = useState<Record<string, InventorySku>>({});
  const [apiErrorText, setApiErrorText] = useState('');
  const [fetchStatus, setFetchStatus] = useState<FetchStatusType | 'idle'>('idle');

  const prevZipRef = useRef(postalCode);
  const debouncedPostalCode = useDebounce(postalCode, 500);

  const filterStores = useMemo<ProductStore[]>(() => {
    const outOfStockStores = outOfStockFiltered
      ? stores.filter((store: ProductStore) => {
          const storeData = storesAvailability[store.storeId];

          return storeData && storeData.availability !== 'OUT_OF_STOCK';
        })
      : stores;

    const filterByPickupOption = {
      [pickupFilterOptions[0]]: () => outOfStockStores,
      [pickupFilterOptions[1]]: filterCuberside,
      [pickupFilterOptions[2]]: filterInStore,
    };
    const filteredStores = filterByPickupOption[pickupSelected](outOfStockStores);
    // @ts-ignore
    return filteredStores?.slice(0, 10) || [];
  }, [outOfStockFiltered, pickupSelected, storesAvailability, stores]);

  const hadFetched = fetchStatus === 'complete' && postalCode !== '';
  const noStoresFoundError = filterStores.length === 0 && hadFetched ? translations.itemNotFoundErrorText : '';
  const errorCopyText = apiErrorText || noStoresFoundError;

  useEffect(() => {
    let ignore = false;

    const fetchStores = async (): Promise<StoreFetchResult> => {
      if (typeof getStores !== 'function') {
        return [[], 'complete'];
      }

      const { stores: lasStores, fetchStatus: lasFetchStatus, errorMessage } = await getStores(debouncedPostalCode);

      if (ignore) {
        return [stores, 'complete'];
      }

      if (lasFetchStatus === 'error') {
        const shouldDisplayZipCodeErrorMsg = errorMessage && errorMessage.includes(LAS_ZIP_CODE_ERROR_MESSAGE);

        if (shouldDisplayZipCodeErrorMsg) {
          setApiErrorText(zipCodeNotFoundMessageText);
        } else {
          setApiErrorText(changeStoreUnavailableMessageText);
        }
      } else {
        setApiErrorText('');
      }

      if (postalCode !== debouncedPostalCode) {
        setPostalCode(postalCode);
      }

      if (selectedStoreId) {
        const foundStore = (lasStores || []).find(store => store.storeId === selectedStoreId);
        setSelectedStore(foundStore);
      }

      setFetchStatus(lasFetchStatus);
      setStores(lasStores);

      return [lasStores, lasFetchStatus];
    };

    const fetchData = async () => {
      if (!debouncedPostalCode) return;

      const shouldFetchStores = debouncedPostalCode && (prevZipRef.current !== debouncedPostalCode || !stores.length);

      setFetchStatus('loading');
      setApiErrorText('');

      prevZipRef.current = debouncedPostalCode;

      const storesFetchPromise = shouldFetchStores ? fetchStores() : Promise.resolve([stores, 'complete'] as StoreFetchResult);

      const [__, dataFetchStatus] = await Promise.all([
        storesFetchPromise,
        storesFetchPromise.then(async result => {
          if (!result) return 'error';

          const [lasStores = [], lasFetchStatus] = result;

          if (lasStores.length && getSkuAvailability) {
            const adaptedAvailability = await getSkuAvailability(lasStores.map(store => store.storeId));
            if (adaptedAvailability && Array.isArray(adaptedAvailability) && adaptedAvailability.length) {
              const storesAvailabilityMap = Object.fromEntries(adaptedAvailability.map(item => [item.storeId, item]));
              setStoresAvailability(storesAvailabilityMap);

              // make sure selected store has inventory
              if (selectedStoreId) {
                const availability = storesAvailabilityMap[selectedStoreId]?.availability;
                if (!availability || availability === 'OUT_OF_STOCK') {
                  setSelectedStore(undefined);
                }
              }
            }
          }

          return lasFetchStatus;
        }),
      ]);

      setFetchStatus(dataFetchStatus);
    };

    fetchData();

    return () => {
      ignore = true;
    };
  }, [debouncedPostalCode]);

  useEffect(() => {
    if (onOpen) {
      onOpen();
    }
  }, []);

  const closeButtonClickHandler = useCallback(() => {
    if (typeof onClose === 'function') {
      onClose();
    }
  }, [selectedStore, stores, postalCode]);

  const doneButtonClickHandler = useCallback(() => {
    if (typeof onDone === 'function' && selectedStore) {
      onDone({
        selectedStore,
        stores,
        zipCode: postalCode,
      });
    }
    closeButtonClickHandler();
  }, [selectedStore, stores, postalCode]);
  const disableApplyButton = (!sku && !hidePdpOnlyFeatures) || selectedStore === undefined || postalCodeError !== '';

  const footer = !storeDetail ? (
    <Button id='done-button' onClick={doneButtonClickHandler} isDisabled={disableApplyButton} data-testid='change-store-modal-done-button'>
      {applyButtonText}
    </Button>
  ) : null;

  const dayId = new Date().getDay();

  const storeDetailData = useMemo(() => {
    if (!storeDetail) return null;

    const { storeHours, storeId } = storeDetail;

    const hourGroups = storeHours
      .map(hours => {
        const delimiterIndex = hours.indexOf(':');
        return [hours.substring(0, delimiterIndex), hours.substring(delimiterIndex + 1).trim()];
      })
      .reduce((result, entry) => {
        const [day, hours] = entry;
        const sameHourDays = result.get(hours);
        if (sameHourDays) {
          sameHourDays.add(`${dayToId[day as keyof DayToId]}`);
        } else {
          const days = new Set<string>();
          const storeHourDayId = `${dayToId[day as keyof DayToId]}`;
          days.add(storeHourDayId);
          result.set(hours, days);
        }
        return result;
      }, new Map<string, Set<string>>());

    const todayHours = storeHours.find(hours => hours.startsWith(idToDay[dayId])) || '';
    const delimiter = todayHours.indexOf(':');
    const todayHoursText = todayHours.substring(delimiter + 1).trim();

    const storeAvailability = storesAvailability[storeId];

    const { inventoryStatusId } = storeAvailability || {};
    const availabilityStatus = availabilityText[inventoryStatusId || 'NOT_AVAILABLE'];

    return {
      hourGroups,
      todayHoursText,
      availabilityStatus,
      inventoryStatusId,
    };
  }, [storeDetail, storesAvailability]);

  const header = (
    <h3 className='text-font-size-1 font-font-weight-base-heavier text-center lowercase first-letter:uppercase'>
      {storeDetail ? storeDetailsTitleText : modalTitleText}
    </h3>
  );

  const isDesktop = useMediaQuery({
    type: 'sm',
    range: '>',
  });

  const changeStoreContent = (
    <>
      <section
        className={clsx('flex h-full w-full flex-col gap-6 [&_*]:box-border', 'p-utk-spacing-l text-font-size-0 sm:p-0', storeDetail ? 'hidden' : '')}
        data-testid='change-store-modal-content'
      >
        <div className='m-[1px] flex flex-initial flex-col gap-6'>
          {showSelectStoreAvailabilityText ? (
            <span data-testid='pdp_select-store-check-availability-text' className='text-start'>
              {selectStoreCheckAvailabilityText}
            </span>
          ) : null}
          <span>
            <TextInput
              aria-label={zipcodeLabelText}
              label={getSentenceCaseText(zipcodeLabelText)}
              defaultValue={postalCode}
              onChange={event => {
                const val = event.target.value.trim();
                if (isValidZipCode(val, market)) {
                  setPostalCode(val);
                  setPostalCodeError('');
                } else {
                  setPostalCodeError(validZipCodeMessageText);
                }
              }}
              hasError={postalCodeError !== ''}
              errorMessage={postalCodeError}
            />
          </span>

          {hidePdpOnlyFeatures ? null : (
            <div className='flex justify-between' data-testid='pdp__in-stock-stores-filter'>
              <span data-testid='pdp__in-stock-stores-label' className='lowercase first-letter:uppercase'>
                {inStockMessageText}
              </span>
              <Switch
                id='available-stores-filter-id'
                name='available-stores-filter-id'
                checked={outOfStockFiltered}
                onChange={() => setOutOfStockFilter(!outOfStockFiltered)}
              />
            </div>
          )}
        </div>

        {!errorCopyText && fetchStatus === 'complete' && postalCode !== '' ? (
          <div
            className='flex flex-1 flex-col gap-4'
            style={{
              overflowY: 'scroll',
              WebkitOverflowScrolling: 'touch',
              maxHeight: 'calc(100vh - 300px)',
            }}
            data-testid='change-store-modal-stores-list'
          >
            {(outOfStockFiltered ? filterStores : stores).map(store => {
              const { storeName, storeAddress, storeId, storeDistance, storeHours } = store;

              const skuAvailability = storesAvailability[storeId];

              const { inventoryStatusId = 'NOT_AVAILABLE' } = skuAvailability || {};
              const availabilityStatus = availabilityText[inventoryStatusId || 'NOT_AVAILABLE'];

              const todayHours = storeHours.find(hours => hours.startsWith(idToDay[dayId])) || '';
              const delimiter = todayHours.indexOf(':');
              const todayHoursText = todayHours.substring(delimiter + 1).trim();
              const isAvailable = (!sku && hidePdpOnlyFeatures) || inventoryStatusId !== 'NOT_AVAILABLE';
              const isChecked = selectedStore?.storeId === storeId && isAvailable;

              return (
                <SelectorTile
                  key={storeId}
                  id={`store-${storeId}`}
                  aria-disabled={!isAvailable}
                  group={`store-selector-${storeId}`}
                  onChange={() => setSelectedStore(store)}
                  className='p-utk-spacing-l gap-utk-spacing-m text-color-type-copy m-[2px] flex flex-col items-start'
                  checked={isChecked}
                  disabled={!isAvailable}
                >
                  <span className='gap-utk-spacing-s flex flex-col items-start'>
                    <Link
                      aria-label={`${storeDetailsTitleText}: ${storeName}`}
                      href={`#store-detail-${storeId}`}
                      className='text-color-type-link font-font-weight-base-heavier !border-b-0 text-left underline'
                      onClick={e => {
                        e.preventDefault();
                        e.stopPropagation();
                        setStoreDetail(store);
                        storeDetailsLinkRef.current = e.currentTarget;
                      }}
                    >
                      {storeName}
                    </Link>
                    <p className='tracking-font-letter-spacing-base text-font-size--1 text-color-type-subtle text-left'>
                      {storeAddress?.addressLine1} {storeAddress?.cityName}, {storeAddress?.stateProvinceCode} {storeAddress?.postalCode}
                    </p>
                    <span className='text-font-size--1 flex gap-2'>
                      <p className='tracking-font-letter-spacing-base'>{storeDistance && getStoreDistanceText(storeDistance)}</p>

                      <p>&bull;</p>

                      <p className='tracking-font-letter-spacing-base'>{openTodayText((todayHoursText || '').toLowerCase())}</p>
                    </span>
                  </span>

                  {hidePdpOnlyFeatures ? null : (
                    <BadgePopover
                      badgeText={availabilityStatus}
                      badgeColor={badgeColorClassName[inventoryStatusId]}
                      showInfoIcon={inventoryStatusId === 'LOW_INVENTORY'}
                    />
                  )}
                </SelectorTile>
              );
            })}
          </div>
        ) : (
          errorCopyText && <span>{errorCopyText}</span>
        )}
      </section>
      {storeDetail && (
        <div
          className='gap-utk-spacing-2xl text-color-type-copy text-font-size-0 p-utk-spacing-l flex w-full flex-col sm:p-0 [&_*]:box-border'
          data-testid='store-detail'
        >
          <span className='gap-utk-spacing-s flex flex-col items-start'>
            <span className='flex w-full justify-between' data-testid='store-detail-name'>
              <h4 className='tracking-font-letter-spacing-base font-font-weight-base-default'>{storeDetail.storeName}</h4>

              <p className='tracking-font-letter-spacing-base'>{storeDetail.storeDistance && getStoreDistanceText(storeDetail.storeDistance)}</p>
            </span>

            <span className='tracking-font-letter-spacing-base text-color-type-subtle text-font-size--1' data-testid='store-detail-address'>
              {storeDetail.storeAddress?.addressLine1} {storeDetail.storeAddress?.cityName}, {storeDetail.storeAddress?.stateProvinceCode}{' '}
              {storeDetail.storeAddress?.postalCode}
            </span>
          </span>

          <span className='gap-utk-spacing-s flex flex-col items-start'>
            <h4 className='tracking-font-letter-spacing-base font-font-weight-base-default' data-testid='store-detail-today-hours'>
              {openTodayText((storeDetailData?.todayHoursText || '').toLowerCase())}
            </h4>

            {hidePdpOnlyFeatures ? null : (
              <BadgePopover
                badgeText={storeDetailData?.availabilityStatus || ''}
                badgeColor={badgeColorClassName[storeDetailData?.inventoryStatusId || 'NOT_AVAILABLE']}
                showInfoIcon={storeDetailData?.inventoryStatusId === 'LOW_INVENTORY'}
              />
            )}
          </span>

          <span className='gap-utk-spacing-s flex flex-col' data-testid='store-detail-hours'>
            <h4 data-testid='pdp_store-hours-string' className='tracking-font-letter-spacing-base font-font-weight-base-default text-start'>
              {storeHoursText}
            </h4>

            {storeDetailData?.hourGroups &&
              Array.from(storeDetailData.hourGroups.entries()).map(([hours, days]) => {
                const daysName = Array.from(days).map(storeDetailDayId => idToDay[storeDetailDayId]);
                const fullDaysName = daysName.map(day => fulldayToId[day as keyof FulldayToId]);
                const first = fullDaysName[0];
                const last = fullDaysName[fullDaysName.length - 1];
                const daysText = first === last ? first : `${first} - ${last}`;

                return (
                  <span key={hours} className='tracking-font-letter-spacing-base text-color-type-subtle text-font-size--1 flex w-full justify-between'>
                    <span>{daysText}</span>
                    <span>{(hours || '').toLowerCase()}</span>
                  </span>
                );
              })}
          </span>
        </div>
      )}
    </>
  );

  return isDesktop ? (
    <Modal
      key={`change-store-modal-${sku || ''}`}
      portalRoot={portalRoot}
      closeButtonAriaLabel={closeModalText}
      isOpen={true}
      hasSkinnyHeader={true}
      callbackFn={closeButtonClickHandler}
      headerContent={header}
      className='font-font-family-base pdp_change-store-modal [&_#modal-header-container]:p-utk-spacing-xl [&_#modal-header-container]:border-color-border-disabled w-full sm:h-4/5 sm:w-1/3 sm:min-w-[400px] [&_#modal-header-container]:border-b [&_#modal-header-container]:border-solid [&_.fds\_modal\_\_content]:h-full [&_.fds\_modal\_\_content]:w-full'
      footerContent={footer}
      showNavigationChevron={storeDetail !== null}
      navigationCallBack={backHandler}
    >
      {changeStoreContent}
    </Modal>
  ) : (
    <Drawer
      key={`change-store-modal-${sku || ''}`}
      portalRoot={portalRoot}
      toggleState='open'
      withCloseIcon
      withHeader
      headerContent={header}
      footerContent={footer}
      callbackFn={closeButtonClickHandler}
      className='font-font-family-base pdp_change-store-modal [&_*]:box-border [&_.fds\_drawer\_\_container]:h-[calc(100%-77px-var(--spacing-600))] [&_.fds\_drawer\_\_container_.fds\_drawer\_\_content]:w-full [&_.fds\_drawer\_\_content]:h-full'
      showNavigationChevron={storeDetail !== null}
      navigationCallBack={backHandler}
    >
      {changeStoreContent}
    </Drawer>
  );
}
