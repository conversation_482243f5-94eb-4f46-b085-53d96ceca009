import { ProductRecommendation } from '@ecom-next/sitewide/aiRecommendationsService/types';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import classNames from 'classnames';
import React from 'react';
import { StarRatings } from '@ecom-next/core/fabric/star-ratings';
import { ProductImage } from '@ecom-next/core/fabric/product-image';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { default as HUIQuickAdd } from '@ecom-next/product/quick-add';
import { formatReviewCount } from '../utils/formatReviewCount';
import { getLocalizedPrice } from '../../../capi-utils/response-adapter/helper/price/get-localized-price';
import { FeaturesConfig } from '../../../pages/getFeatureConfig';
import { QuickAdd as LegacyQuickAddWrapper } from '../quick-add/QuickAdd';

interface ProductCardProps {
  configByViewport?: { itemWidth: number };
  isMultiVariantProduct?: boolean;
  isMvg?: boolean;
  isPercentageEnabled?: boolean;
  layoutType?: 'grid' | 'carousel';
  numberOfSlots?: number;
  product: ProductRecommendation;
  productIndex: number;
  recommendationsConfig: FeaturesConfig['recommendationsConfig'];
  showImage?: boolean;
  showName?: boolean;
}

type ProductImageConfig = {
  CurrentPrice?: string | number;
  DetailURL: string;
  ID: string;
  ImageURL: string;
  ProductName?: string;
  Rating?: string;
  ReviewCount?: string;
  apiDomain?: string;
  imageKey?: string | number;
  isHUIQuickAddEnabled?: boolean;
  isMultiVariantProduct?: boolean;
  isMvg: boolean;
  isQuickAddEnabled: boolean;
  layoutType?: 'grid' | 'carousel';
  productIndex: number;
  showImage: boolean;
  showPercentageOff?: boolean;
};

type ProductCardImageProps = {
  props: ProductImageConfig;
};

const ProductCardImage = ({ props }: ProductCardImageProps) => {
  const {
    apiDomain = '',
    showImage,
    DetailURL,
    ImageURL,
    ProductName = '',
    CurrentPrice = '',
    ID,
    Rating = '',
    ReviewCount = '',
    isQuickAddEnabled,
    layoutType = 'carousel',
    showPercentageOff = false,
    isMvg,
    isHUIQuickAddEnabled = false,
    isMultiVariantProduct = false,
  } = props;

  if (!showImage) return null;

  return (
    <div className={`recs-${layoutType}-product-card--image`}>
      <div className='relative'>
        <a href={DetailURL} tabIndex={-1}>
          <ProductImage className='!p-0' id={String(CurrentPrice)} width='100%' imageUrl={ImageURL} height='auto' imageAltText={ProductName} />
        </a>
      </div>
      {isQuickAddEnabled && (
        <>
          {isHUIQuickAddEnabled ? (
            <HUIQuickAdd apiDomain={apiDomain} pid={ID} showPercentageOff={showPercentageOff} isMultiVariantProduct={isMultiVariantProduct} />
          ) : (
            <LegacyQuickAddWrapper isMvg={isMvg} id={ID} productName={ProductName} rating={Rating} reviewCount={ReviewCount} />
          )}
        </>
      )}
    </div>
  );
};

export const ProductCard = ({
  product,
  numberOfSlots,
  productIndex,
  recommendationsConfig,
  configByViewport = { itemWidth: 300 },
  isPercentageEnabled,
  layoutType = 'carousel',
  showName = true,
  showImage = true,
  isMvg = true,
  isMultiVariantProduct,
}: ProductCardProps) => {
  const { CurrentPrice, DetailURL, ID, ImageURL, isSalePrice, MarketingFlag, OriginalPrice, Percentage, ProductName, Rating, ReviewCount } = product;
  const { locale, appConfig } = usePageContext();
  const { localize } = useLocalize();
  const { localizePrice } = getLocalizedPrice(locale);
  const { isHUIQuickAddEnabled, isQuickAddEnabled, displayRating, showPricesAndMarketingFlag } = recommendationsConfig;
  const apiDomain = appConfig?.brandCodeUrls?.apiHost || '';
  const shouldShowPercentageOff = isSalePrice && isPercentageEnabled && Percentage;
  const percentageOffText = Percentage
    ? localize('pdp.price.percentageOff', {
        value: Percentage,
      })
    : '';

  const currentPriceClass = classNames({
    'recs-product-card--price-sale': isSalePrice,
    'recs-product-card--price-normal': !isSalePrice,
    'w-full': shouldShowPercentageOff,
  });

  const productCardImageConfig: ProductImageConfig = {
    apiDomain,
    CurrentPrice,
    DetailURL,
    ID,
    ImageURL,
    imageKey: ID,
    isHUIQuickAddEnabled,
    isQuickAddEnabled,
    layoutType,
    ProductName,
    productIndex,
    Rating,
    ReviewCount,
    isMvg,
    showPercentageOff: isPercentageEnabled,
    showImage,
    isMultiVariantProduct: isMultiVariantProduct,
  };

  const viewportConfig = layoutType === 'carousel' ? { width: `${configByViewport.itemWidth}px` } : {};

  return (
    <div
      key={ID}
      className={`recs-${layoutType}-product-card`}
      style={viewportConfig}
      role='group'
      aria-label={`${ProductName}. Product ${productIndex + 1} of ${numberOfSlots}`}
    >
      <ProductCardImage props={productCardImageConfig} />

      <div className={`recs-product-card--details`}>
        {showName && (
          <a href={DetailURL}>
            <div className={`recs-product-card--name`}>{ProductName}</div>
          </a>
        )}
        {showPricesAndMarketingFlag && (
          <div className={`recs-product-card--price`}>
            {isSalePrice && <span className={`recs-product-card--price-strike`}>{localizePrice(Number(OriginalPrice))}</span>}
            {shouldShowPercentageOff && <span className={`recs-product-card--price-percentage-off`}>{percentageOffText}</span>}
            <span className={currentPriceClass} style={{ flexBasis: shouldShowPercentageOff ? '100%' : 'auto' }}>
              {localizePrice(Number(CurrentPrice))}
            </span>
          </div>
        )}
        {showPricesAndMarketingFlag && MarketingFlag && <span className='recs-product-card--marketing-flag'>{MarketingFlag}</span>}

        {displayRating && Rating && (
          <a href={`${DetailURL}`}>
            <StarRatings
              ratingValue={Number(Rating)}
              showRatingValue={false}
              postText={formatReviewCount(ReviewCount)}
              showUnderlineForRatingValue={false}
              ratingSize='small'
              id={`recs-product-card-rating-${ID}`}
            />
          </a>
        )}
      </div>
    </div>
  );
};
