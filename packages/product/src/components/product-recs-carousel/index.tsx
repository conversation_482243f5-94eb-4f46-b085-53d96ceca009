'use client';
import { useCallback, useEffect, useState } from 'react';
import useEmblaCarousel from 'embla-carousel-react';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { ProductRecommendation } from '@ecom-next/sitewide/aiRecommendationsService/types';
import { isBRandBRFS } from '../../util/evaluate-brand';
import { FeaturesConfig } from '../../pages/getFeatureConfig';
import CarouselNavigation from './CarouselNavigation';
import { stringToTitleCase } from './utils/stringToTitleCase';
import { RecsCarouselConfigByViewport, recsCarouselConfigByViewport } from './utils/recsCarouselConfigByViewport';
import { ProductCard } from './product-card/ProductCard';

interface ProductCarouselProps {
  headline: string;
  id?: string;
  index?: number;
  isAthletaHUIChanges?: boolean;
  isHUIQuickAddEnabled?: boolean;
  isMultiVariantProduct?: boolean;
  isMvg: boolean;
  isQuickAddEnabled?: boolean;
  pageType?: string;
  products: ProductRecommendation[];
  recommendationsConfig: FeaturesConfig['recommendationsConfig'];
  showPercentageOff?: boolean;
}

export default function ProductCarousel({
  headline,
  products,
  recommendationsConfig,
  showPercentageOff = false,
  pageType = 'PRODUCT',
  id = '',
  index = 0,
  isMvg = false,
  isAthletaHUIChanges = false,
  isMultiVariantProduct = false,
}: ProductCarouselProps) {
  const { isDesktop = false, brand } = usePageContext();
  const [configByViewport, setConfigByViewport] = useState<RecsCarouselConfigByViewport>(recsCarouselConfigByViewport(window?.innerWidth, brand));
  const [batchPoliteMessage, setBatchPoliteMessage] = useState('');
  const slidesToScroll = isDesktop ? configByViewport.slidesToScroll : 1;
  const duration = 25; // default duration used by useEmblaCarousel

  useEffect(() => {
    const handleResize = () => {
      setConfigByViewport(recsCarouselConfigByViewport(window.innerWidth, brand));
    };
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: false,
    align: 'start',
    containScroll: 'trimSnaps',
    slidesToScroll,
    duration,
  });

  const [prevDisabled, setPrevDisabled] = useState(true);
  const [nextDisabled, setNextDisabled] = useState(true);

  const focusOnSlide = useCallback(() => {
    const currentIndex = emblaApi?.selectedScrollSnap() ?? 0;
    const slide = emblaApi?.slideNodes()[currentIndex * slidesToScroll] as HTMLElement;
    const focusable = slide?.querySelector('a[href]') as HTMLElement;
    focusable?.focus();
  }, [emblaApi, slidesToScroll]);

  const scrollPrev = useCallback(() => {
    if (!prevDisabled) {
      emblaApi?.scrollPrev();
      setTimeout(focusOnSlide, duration);
    }
  }, [emblaApi, prevDisabled, focusOnSlide]);

  const scrollNext = useCallback(() => {
    if (!nextDisabled) {
      emblaApi?.scrollNext();
      setTimeout(focusOnSlide, duration);
    }
  }, [emblaApi, nextDisabled, focusOnSlide]);

  useEffect(() => {
    if (!emblaApi) {
      return;
    }
    const updateButtons = () => {
      setPrevDisabled(!emblaApi.canScrollPrev());
      setNextDisabled(!emblaApi.canScrollNext());
    };

    const announceBatchScroll = () => {
      const currentIndex = emblaApi.selectedScrollSnap();
      const newBatch = Math.ceil(currentIndex / slidesToScroll) + 1;
      setBatchPoliteMessage(`Recommended products batch ${newBatch} displayed.`);
    };

    emblaApi.on('select', () => {
      updateButtons();
      announceBatchScroll();
    });

    updateButtons();
  }, [emblaApi, products, slidesToScroll]);

  const shouldCenterTitle = () => brand === 'on' || brand === 'br' || brand === 'brfs' || (!isAthletaHUIChanges && brand === 'at' && isDesktop);

  return (
    <div
      id={id}
      data-test-id={`recs-carousel-${index}`}
      className={`recs-section recs-carousel relative mx-auto bg-white ${isAthletaHUIChanges ? 'recs-carousel-athleta-hui' : ''}`}
      role='region'
      aria-label={stringToTitleCase(headline)}
    >
      <div className='sr-only' aria-live='polite' aria-atomic='true'>
        {batchPoliteMessage}
      </div>
      <div
        className={`recs-title-wrapper relative mb-2 flex w-full ${isBRandBRFS(brand) ? 'items-start' : 'items-end'} ${shouldCenterTitle() ? 'justify-center' : 'justify-start'}`}
      >
        <h2 className={`recs-carousel-title ${isAthletaHUIChanges ? 'recs-title-athleta-hui-changes' : 'recs-title'}`}>
          {brand === 'gap' || brand === 'gapfs' ? headline : stringToTitleCase(headline)}
        </h2>
        {isDesktop && products.length > configByViewport.slidesToScroll && pageType !== 'ADDTOCART' && (
          <CarouselNavigation
            scrollPrev={scrollPrev}
            scrollNext={scrollNext}
            prevDisabled={prevDisabled}
            nextDisabled={nextDisabled}
            aria-label='Product Recommendations Carousel navigation'
          />
        )}
      </div>

      <div ref={emblaRef} className='product-recommendation-wrapper overflow-hidden' role='region' aria-roledescription='Product Recommendations Carousel'>
        <div className='flex gap-3'>
          {products.map((product: ProductRecommendation, productIndex) => {
            return (
              <ProductCard
                key={product.ID}
                product={product}
                productIndex={productIndex}
                recommendationsConfig={recommendationsConfig}
                isPercentageEnabled={showPercentageOff}
                isMvg={isMvg}
                layoutType='carousel'
                isMultiVariantProduct={isMultiVariantProduct}
              />
            );
          })}
        </div>
      </div>
    </div>
  );
}
