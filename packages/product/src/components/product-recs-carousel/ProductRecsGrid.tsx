import { ProductRecommendationScheme, ProductRecommendation } from '@ecom-next/sitewide/aiRecommendationsService/types';
import { FeaturesConfig } from '../../pages/getFeatureConfig';
import { ProductCard } from './product-card/ProductCard';

/**
 * ProductRecsGrid PRIORITY SYSTEM:
 * - Products from the first recs scheme get highest priority (index 0)
 * - Products from second recs scheme get second priority (index 1), etc.
 * - Within each scheme, products maintain their original order
 *
 * ALGORITHM:
 * 1. Flatten all schemes into a single array
 * 2. Add schemePriority to each product based on its scheme index
 * 3. Sort by scheme priority (first scheme products appear first)
 * 4. Take first n products for display
 * 5. Slice the array to fit into a grid of max n slots
 *
 * LAYOUT:
 * MAX_GRID_SLOTS is hardcoded to 15, but can be easily exchanged to a variable on the optimizely flag.
 * - Desktop: 5 columns × 3 rows = 15 slots
 * - Mobile: 2 columns × 7.5 (8) rows = 15 slots
 *
 */

type ProductWithPriority = ProductRecommendation & {
  schemePriority?: number;
};

type ProductRecsGridProps = {
  id?: string;
  isMultiVariantProduct?: boolean;
  isPercentageEnabled: boolean;
  recommendationsConfig: FeaturesConfig['recommendationsConfig'];
  schemes: ProductRecommendationScheme[];
};

export const ProductRecsGrid = ({ schemes, recommendationsConfig, isPercentageEnabled, id = '', isMultiVariantProduct }: ProductRecsGridProps) => {
  const MAX_GRID_SLOTS = 15;

  const allProductsWithPriority = schemes.reduce((accumulatedProducts: ProductWithPriority[], currentScheme, schemeIndex) => {
    const currentSchemeProducts = currentScheme.items.map(product => ({
      ...product,
      schemePriority: schemeIndex,
    }));

    return [...accumulatedProducts, ...currentSchemeProducts];
  }, []);

  const prioritySortedProducts = allProductsWithPriority.sort((productA, productB) => (productA.schemePriority ?? 0) - (productB.schemePriority ?? 0));

  const limitedGridProducts = [...prioritySortedProducts.slice(0, MAX_GRID_SLOTS)];

  const headline = schemes[0]?.explanation?.toLowerCase() || '';

  return (
    <div id={id} className='pdp-recs-grid' role='region' aria-label={headline}>
      <div className='pdp-recs-title-wrapper'>
        <h2 className='pdp-recs-grid-title'>{headline}</h2>
      </div>

      <div className='pdp-recs-grid-container'>
        {limitedGridProducts.map((product, index) => {
          return (
            <div key={product?.ID} className={'pdp-recs-grid-item'}>
              {product ? (
                <ProductCard
                  product={product}
                  productIndex={index}
                  numberOfSlots={MAX_GRID_SLOTS}
                  recommendationsConfig={recommendationsConfig}
                  isPercentageEnabled={isPercentageEnabled}
                  layoutType='grid'
                  isMultiVariantProduct={isMultiVariantProduct}
                />
              ) : (
                ''
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};
