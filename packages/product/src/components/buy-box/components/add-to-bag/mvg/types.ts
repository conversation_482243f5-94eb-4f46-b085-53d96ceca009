import { CID } from '@ecom-next/marketing-ui';
import { FeaturesConfig } from '../../../../../pages/getFeatureConfig';
import { getPageContextData, GetAppConfigType } from '../../../../../pages/getPageState';

export type MvgAddToBagConfigTypes = Pick<ReturnType<typeof getPageContextData>, 'locale' | 'brandName' | 'market'> & {
  abSeg: Record<string, string>;
  brandCodeUrls: Awaited<GetAppConfigType>['appConfig']['brandCodeUrls'];
  cid: CID;
  displayPercentageOffFromCapi: boolean;
  isBopisDisplayable: boolean;
  isHuiLegacyAtbEnabled?: boolean;
  isHuiRampBlockersFix?: boolean;
  isMobile: boolean;
  isMultiVariantProduct?: boolean;
  isPercentageEnabled: boolean;
  recommendationsConfig: FeaturesConfig['recommendationsConfig'];
  scarcityMessageEnabled: boolean;
  showInsituMarketingContainer: boolean;
};
