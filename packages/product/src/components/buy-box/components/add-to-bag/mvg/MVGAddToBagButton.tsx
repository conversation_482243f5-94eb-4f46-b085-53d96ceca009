import React from 'react';
import { ABSegProvider } from '@pdp/src/app/components/abseg-provider';
import { extractCidFromQuery, getPageContextData } from '../../../../../pages/getPageState';
import { cacheableAppConfigDataV2Promise, cacheableEnabledFeaturesV2Promise, cacheableFeaturesConfigMVG } from '../../../../../pages/getReWrittenData';
import { MVGAddToBagButtonClient } from './MVGAddToBagButtonClient';
import { MvgAddToBagConfigTypes } from './types';

type MVGAddToBagButtonProps = {
  isBopisDisplayable: boolean;
  isMultiVariantProduct?: boolean;
  requestParamString: string;
};

export const MVGAddToBagButton = async ({ requestParamString, isBopisDisplayable, isMultiVariantProduct }: MVGAddToBagButtonProps): Promise<JSX.Element> => {
  const { brandName, market, locale } = getPageContextData();
  const [
    {
      isPercentageEnabled,
      scarcityMessageEnabled,
      displayPercentageOffFromCapi,
      showInsituMarketingContainer,
      recommendationsConfig,
      isHuiLegacyAtbEnabled,
      isHuiRampBlockersFix,
    },
    {
      appConfig: { brandCodeUrls },
    },
    featureConfigs,
  ] = await Promise.all([
    cacheableFeaturesConfigMVG(requestParamString),
    cacheableAppConfigDataV2Promise(requestParamString),
    cacheableEnabledFeaturesV2Promise(requestParamString),
  ]);

  const cid = extractCidFromQuery(requestParamString);

  const { abSeg } = featureConfigs;

  const addToBagConfig = {
    brandName,
    market,
    locale,
    isPercentageEnabled,
    isBopisDisplayable,
    displayPercentageOffFromCapi,
    showInsituMarketingContainer,
    scarcityMessageEnabled,
    brandCodeUrls,
    recommendationsConfig,
    cid,
    abSeg,
    isMobile: false,
    isHuiLegacyAtbEnabled,
    isHuiRampBlockersFix,
    isMultiVariantProduct,
  };

  return (
    <ABSegProvider abSeg={abSeg} brandName={brandName}>
      <MVGAddToBagButtonClient {...(addToBagConfig as MvgAddToBagConfigTypes)} />
    </ABSegProvider>
  );
};
