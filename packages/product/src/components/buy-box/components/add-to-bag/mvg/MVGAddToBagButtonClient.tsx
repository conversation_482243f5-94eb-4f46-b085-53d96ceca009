'use client';
import { Dropdown } from '@ecom-next/core/components/fabric/drop-down';
import { Button } from '@ecom-next/core/components/fabric/button';
import React, { useEffect, useRef, useState, useMemo, useCallback } from 'react';
import { useShallow } from 'zustand/react/shallow';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { addPageAction } from '@ecom-next/core/components/reporting';
import { useBopisData } from '@pdp/src/app/hooks/use-bopis-data'; // NOSONAR
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import duration from 'dayjs/plugin/duration';
import { Brands } from '@ecom-next/core/react-stitch';
import classNames from 'classnames';
import { useMVGBuyBoxStore } from '../../../../../providers/mvg-buybox-provider';
import { addToBagService } from '../service/addToBagService';
import { AdaptedAddToBagResponse, AddToBagErrorResponse, AddToBagResponse } from '../types';
import { MvgATBConfirmationModal } from '../../atb-confirmation-modal/mvg/MvgATBConfirmationModal';
import { AdaptedSku } from '../../../../../pages/services/capi-aggregation-service';
import { useMvgAddToBagNotificationMessages } from '../../../../buy-box/components/add-to-bag/mvg/hooks/useMvgAddToBagNotificationMessages';
import { MVGBuyBoxStore } from '../../../../../providers/mvg-buybox-provider/MVGBuyBoxContext';
import { useMvgPDPReporter } from '../../../../../providers/pdp-reporter-provider/v3/mvg-reporter-provider';
import { MvgAddToBagProvider } from './mvg-atb-provider/MvgAtbProvider';
import { MvgAddToBagConfigTypes } from './types';

dayjs.extend(utc);
dayjs.extend(duration);

const DEFAULT_SELECTED_QUANTITY = 1;
const DEFAULT_MAX_QUANTITY_ALLOWED = 25;

export type NotificationKind = 'negative' | 'lowstock' | 'apiError';

type ScarcityMessage = {
  bopisActive?: boolean;
  isShippingAvailable: boolean;
  scarcityMessageEnabled: boolean;
};

type HandleAddToBagClickProps = {
  atbOidcUrl: string;
  brandName: MvgAddToBagConfigTypes['brandName'];
  deliveryLocationId: string;
  description: string;
  locale: MvgAddToBagConfigTypes['locale'];
  market: MvgAddToBagConfigTypes['market'];
  quantity: number;
  selectedSku: MVGBuyBoxStore['selectedSku'];
};

const getShouldDisplayScarcityMessage = ({ scarcityMessageEnabled, isShippingAvailable, bopisActive }: ScarcityMessage) => {
  const shipmentSelected = !bopisActive;
  return scarcityMessageEnabled && isShippingAvailable && shipmentSelected;
};

const getIsFulfillmentInStock = ({
  deliveryLocationId,
  selectedSku,
  isValidSizeSelection,
}: {
  deliveryLocationId: MVGBuyBoxStore['deliveryLocationId'];
  isValidSizeSelection: boolean;
  selectedSku: MVGBuyBoxStore['selectedSku'];
}) => {
  return !!(isValidSizeSelection && (deliveryLocationId ? selectedSku?.bopis_in_stock : selectedSku?.isInstock));
};

const handleAddToBagClick = async (props: HandleAddToBagClickProps): Promise<AddToBagResponse> => {
  const { selectedSku, quantity, brandName, locale, market, description, atbOidcUrl, deliveryLocationId } = props;
  const sku = selectedSku?.sku_id ?? '';
  const response = await addToBagService({
    brandName: brandName as Brands,
    deliveryLocationId,
    locale,
    market,
    name: description,
    quantity,
    sku,
    atbOidcUrl,
  });
  return structuredClone(response);
};

const updateBagCount = (quantity = 0): boolean => {
  const bagCountEventDetail = { quantity };
  const bagCountEvent = new CustomEvent('updateBagCount', { detail: bagCountEventDetail });
  return document.dispatchEvent(bagCountEvent);
};

function backOrderDateMessage(isBackOrdered: boolean, estimatedShipTs: string, backOrderText: string, backOrderAriaLabel: string) {
  if (isBackOrdered) {
    return {
      ariaLabelText: `${backOrderAriaLabel} ${estimatedShipTs}`,
      mainText: `${backOrderText} ${estimatedShipTs}`,
    };
  }
  return { ariaLabelText: '', mainText: '' };
}

export const MVGAddToBagButtonClient = (props: MvgAddToBagConfigTypes) => {
  const { brandName, market, locale, brandCodeUrls, scarcityMessageEnabled, isHuiLegacyAtbEnabled, isHuiRampBlockersFix, isMultiVariantProduct } = props;
  const addToBagConfig = props;
  const [isAddingToBag, setIsAddingToBag] = useState(false);
  const [isCautionState, setIsCautionState] = useState(false);
  const [showNotification, setShowNotification] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [actualQuantity, setActualQuantity] = useState(DEFAULT_SELECTED_QUANTITY);
  const [notificationKind, setNotificationKind] = useState<NotificationKind>('negative');
  const [isOpen, setIsOpen] = useState(false);

  const bopisData = useBopisData();
  const { active: bopisActive } = bopisData;

  const { localize } = useLocalize();
  const backOrderText = localize('pdp.buyBox.errorMessage.onBackOrder');
  const backOrderAriaLabel = localize('pdp.buyBox.errorMessage.onBackOrder.ariaLabel');
  const buttonRef = useRef<HTMLButtonElement>(null);
  const reporter = useMvgPDPReporter();

  const {
    maxQuantityAllowed,
    handleAddToBagResponse,
    selectedCustomerChoice,
    selectedSku,
    styles,
    selectedMultiVariantData,
    alert,
    deliveryLocationId,
    description,
    isShippingAvailable,
    atbResponse,
    marketingFlag,
    isDropShip,
    isFullyOOS,
    isFullyQualifiedSize,
  } = useMVGBuyBoxStore(
    useShallow(state => ({
      isFullyQualifiedSize: state.isFullyQualifiedSize,
      isFullyOOS: state.isFullyOOS,
      maxQuantityAllowed: state.selectedStyle?.max_order_quantity ?? DEFAULT_MAX_QUANTITY_ALLOWED,
      selectedSku: state.selectedSku,
      selectedMultiVariantData: state.selectedMultiVariantData,
      selectedCustomerChoice: state.selectedCustomerChoice,
      alert: state.alert,
      deliveryLocationId: state.deliveryLocationId,
      description: state.selectedStyle?.description ?? '',
      isShippingAvailable: state.isShippingAvailable,
      atbResponse: state.atbResponse,
      handleAddToBagResponse: state.handleAddToBagResponse,
      marketingFlag: state.selectedStyle?.marketing_flags?.[0]?.flag_content ?? null,
      isDropShip: state.selectedStyle?.isDropship,
      bopisAvailable: state.bopisAvailable,
      styles: state.styles,
    }))
  );
  const [isDisabled, setDisabled] = useState(isFullyOOS);
  const addToBagLabel = isFullyOOS ? localize('pdp.addToBag.soldOut') : localize('pdp.addToBag.text');

  const quantityOptions = useMemo(() => Array.from({ length: maxQuantityAllowed }, (_, i) => i + 1), [maxQuantityAllowed]);

  const getInventoryStatus = useCallback(
    (sku: AdaptedSku | null, isValidSizeSelection: boolean) => {
      if (getShouldDisplayScarcityMessage({ scarcityMessageEnabled, isShippingAvailable, bopisActive })) {
        if (isValidSizeSelection && sku?.isLowStock) {
          setShowNotification(true);
          setNotificationKind('lowstock');
        }
      }
    },
    [scarcityMessageEnabled, isShippingAvailable, bopisActive]
  );

  useEffect(() => {
    const isValidSizeSelection = isFullyQualifiedSize(selectedMultiVariantData.dimensions);

    if (alert?.mappedFeedback !== 'selectADimension' || isValidSizeSelection) {
      setErrorMessage('');
      setShowNotification(false);
    }

    getInventoryStatus(selectedSku, isValidSizeSelection);
  }, [selectedSku, selectedMultiVariantData, deliveryLocationId, isShippingAvailable, getInventoryStatus]);

  useEffect(() => {
    const isValidSizeSelection = isFullyQualifiedSize(selectedMultiVariantData.dimensions);

    const isFulfillmentInStock = getIsFulfillmentInStock({ deliveryLocationId, selectedSku, isValidSizeSelection });

    setIsCautionState(false);
    setDisabled(false);

    if (!showNotification && (!selectedSku?.sku_id || !isFulfillmentInStock)) {
      setIsCautionState(true);
    }

    if (alert?.kind === 'negative' || (showNotification && (!selectedSku?.sku_id || errorMessage || !isFulfillmentInStock))) {
      setDisabled(true);
    }
  }, [selectedSku, selectedMultiVariantData, deliveryLocationId, showNotification, alert]);

  const handleClick = async () => {
    if (isCautionState) {
      handleCautionState(true);
      return;
    }
    if (isAddingToBag) {
      return;
    }

    setIsAddingToBag(true);

    const atbClickProps: HandleAddToBagClickProps = {
      selectedSku,
      brandName,
      locale,
      market,
      description,
      atbOidcUrl: brandCodeUrls.atbOidcUrl,
      deliveryLocationId,
      quantity: actualQuantity,
    };

    const response = await handleAddToBagClick(atbClickProps);

    setIsAddingToBag(false);

    if (!response || response.status === 'ERROR') {
      const error = (response as AddToBagErrorResponse).errors?.[0]?.moreInfo ?? '';
      const { errorCode, developerMessage } = (response as AddToBagErrorResponse).errors?.[0] || {};

      setErrorMessage(error);
      setShowNotification(true);
      setNotificationKind('apiError');
      buttonRef.current?.focus();

      addPageAction('addToBagActionLeapFrog', {
        addToBagDeveloperMessage: developerMessage,
        addToBagResponseCode: errorCode,
        addToBagResponseMessage: error,
        rawResponse: { ...response },
        skuId: selectedSku?.sku_id,
        isV3: true,
      });
      return;
    }
    updateBagCount((response as AdaptedAddToBagResponse)?.totalBagQuantity || 1);
    handleAddToBagResponse(
      response as AdaptedAddToBagResponse,
      selectedCustomerChoice?.price?.max_discount_percentage ?? 0,
      backOrderDateMessage(selectedSku?.isBackOrdered ?? false, selectedSku?.backOrderDate ?? '', backOrderText, backOrderAriaLabel)
    );

    setIsOpen(true);

    if (response.status === 'SUCCESS') {
      setTimeout(() => {
        reporter?.reportAddToBagClick(!!deliveryLocationId);
      });
    }

    addPageAction('addToBagActionLeapFrog', {
      numberOfItemsAdded: actualQuantity,
      skuId: selectedSku?.sku_id,
      isHuiRedesign: true,
    });
  };

  const handleCautionState = (state: boolean) => {
    setShowNotification(state);
    setNotificationKind('negative');
  };

  const handleQuantityChange = (quantity: number) => {
    setActualQuantity(quantity);
  };

  useMvgAddToBagNotificationMessages({ errorMessage, alertType: notificationKind, showNotification });

  const closeButtonClickHandler = () => {
    setIsOpen(false);
  };

  const atbButtonClass = classNames({
    'pdp_add-to-bag-button': !isFullyOOS,
    'pdp_add-to-bag-button--is-fully-oos': isFullyOOS,
  });

  useEffect(() => {
    if (isOpen) {
      reporter?.reportAtbSuccess({
        atbResponse,
        isDropShip: !!isDropShip,
        vendorId: selectedCustomerChoice?.vendor_details?.vendor_number as string,
        vendorName: selectedCustomerChoice?.vendor_details?.vendor_name as string,
        marketingFlag: selectedCustomerChoice?.marketing_flags?.find(m => m.flag_content)?.flag_content as string,
        colorPaletteSeasonCode: selectedCustomerChoice?.color_palette_season_code as string,
        styleLevelMarketingFlag: marketingFlag as string,
        bopisOptionSelected: !!bopisActive,
        selectedVariantKey: selectedMultiVariantData.variantKey,
        styleId: styles?.[selectedCustomerChoice?.style_id]?.primary_style_id,
      });
    }
  }, [isOpen]);
  return (
    <MvgAddToBagProvider atbConfig={addToBagConfig}>
      <div className='pdp_add-to-bag-container' data-testid='pdp-atb-container'>
        <div className='pdp_add-to-bag'>
          {!isFullyOOS && (
            <Dropdown
              data-testid='pdp-atb-quantity-selector'
              controlId='pdp-quantity-selector'
              options={quantityOptions}
              noLabel
              native={false}
              value={undefined}
              onChange={handleQuantityChange}
              {...(brandName === Brands.Athleta ? { maxHeight: '13.125rem' } : null)}
            />
          )}
          <Button
            className={atbButtonClass}
            data-testid='pdp-atb-btn'
            data-iscaution={isCautionState}
            data-isdisabled={isDisabled}
            data-isloading={isAddingToBag}
            kind='critical'
            onClick={handleClick}
            isLoading={isAddingToBag}
            isDisabled={isDisabled}
            isCaution={isHuiRampBlockersFix ? false : isCautionState}
            cautionMessage={errorMessage}
            ref={buttonRef}
          >
            {addToBagLabel}
          </Button>
        </div>
      </div>
      <MvgATBConfirmationModal
        isOpen={isOpen}
        closeButtonClickHandler={closeButtonClickHandler}
        atbResponse={atbResponse}
        isHuiLegacyAtbEnabled={isHuiLegacyAtbEnabled}
        isMultiVariantProduct={isMultiVariantProduct}
      />
    </MvgAddToBagProvider>
  );
};
