'use client';
import { SelectorSwatch as ColorSwatch } from '@ecom-next/core/fabric/selector-swatch';
import React from 'react';
import classNames from 'classnames';
import { ProductDimension } from '../dimension-group/types';
import { PriceBase as Price } from '../product-price/ProductPrice';
import { ProductColorGroup, ProductColor, ONLINE_AND_BOPIS_INVENTORY } from './types';
import isColorInStock from './collaborators/color-in-stock';

const ColorPickerGroupedComponent = ({
  colorGroup,
  selectedColorId,
  groupKey,
  dimensions,
  isGiftCard,
  isSingleColorGroup,
  mode = ONLINE_AND_BOPIS_INVENTORY,
  handleClickColor,
  handleHoverColor,
  isPercentageEnabled,
  parentComponent,
  isWrapped,
  isStacked,
  isBlock,
  colorSwatchSize,
  isDesktop,
}: {
  colorGroup: ProductColorGroup;
  colorSwatchSize: 'x-large' | 'large' | 'medium' | 'small' | 'x-small';
  dimensions: ProductDimension[];
  groupKey: string;
  handleClickColor: (color: ProductColor) => void;
  handleHoverColor: (color: ProductColor | null) => void;
  isBlock: boolean;
  isDesktop?: boolean;
  isGiftCard?: boolean;
  isPercentageEnabled?: boolean;
  isSingleColorGroup?: boolean;
  isStacked: boolean;
  isWrapped: boolean;
  mode?: number;
  parentComponent: 'buybox' | 'csm';
  selectedColorId: string;
}) => {
  const { colors = [], price } = colorGroup;
  const showPrice = !isGiftCard && !isSingleColorGroup && !isBlock;

  const colorPickerLayoutClass = classNames('pdp-color-picker-group-container__swatches', {
    'pdp-color-picker-block': isBlock,
    'pdp-color-picker-wrapped': isWrapped,
    'pdp-color-picker-stacked': isStacked,
  });

  return (
    <div className='pdp-color-picker-group'>
      {showPrice && <Price price={price} isPercentageEnabled={isPercentageEnabled} priceStyles='color-swatch-price' />}

      <div className={colorPickerLayoutClass}>
        {Array.isArray(colors) &&
          colors.map((color, key) => {
            const isSelected = selectedColorId === color.businessCatalogItemId;
            const id = `pdp-${parentComponent}-color-swatch--${color?.colorName?.split(' ').join('-')}-${groupKey}-${key}`;
            const isAvailable = isColorInStock(color, dimensions, mode);

            return (
              <div
                key={id}
                onMouseEnter={() => isDesktop && handleHoverColor(color)}
                onMouseLeave={() => isDesktop && handleHoverColor(null)}
                id={color.businessCatalogItemId}
                className='pdp-color-picker-swatch-container'
              >
                <ColorSwatch
                  data-testid={`pdp-color-swatch-${isAvailable ? 'instock' : 'outofstock'}`}
                  data-selected={isSelected ? 'true' : 'false'}
                  groupName={`${parentComponent}-color-swatch`}
                  className='color-swatch h-10 w-80'
                  id={id}
                  onChange={() => handleClickColor(color)}
                  color={color.colorName}
                  ariaLabel='custom color-swatch'
                  imageUrl={color.url}
                  isAvailable={isAvailable}
                  checked={isSelected}
                  size={colorSwatchSize}
                  uncontrolled={false}
                />
              </div>
            );
          })}
      </div>
    </div>
  );
};

export const ColorPickerGrouped = React.memo(ColorPickerGroupedComponent);
