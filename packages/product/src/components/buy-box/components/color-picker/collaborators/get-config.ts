import type { FeatureData } from '@ecom-next/sitewide/feature-flags';
import { PageContextProps } from '@ecom-next/utils/server';

export type LayoutType = 'wrapped' | 'stacked' | 'block';

export const LAYOUTS: { [k: string]: LayoutType } = {
  wrapped: 'wrapped',
  stacked: 'stacked',
  block: 'block',
};

export const getLayoutFromFeatures = ({
  enabledFeatures,
  featureVariables,
  brandName,
  market,
}: FeatureData & { brandName: PageContextProps['brand']; market: PageContextProps['market'] }): LayoutType => {
  const layoutFeatureKey = `pdp-color-swatch-layout-${market}-${brandName}`;

  const isEnabled = enabledFeatures[layoutFeatureKey];
  const { default: wrapped, stacked, block } = featureVariables[layoutFeatureKey] || {};

  if (wrapped || !isEnabled) {
    return LAYOUTS.wrapped;
  }

  if (block) {
    return LAYOUTS.block;
  }

  if (stacked) {
    return LAYOUTS.stacked;
  }

  return LAYOUTS.wrapped;
};
