/* eslint-disable jest/no-disabled-tests */
import React from 'react';
import { fireEvent, render, screen, waitFor } from '@ecom-next/core/test/test-helper';
import userEvent from '@testing-library/user-event';
import { MVGColorPickerGrouped } from '../MVGColorPickerGrouped';
import { AdaptedVariantCustomerChoice } from '../../../../../../pages/services/capi-aggregation-service';
import { wrapMvgInTestApp } from '../../../../../../test-utils/appWrapper';

describe('<MVGColorPickerGrouped  />', () => {
  const handleClickColor = jest.fn();
  const handleHoverColor = jest.fn();

  const colorGroup = [
    {
      style_id: '599749',
      inventory_status: 'IN_STOCK',
      price: {
        currency: 'USD',
        max_effective_price: 109,
        max_regular_price: 109,
        min_effective_price: 109,
        min_regular_price: 109,
        min_discount_percentage: 0,
        max_discount_percentage: 0,
        price_types: ['MARKDOWN'],
        isRegularPrice: false,
        isSalePrice: true,
        isPromoPrice: false,
        localized_max_effective_price: '$109.00',
        localized_min_effective_price: '$109.00',
        localized_max_regular_price: '$109.00',
        localized_min_regular_price: '$109.00',
      },
      skus: [
        {
          sku_id: '322614360',
          alternate_ids: {
            online_legacy_sku_id: '9852180420005',
            retail_legacy_sku_id: '9852180420005',
            online_upc_code: '500126406651',
            vendor_upc_code: null,
            retail_upc_code: '500126406651',
          },
          description: 'XL',
          estimated_days_to_ship: null,
          inventory_status: {
            estimated_ship_ts: null,
            status: 'IN_STOCK',
          },
          made_to_order: false,
          price: {
            currency: 'USD',
            effective_price: 109,
            price_type: 'REGULAR',
            price_details: {
              regular_price: 109,
              markdown_price: 109,
              discount_percentage: null,
              discount_saving: null,
              final_sale: false,
            },
            isRegularPrice: true,
            isSalePrice: false,
            isPromoPrice: false,
            localized_effective_price: '$109.00',
            localized_regular_price: '$109.00',
          },
          size_code: '0005',
          size_dimension1: 'XL',
          size_dimension2: null,
          assortment_ids: null,
          isInstock: true,
          isLowStock: false,
          isBackOrdered: false,
          isOutOfStock: false,
        },
        {
          sku_id: '322614359',
          alternate_ids: {
            online_legacy_sku_id: '9852180420004',
            retail_legacy_sku_id: '9852180420004',
            online_upc_code: '500126406644',
            vendor_upc_code: null,
            retail_upc_code: '500126406644',
          },
          description: 'L',
          estimated_days_to_ship: null,
          inventory_status: {
            estimated_ship_ts: null,
            status: 'IN_STOCK',
          },
          made_to_order: false,
          price: {
            currency: 'USD',
            effective_price: 109,
            price_type: 'REGULAR',
            price_details: {
              regular_price: 109,
              markdown_price: 109,
              discount_percentage: null,
              discount_saving: null,
              final_sale: false,
            },
            isRegularPrice: true,
            isSalePrice: false,
            isPromoPrice: false,
            localized_effective_price: '$109.00',
            localized_regular_price: '$109.00',
          },
          size_code: '0004',
          size_dimension1: 'L',
          size_dimension2: null,
          assortment_ids: null,
          isInstock: true,
          isLowStock: false,
          isBackOrdered: false,
          isOutOfStock: false,
        },
      ],
      isInstock: true,
      isLowStock: false,
      isBackOrdered: false,
      isOutOfStock: false,
      description: 'Freya Abstract Navy',
      swatchImage: '/webcontent/0056/809/924/cn56809924.jpg',
      marketing_flags: null,
      universal_customer_choice_id: '000985218004',
      customer_choice_id: '985218042',
      universal_style_id: '000599749',
    },
  ] as unknown as AdaptedVariantCustomerChoice[];

  const price = {
    currency: 'USD',
    max_effective_price: 109,
    max_regular_price: 109,
    min_effective_price: 109,
    min_regular_price: 109,
    min_discount_percentage: 0,
    max_discount_percentage: 0,
    price_types: ['MARKDOWN'],
    isRegularPrice: false,
    isSalePrice: true,
    isPromoPrice: false,
    localized_max_effective_price: '$109.00',
    localized_min_effective_price: '$109.00',
    localized_max_regular_price: '$109.00',
    localized_min_regular_price: '$109.00',
  } as unknown as AdaptedVariantCustomerChoice['price'];

  it.skip('Should render ColorPickerGrouped with correct price', () => {
    render(
      <MVGColorPickerGrouped
        groupKey='test'
        colorGroup={colorGroup}
        handleClickColor={handleClickColor}
        handleHoverColor={handleHoverColor}
        selectedColorId='1'
        parentComponent='buybox'
        price={price}
      />
    );
    const colorPickerGroupedPrice = screen.getByText('$23212');

    expect(colorPickerGroupedPrice).toBeInTheDocument();
  }); // Update tests related to price as soon as price component is ready to be used
  it('Should render ColorPickerGrouped and execute handleClickColor when it is clicked', () => {
    wrapMvgInTestApp(
      <MVGColorPickerGrouped
        groupKey='test'
        colorGroup={colorGroup}
        handleClickCustomerChoice={handleClickColor}
        handleHoverCustomerChoice={handleHoverColor}
        selectedColorId='1'
        parentComponent='buybox'
        price={price}
        isPercentageEnabled={false}
        colorSwatchSize={'small'}
        dimensions={[]}
        isDesktop={false}
      />
    );
    const colorPickerGrouped = document.getElementById('buyBux-color-swatch--Blue');

    waitFor(() => {
      fireEvent.change(colorPickerGrouped!, { target: { checked: true } });

      expect(handleClickColor).toHaveBeenCalled();
    });
  });
  it('Should render ColorPickerGrouped and execute handleHoverColor when it is hover and unhover', () => {
    wrapMvgInTestApp(
      <MVGColorPickerGrouped
        groupKey='test'
        colorGroup={colorGroup}
        handleClickCustomerChoice={handleClickColor}
        handleHoverCustomerChoice={handleHoverColor}
        selectedColorId='1'
        parentComponent='buybox'
        isPercentageEnabled
        price={price}
        colorSwatchSize={'small'}
        dimensions={[]}
        isDesktop={false}
      />
    );
    const colorPickerGrouped = document.getElementById('color-swatch-container');
    waitFor(() => {
      // Simulate mouse enter
      userEvent.hover(colorPickerGrouped!);
      expect(handleHoverColor).toHaveBeenCalledWith('blue');

      // Simulate mouse leave
      userEvent.unhover(colorPickerGrouped!);
      expect(handleHoverColor).toHaveBeenCalledWith(null);
    });
  });

  it('Should not show price when is giftcard', () => {
    wrapMvgInTestApp(
      <MVGColorPickerGrouped
        groupKey='test'
        isGiftCard
        colorGroup={colorGroup}
        handleClickCustomerChoice={handleClickColor}
        handleHoverCustomerChoice={handleHoverColor}
        selectedColorId='1'
        isPercentageEnabled
        parentComponent='buybox'
        price={price}
        colorSwatchSize={'small'}
        dimensions={[]}
        isDesktop={false}
      />
    );
    const colorPickerGroupedPrice = screen.queryByText('$23212');

    expect(colorPickerGroupedPrice).not.toBeInTheDocument();
  });

  it('Should not show price when is single color group', () => {
    wrapMvgInTestApp(
      <MVGColorPickerGrouped
        groupKey='test'
        isSingleColorGroup
        colorGroup={colorGroup}
        handleClickCustomerChoice={handleClickColor}
        handleHoverCustomerChoice={handleHoverColor}
        selectedColorId='1'
        parentComponent='buybox'
        price={price}
        colorSwatchSize={'small'}
        dimensions={[]}
        isDesktop={false}
        isPercentageEnabled={false}
      />
    );
    const colorPickerGroupedPrice = screen.queryByText('$23212');

    expect(colorPickerGroupedPrice).not.toBeInTheDocument();
  });

  it('Should not show price when block layout is true', () => {
    wrapMvgInTestApp(
      <MVGColorPickerGrouped
        groupKey='test'
        isSingleColorGroup
        colorGroup={colorGroup}
        handleClickCustomerChoice={handleClickColor}
        handleHoverCustomerChoice={handleHoverColor}
        selectedColorId='1'
        parentComponent='buybox'
        price={price}
        isBlock={true}
        colorSwatchSize={'small'}
        dimensions={[]}
        isDesktop={false}
        isPercentageEnabled={false}
      />
    );
    const colorPickerGroupedPrice = screen.queryByText('$23212');

    expect(colorPickerGroupedPrice).not.toBeInTheDocument();
  });

  it('Should render ColorPickerGrouped with wrapped style when isWrapped is true', () => {
    wrapMvgInTestApp(
      <MVGColorPickerGrouped
        groupKey='test'
        colorGroup={colorGroup}
        handleClickCustomerChoice={handleClickColor}
        handleHoverCustomerChoice={handleHoverColor}
        selectedColorId='1'
        isPercentageEnabled
        parentComponent='buybox'
        price={price}
        isWrapped={true}
        colorSwatchSize={'small'}
        dimensions={[]}
        isDesktop={false}
      />
    );
    const swatchesContainer = document.querySelector('.pdp-color-picker-wrapped');
    expect(swatchesContainer).toBeInTheDocument();
  });

  it('Should render ColorPickerGrouped with stacked style when isStacked is true', () => {
    wrapMvgInTestApp(
      <MVGColorPickerGrouped
        groupKey='test'
        colorGroup={colorGroup}
        handleClickCustomerChoice={handleClickColor}
        handleHoverCustomerChoice={handleHoverColor}
        selectedColorId='1'
        isPercentageEnabled
        parentComponent='buybox'
        price={price}
        isWrapped={false}
        isStacked={true}
        colorSwatchSize={'small'}
        dimensions={[]}
        isDesktop={false}
      />
    );
    const swatchesContainer = document.querySelector('.pdp-color-picker-stacked');
    expect(swatchesContainer).toBeInTheDocument();
  });

  it('Should render ColorPickerGrouped with block style when isBlock is true', () => {
    wrapMvgInTestApp(
      <MVGColorPickerGrouped
        groupKey='test'
        colorGroup={colorGroup}
        handleClickCustomerChoice={handleClickColor}
        handleHoverCustomerChoice={handleHoverColor}
        selectedColorId='1'
        isPercentageEnabled
        parentComponent='buybox'
        price={price}
        isWrapped={false}
        isStacked={false}
        isBlock={true}
        colorSwatchSize={'small'}
        dimensions={[]}
        isDesktop={false}
      />
    );
    const swatchesContainer = document.querySelector('.pdp-color-picker-block');
    expect(swatchesContainer).toBeInTheDocument();
  });

  it('Should mark a color as selected when it matches selectedColorId', () => {
    const selectedId = colorGroup[0].customer_choice_id;
    wrapMvgInTestApp(
      <MVGColorPickerGrouped
        groupKey='test'
        colorGroup={colorGroup}
        handleClickCustomerChoice={handleClickColor}
        handleHoverCustomerChoice={handleHoverColor}
        selectedColorId={selectedId}
        parentComponent='buybox'
        price={price}
        colorSwatchSize={'small'}
        dimensions={[]}
        isDesktop={false}
        isPercentageEnabled={false}
      />
    );

    const selectedSwatch = document.querySelector('[data-selected="true"]');
    expect(selectedSwatch).toBeInTheDocument();
  });

  it('Should correctly apply availability status to color swatches', () => {
    // Create a test group with one available and one unavailable color
    const mixedAvailabilityGroup = [
      { ...colorGroup[0], isInstock: true },
      { ...colorGroup[0], isInstock: false, customer_choice_id: '985218043' },
    ];

    wrapMvgInTestApp(
      <MVGColorPickerGrouped
        groupKey='test'
        colorGroup={mixedAvailabilityGroup}
        handleClickCustomerChoice={handleClickColor}
        handleHoverCustomerChoice={handleHoverColor}
        selectedColorId='1'
        isPercentageEnabled
        parentComponent='buybox'
        price={price}
        colorSwatchSize={'small'}
        dimensions={[]}
        isDesktop={false}
      />
    );

    const inStockSwatch = screen.getByTestId('pdp-color-swatch-instock');
    const outOfStockSwatch = screen.getByTestId('pdp-color-swatch-outofstock');

    expect(inStockSwatch).toBeInTheDocument();
    expect(outOfStockSwatch).toBeInTheDocument();
  });

  describe('User Interactions', () => {
    const user = userEvent.setup();

    it('Should call handleClickCustomerChoice with the correct color when a swatch is clicked', async () => {
      wrapMvgInTestApp(
        <MVGColorPickerGrouped
          groupKey='test'
          colorGroup={colorGroup}
          handleClickCustomerChoice={handleClickColor}
          handleHoverCustomerChoice={handleHoverColor}
          selectedColorId=''
          parentComponent='buybox'
          price={price}
          colorSwatchSize={'small'}
          dimensions={[]}
          isDesktop={false}
          isPercentageEnabled={false}
        />
      );

      const inStockSwatch = screen.getByTestId('pdp-color-swatch-instock');
      await user.click(inStockSwatch);

      expect(handleClickColor).toHaveBeenCalledTimes(1);
      expect(handleClickColor).toHaveBeenCalledWith(colorGroup[0]);
    });

    it('Should call handleHoverCustomerChoice on mouse enter and leave when isDesktop is true', async () => {
      wrapMvgInTestApp(
        <MVGColorPickerGrouped
          groupKey='test'
          colorGroup={colorGroup}
          handleClickCustomerChoice={handleClickColor}
          handleHoverCustomerChoice={handleHoverColor}
          selectedColorId=''
          parentComponent='buybox'
          price={price}
          colorSwatchSize={'small'}
          dimensions={[]}
          isDesktop={true}
          isPercentageEnabled={false}
        />
      );

      const swatchContainer = screen.getByTestId('pdp-color-swatch-instock').closest('.pdp-color-picker-swatch-container');

      await user.hover(swatchContainer!);
      expect(handleHoverColor).toHaveBeenCalledWith(colorGroup[0]);

      await user.unhover(swatchContainer!);
      expect(handleHoverColor).toHaveBeenCalledWith(null);
    });

    it('Should NOT call handleHoverCustomerChoice on mouse enter when isDesktop is false', async () => {
      handleHoverColor.mockClear();
      wrapMvgInTestApp(
        <MVGColorPickerGrouped
          groupKey='test'
          colorGroup={colorGroup}
          handleClickCustomerChoice={handleClickColor}
          handleHoverCustomerChoice={handleHoverColor}
          selectedColorId=''
          parentComponent='buybox'
          price={price}
          colorSwatchSize={'small'}
          dimensions={[]}
          isDesktop={false}
          isPercentageEnabled={false}
        />
      );

      const swatchContainer = screen.getByTestId('pdp-color-swatch-instock').closest('.pdp-color-picker-swatch-container');

      await user.hover(swatchContainer!);
      expect(handleHoverColor).not.toHaveBeenCalled();
    });
  });
});
