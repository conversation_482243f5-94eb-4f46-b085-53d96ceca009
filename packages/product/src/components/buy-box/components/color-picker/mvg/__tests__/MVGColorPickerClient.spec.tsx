import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { useMVGBuyBoxStore } from '../../../../../../providers/mvg-buybox-provider';
import { MVGColorPickerClient } from '../MVGColorPickerClient';
import { MVGColorPickerGrouped } from '../MVGColorPickerGrouped';
import { LAYOUTS } from '../../collaborators/get-config';

jest.mock('../../../../../../providers/mvg-buybox-provider');
jest.mock('../MVGColorPickerGrouped', () => ({
  MVGColorPickerGrouped: jest.fn(({ handleClickCustomerChoice, handleHoverCustomerChoice }) => (
    <div
      data-testid='mock-mvg-color-picker-grouped'
      onClick={() => handleClickCustomerChoice({ customer_choice_id: '2', description: 'blue' })}
      onMouseEnter={() => handleHoverCustomerChoice({ customer_choice_id: '2', description: 'blue' })}
      role='button'
      tabIndex={0}
      onKeyDown={() => {}}
    />
  )),
}));
jest.mock('@ecom-next/sitewide/localization-provider', () => ({
  useLocalize: jest.fn(() => ({
    localize: jest.fn(key => (key === 'pdp.colorLabel' ? 'Color' : '')),
  })),
}));

const mockUseMVGBuyBoxStore = useMVGBuyBoxStore as jest.Mock;

describe('<MVGColorPickerClient />', () => {
  const defaultStoreState = {
    selectedCustomerChoice: { customer_choice_id: '1', description: 'red' },
    selectedMultiVariantData: {
      color_groups: {
        regular: [[{ customer_choice_id: '1', description: 'red', price: {} }]],
        markdown: [],
      },
      dimensions: [],
      variantKey: 'variant-1',
    },
    hoverCustomerChoice: null,
    isGiftCard: false,
    customer_choices: {
      '2': { customer_choice_id: '2', description: 'blue' },
    },
    updateSelectedCustomerChoice: jest.fn(),
    handleHoverCustomerChoice: jest.fn(),
  };

  const defaultProps = {
    parentComponent: 'buybox' as const,
    isPercentageEnabled: false,
    brandName: 'gap',
    isDesktop: true,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseMVGBuyBoxStore.mockReturnValue(defaultStoreState);
  });

  it('should call updateSelectedCustomerChoice on click', () => {
    render(<MVGColorPickerClient {...defaultProps} />);
    fireEvent.click(screen.getByTestId('mock-mvg-color-picker-grouped'));
    expect(defaultStoreState.updateSelectedCustomerChoice).toHaveBeenCalledWith({
      customer_choice_id: '2',
      description: 'blue',
    });
  });

  it('should call handleHoverCustomerChoice on hover after debounce', () => {
    jest.useFakeTimers();
    render(<MVGColorPickerClient {...defaultProps} />);
    fireEvent.mouseEnter(screen.getByTestId('mock-mvg-color-picker-grouped'));

    expect(defaultStoreState.handleHoverCustomerChoice).not.toHaveBeenCalled();
    jest.advanceTimersByTime(300);
    expect(defaultStoreState.handleHoverCustomerChoice).toHaveBeenCalledWith({
      customer_choice_id: '2',
      description: 'blue',
    });

    jest.useRealTimers();
  });

  it('should call handleHoverCustomerChoice on hover when isDesktop is true', () => {
    jest.useFakeTimers();
    const handleHoverCustomerChoice = jest.fn();
    mockUseMVGBuyBoxStore.mockReturnValue({
      ...defaultStoreState,
      handleHoverCustomerChoice,
    });

    render(<MVGColorPickerClient {...defaultProps} isDesktop={true} />);
    fireEvent.mouseEnter(screen.getByTestId('mock-mvg-color-picker-grouped'));

    jest.advanceTimersByTime(300);

    expect(handleHoverCustomerChoice).toHaveBeenCalledWith({
      customer_choice_id: '2',
      description: 'blue',
    });
    jest.useRealTimers();
  });

  it('should pass correct layout props to MVGColorPickerGrouped', () => {
    const { rerender } = render(<MVGColorPickerClient {...defaultProps} layout={LAYOUTS.stacked} />);
    expect(MVGColorPickerGrouped).toHaveBeenCalledWith(expect.objectContaining({ isStacked: true }), {});

    rerender(<MVGColorPickerClient {...defaultProps} layout={LAYOUTS.block} />);
    expect(MVGColorPickerGrouped).toHaveBeenCalledWith(expect.objectContaining({ isBlock: true }), {});

    rerender(<MVGColorPickerClient {...defaultProps} layout={LAYOUTS.wrapped} />);
    expect(MVGColorPickerGrouped).toHaveBeenCalledWith(expect.objectContaining({ isWrapped: true }), {});
  });
});
