import { SelectorSwatch as ColorSwatch } from '@ecom-next/core/fabric/selector-swatch';
import React from 'react';
import classNames from 'classnames';
import { MvgBaseProductPriceClient } from '../../product-price/mvg/MvgProductPriceClient';
import { AdaptedVariantCustomerChoice, CAPIV3TransformedData } from '../../../../../pages/services/capi-aggregation-service';
import { isColorInStock } from './collaborators/mvg-color-in-stock';

export const MVGColorPickerGrouped = ({
  colorGroup,
  price,
  selectedColorId,
  groupKey,
  isGiftCard,
  isSingleColorGroup,
  handleClickCustomerChoice = () => {},
  handleHoverCustomerChoice = () => {},
  isPercentageEnabled,
  parentComponent,
  isWrapped,
  isStacked,
  isBlock,
  colorSwatchSize,
  isDesktop,
  mode,
  dimensions,
}: {
  colorGroup: AdaptedVariantCustomerChoice[];
  colorSwatchSize: 'x-large' | 'large' | 'medium' | 'small' | 'x-small';
  dimensions: CAPIV3TransformedData['selectedMultiVariantData']['dimensions'];
  groupKey: string;
  handleClickCustomerChoice: (color: AdaptedVariantCustomerChoice) => void;
  handleHoverCustomerChoice: (color: AdaptedVariantCustomerChoice | null) => void;
  isBlock: boolean;
  isDesktop: boolean;
  isGiftCard?: boolean;
  isInteractionDisabled?: boolean;
  isPercentageEnabled: boolean;
  isSingleColorGroup?: boolean;
  isStacked: boolean;
  isWrapped: boolean;
  mode?: number;
  parentComponent: 'buybox' | 'csm';
  price: AdaptedVariantCustomerChoice['price'];
  selectedColorId: string;
}) => {
  const showPrice = !isGiftCard && !isSingleColorGroup && !isBlock;

  const colorPickerLayoutClass = classNames('pdp-color-picker-group-container__swatches', {
    'pdp-color-picker-wrapped p-1': isWrapped,
    'pdp-color-picker-stacked p-1': isStacked,
    'pdp-color-picker-block p-0.5': isBlock,
  });

  const hasDifferentPrices = !price.isRegularPrice;

  return (
    <div className='pdp-color-picker-group'>
      {showPrice && (
        <MvgBaseProductPriceClient
          price={price}
          hasDifferentPrices={hasDifferentPrices}
          isPercentageEnabled={isPercentageEnabled}
          priceStyles='color-swatch-price'
        />
      )}

      <div className={colorPickerLayoutClass}>
        {Array.isArray(colorGroup) &&
          colorGroup.map((color, key) => {
            const isSelected = selectedColorId === color?.customer_choice_id;
            const id = `pdp-${parentComponent}-color-swatch--${color?.description?.split(' ').join('-')}-${groupKey}-${key}`;
            const isAvailable = isColorInStock(color, dimensions, mode);

            return (
              <div
                key={id}
                onMouseEnter={() => isDesktop && handleHoverCustomerChoice(color)}
                onMouseLeave={() => isDesktop && handleHoverCustomerChoice(null)}
                id={color.customer_choice_id}
                className='pdp-color-picker-swatch-container'
              >
                <ColorSwatch
                  data-testid={`pdp-color-swatch-${isAvailable ? 'instock' : 'outofstock'}`}
                  data-selected={isSelected ? 'true' : 'false'}
                  groupName={`${parentComponent}-color-swatch`}
                  className='color-swatch h-10 w-80'
                  id={id}
                  onChange={() => handleClickCustomerChoice(color)}
                  color={color.description}
                  ariaLabel='custom color-swatch'
                  imageUrl={color.swatchImage}
                  isAvailable={!!isAvailable}
                  checked={isSelected}
                  size={colorSwatchSize}
                  uncontrolled={false}
                />
              </div>
            );
          })}
      </div>
    </div>
  );
};
