'use client';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { useCallback, useRef, useEffect, useMemo } from 'react';
import { debounce } from 'lodash';
import { useShallow } from 'zustand/react/shallow';
import classNames from 'classnames';
import { useMVGBuyBoxStore } from '../../../../../providers/mvg-buybox-provider';
import { AdaptedVariantCustomerChoice } from '../../../../../pages/services/capi-aggregation-service/v3/types';
import { LAYOUTS, LayoutType } from '../collaborators/get-config';
import { MVGColorPickerGrouped } from './MVGColorPickerGrouped';
import { getAllColorsMVG } from './helpers/get-all-colors-mvg';

export const MVGColorPickerClient = ({
  parentComponent,
  isPercentageEnabled,
  brandName,
  layout = LAYOUTS.wrapped,
  isDesktop,
  mode = 0,
}: {
  brandName: string;
  isDesktop: boolean;
  isPercentageEnabled: boolean;
  layout: LayoutType;
  mode?: number;
  parentComponent: 'buybox' | 'csm';
}): JSX.Element => {
  const {
    selectedCustomerChoiceId,
    selectedCustomerChoiceName,
    selectedMultiVariantData,
    hoverCustomerChoiceName,
    isGiftCard,
    updateSelectedCustomerChoice,
    handleHoverCustomerChoice,
    customer_choices,
    variantKey,
  } = useMVGBuyBoxStore(
    useShallow(state => ({
      selectedCustomerChoiceId: state.selectedCustomerChoice?.customer_choice_id ?? '',
      selectedCustomerChoiceName: state.selectedCustomerChoice?.description ?? '',
      selectedMultiVariantData: state.selectedMultiVariantData,
      hoverCustomerChoiceName: state.hoverCustomerChoice?.description ?? '',
      variantKey: state.selectedMultiVariantData?.variantKey,
      isGiftCard: state.selectedStyle?.gift_card,
      customer_choices: state.customer_choices,
      updateSelectedCustomerChoice: state.updateSelectedCustomerChoice,
      handleHoverCustomerChoice: state.handleHoverCustomerChoice,
    }))
  );

  const colorGroups = useMemo(() => selectedMultiVariantData?.color_groups, [selectedMultiVariantData?.variantKey]);
  const dimensions = useMemo(() => selectedMultiVariantData?.dimensions, [selectedMultiVariantData?.variantKey, selectedMultiVariantData?.dimensions]);
  const markdown = useMemo(() => colorGroups?.markdown || [], [colorGroups]);
  const regular = useMemo(() => colorGroups?.regular || [], [colorGroups]);
  const isSingleColorGroup = useMemo(() => markdown?.length + regular?.length === 1, [markdown, regular]);
  const allColorGroups = useMemo(() => getAllColorsMVG(colorGroups), [colorGroups]);
  const isChangeStoreModal = useMemo(() => parentComponent === 'csm', [parentComponent]);

  const isWrapped = useMemo(() => layout === LAYOUTS.wrapped || !!isGiftCard, [layout, isGiftCard]);
  const isBlock = useMemo(() => layout === LAYOUTS.block && !isGiftCard, [layout, isGiftCard]);
  const isStacked = useMemo(() => layout === LAYOUTS.stacked && !isGiftCard, [layout, isGiftCard]);

  const colorPickerLayout = useMemo(
    () =>
      classNames('pdp-color-picker-container-group relative h-max', {
        'pdp-color-picker-wrapped': isWrapped,
        'pdp-color-picker-block': isBlock,
        'pdp-color-picker-stacked': isStacked,
      }),
    [isWrapped, isBlock, isStacked]
  );

  const containerRef = useRef<HTMLDivElement>(null);

  const debouncedHandleHoverCustomerChoice = useCallback(debounce(handleHoverCustomerChoice, 300), []);
  const { localize } = useLocalize();
  const colorLabel = localize('pdp.colorLabel');
  const colorName = useMemo(
    () => (hoverCustomerChoiceName !== '' ? hoverCustomerChoiceName : selectedCustomerChoiceName),
    [hoverCustomerChoiceName, selectedCustomerChoiceName]
  );

  const handleClickCustomerChoice = useCallback((color: AdaptedVariantCustomerChoice) => {
    const newCustomerChoice = { ...customer_choices[color.customer_choice_id], ...color };
    updateSelectedCustomerChoice(newCustomerChoice);
  }, []);

  const getColorLabel = useCallback(
    (colorLabelString: string) => {
      return colorLabelString === 'label' ? colorLabel : colorName;
    },
    [colorLabel, colorName]
  );

  const colorSwatchSize = useMemo(() => (brandName === 'gap' || brandName === 'gapfs' ? 'x-large' : 'medium'), [brandName]);
  const showColorLabel = useMemo(() => parentComponent !== 'csm', [parentComponent]);

  const normalizeColor = useCallback((color: string) => {
    if (!color) {
      return '';
    }

    return color.toLowerCase().replace(/\b\w/g, (char: string) => char.toUpperCase());
  }, []);

  useEffect(() => {
    const container = containerRef.current;
    const innerContainerGroup = container?.children?.[0];

    if (!innerContainerGroup || isChangeStoreModal) {
      return;
    }

    // Let the height of the inner container group vary and use it as a reference to set the new min-height of the outer container div
    const newMinHeight = innerContainerGroup.scrollHeight;
    // This is a workaround to animate min-height instead of height, because not all browsers support animating "height: auto" as a pure CSS solution
    container.style.minHeight = `${newMinHeight}px`;
  }, [variantKey, colorPickerLayout]);

  return (
    <div className='pdp-color-picker-container' data-testid='pdp-color-picker'>
      {showColorLabel && (
        <div className='pdp-color-picker-container__label-container'>
          <h2 className='pdp-color-picker-container__label'>{getColorLabel('label')}</h2>
          <span
            className={`pdp-color-picker-container__value ${brandName === 'gap' || brandName === 'gapfs' ? `${brandName}-color-value` : ''}`}
            data-testid='pdp-color-value'
          >
            {normalizeColor(getColorLabel('color'))}
          </span>
        </div>
      )}

      <div
        ref={containerRef}
        className='flex h-0 min-h-max items-end overflow-hidden'
        style={{ transition: isChangeStoreModal ? 'none' : 'min-height 800ms ease' }}
      >
        <div className={colorPickerLayout}>
          {isBlock ? (
            <MVGColorPickerGrouped
              handleHoverCustomerChoice={debouncedHandleHoverCustomerChoice}
              handleClickCustomerChoice={handleClickCustomerChoice}
              parentComponent={parentComponent}
              groupKey='mergedGroup-0'
              selectedColorId={selectedCustomerChoiceId}
              colorGroup={allColorGroups}
              price={allColorGroups[0]?.price}
              isGiftCard={isGiftCard}
              isSingleColorGroup={isSingleColorGroup}
              isPercentageEnabled={isPercentageEnabled}
              isWrapped={isWrapped}
              isStacked={isStacked}
              isBlock={isBlock}
              colorSwatchSize={colorSwatchSize}
              isDesktop={isDesktop}
              mode={mode}
              dimensions={dimensions}
            />
          ) : (
            <>
              {regular?.map((colorGroup, index: number) => (
                <MVGColorPickerGrouped
                  handleHoverCustomerChoice={debouncedHandleHoverCustomerChoice}
                  handleClickCustomerChoice={handleClickCustomerChoice}
                  key={`regular-${colorGroup[0].customer_choice_id}-${index}`}
                  parentComponent={parentComponent}
                  groupKey={`regular-${index}`}
                  selectedColorId={selectedCustomerChoiceId}
                  price={colorGroup[0].price}
                  colorGroup={colorGroup}
                  isGiftCard={isGiftCard}
                  isSingleColorGroup={isSingleColorGroup}
                  isPercentageEnabled={isPercentageEnabled}
                  isWrapped={isWrapped}
                  isStacked={isStacked}
                  isBlock={isBlock}
                  colorSwatchSize={colorSwatchSize}
                  isDesktop={isDesktop}
                  mode={mode}
                  dimensions={dimensions}
                />
              ))}
              {markdown?.map((colorGroup, index: number) => (
                <MVGColorPickerGrouped
                  handleHoverCustomerChoice={debouncedHandleHoverCustomerChoice}
                  handleClickCustomerChoice={handleClickCustomerChoice}
                  parentComponent={parentComponent}
                  key={`markdown-${colorGroup[0].customer_choice_id}-${index}`}
                  groupKey={`markdown-${index}`}
                  selectedColorId={selectedCustomerChoiceId}
                  colorGroup={colorGroup}
                  price={colorGroup[0].price}
                  isGiftCard={isGiftCard}
                  isSingleColorGroup={isSingleColorGroup}
                  isPercentageEnabled={isPercentageEnabled}
                  isWrapped={isWrapped}
                  isStacked={isStacked}
                  isBlock={isBlock}
                  colorSwatchSize={colorSwatchSize}
                  isDesktop={isDesktop}
                  mode={mode}
                  dimensions={dimensions}
                />
              ))}
            </>
          )}
        </div>
      </div>
    </div>
  );
};
