'use client';
import React, { useCallback } from 'react';
import classNames from 'classnames';
import { useShallow } from 'zustand/react/shallow';
import { debounce } from 'lodash';
import { useBuyBoxStore } from '../../../../providers/buybox-provider';
import { useLocalize } from '../../../../../../sitewide/src/providers/localization';
import { ColorPickerGrouped } from './ColorPickerGrouped';
import { ProductColorGroup, ProductColor, ONLINE_AND_BOPIS_INVENTORY, BOPIS_INVENTORY } from './types';
import { LAYOUTS, LayoutType } from './collaborators/get-config';
import { getAllColorGroups } from './helpers/get-all-colors';
import { mergeColorGroups } from './helpers/get-merged-color-groups';

const useColorPickerData = () => {
  const { selectedColor, selectedVariant, hoverColor, isGiftCard, updateSelectedColor, handleHoverColor } = useBuyBoxStore(
    useShallow(state => ({
      selectedColor: state.selectedColor,
      selectedVariant: state.selectedVariant,
      hoverColor: state.hoverColor,
      isGiftCard: state.isGiftCard,
      updateSelectedColor: state.updateSelectedColor,
      handleHoverColor: state.handleHoverColor,
    }))
  );

  return { selectedColor, selectedVariant, hoverColor, isGiftCard, updateSelectedColor, handleHoverColor };
};
type ColorPickerProps = {
  brandName?: Brands;
  isDesktop?: boolean;
  isPercentageEnabled?: boolean;
  layout?: LayoutType;
  parentComponent: 'buybox' | 'csm';
  superPDP?: {
    enabled: boolean;
    isMergeStyle: boolean;
    messageCopy: 'fabricCopy' | 'defaultCopy';
  };
};

export const ColorPicker = ({ layout = LAYOUTS.wrapped, isPercentageEnabled = false, parentComponent, brandName, isDesktop }: ColorPickerProps) => {
  const { selectedColor, selectedVariant, hoverColor, isGiftCard, updateSelectedColor, handleHoverColor } = useColorPickerData();

  const { colorGroups, dimensionGroups, dimensions } = selectedVariant;

  const handleClickColor = useCallback((color: ProductColor) => {
    updateSelectedColor(color);
  }, []);

  const debouncedHandleHoverColor = useCallback(debounce(handleHoverColor, 300), []);

  const isWrapped = layout === LAYOUTS.wrapped || isGiftCard;
  const isBlock = layout === LAYOUTS.block && !isGiftCard;
  const isStacked = layout === LAYOUTS.stacked && !isGiftCard;

  const colorPickerLayout = classNames('pdp-color-picker-container-group', {
    'pdp-color-picker-wrapped': isWrapped,
    'pdp-color-picker-block': isBlock,
    'pdp-color-picker-stacked': isStacked,
  });

  const colorName = hoverColor?.colorName || selectedColor.colorName;
  const { localize } = useLocalize();
  const colorLabel = localize('pdp.colorLabel');
  const getColorLabel = (colorLabelString: string) => {
    return colorLabelString === 'label' ? colorLabel : colorName;
  };

  const colorSwatchSize = brandName === 'gap' || brandName === 'gapfs' ? 'x-large' : 'medium';

  const normalizeColor = (color: string) => {
    if (!color) {
      return '';
    }

    return color.toLowerCase().replace(/\b\w/g, (char: string) => char.toUpperCase());
  };

  const inventoryMode = parentComponent === 'csm' ? BOPIS_INVENTORY : ONLINE_AND_BOPIS_INVENTORY;
  const showColorLabel = parentComponent !== 'csm';

  const allColorGroups = getAllColorGroups(colorGroups);
  const allColors = mergeColorGroups(allColorGroups);
  const isSingleColorGroup = allColorGroups.length === 1;

  const { fullprice, markdown } = colorGroups;

  return (
    <div className='pdp-color-picker-container' data-testid='pdp-color-picker'>
      {showColorLabel && (
        <div className='pdp-color-picker-container__label-container' data-testid='pdp-color-label-wrapper'>
          <h2 className='pdp-color-picker-container__label'>{getColorLabel('label')}</h2>
          <span
            className={`pdp-color-picker-container__value ${brandName === 'gap' || brandName === 'gapfs' ? `${brandName}-color-value` : ''}`}
            data-testid='pdp-color-value'
          >
            {normalizeColor(getColorLabel('color'))}
          </span>
        </div>
      )}
      <div className={colorPickerLayout}>
        {isBlock ? (
          <ColorPickerGrouped
            handleHoverColor={debouncedHandleHoverColor}
            handleClickColor={handleClickColor}
            parentComponent={parentComponent}
            groupKey='mergedGroup-0'
            selectedColorId={selectedColor.businessCatalogItemId}
            colorGroup={allColors}
            dimensions={dimensionGroups || dimensions}
            isGiftCard={isGiftCard}
            isSingleColorGroup={isSingleColorGroup}
            isPercentageEnabled={isPercentageEnabled}
            isWrapped={isWrapped}
            isStacked={isStacked}
            isBlock={isBlock}
            colorSwatchSize={colorSwatchSize}
            mode={inventoryMode}
            isDesktop={isDesktop}
          />
        ) : (
          <>
            {fullprice?.map((colorGroup: ProductColorGroup, index: number) => (
              <ColorPickerGrouped
                handleHoverColor={debouncedHandleHoverColor}
                handleClickColor={handleClickColor}
                key={index}
                parentComponent={parentComponent}
                groupKey={`fullprice-${index}`}
                selectedColorId={selectedColor.businessCatalogItemId}
                colorGroup={colorGroup}
                dimensions={dimensionGroups || dimensions}
                isGiftCard={isGiftCard}
                isSingleColorGroup={isSingleColorGroup}
                isPercentageEnabled={isPercentageEnabled}
                isWrapped={isWrapped}
                isStacked={isStacked}
                isBlock={isBlock}
                colorSwatchSize={colorSwatchSize}
                mode={inventoryMode}
                isDesktop={isDesktop}
              />
            ))}
            {markdown?.map((colorGroup: ProductColorGroup, index: number) => (
              <ColorPickerGrouped
                handleHoverColor={debouncedHandleHoverColor}
                handleClickColor={handleClickColor}
                parentComponent={parentComponent}
                key={index}
                groupKey={`markdown-${index}`}
                selectedColorId={selectedColor.businessCatalogItemId}
                colorGroup={colorGroup}
                dimensions={dimensionGroups || dimensions}
                isGiftCard={isGiftCard}
                isSingleColorGroup={isSingleColorGroup}
                isPercentageEnabled={isPercentageEnabled}
                isWrapped={isWrapped}
                isStacked={isStacked}
                isBlock={isBlock}
                colorSwatchSize={colorSwatchSize}
                mode={inventoryMode}
                isDesktop={isDesktop}
              />
            ))}
          </>
        )}
      </div>
    </div>
  );
};
