.pdp-color-picker-container__label {
  color: theme('colors.color-type-copy');
  font-family: theme('fontFamily.brand-base');
  font-size: theme('fontSize.font-size-0');
  font-weight: theme('fontWeight.font-weight-base-heavier');
  line-height: normal;
  letter-spacing: theme('letterSpacing.font-letter-spacing-base');
}

.pdp-color-picker-container__value {
  color: theme('colors.color-type-copy');
  font-family: theme('fontFamily.brand-base');
  font-size: theme('fontSize.font-size-0');
  font-style: normal;
  font-weight: theme('fontWeight.font-weight-base-default');
  line-height: normal;
  letter-spacing: theme('letterSpacing.font-letter-spacing-base');
}

.pdp-color-picker-container__value.gap-color-value,
.pdp-color-picker-container__value.gapfs-color-value {
  text-transform: lowercase;
  &::first-letter {
    text-transform: uppercase;
  }
}

.pdp-color-picker-container__label-container {
  gap: theme('gap.utk-spacing-2xs');
  display: flex;
  align-items: center;
}

.pdp-color-picker-container {
  display: flex;
  flex-direction: column;
  gap: theme('gap.utk-spacing-l');
  align-items: flex-start;
}

.pdp-color-picker-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-end;
  gap: theme('gap.utk-spacing-s');
}

.pdp-color-picker-container-group {
  &.pdp-color-picker-wrapped,
  &.pdp-color-picker-stacked {
    display: flex;
    gap: theme('gap.utk-spacing-l');
  }
  &.pdp-color-picker-wrapped {
    flex-wrap: wrap;
  }
  &.pdp-color-picker-stacked {
    flex-direction: column;
  }
}
.pdp-color-picker-group-container__swatches {
  display: flex;
  flex-wrap: wrap;
  align-self: stretch;
  gap: theme('gap.utk-spacing-2xs');
}
