import { ProductColorGroup } from '../types';

/**
 * Merges multiple color groups into a single group containing all colors.
 * The resulting group has a generic ID and no price, as it's a combination.
 */
export function mergeColorGroups(groups: ProductColorGroup[]): ProductColorGroup {
  return {
    id: 'merged-group',
    colors: groups.flatMap(g => g.colors),
    price: {} as ProductColorGroup['price'],
  };
}
