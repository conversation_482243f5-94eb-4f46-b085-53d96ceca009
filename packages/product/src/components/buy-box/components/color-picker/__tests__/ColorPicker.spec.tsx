import { render, screen, fireEvent } from '@testing-library/react';
import { prevVariantMock } from '../../variant-group/__tests__/variantMock';
import { LayoutType } from '../collaborators/get-config';
import { ONLINE_AND_BOPIS_INVENTORY, BOPIS_INVENTORY } from '../types';
import { ColorPicker } from '../ColorPicker';
import { ColorPickerGrouped } from '../ColorPickerGrouped';
import { useBuyBoxStore } from '../../../../../providers/buybox-provider';

jest.mock('../../../../../../../sitewide/src/providers/localization', () => ({
  useLocalize: () => ({
    localize: jest.fn().mockReturnValue('Color Label'),
  }),
}));

jest.mock('../../../../../providers/buybox-provider', () => ({
  useBuyBoxStore: jest.fn(() => ({
    selectedVariant: prevVariantMock,
    selectedColor: {
      colorName: 'red',
      colorCode: '#FF0000',
    },
    updateSelectedColor: jest.fn(),
    handleHoverColor: jest.fn(),
  })),
}));

jest.mock('../ColorPickerGrouped', () => ({
  ColorPickerGrouped: jest.fn(({ handleClickColor, handleHoverColor }) => (
    <div
      data-testid='mock-color-picker-grouped'
      onClick={() => handleClickColor({ colorName: 'blue' })}
      onMouseEnter={() => handleHoverColor({ colorName: 'blue' })}
      role='button'
      tabIndex={0}
      onKeyDown={() => {}}
    />
  )),
}));

const defaultProps = {
  parentComponent: 'buybox' as 'buybox' | 'csm',
  layout: 'stacked' as LayoutType,
  brandName: 'gap' as Brands,
  isPercentageEnabled: false,
};

describe('<ColorPicker />', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should show the color label when parentComponent is not csm', () => {
    render(<ColorPicker {...defaultProps} />);
    expect(screen.getByTestId('pdp-color-label-wrapper')).toBeInTheDocument();
    expect(screen.getByTestId('pdp-color-value')).toHaveTextContent('Red');
  });

  it('should pass ONLINE_AND_BOPIS_INVENTORY mode to ColorPickerGrouped when parentComponent is buybox', () => {
    render(<ColorPicker {...defaultProps} />);

    expect(ColorPickerGrouped).toHaveBeenCalledWith(
      expect.objectContaining({
        mode: ONLINE_AND_BOPIS_INVENTORY,
      }),
      {}
    );
  });

  it('should pass BOPIS_INVENTORY mode to ColorPickerGrouped when parentComponent is csm', () => {
    render(<ColorPicker {...defaultProps} parentComponent='csm' />);

    expect(ColorPickerGrouped).toHaveBeenCalledWith(
      expect.objectContaining({
        mode: BOPIS_INVENTORY,
      }),
      {}
    );
  });

  it('should call updateSelectedColor when a color is clicked', () => {
    const updateSelectedColor = jest.fn();
    (useBuyBoxStore as jest.Mock).mockImplementation(() => ({
      selectedVariant: prevVariantMock,
      selectedColor: { colorName: 'Red' },
      updateSelectedColor,
      handleHoverColor: jest.fn(),
    }));

    render(<ColorPicker {...defaultProps} />);
    fireEvent.click(screen.getByTestId('mock-color-picker-grouped'));
    expect(updateSelectedColor).toHaveBeenCalledWith({ colorName: 'blue' });
  });

  it('should call handleHoverColor when a color is hovered', () => {
    jest.useFakeTimers();
    const handleHoverColor = jest.fn();
    (useBuyBoxStore as jest.Mock).mockImplementation(() => ({
      selectedVariant: prevVariantMock,
      selectedColor: { colorName: 'Red' },
      updateSelectedColor: jest.fn(),
      handleHoverColor,
    }));

    render(<ColorPicker {...defaultProps} />);
    fireEvent.mouseEnter(screen.getByTestId('mock-color-picker-grouped'));

    jest.advanceTimersByTime(300);

    expect(handleHoverColor).toHaveBeenCalledWith({ colorName: 'blue' });
    jest.useRealTimers();
  });

  it('should handle empty color name for normalizeColor', () => {
    (useBuyBoxStore as jest.Mock).mockImplementation(() => ({
      selectedVariant: prevVariantMock,
      selectedColor: { colorName: '' },
      updateSelectedColor: jest.fn(),
      handleHoverColor: jest.fn(),
    }));
    render(<ColorPicker {...defaultProps} />);
    expect(screen.getByTestId('pdp-color-value')).toHaveTextContent('');
  });
});
