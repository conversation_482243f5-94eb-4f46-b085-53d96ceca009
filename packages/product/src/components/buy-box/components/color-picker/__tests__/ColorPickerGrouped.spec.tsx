import React from 'react';
import { fireEvent, render, screen, waitFor } from '@ecom-next/core/test/test-helper';
import userEvent from '@testing-library/user-event';
import { ColorPickerGrouped } from '../ColorPickerGrouped';
import { ProductColorGroup } from '../types';
import { ProductDimension } from '../../dimension-group/types';

describe('<ColorPickerGrouped  />', () => {
  const handleClickColor = jest.fn();
  const handleHoverColor = jest.fn();

  const colorGroup: ProductColorGroup = {
    colors: [
      {
        businessCatalogItemId: '1',
        colorName: 'Blue',
        currencySymbol: '',
        findInStore: {
          enabled: false,
          exclusions: [],
        },
        inStock: false,
        percentageOffFromCAPI: '2',
        localizedCurrentPrice: '',
        localizedRegularPrice: '',
        marketingFlag: null,
        percentageOff: '',
        priceType: 0,
        colorPaletteSeasonCode: '2',
        productStyleColorImages: [],
        rawCurrentPrice: 0,
        rawRegularPrice: 0,
        sizes: [],
        fitInformation: {},
        url: '',
        isBopisEligible: true,
        price: {
          currentMaxLocalizedPrice: '1',
          currentMaxPrice: 1,
          currentMinLocalizedPrice: '1',
          currentMinPrice: 23212,
          localizedCurrencySymbol: 'string',
          maxDiscountPercentageFromCAPI: 1,
          maxPercentageOff: 1,
          minDiscountPercentageFromCAPI: 1,
          minPercentageOff: 1,
          priceType: 1,
          regularMaxPrice: 1,
          regularMinPrice: 1,
        },
      },
    ],
    id: '1',
    price: {
      currentMaxLocalizedPrice: '$23212',
      currentMaxPrice: 23212,
      currentMinLocalizedPrice: '1',
      currentMinPrice: 1,
      localizedCurrencySymbol: 'string',
      maxDiscountPercentageFromCAPI: 1,
      maxPercentageOff: 1,
      minDiscountPercentageFromCAPI: 1,
      minPercentageOff: 1,
      priceType: 1,
      regularMaxPrice: 1,
      regularMinPrice: 1,
    },
  };

  const dimensions: ProductDimension[] = [
    {
      dimensionGroupId: 'sizeDimension1',
      dimensions: [
        {
          bopisInStock: false,
          inStock: true,
          name: 'XS',
        },
        {
          bopisInStock: false,
          inStock: true,
          name: 'S',
        },
        {
          bopisInStock: false,
          inStock: true,
          name: 'M',
        },
      ],
      label: 'Size',
      selectedDimension: '',
    },
  ];

  it('Should render ColorPickerGrouped with correct price', () => {
    render(
      <ColorPickerGrouped
        dimensions={dimensions}
        groupKey='test'
        colorGroup={colorGroup}
        handleClickColor={handleClickColor}
        handleHoverColor={handleHoverColor}
        selectedColorId='1'
        parentComponent='buybox'
      />
    );
    const colorPickerGroupedPrice = screen.getByText('$23212');

    expect(colorPickerGroupedPrice).toBeInTheDocument();
  });
  it('Should render ColorPickerGrouped and execute handleClickColor when it is clicked', () => {
    render(
      <ColorPickerGrouped
        dimensions={dimensions}
        groupKey='test'
        colorGroup={colorGroup}
        handleClickColor={handleClickColor}
        handleHoverColor={handleHoverColor}
        selectedColorId='1'
        parentComponent='buybox'
      />
    );
    const colorPickerGrouped = document.getElementById('buyBux-color-swatch--Blue');

    waitFor(() => {
      fireEvent.change(colorPickerGrouped!, { target: { checked: true } });

      expect(handleClickColor).toHaveBeenCalled();
    });
  });
  it('Should render ColorPickerGrouped and execute handleHoverColor when it is hover and unhover', () => {
    render(
      <ColorPickerGrouped
        dimensions={dimensions}
        groupKey='test'
        colorGroup={colorGroup}
        handleClickColor={handleClickColor}
        handleHoverColor={handleHoverColor}
        selectedColorId='1'
      />
    );
    const colorPickerGrouped = document.getElementById('color-swatch-container');
    waitFor(() => {
      // Simulate mouse enter
      userEvent.hover(colorPickerGrouped!);
      expect(handleHoverColor).toHaveBeenCalledWith('blue');

      // Simulate mouse leave
      userEvent.unhover(colorPickerGrouped!);
      expect(handleHoverColor).toHaveBeenCalledWith(null);
    });
  });

  it('Should not show price when is giftcard', () => {
    render(
      <ColorPickerGrouped
        dimensions={dimensions}
        groupKey='test'
        isGiftCard
        colorGroup={colorGroup}
        handleClickColor={handleClickColor}
        handleHoverColor={handleHoverColor}
        selectedColorId='1'
      />
    );
    const colorPickerGroupedPrice = screen.queryByText('$23212');

    expect(colorPickerGroupedPrice).not.toBeInTheDocument();
  });

  it('Should not show price when is single color group', () => {
    render(
      <ColorPickerGrouped
        dimensions={dimensions}
        groupKey='test'
        isSingleColorGroup
        colorGroup={colorGroup}
        handleClickColor={handleClickColor}
        handleHoverColor={handleHoverColor}
        selectedColorId='1'
      />
    );
    const colorPickerGroupedPrice = screen.queryByText('$23212');

    expect(colorPickerGroupedPrice).not.toBeInTheDocument();
  });

  it('Should not show price when is block layout', () => {
    render(
      <ColorPickerGrouped
        dimensions={dimensions}
        groupKey='test'
        isSingleColorGroup
        isBlock={true}
        colorGroup={colorGroup}
        handleClickColor={handleClickColor}
        handleHoverColor={handleHoverColor}
        selectedColorId='1'
      />
    );
    const colorPickerGroupedPrice = screen.queryByText('$23212');

    expect(colorPickerGroupedPrice).not.toBeInTheDocument();
  });

  it('Should apply block layout class when isBlock is true', () => {
    const { container } = render(
      <ColorPickerGrouped
        dimensions={dimensions}
        groupKey='test'
        isBlock={true}
        colorGroup={colorGroup}
        handleClickColor={handleClickColor}
        handleHoverColor={handleHoverColor}
        selectedColorId='1'
        parentComponent='buybox'
        colorSwatchSize='medium'
        isStacked={false}
        isWrapped={false}
      />
    );
    const swatchesContainer = container.querySelector('.pdp-color-picker-group-container__swatches');
    expect(swatchesContainer).toHaveClass('pdp-color-picker-block');
  });

  it('Should apply wrapped layout class when isWrapped is true', () => {
    const { container } = render(
      <ColorPickerGrouped
        dimensions={dimensions}
        groupKey='test'
        isBlock={false}
        isWrapped={true}
        colorGroup={colorGroup}
        handleClickColor={handleClickColor}
        handleHoverColor={handleHoverColor}
        selectedColorId='1'
        parentComponent='buybox'
        colorSwatchSize='medium'
        isStacked={false}
      />
    );
    const swatchesContainer = container.querySelector('.pdp-color-picker-group-container__swatches');
    expect(swatchesContainer).toHaveClass('pdp-color-picker-wrapped');
  });

  it('Should apply stacked layout class when isStacked is true', () => {
    const { container } = render(
      <ColorPickerGrouped
        dimensions={dimensions}
        groupKey='test'
        isBlock={false}
        isWrapped={false}
        isStacked={true}
        colorGroup={colorGroup}
        handleClickColor={handleClickColor}
        handleHoverColor={handleHoverColor}
        selectedColorId='1'
        parentComponent='buybox'
        colorSwatchSize='medium'
      />
    );
    const swatchesContainer = container.querySelector('.pdp-color-picker-group-container__swatches');
    expect(swatchesContainer).toHaveClass('pdp-color-picker-stacked');
  });

  it('Should call handleHoverColor on mouse enter and leave when isDesktop is true', async () => {
    const user = userEvent.setup();
    render(
      <ColorPickerGrouped
        dimensions={dimensions}
        groupKey='test'
        colorGroup={colorGroup}
        handleClickColor={handleClickColor}
        handleHoverColor={handleHoverColor}
        selectedColorId='1'
        parentComponent='buybox'
        colorSwatchSize='medium'
        isDesktop={true}
        isBlock={false}
        isStacked={false}
        isWrapped={false}
      />
    );

    const swatchContainer = screen.getByTestId('pdp-color-swatch-outofstock').closest('.pdp-color-picker-swatch-container');
    expect(swatchContainer).toBeInTheDocument();

    await user.hover(swatchContainer!);
    expect(handleHoverColor).toHaveBeenCalledWith(colorGroup.colors[0]);

    await user.unhover(swatchContainer!);
    expect(handleHoverColor).toHaveBeenCalledWith(null);
  });

  it('Should not call handleHoverColor on mouse enter and leave when isDesktop is false', async () => {
    const user = userEvent.setup();
    handleHoverColor.mockClear();
    render(
      <ColorPickerGrouped
        dimensions={dimensions}
        groupKey='test'
        colorGroup={colorGroup}
        handleClickColor={handleClickColor}
        handleHoverColor={handleHoverColor}
        selectedColorId='1'
        parentComponent='buybox'
        colorSwatchSize='medium'
        isDesktop={false}
        isBlock={false}
        isStacked={false}
        isWrapped={false}
      />
    );

    const swatchContainer = screen.getByTestId('pdp-color-swatch-outofstock').closest('.pdp-color-picker-swatch-container');
    expect(swatchContainer).toBeInTheDocument();

    await user.hover(swatchContainer!);
    expect(handleHoverColor).not.toHaveBeenCalled();

    await user.unhover(swatchContainer!);
    expect(handleHoverColor).not.toHaveBeenCalled();
  });

  it('Should call handleClickColor when a color swatch is clicked', async () => {
    const user = userEvent.setup();
    handleClickColor.mockClear();
    render(
      <ColorPickerGrouped
        dimensions={dimensions}
        groupKey='test'
        colorGroup={colorGroup}
        handleClickColor={handleClickColor}
        handleHoverColor={handleHoverColor}
        selectedColorId='2'
        parentComponent='buybox'
        colorSwatchSize='medium'
        isBlock={false}
        isStacked={false}
        isWrapped={false}
      />
    );

    const swatch = screen.getByTestId('pdp-color-swatch-outofstock');
    await user.click(swatch);

    expect(handleClickColor).toHaveBeenCalledTimes(1);
    expect(handleClickColor).toHaveBeenCalledWith(colorGroup.colors[0]);
  });
});
