import { getLayoutFromFeatures, LAYOUTS } from '../collaborators/get-config';

describe('getLayoutFromFeatures', () => {
  const baseFeatureData = {
    enabledFeatures: {},
    featureVariables: {},
    brandName: 'gap',
    market: 'us',
  };

  it('should return wrapped layout if feature is not enabled', () => {
    const result = getLayoutFromFeatures({
      ...baseFeatureData,
      enabledFeatures: {},
      featureVariables: {},
    });
    expect(result).toBe(LAYOUTS.wrapped);
  });

  it('should return wrapped layout if feature is enabled but variable wrapped is true', () => {
    const layoutFeatureKey = 'pdp-color-swatch-layout-us-gap';
    const result = getLayoutFromFeatures({
      ...baseFeatureData,
      enabledFeatures: { [layoutFeatureKey]: true },
      featureVariables: { [layoutFeatureKey]: { default: true, stacked: false } },
    });
    expect(result).toBe(LAYOUTS.wrapped);
  });

  it('should return stacked layout if feature is enabled and variable stacked is true and wrapped and block is false', () => {
    const layoutFeatureKey = 'pdp-color-swatch-layout-us-gap';
    const result = getLayoutFromFeatures({
      ...baseFeatureData,
      enabledFeatures: { [layoutFeatureKey]: true },
      featureVariables: { [layoutFeatureKey]: { default: false, stacked: true, block: false } },
    });
    expect(result).toBe(LAYOUTS.stacked);
  });

  it('should return wrapped layout if feature is enabled but variables are missing', () => {
    const layoutFeatureKey = 'pdp-color-swatch-layout-us-gap';
    const result = getLayoutFromFeatures({
      ...baseFeatureData,
      enabledFeatures: { [layoutFeatureKey]: true },
      featureVariables: {},
    });
    expect(result).toBe(LAYOUTS.wrapped);
  });

  it('should return wrapped layout if feature is enabled and all variables are false', () => {
    const layoutFeatureKey = 'pdp-color-swatch-layout-us-gap';
    const result = getLayoutFromFeatures({
      ...baseFeatureData,
      enabledFeatures: { [layoutFeatureKey]: true },
      featureVariables: { [layoutFeatureKey]: { default: false, stacked: false, block: false } },
    });
    expect(result).toBe(LAYOUTS.wrapped);
  });

  it('should use correct feature key for different brand and market', () => {
    const layoutFeatureKey = 'pdp-color-swatch-layout-ca-br';
    const result = getLayoutFromFeatures({
      ...baseFeatureData,
      brandName: 'br',
      market: 'ca',
      enabledFeatures: { [layoutFeatureKey]: true },
      featureVariables: { [layoutFeatureKey]: { default: true, stacked: false, block: false } },
    });
    expect(result).toBe(LAYOUTS.wrapped);
  });

  it('should return "block" layout if feature is enabled and "block" variable is true', () => {
    const layoutFeatureKey = 'pdp-color-swatch-layout-us-gap';
    const result = getLayoutFromFeatures({
      ...baseFeatureData,
      enabledFeatures: { [layoutFeatureKey]: true },
      featureVariables: { [layoutFeatureKey]: { default: false, stacked: false, block: true } },
    });
    expect(result).toBe(LAYOUTS.block);
  });

  it('should return "block" layout if both "block" and "stacked" are true, as block has priority', () => {
    const layoutFeatureKey = 'pdp-color-swatch-layout-us-gap';
    const result = getLayoutFromFeatures({
      ...baseFeatureData,
      enabledFeatures: { [layoutFeatureKey]: true },
      featureVariables: { [layoutFeatureKey]: { default: false, stacked: true, block: true } },
    });
    expect(result).toBe(LAYOUTS.block);
  });

  it('should return "wrapped" layout if "wrapped" is true, even if other layouts are also true', () => {
    const layoutFeatureKey = 'pdp-color-swatch-layout-us-gap';
    const result = getLayoutFromFeatures({
      ...baseFeatureData,
      enabledFeatures: { [layoutFeatureKey]: true },
      featureVariables: { [layoutFeatureKey]: { default: true, stacked: true, block: true } },
    });
    expect(result).toBe(LAYOUTS.wrapped);
  });
});
