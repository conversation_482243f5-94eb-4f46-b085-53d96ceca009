/* TODO: remove next line after increasing the test coverage for this file */
/* istanbul ignore file */
'use client';

import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { SelectorTile as TileSelectorComponent } from '@ecom-next/core/fabric/selector-tile';
import { ReportEasyEnrollLinkClickType } from '@product-page/components/product-page/collaborators/pdp-reporter';
import { ProductStore } from '@ecom-next/core/components/migration/bopis';
import { FulfillmentClientProps } from '../collaborators/mvg-types';
import { BackorderMessage } from '../BackorderMessage';
import { trackFulfillmentTag } from '../track-fulfillment-tag';
import { DoneParamsType, MvgStoreModalClient } from './MvgStorageModalClient';

const getAvailabilityClass = (status: boolean, isSelected: boolean) => {
  return `availability-status ${isSelected ? (status ? 'in-stock-msg' : 'out-stock-msg') : ''}`;
};

const MvgFulfillmentComponent = ({
  url: targetUrl,
  translations: { freeForRewardsMember, freeShipping, inStock, inStore, join, or: orString, outOfStock, signIn, changeStore, selectStore, bopisOutOfStock },
  isShippingAvailable,
  bopisAvailable,
  isValidSizeSelection,
  isHuiChangeStoreModal,
  personalizationContextData,
  reporter,
  locale,
  market,
  bopisData,
  brand,
  availabilityLoaded = true,
  postalCode,
  children,
  storeId,
  contentType,
  env,
  selectedSku,
  alert,
  handleUserFeedback,
  setTemporaryStore,
  onClose,
}: FulfillmentClientProps) => {
  const userIsNotSignedIn = !personalizationContextData?.userContext?.isLoggedInUser || personalizationContextData?.userContext?.isLoggedInUser === 'false';
  const targetUrlWithScapedCharacter = targetUrl?.replace('?', '\\?');
  const linkSignIn = useCallback(
    (label: string, reportClickCustomParam: ReportEasyEnrollLinkClickType, className?: string) => (
      <a
        href={`/my-account/sign-in?targetURL=${targetUrlWithScapedCharacter}`}
        onClick={() => reporter?.reportEasyEnrollLinkClick(reportClickCustomParam)}
        rel='noopener noreferrer'
        target='_blank'
        className={className}
      >
        {label}
      </a>
    ),
    [reporter, targetUrlWithScapedCharacter]
  );

  const createEasyEnrollType = (type: 'SignIn' | 'Join') => ({
    easy_enroll_type: `Product : BuyBox : ${type}`,
  });

  const signInLink = linkSignIn(signIn, createEasyEnrollType('SignIn'), 'with-underline');
  const joinLink = linkSignIn(join, createEasyEnrollType('Join'), 'with-underline');

  const signOrJoinLink = useMemo(
    () => (
      <>
        <span> {signInLink} </span>
        <span> {orString} </span>
        <span> {joinLink} </span>
      </>
    ),
    [signInLink, orString, joinLink]
  );

  const loyaltyEnrollShippingMessage = () => {
    if (userIsNotSignedIn) {
      return (
        <div>
          <div className='fulfillment-rewards-members' data-testid='pdp-loyalty-rewards-members'>{`${freeForRewardsMember} `}</div>
          <span className='fulfillment-link-sign-in-join' data-testid='pdp-sign-or-join-links'>
            {signOrJoinLink}
          </span>
        </div>
      );
    }
    return <div className='fulfillment-rewards-members'>{freeForRewardsMember}</div>;
  };

  const {
    selectedStore: store,
    active: bopisActive,
    setActive,
    setPostalCode: bopisSetPostalCode,
    setStores: bopisSetStores,
    setSelectedStore: bopisSetSelectedStore,
    setEnabled,
    bopisInitialed,
  } = bopisData;

  const [isOpenStoreChange, setIsOpenStoreChange] = useState(false);
  const { isBackOrdered = false, backOrderDate = '' } = selectedSku || {};

  const [selectedStore, setSelectedStore] = useState<ProductStore | null>(null);
  const [shouldEnableBopisTileSelector, setShouldEnableBopisTileSelector] = useState<boolean>(true);

  useEffect(() => {
    if (store) {
      setSelectedStore(store);
    }
  }, [store]);

  useEffect(() => {
    trackFulfillmentTag(!!bopisActive, bopisInitialed, reporter);
  }, [bopisActive, bopisInitialed]);

  const handleOpenModal = () => {
    setIsOpenStoreChange(true);
    reporter?.reportStoreChange();

    if (selectedStore) {
      setTemporaryStore(selectedStore);
    }
  };

  const handleClick = (isBopis: boolean) => {
    if (alert) {
      handleUserFeedback();
    }

    reporter?.reportFullfillmentSelection({ active: isBopis });

    if (!isBopis) {
      setActive(isBopis);
      setShouldEnableBopisTileSelector(true);
      return;
    }

    if (!isValidSizeSelection) {
      handleUserFeedback({
        mappedFeedback: 'fulfillment',
        kind: 'negative',
        customData: { id: 'bopis' },
      });
      return;
    }

    if (selectedStore) {
      setActive(isBopis);
    } else {
      handleOpenModal();
    }
  };

  const handleClickSelectStore = () => {
    if (alert) {
      handleUserFeedback();
    }

    if (isValidSizeSelection) {
      handleOpenModal();
    } else {
      handleUserFeedback({ mappedFeedback: 'fulfillment', kind: 'negative', customData: { id: 'bopis' } });
    }
  };

  const handleDoneToggle = useCallback(
    ({ zipCode, selectedStore: newSelectedStore, stores }: DoneParamsType) => {
      if (zipCode !== '') {
        bopisSetPostalCode(zipCode);
      }

      if (stores.length > 0) {
        bopisSetStores(stores);
      }

      if (newSelectedStore?.storeId !== selectedStore?.storeId) {
        bopisSetSelectedStore(newSelectedStore);
        setEnabled(true);
      }

      if (newSelectedStore) {
        setActive(true);
      }
      setShouldEnableBopisTileSelector(false);
    },
    [setEnabled, setActive, bopisSetSelectedStore, bopisSetPostalCode, bopisSetStores, selectedStore]
  );

  const isShippingSelected = !bopisActive && availabilityLoaded;
  const isBopisSelected = !!(isValidSizeSelection && bopisActive && selectedStore);

  return (
    <div className='pdp_fulfillment-container flex w-full justify-between gap-x-2' data-testid='fulfillment-container'>
      <TileSelectorComponent
        checked={isShippingSelected}
        data-testid='pdp-fulfillment-shipping'
        data-selected={isShippingSelected}
        id='shipping'
        group='group1'
        disabled={!availabilityLoaded}
        singleSelect
        className='w-full'
        onClick={() => handleClick(false)}
        onChange={() => {}}
      >
        <div className='flex h-full flex-col justify-between gap-4'>
          <div>
            <div className='fulfillment-title'>{freeShipping}</div>
            {loyaltyEnrollShippingMessage()}
          </div>
          {isValidSizeSelection && isBackOrdered && backOrderDate ? (
            <div className='backorder-message' data-testid='backorder-message'>
              <BackorderMessage date={backOrderDate} />
            </div>
          ) : (
            <div
              className={`${getAvailabilityClass(isShippingAvailable, isShippingSelected)} fulfillment-store-sentence-case-text`}
              data-testid='shipping-inventory-status'
            >
              {isValidSizeSelection && (isShippingAvailable ? inStock : outOfStock)}
            </div>
          )}
        </div>
      </TileSelectorComponent>

      <TileSelectorComponent
        checked={isBopisSelected}
        disabled={!availabilityLoaded && shouldEnableBopisTileSelector}
        id='pickup'
        group='group1'
        singleSelect
        className='w-full'
        data-testid='pdp-fulfillment-pickup'
        data-selected={isBopisSelected}
        onClick={() => handleClick(true)}
        onChange={() => {}}
      >
        <div className='flex h-full flex-col justify-between gap-4'>
          <div>
            <div className='fulfillment-title'>{inStore}</div>
            <div className='leading-[10px]'>
              {selectedStore && (
                <div className='store-name' data-testid='store-name'>
                  {selectedStore.storeName}
                </div>
              )}
              <button className='fulfillment-link' onClick={handleClickSelectStore} data-testid='change-store-modal-link' disabled={!availabilityLoaded}>
                {selectedStore ? changeStore : selectStore}
              </button>
            </div>
          </div>

          <div
            className={`${getAvailabilityClass(bopisAvailable, isBopisSelected)} fulfillment-store-sentence-case-text`}
            data-testid='pickup-inventory-status'
          >
            {availabilityLoaded && isValidSizeSelection && selectedStore && (bopisAvailable ? inStock : bopisOutOfStock)}
          </div>
        </div>
      </TileSelectorComponent>
      <MvgStoreModalClient
        locale={locale}
        market={market}
        brand={brand}
        postalCode={postalCode}
        storeId={storeId}
        setSelectedStore={setTemporaryStore}
        setIsOpenStoreChange={setIsOpenStoreChange}
        isOpenStoreChange={isOpenStoreChange}
        onDone={handleDoneToggle}
        contentType={contentType}
        selectedSku={selectedSku}
        env={env}
        onClose={onClose}
        isHuiChangeStoreModal={isHuiChangeStoreModal}
      >
        {children}
      </MvgStoreModalClient>
    </div>
  );
};

export const MvgFulfillmentClient = React.memo(MvgFulfillmentComponent);
