import { CID } from '@ecom-next/marketing-ui';
import { AppConfig } from '@ecom-next/sitewide/state-builder-configs';
import { Brands } from '@ecom-next/core/react-stitch';
import { LocalizationContextT } from '@ecom-next/sitewide/localization-provider';
import { BuyBoxState } from '../../../../providers/buybox-provider/BuyBoxContext';
import { FeaturesConfig } from '../../../../pages/getFeatureConfig';

type ProductStoreAddress = {
  addressLine1: string;
  addressLine2?: string;
  cityName: string;
  countryCode: string;
  phoneNumber: string;
  postalCode: string;
  stateProvinceCode: string;
};

export type ProductStore = {
  activeFeatureToggles: {
    curbsidePickup?: boolean;
    inStorePickup?: boolean;
  };
  latitude: string;
  longitude: string;
  storeAddress: ProductStoreAddress;
  storeDistance: string;
  storeHours: string[];
  storeId: string;
  storeName: string;
  storeTimeZone?: string;
};

export type ATBConfirmationModalProps = {
  abSeg: Record<string, string>;
  atbResponse: BuyBoxState['atbResponse'];
  brandCodeUrls: AppConfig['brandCodeUrls'];
  brandName: Brands;
  cid: CID;
  closeButtonClickHandler: () => void;
  displayPercentageOffFromCapi: boolean;
  isGiftCard?: boolean;
  isHuiLegacyAtbEnabled?: boolean;
  isMultiVariantProduct?: boolean;
  isOpen: boolean;
  isPercentageEnabled: boolean;
  recommendationsConfig: FeaturesConfig['recommendationsConfig'];
};

export type ATBConfirmationModalContentProps = {
  atbResponse: BuyBoxState['atbResponse'];
  brandCodeUrls: AppConfig['brandCodeUrls'];
  brandName: Brands;
  cid: CID;
  closeButtonClickHandler: () => void;
  displayPercentageOffFromCapi: boolean;
  isGiftCard?: boolean;
  isHuiLegacyAtbEnabled?: boolean;
  isMobile?: boolean;
  isMultiVariantProduct?: boolean;
  isPercentageEnabled: boolean;
  recommendationsConfig: FeaturesConfig['recommendationsConfig'];
};

export type DisplayAddToBagItemProps = {
  active?: boolean;
  atbResponse: BuyBoxState['atbResponse'];
  displayPercentageOffFromCapi: boolean;
  enabled: boolean;
  isGiftCard?: boolean;
  isPercentageEnabled: boolean;
  localize: LocalizationContextT['localize'];
  selectedStore?: ProductStore;
  warnings?: BuyBoxState['atbResponse']['warnings'];
};

export type AddToBagProductPriceProps = {
  isPercentageEnabled: boolean;
  isSalePrice: boolean;
  localizedRegularPrice: string;
  localizedSalePrice: string;
  percentageOffText: string;
};
