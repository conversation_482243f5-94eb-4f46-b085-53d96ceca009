'use client';
import React from 'react';
import classNames from 'classnames';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { Modal } from '@ecom-next/core/components/fabric/modal';
import { ATBConfirmationModalProps } from '../types';
import { useMvgAddToBagContext } from '../../add-to-bag/mvg/mvg-atb-provider/MvgAtbProvider';
import { MvgATBConfirmationModalContent } from './MvgATBConfirmationModalContent';

export const MvgATBConfirmationModal = ({
  isOpen,
  closeButtonClickHandler,
  atbResponse,
  isHuiLegacyAtbEnabled = false,
  isMultiVariantProduct = false,
}: Omit<
  ATBConfirmationModalProps,
  'brandName' | 'locale' | 'market' | 'abSeg' | 'cid' | 'brandCodeUrls' | 'displayPercentageOffFromCapi' | 'isPercentageEnabled' | 'recommendationsConfig'
>) => {
  const { localize } = useLocalize();
  const title = isHuiLegacyAtbEnabled ? localize('pdp.insitu.title.addToBag') : localize('pdp.atbConfirmation.title.addToBag.light');
  const closeModalAriaText = localize('pdp.closeModal.altText');
  const { isMobile } = useMvgAddToBagContext();
  const wrapperClasses = classNames(
    'atb-confirmation-modal-wrapper',
    { 'pdp_atb-confirmation-modal--legacy-design': isHuiLegacyAtbEnabled },
    { 'h-auto w-[21.625rem]': isMobile },
    { 'max-h-[47.063rem] w-[45.688rem]': !isMobile }
  );

  return (
    <Modal
      closeButtonAriaLabel={closeModalAriaText}
      callbackFn={closeButtonClickHandler}
      headerContent={title}
      headerAlignment={'center'}
      isCrossBrand={true}
      isOpen={isOpen}
      hasSkinnyHeader={true}
      className={wrapperClasses}
    >
      {atbResponse && (
        <MvgATBConfirmationModalContent
          atbResponse={atbResponse}
          closeButtonClickHandler={closeButtonClickHandler}
          isHuiLegacyAtbEnabled={isHuiLegacyAtbEnabled}
          isMultiVariantProduct={isMultiVariantProduct}
        />
      )}
    </Modal>
  );
};
