'use client';

import { BreakpointContext, LARGE } from '@ecom-next/core/breakpoint-provider';
import { InlineMarketing } from '@ecom-next/marketing-ui/product';
import React from 'react';
import { ScriptLoaderProvider } from '@pdp/packages/script-loader-provider';
import { useShallow } from 'zustand/shallow';
import { CertonaProvider } from '@ecom-next/core/components/legacy/certona-provider';
import { ProductRecommendationsProvider } from '@ecom-next/sitewide/product-recs-provider';
import { PDPRecsCarouselWrapper } from '../../../../pdp-recs-carousel-wrapper';
import { useMVGBuyBoxStore } from '../../../../../providers/mvg-buybox-provider';
import { useMvgAddToBagContext } from '../../add-to-bag/mvg/mvg-atb-provider/MvgAtbProvider';

export const MvgATBConfirmationMarketingContainer = ({ isMultiVariantProduct }: { isMultiVariantProduct?: boolean }) => {
  const atbConfirmationMarketingData = {
    instanceName: 'insitu-modal-marketing',
    name: 'Optimize',
    type: 'product',
  };

  const { selectedCustomerChoiceId } = useMVGBuyBoxStore(
    useShallow(state => ({
      selectedCustomerChoiceId: state.selectedCustomerChoice?.customer_choice_id ?? '',
    }))
  );

  const { cid, showInsituMarketingContainer, isPercentageEnabled, recommendationsConfig } = useMvgAddToBagContext();

  // eslint-disable-next-line no-console
  const errorLogger = console.error;
  const fallBackContent = (
    <>
      <InlineMarketing marketing={atbConfirmationMarketingData} cid={cid} />
      <BreakpointContext.Consumer>
        {({ greaterOrEqualTo }) => {
          if (greaterOrEqualTo(LARGE) || showInsituMarketingContainer) {
            return (
              <div className='recommendations-container relative z-0'>
                <CertonaProvider errorLogger={errorLogger} globalCertonaConfig={{ pagetype: 'ADDTOCART' }}>
                  <ScriptLoaderProvider>
                    <ProductRecommendationsProvider>
                      <PDPRecsCarouselWrapper
                        isPercentageEnabled={isPercentageEnabled}
                        recommendationsConfig={recommendationsConfig}
                        selectedCustomerChoiceId={selectedCustomerChoiceId}
                        pageType='ADDTOCART'
                        isMvg
                        isMultiVariantProduct={isMultiVariantProduct}
                      />
                    </ProductRecommendationsProvider>
                  </ScriptLoaderProvider>
                </CertonaProvider>
              </div>
            );
          }
          return null;
        }}
      </BreakpointContext.Consumer>
    </>
  );

  return fallBackContent;
};
