import { SearchParams } from '@product-page/pages/getPageState';
import { getPageContext } from '@ecom-next/utils/server';
import { Breakpoint, XLARGE } from '@ecom-next/core/components/breakpoint-provider';
import { ReactNode } from 'react';
import { MVGProductInfo } from '../../product-info/mvg/MVGProductInfo';
import { cacheableCapiDataV3Promise, cacheableFeaturesConfigMVG } from '../../../pages/getReWrittenData';
import { MVGColorPicker } from '../components/color-picker/mvg/MVGColorPicker';
import { MvgProductTitleClient } from '../../product-title';
import { MVGMarketingFlagClient } from '../components/marketing-flag/MVGMarketingFlagClient';
import { MvgFulfillment } from '../components/fulfillment/mvg/MvgFulfillment';
import { MVGPaymentMethods } from '../components/payment-methods/MVGPaymentMethods';
import { MVGAddToBagButton } from '../components/add-to-bag/mvg/MVGAddToBagButton';
import { MvgProductPrice } from '../components/product-price/mvg/MvgProductPrice';
import { MvgStarRatingsWithDrawer } from '../components/mvg-star-ratings/MvgStarRatingsWithDrawer';
import { MvgVariantGroupClient } from '../components/variant-group/mvg/MvgVariantGroupClient';
import { MvgUserFeedback } from '../components/user-feedback/mvg/MvgUserFeedback';
import { MvgExcludedFromPromotionsClient } from '../components/excluded-from-promotions/mvg/MvgExcludedFromPromotionsClient';
import { MvgDropshipShippingAndReturns } from '../components/dropship-shipping-and-returns/MvgDropshipShippingAndReturn';
import { MVGDimensionGroupsClient } from '../components/dimension-group/mvg/MVGDimensionGroupsClient';
import { MvgStickyContainerClient } from '../components/mvg-sticky-container/MvgStickyContainerClient';
import { MvgStickyContainerDetails } from '../components/mvg-sticky-container/MvgStickContainerDetails';
import { MvgBuyBoxClient } from './mvg-buy-box-client';

type MvgBuyBoxServerProps = {
  brand: string;
  productMarketing: ReactNode;
  requestParamString: string;
  searchParams: SearchParams;
};

export const MVGBuyBoxServer = async ({ requestParamString, searchParams, brand, productMarketing }: MvgBuyBoxServerProps) => {
  const [featureConfigs, capiData] = await Promise.all([cacheableFeaturesConfigMVG(requestParamString), cacheableCapiDataV3Promise(requestParamString)]);
  const containerPositions = { higher: 'a', lower: 'b' };

  const {
    isDropShipEnabled,
    isBopisDisplayable,
    dropshipShippingAndReturnsEnabled,
    reviews: { isReviewsDrawerEnabled },
    marketingContainerEnabled,
    marketingContainerPlacement,
    isAthletaHUIChanges,
  } = featureConfigs;

  const { isDesktop } = getPageContext();

  if (!capiData) {
    return <></>;
  }

  const sharedProps = { requestParamString, isMultiVariantProduct: !!capiData?.isMultiVariantProduct };

  const renderMobileMarketingContainer = (position: string) => {
    if (!marketingContainerEnabled || marketingContainerPlacement.mobile !== position) {
      return null;
    }

    return (
      <Breakpoint is='smallerThan' size={XLARGE}>
        {productMarketing}
      </Breakpoint>
    );
  };

  return (
    <MvgBuyBoxClient featureConfigs={featureConfigs} isDesktop={isDesktop} brandName={brand}>
      {!isDesktop && (
        <MvgStickyContainerClient>
          <MvgProductTitleClient isAthletaHUIChanges={isAthletaHUIChanges} />
          <div className='flex items-start'>
            <MvgStickyContainerDetails>
              <div className='pdp-title-price-wrapper'>
                <MvgProductPrice {...sharedProps} priceStyles='title-price' />
              </div>
            </MvgStickyContainerDetails>
            <MvgStarRatingsWithDrawer isReviewsDrawerEnabled={isReviewsDrawerEnabled} brandName={brand} searchParams={searchParams} {...sharedProps} />
          </div>
        </MvgStickyContainerClient>
      )}
      <div className='pdp-margin-bottom-components hidden sm:block'>
        <MvgProductTitleClient isAthletaHUIChanges={isAthletaHUIChanges} />
        <MVGMarketingFlagClient isStyleLevelFlag />
      </div>
      {!capiData.selectedStyle?.gift_card && (
        <div className='pdp-margin-top-s-components'>
          <div className='flex w-full justify-between'>
            <div className='pdp-sticky-container-breakpoint' id='pdp-sticky-container-breakpoint'></div>
            <div className='pdp-title-price-wrapper' data-testid='pdp-title-price-wrapper'>
              <MvgProductPrice {...sharedProps} priceStyles='title-price' />
              <MvgExcludedFromPromotionsClient isDropShipEnabled={isDropShipEnabled} />
            </div>
            <MvgStarRatingsWithDrawer isReviewsDrawerEnabled={isReviewsDrawerEnabled} brandName={brand} searchParams={searchParams} {...sharedProps} />
          </div>
          <MVGMarketingFlagClient isStyleLevelFlag={false} />
        </div>
      )}
      <div className='pdp-margin-bottom-components pdp-margin-top-components' id='pdp-color-picker'>
        <MVGColorPicker parentComponent='buybox' {...sharedProps} />
      </div>
      <div className='pdp-buy-box-size-selector-container pdp-margin-bottom-components flex flex-col'>
        <MvgVariantGroupClient>
          <MVGDimensionGroupsClient parentComponent='buybox' />
        </MvgVariantGroupClient>
      </div>
      {dropshipShippingAndReturnsEnabled && <MvgDropshipShippingAndReturns />}
      <MvgUserFeedback brandName={capiData.brand} />
      <div className='pdp-margin-bottom-components pdp-margin-top-components w-full'>
        <MVGAddToBagButton {...sharedProps} isBopisDisplayable={isBopisDisplayable} />
        <MVGPaymentMethods {...sharedProps} />
      </div>
      {isBopisDisplayable && (
        <div className='pdp-margin-bottom-components'>
          <MvgFulfillment searchParams={searchParams} />
        </div>
      )}

      {renderMobileMarketingContainer(containerPositions.higher)}

      <MVGProductInfo {...sharedProps} />

      {renderMobileMarketingContainer(containerPositions.lower)}
    </MvgBuyBoxClient>
  );
};
