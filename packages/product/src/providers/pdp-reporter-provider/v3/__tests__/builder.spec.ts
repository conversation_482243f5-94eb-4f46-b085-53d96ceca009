import { mvgProductAttributes, getMarketingFlagData, getNodeNameByType } from '../builder';

const VALIDATION_PATTERNS = {
  STRING_PATTERN: /^[a-zA-Z0-9\s\-_.:]+$/,
  NUMERIC_PATTERN: /^\d+(\.\d+)?$/,
  BOOLEAN_PATTERN: /^(true|false)$/,
  ARRAY_PATTERN: /^\[.*\]$/,
  ID_PATTERN: /^[a-zA-Z0-9\-_]+$/,
  PRICE_PATTERN: /^\d+(\.\d{2})?$/,
  DATE_PATTERN: /^\d{4}-\d{2}-\d{2}/,
};

describe('builder', () => {
  const commonMockData = {
    featureConfigs: { pdpMVGRefreshEnabled: true },
    isMultiVariantProduct: true,
    pageType: 'Product',
    brand: 'br',
    businessUnitId: 3,
    customer_choices: {
      '111111001': {
        customer_choice_id: '111111001',
        alternate_ids: {},
      },
    },
    styles: [],
    metadata: {},
    selectedNodes: [],
    searchParams: { cid: '', autosuggest: false, searchText: '', position: -1, results: 0 },
    breadcrumbs: [],
    dataLayerEngagement: {},
    selectedCustomerChoice: { marketing_flags: '', style_id: '123' },
    selectedMultiVariantData: { price: { min_regular_price: '' } },
    variants: {},
  };

  describe('mvgProductAttributes - Validation of data types', () => {
    test('Should validate all string attributes match expected patterns', () => {
      const data = mvgProductAttributes({
        ...commonMockData,
        selectedCustomerChoice: {
          customer_choice_id: '111111001',
          description: 'Test Product',
        },
        selectedStyle: { description: 'Style Description' },
        selectedNodes: [
          { type: 'division', name: 'Men' },
          { type: 'category', name: 'T-Shirts' },
        ],
        breadcrumbs: {
          category: { name: 'Casual Wear' },
          division: { name: 'Men' },
        },
      });

      expect(data.channel).toMatch(VALIDATION_PATTERNS.STRING_PATTERN);
      expect(data.page_name).toMatch(VALIDATION_PATTERNS.STRING_PATTERN);
      expect(data.page_type).toMatch(VALIDATION_PATTERNS.STRING_PATTERN);
      expect(data.product_brand[0]).toMatch(VALIDATION_PATTERNS.STRING_PATTERN);
      expect(data.product_category[0]).toMatch(VALIDATION_PATTERNS.STRING_PATTERN);
      expect(data.product_cc_id[0]).toMatch(VALIDATION_PATTERNS.ID_PATTERN);
      expect(data.product_name[0]).toMatch(VALIDATION_PATTERNS.STRING_PATTERN);
    });

    test('Should validate numeric attributes match expected patterns', () => {
      const data = mvgProductAttributes({
        ...commonMockData,
        selectedMultiVariantData: { price: { min_regular_price: '19.99' } },
        businessUnitId: 3,
      });

      expect(data.product_seller_id[0]).toMatch(VALIDATION_PATTERNS.NUMERIC_PATTERN);
      expect(data.product_mvg_price[0]).toMatch(VALIDATION_PATTERNS.PRICE_PATTERN);
    });

    test('Should validate boolean attributes match expected patterns', () => {
      const data = mvgProductAttributes({
        ...commonMockData,
        isDropShip: true,
      });

      expect(data.product_dropship[0]).toMatch(VALIDATION_PATTERNS.BOOLEAN_PATTERN);
    });

    test('Should validate array attributes are properly formatted', () => {
      const data = mvgProductAttributes({ ...commonMockData });

      expect(Array.isArray(data.product_brand)).toBe(true);
      expect(Array.isArray(data.product_category)).toBe(true);
      expect(Array.isArray(data.product_id)).toBe(true);
      expect(data.product_id).toHaveLength(1);
    });
  });

  describe('mvgProductAttributes - Edge cases and undefined', () => {
    test('Should handle undefined selectedStyle gracefully', () => {
      const data = mvgProductAttributes({
        ...commonMockData,
        selectedStyle: undefined,
      });

      expect(data.product_name[0]).toBe('undefined');
      expect(typeof data.product_name[0]).toBe('string');
    });

    test('Should handle undefined selectedCustomerChoice gracefully', () => {
      const data = mvgProductAttributes({
        ...commonMockData,
        selectedCustomerChoice: undefined,
      });

      expect(data.product_cc_id[0]).toBe('undefined');
      expect(data.product_marketing_flag).toBe('No flag');
    });

    test('Should handle undefined breadcrumbs gracefully', () => {
      const data = mvgProductAttributes({
        ...commonMockData,
        breadcrumbs: undefined,
      });

      expect(data.product_primary_category[0]).toBe('');
      expect(data.channel).toBe('br:');
    });

    test('Should handle undefined selectedNodes gracefully', () => {
      const data = mvgProductAttributes({
        ...commonMockData,
        selectedNodes: undefined,
      });

      expect(data.channel).toBe('br:');
      expect(data.product_category[0]).toBe('');
    });

    test('Should handle undefined metadata gracefully', () => {
      const data = mvgProductAttributes({
        ...commonMockData,
        metadata: undefined,
      });

      expect(data.product_page_type).toEqual(['Standard PDP']);
    });

    test('Should handle undefined styles gracefully', () => {
      const data = mvgProductAttributes({
        ...commonMockData,
        styles: undefined,
      });

      expect(data.product_id).toEqual(['']);
    });

    test('Should handle undefined params gracefully', () => {
      const data = mvgProductAttributes({
        ...commonMockData,
        params: undefined,
      });

      expect(data.page_name).toContain(':product:');
    });

    test('Should handle null values in nested objects', () => {
      const data = mvgProductAttributes({
        ...commonMockData,
        selectedCustomerChoice: {
          customer_choice_id: null,
          description: null,
          marketing_flags: null,
        },
        selectedStyle: { description: null },
      });

      expect(data.product_cc_id[0]).toBe('null');
      expect(data.product_name[0]).toBe('null');
      expect(data.product_marketing_flag).toBe('No flag');
    });
  });

  describe('mvgProductAttributes - Validation of methods and calls', () => {
    test('Should call getNodeNameByType correctly for division and category', () => {
      const selectedNodes = [
        { type: 'division', name: 'Men' },
        { type: 'category', name: 'T-Shirts' },
        { type: 'sale', name: 'Clearance' },
      ];

      const data = mvgProductAttributes({
        ...commonMockData,
        selectedNodes,
      });

      expect(data.channel).toBe('br:Men');
      expect(data.product_category[0]).toBe('T-Shirts');
    });

    test('Should call getMarketingFlagData correctly', () => {
      const data = mvgProductAttributes({
        ...commonMockData,
        selectedCustomerChoice: {
          marketing_flags: [{ content: 'New Arrival' }],
        },
      });

      expect(data.product_marketing_flag).toBe('New Arrival');
    });

    test('Should call getProductPageType correctly', () => {
      const data = mvgProductAttributes({
        ...commonMockData,
        metadata: {
          variant_group: true,
          includes_merged_style: true,
        },
      });

      expect(data.product_page_type).toEqual(['MVG PDP : Merged Styles']);
    });

    test('Should call getProductInventoryStatusProp correctly', () => {
      const data = mvgProductAttributes({
        ...commonMockData,
        selectedCustomerChoice: {
          skus: [
            {
              sku_id: '123',
              inventory_status: { status: 'IN_STOCK' },
            },
          ],
        },
      });

      expect(data).toHaveProperty('product_inventory_status');
    });

    test('Should call getOutOfStockItems correctly', () => {
      const data = mvgProductAttributes({
        ...commonMockData,
        variants: {
          'XLARGE|SHORTSLEEVE': {
            customer_choices: {
              '811426002': {
                skus: [
                  {
                    sku_id: '8114260020304',
                    inventory_status: { status: 'OUT_OF_STOCK' },
                  },
                ],
              },
            },
          },
        },
      });

      expect(typeof data.pdp_oos_size).toBe('string');
    });

    test('Should call lpoData correctly', () => {
      const data = mvgProductAttributes({
        ...commonMockData,
        template: 'LPO',
        selectedCustomerChoice: {
          skus: [
            {
              sku_id: '123',
              inventory_status: { status: 'IN_STOCK' },
            },
          ],
        },
      });

      expect(data).toHaveProperty('product_lpo_type');
      expect(data.product_lpo_type).toBe('PDP + Certona Template');
    });
  });

  describe('mvgProductAttributes - Validation of all attributes', () => {
    test('Should include all required attributes with correct types', () => {
      const data = mvgProductAttributes({
        ...commonMockData,
        selectedCustomerChoice: {
          customer_choice_id: '111111001',
          description: 'Test Product',
          marketing_flags: [{ content: 'New Arrival' }],
          color_palette_season_code: 'SPRING',
          vendor_details: { vendor_number: '123', vendor_name: 'VendorName' },
        },
        selectedStyle: { description: 'Style Description' },
        selectedNodes: [
          { type: 'division', name: 'Men' },
          { type: 'category', name: 'T-Shirts' },
        ],
        breadcrumbs: {
          category: { name: 'Casual Wear' },
          division: { name: 'Men' },
        },
        metadata: {
          variant_group_id: '12345',
          includes_merged_style: true,
        },
        selectedMultiVariantData: { price: { min_regular_price: '19.99' } },
        selectedMultiVariantKey: 'XLARGE|SHORTSLEEVE',
        variants: {},
      });

      const requiredAttributes = [
        'channel',
        'page_name',
        'page_type',
        'pdp_oos_size',
        'product_brand',
        'product_category',
        'product_cc_id',
        'product_name',
        'product_page_type',
        'product_primary_category',
        'product_marketing_flag',
        'product_season_code',
        'product_mvg_id',
        'product_variant_selected',
        'product_mvg_price',
        'product_dropship',
        'product_seller_id',
        'product_seller_name',
        'product_id',
      ];

      requiredAttributes.forEach(attr => {
        expect(data).toHaveProperty(attr);
        expect(data[attr]).not.toBeUndefined();
      });
    });

    test('Should handle optional attributes correctly when not provided', () => {
      const data = mvgProductAttributes({
        ...commonMockData,
        draprEnabled: false,
        isFitOnly: false,
        dataLayerEngagement: null,
        aiReviews: null,
        findMineEngagement: null,
        isFullyOOS: false,
      });

      expect(data).not.toHaveProperty('drapr_interaction');
      expect(data).not.toHaveProperty('drapr_version');
      expect(data).not.toHaveProperty('ai_review_summary_model');
      expect(data).not.toHaveProperty('findmine_engagement');
      expect(data).not.toHaveProperty('product_inventory_status');
    });

    test('Should adjust product_variant_selected when multiple options are selected', () => {
      const data = mvgProductAttributes({
        ...commonMockData,
        selectedMultiVariantKey: 'XLARGE|SHORTSLEEVE',
        variants: {
          'XLARGE|SHORTSLEEVE': {
            customer_choices: {
              '811426002': {
                skus: [
                  {
                    sku_id: '8114260020304',
                    inventory_status: { status: 'IN_STOCK', estimated_ship_ts: null },
                  },
                ],
              },
            },
            variantKey: 'XLARGE|SHORTSLEEVE',
          },
        },
      });

      expect(data.product_variant_selected).toEqual(['XLARGE:SHORTSLEEVE']);
    });

    test('Should not make any changes to product_variant_selected if only one is selected', () => {
      const data = mvgProductAttributes({
        ...commonMockData,
        selectedMultiVariantKey: 'XLARGE',
        variants: {
          XLARGE: {
            customer_choices: {
              '811426002': {
                skus: [
                  {
                    sku_id: '8114260020304',
                    inventory_status: { status: 'IN_STOCK', estimated_ship_ts: null },
                  },
                ],
              },
            },
            variantKey: 'XLARGE',
          },
        },
      });

      expect(data.product_variant_selected).toEqual(['XLARGE']);
    });

    test('Should report MVG attributes when pdpMVGRefreshEnabled is true and product is MVG', () => {
      const mockData = {
        featureConfigs: { pdpMVGRefreshEnabled: true },
        isMultiVariantProduct: true,
        pageType: 'Product',
        brand: 'br',
        styles: [],
        metadata: {},
        params: { cid: '', autosuggest: false, searchText: '', position: -1, results: 0 },
        selectedCustomerChoice: { marketing_flags: '' },
        selectedMultiVariantData: { price: { min_regular_price: '' } },
        selectedMultiVariantKey: 'XLARGE|SHORTSLEEVE',
        variants: {
          'XLARGE|SHORTSLEEVE': {
            customer_choices: {
              '811426002': {
                skus: [
                  {
                    sku_id: '8114260020304',
                    inventory_status: { status: 'IN_STOCK', estimated_ship_ts: null },
                  },
                ],
              },
            },
            variantKey: 'XLARGE|SHORTSLEEVE',
          },
        },
      };

      const data = mvgProductAttributes({ ...mockData });

      expect(data.product_variant_selected).toBeDefined();
      expect(data.product_mvg_id).toBeDefined();
      expect(data.product_mvg_price).toBeDefined();
    });

    test('Should not report MVG attributes when pdpMVGRefreshEnabled is false', () => {
      const mockData = {
        featureConfigs: { pdpMVGRefreshEnabled: false },
        isMultiVariantProduct: false,
        pageType: 'Product',
        brand: 'br',
        styles: [],
        metadata: {},
        params: { cid: '', autosuggest: false, searchText: '', position: -1, results: 0 },
        selectedCustomerChoice: { marketing_flags: '' },
        variants: {
          XLARGE: {
            customer_choices: {
              '811426002': {
                skus: [
                  {
                    sku_id: '8114260020304',
                    inventory_status: { status: 'IN_STOCK', estimated_ship_ts: null },
                  },
                ],
              },
            },
            variantKey: 'XLARGE',
          },
        },
      };

      const data = mvgProductAttributes({ ...mockData });

      expect(data.product_variant_selected).toBeUndefined();
      expect(data.product_mvg_id).toBeUndefined();
      expect(data.product_mvg_price).toBeUndefined();
    });

    test('Should not report MVG attributes when pdpMVGRefreshEnabled is true but product is not MVG', () => {
      const mockData = {
        featureConfigs: { pdpMVGRefreshEnabled: true },
        isMultiVariantProduct: false,
        pageType: 'Product',
        brand: 'br',
        styles: [],
        metadata: {},
        params: { cid: '', autosuggest: false, searchText: '', position: -1, results: 0 },
        selectedCustomerChoice: { marketing_flags: '' },
        variants: {
          XLARGE: {
            customer_choices: {
              '811426002': {
                skus: [
                  {
                    sku_id: '8114260020304',
                    inventory_status: { status: 'IN_STOCK', estimated_ship_ts: null },
                  },
                ],
              },
            },
            variantKey: 'XLARGE',
          },
        },
      };

      const data = mvgProductAttributes({ ...mockData });

      expect(data.product_variant_selected).toBeUndefined();
      expect(data.product_mvg_id).toBeUndefined();
      expect(data.product_mvg_price).toBeUndefined();
    });
  });
});

describe('getNodeNameByType', () => {
  const selectedNodes = [
    { type: 'division', name: 'Men' },
    { type: 'category', name: 'T-Shirts' },
    { type: 'sale', name: 'Clearance' },
  ];

  it('should return the name of the node for a given type', () => {
    expect(getNodeNameByType(selectedNodes, ['category'])).toBe('T-Shirts');
  });

  it('should return the name of the first matching node if multiple types are provided', () => {
    expect(getNodeNameByType(selectedNodes, ['category', 'division'])).toBe('Men');
  });

  it('should return an empty string if no node with the given type is found', () => {
    expect(getNodeNameByType(selectedNodes, ['sub-category'])).toBe('');
  });

  it('should return an empty string for an empty nodes array', () => {
    expect(getNodeNameByType([], ['category'])).toBe('');
  });

  it('should handle undefined selectedNodes', () => {
    expect(getNodeNameByType(undefined, ['category'])).toBe('');
  });

  it('should handle null selectedNodes', () => {
    expect(getNodeNameByType(null, ['category'])).toBe('');
  });

  it('should handle undefined types array', () => {
    expect(getNodeNameByType(selectedNodes, undefined)).toBe('');
  });

  it('should handle empty types array', () => {
    expect(getNodeNameByType(selectedNodes, [])).toBe('');
  });

  it('should validate node name matches string pattern', () => {
    const result = getNodeNameByType(selectedNodes, ['category']);
    expect(result).toMatch(VALIDATION_PATTERNS.STRING_PATTERN);
  });
});

describe('getMarketingFlagData', () => {
  it('should return the marketing flag when it exists', () => {
    const customerChoice = {
      marketing_flags: [{ content: 'New Arrival' }],
    };
    expect(getMarketingFlagData(customerChoice)).toEqual({ product_marketing_flag: 'New Arrival' });
  });

  it('should return "No flag" when marketing_flags content is null or undefined', () => {
    const customerChoice = {
      marketing_flags: [{ content: null }],
    };
    expect(getMarketingFlagData(customerChoice)).toEqual({ product_marketing_flag: 'No flag' });
  });

  it('should return "No flag" when marketing_flags is an empty array', () => {
    const customerChoice = {
      marketing_flags: [],
    };
    expect(getMarketingFlagData(customerChoice)).toEqual({ product_marketing_flag: 'No flag' });
  });

  it('should return "No flag" when marketing_flags is not present', () => {
    const customerChoice = {};
    expect(getMarketingFlagData(customerChoice)).toEqual({ product_marketing_flag: 'No flag' });
  });

  it('should handle undefined customerChoice', () => {
    expect(getMarketingFlagData(undefined)).toEqual({ product_marketing_flag: 'No flag' });
  });

  it('should handle null customerChoice', () => {
    expect(getMarketingFlagData(null)).toEqual({ product_marketing_flag: 'No flag' });
  });

  it('should handle marketing_flags with undefined content', () => {
    const customerChoice = {
      marketing_flags: [{ content: undefined }],
    };
    expect(getMarketingFlagData(customerChoice)).toEqual({ product_marketing_flag: 'No flag' });
  });

  it('should validate marketing flag content matches string pattern', () => {
    const customerChoice = {
      marketing_flags: [{ content: 'New Arrival 2024' }],
    };
    const result = getMarketingFlagData(customerChoice);
    expect(result.product_marketing_flag).toMatch(VALIDATION_PATTERNS.STRING_PATTERN);
  });
});

describe('productObject', () => {
  const commonMockData = {
    featureConfigs: { pdpMVGRefreshEnabled: true },
    isMultiVariantProduct: true,
    pageType: 'Product',
    brand: 'br',
    businessUnitId: 3,
    customer_choices: {
      '111111001': {
        customer_choice_id: '111111001',
        alternate_ids: {},
      },
    },
    styles: {
      style1: { style_id: 'style1', primary_style_id: '123' },
      style2: { style_id: 'style2', primary_style_id: '456' },
      123456: { style_id: '123456', primary_style_id: '789' },
    },
    selectedSku: { sku_id: '123456' },
    metadata: { variant_group_id: '12345', includes_merged_style: true },
    selectedNodes: [
      { type: 'division', name: 'Men' },
      { type: 'category', name: 'T-Shirts' },
    ],
    searchParams: { cid: '123', autosuggest: false, searchText: '', position: -1, results: 0 },
    breadcrumbs: { category: { name: 'Casual Wear' }, division: { name: 'Men' } },
    dataLayerEngagement: {},
    selectedCustomerChoice: {
      customer_choice_id: '111111001',
      description: 'CC Product Description',
      skus: [],
      marketing_flags: [],
      color_palette_season_code: 'SPRING',
      vendor_details: { vendor_number: '123', vendor_name: 'VendorName' },
      style_id: '123456',
    },
    selectedStyle: { description: 'Style Product Description' },
    selectedMultiVariantData: { price: { min_regular_price: '19.99' } },
    selectedMultiVariantKey: 'XLARGE|SHORTSLEEVE',
    variants: {},
  };

  test('Should generate correct productObject with multi-variant data', () => {
    const data = mvgProductAttributes(commonMockData);

    expect(data).toMatchObject({
      channel: 'br:Men',
      page_name: 'br:browse:Men:T-Shirts:123:product:Style Product Description:',
      page_type: 'Product',
      pdp_oos_size: '',
      product_brand: ['BR'],
      product_category: ['T-Shirts'],
      product_cc_id: ['111111001'],
      product_name: ['Style Product Description'],
      product_page_type: ['Super PDP'],
      product_primary_category: ['Casual Wear'],
      product_mvg_id: ['12345'],
      product_variant_selected: ['XLARGE:SHORTSLEEVE'],
      product_mvg_price: ['19.99'],
      product_dropship: ['false'],
      product_seller_id: ['3'],
      product_seller_name: ['BR'],
      product_id: ['789'],
      product_marketing_flag: 'No flag',
      product_season_code: 'SPRING',
    });
  });

  test('Should get the primary selling style id of selectedCustomerChoice', () => {
    const data = mvgProductAttributes({
      ...commonMockData,
      selectedCustomerChoice: {
        style_id: 'style1',
      },
    });

    expect(data.product_id).toEqual(['123']);
  });

  test('Should handle missing optional fields gracefully', () => {
    const data = mvgProductAttributes({
      ...commonMockData,
      selectedCustomerChoice: {},
      breadcrumbs: {},
      styles: [],
    });

    expect(data).toMatchObject({
      channel: 'br:Men',
      page_name: 'br:browse:Men:T-Shirts:123:product:Style Product Description:',
      page_type: 'Product',
      pdp_oos_size: '',
      product_brand: ['BR'],
      product_category: ['T-Shirts'],
      product_cc_id: ['undefined'],
      product_name: ['Style Product Description'],
      product_page_type: ['Super PDP'],
      product_primary_category: [''],
      product_dropship: ['false'],
      product_seller_id: ['3'],
      product_seller_name: ['BR'],
      product_id: [''],
      product_marketing_flag: 'No flag',
    });
  });

  test('Should include vendor details when isDropShip is true', () => {
    const data = mvgProductAttributes({
      ...commonMockData,
      isDropShip: true,
    });

    expect(data).toMatchObject({
      product_dropship: ['true'],
      product_seller_id: ['123'],
      product_seller_name: ['VendorName'],
    });
  });
});

describe('outOfStock', () => {
  const commonMockData = {
    pageType: 'product',
    brand: 'br',
    businessUnitId: 3,
    styles: [],
    metadata: {},
    selectedNodes: [],
    searchParams: { cid: '', autosuggest: false, searchText: '', position: -1, results: 0 },
    breadcrumbs: [],
    dataLayerEngagement: {},
    featureConfigs: {},
    selectedCustomerChoice: { marketing_flags: '' },
    selectedMultiVariantData: { price: { min_regular_price: '' } },
    isFullyOOS: true,
    variants: {
      'XLARGE|SHORTSLEEVE': {
        customer_choices: {
          '811426002': {
            skus: [
              {
                sku_id: '8114260020304',
                inventory_status: { status: 'OUT_OF_STOCK', estimated_ship_ts: null },
              },
            ],
          },
        },
        variantKey: 'XLARGE|SHORTSLEEVE',
      },
    },
    selectedMultiVariantKey: 'XLARGE|SHORTSLEEVE',
  };

  test('Should include outOfStock data when isFullyOOS is true and isOOSFeatureEnabled is true', () => {
    const data = mvgProductAttributes({
      ...commonMockData,
      featureConfigs: { isOOSFeatureEnabled: true },
    });

    expect(data).toMatchObject({
      product_inventory_status: 'out of stock',
      fulfillment_display: false,
      fulfillment_type: 'Ship to an Address',
      pagetype_fulfillment_status_type: 'Product|Impression|Ship to an Address',
      product_lpo_type: 'OOS PDP template',
    });
  });

  test('Should include outOfStock data when isFullyOOS is true and isSpecialOOSEnabled is true', () => {
    const data = mvgProductAttributes({
      ...commonMockData,
      featureConfigs: { isSpecialOOSEnabled: true },
    });

    expect(data).toMatchObject({
      product_inventory_status: 'out of stock',
      fulfillment_display: false,
      fulfillment_type: 'Ship to an Address',
      pagetype_fulfillment_status_type: 'Product|Impression|Ship to an Address',
      product_lpo_type: 'Sold Out PDP Experience',
    });
  });

  test('Should not include outOfStock data when isFullyOOS is false', () => {
    const data = mvgProductAttributes({
      ...commonMockData,
      isFullyOOS: false,
      featureConfigs: { isOOSFeatureEnabled: true },
    });

    expect(data).not.toHaveProperty('product_inventory_status');
    expect(data).not.toHaveProperty('fulfillment_display');
    expect(data).not.toHaveProperty('fulfillment_type');
    expect(data).not.toHaveProperty('pagetype_fulfillment_status_type');
    expect(data).not.toHaveProperty('product_lpo_type');
  });

  test('Should not include outOfStock data when isOOSFeatureEnabled and isSpecialOOSEnabled are false', () => {
    const data = mvgProductAttributes({
      ...commonMockData,
      featureConfigs: { isOOSFeatureEnabled: false, isSpecialOOSEnabled: false },
    });

    expect(data).not.toHaveProperty('product_inventory_status');
    expect(data).not.toHaveProperty('fulfillment_display');
    expect(data).not.toHaveProperty('fulfillment_type');
    expect(data).not.toHaveProperty('pagetype_fulfillment_status_type');
    expect(data).not.toHaveProperty('product_lpo_type');
  });
});

describe('Default export function', () => {
  test('Should handle reporterContext.add correctly', () => {
    const mockReporterContext = {
      add: jest.fn(),
      link: jest.fn(),
    };

    const props = {
      reporterContext: mockReporterContext,
      productAttributesResult: { test: 'data' },
      draprEnabled: false,
      isFitOnly: false,
    };

    const defaultExport = require('../builder').default;
    defaultExport(props);

    expect(mockReporterContext.add).toHaveBeenCalledWith({
      data: { test: 'data' },
      name: 'product mvg',
    });
  });

  test('Should handle draprEnabled true correctly', () => {
    const mockReporterContext = {
      add: jest.fn(),
      link: jest.fn(),
    };

    const props = {
      reporterContext: mockReporterContext,
      draprEnabled: true,
      isFitOnly: false,
      brand: 'br',
      styles: [{ style_id: 'style1' }],
      productAttributesResult: {
        product_brand: ['BR'],
        product_name: ['Test Product'],
        product_primary_category: ['Test Category'],
      },
    };

    const defaultExport = require('../builder').default;
    defaultExport(props);

    expect(mockReporterContext.link).toHaveBeenCalledWith({
      drapr_interaction: 'default prodview',
      event_name: 'default-product-view',
      product_brand: ['BR'],
      product_id: undefined,
      product_name: ['Test Product'],
      product_primary_category: ['Test Category'],
    });
  });

  test('Should handle isFitOnly true correctly', () => {
    const mockReporterContext = {
      add: jest.fn(),
      link: jest.fn(),
    };

    const props = {
      reporterContext: mockReporterContext,
      draprEnabled: true,
      isFitOnly: true,
      brand: 'br',
      styles: [{ style_id: 'style1' }],
      productAttributesResult: {
        product_brand: ['BR'],
        product_name: ['Test Product'],
        product_primary_category: ['Test Category'],
      },
    };

    const defaultExport = require('../builder').default;
    defaultExport(props);

    expect(mockReporterContext.link).toHaveBeenCalledWith({
      drapr_interaction: 'default prodview',
      event_name: 'default-product-view',
      product_brand: ['BR'],
      product_id: undefined,
      product_name: ['Test Product'],
      product_primary_category: ['Test Category'],
      drapr_version: 'fit-only',
    });
  });

  test('Should handle undefined productAttributesResult', () => {
    const mockReporterContext = {
      add: jest.fn(),
      link: jest.fn(),
    };

    const props = {
      reporterContext: mockReporterContext,
      draprEnabled: false,
      isFitOnly: false,
      brand: 'br',
      styles: [],
      searchParams: { cid: '', autosuggest: false, searchText: '', position: -1, results: 0 },
      selectedNodes: [],
      breadcrumbs: {},
      metadata: {},
      selectedCustomerChoice: {},
      selectedStyle: {},
      selectedMultiVariantData: { price: { min_regular_price: '' } },
      variants: {},
      featureConfigs: {},
    };

    const defaultExport = require('../builder').default;
    defaultExport(props);

    expect(mockReporterContext.add).toHaveBeenCalledWith({
      data: expect.any(Object),
      name: 'product mvg',
    });
  });

  test('Should validate all data passed to reporterContext matches expected patterns', () => {
    const mockReporterContext = {
      add: jest.fn(),
      link: jest.fn(),
    };

    const props = {
      reporterContext: mockReporterContext,
      draprEnabled: true,
      isFitOnly: false,
      brand: 'br',
      styles: [{ style_id: 'style1' }],
      productAttributesResult: {
        product_brand: ['BR'],
        product_name: ['Test Product'],
        product_primary_category: ['Test Category'],
      },
    };

    const defaultExport = require('../builder').default;
    defaultExport(props);

    const addCall = mockReporterContext.add.mock.calls[0][0];
    const linkCall = mockReporterContext.link.mock.calls[0][0];

    expect(addCall.data.product_brand[0]).toMatch(VALIDATION_PATTERNS.STRING_PATTERN);
    expect(linkCall.product_brand[0]).toMatch(VALIDATION_PATTERNS.STRING_PATTERN);
    expect(linkCall.product_name[0]).toMatch(VALIDATION_PATTERNS.STRING_PATTERN);
  });
});
