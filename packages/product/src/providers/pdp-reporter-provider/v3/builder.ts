// @ts-nocheck
'use client';

import { getProductPageType } from '../../../util/get-merge-type';
import { getProductInventoryStatusProp } from '../../../providers/pdp-reporter-provider/v3/get-inventory-status';
import { toNumeric } from '../../../components/buy-box/components/add-to-bag/helpers/price-utils';
import { getOutOfStockItems } from './get-out-of-stock-items';
import { lpoData } from './lpo-data';

const getNodeNameByType = (selectedNodes, types) => {
  if (!selectedNodes || !Array.isArray(selectedNodes) || !types || !Array.isArray(types)) {
    return '';
  }
  const node = selectedNodes.find(n => types.includes(n.type));
  return node?.name || '';
};

const getMarketingFlagData = customerChoice => {
  const noFlag = 'No flag';
  if (!customerChoice || !customerChoice.marketing_flags) {
    return { product_marketing_flag: noFlag };
  }
  const hasMarketingFlag = Array.isArray(customerChoice.marketing_flags) && customerChoice.marketing_flags.length > 0;
  return { product_marketing_flag: hasMarketingFlag ? customerChoice.marketing_flags[0].content ?? noFlag : noFlag };
};

const mvgProductAttributes = props => {
  const {
    pageType,
    brand,
    businessUnitId,
    selectedStyle,
    selectedMultiVariantData,
    metadata,
    draprEnabled,
    isFitOnly,
    selectedNodes = [],
    searchParams,
    breadcrumbs,
    styles,
    dataLayerEngagement,
    featureConfigs,
    findMineEngagement,
    isDropShip,
    template,
    selectedMultiVariantKey,
    selectedCustomerChoice,
    variants,
    isMultiVariantProduct,
    isFullyOOS,
  } = props || { featureConfigs: {} };

  const { cid = '', autosuggest = false, searchText = '', position = -1, results = 0 } = searchParams || {};

  const { pdpMVGRefreshEnabled, aiReviews, isOOSFeatureEnabled, isSpecialOOSEnabled } = featureConfigs || {};
  const isOutOfStock = (isOOSFeatureEnabled || isSpecialOOSEnabled) && isFullyOOS;

  const brandName = brand;

  const productName = selectedStyle?.description;

  const variantSkus = selectedCustomerChoice?.skus || [];

  const divisionName = getNodeNameByType(selectedNodes, ['division', 'sub-division']);
  const categoryName = getNodeNameByType(selectedNodes, ['category', 'sale']);

  const primaryCategoryName = breadcrumbs?.category?.name || '';
  const divisionNameFromBreadcrums = props?.breadcrumbs?.division?.name !== undefined ? props.breadcrumbs.division.name : '';

  const pageName = `${divisionName || divisionNameFromBreadcrums}:${categoryName}:${cid}:product:${productName || primaryCategoryName}:`;

  const sizesOutOfStock = getOutOfStockItems(variants);
  const productInventoryStatusProp = getProductInventoryStatusProp(variantSkus);

  const marketingFlagData = getMarketingFlagData(selectedCustomerChoice);

  const marketingFlagSessionCodeData = selectedCustomerChoice?.color_palette_season_code
    ? {
        product_season_code: selectedCustomerChoice?.color_palette_season_code,
      }
    : {};

  const vendorDetails = selectedCustomerChoice?.vendor_details || {};

  const mvgMultiVariantProps =
    pdpMVGRefreshEnabled && isMultiVariantProduct
      ? {
          product_mvg_id: [`${metadata?.variant_group_id || ''}`],
          product_variant_selected: [selectedMultiVariantKey?.replaceAll('|', ':')],
          product_mvg_price: [toNumeric(selectedMultiVariantData?.price?.min_regular_price || 0).toFixed(2)],
        }
      : {};

  const primaryStyleId = styles?.[selectedCustomerChoice?.style_id]?.primary_style_id || selectedStyle?.primary_style_id || '';
  const productObject = {
    channel: `${brandName}:${divisionName || divisionNameFromBreadcrums}`,
    page_name: `${brandName}:browse:${pageName}`,
    page_type: `${pageType?.charAt(0)?.toUpperCase() || ''}${pageType?.substring(1) || ''}`,
    pdp_oos_size: `${sizesOutOfStock}`,
    product_brand: [`${brandName?.toUpperCase() || ''}`],
    product_category: [`${categoryName}`],
    product_cc_id: [`${selectedCustomerChoice?.customer_choice_id}`],
    product_name: [`${productName}`],
    product_page_type: getProductPageType(metadata?.variant_group, metadata?.includes_merged_style),
    product_primary_category: [`${primaryCategoryName}`],
    ...productInventoryStatusProp,
    ...marketingFlagData,
    ...marketingFlagSessionCodeData,
    ...mvgMultiVariantProps,
    product_dropship: [`${!!isDropShip}`],
    product_seller_id: [`${isDropShip ? vendorDetails.vendor_number : businessUnitId}`],
    product_seller_name: [isDropShip ? vendorDetails.vendor_name : brandName.toUpperCase()],
    product_id: [primaryStyleId],
  };

  const autosuggestVisualObject = autosuggest
    ? {
        internal_search_autosuggest_flag: autosuggest,
        internal_search_keyword: `${brandName}:autosuggest_visual:${searchText} -> position ${position}`,
        internal_search_results: results,
      }
    : null;

  const draprTrackingInfo = draprEnabled
    ? {
        drapr_interaction: 'default prodview',
      }
    : null;

  const draprVersion = isFitOnly
    ? {
        drapr_version: 'fit-only',
      }
    : null;

  const dataLayerInfo = dataLayerEngagement && {
    ...dataLayerEngagement.starRatingsData,
    ...dataLayerEngagement.badgingData,
  };

  const { display: isAIReviewSummaryPresent, enabled: isAIReviewSummaryEnabled, modelNameForTracking: aiReviewSummaryModel } = aiReviews || {};

  const reviewSummaryReporting = isAIReviewSummaryEnabled
    ? {
        ai_review_summary_model: isAIReviewSummaryPresent ? [aiReviewSummaryModel] : ['summary not available'],
      }
    : null;

  const findMineBuyClick = findMineEngagement
    ? {
        event_name: 'findmine_click',
        findmine_engagement: 'Product.do : FindMine : W1',
        findmine_name_rank: 'Product.do : FindMine : W1',
      }
    : null;

  const outOfStock = isOutOfStock
    ? {
        product_inventory_status: 'out of stock',
        fulfillment_display: false,
        fulfillment_type: 'Ship to an Address',
        pagetype_fulfillment_status_type: 'Product|Impression|Ship to an Address',
        product_lpo_type: featureConfigs?.isSpecialOOSEnabled ? 'Sold Out PDP Experience' : 'OOS PDP template',
      }
    : null;

  return {
    ...productObject,
    ...autosuggestVisualObject,
    ...dataLayerInfo,
    ...draprTrackingInfo,
    ...findMineBuyClick,
    ...draprVersion,
    ...lpoData(template, variantSkus),
    ...reviewSummaryReporting,
    ...outOfStock,
  };
};

export { mvgProductAttributes, getNodeNameByType, getMarketingFlagData };

export default ({ reporterContext, ...props }) => {
  const initProductData = props.productAttributesResult || mvgProductAttributes(props);

  const hasDrapr = props.draprEnabled;
  const draprData = {
    drapr_interaction: 'default prodview',
    event_name: 'default-product-view',
    product_brand: initProductData.product_brand,
    product_id: initProductData?.styles?.filter(style => style.style_id).map(style => style.style_id),
    product_name: initProductData.product_name,
    product_primary_category: initProductData.product_primary_category,
  };

  const draprVersion = props.isFitOnly ? { drapr_version: 'fit-only' } : {};
  reporterContext.add({
    data: initProductData,
    name: 'product mvg',
  });
  hasDrapr && reporterContext.link({ ...draprData, ...draprVersion });
};
