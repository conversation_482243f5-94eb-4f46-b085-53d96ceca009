@import './product.css';
@import '../../src/components/reviews/ReviewsContainerClient/styles/Reviews.on.css';
@import '../../src/components/find-mine/styles/findmine-rewritten.on.css';

@config "../../../core/src/themes/tailwind.config.on.ts";

.recommendations-container {
  .recs-section {
    .recs-carousel-title {
      margin: 0 auto;
    }
  }
}

.quick-add .add-to-bag {
  border: revert;
}

.or-span.icon-style {
  position: relative;
  top: 0.3125rem;
}

.ap-text-content {
  position: relative;
  top: 0.3125rem;
}

.stores__list-item .low-inventory .low-stock-message--label {
  line-height: normal !important;
}
.stores__list-item .low-inventory .low-stock-message button svg {
  vertical-align: revert !important;
}

.stores__list-item .low-inventory .low-stock-message div[role='dialog'] button {
  padding: 0 !important;
}
.store-toggle[aria-expanded='false']::before,
.store-toggle[aria-expanded='true']::before {
  height: 8px !important;
  width: 8px !important;
}

button.store-toggle[aria-expanded='true']::before {
  top: 8px !important;
}

@media (max-width: 569px) {
  .panel-location div:has(#done-button) {
    padding: 1rem 0px !important;
    margin-left: 0px !important;
  }
}

.low-inventory .low-stock-message .tooltip-message {
  padding-right: 0.55rem !important;
}

#AddToBag_add-to-bag__button {
  height: 40px;
}

.dimensions-group .dimension-list--with-size-sampling,
.dimensions-group .dimension-list {
  padding: inherit;
  white-space: inherit;
}

.pp-learn-more-link,
.paypal-icon-style,
.ap-icon-style,
.ap-learn-more-link {
  display: inline-block;
}

.pp-learn-more-link,
.ap-learn-more-link {
  top: 0.45rem !important;
}

.ap-paragraph .paypal-icon-style {
  width: 65px !important;
}

div.product-page-container {
  background-color: #efefef;
}

div.product-page {
  max-width: 87.5rem;
  margin-left: auto;
  margin-right: auto;

  /* Ensure screen boundary for GAP Findmine 3rd party - tablet res on rewritten app*/
  @media (max-width: 780px) and (min-width: 451px) {
    overflow-x: hidden;
  }
}
div.product-page div.top-breadcrumbs {
  background-color: #ffffff;
}

.bricks-layout-grid,
.thumbnail-layout-grid,
.desktop-breadcrumbs {
  background-color: #ffffff;
}

.seo-smart-crosslinks-background {
  background-color: #ffffff;
}

/* Makes legacy brick layout fit in tablet viewport */
#gallery-wrapper .brick > div.brick__product-image-wrapper {
  @media (min-width: theme('screens.sm')) and (max-width: 1023px) {
    padding-bottom: 0.3rem;
  }
}

/* Fix for marketing banner height inconsistency - ensures fixed-h-holder uses border-box when feature flag is enabled */
[data-marketing-slot] .fixed-h-holder {
  box-sizing: border-box;
}
