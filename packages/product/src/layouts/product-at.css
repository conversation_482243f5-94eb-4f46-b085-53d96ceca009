@import './product.css';
@import '../../src/components/reviews/ReviewsContainerClient/styles/Reviews.at.css';
@import '../../src/components/find-mine/styles/findmine-rewritten.at.css';

@config "../../../core/src/themes/tailwind.config.at.ts";

#pixlee #pixlee_wrapper {
  > h2 {
    color: theme('colors.color-type-copy');
    font-family: theme('fontFamily.font-family-display');
    letter-spacing: theme('letterSpacing.font-letter-spacing-display');
    font-style: normal;
    line-height: normal;
    font-weight: theme('fontWeight.font-weight-display-default');
    font-size: theme('fontSize.font-size-2');
    text-transform: capitalize;
  }
}

#findmine-app-container section h2 {
  color: theme('colors.color-type-copy');
  font-family: theme('fontFamily.font-family-display') !important;
  letter-spacing: theme('letterSpacing.font-letter-spacing-display');
  font-style: normal;
  line-height: normal;
  font-weight: theme('fontWeight.font-weight-display-default') !important;
  font-size: theme('fontSize.font-size-2') !important;
  text-transform: capitalize;
}

.quick-add .add-to-bag {
  border: revert;
}

.size-guide-button {
  text-decoration: underline;
  text-transform: uppercase;
}

@media (max-width: 569px) {
  .panel-location div:has(#done-button) {
    padding: 1rem 0px !important;
    margin-left: 0px !important;
  }
}

.low-stock-message .low-stock-message--label {
  margin-top: 0 !important;
}

.pp-learn-more-link,
.paypal-icon-style,
.ap-icon-style,
.ap-learn-more-link {
  display: inline-block;
}

.paypal-icon-style,
.ap-icon-style {
  top: 0.125rem !important;
}

.pdp-dimension {
  box-sizing: revert;
}

/* Fix for marketing banner height inconsistency - ensures fixed-h-holder uses border-box when feature flag is enabled */
[data-marketing-slot] .fixed-h-holder {
  box-sizing: border-box;
}

div[role='presentation'] + div,
.messages__error-messaging + div,
.messages__backorder-messaging + div,
.messages__backorder + div {
  width: 100%;
  box-sizing: border-box;
}

.pp-learn-more-link,
.paypal-icon-style,
.ap-icon-style,
.ap-learn-more-link {
  display: inline-block;
}

.paypal-icon-style {
  top: 0.125rem !important;
}

.ap-icon-style {
  top: 0.1875rem !important;
}

.pp-learn-more-link,
.ap-learn-more-link {
  top: 0.375rem !important;
}

.ap-paragraph .paypal-icon-style {
  width: 65px !important;
}
.buybox-container .label-message,
.buybox-container .swatch-label__group {
  font-weight: 600 !important;
}

.ap-paragraph .or-span {
  position: relative;
  top: 0.125rem;
}

.ap-paragraph .ap-icon-style {
  line-height: 2rem;
}

.ap-paragraph .ap-text-content {
  line-height: 1.5rem;
}

div.product-page-container {
  background-color: #efefef;
}

div.product-page {
  max-width: 87.5rem;
  margin-left: auto;
  margin-right: auto;
  background-color: #efefef;
}

.seo-smart-crosslinks-background {
  background-color: #ffffff;
}

.buybox-container .label-message[data-testid='excluded-from-promotions'] {
  font-weight: 400 !important;
}

/* Makes legacy brick layout fit in tablet viewport */
#gallery-wrapper .brick > div.brick__product-image-wrapper {
  @media (min-width: theme('screens.sm')) and (max-width: 1023px) {
    padding-bottom: 0.3rem;
  }
}

.pdp-photo-brick-wrapper {
  padding-top: 0;
}
