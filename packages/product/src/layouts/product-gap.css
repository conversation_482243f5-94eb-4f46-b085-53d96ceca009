@import './product.css';
@import '../../src/components/reviews/ReviewsContainerClient/styles/Reviews.gap.css';
@import '../../src/components/find-mine/styles/findmine-rewritten.gap.css';

@config "../../../core/src/themes/tailwind.config.gap.ts";

.review-ratings {
  max-width: 200px !important;
}

#AddToBag_add-to-bag__button {
  padding: revert;
}

#AddToBag_add-to-bag__button > span {
  margin: revert;
}

.pdp-reviews-widget h2 {
  height: revert !important;
  padding: 2.5rem 0 2rem 0.5rem;

  @media (max-width: 768px) {
    padding: 1.5rem 0 0.5rem 0.5rem;
  }
}

/* @media (max-width: 768px) {
  .pdp-reviews-widget h2 {
    padding: 1rem 0 2rem 1.125rem;
  }
} */

.quick-add .add-to-bag {
  border: revert;
}

.pdp-dimension {
  box-sizing: revert;
}

/* Fix for marketing banner height inconsistency - ensures fixed-h-holder uses border-box when feature flag is enabled */
[data-marketing-slot] .fixed-h-holder {
  box-sizing: border-box;
}

#product-page,
div.product-page {
  max-width: 87.5rem;
  margin-left: auto;
  margin-right: auto;

  /* Ensure screen boundary for GAP Findmine 3rd party - tablet res on rewritten app*/
  @media (max-width: 780px) and (min-width: 451px) {
    overflow-x: hidden;
  }
}

.fulfillment-join-text {
  text-transform: lowercase;
}

.fulfillment-store-sentence-case-text {
  text-transform: lowercase;
}

.fulfillment-store-sentence-case-text::first-letter {
  text-transform: uppercase;
}

.store-name {
  text-transform: capitalize;
}

.pdp-photo-brick-wrapper {
  padding-top: 0;
}
