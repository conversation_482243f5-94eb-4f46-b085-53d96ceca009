@import './product.css';
@import '../../src/components/reviews/ReviewsContainerClient/styles/Reviews.br.css';
@import '../../src/components/find-mine/styles/findmine-rewritten.br.css';

@config "../../../core/src/themes/tailwind.config.br.ts";

body {
  line-height: normal;
}

.pdp-review-ratings {
  margin-bottom: 30px;
}

.pdp-review-ratings .review-ratings {
  display: -webkit-inline-box;
}
.pdp-review-ratings .review-ratings > div {
  color: rgb(102, 102, 102);
}

.p-w-r .pr-content-collection-form .pr-media-modal .button__close .pr-cross-icon__line {
  stroke: #303533 !important;
}

#done-button {
  background: none rgb(44, 40, 36);
  color: rgb(246, 244, 235);
}

.isBrBrfsRedesign #done-button {
  background-color: rgb(237, 232, 224);
  color: rgb(0, 0, 0);
}

.pdp-review-ratings-btn {
  border: 1px solid #000;
  color: #000;
  background: transparent;
  width: 100%;
  height: 40px;
  font-weight: 400;
  font-size: 16px;
  line-height: 19px;
  letter-spacing: 0.5px;
  font-family: Lynstone, Helvetica, Arial, sans-serif;
}

.pdp-review-ratings-ul {
  margin-bottom: 31px;
}

.modal__body .p-w-r .pr-control-label span,
.pr-header-title,
.modal__body .p-w-r .btn-group-radio-horizontal-linked .pr-btn {
  color: revert !important;
  background-color: revert !important;
}
.modal__body .p-w-r .pr-btn-fileinput {
  background-color: #ffffff !important;
  color: #2c2824 !important;
  border: 1px solid #2c2824 !important;
}

.modal__body .p-w-r .pr-btn-fileinput:hover {
  background-color: #2c2824 !important;
  color: #ffffff !important;
  border: 1px solid #2c2824 !important;
}
/* write a review button */
.modal__body.isBrBrfsRedesign .p-w-r .pr-btn-fileinput {
  background-color: #ede8e0 !important;
  color: #2c2824 !important;
  border: 1px solid #ede8e0 !important;
}
.modal__body.isBrBrfsRedesign .p-w-r .pr-btn-fileinput:hover {
  background-color: #ede8e0 !important;
  color: #000000 !important;
  border: none !important;
}

.modal__body .p-w-r .pr-btn-fileinput::before {
  mix-blend-mode: exclusion !important;
}

.modal__body .p-w-r .pr-header .pr-header-product-name a {
  color: #2c2824 !important;
}

/* swatch colors styles  */
.isBrBrfsRedesign .swatch-price-group__list .swatch--color {
  box-sizing: revert;
}
.isBrBrfsRedesign .swatch-price-group__list .swatch--color img {
  vertical-align: revert;
}

.isBrBrfsRedesign .swatch-price-group__list .swatch--color.swatch-color__selected {
  border-bottom: revert;
  outline: none;
}

.isBrBrfsRedesign .swatch-color__oos::after,
.isBrBrfsRedesign .swatch-color__oos::before {
  pointer-events: none;
  z-index: 10 !important;
  height: 1.2rem !important;
}
.isBrBrfsRedesign .swatch-color__oos::after {
  transform: rotate(-225deg) translate(4.5px, 7px) !important;
}
.isBrBrfsRedesign .swatch-color__oos::before {
  transform: rotate(225deg) translate(-4px, 6.2px) !important;
}

.pdp-dimension {
  box-sizing: revert;
}

/* Fix for marketing banner height inconsistency - ensures fixed-h-holder uses border-box when feature flag is enabled */
[data-marketing-slot] .fixed-h-holder {
  box-sizing: border-box;
}

.or-span.icon-style {
  position: relative;
  top: 0.3125rem;
}

div.panel-location label[aria-checked='true'] > span {
  background-color: rgb(44, 40, 36);
}

.store-details__info-wrapper .store-details__phone > a {
  color: #2c2824 !important;
}

.low-inventory .low-stock-message button:first-child {
  padding: 1px 6px !important;
}

.isBrBrfsRedesign .low-inventory .low-stock-message button:first-child {
  display: block !important;
  padding: 0.125rem 0.5rem !important;
}

.isBrBrfsRedesign .store-toggle {
  width: fit-content !important;
}

.low-inventory .low-stock-message div[role='dialog'] {
  bottom: 3.4rem !important;
}
.isBrBrfsRedesign .low-inventory .low-stock-message div[role='dialog'] {
  bottom: 2.6rem !important;
}

.low-inventory .low-stock-message div[role='dialog'],
.low-inventory .low-stock-message div[role='dialog'] div:last-of-type::after {
  background: white;
}

.pdp-reviews-widget div h2 span::after {
  border-top: 1px solid #f6f4eb80 !important;
}

.dimensions-group div[role='dialog'],
.dimensions-group div[role='dialog'] div:last-of-type::after {
  background: white;
}

.product-information-list figure {
  width: fit-content !important;
  span {
    width: 11px;
    min-width: 11px;
  }
}

.product-information-list button a div:nth-of-type(1) {
  width: fit-content;
}

.pdp-header__title {
  text-transform: uppercase;
}

.single-column-layout-grid {
  grid-template-columns: 50% 1fr;

  .buy-box_wrapper {
    padding-top: theme('spacing.utk-spacing-2xl');
    width: 390px;
    margin: 0px auto;
  }

  .carousel {
    padding-left: 0px !important;
  }

  @media (min-width: 768px) and (max-width: 780px) {
    .single-column-layout-grid {
      grid-template-columns: calc(100% - 390px) 390px;
    }
  }
}

.pdp-quick-add-modal-legacy {
  background-color: theme('colors.wh') !important;

  .pdp-dimension--unavailable,
  .pdp-dimension.pdp-dimension--should-display-redesign-in-stock.pdp-dimension--unavailable {
    background-color: transparent !important;
  }

  .add-to-bag--disabled {
    background-color: theme('colors.bk') !important;
    color: theme('colors.wh') !important;
  }

  .add-to-bag {
    background-color: theme('colors.bk') !important;
    color: theme('colors.wh') !important;
  }
}

/* Fixes box shadow of Image Gallery dots on mobile HUI PDP */
#gallery-wrapper .pdp-slick-dots > li > div {
  box-shadow: 0 2px 6px rgba(44, 40, 36, 0.1);
}
