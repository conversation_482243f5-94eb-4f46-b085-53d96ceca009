import { PageParams } from '@ecom-next/sitewide/pages/PageWrapper';
import pino from 'pino';
import type { Market } from '@ecom-next/utils/server';
import { BrandMarketProvider } from '../providers/brand-market-provider';
import { MVGBuyBoxStoreProvider } from '../providers/mvg-buybox-provider';
import { MvgPDPReporterClient } from '../providers/pdp-reporter-provider/v3/mvg-reporter-provider';
import type { AppWrapperApps } from './Product';
import { getPageContextData } from './getPageState';
import { cacheableCapiDataV3Promise, cacheableEnabledFeaturesV2Promise, cacheableFeaturesConfigMVG, cacheableMvgSeoDataPromise } from './getReWrittenData';
import { getUrlParamsString } from './helpers';
import { GeneralNoResultsError } from './Errors';
import { OutOfStockTemplate } from './templates/OutOfStockTemplate';

interface OosAppProps {
  market: Market;
  pdpLogger: pino.BaseLogger;
  requestParamString: string;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const getAppData = async (searchParams: PageParams['searchParams'], pdpLogger: pino.BaseLogger) => {
  const baseAppData = {
    error: true,
    featureConfigs: {
      enabledFeatures: {},
      featureVariables: {},
      abSeg: {},
    },
    capiData: null,
  };
  const requestParamString = getUrlParamsString(searchParams);
  try {
    const [capiData, featureConfigs] = await Promise.all([
      cacheableCapiDataV3Promise(requestParamString),
      cacheableEnabledFeaturesV2Promise(requestParamString),
    ]);
    if (!capiData) {
      pdpLogger.error('Capi data is not defined.');
      return baseAppData;
    }
    return { error: false, capiData, featureConfigs };
  } catch (error) {
    pdpLogger.error(error);
    return baseAppData;
  }
};

export const OutOfStockApp = async ({
  pdpLogger,
  brandCode,
  requestParamString,
  brandInformation,
  market,
  searchParams,
  ...restOfProps
}: PageParams & Omit<AppWrapperApps, 'breadcrumbs' | 'crosslinks' | 'productMarketing'> & OosAppProps) => {
  const [featureConfigs, { capiData, error }, seo] = await Promise.all([
    cacheableFeaturesConfigMVG(requestParamString),
    getAppData(searchParams, pdpLogger),
    cacheableMvgSeoDataPromise(requestParamString),
  ]);
  if (error || !capiData) {
    throw new GeneralNoResultsError('No results found');
  }
  const { locale } = getPageContextData();
  const brandMarketProps = {
    brandInformation,
    locale,
    market,
  };
  const allDataLayerInitialProps = {
    businessUnitId: brandCode,
    productName: capiData.selectedStyle?.description || '',
    styleId: capiData.selectedStyle?.style_id || '',
    ...brandInformation,
    ...capiData,
    featureConfigs,
    mergeType: 'NONE',
    isDropShip: capiData.selectedStyle?.isDropship || false,
    vendorId: capiData.selectedStyle?.vendor_number,
    vendorName: capiData.selectedStyle?.vendor_name,
    pageType: 'product',
    selectedNodes: seo.selectedNodes,
    breadcrumbs: seo.breadcrumbs,
    ...restOfProps,
  };

  return (
    <div id='product-page' className='product-page out-of-stock-page'>
      <BrandMarketProvider {...brandMarketProps}>
        <MVGBuyBoxStoreProvider data={capiData}>
          <MvgPDPReporterClient {...allDataLayerInitialProps}>
            <OutOfStockTemplate
              isMultiVariantProduct={!!capiData?.isMultiVariantProduct}
              brandInformation={brandInformation}
              searchParams={searchParams}
              brandCode={brandCode}
            />
          </MvgPDPReporterClient>
        </MVGBuyBoxStoreProvider>
      </BrandMarketProvider>
    </div>
  );
};
