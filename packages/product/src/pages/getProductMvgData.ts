import { isValidCid } from '@ecom-next/marketing-ui/src/fetchMarketing';
import { getPageDataPromisesWithParams } from '@ecom-next/sitewide/state-builder';
import { getPageContext } from '@ecom-next/utils/server';
import logger from '@ecom-next/app/logger';
import { capiAggregationServiceV3 } from './services/capi-aggregation-service';
import { getSanitizedQuery } from './helpers';
import { cacheableInitProductFeaturesConfig } from './getReWrittenData';

export async function getV3CapiDataPromise(requestParamString: string) {
  const searchParamsObj = new URLSearchParams(requestParamString);
  const sanitizedQuery = getSanitizedQuery(searchParamsObj);
  const { cid: maybeCid } = sanitizedQuery;

  const cid = isValidCid(maybeCid) ? maybeCid : '';
  const { staticDataPromise } = getPageDataPromisesWithParams(cid, 'product', sanitizedQuery);

  const { brand, market, locale } = getPageContext();
  const vid = searchParamsObj.get('vid') ?? '';
  const pid = searchParamsObj.get('pid') ?? '';

  const country = locale === 'en_US' ? 'us' : 'ca';

  const [{ previewDate = '', contentType = 'ecom', appConfig }, { pdpMVGRefreshEnabled, isPdpLoyaltyGated }] = await Promise.all([
    staticDataPromise,
    cacheableInitProductFeaturesConfig(requestParamString),
  ]);
  const { apiConfig } = appConfig;

  return capiAggregationServiceV3({
    brand,
    cid,
    country,
    date: previewDate,
    internalOidcUrl: apiConfig.internalOidcUrl,
    locale,
    market,
    pid,
    requestType: contentType,
    vid,
    pdpLogger: logger,
    responseType: pdpMVGRefreshEnabled ? 'GROUP' : 'MERGE',
  })
    .then(capiData => {
      if (capiData) {
        const { hasRestrictions, restrictedCustomerChoices } = capiData;
        if (hasRestrictions && isPdpLoyaltyGated) {
          // Need to call the decision service to check if the user has access to the restricted customer choices
          // Handled as part of below story
          // https://gapinc.atlassian.net/browse/EVAL-6526
          logger.info(`Product ${pid} has restricted customer choices`, {
            pid,
            restrictedCustomerChoices,
          });
        }
      }
      // Return the transformed CAPI data
      return capiData;
    })
    .catch(error => {
      // As there is already a logger in the capiAggregationServiceV3,
      // when the decision service is called, it will log the error as part of the api call.
      // so we can just logging the restrictions errors here
      // logger.error(`Error while processing the restrictions in CAPI data: ${pid}`, error);
      throw error;
    });
}
