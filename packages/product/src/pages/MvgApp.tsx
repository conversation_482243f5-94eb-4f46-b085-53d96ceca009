import { PageParams } from '@ecom-next/sitewide/pages/PageWrapper';
import { Market } from '@ecom-next/utils/server';
import pino from 'pino';
import type { LasConfigType } from '@ecom-next/core/components/migration/bopis/collaborators/bopis-data';
import { DataLayer } from '../components/product-page/collaborators/pdp-reporter';
import { BrandMarketProvider } from '../providers/brand-market-provider';
import { BopisProviderWrapper } from '../providers/bopis-provider';
import { MVGBuyBoxStoreProvider } from '../providers/mvg-buybox-provider';
import { MvgPDPReporterClient } from '../providers/pdp-reporter-provider/v3/mvg-reporter-provider';
import { getSanitizedQuery, getUrlParamsString } from './helpers';
import {
  cacheableAppConfigDataV2Promise,
  cacheableCapiDataV3Promise,
  cacheableEnabledFeaturesV2Promise,
  cacheableFeaturesConfigMVG,
  cacheableMvgSeoDataPromise,
} from './getReWrittenData';
import type { AppWrapperApps } from './Product';
import { getPageContextData } from './getPageState';
import { checkValidParams } from './state-builder';
import { PDPMVGTemplate } from './templates/PDPMVGTemplate';
import { GeneralNoResultsError, InvalidIdNoResultsError } from './Errors';

interface MvgAppProps {
  datalayer?: DataLayer;
  market: Market;
  pdpLogger: pino.BaseLogger;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const getAppData = async (searchParams: PageParams['searchParams'], pdpLogger: pino.BaseLogger) => {
  const baseAppData = {
    error: true,
    config: { locationConfig: {} as LasConfigType },
    featureConfigs: {
      enabledFeatures: {},
      featureVariables: {},
      abSeg: {},
    },
    capiData: null,
  };
  const requestParamString = getUrlParamsString(searchParams);
  try {
    const [capiData, config, featureConfigs] = await Promise.all([
      cacheableCapiDataV3Promise(requestParamString),
      cacheableAppConfigDataV2Promise(requestParamString),
      cacheableEnabledFeaturesV2Promise(requestParamString),
    ]);
    if (!capiData) {
      pdpLogger.error('Capi data is not defined.');
      return baseAppData;
    }
    return { error: false, capiData, config, featureConfigs };
  } catch (error) {
    pdpLogger.error(error);
    return baseAppData;
  }
};

export const getSanitizedSearch = (searchParams: PageParams['searchParams']) => {
  const requestParamString = getUrlParamsString(searchParams);
  const searchParamsObj = new URLSearchParams(requestParamString);
  return getSanitizedQuery(searchParamsObj);
};

export const MvgApp = async (props: PageParams & AppWrapperApps & MvgAppProps) => {
  const { breadcrumbs: breadcrumbsComponent, pdpLogger, crosslinks, brandInformation, productMarketing, ...restOfTheProps } = props;
  const { brandCode, searchParams, market, params } = restOfTheProps;
  const requestParamString = getUrlParamsString(searchParams);
  const searchParamsObj = new URLSearchParams(requestParamString);
  const sanitizedQuery = getSanitizedQuery(searchParamsObj);

  const { pid } = sanitizedQuery;

  if (!checkValidParams(sanitizedQuery)) {
    pdpLogger.info('Invalid params', { searchParams });
    throw new GeneralNoResultsError('Invalid parameters provided for product data retrieval');
  }

  if (!pid) {
    pdpLogger.info('Invalid pid', { searchParams });
    throw new InvalidIdNoResultsError('Invalid pid provided');
  }

  const [seo, featureConfigs, { capiData, error, config }] = await Promise.all([
    cacheableMvgSeoDataPromise(requestParamString),
    cacheableFeaturesConfigMVG(requestParamString),
    getAppData(searchParams, pdpLogger),
  ]);

  if (error || !capiData) {
    throw new GeneralNoResultsError('No results found');
  }

  const { locale } = getPageContextData();
  // TODO: Add back when we have the data transformed
  //   const template = getTemplateFactory(capiData.features);

  const brandMarketProps = {
    brandInformation,
    locale,
    market: props.market,
  };

  const allDataLayerInitialProps = {
    businessUnitId: brandCode,
    productName: capiData.selectedStyle?.description || '',
    styleId: capiData.selectedStyle?.style_id || '',
    ...restOfTheProps,
    ...brandInformation,
    ...capiData,
    featureConfigs,
    breadcrumbs: seo.breadcrumbs,
    mergeType: 'NONE',
    isDropShip: capiData.selectedStyle?.isDropship || false,
    vendorId: capiData.selectedStyle?.vendor_number,
    vendorName: capiData.selectedStyle?.vendor_name,
    pageType: 'product',
    selectedNodes: seo.selectedNodes,
    selectedMultiVariantKey: capiData.selectedMultiVariantKey,
  };

  return (
    <div id='product-page' className='product-page'>
      <BopisProviderWrapper brandName={brandInformation.brandName} locationConfig={config.locationConfig} market={market} pageType='product'>
        <BrandMarketProvider {...brandMarketProps}>
          <MVGBuyBoxStoreProvider data={capiData}>
            <MvgPDPReporterClient {...allDataLayerInitialProps}>
              <PDPMVGTemplate
                brandInformation={brandInformation}
                crosslinks={crosslinks}
                searchParams={searchParams}
                params={params}
                brandCode={brandCode}
                productMarketing={productMarketing}
                breadcrumbs={breadcrumbsComponent}
                isMultiVariantProduct={!!capiData?.isMultiVariantProduct}
              />
            </MvgPDPReporterClient>
          </MVGBuyBoxStoreProvider>
        </BrandMarketProvider>
      </BopisProviderWrapper>
    </div>
  );
};
