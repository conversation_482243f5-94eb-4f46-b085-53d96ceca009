import { serverFetch as fetch } from '@ecom-next/utils/serverFetch';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { Brand, Locale, Market } from '@ecom-next/utils/server';
import pino from 'pino';
import { getId } from '../../correlation-id';
import { ServicesArgs } from '../capiAggregationService';
import { formatQueryParams } from '../../../../capi-utils/request-builder/helper/format-query-params';
import { parseRequestDate, parseRequestType, RequestType } from '../../../../capi-utils';
import { CapiV3AggregationServiceRaw, CAPIV3TransformedData } from './types';
import { productDataTransformer } from './product-data-transformer';

dayjs.extend(utc);

type GetCapiV3UrlProps = {
  brand: Brand;
  effectiveDate?: string;
  locale: Locale;
  market: Market;
  pid: string;
  requestType?: Omit<RequestType, 'wip' | 'app'>;
  responseType: 'GROUP' | 'MERGE' | 'STYLE' | null;
  storeId?: string;
  vid: string;
};

export type CapiV3Args = ServicesArgs & {
  cid: string;
  internalOidcUrl: string;
  pdpLogger: pino.BaseLogger;
  pid: string;
  requestType: 'wip' | 'app' | 'ecom';
  responseType: GetCapiV3UrlProps['responseType'] | null;
  vid: string;
};

type DetailedError = {
  error: {
    details: string[];
    message: string;
  };
};

type CapiError = {
  errorDetails: Error | DetailedError;
  status: number;
};

const getCapiV3Url = (props: GetCapiV3UrlProps) => {
  const { pid, market, brand, locale, responseType = 'MERGE', storeId = null, requestType = null, effectiveDate = null, vid } = props;

  const brandUpperCased = brand?.toUpperCase() as Brand;
  const marketUpperCased = market?.toUpperCase() as Market;

  const baseUrl = `${process.env.CATALOG_OMNI_PRODUCT_SERVICE_BASE_URL}/v3/products?product_id=${pid}`;

  const localeParam = locale ? `&locale=${locale}` : '';
  const baseQueryParams = `&market=${marketUpperCased}&brand=${brandUpperCased}${localeParam}`;
  const optionalParams = formatQueryParams([
    { key: 'fit_variant_id', value: !vid ? null : vid },
    { key: 'store_id', value: storeId },
    { key: 'response_type', value: responseType },
    { key: 'request_type', value: parseRequestType(requestType as RequestType) },
    { key: 'effective_date', value: parseRequestDate(effectiveDate) },
  ]);
  return baseUrl + baseQueryParams + optionalParams;
};

export const capiAggregationServiceV3 = async (options: CapiV3Args): Promise<CAPIV3TransformedData | null> => {
  const { brand, market, locale, pid, requestType, date, pdpLogger, vid, responseType = 'MERGE' } = options;

  const capiUrl = getCapiV3Url({
    brand,
    effectiveDate: date,
    locale,
    market,
    requestType: requestType === 'ecom' ? undefined : requestType,
    pid,
    vid,
    responseType,
  });

  const fetchOptions = {
    retries: 1,
    headers: {
      Accept: 'application/vnd.snake+json',
      Connection: 'keep-alive',
      'User-Agent': 'Product Page Next',
      'X-Client-Application-Name': 'product-page-next',
      'x-correlation-id': getId() ?? '',
    },
  };
  pdpLogger.info(`CAPI v3 Aggregation Service: ${capiUrl}`);
  return fetch<CapiV3AggregationServiceRaw>(capiUrl, fetchOptions)
    .then(data => {
      if (!data?.brand) {
        pdpLogger.error('No brand data found in CAPI v3 response');
        return null;
      }
      return productDataTransformer({ vid, capiData: data, locale });
    })
    .catch((error: CapiError) => {
      const parsedError = (error?.errorDetails as Error)?.message ?? (error?.errorDetails as DetailedError)?.error?.message ?? error;
      if (error?.status === 404) {
        pdpLogger.warn(`CAPI v3 Aggregation Service: No results found for pid ${pid}`, parsedError);
        throw error;
      }
      pdpLogger.error(parsedError);
      throw error;
    });
};
