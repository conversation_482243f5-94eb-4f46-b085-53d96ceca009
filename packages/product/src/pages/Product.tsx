import PageWrapper, { type PageParams } from '@ecom-next/sitewide/page-wrapper';
import { StitchStyleProvider } from '@ecom-next/core/react-stitch';
import { getBrandInformation, BrandInfo } from '@mfe/brand-info';
import { ScriptLoaderProvider } from '@product-page/legacy/script-loader-provider';
import React, { PropsWithChildren } from 'react';
import { Brand, getPageContext } from '@ecom-next/utils/server';
import { getLocaleSpecificTranslations } from '@ecom-next/sitewide/localization';
import LocalizationProvider from '@ecom-next/sitewide/localization-provider';
import { redirect } from 'next/navigation';
import Script from 'next/script';
import logger from '@ecom-next/app/logger';
import pino from 'pino';
import { ProductBreadcrumbs } from '@ecom-next/core/legacy/product-breadcrumbs';
import { AppConfigProvider } from '../legacy/src/app/components/app-config-provider';
import { ABSegProvider } from '../legacy/src/app/components/abseg-provider';
import { BrandMarketProvider } from '../legacy/src/app/components/brand-market-provider';
import { ModelSizeProvider } from '../legacy/src/app/components/model-size-provider';
import { CustomThemeProvider } from '../legacy/src/app/components/custom-theme-provider';
import SeoSmartCrosslinks from '../legacy/src/app/components/seo-smart-crosslinks';
import MarketingContainer from '../components/marketing-container/MarketingContainer';
import { BreadCrumbsClient } from '../components/breadcrumbs/BreadCrumbsClient';
import SeoSmartCrosslinksClient from '../components/seo-smart-crosslinks';
import { cachedMvgMetaData } from './state-builder/get-meta-data-v3';
import App, { CertonaWrapper } from './App';
import { checkValidParams, getCacheableDataPromises, getMetaData } from './state-builder';
import getAppConfig from './state-builder/appConfig';
import shippingAndReturnsData from './state-builder/ship-and-return-data';
import { ReWrittenApp } from './ReWrittenApp';
import { MvgApp } from './MvgApp';
import { OutOfStockApp } from './OutOfStockApp';
import {
  cacheableCapiDataPromise,
  cacheableCapiDataV3Promise,
  cacheableInitProductFeaturesConfig,
  cacheableMarketingPromise,
  cacheableMvgSeoDataPromise,
  cacheableSmartCrosslinksDataPromise,
  cacheableEnabledFeaturesV2Promise,
  getUrlParamsString,
} from './getReWrittenData';
import { GeneralNoResultsError, isPDPErrors } from './Errors';
import { toParam } from './helpers';

export interface AppWrapperApps {
  brandCode: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  brandInformation: any;
  breadcrumbs: JSX.Element;
  crosslinks: JSX.Element | null;
  isMultiVariantProduct?: boolean;
  productMarketing: JSX.Element;
}
interface ProductMainProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  brandInformation: any;
}

type BrandCodeString = `${BrandInfo[Brand]['marketAgnosticBrandCode']}`;

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type TemplateProps = (...args: any[]) => JSX.Element;

const abbrBrandForTruefit = {
  gap: 'gp',
  br: 'br',
  on: 'on',
  at: 'at',
  gapfs: 'gf',
  brfs: 'brf',
} as const;

async function MarketingComponent(props: PropsWithChildren<PageParams>) {
  const { searchParams } = props;
  const { marketingDataPromise, productDataPromise, staticDataPromise } = getCacheableDataPromises(searchParams);

  const [marketingData, productData, staticData] = await Promise.all([marketingDataPromise, productDataPromise, staticDataPromise]);
  const {
    productData: { primarySellingStyleId, primaryCategoryId },
  } = productData;

  const { brand } = getPageContext();

  return (
    <ABSegProvider abSeg={staticData.abSeg} brandName={brand}>
      <MarketingContainer brandName={brand} pid={primarySellingStyleId} cid={primaryCategoryId} customMarketing={marketingData} />
    </ABSegProvider>
  );
}

async function MvgMarketingComponent(props: PropsWithChildren<PageParams>) {
  const { searchParams } = props;
  const requestParamString = getUrlParamsString(searchParams);
  const [marketingData, productData, featureConfigs] = await Promise.all([
    cacheableMarketingPromise(requestParamString),
    cacheableCapiDataV3Promise(requestParamString),
    cacheableEnabledFeaturesV2Promise(requestParamString),
  ]);

  const { abSeg } = featureConfigs;

  if (!productData?.selectedStyle) {
    return <></>;
  }
  const {
    selectedStyle: { primary_style_id: primarySellingStyleId, primary_category_id: primaryCategoryId },
  } = productData;
  return (
    <ABSegProvider abSeg={abSeg} brandName={productData.brand}>
      <MarketingContainer brandName={productData.brand} pid={primarySellingStyleId} cid={primaryCategoryId as string} customMarketing={marketingData} />
    </ABSegProvider>
  );
}

async function ServerCertonaWrapper(props: PropsWithChildren<PageParams>) {
  const { children, searchParams } = props;
  const { productDataPromise, sanitizedQuery, featureFlagsPromise } = getCacheableDataPromises(searchParams);

  const [data, featureFlags] = await Promise.all([productDataPromise, featureFlagsPromise]);

  return (
    <CertonaWrapper productData={data.productData} featureFlags={featureFlags} searchParams={sanitizedQuery}>
      {children}
    </CertonaWrapper>
  );
}
async function ModelSizeWrapper(props: PropsWithChildren<PageParams>) {
  const { searchParams, children } = props;
  const { productDataPromise, sanitizedQuery } = getCacheableDataPromises(searchParams);

  const [data] = await Promise.all([productDataPromise]);

  const { productData } = data;
  const modelSizes = productData?.infoTabs?.overview?.modelSizes || [];
  const { modelSize } = sanitizedQuery;

  return (
    <ModelSizeProvider
      modelSizes={modelSizes.length ? modelSizes[0] : []}
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      preSelectedModelSize={modelSize as any}
    >
      {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        children as any
      }
    </ModelSizeProvider>
  );
}

async function SmartCrosslinks(props: PageParams & { isRewriteSEO: boolean }) {
  const { searchParams, isRewriteSEO } = props;
  const { crossLinkDataPromise, featureFlagsPromise } = getCacheableDataPromises(searchParams);

  const [data, featureFlags] = await Promise.all([crossLinkDataPromise, featureFlagsPromise]);
  const { enabledFeatures } = featureFlags;
  const smartCrosslinks = 'smartCrosslinks' in data ? data.smartCrosslinks : data;
  const crosslinks = smartCrosslinks?.crosslinks || [];
  const fallback = smartCrosslinks?.fallback || [];
  const pdpInterlinksEnabled = enabledFeatures['adeptmind-crosslinks-pdp'];
  const displayCrossLinks = enabledFeatures['seo-show-crosslinks-pdp'];
  const vendor = 'adeptmind';
  const links = pdpInterlinksEnabled && crosslinks?.length > 0 ? crosslinks : fallback;
  const source = pdpInterlinksEnabled && crosslinks?.length > 0 ? vendor : 'Web Hierarchy';

  return displayCrossLinks ? (
    isRewriteSEO ? (
      <SeoSmartCrosslinksClient displayHeaderAlways links={links} source={source} />
    ) : (
      <SeoSmartCrosslinks displayHeaderAlways links={links} source={source} />
    )
  ) : null;
}
async function MvgSmartCrosslinks(props: PageParams & { isRewriteSEO: boolean }) {
  const { searchParams, isRewriteSEO } = props;
  const requestParamString = getUrlParamsString(searchParams);

  const [data, featureFlags] = await Promise.all([
    cacheableSmartCrosslinksDataPromise(requestParamString),
    cacheableEnabledFeaturesV2Promise(requestParamString),
  ]);
  const { enabledFeatures } = featureFlags;
  const { smartCrosslinks } = data;
  const crosslinks = smartCrosslinks?.crosslinks || [];
  const displayCrossLinks = enabledFeatures['seo-show-crosslinks-pdp'];
  const fallback = smartCrosslinks?.fallback || [];
  const pdpInterlinksEnabled = enabledFeatures['adeptmind-crosslinks-pdp'];
  const vendor = 'adeptmind';
  const links = pdpInterlinksEnabled && crosslinks?.length > 0 ? crosslinks : fallback;
  const source = pdpInterlinksEnabled && crosslinks?.length > 0 ? vendor : 'Web Hierarchy';

  return displayCrossLinks ? (
    isRewriteSEO ? (
      <SeoSmartCrosslinksClient displayHeaderAlways links={links} source={source} />
    ) : (
      <SeoSmartCrosslinks displayHeaderAlways links={links} source={source} />
    )
  ) : null;
}

async function BreadCrumbs(props: PageParams & { isRewriteBreadcrumbsEnabled: boolean }) {
  const { searchParams, isRewriteBreadcrumbsEnabled } = props;
  const { seoPromise } = getCacheableDataPromises(searchParams);

  const [seo] = await Promise.all([seoPromise]);

  return isRewriteBreadcrumbsEnabled ? <BreadCrumbsClient seo={seo} isMvg={false} /> : <ProductBreadcrumbs breadcrumbs={seo.breadcrumbs} lastClickable />;
}

async function MvgBreadCrumbs(props: PageParams & { isRewriteBreadcrumbsEnabled: boolean; pdpLogger: pino.BaseLogger }) {
  const { searchParams, isRewriteBreadcrumbsEnabled } = props;
  const requestParamString = getUrlParamsString(searchParams);

  const seoPromise = cacheableMvgSeoDataPromise(requestParamString);
  const seo = await seoPromise;

  return isRewriteBreadcrumbsEnabled ? <BreadCrumbsClient seo={seo} isMvg /> : <ProductBreadcrumbs breadcrumbs={seo.breadcrumbs} lastClickable />;
}

async function AppWrapper(props: PageParams & AppWrapperApps) {
  const { searchParams, brandCode, productMarketing, crosslinks, breadcrumbs, brandInformation } = props;

  const { productDataPromise, sanitizedQuery, productAppConfigsPromise, reviewRatingsPromise, seoPromise } = getCacheableDataPromises(searchParams);

  const [data, productAppConfigs, reviewRatings, seo] = await Promise.all([productDataPromise, productAppConfigsPromise, reviewRatingsPromise, seoPromise]);
  const { productData } = data;
  const { appConfig } = productAppConfigs;
  const { brand, locale } = getPageContext();
  const { breadcrumbs: breadcrumbsData, selectedNodes } = seo;
  const returnsData = shippingAndReturnsData[locale][brand];
  const { socialGalleryConfig } = getAppConfig();
  const pageState = {
    productData,
    // marketingData,
    params: sanitizedQuery,
    brandInformation,
    brandCode,
    breadcrumbsData,
    selectedNodes,
    shippingAndReturnsData: returnsData,
    productAppConfigs: appConfig,

    reviewRatings,
    socialGalleryConfig,
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return <App {...(pageState as any)} marketing={productMarketing} breadcrumbs={breadcrumbs} crosslinks={crosslinks} />;
}

async function ProductMain(props: PropsWithChildren<PageParams & ProductMainProps>) {
  const { searchParams, children, brandInformation } = props;
  const { staticDataPromise, featureFlagsPromise, productAppConfigsPromise, productDataPromise } = getCacheableDataPromises(searchParams);

  const { brand, market, locale } = getPageContext();
  const [staticData, featureFlags, productAppConfigData] = await Promise.all([
    staticDataPromise,
    featureFlagsPromise,
    productAppConfigsPromise,
    productDataPromise,
  ]);

  const { enabledFeatures } = featureFlags;
  const { abSeg } = staticData;

  const { appConfig: productAppConfigs } = productAppConfigData;
  const gapRedesign2024Segment = abSeg[`${brand.toLowerCase()}214`];
  const isGapBuyboxRedesign2024 = enabledFeatures['gap-buybox-2024'] && ['a', 'b'].includes(gapRedesign2024Segment);

  const useAtRedesignFlag = brand === 'at' && enabledFeatures['at-redesign-2024'] ? { 'at-redesign-2023': !!enabledFeatures['at-redesign-2023'] } : '';
  const newGap2022ColorsFeatureFlag = ['gap', 'gapfs'].includes(brand) ? 'gap-colors-2022' : '';
  const stitchStyleProviderData = {
    'enable-font-display-swap': true,
    'gap-colors-2022': !!enabledFeatures[newGap2022ColorsFeatureFlag],
    'gap-redesign-2024': isGapBuyboxRedesign2024,
    ...useAtRedesignFlag,
  };

  return (
    <BrandMarketProvider brandInformation={brandInformation as BrandInformation} locale={locale} market={market}>
      <ABSegProvider abSeg={abSeg} brandName={brand}>
        <CustomThemeProvider avoidATRedesign>
          <StitchStyleProvider brand={brand} enabledFeatures={stitchStyleProviderData}>
            <AppConfigProvider appConfig={productAppConfigs}>
              <ModelSizeWrapper {...props}>
                <ServerCertonaWrapper {...props}>
                  <ScriptLoaderProvider>
                    <div id='product-page'>{children}</div>
                  </ScriptLoaderProvider>
                </ServerCertonaWrapper>
              </ModelSizeWrapper>
            </AppConfigProvider>
          </StitchStyleProvider>
        </CustomThemeProvider>
      </ABSegProvider>
    </BrandMarketProvider>
  );
}

async function renderCapiV3App({
  props,
  requestParamString,
  pdpOos2025Enabled,
  oosAppProps,
  mvgAppProps,
  OutOfStockTemplate,
  LegacyAppTemplate,
  MVGAppTemplate,
  pdpHUIRedesignEnabled,
  ReWrittenAppTemplate,
}: {
  LegacyAppTemplate: TemplateProps;
  MVGAppTemplate: TemplateProps;
  OutOfStockTemplate: TemplateProps;
  ReWrittenAppTemplate: TemplateProps;
  mvgAppProps: object;
  oosAppProps: object;
  pdpHUIRedesignEnabled: boolean;
  pdpLogger: pino.BaseLogger;
  pdpOos2025Enabled: boolean;
  props: PageParams;
  requestParamString: string;
}): Promise<[JSX.Element, string]> {
  const capiDataV3 = await cacheableCapiDataV3Promise(requestParamString);

  if (capiDataV3?.isFullyOOS && !capiDataV3?.metadata?.variant_group) {
    if (pdpOos2025Enabled) {
      return [<OutOfStockTemplate isMultiVariantProduct={!!capiDataV3?.isMultiVariantProduct} key='oos2025Template' {...oosAppProps} />, 'oos2025Template'];
    }
    const legacy = await renderLegacyApp({
      props,
      requestParamString,
      pdpHUIRedesignEnabled,
      ReWrittenAppTemplate,
      LegacyAppTemplate,
    });

    return legacy;
  }
  return [<MVGAppTemplate key='mvg' {...mvgAppProps} />, 'mvg'];
}

async function renderLegacyApp({
  props,
  requestParamString,
  pdpHUIRedesignEnabled,
  ReWrittenAppTemplate,
  LegacyAppTemplate,
}: {
  LegacyAppTemplate: TemplateProps;
  ReWrittenAppTemplate: TemplateProps;
  pdpHUIRedesignEnabled: boolean;
  props: PageParams;
  requestParamString: string;
}): Promise<[JSX.Element, string]> {
  const capiData = await cacheableCapiDataPromise(requestParamString);
  const isProductInstock = capiData.productData?.inStock;

  if (isProductInstock && pdpHUIRedesignEnabled) {
    return [<ReWrittenAppTemplate key='rewritten' {...props} />, 'rewritten'];
  }

  const { seoPromise } = getCacheableDataPromises(props.searchParams);
  const [seo] = await Promise.all([seoPromise]);
  return [<LegacyAppTemplate key='legacy' appProps={props} seo={seo} />, 'legacy'];
}

export async function generateMetadata(props: PageParams) {
  const pdpLogger = logger.child({ module: 'pdp' });
  try {
    const requestParamString = getUrlParamsString(props.searchParams);
    const searchParamsObj = new URLSearchParams(requestParamString);
    const sanitizedQuery = Object.fromEntries(Array.from(searchParamsObj.entries()).map(entry => [entry[0], toParam(entry[1])]));
    if (!checkValidParams(sanitizedQuery)) {
      throw new GeneralNoResultsError('Invalid parameters provided for product data retrieval');
    }
    const { pdpCAPIv3Enabled } = await cacheableInitProductFeaturesConfig(requestParamString);
    if (!pdpCAPIv3Enabled) {
      return await getMetaData(props);
    }
    return await cachedMvgMetaData(requestParamString);
  } catch (error) {
    const formattedError = {
      message: error instanceof Error ? error.message : 'Error generating metadata',
      name: error instanceof Error ? error.name : 'UnknownError',
    };
    pdpLogger.warn(formattedError);
    // Log the error and return a default metadata object
    return {};
  }
}

export async function Product(props: PageParams) {
  const { brand, market, locale, targetEnv } = getPageContext();
  const pdpLogger = logger.child({ module: 'pdp' });
  let _appName = 'legacy';

  const brandedTranslationBundleName = `product-page-${brand}` as const;
  const translations = getLocaleSpecificTranslations(locale, ['product-page', brandedTranslationBundleName]);

  const brandInfo = getBrandInformation(brand, market);
  const brandName = brand;
  const { abbrNameForTealium, marketAgnosticBrandCode } = brandInfo;
  const brandCode = `${brandInfo.marketAwareBrandCode}` as BrandCodeString;
  const abbrBrand = abbrNameForTealium === 'gp' ? 'gap' : abbrNameForTealium;
  const brandInformation = {
    ...brandInfo,
    brandName: brandName as Brands,
    abbrBrandForTealium: abbrBrand as AbbrBrandForTealium,
    abbrBrandForTrueFit: abbrBrandForTruefit[brand] as AbbrBrandForTrueFit,
    marketAgnosticBrandCode: `${marketAgnosticBrandCode}`,
    marketAwareBrandCode: brandCode as BrandCode,
  } as BrandInformation;
  let isPdpCAPIv3Enabled = false,
    isPdpHUIRedesignEnabled = false,
    isPdpMVGRefreshEnabled = false,
    rawRequestParamString = '';
  try {
    const requestParamString = getUrlParamsString(props.searchParams);
    const searchParamsObj = new URLSearchParams(requestParamString);
    const sanitizedQuery = Object.fromEntries(Array.from(searchParamsObj.entries()).map(entry => [entry[0], toParam(entry[1])]));
    if (!checkValidParams(sanitizedQuery)) {
      throw new GeneralNoResultsError('Invalid parameters provided for product data retrieval');
    }
    const { pdpCAPIv3Enabled, pdpHUIRedesignEnabled, pdpMVGRefreshEnabled, rewriteBreadcrumbs, pdpOos2025Enabled, rewriteSEO } =
      await cacheableInitProductFeaturesConfig(requestParamString);
    isPdpCAPIv3Enabled = pdpCAPIv3Enabled;
    isPdpHUIRedesignEnabled = pdpHUIRedesignEnabled;
    isPdpMVGRefreshEnabled = pdpMVGRefreshEnabled;
    rawRequestParamString = requestParamString;
    const mvgAppProps = { ...props, pdpLogger, isRewriteBreadcrumbsEnabled: rewriteBreadcrumbs, isRewriteSEO: rewriteSEO };
    const oosAppProps = { ...props, pdpLogger, brandInformation, brandCode, market, locale, requestParamString };

    const productMarketing = <MarketingComponent {...props} />;
    const app = (
      <LocalizationProvider locale={locale} market={market} supportNesting translations={translations}>
        <AppWrapper
          {...props}
          brandInformation={brandInformation}
          breadcrumbs={<BreadCrumbs {...{ ...props, isRewriteBreadcrumbsEnabled: false }} />}
          productMarketing={productMarketing}
          crosslinks={<SmartCrosslinks {...{ ...props, isRewriteSEO: false }} />}
          brandCode={brandCode}
        />
      </LocalizationProvider>
    );

    const appMain = (
      <ProductMain {...props} brandInformation={brandInformation}>
        {app}
      </ProductMain>
    );

    const rewriteApp = (
      <ReWrittenApp
        {...props}
        brandInformation={brandInformation}
        productMarketing={productMarketing}
        brandCode={brandCode}
        market={market}
        breadcrumbs={<BreadCrumbs {...{ ...props, isRewriteBreadcrumbsEnabled: rewriteBreadcrumbs }} />}
        crosslinks={<SmartCrosslinks {...{ ...props, isRewriteSEO: rewriteSEO }} />}
      />
    );
    const MVGApp = (
      <MvgApp
        {...mvgAppProps}
        brandInformation={brandInformation}
        productMarketing={<MvgMarketingComponent {...mvgAppProps} />}
        crosslinks={<MvgSmartCrosslinks {...mvgAppProps} />}
        breadcrumbs={<MvgBreadCrumbs {...mvgAppProps} />}
        brandCode={brandCode}
        market={market}
      />
    );

    const OOSApp = <OutOfStockApp {...oosAppProps} />;

    const hasBackground = ['on', 'at'].includes(brandName);
    const containerClass = hasBackground ? 'product-page-container' : '';

    // eslint-disable-next-line @typescript-eslint/no-shadow
    const ReWrittenAppTemplate = (props: PageParams) => (
      <PageWrapper {...props} pageType='product'>
        <LocalizationProvider locale={locale} market={market} supportNesting translations={translations}>
          <div className={containerClass} id='pdp-page-content'>
            {rewriteApp}
          </div>
        </LocalizationProvider>
      </PageWrapper>
    );

    const MVGAppTemplate = (appProps: PageParams & { pdpLogger: pino.Logger }) => (
      <PageWrapper {...appProps} pageType='product'>
        <LocalizationProvider locale={locale} market={market} supportNesting translations={translations}>
          <div className={containerClass} id='pdp-page-content'>
            {MVGApp}
          </div>
        </LocalizationProvider>
      </PageWrapper>
    );

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const LegacyAppTemplate = ({ appProps, seo }: { appProps: any; seo: any }) => (
      <PageWrapper {...appProps} pageType='product'>
        <Script id='seo-page-schema' type='application/ld+json'>
          {seo.seoPageSchema || ''}
        </Script>
        <Script id='seo-breadcrumbs-schema' type='application/ld+json'>
          {seo.seoBreadcrumbSchema || ''}
        </Script>
        {appMain}
      </PageWrapper>
    );

    const OutOfStockTemplate = (appProps: PageParams & { pdpLogger: pino.Logger }) => (
      <PageWrapper {...appProps} pageType='product'>
        <LocalizationProvider locale={locale} market={market} supportNesting translations={translations}>
          <div className={containerClass} id='pdp-page-content'>
            {OOSApp}
          </div>
        </LocalizationProvider>
      </PageWrapper>
    );

    if (pdpCAPIv3Enabled) {
      const [App, resolvedAppName] = await renderCapiV3App({
        props,
        pdpLogger,
        requestParamString,
        pdpOos2025Enabled,
        oosAppProps,
        mvgAppProps,
        OutOfStockTemplate,
        LegacyAppTemplate,
        MVGAppTemplate,
        pdpHUIRedesignEnabled,
        ReWrittenAppTemplate,
      });
      _appName = resolvedAppName;
      return App;
    }

    // eslint-disable-next-line @typescript-eslint/no-shadow
    const [App, resolvedAppName] = await renderLegacyApp({
      props,
      requestParamString,
      pdpHUIRedesignEnabled,
      ReWrittenAppTemplate,
      LegacyAppTemplate,
    });
    _appName = resolvedAppName;
    return App;
  } catch (error) {
    const formattedError = {
      message: error instanceof Error ? error.message : 'An unexpected error occurred',
      name: error instanceof Error ? error.name : 'UnknownError',
      app: _appName,
      brand: brandInformation.brandName,
      market: brandInformation.marketAwareBrandCode,
      pid: props.searchParams['pid'],
      pdpCAPIv3Enabled: isPdpCAPIv3Enabled,
      pdpHUIRedesignEnabled: isPdpHUIRedesignEnabled,
      pdpMVGRefreshEnabled: isPdpMVGRefreshEnabled,
      requestParamString: rawRequestParamString,
      targetEnv,
      ...props,
    };
    pdpLogger.error(formattedError);
    let redirectUrl = '/browse/GeneralNoResults.do';
    if (isPDPErrors(error)) {
      redirectUrl = error.url;
    }
    return redirect(redirectUrl);
  }
}
