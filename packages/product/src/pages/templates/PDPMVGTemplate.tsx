import { PageParams } from '@ecom-next/sitewide/pages/PageWrapper';
import { ScriptLoaderProvider } from '@pdp/packages/script-loader-provider';
import { ABSegProvider } from '@pdp/src/app/components/abseg-provider';
import classNames from 'classnames';
import { Breakpoint, XLARGE } from '@ecom-next/core/components/breakpoint-provider';
import type { AppWrapperApps } from '../Product';
import { MVGBuyBoxServer } from '../../components/buy-box/mvg-buy-box-wrapper/mvg-buy-box-server';
import { getUrlParamsString } from '../helpers';
import { getPageContextData } from '../getPageState';
import { cacheableEnabledFeaturesV2Promise, cacheableFeaturesConfigMVG } from '../getReWrittenData';
import { MvgProductTitleClient } from '../../components/product-title';
import { MVGMarketingFlagClient } from '../../components/buy-box/components/marketing-flag/MVGMarketingFlagClient';
import { ImageGalleryMVG } from '../../components/image-gallery/mvg/ImageGalleryMVG';
import { MVGReviews } from '../../components/reviews/MVGReviews';
import { MvgFindMine } from '../../components/find-mine/MvgFindMine';
import { MvgPDPRecsCarouselWrapper } from '../../components/pdp-recs-carousel-wrapper/mvg/MvgPDPRecsCarouselWrapper';
import { MvgSocialGallery } from '../../components/social-gallery/MvgSocialGallery';

export const PDPMVGTemplate = async ({
  searchParams,
  crosslinks,
  brandInformation,
  productMarketing,
  breadcrumbs,
  isMultiVariantProduct,
}: PageParams & AppWrapperApps) => {
  const requestParamString = getUrlParamsString(searchParams);
  const [featureConfig, { abSeg }] = await Promise.all([cacheableFeaturesConfigMVG(requestParamString), cacheableEnabledFeaturesV2Promise(requestParamString)]);
  const { isDesktop } = getPageContextData();
  const {
    findmine: { isFindmineEnabled },
    reviews: { showReviews },
    bricks: { isBricksEnabled },
    marketingContainerEnabled,
    marketingContainerPlacement,
    recommendationsConfig,
    isPercentageEnabled,
    bottomBreadcrumbsEnabled,
    isAthletaHUIChanges,
    isRewriteBreadcrumbsEnabled,
  } = featureConfig;

  const breadcrumbClasses = classNames('pdp-top-breadcrumbs', {
    'desktop-breadcrumbs': isDesktop,
    'bg-white': !isDesktop,
    'top-breadcrumbs': isDesktop && isBricksEnabled,
  });

  const gridClasses = classNames('grid', {
    'bg-white grid-cols-1': !isDesktop,
    'single-column-layout-grid': isDesktop && ['br', 'brfs'].includes(brandInformation.brandName),
    'bricks-layout-grid': isDesktop && isBricksEnabled,
    'thumbnail-layout-grid': isDesktop && !isBricksEnabled && !['br', 'brfs'].includes(brandInformation.brandName),
  });

  const commonProps = { searchParams };
  const containerPositions = { higher: 'a', lower: 'b' };

  const renderDesktopMarketingContainer = (position: string) => {
    if (!marketingContainerEnabled || marketingContainerPlacement.desktop !== position) {
      return null;
    }

    return (
      <Breakpoint is='greaterOrEqualTo' size={XLARGE}>
        {productMarketing}
      </Breakpoint>
    );
  };

  return (
    <ScriptLoaderProvider>
      {(isRewriteBreadcrumbsEnabled || !bottomBreadcrumbsEnabled) && <div className={breadcrumbClasses}>{breadcrumbs}</div>}
      <div className={gridClasses} id='gallery-wrapper'>
        <div className={`m-4 block sm:hidden${!isDesktop && isRewriteBreadcrumbsEnabled ? ' mt-2' : ''}`}>
          <MvgProductTitleClient isAthletaHUIChanges={isAthletaHUIChanges} />
          <MVGMarketingFlagClient isStyleLevelFlag />
        </div>
        <ImageGalleryMVG {...commonProps} />
        <MVGBuyBoxServer
          requestParamString={requestParamString}
          searchParams={searchParams}
          brand={brandInformation.brandName}
          productMarketing={productMarketing}
        />
      </div>

      <ABSegProvider abSeg={abSeg} brandName={brandInformation.brandName}>
        <MvgSocialGallery requestParamString={requestParamString} />
        {renderDesktopMarketingContainer(containerPositions.higher)}
        {isFindmineEnabled && <MvgFindMine {...commonProps} brand={brandInformation.brandName} />}
        {renderDesktopMarketingContainer(containerPositions.lower)}
        <MvgPDPRecsCarouselWrapper
          isMultiVariantProduct={!!isMultiVariantProduct}
          isPercentageEnabled={isPercentageEnabled}
          recommendationsConfig={recommendationsConfig}
          reportCertonaLoad
        />
      </ABSegProvider>

      {showReviews && <MVGReviews searchParams={searchParams} />}
      {crosslinks}
      {!isRewriteBreadcrumbsEnabled && bottomBreadcrumbsEnabled && <div className={breadcrumbClasses}>{breadcrumbs}</div>}
    </ScriptLoaderProvider>
  );
};
