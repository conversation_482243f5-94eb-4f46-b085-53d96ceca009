import type { PageParams } from '@ecom-next/sitewide/pages/PageWrapper';
import { ScriptLoaderProvider } from '@pdp/packages/script-loader-provider'; // NOSONAR
import { ABSegProvider } from '@pdp/src/app/components/abseg-provider'; // NOSONAR
import classNames from 'classnames';
import type { AppWrapperApps } from '../Product';
import { OosBuyBoxServer } from '../../components/buy-box/oos-buy-box-wrapper/oos-buy-box-server';
import { getUrlParamsString } from '../helpers';
import { getPageContextData } from '../getPageState';
import { cacheableEnabledFeaturesV2Promise, cacheableFeaturesConfigMVG } from '../getReWrittenData';
import { MvgProductTitleClient } from '../../components/product-title';
import { MVGMarketingFlagClient } from '../../components/buy-box/components/marketing-flag/MVGMarketingFlagClient';
import { ImageGalleryMVG } from '../../components/image-gallery/mvg/ImageGalleryMVG';
import { MvgPDPRecsCarouselWrapper } from '../../components/pdp-recs-carousel-wrapper/mvg/MvgPDPRecsCarouselWrapper';

export type OutOfStockTemplateProps = Omit<PageParams, 'params'> & Omit<AppWrapperApps, 'breadcrumbs' | 'crosslinks' | 'productMarketing'>;

export const OutOfStockTemplate = async ({ searchParams, brandInformation, isMultiVariantProduct }: OutOfStockTemplateProps) => {
  const requestParamString = getUrlParamsString(searchParams);
  const [featureConfig, { abSeg }] = await Promise.all([cacheableFeaturesConfigMVG(requestParamString), cacheableEnabledFeaturesV2Promise(requestParamString)]);
  const { isDesktop } = getPageContextData();
  const { recommendationsConfig, isPercentageEnabled, isAthletaHUIChanges } = featureConfig;

  const gridClasses = classNames('grid bg-white', {
    'grid-cols-1': !isDesktop,
    'grid-cols-[1fr_minmax(390px,390px)] gap-[30px] px-[30px] pt-6': isDesktop,
  });

  const productRecsWrapperClasses = classNames({
    'px-[30px]': isDesktop,
  });

  const commonProps = { searchParams, isFullyOOS: true };

  return (
    <ScriptLoaderProvider>
      <div className={gridClasses} id='oos-template-wrapper'>
        <div className='mx-4 my-2 mb-0 block sm:hidden'>
          <MvgProductTitleClient isAthletaHUIChanges={isAthletaHUIChanges} />
          <MVGMarketingFlagClient isStyleLevelFlag />
        </div>
        <ImageGalleryMVG {...commonProps} />
        <OosBuyBoxServer requestParamString={requestParamString} searchParams={searchParams} brand={brandInformation.brandName} />
      </div>

      <ABSegProvider abSeg={abSeg} brandName={brandInformation.brandName}>
        <div className={productRecsWrapperClasses}>
          <MvgPDPRecsCarouselWrapper
            isPercentageEnabled={isPercentageEnabled}
            recommendationsConfig={recommendationsConfig}
            layoutType='grid'
            reportCertonaLoad={true}
            isMultiVariantProduct={!!isMultiVariantProduct}
          />
        </div>
      </ABSegProvider>
    </ScriptLoaderProvider>
  );
};
