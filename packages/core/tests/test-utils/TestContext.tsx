// import { defaultPage<PERSON>ontext } from "./init-state";
import { StitchStyleProvider, StitchInverseStyleProvider } from '@ecom-next/core/react-stitch';
import { PageContextProvider, PageAppState } from '@ecom-next/sitewide/app-state-provider';
import BrandInfoProvider from '@ecom-next/sitewide/brand-info-provider';
import defaultBrandSiteData from './fixtures/brandSiteData.json';
import { MarketingProvider } from '@ecom-next/marketing-ui/marketing-provider';
import { Provider as FeatureFlagsProvider, FeatureFlagsContextData } from '@ecom-next/core/legacy/feature-flags';
import { StickyProviderFragment } from '@sitewide/components/legacy/sticky-manager-new/StickyProviderFragment';
import mockDatalayer from '@mfe/data-layer';
import { BreakpointProvider, LARGE, Size, XLARGE, SMALL } from '@ecom-next/core/breakpoint-provider';
import LocalizationProvider, { deNormalizeLocale, normalizeLocale } from '@ecom-next/sitewide/localization-provider';
import { Translation, getLocaleSpecificTranslations } from '@ecom-next/sitewide/localization';
import { PersonalizationContext, PersonalizationContextData } from '@ecom-next/sitewide/personalization-provider';
import { SitewideContextProvider } from '@ecom-next/core/legacy/sitewide';
import { PropsWithChildren } from 'react';
import { LocalizationContextT } from '@ecom-next/sitewide/localization-provider';
import { MarketingContent } from '@mui/fetchMarketing';

jest.mock(
  '@ecom-next/core/legacy/feature-flags',
  () => {
    const path = jest.requireActual('node:path');
    const fs = jest.requireActual('node:fs');

    const portedCoreUiPath = path.resolve(__dirname, '../../src/components/legacy');
    const hasCoreUiPorted = fs.existsSync(portedCoreUiPath);

    const { createContext } = jest.requireActual('react');
    const MockContext = createContext(null);

    const mod = hasCoreUiPorted
      ? jest.requireActual('@ecom-next/core/legacy/feature-flags')
      : { FeatureFlagsContext: MockContext, Provider: MockContext.Provider };

    return mod;
  },
  { virtual: true }
);

jest.mock('@ecom-next/sitewide/personalization-provider', () => {
  const orig = jest.requireActual('@ecom-next/sitewide/personalization-provider');

  return {
    __esModule: true,
    ...orig,
    getPersonalizationData: jest.fn(),
  };
});

jest.mock('@mfe/data-layer', () => {
  const orig = jest.requireActual('@mfe/data-layer').default;

  return {
    ...orig,
    builderNames: jest.fn(() => 'builder1'),
    isTealiumReady: jest.fn(() => true),
    add: jest.fn(orig.add),
    view: jest.fn(orig.view),
    viewWith: jest.fn(orig.viewWith),
    link: jest.fn(orig.link),
    viewWithQueue: jest.fn(orig.viewWithQueue),
  };
});

// afterEach(() => {
//   window.gap = {...defaultPageContext};
// });

export type TestContextProps<ExtendedAppState = Record<string, unknown>> = PropsWithChildren<{
  appState?: Partial<PageAppState & ExtendedAppState>;
  breakpoint?: Size;
  enabledFeatures?: FeatureFlagsContextData['enabledFeatures'];
  localization?: Omit<LocalizationContextT, 'translations'> & { translations: Translation };
  personalizationData?: PersonalizationContextData;
  invert?: boolean;
  content: MarketingContent;
}>;

export const JsonMarketing = jest.fn().mockImplementation(() => null);
export const CmsMarketing = jest.fn().mockImplementation(() => null);

const defaultPersonalizationData = {
  isLoading: false,
  isError: false,
  totalItemCount: '0',
  firstName: '',
  isLoggedInUser: false,
  tier: 'CORE',
  totalRewardValue: '0',
  shoppingBag: { totalItemCount: '0' },
} satisfies PersonalizationContextData;

function TestContextWrapper<AppState>(pageContext: TestContextProps<AppState>): JSX.Element {
  const context = {
    ...(pageContext.appState || {}),
    ...pageContext,
  };

  const defaultContext = window.gap;
  let breakpointWithDefault = context.breakpoint || defaultContext.breakpoint;
  breakpointWithDefault = breakpointWithDefault === LARGE || breakpointWithDefault === XLARGE || breakpointWithDefault === 'Desktop' ? LARGE : SMALL;
  const breakpoint = breakpointWithDefault === LARGE ? ('Desktop' as const) : ('Mobile' as const);
  const brandName = context.brand || context.brandName || context.brandAbbr || defaultContext.brand;
  const isDesktop = breakpoint === 'Desktop';

  const {
    invert,
    cid,
    children,
    enabledFeatures = {},
    personalizationData = defaultPersonalizationData,
    localization,
    appState: contextAppState,
    apis,
    targetEnv = 'prod',
    abSeg = {},
    brandSiteData = defaultBrandSiteData,
    hosts = {},
    content = {},
    accountDropdownItems,
  } = context;

  const locale = deNormalizeLocale(localization?.locale || context.locale || defaultContext.locale);
  const translations =
    localization?.translations?.[normalizeLocale(locale)]?.translation || localization?.translations || localization || getLocaleSpecificTranslations(locale);

  const market = context.market || (locale === 'en_CA' || locale === 'fr_CA' ? ('ca' as const) : ('us' as const));

  const appState = {
    locale,
    isDesktop,
    brand: brandName,
    brandName,
    brandAbbr: brandName,
    cid,
    brandSiteData,
    breakpoint,
    market,
    datalayer: context.datalayer || mockDatalayer,
    pageType: contextAppState?.pageType,
    brandtype: context.brandtype || defaultContext.brandtype,
    ecomApiBaseUrl: defaultContext.ecomApiBaseUrl,
    apis,
    targetEnv,
    abSeg,
    hosts,
    accountDropdownItems,
  };

  return (
    <PageContextProvider value={appState}>
      <StitchStyleProvider brand={brandName}>
        <StitchInverseStyleProvider invert={invert}>
          <BreakpointProvider initialSizeClass={isDesktop ? 'Desktop' : 'Mobile'} testSize={context.breakpoint}>
            <LocalizationProvider locale={locale} market={market} translations={translations}>
              <BrandInfoProvider brand={appState.brand} market={market}>
                <PersonalizationContext.Provider value={personalizationData}>
                  <FeatureFlagsProvider value={{ enabledFeatures }}>
                    <MarketingProvider value={content} jsonMarketingComponent={JsonMarketing} cmsMarketingComponent={CmsMarketing}>
                      <SitewideContextProvider>
                        <StickyProviderFragment>{children}</StickyProviderFragment>
                      </SitewideContextProvider>
                    </MarketingProvider>
                  </FeatureFlagsProvider>
                </PersonalizationContext.Provider>
              </BrandInfoProvider>
            </LocalizationProvider>
          </BreakpointProvider>
        </StitchInverseStyleProvider>
      </StitchStyleProvider>
    </PageContextProvider>
  );
}

export const TestContext = global.window ? TestContextWrapper : () => null;
export default TestContext;
