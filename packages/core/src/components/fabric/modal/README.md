# Modal

The Modal displays information which calls for user's attention.

## Usage

The modal focuses user attention on a specific task or content by temporarily overlaying the main interface. The Modal component uses the z-index of dialog which is 710.

### Required Props

| Name       | Type      | Description                               |
| ---------- | --------- | ----------------------------------------- |
| `children` | ReactNode | The content to be displayed in the modal. |

### Optional Props

| Name                         | Type                     | Default                             | Description                                                                                                                                                                                                                                                                                                                                                            |
| ---------------------------- | ------------------------ | ----------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `id`                         | `string`                 |                                     | Id for the modal element. If one is not passed then it will be generated.                                                                                                                                                                                                                                                                                              |
| `backButtonAriaLabel`        | string                   | `Go back to previous modal content` | The aria label for the back button which appears when multiple modal contents are available.                                                                                                                                                                                                                                                                           |
| `callbackFn`                 | function                 | N/A                                 | A callback function invoked after modal is closed.                                                                                                                                                                                                                                                                                                                     |
| `className`                  | string                   | `fds_modal`                         | This prop can be used to customize the Modal component further by overriding the default styles.                                                                                                                                                                                                                                                                       |
| `footerContent`              | ReactNode                | N/A                                 | if passed will render a sticky footer                                                                                                                                                                                                                                                                                                                                  |
| `closeButtonAriaLabel`       | string                   | N/A                                 | The aria text for the close icon button.                                                                                                                                                                                                                                                                                                                               |
| `disableCloseOnClickOutside` | boolean                  | N/A                                 | A flag to disable the outside click to close the Modal.                                                                                                                                                                                                                                                                                                                |
| `disableCloseOnEscape`       | boolean                  | N/A                                 | A flag to disable escape key to close the Modal.                                                                                                                                                                                                                                                                                                                       |
| `disablePortal`              | boolean                  | N/A                                 | A flag to disable/enable the portal.                                                                                                                                                                                                                                                                                                                                   |
| `focusTrapDelay`             | number                   | 500                                 | The time delay in milliseconds before the focus trap is applied to the modal.                                                                                                                                                                                                                                                                                          |
| `hasSkinnyHeader`            | boolean                  | N/A                                 | A flag to indicate whether the modal has skinny header or not.                                                                                                                                                                                                                                                                                                         |
| `headerAlignment`            | `HeaderAlignmentType`    | `center`                            | Position of the header context. Allowed values are `center` and `left`.                                                                                                                                                                                                                                                                                                |
| `headerContent`              | ReactNode                | N/A                                 | The content of the modal header to be displayed when the header is enabled.                                                                                                                                                                                                                                                                                            |
| `isCrossBrand`               | boolean                  | `false`                             | If true, Modal component will identify the theme as Cross Brand.                                                                                                                                                                                                                                                                                                       |
| `isFullModal`                | boolean                  | N/A                                 | When provided, it will cover the entire screen for mobile resolutions.                                                                                                                                                                                                                                                                                                 |
| `isOpen`                     | boolean                  | N/A                                 | A flag to indicate whether the modal is open or not.                                                                                                                                                                                                                                                                                                                   |
| `navigationCallBack`         | function                 | N/A                                 | A callback function to call to go back to the previous modal content for the multiple content scenario.                                                                                                                                                                                                                                                                |
| `portalRoot`                 | stringElement / Selector | `#portal-root`                      | The name of the modal root container or an element.                                                                                                                                                                                                                                                                                                                    |
| `showNavigationChevron`      | boolean                  | `false`                             | A flag to handle navigation to a previous content when the modal traverses through multiple contents.                                                                                                                                                                                                                                                                  |
| `withCloseIcon`              | boolean                  | `true`                              | A flag to indicate whether the modal has a close icon or not.                                                                                                                                                                                                                                                                                                          |
| `withHeader`                 | boolean                  | `true`                              | A flag to indicate whether the modal has header content or not. If the header is unavailable but the close icon is enabled, the close icon has the risk of overlapping the text of the content (like a long title text). To avoid this, the content must be adjusted like reducing the width of the title container or setting a margin-right for the title container. |

## Component Styles

The styles for the Modal component are defined in the file `packages/core/src/components/fabric/modal/styles.css`.

- The base classname for the modal is `fds_modal`.
- For CrossBrand, the classname is `fds_modal crossbrand`.
- The Modal component can be customized using the `className` prop.

## Using the Component

#### For main themes

```jsx
<Modal>
  <div>This is a modal content</div>
</Modal>
```

#### With overridden prop values

```jsx
<Modal
  className='custom-modal'
  closeButtonAriaLabel='Click to close the modal'
  disableCloseOnClickOutside={false}
  disableCloseOnEscape={false}
  disablePortal={false}
  hasSkinnyHeader
  headerContent={<p>Header</p>}
  headerAlignment='left'
  focusTrapDelay={100}
  isFullModal
  isOpen
  withCloseIcon
  withHeader
  navigationCallBack={goBackToPreviousContent}
  showNavigationChevron={true}
  backButtonAriaLabel='Go back to previous content'
>
  <div>This is a modal content</div>
</Modal>
```

#### For Cross Brand

```jsx
<Modal isCrossBrand>
  <div>This is a modal content</div>
</Modal>
```

## Custom styling variables

The custom Figma variables used for the Modal component are available in the file `packages/core/src/components/fabric/modal/styles.css`.
