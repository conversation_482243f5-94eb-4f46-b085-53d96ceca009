import React from 'react';
import { fireEvent, render, screen, waitFor } from 'test-utils';
import { Modal } from '../index';
import useFocusTrap from '../../accessibility/useFocusTrap';

jest.mock('../../accessibility/useFocusTrap', () => jest.fn(() => jest.fn()));

describe('Modal component', () => {
  it('should render correctly when open', () => {
    render(
      <Modal isOpen={true} headerContent='Header Content'>
        <div>Modal Content</div>
      </Modal>
    );
    expect(screen.getByText('Header Content')).toBeInTheDocument();
    expect(screen.getByText('Modal Content')).toBeInTheDocument();
  });

  it('should not render when closed', () => {
    render(
      <Modal isOpen={false} headerContent='Header Content'>
        <div>Modal Content</div>
      </Modal>
    );
    expect(screen.queryByText('Header Content')).not.toBeInTheDocument();
    expect(screen.queryByText('Modal Content')).not.toBeInTheDocument();
  });

  it('should close the modal when the close button is clicked', async () => {
    render(
      <Modal isOpen={true} withCloseIcon={true} headerContent='Header Content'>
        <div>Modal Content</div>
      </Modal>
    );
    await waitFor(() => {
      fireEvent.click(screen.getByLabelText('Click to close the Modal'));
    });
    expect(screen.queryByText('Modal Content')).not.toBeInTheDocument();
  });

  it('should close the modal when clicking outside', async () => {
    render(
      <Modal id='modal' isOpen={true} disableCloseOnClickOutside={false} headerContent='Header Content'>
        <div>Modal Content</div>
      </Modal>
    );
    await waitFor(() => {
      fireEvent.mouseDown(document.getElementById('fds_modal__isolation-layer-modal')!);
    });
    expect(screen.queryByText('Modal Content')).not.toBeInTheDocument();
  });

  it('should not close the modal when clicking outside if disableCloseOnClickOutside is true', async () => {
    render(
      <Modal id='modal' isOpen={true} disableCloseOnClickOutside={true} headerContent='Header Content'>
        <div>Modal Content</div>
      </Modal>
    );
    await waitFor(() => {
      fireEvent.mouseDown(document.getElementById('fds_modal__isolation-layer-modal')!);
    });
    expect(screen.getByText('Modal Content')).toBeInTheDocument();
  });

  it('should close the modal when the Escape key is pressed', async () => {
    render(
      <Modal isOpen={true} disableCloseOnEscape={false} headerContent='Header Content'>
        <div>Modal Content</div>
      </Modal>
    );
    await waitFor(() => {
      fireEvent.keyDown(document, { key: 'Escape' });
    });
    expect(screen.queryByText('Modal Content')).not.toBeInTheDocument();
  });

  it('should not close the modal when the Escape key is pressed if disableCloseOnEscape is true', async () => {
    render(
      <Modal isOpen={true} disableCloseOnEscape={true} headerContent='Header Content'>
        <div>Modal Content</div>
      </Modal>
    );
    await waitFor(() => {
      fireEvent.keyDown(document, { key: 'Escape' });
    });
    expect(screen.getByText('Modal Content')).toBeInTheDocument();
  });

  it('should not render header if header and close icon are disabled', async () => {
    render(
      <Modal isOpen={true} withHeader={false} withCloseIcon={false} headerContent='Header Content'>
        <div>Modal Content</div>
      </Modal>
    );

    expect(screen.queryByText('Header Content')).not.toBeInTheDocument();
  });

  it('should render regular header if header is not disabled but close icon is disabled', async () => {
    render(
      <Modal isOpen={true} withHeader hasSkinnyHeader={false} withCloseIcon={false} headerContent='Header Content'>
        <div>Modal Content</div>
      </Modal>
    );

    expect(screen.getByText('Header Content')).toBeInTheDocument();
    expect(screen.getByTestId('modal')).not.toHaveClass('skinny-header');
  });

  it('should not render close icon when close icon is disabled', async () => {
    render(
      <Modal isOpen={true} withCloseIcon={false} headerContent='Header Content'>
        <div>Modal Content</div>
      </Modal>
    );

    expect(screen.queryByLabelText('Click to close the Modal')).not.toBeInTheDocument();
  });

  it('should render skinny header correctly', async () => {
    render(
      <Modal isOpen={true} withHeader={false} hasSkinnyHeader withCloseIcon={false} headerContent='Header Content'>
        <div>Modal Content</div>
      </Modal>
    );

    expect(screen.getByTestId('modal')).toHaveClass('fds_modal fds_modal--skinny-header fds_modal--no-header');
  });

  it('should render the footer when footerContent is passed', () => {
    render(
      <Modal isOpen={true} footerContent={<button>submit</button>}>
        <div>Modal Content</div>
      </Modal>
    );
    const buttonFooter = screen.getByText('submit');

    expect(buttonFooter).toBeInTheDocument();
  });

  it('should call focus trap with the default focusTrapDelay value', () => {
    render(
      <Modal isOpen={true} headerContent='Header Content'>
        <div>Modal Content</div>
      </Modal>
    );

    expect(useFocusTrap).toHaveBeenCalledWith({ isActive: true, isContentUpdated: false, onCompleteReload: expect.any(Function), focusDelay: 500 });
  });

  it('should call focus trap with the custom focusTrapDelay value', () => {
    render(
      <Modal isOpen={true} headerContent='Header Content' focusTrapDelay={100}>
        <div>Modal Content</div>
      </Modal>
    );

    expect(useFocusTrap).toHaveBeenCalledWith({ isActive: true, isContentUpdated: false, onCompleteReload: expect.any(Function), focusDelay: 100 });
  });
});
