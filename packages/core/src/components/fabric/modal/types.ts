import { PortalProps } from '@ecom-next/core/fabric/portal';

export type HeaderAlignmentType = 'center' | 'left';

export type ModalProps = {
  /**
   * Optional id for the modal component.
   */
  id?: string;
  /**
   * The aria label for the back button which appears when multiple modal contents are available.
   */
  backButtonArialLabel?: string;
  /**
   * A callback value to return values.
   */
  callbackFn?: (values?: object) => void;
  /**
   * The content of the modal passed as children.
   */
  children: React.ReactNode;
  /**
   * The custom class name to override the default styles.
   */
  className?: string;
  /**
   * The aria label for the close icon.
   */
  closeButtonAriaLabel?: string;
  /**
   *  A flag to disable the outside click to close the Popover.
   */
  disableCloseOnClickOutside?: boolean;
  /**
   * A flag to disable escape key to close the Popover.
   */
  disableCloseOnEscape?: boolean;
  /**
   * A flag to disable portal. Default value is false.
   */
  disablePortal?: boolean;
  /**
   * The time delay in milliseconds before the focus trap is applied to the modal. Default value is 500.
   */
  focusTrapDelay?: number;
  /**
   * Will be rendered as a sticky footer.
   */
  footerContent?: React.ReactNode;
  /**
   * A flag to indicate whether the modal has skinny header or not.
   */
  hasSkinnyHeader?: boolean;
  /**
   * The alignment of the header content. Default value is 'center'.
   */
  headerAlignment?: HeaderAlignmentType;
  /**
   * The content of the modal header to be displayed when the header is enabled.
   */
  headerContent?: React.ReactNode;
  /**
   * When provided, it will load the styles specific to the Cross Brand.
   */
  isCrossBrand?: boolean;
  /**
   * When provided, it will cover the entire screen for mobile resolution.
   */
  isFullScreenOnMobile?: boolean;
  /**
   * A flag to indicate whether the modal is open or not.
   */
  isOpen?: boolean;
  /**
   * A callback function to call to go back to the previous modal content for the multiple content scenario.
   */
  navigationCallBack?: () => void;
  /**
   * The portal root container. Default value is `#portal-root`.
   */
  portalRoot?: PortalProps['container'];
  /**
   * A flag to handle navigation to a previous content when the modal traverses through multiple contents.
   */
  showNavigationChevron?: boolean;
  /**
   * A flag to indicate whether the modal has a close icon or not. Default value is true.
   */
  withCloseIcon?: boolean;
  /**
   * A flag to indicate whether the modal has header content or not. Default value is true.
   */
  withHeader?: boolean;
};
