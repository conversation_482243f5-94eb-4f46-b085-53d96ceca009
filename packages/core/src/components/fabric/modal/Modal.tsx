import React, { useState, useEffect, useId } from 'react';
import classNames from 'classnames';
import { CloseIcon, AccordionChevronIcon } from '@ecom-next/core/fabric/icons';
import { Portal } from '@ecom-next/core/fabric/portal';
import useFocusTrap from '../accessibility/useFocusTrap';
import { ModalContext } from './ModalContext';
import { ModalProps } from './types';

const Modal: React.FC<ModalProps> = ({
  id,
  backButtonArialLabel = 'Go back to previous modal content',
  callbackFn,
  children,
  footerContent,
  className,
  closeButtonAriaLabel = 'Click to close the Modal',
  disableCloseOnClickOutside = false,
  disableCloseOnEscape = false,
  disablePortal = false,
  focusTrapDelay = 500,
  hasSkinnyHeader = false,
  headerContent,
  headerAlignment = 'center',
  isCrossBrand = false,
  isFullScreenOnMobile = false,
  isOpen = false,
  navigationCallBack,
  portalRoot = '#portal-root',
  showNavigationChevron = false,
  withCloseIcon = true,
  withHeader = true,
}) => {
  const generatedId = useId();
  const modalIsolationLayerId = id ? `fds_modal__isolation-layer-${id}` : `fds_modal__isolation-layer-${generatedId}`;
  const modalId = id ? `fds_modal-${id}` : `fds_modal-${generatedId}`;

  const closeModal = () => {
    setModalOpen(false);
    callbackFn?.();
  };

  const requestFocusReload = () => {
    setIsContentUpdated(true);
  };

  const completeFocusReload = () => {
    setIsContentUpdated(false);
  };

  const dismissModal = (event: KeyboardEvent | MouseEvent) => {
    if (event.type === 'keydown' && (event as KeyboardEvent).key === 'Escape') {
      closeModal();

      return;
    }

    const target = event.target as HTMLElement;
    const modalElementId = typeof target?.id === 'string' ? target?.id : '';

    if (event.type === 'mousedown' && modalElementId === modalIsolationLayerId) {
      closeModal();
    }
  };

  const modalClass = classNames(
    'fds_modal',
    {
      crossbrand: isCrossBrand,
      'fds_modal--skinny-header': hasSkinnyHeader,
      'fds_modal--full-modal': isFullScreenOnMobile,
      'fds_modal--no-header': !withHeader,
      'fds_modal--footer': footerContent,
    },
    className
  );
  const [isModalOpen, setModalOpen] = useState<boolean>(isOpen);
  const [isContentUpdated, setIsContentUpdated] = useState<boolean>(false);
  const focusTrapRef = useFocusTrap({ isActive: isOpen, isContentUpdated, onCompleteReload: completeFocusReload, focusDelay: focusTrapDelay });

  useEffect(() => {
    if (!disableCloseOnClickOutside) {
      document.addEventListener('mousedown', dismissModal);
    }
    return () => {
      if (!disableCloseOnClickOutside) {
        document.removeEventListener('mousedown', dismissModal);
      }
    };
  }, [disableCloseOnClickOutside]);

  useEffect(() => {
    if (!disableCloseOnEscape) {
      document.addEventListener('keydown', dismissModal);
    }
    return () => {
      if (!disableCloseOnEscape) {
        document.removeEventListener('keydown', dismissModal);
      }
    };
  }, [disableCloseOnEscape]);

  useEffect(() => {
    setModalOpen(isOpen);
  }, [isOpen]);

  if (!isModalOpen) {
    return null;
  }

  return (
    <Portal disablePortal={disablePortal} container={portalRoot}>
      <div className='fds_modal__container'>
        <div data-testid='fds_modal__isolation-layer' id={modalIsolationLayerId} className='fds_isolation-layer' />
        <div
          role='dialog'
          id={modalId}
          data-testid='modal'
          aria-modal={isModalOpen}
          aria-labelledby={withHeader ? 'model-header' : undefined}
          className={modalClass}
          ref={focusTrapRef}
        >
          {withHeader && (
            <div id='modal-header-container' className='fds_modal__header-container'>
              <div id='model-header' className={`fds_modal__header fds_modal__header--${headerAlignment}`}>
                {showNavigationChevron && (
                  <div className='fds_modal__header-chevron-icon'>
                    <button onClick={navigationCallBack} aria-label={backButtonArialLabel}>
                      <AccordionChevronIcon iconDirection='left' width={16} height={16} viewBox='-1 -2 12 10' strokeWidth={0.25} />
                    </button>
                  </div>
                )}
                {headerContent}
                {withCloseIcon && (
                  <div className='fds_modal-header-close-icon'>
                    <button onClick={closeModal} aria-label={closeButtonAriaLabel}>
                      <CloseIcon />
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}
          <div id='modal-content' className='fds_modal__modal-content'>
            <ModalContext.Provider value={{ closeModal, requestFocusReload }}>
              <div id='content' className='fds_modal__content'>
                {!withHeader && withCloseIcon && (
                  <div className='fds_modal-no-header-close-icon'>
                    <button onClick={closeModal} aria-label={closeButtonAriaLabel}>
                      <CloseIcon />
                    </button>
                  </div>
                )}
                {children}
              </div>
            </ModalContext.Provider>
          </div>
          {footerContent && <div className='fds_modal__footer'>{footerContent}</div>}
        </div>
      </div>
    </Portal>
  );
};

export { Modal };
