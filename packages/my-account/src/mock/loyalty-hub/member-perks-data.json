{"user": {"dob": "April", "tier": "", "points": 0, "nextTier": "", "loyaltyId": "", "phoneNumber": "", "amountSpent": 0, "rewardsBalance": "", "isCardHolder": false, "isBirthdayMonth": false, "amountToSpendToNextTier": 0}, "offers": {"customerOffers": [{"promoId": "1077123", "promoName": "Birthday Discount", "promoDescription": "Get and additional 15% off from Gap for your birthday", "promoCode": "D7JP2L34HB61", "barCode": "9X1D7JP2L34HB61K", "market": "US", "brand": "GP", "startDate": "2025-05-06T14:30:00-07:00", "endDate": "2025-05-16T14:30:00-07:00", "offerType": "Birthday", "amount": 15, "discountType": "Percent off", "legalTerms": "Code valid for 15% off your purchase for one-day only...", "daysToRedeem": 0, "expiresSoon": false}, {"promoId": "1077123", "promoName": "Birthday Discount", "promoDescription": "Get and additional 15% off from Gap for your birthday", "promoCode": "D7JP2L34HB62", "barCode": "9X1D7JP2L34HB612", "market": "US", "brand": "GP", "startDate": "2025-05-06T14:30:00-07:00", "endDate": "2025-05-16T14:30:00-07:00", "offerType": "Birthday", "amount": 15, "discountType": "Percent off", "legalTerms": "Code valid for 15% off your purchase for one-day only...", "daysToRedeem": 0, "expiresSoon": false}, {"promoId": "1077157", "promoName": "Choose Your Own Sale Day", "promoDescription": "Receive an extra 15% off on any purchase at our family of brands", "promoCode": "D7JP2L34HB63", "barCode": "9X1D7JP2L34HB613", "market": "US", "brand": "GP", "startDate": "2025-05-06T14:30:00-07:00", "endDate": "2026-07-10T14:30:00-07:00", "offerType": "CYOSD", "amount": 15, "discountType": "Percent off", "legalTerms": "Code valid for 15% off your purchase for one-day only...", "daysToRedeem": 0, "expiresSoon": false}, {"promoId": "1077158", "promoName": "Choose Your Own Sale Day", "promoDescription": "Receive an extra 20% off on any purchase at our family of brands", "promoCode": "D7JP2L34HB64", "barCode": "9X1D7JP2L34HB614", "market": "US", "brand": "GP", "startDate": "2025-05-06T14:30:00-07:00", "endDate": "2027-11-16T14:30:00-07:00", "offerType": "CYOSD", "amount": 20, "discountType": "Percent off", "legalTerms": "Code valid for 20% off your purchase for one-day only...", "daysToRedeem": 0, "expiresSoon": false}], "bounceBackOffers": [{"amount": 30, "barCode": "9X3PWCVQLKV4F1YY", "brand": "ON", "endDate": "2025-10-12T03:00:00.000-04:00", "daysToRedeem": 4, "legalTerms": "Redeem SuperCash 30 dollar", "promotionCode": "PWCVQLKV4F1Y", "startDate": "2025-10-02T03:00:00.000-04:00", "discountType": "Dollar Off", "expiresSoon": true}, {"amount": 20, "barCode": "9X3PWCVQLKV4F1Y2", "brand": "BR", "endDate": "2025-10-12T03:00:00.000-04:00", "daysToRedeem": 12, "legalTerms": "Redeem StyleCash 20% percent off", "promotionCode": "PWCVQLKV4F12", "startDate": "2025-10-02T03:00:00.000-04:00", "discountType": "Percentage", "expiresSoon": false}]}, "pointsActivity": [], "appliedPromos": {"promoCode": ""}, "convertedPoints": {"points": 0}, "loyaltyHubLandingReq": {"isSuccess": false, "isFail": false}, "loyaltyHubMemberPerksReq": {"isSuccess": false, "isFail": false, "isCardsFail": false, "isBounceBackOffersFail": false, "isCustomerOffersFail": false}, "loyaltyHubMyMembershipReq": {"isSuccess": false, "isFail": false}, "loyaltyHubRewardsMarketplaceReq": {"isSuccess": false, "isFail": false}, "loyaltyHubConvertPointsReq": {"isSuccess": false, "isFail": false}, "loyaltyHubApplyPromoReq": {"isSuccess": false, "isFail": false}}