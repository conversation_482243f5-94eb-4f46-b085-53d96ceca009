{"quickAdd.colorLabel": "Color", "quickAdd.variantGroupLabel": "Variants", "quickAdd.selectASize": "Select a Size", "quickAdd.openModal": "Open Quick Add", "quickAdd.closeModal": "Close Quick Add", "quickAdd.addToBag": "Add to Bag", "quickAdd.addToBagSuccess": "Added to Bag", "quickAdd.addToBagFailure": "Item Not Added", "quickAdd.error.general": "We're having a technical problem on this page. Refresh the page and try your selection again or find another product.", "quickAdd.error.sizeSelection": "Please select a {{unselectedDimensions}} before adding to bag.", "quickAdd.error.itemOutOfStock": "We're sorry, this item is now out of stock. Try another selection.", "quickAdd.onBackOrderMessage": "On back order - est. shipping date {{date}}", "quickAdd.onBackOrderMessage.ariaLabel": "On back order. Estimated shipping date is {{date}}", "homeRedesign.breadcrumbs.home": "Home", "homeRedesign.breadcrumbs.account": "Account", "homeRedesign.breadcrumbs.settings": "Personal Info", "homeRedesign.breadcrumbs.changePassword": "Change Password", "homeRedesign.breadcrumbs.orderDetails": "Order Details", "homeRedesign.breadcrumbs.details": "Details", "homeRedesign.breadcrumbs.shippingAddresses": "Shipping Addresses", "homeRedesign.breadcrumbs.orderHistory": "Order History", "homeRedesign.breadcrumbs.savedCards": "Saved Cards", "homeRedesign.breadcrumbs.orderLookup": "Guest Order Lookup", "homeRedesign.breadcrumbs.communicationPreferences": "Communication Preferences", "homeRedesign.breadcrumbs.giftCardsBalance": "Gift Card Balance", "homeRedesign.breadcrumbs.valueCenter": "My Points & Rewards", "homeRedesign.breadcrumbs.myPointsRewardsNew": "Points & Rewards", "homeRedesign.breadcrumbs.purchaseHistory": "Purchase History", "homeRedesign.breadcrumbs.accountSecurity": "Account Security", "homeRedesign.sidebarNav.userCardText": "Hi,", "homeRedesign.sidebarNav.personalInfo": "Personal Info", "homeRedesign.giftCardNav.checkGiftCardBalance": "Check Gift Card Balance", "homeRedesign.giftCardNav.reloadGiftCard": "Reload Gift Card", "homeRedesign.certona.title": "Recently Viewed", "homeRedesign.certona.previous": "Prev", "homeRedesign.certona.next": "Next", "homeRedesign.errors.myPointsRewards": "Your Points & Rewards activity is not available at this time. Please try again later.", "homeRedesign.errors.rewardsNotAvailable": "We’re having trouble accessing your rewards information. Visit my ", "homeRedesign.errors.pointsRewardsLinkText": "points and rewards", "homeRedesign.errors.forMoreInfo": " for more info.", "homeRedesign.errors.myRewardsCreditCards": "Your Rewards Credit Cards activity is not available at this time. Please try again later.", "homeRedesign.myRewardsCreditCards": "My Rewards Credit Cards", "homeRedesign.myRewardsCreditCardsNew": "Rewards Credit Cards", "homeRedesign.rewardsTileRedeemText": "Rewards are redeemed in checkout.", "homeRedesign.rewardsTileAvailableText": "in rewards available.", "homeRedesign.rewardsCardMemberText": "Rewards Member", "homeRedesign.tierText.core": "CORE", "homeRedesign.tierText.enthusiast": "ENTHUSIAST", "homeRedesign.tierText.icon": "ICON", "communicationPreferences.pageHeading": "Communication Preferences", "communicationPreferences.formHeading": "Email Subscription", "communicationPreferences.emailLabel": "Email Address", "communicationPreferences.buttonText": "Subscribe", "communicationPreferences.emailOptIn": "Yes! I would like to receive marketing emails (style news and exclusive offers) from Gap Inc. on behalf of Gap (Canada) Inc. and Old Navy (Canada) Inc. and their brands: Gap, Banana Republic, Old Navy, and Athleta.", "communicationPreferences.textNotification": "TEXT NOTIFICATION", "communicationPreferences.checkBoxText": "Yes! Send me text messages about hot deals and promos.", "communicationPreferences.textNotificationForBR": "Join the Conversation", "communicationPreferences.checkBoxTextForBR": "Sign up to receive text messages from Banana Republic and enjoy a 20% off welcome offer, exclusive access, and more.", "communicationPreferences.checkBoxTextForBRFactory": "Sign up to receive text messages from Banana Republic Factory and enjoy a 20% off welcome offer, new product drops, and more.", "communicationPreferences.checkBoxTextForGapFactory": "Sign up to receive text messages from Gap Factory and enjoy a 20% off welcome offer, new product drops, and more.", "communicationPreferences.phoneNumber": "Phone Number", "communicationPreferences.textDisclaimer": "*Msg & Data Rates May Apply. By entering your phone number, clicking submit, completing the sign-up instructions, you consent to receive one or more recurring marketing text messages each week at the mobile number provided that may be sent via an automated system, and you also consent to the text terms & privacy policy. Consent is not a condition of purchasing goods or services. You can opt-out at any time by responding STOP. You can also respond HELP for help.", "communicationPreferences.signUp": "SIGN UP", "communicationPreferences.unsubscribeButtonText": "Unsubscribe", "communicationPreferences.unsubscribeText": "Enter your email address to unsubscribe from all marketing emails. You'll still receive transactional and account-related emails.", "communicationPreferences.unsubscribeOneText": "Unsubscribe from all {{brand}} marketing emails.", "communicationPreferences.unsubscribeAllText": "Unsubscribe from all Gap, Old Navy, Banana Republic, Athleta, Gap Factory, and Banana Republic Factory marketing emails.", "communicationPreferences.textTerms.consentToRecieve": "By submitting your phone number you consent to receive recurring automated personalized (e.g. cart reminders) and marketing text messages from ", "communicationPreferences.textTerms.atCellNum": " at the cell phone number used when signing up.", "communicationPreferences.textTerms.termsAndConditions": " Terms & Conditions", "communicationPreferences.textTerms.termsAndConditionsCA": " {{brand}} Terms & Conditions", "communicationPreferences.textTerms.learnMore": " apply. Check our ", "communicationPreferences.textTerms.privacyPolicy": "Privacy Policy", "communicationPreferences.textTerms.disclaimer": " to learn how we handle your personal information. Consent is not a condition of purchase. Reply HELP for help and STOP to opt out. Msg frequency varies. Msg and data rates apply. Contact {{brand}} at Canada Customer Service, 9500. McLaughlin, Road North, Brampton, ON, L6X 0B8, Canada + **************.", "communicationPreferences.textTerms.disclaimerCA": " to learn how we handle your personal information. Consent is not a condition of purchase. Reply HELP for help and STOP to opt out. Msg frequency varies. Msg and data rates apply. Contact {{brand}} at Canada Customer Service, 9500. McLaughlin, Road North, Brampton, ON, L6X 0B8, Canada + **************.", "communicationPreferences.category.categoryHeader": "Which categories are you interested in? (optional)", "communicationPreferences.category.categoryWomens": "Women", "communicationPreferences.category.categoryMens": "Men", "communicationPreferences.category.categoryMaternity": "Maternity", "communicationPreferences.category.categoryKids": "Kids", "communicationPreferences.category.categoryBaby": "Baby", "communicationPreferences.category.at": "Athleta", "communicationPreferences.category.atGirl": "Athleta Girl", "communicationPreferences.category.categoryGirls": "Girls", "communicationPreferences.category.categoryBoys": "Boys", "communicationPreferences.category.categoryToddlerGirls": "Toddler Girls 12m-5T", "communicationPreferences.category.categoryToddlerBoys": "Toddler Boys 12m-5T", "communicationPreferences.category.categoryBabyWithAges": "Baby 0-24m", "communicationPreferences.category.selectAll": "Select All", "communicationPreferences.category.categoryHome": "Home", "communicationPreferences.birthday.header": "Sign up for your birthday gift", "communicationPreferences.birthday.text": "Yes, send me my birthday gift", "communicationPreferences.birthday.selectMonth": "Select Month", "communicationPreferences.birthday.selectDay": "Select Day", "communicationPreferences.maternity.header": "If you’d like, share your due date with us!", "communicationPreferences.maternity.selectMonth": "Select Month", "communicationPreferences.maternity.selectYear": "Select Year", "communicationPreferences.rewards.rewardsPrefix": "Bonus! Rewards Members, use the email associated with your Membership for", "communicationPreferences.rewards.rewardsEmphasised": " 250 BONUS POINTS ", "communicationPreferences.rewards.rewardsSuffix": "when you subscribe.*", "communicationPreferences.rewards.emailOptInPopover": "*Rewards Members who sign up for Marketing Emails will earn 250 bonus points. Points will post to account within 14 days. Valid for first-time registrants only to Gap Inc. on behalf of Gap (Canada) Inc. and Old Navy (Canada) Inc. and their brands: Gap, Old Navy, Athleta and Banana Republic.", "communicationPreferences.rewards.notMember": "Not a Rewards Member?", "communicationPreferences.rewards.joinNowUnderlined": "Join Now", "communicationPreferences.rewards.joinNowNotUnderlined": " for Free.", "communicationPreferences.rewards.freeSignup": "Sign up for free today.", "communicationPreferences.postalCode.heading": "Postal Code", "communicationPreferences.postalCode.label": "Postal Code", "communicationPreferences.postalCode.details": "Please provide your postal code to receive information on events in your area.", "communicationPreferences.athletaModal.mmac": "Yes! Mail me an Athleta Catalog", "communicationPreferences.athletaModal.firstName": "First Name", "communicationPreferences.athletaModal.lastName": "Last Name", "communicationPreferences.athletaModal.addressLine1": "Address line 1", "communicationPreferences.athletaModal.addressLine2": "Address line 2", "communicationPreferences.athletaModal.city": "City", "communicationPreferences.athletaModal.US.state": "State", "communicationPreferences.athletaModal.US.zipcode": "Zip Code", "communicationPreferences.athletaModal.CA.state": "Province", "communicationPreferences.athletaModal.CA.zipcode": "Postal Code", "communicationPreferences.ATCalalog.successNotification": "Success! Your communication preferences have been updated.", "communicationPreferences.ATCalalog.failNotification": "We weren't able to update your communication preferences. Please try again.", "communicationPreferences.subscribe.successNotification": "Success! Your communication preferences have been updated.", "communicationPreferences.subscribe.failNotification": "We weren't able to update your communication preferences. Please try again.", "communicationPreferences.subscribe.optInDisclosure1": "Yes!  I would like to receive style news and promotions from Gap Inc., Gap (Canada) Inc., Old Navy (Canada) Inc., Banana Republic, and Athleta. Review our ", "communicationPreferences.subscribe.optInDisclosure2": " and ", "communicationPreferences.subscribe.optInDisclosure3": " if you have any questions.", "communicationPreferences.subscribe.privacyPolicy": "Privacy Policy", "communicationPreferences.subscribe.contactUs": "Contact Us", "communicationPreferences.textMessage.successNotification": "Success! Your communication preferences have been updated.", "communicationPreferences.textMessage.failNotification": "We weren't able to update your communication preferences. Please try again.", "communicationPreferences.unsubscribe.successNotification": "Success! Your communication preferences have been updated.", "communicationPreferences.unsubscribe.failNotification": "We weren't able to update your communication preferences. Please try again.", "communicationPreferences.language.heading": "Language", "communicationPreferences.language.options.english": "English", "communicationPreferences.language.options.french": "French", "resetYourPasswordModal.resetYourPasswordTitle": "Reset your password", "resetYourPasswordModal.createPassword": "CREATE NEW PASSWORD", "resetYourPasswordModal.confirmPassword": "Confirm Password", "resetYourPasswordModal.savePassword": "SAVE PASSWORD", "resetYourPasswordModal.resetPasswordFor": "Reset password for: ", "resetYourPasswordModal.passwordSentTitle": "Your password is updated", "resetYourPasswordModal.errorLabels.passwordCannotBeBlank": "Password cannot be blank.", "resetYourPasswordModal.errorLabels.passwordsDoNotMatch": "Passwords do not match.", "resetYourPasswordModal.errorLabels.passwordCriteriaNotMet": "Password criteria are not met.", "resetYourPasswordModal.validationRules.eightToTwentyFourChars": "8 to 24 characters", "resetYourPasswordModal.validationRules.aLowercaseLetter": "A lowercase letter", "resetYourPasswordModal.validationRules.anUppercaseLetter": "An uppercase letter", "resetYourPasswordModal.validationRules.aNumber": "A number", "resetYourPasswordModal.validationRules.aSpecialChar": "A special character", "resetYourPasswordModal.forcedResetPassword.exampleSpecialChar": "- ! @ # $ % ^ & * ( ) _ +", "resetYourPasswordModal.forcedResetPassword.forcedResetLabel1": "Thanks for shopping with us, ", "resetYourPasswordModal.forcedResetPassword.forcedResetLabel2": ". Please take a moment to update and strengthen your password.", "resetYourPasswordModal.forcedResetPassword.forcedResetTitle": "Protect Your Account", "resetYourPasswordModal.forcedResetPassword.title": "Protect your account", "resetYourPasswordModal.forcedResetPassword.btnText": "Send Email", "resetYourPasswordModal.forcedResetPassword.backToSignIn": "Back to Sign In", "resetYourPasswordModal.forcedResetPassword.description-1": "To help protect your account, please update and strengthen your password. We'll send a reset password link to {{email}}", "resetYourPasswordModal.forcedResetPassword.description-2": "To help protect your account, please update and strengthen your password. We'll email a reset password link.", "resetYourPasswordModal.forcedResetPassword.errorMessage": "We're sorry, this page is temporarily unavailable. Try again later or call customer service {supportNumber} if the problem persists.", "resetYourPasswordModal.forcedResetPassword.error": "Unfortunately a technical error has occurred. <PERSON><PERSON>d the email to try again.", "resetYourPasswordModal.forcedResetPassword.resendEmailBtnText": "<PERSON><PERSON><PERSON>", "resetYourPasswordModal.forcedResetPassword.successTitle": "Check Your Inbox", "resetYourPasswordModal.forcedResetPassword.successDescription": "We've sent instructions on how to reset your password to {{email}}", "resetYourPasswordModal.forcedResetPassword.successDescription2": "We've emailed instructions on how to reset your password.", "resetYourPasswordModal.ariaLabeling.forcedResetHeaderAriaLabel1": "Thanks for shopping with us, ", "resetYourPasswordModal.ariaLabeling.forcedResetHeaderAriaLabel2": ". You are inside of the password reset menu, which has been placed for your protection. Your password is outdated and must be updated in order to continue checking out as a returning customer. ", "resetYourPasswordModal.ariaLabeling.passwordCriteriaAriaLabel": "Update your password to protect your account. The password criteria is as follows: Your password needs to be between eight and twenty four characters long, include a lower case letter, an upper case letter, a number and a special character, including at least one of the following: a dash, at sign, hashtag, dollar sign, percent sign, upper arrow, ampersand, star, left bracket, right bracket, underscore, plus sign. Once you enter your password, you will need to confirm your password by entering it a second time in the following text input and they must match in order for your new password to be saved successfully. Please enter your new password now. ", "resetYourPasswordModal.ariaLabeling.confirmPasswordAriaLabel": "Please confirm your new password. Please enter the same password again so it can be successfully saved to your account.", "resetYourPasswordModal.ariaLabeling.ariaPasswordCriteriaErrorLabel1": "The password you entered does not meet the criteria. Your password must ", "resetYourPasswordModal.ariaLabeling.ariaPasswordMismatchErrorLabel": "The password you entered does not meet the criteria. Your passwords do not match", "resetYourPasswordModal.ariaLabeling.ariaPasswordErrorUpdateInputLabel": ". Please press shift, tab to go back to update your input.", "resetYourPasswordModal.ariaLabeling.ariaValidationRules.hasEightToTwentyFourChars": "have eight to twenty four characters, ", "resetYourPasswordModal.ariaLabeling.ariaValidationRules.hasLowercaseChar": "include a lowercase character, ", "resetYourPasswordModal.ariaLabeling.ariaValidationRules.hasUppercaseChar": "include an uppercase character, ", "resetYourPasswordModal.ariaLabeling.ariaValidationRules.hasANumber": "include a number, ", "resetYourPasswordModal.ariaLabeling.ariaValidationRules.hasSpecialChar": "include a special character, including at least one of the following: a dash, exclamation sign, at sign, hashtag, dollar sign, percent sign, upper arrow, ampersand, star, left bracket, right bracket, underscore, plus sign ", "passwordSentModal.passwordUpdatedTitle": "Your password is updated", "passwordSentModal.continueShopping": "CONTINUE SHOPPING", "passwordSentModal.passwordResetAriaLabel": "Your password is updated. Press enter to continue shopping.", "passwordSentModal.forcedResetPassword.passwordUpdatedLabel": "For your security, any billing information — including credit card details — have been deleted from your account.", "passwordSentModal.forcedResetPassword.btnLabel": "Return to checkout", "passwordSentModal.forcedResetPassword.ariaLabel": "Your password is updated. For your security, any billing information — including credit card details — have been deleted from your account. Press enter to return to checkout.", "finalForm.example": "e.g. ", "finalForm.optional": "Optional", "usedLinkModal.header": "Used Link", "usedLinkModal.usedLinkTitle": "You've already used this link", "usedLinkModal.usedLinkLabel": "Sign in with your new password, or ", "usedLinkModal.signinText": "Sign in", "usedLinkModal.forgotYourPassword": "request another link", "usedLinkModal.errorEmailFailed": "Error: Failed to send email. Please try again later.", "usedLinkModal.resentEmailSuccess": "We have resent instructions on how to reset your password.  Once you have changed your password, you can sign in.", "forgotPasswordModal.sendEmailTitle": "Forgot your password?", "forgotPasswordModal.enterEmailAddress": "Enter your email address below. We'll send you a link to reset your password.", "forgotPasswordModal.emailAddressLabel": "EMAIL ADDRESS", "forgotPasswordModal.sendEmail": "SEND EMAIL", "forgotPasswordModal.backToSignIn": "BACK TO SIGN IN", "forgotPasswordModal.invalidEmail": "Enter a valid email address.", "forgotPasswordModal.blankEmail": "Email address cannot be blank", "forgotPasswordModal.checkInbox": "Check your inbox", "forgotPasswordModal.emailSentText": "If {{email}} is associated with an account, we’ll email you instructions to reset your password.", "forgotPasswordModal.OkLabel": "OK", "forgotPasswordModal.emailSendFailed": "Error: Failed to send email address. Please try again later.", "expiredLinkPage.expiredLinkTitle": "This link has expired", "expiredLinkPage.returnToForgotPasswordPart1": "Please return to ", "expiredLinkPage.returnToForgotPasswordPart2": " to get a new reset password link.", "expiredLinkPage.forgotYourPassword": "Forgot Your Password", "emailSentModal.emailSentTitle": "Email sent", "emailSentModal.emailSentLabel": "We've sent instructions on how to reset your password to ", "emailSentModal.changePasswordLabel": ". Once you have changed your password, you can sign in.", "emailSentModal.backToSignIn": "BACK", "emailSentModal.ok": "OK", "accountLogin.barclays.creditCardApplication": "credit card application", "accountLogin.barclays.getStarted": "Let’s Get Started", "accountLogin.barclays.signInOrCreateAccount": "Sign in or create your free account to begin your credit card application.", "accountLogin.barclays.emailRewardsProfile": "Use the email tied to your Rewards profile to save time and make using your credit card easier.", "accountLogin.barclays.applyWithoutLinking": "Go directly to the credit card application without linking your rewards profile.", "accountLogin.barclays.continueWithoutAccount": "Continue Without An Account", "accountLogin.barclays.signInToAccount": "Sign In to Your Account", "accountLogin.errors.accountAlreadyExist": "This email address is already registered. If you already have an account with one of our sister brands, you can sign in with the same email address.", "accountLogin.errors.sessionTimeout": "Your session timed out. For your security, please sign in again.", "accountLogin.errors.passwordsDoNotMatch": "Passwords do not match.", "accountLogin.errors.noEmailSocialSignin": "An email is required to continue with Facebook.", "accountLogin.errors.pingUniqueIdErrorNotification": "There was an error. Please try again later.", "accountLogin.errors.verifyEmailError": "We couldn't verify your email. Please try again.", "accountLogin.errors.pingFlowIdExpired": "Your session has timed out. Try again.", "accountLogin.errors.accountNotExists": "We didn’t recognize the username entered. Try again or create an account.", "accountLogin.errors.accountExists": "This email address is already associated with an account. If you have an account with any of our family of brands, you can sign in from any Gap Inc. site with this email address.", "accountLogin.errors.resetPasswordFail": "<PERSON><PERSON> failed to send. Please try again later.", "accountLogin.form.emailAddressLabel": "Email address", "accountLogin.form.emailLabel": "Email", "accountLogin.form.password": "Password", "accountLogin.form.passwordLabel": "Create a password", "accountLogin.form.passwordCriteria": "Passwords must contain 8 to 24 characters including an uppercase letter, a lowercase letter, a number, and a special character.", "accountLogin.loyalty.backButton": "Back button", "accountLogin.loyalty.buttonContinue": "Continue", "accountLogin.loyalty.tryAgain": "Try again", "accountLogin.loyalty.headerText": "Enter your email address", "accountLogin.loyalty.enterEmail": "Enter Your Email Address", "accountLogin.loyalty.headerTextSignInSegmentation": "Sign In | Join", "accountLogin.loyalty.paragraphText": "Enter your email to get started.", "accountLogin.loyalty.emailAddressLabel": "Email Address", "accountLogin.loyalty.signIn": "Sign In", "accountLogin.loyalty.signInBarclaysButton": "Sign in and Apply for Card", "accountLogin.loyalty.messaging": "Complete your card application within 20 minutes of signing in to link your approved card to your Rewards Account.", "accountLogin.loyalty.barclays.messaging": "Complete your card application within 20 minutes of signing in to link your approved card to your Rewards Account.", "accountLogin.navigationTabs.signupText": "CREATE AN ACCOUNT", "accountLogin.navigationTabs.signinText": "SIGN IN", "accountLogin.navigationTabs.backToShoppingText": "Back to shopping", "accountLogin.navigationTabs.hiddenBodyText": "You are on the sign up menu to create a user account with Gap, Banana Republic, Old Navy and Athleta brands. Please tab to proceed forward with creating an account. To return to sign in, please press shift and tab. If you are a brand card holder and have a credit card with us, please press ...... to skip to card holders' sign in", "accountLogin.createAccount.nextButtonLabel": "NEXT", "accountLogin.createAccount.createButtonLabel": "CREATE ACCOUNT", "accountLogin.createAccount.barclaysHeader": "Create Account", "accountLogin.createAccount.barclaysSubHeader": "Setting up an account will help you get the most out of your credit card.", "accountLogin.createAccount.createButtonLabelBarclays": "Create Account and Apply for Card", "accountLogin.createAccount.createButtonLabelLoyality": "CREATE ACCOUNT AND CHECKOUT", "accountLogin.createAccount.trackYourOrders": "Track your orders", "accountLogin.createAccount.enjoyPersonalizedExp": "Enjoy a personalized experience", "accountLogin.createAccount.earnRewards": "<PERSON><PERSON><PERSON> for shopping", "accountLogin.createAccount.requiredField": "This field is required.", "accountLogin.createAccount.invalidEmail": "Enter a valid email address.", "accountLogin.createAccount.blankEmail": "Email address cannot be blank.", "accountLogin.createAccount.invalidPassword": "Enter a valid password.", "accountLogin.createAccount.blankPassword": "Password cannot be blank.", "accountLogin.createAccount.blankFirstName": "Enter your first name.", "accountLogin.createAccount.blankLastName": "Enter your last name.", "accountLogin.createAccount.blankFullName": "Enter a first name and last name", "accountLogin.createAccount.blankMobile": "Enter your mobile number.", "accountLogin.createAccount.emailAddressLabel": "Email Address", "accountLogin.createAccount.passwordLabel": "Create a Password", "accountLogin.createAccount.passwordConfirmLabel": "Confirm Password", "accountLogin.createAccount.firstName": "First Name", "accountLogin.createAccount.lastName": "Last Name", "accountLogin.createAccount.firstAndLastName": "First and Last Name", "accountLogin.createAccount.mobileNumber": "Mobile Number", "accountLogin.createAccount.mobileNumberOptionalText": "Optional", "accountLogin.createAccount.mobileNumberOptional": "Mobile Number (optional)", "accountLogin.createAccount.dateOfBirthOptional": "Birthday MM/DD (optional)", "accountLogin.createAccount.dateOfBirth.invalid": "Enter a valid date of birth (MM/DD).", "accountLogin.createAccount.dateOfBirth.invalidMonth": "Enter a valid birth month (MM).", "accountLogin.createAccount.dateOfBirth.invalidDay": "Enter a valid birth day (DD).", "accountLogin.createAccount.phoneNote": "Add phone number for easier rewards lookup in store", "accountLogin.createAccount.accountCreated": "Your account has been created.", "accountLogin.createAccount.welcomeLoyalty": "Welcome!", "accountLogin.createAccount.errorNotification": "We're sorry, this page is temporarily unavailable. Try again later or call customer service at {{brandNumber}} if the problem persists.", "accountLogin.createAccount.welcomeWithoutLoyalty": "Become a Rewards Member", "accountLogin.createAccount.welcomeTextBarclays": "Create an Account", "accountLogin.createAccount.welcomeNoteExpanded": "You’re one step away from free shipping on all orders $50+, points on each purchase, and more!", "accountLogin.createAccount.welcomeNoteExpandedBarclays": "Create an account to link a new Gap Inc. Rewards Credit Card with other Membership benefits like free shipping on orders $50+, points on each purchase, and more!", "accountLogin.createAccount.WelcomeNoteExpandedWithLoyalty": "Your Rewards Program just got better. Create a password to unlock free shipping on all orders $50+, points on each purchase, and more!", "accountLogin.createAccount.emailAlreadyRegistered": "Use another email address", "accountLogin.createAccount.show": "SHOW", "accountLogin.createAccount.hide": "HIDE", "accountLogin.createAccount.globalMessageError": "Please try again. We're experiencing a temporary interruption in service.", "accountLogin.createAccount.globalMessagePingError": "There was an error. Please try again later.", "accountLogin.createAccount.globalMessagePingErrorV2": "There was an error. Please try again.", "accountLogin.createAccount.legalDisclosure": "By creating an account, you’re joining the Gap Inc. Rewards Program and agree to the Terms & Conditions and Privacy Policy of the Rewards Program and consent to receive marketing emails.", "accountLogin.createAccount.rewardAccount": "Your Rewards account has been created. Sign in to continue.", "accountLogin.createAccount.isAccountExists": "Account with given email address already exists.", "accountLogin.createAccount.isCreateAccountError": "We couldn't create your account. Please try again.", "accountLogin.createAccount.isPingCreateAccountError": "We were able to create your account. However, something went wrong and we could not sign you in automatically. Please use the same email and password combination to sign in.", "accountLogin.createAccount.signinCheckout": "Sign In and Checkout", "accountLogin.createAccount.transactionalEmailDisclosure": "Transactional emails. By joining, you consent to receive transactional emails related to your membership, including welcome email, recurring membership status statements and/or updates, and reminders of upcoming expiration of earned points. Transactional emails are different from Marketing emails. You can only stop transactional emails by cancelling your membership in the program. Marketing emails are optional. You will only receive them if you give your consent above.", "accountLogin.createAccount.passwordPreExistError": "Password not associated with this email address. You have a limited number of attempts before your account is temporarily locked.", "accountLogin.canadaLogin.welcome": "Welcome!", "accountLogin.canadaLogin.welcomeBack": "Welcome Back!", "accountLogin.canadaLogin.welcomeBecomeRewardsMember": "Become a Rewards Member", "accountLogin.canadaLogin.welcomeNote": "Sign in for a faster checkout, order history, account details, and more!", "accountLogin.canadaLogin.welcomeNoteCreateAccount": "You’re one step away from creating an account, easily viewing your order history, and more.", "accountLogin.canadaLogin.welcomeNoteExpanded": "You’re one step away from access to free shipping, points on each purchase, and more!", "accountLogin.canadaLogin.welcomeNotePhaseTwo": "Sign in for access to free shipping, points on each purchase, and more!", "accountLogin.canadaLogin.welcomeNotePhaseTwoNoLoyalty": "Your account now comes with rewards! Sign in for access to free shipping, points on each purchase, and more!", "accountLogin.canadaLogin.passwordCriteria": "Use your Gap, Old Navy, Banana Republic, or Athleta account password. It must contain 8 to 24 characters including an uppercase letter, a lowercase letter, a number, and a special character.", "accountLogin.canadaLogin.marketingEmailOptIn": "Yes! I would like to receive marketing emails (style news and exclusive offers) from from Gap Inc. on its own behalf and on behalf of its related companies Old Navy (Canada) Inc. and Gap (Canada) Inc. and their brands: Gap, Old Navy, Athleta and Banana Republic. I understand I can withdraw my consent to receive marketing emails at any time.", "accountLogin.canadaLogin.marketingEmailOptInHeader": "Check the box below to sign up for marketing emails and get", "accountLogin.canadaLogin.marketingEmailOptInSubHeader": "250 BONUS POINTS", "accountLogin.canadaLogin.marketingEmailOptInPopover": "Rewards Members who sign up for Marketing Emails will earn 250 bonus points. Points will post to account within 14 days. Valid for first-time registrants only to Gap Inc. on behalf of Gap (Canada) Inc. And Old Navy (Canada) Inc. and their brands: Gap, Old Navy, Athleta and Banana Republic.", "accountLogin.canadaLogin.loyaltyContactUs": "Contact us.", "accountLogin.canadaLogin.marketingLoyaltyAgreement": "Yes! I would like to receive marketing emails from Gap Inc. on behalf of Gap (Canada) Inc. and Old Navy (Canada) Inc. and their brands: Gap, Banana Republic, Old Navy, and Athleta.", "accountLogin.canadaLogin.guestCheckoutTitle": "Or", "accountLogin.canadaLogin.signInAndCheckout": "Sign in and checkout", "accountLogin.canadaLogin.createAccountAndCheckout": "Create Account and checkout", "accountLogin.canadaLogin.completeAccountAndCheckout": "Complete Account and checkout", "accountLogin.canadaLogin.loyaltyQuestions": "Questions?", "accountLogin.canadaLogin.loyaltyUnsubscribe": "You can unsubscribe at any time.", "accountLogin.canadaLogin.welcomeNoteExpandedWithLoyalty": "Your Rewards Program just got better. Create a password to unlock access to free shipping, points on each purchase, and more!", "accountLogin.canadaLogin.completeAccount": "Complete Account", "accountLogin.canadaPrivacyPolicy.privacyPolicy": "https://www.gapinc.com/en-ca/consumer-privacy-policy", "accountLogin.canadaContactInfo.gap.url": "https://www.gapcanada.ca/customerService/info.do?cid=2136", "accountLogin.canadaContactInfo.gap.label": "gap.com contact", "accountLogin.canadaContactInfo.gp.url": "https://www.gapcanada.ca/customerService/info.do?cid=2136", "accountLogin.canadaContactInfo.gp.label": "gap.com contact", "accountLogin.canadaContactInfo.at.url": "https://athleta.gapcanada.ca/customerService/info.do?cid=44959", "accountLogin.canadaContactInfo.on.url": "https://oldnavy.gapcanada.ca/customerService/info.do?cid=3171", "accountLogin.canadaContactInfo.on.label": "oldnavy.gap.com contact", "accountLogin.canadaContactInfo.br.url": "https://bananarepublic.gapcanada.ca/customerService/info.do?cid=6740", "accountLogin.canadaContactInfo.brfs.url": "https://bananarepublicfactory.gapfactory.ca/customerService/info.do?cid=1037881", "accountLogin.canadaContactInfo.br.label": "bananarepublic.gap.com contact", "accountLogin.canadaContactInfo.gapfs.url": "https://www.gapfactory.ca/customerService/info.do?cid=1037175", "accountLogin.canadaContactInfo.gapfs.label": "gapfactory.ca contact", "accountLogin.canadaContactInfo.gpfs.url": "https://www.gapfactory.ca/customerService/info.do?cid=1037175", "accountLogin.canadaContactInfo.gpfs.label": "gapfactory.ca contact", "accountLogin.login.welcome": "Welcome back!", "accountLogin.login.welcomeNote": "Sign in for free shipping on all orders $50+, points on each purchase, and more!", "accountLogin.login.welcomeNoteBarclays": "Sign in to link your existing account to your new Gap Inc. Rewards Credit Card, upon approval.", "accountLogin.login.signinText": "Sign in", "accountLogin.login.goToSignIn": "Go to Sign In", "accountLogin.login.tryAgain": "Something went wrong. Please try again.", "accountLogin.login.hiddenBodyText": "You are on the sign up menu to create a user account with Gap, Banana Republic, Old Navy and Athleta brands. Please tab to proceed forward with creating an account. To return to sign in, please press shift and tab. If you are a brand card holder and have a credit card with us, please press ...... to skip to card holders' sign in", "accountLogin.login.sendEmailTitle": "Forgot your password?", "accountLogin.login.invalidPingSignInCredens": "We didn't recognize the username or password you entered. Please try again.", "accountLogin.login.notAllowedPingSignIn": "Your account has been locked. Please call customer service.", "accountLogin.login.accountLocked": "Your account has been temporarily locked. Try again later or reset your password.", "accountLogin.login.emailTextHeader": "We’ll send you a link or one-time code to reset your password.", "accountLogin.login.sendVerificationLinkToEmail": "Send Link To Email", "accountLogin.login.sendVerificationCodeByText": "Send Code By Text", "accountLogin.login.keepMeSignedIn": "Keep me signed in on this device. Uncheck if using a public device/network.", "accountLogin.login.moreInformation": "More information", "accountLogin.login.popoverText": "Check this box and you won't need to sign in as often. If you change your mind, sign out and uncheck this box before signing in. We do not recommend using this feature on a public device.", "accountLogin.login.signingIn": "Signing In", "accountLogin.login.forgotPassword": "Forgot your password?", "accountLogin.login.emailLink": "We’ll email you a link so you can reset it.", "accountLogin.login.sendEmailBtn": "SEND EMAIL", "accountLogin.login.emailWillBeSent": "The email will be sent to:", "accountLogin.login.emailSentSuccess": "<PERSON><PERSON>", "accountLogin.login.signInDataStoreError": "We’re experiencing technical issues. Please try again later.", "accountLogin.guestLogin.guestCheckoutTitle": "Or checkout as a guest. Guests don’t qualify for free shipping.*", "accountLogin.guestLogin.guestCheckoutDisclaimer": "*Guests only qualify for free shipping during select promotions.", "accountLogin.guestLogin.continueAsGuestButton": "Continue As Guest", "accountLogin.guestLogin.dividerText": "OR", "accountLogin.guestLogin.continueToCheckoutButton": "Checkout as guest", "accountLogin.guestLogin.errorNotification": "We're sorry, this page is temporarily unavailable. Try again later or call customer service at {{brandNumber}} if the problem persists.", "accountLogin.guestLogin.isGuestCheckoutError": "We're unable to checkout you as guest. Please try again.", "accountLogin.brandCard.label": "Looking to manage your {{brand}} card?", "accountLogin.recaptchaDisclaimer.siteProtected": "This site is protected by reCAPTCHA and the Google", "accountLogin.recaptchaDisclaimer.privacyPolicy": "Privacy Policy", "accountLogin.recaptchaDisclaimer.and": "and", "accountLogin.recaptchaDisclaimer.termsOfService": "Terms of Service", "accountLogin.recaptchaDisclaimer.apply": "apply", "accountLogin.legalDisclosure.signIn": "By signing in, you're joining the Gap Inc. Rewards program and agree to the", "accountLogin.legalDisclosure.signUp": "By creating an account, you’re joining the Gap Inc. Rewards Program and agree to the", "accountLogin.legalDisclosure.termsAndCondition": "Terms & Conditions", "accountLogin.legalDisclosure.and": "and", "accountLogin.legalDisclosure.privacyPolicy": "Privacy Policy", "accountLogin.legalDisclosure.reward": "of the Rewards program and consent to receive marketing emails. Check our", "accountLogin.legalDisclosure.rewardsEmailText": "of the Rewards Program and consent to receive transactional emails related to your membership, including welcome email, recurring statements, and reminders of upcoming points expiration. You can only stop transactional emails by cancelling your membership in the program. You will only receive optional marketing emails them if you give your consent above. Check our", "accountLogin.legalDisclosure.about": "Learn more about the Rewards program", "accountLogin.legalDisclosure.personalInfo": "to learn how we handle your personal information.", "accountLogin.validationRules.eightToTwentyFourChars": "8 to 24 characters", "accountLogin.validationRules.aLowercaseLetter": "A lowercase letter", "accountLogin.validationRules.anUppercaseLetter": "An uppercase letter", "accountLogin.validationRules.aNumber": "A number", "accountLogin.validationRules.aSpecialChar": "A special character", "accountLogin.ariaLabels.showPasswordAriaLabel": "Show password as plain text. Note: this will visually expose your password on the screen.", "accountLogin.loyaltyFooter.gp.linkText": "Manage", "accountLogin.loyaltyFooter.gp.link": "https://gap.syf.com/login/", "accountLogin.loyaltyFooter.gp.text": "your Gap Good Rewards Credit Card", "accountLogin.loyaltyFooter.on.linkText": "Manage", "accountLogin.loyaltyFooter.on.link": "https://oldnavy.syf.com/login/", "accountLogin.loyaltyFooter.on.text": "your Navyist Rewards Credit Card", "accountLogin.loyaltyFooter.br.linkText": "Manage", "accountLogin.loyaltyFooter.br.link": "https://bananarepublic.syf.com/login/", "accountLogin.loyaltyFooter.br.text": "your Banana Republic Rewards Credit Card", "accountLogin.loyaltyFooter.at.linkText": "Manage", "accountLogin.loyaltyFooter.at.link": "https://athleta.syf.com/login/", "accountLogin.loyaltyFooter.at.text": "your Athleta Rewards Credit Card", "accountLogin.loyaltyFooter.gpfs.linkText": "Manage", "accountLogin.loyaltyFooter.gpfs.link": "https://gap.syf.com/login/", "accountLogin.loyaltyFooter.gpfs.text": "your Gap Factory Rewards Credit Card", "accountLogin.loyaltyFooter.brfs.linkText": "Manage", "accountLogin.loyaltyFooter.brfs.link": "https://bananarepublic.syf.com/login/", "accountLogin.loyaltyFooter.brfs.text": "your Banana Republic Factory Rewards Credit Card", "accountLogin.BarclaysFooter.gp.linkText": "Manage", "accountLogin.BarclaysFooter.gp.link": "https://Gap.barclaysus.com", "accountLogin.BarclaysFooter.gp.text": "your Gap Good Rewards Credit Card", "accountLogin.BarclaysFooter.on.linkText": "Manage", "accountLogin.BarclaysFooter.on.link": "https://Oldnavy.barclaysus.com", "accountLogin.BarclaysFooter.on.text": "your Navyist Rewards Credit Card", "accountLogin.BarclaysFooter.br.linkText": "Manage", "accountLogin.BarclaysFooter.br.link": "https://Bananarepublic.barclaysus.com", "accountLogin.BarclaysFooter.br.text": "your Banana Republic Rewards Credit Card", "accountLogin.BarclaysFooter.at.linkText": "Manage", "accountLogin.BarclaysFooter.at.link": "https://Athleta.barclaysus.com", "accountLogin.BarclaysFooter.at.text": "your Athleta Rewards Credit Card", "accountLogin.BarclaysFooter.gpfs.linkText": "Manage", "accountLogin.BarclaysFooter.gpfs.link": "https://Gap.barclaysus.com", "accountLogin.BarclaysFooter.gpfs.text": "your Gap Good Rewards Credit Card", "accountLogin.BarclaysFooter.brfs.linkText": "Manage", "accountLogin.BarclaysFooter.brfs.link": "https://Bananarepublic.barclaysus.com", "accountLogin.BarclaysFooter.brfs.text": "your Banana Republic Rewards Credit Card", "passwordValidation.blankPassword": "Password cannot be blank.", "passwordValidation.passwordsDoNotMatch": "Passwords do not match.", "passwordValidation.passwordCriteriaNotMet": "Password criteria are not met.", "singleOrderLookup.singleOrderLookupTitle": "Single Order Lookup", "singleOrderLookup.singleOrderLookupText": "Please enter your order information to get more details about your order.", "singleOrderLookup.orderNumber": "Order Number", "singleOrderLookup.emailAddress": "Email Address", "singleOrderLookup.findOrderNumber": "Where can I find my order number?", "singleOrderLookup.gapPhoneNumber": "****************", "singleOrderLookup.findOrderNumberPopupText1": "Your order number is included in the email you received when you placed your order. If you no longer have this email, please call ", "singleOrderLookup.findOrderNumberPopupText2": " for assistance.", "singleOrderLookup.signInViewOrderHistory1": "Have an account? ", "singleOrderLookup.signInViewOrderHistory2": " to view your order history", "singleOrderLookup.lookUpOrder": "LOOK UP AN ORDER", "singleOrderLookup.lookUpOrderAuthenticatedFlow": "LOOK UP ORDER", "singleOrderLookup.signIn": "Sign in", "singleOrderLookup.blankEmail": "Enter your email address", "singleOrderLookup.invalidEmail": "Enter a valid email address", "singleOrderLookup.invalidOrderNumber": "This order number is invalid. Order numbers must contain at least 7 characters and include letters and numbers only", "singleOrderLookup.blankOrderNumber": "Enter your order number", "singleOrderLookup.accessibility.enterYourOrderNumber": "You are on Single Order Lookup menu. Please enter your order details to get more information about your order. Enter your order number", "singleOrderLookup.accessibility.findOrderNumber": "Where can I find my order number? Click this link to learn more", "singleOrderLookup.accessibility.enterYourEmail": "Enter your email address", "singleOrderLookup.accessibility.signInToViewOrder": "Have an account? sign in to view your order history.", "orDivider.orLabel": "or", "mobileHeader.backButtonLabel": "Back", "accessibility.ModalCloseButton": "close modal", "brands.on": "Old Navy", "brands.br": "Banana Republic", "brands.gp": "Gap", "brands.at": "Athleta", "brands.hc": "Hill City", "ordersAndReturns.singleOrderLookupLabel": "Look up a single order by entering your order number and the email used to place the order.", "ordersAndReturns.lookupSingleOrderButton": "Look up an order", "ordersAndReturns.ordersAndReturnsHeaderLabel": "Orders & Returns", "ordersAndReturns.trackOrdersStartReturnLabel": "Sign in to track your orders, start a return, and view your order history.", "ordersAndReturns.ordersAndReturnsAriaLabel": "You are inside orders and returns menu, which has 2 options for access. You are currently on Sign in access. You need to sign in to track your orders, start a return or view your order history over the past six months. OR If you made a purchase as a guest, or want to look up a specific order, please use single order access. To use single order access, tab forward. To sign in, please click enter.", "ordersAndReturns.errors.orderNotFound": "Enter a valid email address and order number. Order details are available for up to 13 months.", "ordersAndReturns.errors.globalMessage": "Please try again. We're experiencing a temporary interruption in service.", "shared.giveUsFeedbackLabel": "Give Us Feedback", "shared.signInLabel": "Sign In", "orderSummary.orderedOn": "Ordered on", "orderSummary.orderNumber": "Order number", "orderSummary.totalCost": "Total cost", "orderSummary.orderedFrom": "Ordered from", "orderSummary.packageCount": "Package count", "orderSummary.returnItem": "Return items ", "orderSummary.covidMessage": "Due to concerns about the coronavirus, purchases made between January 1, 2020 and April 30, 2020 have an extended return window through ", "orderSummary.covidMessageDate": "July 1, 2020", "orderSummary.finalSaleNotReturnable": "Final sale items are not eligible for returns", "orderSummary.returnWindowClosed": "Return window closed on", "orderSummary.orderSummaryHeader": "ORDER SUMMARY", "orderSummary.purchaseSummaryHeader": "PURCHASE SUMMARY", "orderSummary.purchased": "Purchased", "orderSummary.purchaseNumber": "Purchase #", "orderSummary.returnWindowClosedOn": "Return window closed on ", "orderSummary.fromThisOrderThrough": "from this order through ", "orderSummary.totalCostDisplayTextSingular": "{{price}} ({{itemCount}} item)", "orderSummary.totalCostDisplayTextPlural": "{{price}} ({{itemCount}} items)", "orderSummary.itemNotShipped": "", "orderSummary.returnAvailable": "One or more items in your order may be available to ", "orderSummary.returnUnAvailableWithBackorder": "Received items in your order are not available for return. ", "orderSummary.returnUnAvailable": "Items in your order are no longer available for return. ", "orderSummary.returnItems": "return. ", "orderSummary.see": "See our ", "orderSummary.returnPolicy": "return policy.", "orderSummary.optoroExchange.returnAvailable": "One or more items in your order may be available to ", "orderSummary.optoroExchange.returnUnAvailableWithBackorder": "Received items in your order are not available for return or exchange. ", "orderSummary.optoroExchange.returnUnAvailable": "Items in your order are no longer available for return or exchange. ", "orderSummary.optoroExchange.returnItems": "return or exchange. ", "orderSummary.optoroExchange.returnPolicy": "returns and exchanges policy.", "orderSummary.optoroExchange.cannotReturn": "Returns can no longer be initiated for items in this order. To reprint label, go to ", "orderSummary.optoroExchange.returnPortal": "returns portal. ", "orderTimeout.orderTimeoutErrorMsgStart": "The order status has changed so we are unable to make any changes to this order.  See our ", "orderTimeout.orderTimeoutErrorMsgEnd": " page for more information or return to Order Details.", "orderTimeout.itemInOrder": "Items in this order:", "orderTimeout.shippingAddress": "Your items will ship to the following address:", "orderTimeout.itemCountSingular": "{{itemCount}} Item", "orderTimeout.itemCountPlural": "{{itemCount}} Items", "orderTimeout.backToDetails": "BACK TO ORDER DETAILS", "orderDetails.newOrderNumber": "New Order", "orderDetails.originalOrderNumber": "Original Order", "orderDetails.header": "Order Details", "orderDetails.details": "Details", "orderDetails.accessibility.pickupItems": "Pick up your items.", "orderDetails.deliveryStatus.itemCountSingular": "{{itemCount}} ITEM", "orderDetails.deliveryStatus.itemCountPlural": "{{itemCount}} ITEMS", "orderDetails.deliveryStatus.cancelledMessage.cancelledItem": "Item unavailable", "orderDetails.deliveryStatus.cancelledMessage.cancelledCard": "Unable to verify payment", "orderDetails.deliveryStatus.cancelledMessage.cancelledCustomer": "Customer requested cancellation", "orderDetails.deliveryStatus.cancelledMessage.cancelledUnavailable": "We weren't able to find these items when the order arrived at our store. We apologize for any inconvenience. Please wait a few days for the hold on your credit card to be released.", "orderDetails.deliveryStatus.cancelledMessage.cancelled": "The cancellation request was received. Please allow 3-5 business days for the hold on your original form of payment to be released.", "orderDetails.deliveryStatus.delayedMessage.late4days": "We're working on your order and it's currently expected to arrive on time.", "orderDetails.deliveryStatus.delayedMessage.fpddDelayed": "We couldn't ship your order by the estimated delivery date. We're actively working on shipping it as soon as possible. For further details, please call {{hotlinePhoneNumber}}.", "orderDetails.deliveryStatus.deliveryStatus.backOrdered": "BACKORDERED", "orderDetails.deliveryStatus.deliveryStatus.delayed": "DELAYED", "orderDetails.deliveryStatus.deliveryStatus.delivered": "DELIVERED", "orderDetails.deliveryStatus.deliveryStatus.orderPlaced": "ORDER PLACED", "orderDetails.deliveryStatus.deliveryStatus.orderReceived": "ORDER RECEIVED", "orderDetails.deliveryStatus.deliveryStatus.outForDelivery": "OUT FOR DELIVERY", "orderDetails.deliveryStatus.deliveryStatus.preparingForShipment": "PREPARING SHIPMENT", "orderDetails.deliveryStatus.deliveryStatus.inTransit": "IN TRANSIT", "orderDetails.deliveryStatus.deliveryStatus.justShipped": "JUST SHIPPED", "orderDetails.deliveryStatus.deliveryStatus.undeliverable": "UNDELIVERABLE", "orderDetails.deliveryStatus.deliveryStatus.exception": "EXCEPTION", "orderDetails.deliveryStatus.deliveryStatus.shipped": "SHIPPED", "orderDetails.deliveryStatus.deliveryStatus.returnRequested": "RETURN REQUESTED", "orderDetails.deliveryStatus.deliveryStatus.returned": "RETURNED", "orderDetails.deliveryStatus.deliveryStatus.cancelled": "CANCELLED", "orderDetails.deliveryStatus.deliveryStatus.cancelled-customer": "CANCELLED", "orderDetails.deliveryStatus.deliveryStatus.cancelled-item": "CANCELLED", "orderDetails.deliveryStatus.deliveryStatus.cancelled-card": "CANCELLED", "orderDetails.deliveryStatus.deliveryStatus.WORKING_ON_YOUR_ORDER": "PREPARING PICKUP", "orderDetails.deliveryStatus.deliveryStatus.CANCELED_ITEM_UNAVALIABLE": "CANCELED - UNAVAILABLE", "orderDetails.deliveryStatus.deliveryStatus.BOPIS_RETURNED": "RETURNED", "orderDetails.deliveryStatus.deliveryStatus.ORDER_READY": "READY FOR PICKUP", "orderDetails.deliveryStatus.deliveryStatus.REFUNDED": "REFUNDED", "orderDetails.deliveryStatus.deliveryStatus.statusUnavailable": "STATUS UNAVAILABLE", "orderDetails.deliveryStatus.deliveryStatus.PICKED_UP": "PICKED UP", "orderDetails.deliveryStatus.deliveryStatus.PICK_UP": "PICKED UP", "orderDetails.deliveryStatus.deliveryStatus.VERIFIED": "VERIFIED", "orderDetails.deliveryStatus.deliveryStatus.AWAITING_CANCELLATION": "AWAITING_CANCELLATION", "orderDetails.deliveryStatus.deliveryStatus.workingOnYourOrder": "PREPARING PICKUP", "orderDetails.deliveryStatus.deliveryStatus.cancelledUnavailable": "CANCELED - UNAVAILABLE", "orderDetails.deliveryStatus.deliveryStatus.orderReady": "READY FOR PICKUP", "orderDetails.deliveryStatus.deliveryStatus.refunded": "REFUNDED", "orderDetails.deliveryStatus.deliveryStatus.pickUp": "PICKED UP", "orderDetails.deliveryStatus.deliveryStatus.verified": "VERIFIED", "orderDetails.deliveryStatus.deliveryStatus.awaitingCancellation": "AWAITING CANCELLATION", "orderDetails.deliveryStatus.deliveryStatus.cancelledItem": "CANCELLED", "orderDetails.deliveryStatus.deliveryStatus.cancelledCard": "CANCELLED", "orderDetails.deliveryStatus.deliveryStatus.cancelledCustomer": "CANCELLED", "orderDetails.deliveryStatus.deliveryStatus.bopisReturned": "RETURNED", "orderDetails.deliveryStatus.deliveryStatus.inProgress": "PROCESSING", "orderDetails.deliveryStatus.deliveryStatus.returnInitiated": "RETURN INITIATED", "orderDetails.deliveryStatus.deliveryStatus.packageUpdate": "PACKAGE UPDATE", "orderDetails.deliveryStatus.deliveryStatus.purchasedInStore": "Purchased In Store", "orderDetails.deliveryStatus.bopisStatusText.returned": "Returned", "orderDetails.deliveryStatus.bopisStatusText.orderReady": "Pick up by {{dateTime}}", "orderDetails.deliveryStatus.bopisStatusText.refunded": "Refunded", "orderDetails.deliveryStatus.bopisStatusText.pickedUp": "Picked up", "orderDetails.deliveryStatus.deliveryStatusBodyText.estimatedDeliveryBy": "Estimated delivery by", "orderDetails.deliveryStatus.deliveryStatusBodyText.estimatedDeliveryIs": "Estimated delivery is", "orderDetails.deliveryStatus.deliveryStatusBodyText.packageDeliveredAt": "Package delivered at", "orderDetails.deliveryStatus.deliveryStatusBodyText.estimatedShipDateBy": "Estimated ship date by", "orderDetails.deliveryStatus.deliveryStatusBodyText.estimatedShipBackBy": "Ship back by", "orderDetails.deliveryStatus.deliveryStatusBodyText.estimatedShipBy": "Estimated ship by", "orderDetails.deliveryStatus.deliveryStatusBodyText.cancelledCustomerCancellation": "Customer-requested cancellation", "orderDetails.deliveryStatus.deliveryStatusBodyText.cancelledItemUnavailable": "Item unavailable", "orderDetails.deliveryStatus.deliveryStatusBodyText.cancelledPotentialFraud": "Unable to verify payment", "orderDetails.deliveryStatus.deliveryStatusBodyText.cancelledGenericReason": "Item cancelled", "orderDetails.deliveryStatus.deliveryStatusBodyText.returnProcessedOn": "Return processed on", "orderDetails.deliveryStatus.deliveryStatusBodyText.bopisReceivedText": "We’ll send you an email as soon as this order is ready. Orders are usually ready within 2 hours of completing the purchase online. Please note: Orders placed after 4 PM will be ready the next day.", "orderDetails.deliveryStatus.deliveryStatusBodyText.bopisItemUnavailable": "We weren't able to find these items when the order arrived at our store. We apologize for any inconvenience. Please wait a few days for the hold on your credit card to be released.", "orderDetails.deliveryStatus.deliveryStatusBodyText.bopisOrderReady": "Available for pickup {dateTime}", "orderDetails.deliveryStatus.deliveryStatusBodyText.bopisOrderReadyComplement": "Pick up your items by ", "orderDetails.deliveryStatus.deliveryStatusBodyText.bopisRefunded": "This order wasn't picked up by {dateTime}. The amount for these items has been refunded back to your original form of payment.", "orderDetails.deliveryStatus.deliveryStatusBodyText.pilotCarrierSchedulePromptHeader": "ACTION REQUIRED:", "orderDetails.deliveryStatus.deliveryStatusBodyText.pilotCarrierSchedulePromptTextNormal": "If you haven't already, you", "orderDetails.deliveryStatus.deliveryStatusBodyText.pilotCarrierSchedulePromptTextEmphasized": "must schedule your white-glove delivery.", "orderDetails.deliveryStatus.deliveryStatusBodyText.pilotCarrierUrlPrefix": "Visit pilot at", "orderDetails.deliveryStatus.deliveryStatusBodyText.pilotCarrierUrlSuffix": "and enter the above tracking number into the shipment number field to schedule or to see any updates.", "orderDetails.deliveryStatus.deliveryStatusBodyText.packageUpdateText": "Click on the tracking number below to see detailed tracking and delivery information.", "orderDetails.deliveryStatus.bopisPopUpMessage": "If your items aren't picked up by this date, we'll refund the amount to your original method of payment.", "orderDetails.deliveryStatus.packageOrdering": "Package {{packageOrdering}} of {{numberOfPackages}}", "orderDetails.deliveryStatus.localTime": "Local Time", "orderDetails.shippingSummaryCard.businessDays": "Business Days)", "orderDetails.shippingSummaryCard.header": "DELIVERY", "orderDetails.shippingSummaryCard.storePurchaseHeader": "STORE PURCHASE", "orderDetails.shippingSummaryCard.itemCountSingular": "({{itemCount}} item)", "orderDetails.shippingSummaryCard.itemCountPlural": "({{itemCount}} items)", "orderDetails.shippingSummaryCard.bopisLabel": "In-Store Pickup ", "orderDetails.shippingSummaryCard.shippingLabel": "Shipping ", "orderDetails.shippingSummaryCard.packageCount": "Package Count: {{itemCount}}", "orderDetails.shippingSummaryCard.alt.pickup": "BOPIS pickup icon", "orderDetails.shippingSummaryCard.alt.shipping": "shipping icon", "orderDetails.shippingSummaryCard.alt.storePurchase": "store purchase icon", "orderDetails.shippingSummaryCard.dropship.packageCount": "Total package count: {{itemCount}}", "orderDetails.shippingSummaryCard.dropship.description": "See below for estimated delivery dates", "orderDetails.paymentSummaryCard.shippingPaymentHeader": "DELIVERY", "orderDetails.paymentSummaryCard.shippingType": "Shipping Type", "orderDetails.paymentSummaryCard.shippingTo": "Shipping to", "orderDetails.paymentSummaryCard.paymentLabel": "Payment", "orderDetails.paymentSummaryCard.paymentType.GIFT_CARD": "Gift Card", "orderDetails.paymentSummaryCard.paymentType.REFUND_CHECK": "Refund Check", "orderDetails.paymentSummaryCard.paymentType.RETURN_CREDIT": "Return Credit", "orderDetails.paymentSummaryCard.paymentType.CREDIT_CARD": "Credit Card", "orderDetails.paymentSummaryCard.paymentType.CHECK": "Check", "orderDetails.paymentSummaryCard.paymentType.PRE_PAID": "Pre-Paid", "orderDetails.paymentSummaryCard.paymentType.BABY_GIFT": "Baby Gift", "orderDetails.paymentSummaryCard.paymentType.VISA_CARD": "Visa", "orderDetails.paymentSummaryCard.paymentType.GAPPLCC_CARD": "Gap Card", "orderDetails.paymentSummaryCard.paymentType.MC_CARD": "MasterCard", "orderDetails.paymentSummaryCard.paymentType.AE_CARD": "American Express", "orderDetails.paymentSummaryCard.paymentType.DISCOVER_CARD": "Discover", "orderDetails.paymentSummaryCard.paymentType.BRPLCC_CARD": "Banana Republic Card", "orderDetails.paymentSummaryCard.paymentType.JCB_CARD": "JCP Card", "orderDetails.paymentSummaryCard.paymentType.ONPLCC_CARD": "Old Navy Card", "orderDetails.paymentSummaryCard.paymentType.DINERS_CARD": "Diners Card", "orderDetails.paymentSummaryCard.paymentType.MS_CARD": "MS Card", "orderDetails.paymentSummaryCard.paymentType.ATPLCC_CARD": "Athleta Card", "orderDetails.paymentSummaryCard.paymentType.PayPal": "PayPal", "orderDetails.paymentSummaryCard.paymentType.AFTERPAY": "Afterpay", "orderDetails.paymentSummaryCard.paymentType.INSTANT_CREDIT": "Merch Return Card", "orderDetails.paymentSummaryCard.paymentType.Cash": "Cash", "orderDetails.paymentSummaryCard.paymentType.Klarna": "<PERSON><PERSON><PERSON>", "orderDetails.paymentSummaryCard.paymentEndingIn": " ending in ", "orderDetails.paymentSummaryCard.shippingOptions.free": "Free shipping (5-7 business days)", "orderDetails.paymentSummaryCard.shippingOptions.fiveToSeven": "5-7 business days", "orderDetails.paymentSummaryCard.shippingOptions.threeToFive": "3-5 business days", "orderDetails.paymentSummaryCard.shippingOptions.twoToThree": "2-3 business days", "orderDetails.paymentSummaryCard.shippingOptions.oneDay": "1 business day", "orderDetails.paymentSummaryCard.linkedExchange": "Exchange credit from order ", "orderDetails.shipmentTrackingCard.trackingNumber": "Tracking Number", "orderDetails.shipmentTrackingCard.packageCarrier": "Package Carrier", "orderDetails.shipmentTrackingCard.showMore": "Show More", "orderDetails.shipmentTrackingCard.showLess": "Show Less", "orderDetails.shipmentTrackingCard.shipmentHistory": "Shipment History", "orderDetails.shipmentTrackingCard.distributionCenter": " (Distribution Center)", "orderDetails.shipmentTrackingCard.deliveryType.GROUND_FREIGHT_PRICING": "Ground with Freight Pricing", "orderDetails.chargesSummary.summaryChargesHeader": "SUMMARY OF CHARGES", "orderDetails.chargesSummary.shippingLabel": "Shipping ({{shippingOptionDescription}})", "orderDetails.chargesSummary.shippingOnlyLabel": "Shipping", "orderDetails.chargesSummary.promotionsAndRewardsLabel": "My Savings", "orderDetails.chargesSummary.giftServicesLabel": "Gift Services", "orderDetails.chargesSummary.taxLabel": "Est. Tax", "orderDetails.chargesSummary.stateTaxLabel": "State Regulatory Fee", "orderDetails.chargesSummary.totalLabel": "Total", "orderDetails.chargesSummary.subtotalLabelPlural": "Subtotal ({{itemCount}} items)", "orderDetails.chargesSummary.subtotalLabelSingular": "Subtotal ({{itemCount}} item)", "orderDetails.chargesSummary.subtotalLabel": "Subtotal", "orderDetails.chargesSummary.free": "FREE", "orderDetails.chargesSummary.returnProductLabel": "Returned Product {{itemCount}}", "orderDetails.chargesSummary.returnsTaxDisclaimer": "Return amount includes tax", "orderDetails.chargesSummary.exchangeCreditLabel": "Exchange Credit", "orderDetails.chargesSummary.promotionsLabel": "Promotions", "orderDetails.chargesSummary.inStorePickup": "In-Store Pickup", "orderDetails.chargesSummary.returnProductLabelPlural": "Returned Products ({{itemCount}} items)", "orderDetails.chargesSummary.returnProductLabelSingular": "Returned Product ({{itemCount}} item)", "orderDetails.chargesSummary.exchangeCreditLabelSingular": "Exchange Credit ({{itemCount}} item)", "orderDetails.chargesSummary.exchangeCreditLabelPlural": "Exchange Credit ({{itemCount}} items)", "orderDetails.chargesSummary.exchangeTaxDisclaimer": "Includes tax", "orderDetails.chargesSummary.homePickupFeeLabelSingular": "Home Pickup Fee ({{itemCount}} item)", "orderDetails.chargesSummary.homePickupFeeLabelPlural": "Home Pickup Fee ({{itemCount}} items)", "orderDetails.chargesSummary.homePickupFeeLabelText": "Home Pickup Fee", "orderDetails.chargesSummary.merchInstantCreditLabelSingular": "Merch Return Card ({{itemCount}} item)", "orderDetails.chargesSummary.merchInstantCreditLabelPlural": "Merch Return Card ({{itemCount}} items)", "orderDetails.chargesSummary.merchInstantCreditLabelText": "Merch Return Card", "orderDetails.chargesSummary.savings": "Savings", "orderDetails.chargesSummary.rewards": "Rewards", "orderDetails.orderSelfServiceSummary.additionalChangesPrompt": "Need to make additional changes?", "orderDetails.orderSelfServiceSummary.cancelItems": "Cancel Items", "orderDetails.orderSelfServiceSummary.cancelOrder": "Cancel Order", "orderDetails.orderSelfServiceSummary.changeShippingAddress": "Change Shipping Address", "orderDetails.orderSelfServiceSummary.contactCustomerService": "Visit Customer Service page", "orderDetails.orderSelfServiceSummary.header": "SELF-SERVICE OPTIONS", "orderDetails.orderSelfServiceSummary.addressUpdateConfirmationText": "You requested to change the address on this order on {{updatedDate}} at {{updatedTime}}. Once the address change is processed, you will receive an email.", "orderDetails.orderSelfServiceSummary.cancelledMessage.cancelledItem": "You requested to cancel item(s) in this order on {{updatedDate}}. Once the cancellation is processed, you will receive an email. Please wait a few days for the hold on your credit card to be released.", "orderDetails.orderSelfServiceSummary.cancelledMessage.cancelledCard": "Unable to verify payment", "orderDetails.orderSelfServiceSummary.cancelledMessage.cancelledCustomer": "Customer requested cancellation", "orderDetails.orderSelfServiceSummary.cancelledMessage.cancelledUnavailable": "We weren't able to find these items when the order arrived at our store. We apologize for any inconvenience. Please wait a few days for the hold on your credit card to be released.", "orderDetails.orderSelfServiceSummary.cancelledMessage.cancelled": "You requested to cancel this order on {{updatedDate}}. Once the cancellation is processed, you will receive an email. Please wait a few days for the hold on your credit card to be released.", "orderDetails.orderSelfServiceSummary.emptySelfServiceText": "No updates to your order can be made due to the order status or type.", "orderDetails.orderSelfServiceSummary.learnMore": "Learn More", "orderDetails.orderSelfServiceSummary.returnOrExchangeItemText": "Return or Exchange an Item", "orderDetails.orderSelfServiceSummary.accessReturnPortalText": "Access Return Portal", "orderDetails.orderSelfServiceSummary.returnAnItem": "Return an Item", "orderDetails.signInCard.signInLabel": "Sign in to view your delivery and payment details for this order.", "orderDetails.signInCard.buttonText": "Sign In", "orderDetails.signInCard.signUpLabel1": "Don't have an online account with our family of brands?", "orderDetails.signInCard.signUpLinkText": "Create an account", "orderDetails.shared.cost": "{{price}}", "orderDetails.shared.deductionAmount": "{{price}}", "orderDetails.productPanel.productName": "{{<PERSON><PERSON><PERSON><PERSON>}}", "orderDetails.productPanel.sku": "#{{skuNumber}}", "orderDetails.productPanel.colorLabel": "Color", "orderDetails.productPanel.sizeLabel": "Size", "orderDetails.productPanel.priceLabel": "Price", "orderDetails.productPanel.promoLabel": "Promo", "orderDetails.productPanel.quantityLabel": "Qty", "orderDetails.productPanel.finalSaleLabel": "FINAL SALE", "orderDetails.productPanel.finalSaleUnreturnableLabel": "Final sale items cannot be returned.", "orderDetails.productPanel.returnByMailLabel": "Returnable by mail only.", "orderDetails.productPanel.imageNotAvailable": "Image Not Available", "orderDetails.productPanel.item": "<PERSON><PERSON>", "orderDetails.productPanel.imageUnavailable": "Image Unavailable", "orderDetails.productPanel.finalSaleText": "Final Sale", "orderDetails.productPanel.noReturnsExchanges": "No Returns or Exchanges", "orderDetails.orderSelfServiceSummary.missingItems": "Missing Items", "orderDetails.productPanel.savings": "Savings", "orderDetails.productPanel.each": "each", "orderDetails.defaultCopyRightText": "You have requested to exchange this item. Please send it back as soon as possible.", "orderDetails.copyRightTextWithOriginalOrder": "This order was generated from your requested exchange. If you haven't already, please send the original item back as soon as possible.", "orderDetails.submitted": "Submitted", "orderDetails.returnProcessed": "Return processed on", "orderDetails.globalMessageError": "A technical error has unfortunately occurred. Please refresh to try again.", "utils.dateTimeFormatter.byDayOfWeekMonthDateTime": "{{dayOfWeekMonthDate}} by {{time}}", "utils.dateTimeFormatter.atDayOfWeekMonthDateTime": "{{dayOfWeekMonthDate}} at {{time}}", "utils.dateTimeFormatter.monthDaySuffixOrSooner": "{{monthDaySuffix}} or sooner", "utils.dateTimeFormatter.today": "Today", "support.supportNumber.athleta": "**************", "support.supportNumber.at": "**************", "support.supportNumber.oldnavy": "1-800-OLD-NAVY (**************)", "support.supportNumber.on": "**************", "support.supportNumber.bananarepublic": "1-888-<PERSON> (**************)", "support.supportNumber.br": "**************", "support.supportNumber.bananarepublicfactory": "**************", "support.supportNumber.brfs": "**************", "support.supportNumber.gap": "1-800-GAPSTY<PERSON> (**************)", "support.supportNumber.gp": "**************", "support.supportNumber.gapfactory": "**************", "support.supportNumber.gpfs": "**************", "support.supportNumber.hillcity": "1-833-HILL-CIT<PERSON> (1-833-445-5248)", "support.supportNumber.hc": "1-833-445-5248", "support.homeLink.gp.url": "https://www.gap.com", "support.homeLink.gp.label": "gap.com homepage", "support.homeLink.at.url": "https://athleta.gap.com", "support.homeLink.at.label": "athleta.gap.com homepage", "support.homeLink.on.url": "https://oldnavy.gap.com", "support.homeLink.on.label": "oldnavy.gap.com homepage", "support.homeLink.br.url": "https://bananarepublic.gap.com", "support.homeLink.br.label": "bananarepublic.gap.com homepage", "support.homeLink.hc.url": "https://hillcity.gap.com", "support.homeLink.hc.label": "hillcity.gap.com homepage", "support.relayService": "Please dial 711 for relay service", "support.customerServicePage.gap": "/customerService/info.do?cid=2136", "support.customerServicePage.gapfactory": "/customerService/info.do?cid=1037175", "support.customerServicePage.bananarepublic": "/customerService/info.do?cid=6740", "support.customerServicePage.bananarepublicfactory": "/customerService/info.do?cid=1037835", "support.customerServicePage.athleta": "/customerService/info.do?cid=44959", "support.customerServicePage.oldnavy": "/customerService/info.do?cid=3171", "support.customerServicePage.hillcity": "/customerService/info.do?cid=1113884", "support.chat.chatHeaderLabel": "Real Time Chat", "support.chat.timing": "Open Mon-Sun, 8a-10p ET", "support.chat.chatNow": "Chat Now", "support.chat.chatIsClosed": "Chat is Closed", "support.giveUsFeedbackLabel": "Give Us Feedback", "support.customerServiceLabel": "Customer Service ", "support.customerServiceHours": "7am-1am ET", "bopis.accordion.tabOne.label": "STORE DETAILS", "bopis.accordion.tabOne.storeHours": "Store Hours", "bopis.accordion.tabTwo.label": "PICKUP INSTRUCTIONS", "bopis.accordion.tabTwo.pickUpNameLabel": "Pickup person: ", "bopis.accordion.tabTwo.pickUpName": "<PERSON><PERSON>", "bopis.accordion.tabTwo.instructionLabel": "What should I bring?", "bopis.accordion.tabTwo.stepOne": "1. Your confirmation email", "bopis.accordion.tabTwo.stepTwo": "2. Government issued ID", "bopis.accordion.tabTwo.goTo": "Go to ", "bopis.accordion.tabTwo.storeName": "Old Navy", "bopis.accordion.tabTwo.storeAddress": "55 Colma Blvd, Colma, CA 94102", "bopis.accordion.tabTwo.openUntil": "Open today until ", "bopis.accordion.tabTwo.closingHour": "9:00 PM", "bopis.accordion.tabTwo.idCard": "ID Card", "bopis.accordion.tabTwo.shoppingBag": "Shopping Bag", "bopis.accordion.tabTwo.check": "Check", "bopis.accordion.tabTwo.currentHours": "current hours", "bopis.accordion.tabThree.label": "Get your order!", "bopis.accordion.tabThree.message": "If you can't pick up your order within 5 days, we'll cancel it and immediately release the hold on the funds in your account. ", "returns.header": "Returns", "returns.learnMore": "Learn more about returns and exchanges", "returns.inStore.label": "IN STORE", "returns.inStore.labelInfo": "Return in store for our fastest possible refund.", "returns.inStore.labelTwo": "What Should I Bring to the Store?", "returns.inStore.labelTwoInfo": "Bring the items you’d like to return along with either:", "returns.inStore.labelTwoBold": "either:", "returns.inStore.bulletOne": "The credit card used for this purchase, or", "returns.inStore.bulletTwo": "Your Shipping Confirmation email", "returns.inStore.labelThree": "Find a Store Near You", "returns.inStore.labelThreeInfo": "In-store returns are accepted by each of our brands exclusively at their stores", "returns.inStore.atStoreLocatorLabel": "Athleta store locator", "returns.inStore.onStoreLocatorLabel": "Old Navy store locator", "returns.inStore.brStoreLocatorLabel": "Banana Republic store locator", "returns.inStore.gpStoreLocatorLabel": "Gap store locator", "returns.inStore.hcStoreLocatorLabel": "Hill City store locator", "returns.inStore.gpfsStoreLocatorLabel": "Gap Factory store locator", "returns.inStore.brfsStoreLocatorLabel": "Banana Republic Factory store locator", "returns.inStore.atStoreLocatorLink": "https://athleta.gap.com/customerService/storeLocator.do", "returns.inStore.onStoreLocatorLink": "https://oldnavy.gap.com/customerService/storeLocator.do", "returns.inStore.brStoreLocatorLink": "https://bananarepublic.gap.com/customerService/storeLocator.do", "returns.inStore.gpStoreLocatorLink": "https://www.gap.com/customerService/storeLocator.do", "returns.inStore.header": "Want your refund sooner?", "returns.byMail.byMail": "By Mail", "returns.byMail.createLabel": "Create a shipping label", "returns.byMail.createLabelDetails": "to complete a return by mail. All returns are free and can be easily tracked online. Returns must be processed within the return window.", "returns.byMail.header": "Prefer to ship your return?", "orderHistory.header": "Order History", "orderHistory.card.estimatedDeliveryLabel": "Est. delivery by ", "orderHistory.card.trackPackageLink": "Track Package", "orderHistory.card.orderDetailsLink": "Order Details", "orderHistory.card.sellerLabel": "Sold & Shipped by", "orderHistory.card.details": "Details", "orderHistory.purchaseHistory": "Purchase History", "orderHistory.purchasedOn": "Purchased on", "orderHistory.next": "Next", "orderHistory.prev": "Prev", "orderSelfService.addNewAddressHeader": "Enter New Shipping Address", "orderSelfService.addNewShippingAddressHeader": "ENTER NEW SHIPPING ADDRESS", "orderSelfService.newAddressNote": "We are currently processing this order to be shipped. Please enter the new address below.", "orderSelfService.editShippingAddressHeader": "EDIT SHIPPING ADDRESS", "orderSelfService.saveNewAddrFooterBtnLabel": "SAVE CHANGES", "orderSelfService.selectAddressButtonLabel": "SELECT SHIPPING ADDRESS", "orderSelfService.confirmShipingAddressButtonLabel": "CONFIRM SHIPPING ADDRESS", "orderSelfService.changeAddressPrefix": "You are ", "orderSelfService.changeAddressEmphasised": "changing the shipping address", "orderSelfService.changeAddressSuffix": " for this order. You can only change your shipping address once.", "orderSelfService.changeAddressNote1": "Once the address change is processed, you will receive an email and your order details will be updated.", "orderSelfService.changeAddressNote2": "Depending on the location of the new address, you may be subjected to changes in sales tax.", "orderSelfService.newShippingAddressHeader": "New Shipping Address", "orderSelfService.selectAddressHeader": "Select a new shipping address:", "orderSelfService.changeShippingAddressHeader": "CHANGE SHIPPING ADDRESS", "orderSelfService.changeAddressWarning": "You can change the address for this order until {{time}} today, {{date}}.", "orderSelfService.currentShippingAddressLabel": "Current shiping address:", "orderSelfService.updateAddressButtonLabel": "UPDATE SHIPPING ADDRESS", "orderSelfService.header": "CANCEL ITEMS", "orderSelfService.content": "We are currently processing this order for shipping. Please indicate the item(s) you would like to cancel.", "orderSelfService.cancelMyEntireOrder": "Cancel my entire order", "orderSelfService.useSuggestedAddrTitle": "Or Use Suggested Address", "orderSelfService.verifyAddressTitle": "Verify the Address You Entered", "orderSelfService.useAddressButtonTitle": "USE THIS ADDRESS", "orderSelfService.buttonContinue": "CANCEL ORDER", "orderSelfService.confirmButton": "confirm items to cancel", "orderSelfService.selectReasonForCancel": "select reason for cancellation", "orderSelfService.cancelItem": "Cancel Item", "orderSelfService.itemsInThisOrder": "Items in this order", "orderSelfService.reasonForCancel": "Reason for cancellation (required)", "orderSelfService.cancelWarning": "You can cancel items in this order until {{time}} today, {{date}}.", "orderSelfService.options.0": "Forgot to use discount or promo code", "orderSelfService.options.1": "Incorrect size, color, or item", "orderSelfService.options.2": "Incorrect shipping address", "orderSelfService.options.3": "Incorrect shipping method", "orderSelfService.options.4": "Incorrect payment method", "orderSelfService.options.5": "Was not done building my order", "orderSelfService.options.6": "Changed my mind", "orderSelfService.options.7": "Placed duplicate order", "orderSelfService.options.8": "Other", "orderSelfService.termsAndConditions": "You are confirming the", "orderSelfService.cancellation": "cancellation of item(s)", "orderSelfService.order": "in this order.", "orderSelfService.action": "This action cannot be undone.", "orderSelfService.process": "Once the cancellation is processed, you will receive an email and your order details will be updated.", "orderSelfService.holdYourCreditCard": "Please wait a few days for the hold on your credit card to be released. If you used a gift card, an eGift Card will be emailed.", "orderSelfService.goBack": "GO BACK", "orderSelfService.releaseSummary": "Release Summary", "orderSelfService.shippingDetails": "Shipping (3-5 business days)", "orderSelfService.reasonForCancellation": "Reason for cancellation", "orderSelfService.incorrectPayment": "Incorrect payment method", "orderSelfService.releaseSubtotal": "Release Subtotal", "orderSelfService.releaseTotal": "Release Total", "orderSelfService.tax": "Tax", "orderSelfService.item": "ITEM", "orderSelfService.cancel": "CANCEL", "orderSelfService.cancelSelectitem": "item", "orderSelfService.cancelNumItems": "CANCEL {{num}} ITEM(S)", "orderSelfService.items": "Items", "orderSelfService.SelectItems": "Select item(s) to cancel", "orderSelfService.validOption": "Select a valid option.", "orderSelfService.editButtonTitle": "EDIT", "orderSelfService.itemIndexTotal": "Item {{index}} of {{total}}", "orderSelfService.cancelQtyLabel": "Qty to cancel", "accountSettings.header": "Personal Information", "accountSettings.labels.firstName": "First Name", "accountSettings.labels.lastName": "Last Name", "accountSettings.labels.fullName": "First and Last Name", "accountSettings.labels.mobile": "Mobile number", "accountSettings.labels.dob": "Birthday (MM/DD)", "accountSettings.labels.tmmr": "Text Me About My Rewards Program", "accountSettings.labels.email": "Email Address", "accountSettings.labels.phoneNumber": "Mobile Number", "accountSettings.labels.language": "Language", "accountSettings.errors.required.firstName": "Enter your first name.", "accountSettings.errors.required.lastName": "Enter your last name.", "accountSettings.errors.required.email": "Enter your email address.", "accountSettings.errors.required.phoneNumber": "Enter a phone number.", "accountSettings.errors.required.globalMessageError": "Please try again. We're experiencing a temporary interruption in service.", "accountSettings.errors.valid.firstName": "Enter a valid first name.", "accountSettings.errors.valid.lastName": "Enter a valid last name.", "accountSettings.errors.valid.nameNoSpecialChars": "Please re-enter your name without special characters to continue.", "accountSettings.errors.valid.email": "Enter a valid email address.", "accountSettings.errors.valid.phoneNumber": "Enter a valid phone number.", "accountSettings.errors.valid.dob": "Enter a valid date of birth(MM/DD).", "accountSettings.errors.valid.dobMonth": "Enter a valid birth month(MM).", "accountSettings.errors.valid.dobDate": "Enter a valid birth day(DD).", "accountSettings.errors.notificationSocialSignIn": "You created your Gap Inc. account using Facebook and cannot create a password.", "accountSettings.formTitle": "Account Profile", "accountSettings.formHelpContent.mobileNumberText": "Add phone number for easier rewards lookup in store.", "accountSettings.formHelpContent.dobText": "Add birthday for a birthday bonus.", "accountSettings.formHelpContent.disclaimer.signUpText": "Mobile number must be provided above to sign up for text messages. ", "accountSettings.formHelpContent.disclaimer.disclaimerText": "Consent is not a condition of purchasing goods and services. You can opt-out anytime by responding STOP. You can respond HELP for help.", "accountSettings.formHelpContent.disclaimer.continuationText": "Msg & Data Rates May Apply. By entering your phone number, clicking OK, and completing the sign-up, you consent to receive recurring marketing text messages regarding any Gap Inc. Rewards Program in which you enroll (e.g. Loyalty, CreditCard) which will include receiving as many rewards as you have earned at the mobile number provided that may be sent via an automated system each week, and you also consent to the ", "accountSettings.formHelpContent.disclaimer.textTerms": "text terms", "accountSettings.formHelpContent.disclaimer.privacyPolicy": "privacy policy. ", "accountSettings.notificationSocialSignIn": "You created your Gap Inc. account using Facebook and cannot edit your email address.", "accountSettings.saveButton.save": "Save Personal Info", "accountSettings.saveButton.saved": "Saved", "accountSettings.successNotification": "Success! Your account has been updated.", "accountSettings.errorNotification": "Please refresh your page and try again. We're experiencing a temporary interruption in service.", "accountSettings.emailExistsNotification": "This email address is associated with another account. Please try entering it again.", "accountSettings.phNoExistsNotification": "This phone number is associated with another account. Please try a different number.", "accountSettings.mergedAccountNotification": "Your accounts have been merged. Please sign in with your active account to access your Rewards benefits.", "accountSettings.changePassword": "Change password", "accountSettings.language.english": "English", "accountSettings.language.french": "French", "accountSettings.OTP.identityVerification.Main": "Identity Verification", "accountSettings.OTP.identityVerification.header": "Please select a method to receive a verfication code.", "accountSettings.OTP.identityVerification.secondaryHeader": "For account security, enter the verification code here (code expires after 15 minutes):", "accountSettings.OTP.identityVerification.submitButtonEmail": "SEND CODE BY EMAIL", "accountSettings.OTP.identityVerification.submitButtonPhone": "SEND CODE BY TEXT", "accountSettings.OTP.identityVerification.disclaimer": "By choosing to send code by text you are agreeing to receive text messages via SMS. Messages and data rates may apply.", "accountSettings.OTP.identityVerification.otpPhoneHeader": "We've sent a verification code to the following  mobile phone number:", "accountSettings.OTP.identityVerification.otpEmailHeader": "We've sent a verification code to the following email address:", "accountSettings.OTP.identityVerification.otpSecondaryHeader": "For account security, enter the verification code here (code expires after 15 minutes):", "accountSettings.OTP.identityVerification.verifyMyIdentity": "VERIFY MY IDENTITY", "accountSettings.OTP.identityVerification.sessionTimeout": "Unfortunately your personal information could not be saved. Please re-verify your identity in order to continue.", "accountSettings.OTP.identityVerification.resendVerificationCode": "Resend Verification Code", "accountSettings.OTP.identityVerification.resendVerificationCodeByEmail": "Resend Verification Code by Email", "accountSettings.OTP.identityVerification.resendVerificationCodeByText": "Resend Verification Code by Text", "accountSettings.OTP.identityVerification.sendVerificationCodeByText": "Send Verification Code by Text", "accountSettings.OTP.identityVerification.sendVerificationCodeByEmail": "Send Verification Code by Email", "accountSettings.OTP.identityVerification.resendSuccessMessageEmail": "The Verification code has been resent. Please wait a few minutes before requesting another code.", "accountSettings.OTP.identityVerification.otpCodeExpired": "That verification code has expired. Please select “Resend Verification Code” and try again.", "accountSettings.OTP.identityVerification.otpServiceError": "There was an error on our end. Please try again.", "accountSettings.OTP.identityVerification.otpBotError": "There was an error verifying your account. Please try again.", "accountSettings.OTP.footerText": "Some edits may require identity verification.", "accountSettings.OTP.save": "Save", "accountSettings.OTP.cancel": "Cancel", "accountSettings.OTP.edit": "Edit", "accountSettings.OTP.add": "Add", "accountSettings.OTP.addPhoneNumberText": "Add mobile number", "accountSettings.OTP.addDateOfBirthText": "Add birthday", "accountSettings.OTP.addPhoneNumberDescription": "Add a phone number for easier rewards lookup in store", "accountSettings.OTP.addDateOfBirthDescription": "Add a birthday for redeeming points in store", "accountSettings.OTP.validateOTPSuccessMessage": "Your identity has been verified", "accountSettings.OTP.blankOtp": "Please enter a valid 6-digit verification code to continue.", "accountSettings.OTP.invalidOtp": "That verification code is incorrect. Please double-check the entry and try again.", "accountSettings.OTP.otpMaxAttemptsReached": "The verification code has been locked due to too many attempts. Please select “Resend Verification Code”.", "accountSettings.OTP.isInvalidSessionError": "Unfortunately your personal information could not be saved. Please select edit to re-verify your identity and try again.", "accountSettings.OTP.verificationPlaceholderText": "Enter Verification Code (e.g. 123456)", "changePassword.header": "Change Password", "changePassword.subHeader": "After changing your password, you may need to re-enter your billing details next time you place an order.", "changePassword.showPasswordAriaLabel": "Show password as plain text. Note: this will visually expose your password on the screen.", "changePassword.hidePasswordAriaLabel": "Hide password. Note: This will no longer visually expose your password on the screen.", "changePassword.show": "SHOW", "changePassword.hide": "HIDE", "changePassword.buttonText": "Save Password", "changePassword.buttonTextSaved": "Saved", "changePassword.include": "Your password must include:", "changePassword.currentPassword": "Current Password", "changePassword.newPassword": "New Password", "changePassword.confirmPassword": "Confirm Password", "changePassword.invalidPassword": "The current password you entered is incorrect.", "changePassword.blankPassword": "Password cannot be blank.", "changePassword.globalMessageError": "Please try again. We're experiencing a temporary interruption in service.", "changePassword.errorNotification": "Please refresh your page and try again. We're experiencing a temporary interruption in service.", "changePassword.errorPasswordAlreadyUsed": "This password has already been used. Please try again with a new password.", "resetPassword.header": "Reset Your Password", "resetPassword.subHeader": "Reset password for: ", "resetPassword.successHeader": "Your password has been updated.", "resetPassword.signInText": "sign in", "resetPassword.continueShopping": "Continue Shopping", "resetPassword.expiredLinkHeader": "This link has expired.", "resetPassword.usedLinkHeader": "This link has already been used.", "resetPassword.invalidLinkHeader": "This link is invalid.", "resetPassword.saveButton": "Save Password", "resetPassword.requestLinkButton": "Request A New Link", "resetPassword.errorOnLoadMessage": "Please request a new email link we are experiencing temporary interruption in service.", "resetPassword.errorOnSaveMessage": "We were unable to update the password for {{emailAddress}} at this time. You can try again or call {{supportNumber}} for further assistance.", "forgotPasswordOTP.enterVerificationCode": "Enter Verification Code", "forgotPasswordOTP.OTPPhoneHeader": "We’ve sent a verification code to the following mobile phone number:", "forgotPasswordOTP.OTPVerificationCodeDescription": "For account security, enter the verification code here (code expires after 15 minutes)", "forgotPasswordOTP.verificationCodeButton": "Verification Code", "forgotPasswordOTP.submitCodeButton": "Submit Code", "forgotPasswordOTP.resendVerificationCode": "The verification code has been resent. Please wait a few minutes before requesting another code.", "forgotPasswordOTP.resendVerificationLink": "The email has been sent.", "forgotPasswordOTP.identityVerified": "Your identity has been verified.", "forgotPasswordOTP.resendCode": "Resend Code", "forgotPasswordOTP.returnToSignIn": "Return to Sign In", "forgotPasswordOTP.resendLink": "Resend Link", "forgotPasswordOTP.getEmailLink": "Didn’t get an email?", "forgotPasswordOTP.checkYourInbox": "Check your inbox", "forgotPasswordOTP.if": "If", "forgotPasswordOTP.emailSentDescription": "is associated with an account, we’ll email you instructions to reset your password.", "forgotPasswordOTP.blankOtpErrorMessage": "Please enter a valid 6-digit verification code to continue", "forgotPasswordOTP.resetPasswordSuccess": "Success! Your password has been reset.", "forgotPasswordOTP.resetPasswordError": "Unfortunately your password could not be reset. Please re-verify your identity in order to continue.", "forgotPasswordOTP.incorrectOtpErrorMessage": "That verification code is incorrect. Please double-check the entry and try again.", "forgotPasswordOTP.expiredOtpErrorMessage": "That verification code has expired. Please select “Resend Code” and try again.", "forgotPasswordOTP.technicalErrorMessage": "Something went wrong. Please try again", "noOrderHistory.header": "You don't have any orders from the past 13 months.", "noOrderHistory.summary2": "If you have any questions, please contact customer service at  ", "noOrderHistory.or": " or custserv@", "noOrderHistory.endEmail": "canada.ca", "noOrderHistory.summary3": "If you have any questions, please contact customer service at 1-800-<NAME_EMAIL>", "noOrderHistory.noOrderHistoryImg": "There are no orders from the past 13 months", "noOrderHistory.noRecentOrders": "No Recent Purchases", "orderHistoryFooter.summary": "Your order history shows all of your orders from the past 13 months. Orders placed when you weren't signed in aren't included.", "orderHistoryFooter.lookUpCTA": "Guest Order Lookup", "orderHistoryFooter.lookUpCTAPrefix": "Visit", "orderHistoryFooter.lookUpContent": "Need to look up a Guest order?", "orderHistoryFooter.summary2": "Your purchase history shows all of your online purchases from the past 13 months. Purchases placed when you weren't signed in aren't included.", "orderHistoryFooter.lookupLinkText": "Visit Guest Order Lookup", "guestOrderLookup.header": "Guest Order Lookup", "guestOrderLookup.summaryPrompt": "Not seeing an order in your order history?", "guestOrderLookup.summaryMessage": "Look up a single order by entering the order number and email address used to place the order.", "loyaltyHub.error.header": "Oh no! We hit a snag.", "loyaltyHub.error.summary1": "This page is temporarily unavailable. Thank you for your patience as we patch things up.", "loyaltyHub.error.summary2": "If the problem persists, please call ", "loyaltyHub.error.button": "Try Again", "systemError.header": "We're sorry.", "systemError.summary1": "An error occurred that prevented this page from being displayed. To continue shopping, return to the ", "systemError.summary2": "If this problem persists, please give us a call at ", "technicalDifficulties.header": "Oh no! We hit a snag.", "technicalDifficulties.summary1": "This page is temporarily unavailable. Thank you for your patience as we patch things up.", "technicalDifficulties.summary2": "If the problem persists, please call ", "technicalDifficulties.button": "Try Again", "addressNotSaved.header": "Shipping Addresses", "addressNotSaved.summary1": "You currently don't have any addresses saved", "addressNotSaved.summary2_link": "Add an address", "addressNotSaved.summary2": "for a faster checkout experience!", "addressNotSaved.altText": "House with trees", "address.header": "Shipping Addresses", "address.addAddress": "Add New Address", "address.edit": "Edit", "address.delete": "Delete", "address.default": "<PERSON><PERSON><PERSON>", "address.setAsDefault": "Set as <PERSON><PERSON><PERSON>", "address.deleteModal.title": "Delete Address", "address.deleteModal.confirmationMessage": "Are you sure you want to delete this shipping address?", "address.deleteModal.yesButton": "Yes, delete", "address.deleteModal.noButton": "No, keep address", "address.verifyModal.or": "Or verify the address you entered:", "address.verifyModal.addressNotRecognized": "The U.S. Postal Service doesn't recognize the address you entered. ", "address.verifyModal.reviewAddress": "Review the suggested address:", "address.verifyModal.reviewAddresses": "Review the suggested addresses:", "address.verifyModal.yesButton": "Keep as Entered", "address.verifyModal.editButton": "Edit Address", "address.verifyModal.accept": "Accept Suggested Address", "address.verifyModal.verifyAddressModalError": "We're experiencing technical issues.", "address.verifyModal.verifyAddressModalTryAgain": "Please close and try again or select one of the alternative addresses below.", "address.addModal.setAsDefault": "Set as default shipping address", "address.addModal.addNewAddress": "Add New Address", "address.addModal.saveAddress": "Save Address", "address.addModal.fullName": "Full Name", "address.addModal.addressLine1": "Street Address", "address.addModal.addressLine2": "APT #", "address.addModal.townCity": "Town/City", "address.addModal.phoneNumber": "Phone Number", "address.addModal.US.state": "State", "address.addModal.US.zipcode": "Zip Code", "address.addModal.CA.state": "Province", "address.addModal.CA.zipcode": "Postal Code", "address.editModal.editAddress": "Edit Address", "address.editModal.updateAddress": "Update Address", "address.errors.required.fullName": "Enter a first name and last name", "address.errors.required.lastName": "Enter a last name", "address.errors.required.addressLine1": "Enter a street address", "address.errors.required.townCity": "Enter a town/city", "address.errors.required.phoneNumber": "Enter a phone number", "address.errors.required.ca.state": "Select a state", "address.errors.required.ca.zipcode": "Enter a Postal code", "address.errors.required.CA.state": "Select a province", "address.errors.required.CA.zipcode": "Enter a Postal Code", "address.errors.valid.fullName": "Enter a valid first name and last name", "address.errors.valid.addressLine1": "Enter a street number & street name", "address.errors.valid.townCity": "Enter a valid town/city", "address.errors.valid.phoneNumber": "Enter a valid phone number", "address.errors.valid.US.state": "Select a valid state", "address.errors.valid.US.zipcode": "Enter a valid ZIP code", "address.errors.valid.us.zipcode": "Enter a valid postal code", "address.errors.valid.CA.state": "Select a valid province", "address.errors.valid.CA.zipcode": "Enter a valid Postal Code", "address.errors.valid.ca.zipcode": "Enter a valid postal code", "address.errors.globalMessageError": "Please try again. We're experiencing a temporary interruption in service.", "wallet.edit": "Edit", "wallet.delete": "Delete", "wallet.endingIn": "ending in", "wallet.addNewCard": "ADD NEW CARD", "wallet.savedCards": "Saved Cards", "wallet.default": "<PERSON><PERSON><PERSON>", "wallet.expires": "Expires", "wallet.noCardsAltText": "Blank credit card icon", "wallet.noCardsCopy": "You currently don't have any payment methods saved", "wallet.addCardCopy": "Add a credit or debit card", "wallet.fasterCheckoutCopy": "for a faster checkout experience!", "wallet.deleteModal.title": "Delete Payment Method", "wallet.deleteModal.confirmationMessage": "Are you sure you want to delete this card?", "wallet.deleteModal.yesButton": "Yes, delete", "wallet.deleteModal.noButton": "No, keep Card", "wallet.addModal.addPaymentMethod": "Add Payment Method", "wallet.addModal.creditCardDetails": "Credit Card Details", "wallet.addModal.billingAddress": "Billing Address", "wallet.addModal.saveCard": "Save Card", "wallet.addModal.cardNumber": "Card Number", "wallet.addModal.expirationDate": "Expiration Date (MM/YY)", "wallet.addModal.setAsDefault": "Set as default payment", "wallet.editModal.editPaymentMethod": "Edit Payment Method", "wallet.editModal.updateCard": "Update Card", "wallet.errors.required.cardNumber": "Enter a card number", "wallet.errors.required.expirationDate": "Enter a expiration date", "wallet.errors.valid.cardNumber": "Enter a valid card number", "wallet.errors.valid.field": "Field has an invalid value", "wallet.errors.valid.expirationDate": "Enter a valid expiration date", "wallet.errors.valid.cvv": "Enter a valid CVV number", "wallet.errors.globalMessageError": "Please try again. We're experiencing a temporary interruption in service.", "wallet.errors.barclays.signIn": "There was an error. Try again.", "wallet.errors.barclays.guestSignIn": "There was an error. Refresh the page and try again.", "wallet.errors.technicalError": "We're experiencing technical difficulties. Please try again later.", "wallet.barclays.globalInfoMessagePhase1a": "Your Gap Inc. Rewards Credit Card(s) ending -- ", "wallet.barclays.globalInfoMessagePhase1b": " -- will be deactivated June 20, 2022 and replaced with new cards. No action is needed.", "wallet.barclays.globalInfoMessagePhase1Halfa": "Your new Gap Inc. Rewards Credit Card(s) ending -- ", "wallet.barclays.globalInfoMessagePhase1Halfb": " -- has been added to your account.", "wallet.barclays.globalInfoMessagePhase2a": "Start using your new Gap Inc. Rewards Credit Card(s) ending -- ", "wallet.barclays.globalInfoMessagePhase2b": ". Those issued before May 2022 have been deactivated.", "wallet.barclays.deactivationMessage": "This card deactivates on June 20, 2022", "wallet.barclays.deactivatedMessage": "This card was deactivated on June 20, 2022", "wallet.barclays.newCardEndingIn": "New card ending in ****", "wallet.barclays.newCardMessage": "Your new card will be available to use on June 20, 2022", "wallet.barclays.deactivated": "Deactivated", "wallet.gapCreditCardLinkFooter.linkText": "Manage my Gap Inc. Rewards Credit Cards", "wallet.gapCreditCardLinkFooter.link": "/my-account/gap-cards", "home.myAccount": "My Account", "home.privacyPolicyMessage": "Your information will be collected and handled in accordance with our", "home.privacyPolicyLink": "Privacy Policy", "home.myAccountNav.accountDetails": "Account Details", "home.myAccountNav.settings": "Settings", "home.myAccountNav.changePassword": "Change Password", "home.myAccountNav.shippingAddresses": "Shipping Addresses", "home.myAccountNav.myPointsRewardsNew": "Points & Rewards", "home.myAccountNav.loyaltyHub": "<PERSON><PERSON><PERSON>", "home.myAccountNav.rewardsMarketplace": "Rewards Marketplace", "home.myAccountNav.memberPerks": "Member <PERSON><PERSON>", "home.myAccountNav.myMembership": "My Membership", "home.myAccountNav.purchaseHistory": "Purchase History", "home.myAccountNav.communicationPreferences": "Communication Preferences", "home.myAccountNav.ordersReturns": "Orders & Returns", "home.myAccountNav.orderHistory": "Order History", "home.myAccountNav.returnsFaq": "Returns FAQ", "home.myAccountNav.storeFinder": "Store Finder", "home.myAccountNav.wallet": "Wallet", "home.myAccountNav.savedCards": "Saved Cards", "home.myAccountNav.savedPaymentMethods": "Saved Payment Methods", "home.myAccountNav.myPointsRewards": "My Points & Rewards", "home.myAccountNav.checkGiftCardBalances": "Check Gift Card Balances", "home.myAccountNav.payMyCard": "Pay my Card", "home.myAccountNav.payMyCardgap": "Pay My Gap Inc. Card", "home.myAccountNav.payMyCardoldnavy": "Pay My Old Navy Card", "home.myAccountNav.payMyCardbananarepublic": "Pay My Banana Republic Card", "home.myAccountNav.payMyCardathleta": "Pay My Athleta Card", "home.myAccountNav.recentOrders": "Recent Orders", "home.myAccountNav.gapIncCards": "Gap Inc. Cards", "home.myAccountNav.manageAccount": "Manage Account", "home.myAccountNav.giftCards": "Gift Cards", "home.myAccountNav.giftCardCenter": "Gift Card Center", "home.myAccountNav.customerService": "Customer Service", "home.myAccountNav.payGapIncRewardCard": "My Gap Inc. Rewards Credit Cards", "home.myAccountNav.payGapIncCard": "Pay My Gap Inc. Card", "home.myAccountNav.accountSecurity": "Account Security", "home.bdss.description": "Looks like you have two Rewards accounts. Merge them to access all your benefits and points in one place.", "home.bdss.title": "You might be missing out", "home.bdss.continueToMerge": "CONTINUE TO MERGE", "home.bdss.thisAccount": "This account", "home.bdss.anotherAccount": "is associated with another account", "home.bdss.drawerHeader": "<PERSON><PERSON> Accounts", "home.bdss.loadingMessage": "Loading", "home.bdss.loadingSubMessage": "Please do not refresh.", "home.bdss.mergeForm.header": "Merge accounts to access your points balance and benefits through this email account:", "home.bdss.mergeForm.secondaryHeader": "For account security, enter this complete email:", "home.bdss.mergeForm.disclaimer": "Member level may be updated according to your total qualified activity. Once accounts are merged, it can’t be undone.", "home.bdss.mergeForm.inputLabel": "Email Address", "home.bdss.mergeForm.submitButton": "MERGE ACCOUNTS", "home.bdss.mergeForm.incorrectEmailThreeTimes": "You have entered the incorrect email address too many times. Please contact customer service at 1.800.GAP.STYLE", "home.bdss.mergeForm.incorrectEmailTwoTimes": "After two failed attempts, you have one more chance at entering the correct email online. If unsuccessful, you'll have to call customer service to continue.", "home.bdss.mergeForm.incorrectEmailOneTime": "After one failed attempt, you have two more chances at entering the correct email online. If unsuccessful, you'll have to call customer service to continue.", "home.bdss.mergeForm.mergeUnsuccessful": "We’ve unfortunately run into issues and your accounts haven’t been merged. Please try again later or contact customer service at ************.", "home.bdss.mergeReview.headerStateOne": "Your Accounts Are Merged", "home.bdss.mergeReview.secondaryHeaderStateOne": "You’ll receive an email once the merge is complete. Earned points and all benefits will then be associated with one account, ", "home.bdss.mergeReview.reviewMessageStateOne": "Review your phone number and birthday", "home.bdss.mergeReview.reviewMessageStateTwo": "Add your birthday", "home.bdss.mergeReview.reviewMessageStateThree": "Add your phone number", "home.bdss.mergeReview.reviewMessageStateFour": "Add your phone number and birthday", "home.bdss.mergeReview.mmdd": " (MM/DD)", "home.bdss.mergeReview.inYour": " in your ", "home.bdss.mergeReview.toYour": " to your ", "home.bdss.mergeReview.profile": "profile", "home.bdss.mergeReview.reviewMessagePartTwo": " to earn points in stores and to make in-store purchases with your rewards.", "home.bdss.mergeReview.emailLabel": "Email", "home.bdss.mergeReview.phoneNumberLabel": "Phone Number", "home.bdss.mergeReview.reviewButton": "REVIEW PROFILE", "home.bdss.mergeReview.goToButton": "GO TO PROFILE", "home.bdss.mergeReview.notNow": "Not Now", "home.bdss.mergeReview.disclaimer": "Member level may be updated according to total qualified activity.", "unsubscribe.success.header": "Unsubscribed!", "unsubscribe.success.email": "The email address:", "unsubscribe.success.hasBeen": "has been unsubscribed from", "unsubscribe.success.marketingEmails": "marketing emails.", "unsubscribe.success.preferences": "Edit your communication preferences", "unsubscribe.success.toStart": "to start receiving emails from us again.", "unsubscribe.success.startShopping": "start shopping", "banner.rewards": "Rewards", "banner.rewardsBalances": "Re<PERSON>s Balance", "banner.rewardsCardmember": "Enthusiast <PERSON><PERSON><PERSON> Cardmember", "banner.rewardsAvailable": "in rewards available", "banner.cashAvailable": "super cash available", "banner.availableToUse": "Available for use until", "banner.hello": "Hello", "banner.helloLoud": "Hello!", "banner.recentOrder": "recent order", "banner.recentOrders": "recent orders", "banner.shipmentReady": "A shipment is almost ready to go!", "banner.daysLeft": "days left", "banner.expiresToday": "Expires today", "banner.expiresTodayToRedeem": "Expires today to Redeem", "banner.daysLeftToRedeem": "Days Left to Redeem", "banner.daysLeftToRedeemLower": "days left to redeem", "banner.daysLeftToRedeemYour": "days left to redeem your", "banner.youHave": "You have", "banner.inRewardsWith": "in rewards with", "banner.dollarsOfYour": "dollars of your", "banner.dollarsInRewards": "dollars in rewards", "banner.in": "in", "banner.with": "with", "loader.loading": "loading...", "barclaysServicing.peekInfoLoadingText": "Loading...please don't refresh while we connect.", "barclaysServicing.labels.headerBarclaysOrSynchrony": "Gap Inc. Rewards Visa Cards", "barclaysServicing.labels.dontSeeCreaditCards": "Don’t see your credit cards below?", "barclaysServicing.labels.Select": "Select manage and pay to log in and access your card information.", "barclaysServicing.labels.cardIssuedBefore": "All Gap Inc. Rewards Credit Cards applied", "barclaysServicing.labels.Before": " before", "barclaysServicing.labels.cardIssuedAfter": "All Gap Inc. Rewards Credit Cards applied", "barclaysServicing.labels.After": " on or after", "barclaysServicing.labels.date": " May, 2022", "barclaysServicing.labels.header": "Manage My {{brands}} Credit Cards", "barclaysServicing.labels.faq": "FAQ", "barclaysServicing.labels.enableAccountPreview": "Enable account preview", "barclaysServicing.labels.manageAndPay": "Manage and Pay", "barclaysServicing.labels.enablePreview": "Enable Quickview", "barclaysServicing.labels.peekWarning": "Quickview unavailable at this time. Refresh or try again later.", "barclaysServicing.labels.myAccount": "My Account", "barclaysServicing.labels.savedCards": "Saved Cards", "barclaysServicing.labels.apply": "Apply", "barclaysServicing.labels.rewardsCards": "Rewards Cards", "barclaysServicing.labels.servicingHeader": "My Rewards Credit Cards", "barclaysServicing.cardDetails.availableCredit": "Available Credit", "barclaysServicing.cardDetails.currentBal": "Current Balance", "barclaysServicing.cardDetails.lastPayment": "Last Payment Received", "barclaysServicing.cardDetails.statementBal": "Statement Balance", "barclaysServicing.cardDetails.dueDate": "Payment Due", "barclaysServicing.cardDetails.manageCard": "Manage and Pay", "barclaysServicing.cardInfo.isSynchrony": "Icon Visa", "barclaysServicing.cardInfo.isBarclays": "Rewards Mastercard", "barclaysServicing.cardInfo.availableDate": "This card will be available to use on ", "barclaysServicing.payCard.at": "https://athleta.syf.com/login/", "barclaysServicing.payCard.br": "https://bananarepublic.syf.com/login/", "barclaysServicing.payCard.gp": "https://gap.syf.com/login/", "barclaysServicing.payCard.gap": "https://gap.syf.com/login/", "barclaysServicing.payCard.on": "https://oldnavy.syf.com/login/", "barclaysServicing.payCard.gapfs": "https://gap.syf.com/login/", "barclaysServicing.payCard.brfs": "https://bananarepublic.syf.com/login/", "barclaysServicing.manageAndPayBarclays.prod.gap": "https://gap.barclaysus.com/", "barclaysServicing.manageAndPayBarclays.prod.gp": "https://gap.barclaysus.com/", "barclaysServicing.manageAndPayBarclays.prod.on": "https://oldnavy.barclaysus.com/", "barclaysServicing.manageAndPayBarclays.prod.br": "https://bananarepublic.barclaysus.com/", "barclaysServicing.manageAndPayBarclays.prod.at": "https://athleta.barclaysus.com/", "barclaysServicing.manageAndPayBarclays.prod.gapfs": "https://gap.barclaysus.com/", "barclaysServicing.manageAndPayBarclays.prod.brfs": "https://bananarepublic.barclaysus.com/", "barclaysServicing.manageAndPayBarclays.stage.gap": "https://qa01-gap.barclaysus.com/servicing/home", "barclaysServicing.manageAndPayBarclays.stage.gp": "https://qa01-gap.barclaysus.com/servicing/home", "barclaysServicing.manageAndPayBarclays.stage.on": "https://qa01-oldnavy.barclaysus.com/servicing/home", "barclaysServicing.manageAndPayBarclays.stage.br": "https://qa01-bananarepublic.barclaysus.com/servicing/home", "barclaysServicing.manageAndPayBarclays.stage.at": "https://qa01-athleta.barclaysus.com/servicing/home", "barclaysServicing.manageAndPayBarclays.stage.gapfs": "https://qa01-gap.barclaysus.com/servicing/home", "barclaysServicing.manageAndPayBarclays.stage.brfs": "https://qa01-bananarepublic.barclaysus.com/servicing/home", "barclaysServicing.unKnownCard.synchrony": "Gap Inc. Rewards Visa Cards", "barclaysServicing.unKnownCard.barclays": "Gap Inc. Rewards Mastercards®", "barclaysServicing.barclaysPLCC.gp": "Gap Good Rewards Credit Card", "barclaysServicing.barclaysPLCC.on": "Navyist Rewards Credit Card", "barclaysServicing.barclaysPLCC.br": "Banana Republic Rewards Credit Card", "barclaysServicing.barclaysPLCC.at": "Athleta Rewards Credit Card", "barclaysServicing.barclaysCBCC.gp": "Gap Good Rewards Mastercard®", "barclaysServicing.barclaysCBCC.on": "Navyist Rewards Mastercard®", "barclaysServicing.barclaysCBCC.br": "Banana Republic Rewards Mastercard®", "barclaysServicing.barclaysCBCC.at": "Athleta Rewards Mastercard®", "barclaysServicing.barclaysCBCC.gpw": "Gap Good Rewards World Mastercard®", "barclaysServicing.barclaysCBCC.onw": "Navyist Re<PERSON>s World Mastercard®", "barclaysServicing.barclaysCBCC.brw": "Banana Republic Rewards World Mastercard®", "barclaysServicing.barclaysCBCC.atw": "Athleta Rewards World Mastercard®", "barclaysServicing.synchronyCBCC.gp": "Gap Good Rewards Visa Credit Card", "barclaysServicing.synchronyCBCC.on": "Navyist Rewards Visa Credit Card", "barclaysServicing.synchronyCBCC.br": "Banana Republic Rewards Visa Credit Card", "barclaysServicing.synchronyCBCC.at": "Athleta Rewards Visa Credit Card", "barclaysServicing.synchronyCBCC.gf": "Gap Inc. Visa Signature Credit Card", "barclaysServicing.synchronyPLCC.gp": "Gap Good Rewards Credit Card", "barclaysServicing.synchronyPLCC.on": "Navyist Rewards Credit Card", "barclaysServicing.synchronyPLCC.br": "Banana Republic Rewards Credit Card", "barclaysServicing.synchronyPLCC.at": "Athleta Rewards Credit Card", "barclaysServicing.synchronyPLCC.gf": "Gap Inc. Signature Credit Card", "barclaysServicing.applyAndEarnText.applyText": "Apply for a", "barclaysServicing.applyAndEarnText.earnText": "and earn 5 points per $1 spent.", "barclaysServicing.pendingApproval": "*Pending approval", "privacypolicy.url": "https://www.gapinc.com/en-us/consumer-privacy-policy?mlink=5058,16926950,CS_Footer_PrivacyPolicy&clink=16926950", "privacypolicy.labels.headerPrivacypolicy": "Have questions about your Gap Inc. Rewards Credit Cards? ", "privacypolicy.labels.askedQuestions": "View Frequently Asked Questions", "privacypolicy.labels.information": "Your information will be collected and handled in accordance with our ", "privacypolicy.labels.policy": "Privacy Policy", "privacypolicy.labels.dontSeeCreaditCards": "Don’t have a Gap Inc. Rewards Credit Card?", "privacypolicy.labels.applyForCard": "Apply for a Card", "privacypolicy.barclays.startButton": "Start Card Application", "privacypolicy.barclays.skip": "Or, skip this step. If skipped, your card won't be saved automatically*", "privacypolicy.barclays.disclosure": "Customers not signed into a Rewards Account prior to applying for a card will not have their card credentials saved to their account.", "privacypolicy.askedQuestionLinks.gap.url": "https://www.gap.com/browse/info.do?cid=1099006&mlink=1099008,********,ggr_LP_faq&clink=********", "privacypolicy.askedQuestionLinks.gap.label": "gap.com faq", "privacypolicy.askedQuestionLinks.gp.url": "https://www.gap.com/browse/info.do?cid=1099006&mlink=1099008,********,ggr_LP_faq&clink=********", "privacypolicy.askedQuestionLinks.gp.label": "gap.com faq", "privacypolicy.askedQuestionLinks.at.url": "https://athleta.gap.com/browse/info.do?cid=1178284&mlink=1098761,********,ILP_0629_FAQs&clink=******** ", "privacypolicy.askedQuestionLinks.at.label": "athleta.gap.com faq", "privacypolicy.askedQuestionLinks.on.url": "https://oldnavy.gap.com/browse/info.do?cid=1178224", "privacypolicy.askedQuestionLinks.on.label": "oldnavy.gap.com faq", "privacypolicy.askedQuestionLinks.br.url": "https://bananarepublic.gap.com/browse/info.do?cid=1178564&mlink=1098875,********,rewards_faqcta&clink=******** ", "privacypolicy.askedQuestionLinks.br.label": "bananarepublic.gap.com faq", "privacypolicy.askedQuestionLinks.hc.url": "#", "privacypolicy.askedQuestionLinks.hc.label": "hillcity.gap.com faq", "privacypolicy.askedQuestionLinks.gapfs.url": "https://www.gapfactory.com/customerService/info.do?cid=1099136&mlink=1099133,28445517,ggr_LP_faq&clink=28445517", "privacypolicy.askedQuestionLinks.gapfs.label": "gapfactory.com faq", "privacypolicy.askedQuestionLinks.brfs.url": "https://bananarepublicfactory.gapfactory.com/browse/info.do?cid=1098818", "privacypolicy.askedQuestionLinks.brfs.label": "bananarepublicfactory.gapfactory.com", "giftCardsBalance.title": "GIFT CARDS", "giftCardsBalance.header": "Check Gift Card Balance", "giftCardsBalance.checkBalance": "Check Balance", "giftCardsBalance.cardPlaceHolder": "16-digit card number", "giftCardsBalance.pinPlaceHolder": "4-digit PIN number", "giftCardsBalance.checkBalanceAmountHeader": "Your Gift Card (#{{cardsLastFourDigits}}) Balance:", "giftCardsBalance.checkBalanceAmountReloadButton": "Reload your Card", "giftCardsBalance.errors.cardNumber": "Enter a valid gift card number", "giftCardsBalance.errors.cardNumberError": "Card number must be 16 characters", "giftCardsBalance.errors.invalidCardNumber": "Enter a valid card number.", "giftCardsBalance.errors.cardPinError": "Pin must be 4 characters", "giftCardsBalance.errors.cardPin": "Enter a valid pin number", "giftCardsBalance.errors.vaultError": "A technical error has unfortunately occurred. Please refresh to try again.", "giftCardsBalance.errors.giftCardError": "We don’t recognize that gift card information. Please try again.", "valueCenter.tabs.membership": "MEMBERSHIP", "valueCenter.tabs.trackPoints": "TRACK POINTS", "valueCenter.tabs.earnRedeem": "EARN & REDEEM", "valueCenter.membership.featuredBenefits.header": "Your Featured Benefits", "valueCenter.membership.featuredBenefits.pointsTitle1": "1 POINT PER $1 SPENT", "valueCenter.membership.featuredBenefits.pointsTitle2": "5 POINTS PER $1 SPENT", "valueCenter.membership.featuredBenefits.earnOneAltText": "Earn Points Icon", "valueCenter.membership.featuredBenefits.pointsCopytext1": "You earn across our family of brands.", "valueCenter.membership.featuredBenefits.pointsCopytext2": "100 points = $1 in rewards", "valueCenter.membership.featuredBenefits.shippingTitle": "FREE SHIPPING", "valueCenter.membership.featuredBenefits.shippingCopytext1": "Online orders get to you fast. Get free 3-5 day shipping on all orders $50+.", "valueCenter.membership.featuredBenefits.shippingCopytext2": "Online orders get to you fast. Get free 2-3 day shipping on all orders $50+.", "valueCenter.membership.featuredBenefits.shippingCopytext3": "Get free 7-9 day shipping on all orders $50+.", "valueCenter.membership.featuredBenefits.shippingCopytext4": "Get free 7-9 day shipping on all orders $35+.", "valueCenter.membership.featuredBenefits.shippingAltText": "Flying Package Icon", "valueCenter.membership.featuredBenefits.quarterlyTitle": "QUARTERLY BONUS", "valueCenter.membership.featuredBenefits.quarterlyCopytext1": "Earn 125 bonus points each quarter you shop any of our brands.", "valueCenter.membership.featuredBenefits.quarterlyCopytext2": "Earn 250 bonus points each quarter you shop any of our brands.", "valueCenter.membership.featuredBenefits.quarterlyCopytext3": "Earn 500 bonus points each quarter you shop any of our brands.", "valueCenter.membership.featuredBenefits.bonusAltText": "Quarterly Bonus Icon", "valueCenter.membership.featuredBenefits.brandsTitle": "FAMILY OF BRANDS BONUS", "valueCenter.membership.featuredBenefits.brandsCopytext": "Unlock up to 2,000 bonus points annually when you shop at 2 or more of our Gap Inc. brands.", "valueCenter.membership.featuredBenefits.familyBrandsAltText": "Family of Brands Shopping Bag Icon", "valueCenter.membership.featuredBenefits.birthdayTitle": "EXCLUSIVE BIRTHDAY OFFERS", "valueCenter.membership.featuredBenefits.birthdayCopytext": "Loyalty members receive exclusive deals from our family of brands around their birthday.", "valueCenter.membership.compareBenefits.header": "Compare Benefits", "valueCenter.membership.compareBenefits.allBrandsAltText": "One Membership. Four Brands.", "valueCenter.membership.compareBenefits.or": "OR", "valueCenter.membership.compareBenefits.coreLabel": "CORE", "valueCenter.membership.compareBenefits.coreCopytext": "<$500 Annual Brand Spend", "valueCenter.membership.compareBenefits.coreAltText": "Core Level Badge", "valueCenter.membership.compareBenefits.enthusiastLabel": "ENTHUSIAST", "valueCenter.membership.compareBenefits.enthusiastCopytext1": "Cardmembers Start Here", "valueCenter.membership.compareBenefits.enthusiastCopytext2": "$500-$999 Annual Brand Spend", "valueCenter.membership.compareBenefits.enthusiastAltText": "Enthusiast Level Badge", "valueCenter.membership.compareBenefits.iconLabel": "ICON", "valueCenter.membership.compareBenefits.iconCopytext1": "5,000 Points as a Cardmember", "valueCenter.membership.compareBenefits.iconCopytext2": "$1,000+ Annual Brand Spend", "valueCenter.membership.compareBenefits.iconAltText": "Icon Level Badge", "valueCenter.membership.compareBenefits.membersAndCardmembers": "Members and Cardmembers*", "valueCenter.membership.compareBenefits.membersAndCardmembersAltText": "Members and Cardmembers", "valueCenter.membership.compareBenefits.cardmemberExclusive": "Cardmember Exclusive*", "valueCenter.membership.compareBenefits.cardmemberExclusiveAltText": "Cardmember Exclusive", "valueCenter.membership.newCardMember.header": "NEW CARDMEMBER OFFER", "valueCenter.membership.newCardMember.copytext1": "Get 20% off the first time you use our credit card at each of our brands in the first 14 days", "valueCenter.membership.newCardMember.copytext2": "Sign Up For Email & Stay in the know", "valueCenter.membership.newCardMember.copytext3": "250 BONUS POINTS", "valueCenter.membership.earn.header": "EARN AND REDEEM", "valueCenter.membership.earn.or": "OR", "valueCenter.membership.earn.copytext1": "Earn 5 points for every $1 spent as a Cardmember across our family of brands", "valueCenter.membership.earn.copytext2": "Earn 1 point for every $1 spent at our family of brands as a Rewards Member", "valueCenter.membership.earn.copytext3": "Gap Inc. Mastercard® Cardmembers earn 1 point for every $1 spent outside our family of brands", "valueCenter.membership.earn.copytext4": "Every 100 points = $1 reward redeemable across our family of brands, no merchandise restrictions", "valueCenter.membership.specialPerks.header": "SPECIAL PERKS", "valueCenter.membership.specialPerks.shipping1": "Free fast shipping (on orders $50 and more)", "valueCenter.membership.specialPerks.shipping2": "Free shipping", "valueCenter.membership.specialPerks.shipDays1": "3-5 DAYS", "valueCenter.membership.specialPerks.shipDays2": "2-3 DAYS", "valueCenter.membership.specialPerks.shipCash1": "ORDERS $50+", "valueCenter.membership.specialPerks.shipCash2": "ORDERS $35+", "valueCenter.membership.specialPerks.copytext1": "No Annual Fee & $0 Fraud Liability Protection**", "valueCenter.membership.specialPerks.copytext2": "Do Good: Pay it forward when you donate your rewards to a good cause", "valueCenter.membership.specialPerks.copytext3": "Members only exclusive offers", "valueCenter.membership.specialPerks.copytext4": "Early access", "valueCenter.membership.bonuses.header": "BONUSES", "valueCenter.membership.bonuses.brandBonusCopytext": "Family of Brands Bonus: Unlock up to 2,000 bonus points annually when you shop at", "valueCenter.membership.bonuses.brandPoints1": "2 Brands = 500 pts", "valueCenter.membership.bonuses.brandPoints2": "3 Brands = 1,000 pts", "valueCenter.membership.bonuses.brandPoints3": "4 Brands = 2,000 pts", "valueCenter.membership.bonuses.quarterlyBonusCopytext": "Quarterly Bonus: Get bonus points each quarter that you make a purchase at our family of brands", "valueCenter.membership.bonuses.bonusPoints1": "125 BONUS POINTS", "valueCenter.membership.bonuses.bonusPoints2": "250 BONUS POINTS", "valueCenter.membership.bonuses.bonusPoints3": "500 BONUS POINTS", "valueCenter.membership.bonuses.birthday": "Birthday Bonus: Ce<PERSON><PERSON>te your birthday with extra perks—it’s our treat!", "valueCenter.membership.bonuses.copytext1": "Double Reward Day: Double the value of your rewards", "valueCenter.membership.bonuses.copytext2": "Create Your Own Sale Day and receive an extra 15% off any purchase at our family of brands", "valueCenter.membership.exclusives.header": "BRAND EXCLUSIVES", "valueCenter.membership.exclusives.gp": "GAP", "valueCenter.membership.exclusives.gpCopytext": "Convert unused GapCash to points", "valueCenter.membership.exclusives.br": "BANANA REPUBLIC", "valueCenter.membership.exclusives.brCopytext1": "Free basic alterations on Banana Republic merchandise", "valueCenter.membership.exclusives.brCopytext2": "Convert unused Banana Republic Factory StyleCash to points", "valueCenter.membership.exclusives.on": "OLD NAVY", "valueCenter.membership.exclusives.onCopytext": "Convert unused Old Navy Super Cash to points", "valueCenter.membership.exclusives.at": "ATHLETA", "valueCenter.membership.exclusives.atCopytext1": "Access to the Athleta community, including digital and live events, expert resources, fitness classes and more", "valueCenter.membership.addCard.header": "Have a Card?", "valueCenter.membership.addCard.copytext": "Add your Gap Inc. Rewards Credit Card to your account to track your points and rewards.", "valueCenter.membership.addCard.buttonText": "ADD YOUR CARD", "valueCenter.membership.earnPoints.header": "Want to earn more points?", "valueCenter.membership.earnPoints.copytext": "Apply for a {{brand}} Rewards Credit Card and earn 5 points for every $1 spent at our family of brands with your new card as soon as you're approved.**", "valueCenter.membership.earnPoints.buttonText": "APPLY", "valueCenter.membership.earnPoints.altText": "credit card icon", "valueCenter.membership.startShopping.core": "CORE", "valueCenter.membership.startShopping.coreAltText": "Core Level Badge", "valueCenter.membership.startShopping.enthusiast": "ENTHUSIAST", "valueCenter.membership.startShopping.enthusiastAltText": "Enthusiast Level Badge", "valueCenter.membership.startShopping.icon": "ICON", "valueCenter.membership.startShopping.iconAltText": "Icon Level Badge", "valueCenter.membership.startShopping.header1": "Start Shopping!", "valueCenter.membership.startShopping.congratulationsHeader": "Congratulations!", "valueCenter.membership.startShopping.congratulationsHeader_icon": "Congratulations on being Icon!", "valueCenter.membership.startShopping.iconCelebrateAltText": "Icon Level Celebration Trophy", "valueCenter.membership.startShopping.header_core": "You’re on your way to <PERSON>thus<PERSON><PERSON>!", "valueCenter.membership.startShopping.header_enthusiast": "You’re on your way to Icon!", "valueCenter.membership.startShopping.header_icon": "You’re on your way!", "valueCenter.membership.startShopping.congratulationsText_icon": "As an Icon Member, you now have access to exclusive benefits. Enjoy!", "valueCenter.membership.startShopping.congratulationsText_visasignature": "As a Gap Inc. Visa Signature® Cardmember, you have access to all Icon benefits. Enjoy!", "valueCenter.membership.startShopping.copyright_core_noProgress_noCardHolder": "Spend {{amount}} or become a Cardmember by the end of the year to get all the ", "valueCenter.membership.startShopping.copyright_core_progress_noCardHolder": "Spend {{amount}} by the end of the year or become a Cardmember to get all the ", "valueCenter.membership.startShopping.copyright_core_noProgress_cardHolder": "Spend {{amount}} by the end of the year to get all the ", "valueCenter.membership.startShopping.copyright_core_progress_cardHolder": "Spend {{amount}} by the end of the year to get all the ", "valueCenter.membership.startShopping.copyright_enthusiast_cardHolder": "Spend {{amount}} in our family of brands or earn {{points}} points by the end of the year to get all the ", "valueCenter.membership.startShopping.copyright_enthusiast_noCardHolder": "Spend {{amount}} by the end of the year to get all the ", "valueCenter.membership.startShopping.copyright_icon_cardHolder": "Spend {{amount}} in our family of brands or earn {{points}} points by the end of the year to stay Icon", "valueCenter.membership.startShopping.copyright_icon_noCardHolder": "Spend {{amount}} by the end of the year to stay Icon", "valueCenter.membership.startShopping.copyright_next_core": "benefits of Enthusiast", "valueCenter.membership.startShopping.copyright_next_enthusiast": "benefits of Icon", "valueCenter.membership.startShopping.copyright_transition": "Congrats! You've qualified for the next level up. Your new benefits will kick in within 60 days.", "valueCenter.membership.startShopping.spent": "You've Spent", "valueCenter.membership.startShopping.earned": "You’ve Earned", "valueCenter.membership.startShopping.or": "OR", "valueCenter.membership.startShopping.applyCard_gp": "Apply for a Gap Good Rewards Credit Card", "valueCenter.membership.startShopping.applyCard_gap": "Apply for a Gap Good Rewards Credit Card", "valueCenter.membership.startShopping.applyCard_on": "Apply for a Navyist Rewards Credit Card", "valueCenter.membership.startShopping.applyCard_br": "Apply for a Banana Republic Rewards Credit Card", "valueCenter.membership.startShopping.applyCard_at": "Apply for an Athleta Rewards Credit Card", "valueCenter.membership.startShopping.applyCard_gpfs": "Apply for a Gap Good Rewards Credit Card", "valueCenter.membership.startShopping.applyCard_gapfs": "Apply for a Gap Good Rewards Credit Card", "valueCenter.membership.startShopping.applyCard_brfs": "Apply for a Banana Republic Rewards Credit Card", "valueCenter.membership.startShopping.applyCard_CORE": " and get all the benefits of Enthusiast as soon as you’re approved—plus earn 5 points per $1 spent at our family of brands with your new card!**", "valueCenter.membership.startShopping.applyCard_ENTHUSIAST": " and earn 5 points per $1 spent at our family of brands with your new card!**", "valueCenter.membership.startShopping.applyCard_ICON": " and earn 5 points per $1 spent at our family of brands with your new card!**", "valueCenter.membership.startShopping.footer_core": "Enjoy your benefits until you’re upgraded. Eligibility for level upgrade subject to change based on account activity.**", "valueCenter.membership.startShopping.footer_enthusiast_noCardHolder": "Enjoy your current benefits until you're upgraded. Eligibility for level upgrade subject to change based on account activity.**", "valueCenter.membership.startShopping.footer_enthusiast_cardHolder": "Enjoy your current benefits until you're upgraded. Eligibility for level upgrade subject to change based on account activity.**", "valueCenter.membership.startShopping.footer_icon_noCardHolder_nextYearExp": "Enjoy your current benefits until {{date}}.**", "valueCenter.membership.startShopping.footer_icon_noCardHolder_currentYearExp": "Enjoy your current benefits until {{date}}. Maintaining level eligibility subject to change based on account activity.**", "valueCenter.membership.startShopping.footer_icon_cardHolder_currentYearExp": "Enjoy your current benefits until {{date}}. Maintaining level eligibility subject to change based on account activity.**", "valueCenter.membership.startShopping.manageCard": "Manage and Pay My Gap Inc. Rewards Credit Card", "valueCenter.trackPoints.pointsSummary.youHave": "You have", "valueCenter.trackPoints.pointsSummary.points": "Points", "valueCenter.trackPoints.pointsSummary.inRewards": "({{rewards}} in Rewards)", "valueCenter.trackPoints.pointsSummary.viewCopyright": "View and redeem all rewards in checkout and bag", "valueCenter.trackPoints.pointsSummary.legalCopyright": "Some legal conditions apply. Follow Details and Terms and Conditions links at bottom of page for more", "valueCenter.trackPoints.pointsSummary.earnPointsText": "Earn points for every $1 spent", "valueCenter.trackPoints.pointsSummary.rewardsText": "100 points = $1 in rewards", "valueCenter.trackPoints.table.activity": "ACTIVITY", "valueCenter.trackPoints.table.points": "POINTS", "valueCenter.trackPoints.table.pagination": "Page of {{index}} of {{total}}", "valueCenter.trackPoints.table.empty": "Check back here to track points as you earn them.", "valueCenter.trackPoints.table.history": "Includes up to 3 months of points activity", "valueCenter.trackPoints.table.order": "Order #", "valueCenter.trackPoints.pagination.page": "Page", "valueCenter.trackPoints.pagination.of": "of", "valueCenter.trackPoints.pagination.left": "Go to previous page", "valueCenter.trackPoints.pagination.right": "Go to next page", "valueCenter.trackPoints.pagination.pagination": "Pagination", "valueCenter.earnRedeem.rewardsAndEarnings.title": "Rewards & Earnings**", "valueCenter.earnRedeem.rewardsAndEarnings.description1": "Redeem rewards and Bonus Cash in store or online. Combine them for extra savings!", "valueCenter.earnRedeem.rewardsAndEarnings.description2": "As you earn points, you'll be able to view them here and redeem rewards in checkout.", "valueCenter.earnRedeem.rewardPoints.header": "REWARD", "valueCenter.earnRedeem.rewardPoints.redeem": "Redeem in Checkout", "valueCenter.earnRedeem.rewardPoints.balance": "Points balance: {{points}} points", "valueCenter.earnRedeem.rewardPoints.description": "Rewards are redeemed in 100-point increments.", "valueCenter.earnRedeem.rewardPoints.doubleRewardTitle": "It's Double Reward Day!", "valueCenter.earnRedeem.rewardPoints.doubleRewardText": "Today only, redeem double the value of your rewards. The rewards amount below is double the usual value.", "valueCenter.earnRedeem.rewardPoints.altText": "Reward Stars Icon", "valueCenter.earnRedeem.rewardTile.header": "GapCash", "valueCenter.earnRedeem.rewardTile.redeemDate": "Redeem {{date1}} - {{date2}}", "valueCenter.earnRedeem.rewardTile.expiresDate": "Expires {{date}}", "valueCenter.earnRedeem.rewardTile.viewButton": "VIEW CODE", "valueCenter.earnRedeem.rewardTile.terms": "View Terms", "valueCenter.earnRedeem.rewardTile.gapCash": "GapCash", "valueCenter.earnRedeem.rewardTile.styleCash": "BR Factory StyleCash", "valueCenter.earnRedeem.rewardTile.superCash": "Old Navy SuperCash", "valueCenter.earnRedeem.rewardTile.card": "Gap inc credit card reward", "valueCenter.earnRedeem.rewardTile.reward": "<PERSON><PERSON>", "valueCenter.earnRedeem.rewardTile.cashApplied": "Cash will be applied at checkout.", "valueCenter.earnRedeem.rewardTile.rewardApplied": "Rewards will be redeemed at checkout.", "valueCenter.earnRedeem.rewardTile.cardApplied": "Use your Gap Inc. Credit Card to redeem at checkout.", "valueCenter.earnRedeem.rewardTile.errorMessage": "We're unable to apply these savings. Please refresh the page or try again in checkout.", "valueCenter.earnRedeem.rewardTile.BonusCash_altText": "Bonus Cash Icon", "valueCenter.earnRedeem.rewardTile.rewardsStars_altText": "Rewards Icon", "valueCenter.earnRedeem.barcodeModal.reward": "{{cash}} Reward", "valueCenter.earnRedeem.barcodeModal.description1": "To redeem in store, present this code to the cashier.", "valueCenter.earnRedeem.barcodeModal.description2": "To redeem online, enter the promo code below in checkout.", "valueCenter.earnRedeem.barcodeModal.expires": "Expires {{date}}", "valueCenter.earnRedeem.barcodeModal.specialOffer": "Special Offer", "valueCenter.earnRedeem.barcodeModal.altText": "Family of Brands Lo<PERSON>", "valueCenter.earnRedeem.termsModal.title1": "REWARDS, TERMS & SERVICES", "valueCenter.earnRedeem.termsModal.title2": "VIEW TERMS", "valueCenter.earnRedeem.payItForward.title": "Pay it Forward**", "valueCenter.earnRedeem.donateRewards.header": "DO GOOD", "valueCenter.earnRedeem.donateRewards.altText": "Helping Hands Icon", "valueCenter.earnRedeem.donateRewards.title": "Donate Your Rewards", "valueCenter.earnRedeem.donateRewards.description": "Pay it forward when you donate your rewards to a good cause. We’ll match all donations up to USD $1 million combined across our U.S. & Canada Rewards Programs.", "valueCenter.earnRedeem.donateRewards.descriptionAT": "For the past 23 years, Athleta has been dedicated to a single aim: igniting the potential of all women and girls. The Power of She Fund puts this mission into action with grant programs that fuel confidence through movement and connection by supporting amazing non-profits like the Women’s Sports Foundation and YWCA.", "valueCenter.earnRedeem.donateRewards.learnMore": "Learn more about the organizations", "valueCenter.earnRedeem.donateRewards.balance": "Balance: {{points}} points ({{cash}} in rewards)", "valueCenter.earnRedeem.donateRewards.balanceDoubleRewards": "Balance: {{points}} points", "valueCenter.earnRedeem.donateRewards.donate": "DONATE", "valueCenter.earnRedeem.donateRewards.infoDescription": "Rewards are donated in 100-point increments.", "valueCenter.earnRedeem.donateRewards.organizationPlaceholder": "Select Organization", "valueCenter.earnRedeem.donateRewards.amountPlaceholder": "Select Amount", "valueCenter.earnRedeem.donateRewards.amountSelectItem": "{{cash}} ({{points}} points)", "valueCenter.earnRedeem.donateRewards.partnership": "in partnership with ", "valueCenter.earnRedeem.donateRewards.supportedBy": "supported by our family of brands", "valueCenter.earnRedeem.donateRewards.validationAmountError": "Select an amount to donate.", "valueCenter.earnRedeem.donateRewards.validationOrganizationError": "Select an organization.", "valueCenter.earnRedeem.donateRewards.successMessage1": "You’ve donated {{amount}} points in rewards to {{charityName}}.", "valueCenter.earnRedeem.donateRewards.successMessage2": "Thank you for paying it forward and making a difference. We’ve doubled the power of your donation by matching it.", "valueCenter.earnRedeem.donateRewards.errorMessage": "Unable to complete this Donation. Please refresh the page and try again.", "valueCenter.earnRedeem.donateRewards.lowBalance": "Once you have earned at least $1 in rewards, they can be donated.", "valueCenter.earnRedeem.donateRewards.doubleRewardsMessage": "The balance above is available to donate. Double Rewards Day doesn't apply to Donate Your Rewards.", "valueCenter.earnRedeem.organizationModal.header": "ORGANIZATION DETAILS", "valueCenter.earnRedeem.organizationModal.title": "Do Good", "valueCenter.earnRedeem.organizationModal.description": "Pay it forward when you donate your rewards to a good cause! Select from the organizations below, in partnership with our family of brands, to do good.", "valueCenter.earnRedeem.organizationModal.handsAltText": "Helping Hands Icon", "valueCenter.earnRedeem.earnMorePoints.title": "<PERSON><PERSON><PERSON>**", "valueCenter.earnRedeem.birthdayBonus.header": "BONUS POINTS", "valueCenter.earnRedeem.birthdayBonus.altText": "Birthday Cupcake", "valueCenter.earnRedeem.birthdayBonus.addButton": "ADD BIRTHDAY", "valueCenter.earnRedeem.birthdayBonus.outside.title": "Birthday Bonus", "valueCenter.earnRedeem.birthdayBonus.outside.description": "Celebrate your birthday with an extra perk in {{month}}—it’s our treat!", "valueCenter.earnRedeem.birthdayBonus.during.title": "Happy Birthday!", "valueCenter.earnRedeem.birthdayBonus.during.description": "Celebrate your birthday with an extra perk—it’s our treat! Check your points tracker to see your gift.", "valueCenter.earnRedeem.birthdayBonus.missing.title": "Tell Us Your Birthday", "valueCenter.earnRedeem.birthdayBonus.missing.description": "Celebrate your birthday with an extra perk—it’s our treat!", "valueCenter.earnRedeem.birthdayBonusPromo.header": "BIRTHDAY BONUS", "valueCenter.earnRedeem.birthdayBonusPromo.promoLabel": "Enjoy {{percentage}}% off from {{brand}} for your birthday!", "valueCenter.earnRedeem.birthdayBonusPromo.description": "Exclusions apply. View terms below for details.", "valueCenter.earnRedeem.birthdayBonusPromo.viewCode": "VIEW CODE", "valueCenter.earnRedeem.birthdayBonusPromo.viewTerms": "View Terms", "valueCenter.earnRedeem.birthdayBonusPromo.modal.header": "BIRTHDAY BONUS CODE", "valueCenter.earnRedeem.birthdayBonusPromo.modal.description": "To redeem in store, present this code to the cashier. To redeem online, enter the promo code below in checkout.", "valueCenter.earnRedeem.expiredRewardTile.header": "BONUS POINTS", "valueCenter.earnRedeem.expiredRewardTile.title": "Convert Expired {{type}} to Points", "valueCenter.earnRedeem.expiredRewardTile.expiredDate": "Expired {{date}}", "valueCenter.earnRedeem.expiredRewardTile.description": "Tap to convert to {{points}} points.", "valueCenter.earnRedeem.expiredRewardTile.convertButton": "CONVERT TO {{points}} points", "valueCenter.earnRedeem.expiredRewardTile.successMessage": "{{points}} points converted", "valueCenter.earnRedeem.expiredRewardTile.errorAlreadyConverted": "Bonus Cash has already been converted. Check Track Points for details.", "valueCenter.earnRedeem.specialOffer.header": "MEMBER EXCLUSIVE", "valueCenter.earnRedeem.specialOffer.title": "Special Offer", "valueCenter.earnRedeem.specialOffer.availableDate": "Available {{date1}} - {{date2}}", "valueCenter.earnRedeem.specialOffer.description1": "Barcode Test Offer", "valueCenter.earnRedeem.specialOffer.description2": "Exclusions apply. View terms below, for details.", "valueCenter.earnRedeem.specialOffer.viewButton": "VIEW CODE", "valueCenter.earnRedeem.specialOffer.terms": "View Terms", "valueCenter.earnRedeem.specialOffer.offerApplied": "Special offer will be applied at checkout.", "valueCenter.earnRedeem.specialOffer.altText": "Special Offer <PERSON><PERSON>", "valueCenter.earnRedeem.familyBrands.header": "BONUS POINTS", "valueCenter.earnRedeem.familyBrands.header1": "LEVEL UP TO UNLOCK", "valueCenter.earnRedeem.familyBrands.bagAltText": "Family of Brands Shopping Bag Icon", "valueCenter.earnRedeem.familyBrands.logosAltText": "Family of Brands Lo<PERSON>", "valueCenter.earnRedeem.familyBrands.title": "Family of Brands Bonus", "valueCenter.earnRedeem.familyBrands.description1": "Unlock up to 2,000 bonus points annually when you shop at 2 or more of our Gap Inc. brands.", "valueCenter.earnRedeem.familyBrands.description2": "Congrats! You’ve unlocked 500 bonus points by shopping at 2 of our Gap Inc. brands! Keep earning!", "valueCenter.earnRedeem.familyBrands.description3": "Congrats! You’ve unlocked 1,000 bonus points by shopping at 3 of our Gap Inc. brands! Keep earning!", "valueCenter.earnRedeem.familyBrands.description4": "Congrats! You’ve unlocked 2,000 bonus points by shopping at all 4 of our Gap Inc. brands!", "valueCenter.earnRedeem.familyBrands.description5_part_1": "Core Member, ", "valueCenter.earnRedeem.familyBrands.description5_part_2": "level up to Enthusiast ", "valueCenter.earnRedeem.familyBrands.description5_part_3": "to unlock up to ", "valueCenter.earnRedeem.familyBrands.description5_part_4": "2,000 bonus points ", "valueCenter.earnRedeem.familyBrands.description5_part_5": "annually when you shop at 2 or more of our Gap Inc. brands.", "valueCenter.earnRedeem.familyBrands.brand2": "2 brands = 500 pts", "valueCenter.earnRedeem.familyBrands.brand3": "3 brands = 1,000 pts", "valueCenter.earnRedeem.familyBrands.brand4": "4 brands = 2,000 pts", "valueCenter.earnRedeem.familyBrands.brandChecks.hasGP_true": "You have shopped at Gap", "valueCenter.earnRedeem.familyBrands.brandChecks.hasGP_false": "You have not shopped at Gap", "valueCenter.earnRedeem.familyBrands.brandChecks.hasBR_true": "You have shopped at Banana Republic", "valueCenter.earnRedeem.familyBrands.brandChecks.hasBR_false": "You have not shopped at Banana Republic", "valueCenter.earnRedeem.familyBrands.brandChecks.hasON_true": "You have shopped at Old Navy", "valueCenter.earnRedeem.familyBrands.brandChecks.hasON_false": "You have not shopped at Old Navy", "valueCenter.earnRedeem.familyBrands.brandChecks.hasAT_true": "You have shopped at Athleta", "valueCenter.earnRedeem.familyBrands.brandChecks.hasAT_false": "You have not shopped at Athleta", "valueCenter.earnRedeem.convertCashPoints.header": "BONUS POINTS", "valueCenter.earnRedeem.convertCashPoints.altText": "Bonus Cash Icon", "valueCenter.earnRedeem.convertCashPoints.title": "Convert Expired Bonus Cash to Points", "valueCenter.earnRedeem.convertCashPoints.description": "Convert unused Super Cash, GapCash or StyleCash to points. Add code of unused Bonus Cash within 30 days of expiration to convert.", "valueCenter.earnRedeem.convertCashPoints.placeholder": "Redemption Code", "valueCenter.earnRedeem.convertCashPoints.convertButton": "CONVERT", "valueCenter.earnRedeem.convertCashPoints.validationError": "Enter a 12 digit code", "valueCenter.earnRedeem.convertCashPoints.successMessage": "{{points}} points converted from Bonus Cash.", "valueCenter.earnRedeem.convertCashPoints.errorMessage": "Unable to convert this Bonus Cash. Please refresh the page and try again.", "valueCenter.earnRedeem.wantEarnMore.header": "BONUS POINTS", "valueCenter.earnRedeem.wantEarnMore.title": "Want to Earn More Points?", "valueCenter.earnRedeem.wantEarnMore.description": "Apply for a {{brand}} Rewards Credit Card and get all the benefits of Enthusiast as soon as you’re approved—plus earn {{points}} points per $1 spent at our family of brands with your new card!**", "valueCenter.earnRedeem.wantEarnMore.applyButton": "APPLY NOW", "valueCenter.earnRedeem.wantEarnMore.applyCardLinkText_gp": "Apply for a Gap Good Rewards Credit Card", "valueCenter.earnRedeem.wantEarnMore.applyCardLinkText_on": "Apply for a Navyist Rewards Credit Card", "valueCenter.earnRedeem.wantEarnMore.applyCardLinkText_br": "Apply for a Banana Republic Rewards Credit Card", "valueCenter.earnRedeem.wantEarnMore.applyCardLinkText_at": "Apply for an Athleta Rewards Credit Card", "valueCenter.earnRedeem.wantEarnMore.applyCardLinkText_gpfs": "Apply for a Gap Good Rewards Credit Card", "valueCenter.earnRedeem.wantEarnMore.applyCardLinkText_brfs": "Apply for a Banana Republic Rewards Credit Card", "valueCenter.earnRedeem.wantEarnMore.applyCardEncourageText_CORE": " and get all the benefits of Enthusiast as soon as you’re approved—plus earn {{points}} points per $1 spent at our family of brands with your new card!**", "valueCenter.earnRedeem.wantEarnMore.applyCardEncourageText_ENTHUSIAST": " and earn {{points}} points per $1 spent at our family of brands with your new card!**", "valueCenter.earnRedeem.wantEarnMore.applyCardEncourageText_ICON": " and earn {{points}} points per $1 spent at our family of brands with your new card!**", "valueCenter.earnRedeem.shopQuarterly.header": "BONUS POINTS", "valueCenter.earnRedeem.shopQuarterly.header1": "LEVEL UP TO UNLOCK", "valueCenter.earnRedeem.shopQuarterly.altText": "Quarterly Bonus Icon", "valueCenter.earnRedeem.shopQuarterly.title": "Shop Quarterly", "valueCenter.earnRedeem.shopQuarterly.description1": "Congrats! You’ve earned by making a purchase at our family of brands this quarter.", "valueCenter.earnRedeem.shopQuarterly.description2": "Get bonus points each quarter that you make a purchase at our family of brands.", "valueCenter.earnRedeem.shopQuarterly.description3": "<PERSON><PERSON><PERSON> ", "valueCenter.earnRedeem.shopQuarterly.description4": "by making a purchase at our family of brands this quarter.", "valueCenter.earnRedeem.shopQuarterly.corePoints": "125 bonus points ", "valueCenter.earnRedeem.shopQuarterly.enthusiastPoints": "250 bonus points ", "valueCenter.earnRedeem.shopQuarterly.iconPoints": "500 bonus points ", "valueCenter.earnRedeem.shopQuarterly.core": "Core Members = 125 points", "valueCenter.earnRedeem.shopQuarterly.enthusiast": "Enthusiast Members = 250 points", "valueCenter.earnRedeem.shopQuarterly.icon": "Icon Members = 500 points", "valueCenter.earnRedeem.shopQuarterly.linkActivity": "Track points", "valueCenter.earnRedeem.shopQuarterly.description5_part_1": "Core Member, ", "valueCenter.earnRedeem.shopQuarterly.description5_part_2": "level up to Enthusiast ", "valueCenter.earnRedeem.shopQuarterly.description5_part_3": "to get ", "valueCenter.earnRedeem.shopQuarterly.description5_part_4": "250 bonus points ", "valueCenter.earnRedeem.shopQuarterly.description5_part_5": "when you make a purchase at our family of brands starting next quarter.", "valueCenter.unlockMoreBenefits.header": "Unlock More Benefits", "valueCenter.footer.text1": "Gap Inc. Credit Cards subject to credit approval. ", "valueCenter.footer.text2": "See ", "valueCenter.footer.text3": "Details", "valueCenter.footer.text4": " and Rewards Program ", "valueCenter.footer.text5": "Terms and Conditions", "valueCenter.errors.infoUnavailable": "Some information is unavailable due to a technical error. Please try again later.", "accountSecurity.header": "Account Security", "accountSecurity.mainDescription": "These recognized devices are currently signed in to your Gap, Old Navy, Banana Republic, and Athleta account.", "accountSecurity.footerDescription": "This will not impact your current visit but you'll be prompted to sign in on a future visit.", "accountSecurity.removeDevices": "Remove Devices", "accountSecurity.signout": "Sign Out", "accountSecurity.lastAccessed": "Last accessed", "accountSecurity.webBrowser": "Web Browser", "accountSecurity.mobileApp": "Mobile App", "accountSecurity.mac": "<PERSON>", "accountSecurity.linux": "Linux", "accountSecurity.windows": "Windows", "accountSecurity.iosDevice": "iOS Device", "accountSecurity.androidDevice": "Android Device", "accountSecurity.today": "Today", "accountSecurity.error1": "There was a technical error loading your recognized devices. Please refresh the page to try again.", "accountSecurity.error2": "There was a technical error. Please refresh the page and try again to remove your devices.", "accountSecurity.summaryDescription": "End all sessions of any saved signed-in visits to your Gap, Old Navy, Banana Republic, and Athleta account.", "accountSecurity.endAllSessions": "End All Sessions", "accountSecurity.emptyStateDescription": "There are no other recognized devices currently signed in to your Gap, Old Navy, Banana Republic, and Athleta account.", "accountSecurity.emptyStateInfo": "Recently-used-devices, including this one, may remain signed in for a short period.", "accountSecurity.unknown": "Unknown", "wismo.noOrders": "You have no recent orders to track.", "wismo.wismoFetchError": "Your order activity is not available at this time. Please try again later.", "wismo.wismoCardmyOrdersText": "My Orders", "wismo.wismoTile.estimatedDeliveryDate": "Est. delivery by ", "wismo.wismoTile.estimatedShipDate": "Est. ship by ", "wismo.wismoTile.trackPackage": "Track Package", "wismo.wismoTile.orderDetails": "Order Details", "wismo.wismoTile.details": "Details", "wismo.wismoTile.imageNotAvailable": "Image Not Available", "wismo.deviverStattus.backOrdered": "One or more of your items is on backorder.", "wismo.deviverStattus.delayed": "A package has been delayed.", "wismo.deviverStattus.delivered": "A package was delivered!", "wismo.deviverStattus.orderPlaced": "Success! Your order has been received.", "wismo.deviverStattus.orderReceived": "Success! Your order has been received.", "wismo.deviverStattus.outForDelivery": "A package is arriving soon!", "wismo.deviverStattus.preparingForShipment": "A package is almost ready to go!", "wismo.deviverStattus.shipped": "A package has shipped!", "wismo.deviverStattus.returnRequested": "Your return is in progress.", "wismo.deviverStattus.returned": "Your return has been processed.", "wismo.deviverStattus.cancelled": "A delivery has been cancelled.", "wismo.deviverStattus.cancelled-customer": "A delivery has been cancelled.", "wismo.deviverStattus.cancelled-item": "Your cancellation request has been received.", "wismo.deviverStattus.cancelled-card": "A delivery has been cancelled.", "wismo.deviverStattus.WORKING_ON_YOUR_ORDER": "A shipment is almost ready to go!", "wismo.deviverStattus.CANCELED_ITEM_UNAVALIABLE": "A delivery has been cancelled.", "wismo.deviverStattus.BOPIS_RETURNED": "Your return has been received.", "wismo.deviverStattus.ORDER_READY": "A pickup is ready!", "wismo.deviverStattus.REFUNDED": "Your refund is complete.", "wismo.deviverStattus.statusUnavailable": "Check back later for an update on your order.", "wismo.deviverStattus.PICKED_UP": "Picked up", "wismo.deviverStattus.PICK_UP": "Picked up", "wismo.deviverStattus.VERIFIED": "Success! Your order has been received.", "wismo.deviverStattus.AWAITING_CANCELLATION": "Your cancellation request has been received.", "wismo.deviverStattus.workingOnYourOrder": "Your pickup order is being prepared.", "wismo.deviverStattus.cancelledUnavailable": "A delivery has been cancelled.", "wismo.deviverStattus.orderReady": "A pickup is ready!", "wismo.deviverStattus.refunded": "Your refund is complete.", "wismo.deviverStattus.pickUp": "Picked up", "wismo.deviverStattus.verified": "Success! Your order has been received.", "wismo.deviverStattus.awaitingCancellation": "Your cancellation request has been received.", "wismo.deviverStattus.cancelledItem": "Your cancellation request has been received.", "wismo.deviverStattus.cancelledCard": "A delivery has been cancelled.", "wismo.deviverStattus.cancelledCustomer": "Item(s) in your order have been cancelled.", "wismo.deviverStattus.bopisReturned": "Your return has been received.", "wismo.deviverStattus.inProgress": "Your pickup order is being prepared.", "wismo.deviverStattus.inTransit": "A package is on the way!", "wismo.deviverStattus.undeliverable": "There was an issue with your package delivery.", "wismo.deviverStattus.exception": "Check back later for an update on your order.", "wismo.deviverStattus.justShipped": "A package has shipped!", "wismo.deviverStattus.returnInitiated": "Your return is in progress.", "wismo.deviverStattus.packageUpdate": "Check the status of your package!", "wismo.deviverStattus.purchasedInStore": "Purchased In Store", "wismo.omniDeliveryStatus.backOrdered": "Backordered", "wismo.omniDeliveryStatus.delayed": "Delayed", "wismo.omniDeliveryStatus.delivered": "Delivered", "wismo.omniDeliveryStatus.orderPlaced": "Order Placed", "wismo.omniDeliveryStatus.orderReceived": "Order Placed", "wismo.omniDeliveryStatus.outForDelivery": "Arriving Soon", "wismo.omniDeliveryStatus.preparingForShipment": "Preparing for Shipment", "wismo.omniDeliveryStatus.shipped": "Shipped", "wismo.omniDeliveryStatus.returnRequested": "Return In Progress", "wismo.omniDeliveryStatus.returned": "Returned", "wismo.omniDeliveryStatus.cancelled": "Delivery Cancellation", "wismo.omniDeliveryStatus.cancelled-customer": "Delivery Cancellation", "wismo.omniDeliveryStatus.cancelled-item": "Awaiting Can<PERSON><PERSON>", "wismo.omniDeliveryStatus.cancelled-card": "Delivery Cancellation", "wismo.omniDeliveryStatus.WORKING_ON_YOUR_ORDER": "Preparing for Shipment", "wismo.omniDeliveryStatus.CANCELED_ITEM_UNAVALIABLE": "Delivery Cancellation", "wismo.omniDeliveryStatus.BOPIS_RETURNED": "Returned Received", "wismo.omniDeliveryStatus.ORDER_READY": "Ready for Pickup", "wismo.omniDeliveryStatus.REFUNDED": "Refund Complete", "wismo.omniDeliveryStatus.statusUnavailable": "Status Unavailable", "wismo.omniDeliveryStatus.PICKED_UP": "Picked up", "wismo.omniDeliveryStatus.PICK_UP": "Picked up", "wismo.omniDeliveryStatus.VERIFIED": "Order Placed", "wismo.omniDeliveryStatus.AWAITING_CANCELLATION": "Awaiting Can<PERSON><PERSON>", "wismo.omniDeliveryStatus.workingOnYourOrder": "Being Prepared", "wismo.omniDeliveryStatus.cancelledUnavailable": "Delivery Cancellation", "wismo.omniDeliveryStatus.orderReady": "Ready for Pickup", "wismo.omniDeliveryStatus.refunded": "Refund Complete", "wismo.omniDeliveryStatus.pickUp": "Picked up", "wismo.omniDeliveryStatus.verified": "Order Placed", "wismo.omniDeliveryStatus.awaitingCancellation": "Awaiting Can<PERSON><PERSON>", "wismo.omniDeliveryStatus.cancelledItem": "Awaiting Can<PERSON><PERSON>", "wismo.omniDeliveryStatus.cancelledCard": "Delivery Cancellation", "wismo.omniDeliveryStatus.cancelledCustomer": "Item(s) Cancelled", "wismo.omniDeliveryStatus.bopisReturned": "Returned Received", "wismo.omniDeliveryStatus.inProgress": "Being Prepared", "wismo.omniDeliveryStatus.inTransit": "Shipped", "wismo.omniDeliveryStatus.undeliverable": "Delivery Issue", "wismo.omniDeliveryStatus.exception": "Status Unavailable", "wismo.omniDeliveryStatus.justShipped": "Shipped", "wismo.omniDeliveryStatus.returnInitiated": "Return In Progress", "wismo.omniDeliveryStatus.packageUpdate": "Check the status of your package!", "wismo.omniDeliveryStatus.purchasedInStore": "Purchased In Store", "wismo.bopisStatusText.returned": "Returned", "wismo.bopisStatusText.orderReady": "Pick up by {{dateTime}}", "wismo.bopisStatusText.refunded": "Refunded", "wismo.bopisStatusText.pickedUp": "Picked up", "loyaltyHub.memberPerks": "Member <PERSON><PERSON>", "loyaltyHub.memberPerks.lastChance": "Last Chance", "loyaltyHub.memberPerks.viewCode": "View Code", "loyaltyHub.memberPerks.viewTerms": "View Terms", "loyaltyHub.memberPerks.learnMore": "Learn More", "loyaltyHub.memberPerks.warning": "Some Member Perks are not available. Please refresh the page or try again later.", "loyaltyHub.memberPerks.addCode": "Add Code", "loyaltyHub.memberPerks.error.invalidCode": "Invalid Code", "loyaltyHub.memberPerks.bonusCashTile.gapCash": "GapCash", "loyaltyHub.memberPerks.bonusCashTile.styleCash": "StyleCash", "loyaltyHub.memberPerks.bonusCashTile.superCash": "SuperCash", "loyaltyHub.memberPerks.bonusCashTile.percentageOff": "Off {{brand}}", "loyaltyHub.memberPerks.bonusCashTile.dateRange": "Available {{date1}} - {{date2}}", "loyaltyHub.memberPerks.bonusCashTile.availableDate": "Available: {{date1}}", "loyaltyHub.memberPerks.bonusCashTile.expiresDate": "Expires in {{num}} days", "loyaltyHub.memberPerks.bonusCashTile.convertExpired": "Convert Expired Bonus cash to Points", "loyaltyHub.memberPerks.birthdayOffer": "Birthday Offer", "loyaltyHub.memberPerks.birthdayOfferTitle": "Birthday Offer Code", "loyaltyHub.memberPerks.birthdayOffers": "Birthday Offers ({{num}})", "loyaltyHub.memberPerks.addBirthday": "Add Birthday", "loyaltyHub.memberPerks.birthdayTile.addBirthday.header": "Tell Us Your Birthday", "loyaltyHub.memberPerks.birthdayTile.addBirthday.description": "Celebrate your birthday with an extra perk—it’s our treat!", "loyaltyHub.memberPerks.birthdayTile.dateRange": "{{date1}} - {{date2}}", "loyaltyHub.memberPerks.birthdayTile.availableDate": "{{date1}}", "loyaltyHub.memberPerks.birthdayTile.upcomingBirthday.description": "Check back in {{month}} for a special birthday discount from each of our brands", "loyaltyHub.memberPerks.unlockMore": "Unlock More", "loyaltyHub.memberPerks.cardmemberTile.header": "Level Up To Cardmember", "loyaltyHub.memberPerks.cardmemberTile.description1": "Cardmembers get 5 points for every $1 spent at our family of brands", "loyaltyHub.memberPerks.cardmemberTile.description2": "They also get exclusive access to styles and offers, new cardmember discounts across all of our family of brands and more", "loyaltyHub.memberPerks.applyNow": "Apply Now", "loyaltyHub.memberPerks.customerOfferTile.iconMembersOnly": "Icon Members Only", "loyaltyHub.memberPerks.activate": "Activate", "loyaltyHub.memberPerks.customerOfferTile.dateRange": "Available {{date1}} - {{date2}}", "loyaltyHub.memberPerks.customerOfferTile.availableDate": "Available: {{date1}}", "loyaltyHub.memberPerks.customerOfferTile.expiresDatePlural": "Expires in {{num}} days", "loyaltyHub.memberPerks.customerOfferTile.expiresDateSingular": "Expires in 1 day", "loyaltyHub.memberPerks.customerOfferTile.errorMessage": "We're unable to apply these savings. Please refresh the page or try again in checkout.", "loyaltyHub.memberPerks.yourOtherBenefits": "Your Other Benefits", "loyaltyHub.memberPerks.cardmemberBenefits.header": "Cardmember Benefits", "loyaltyHub.memberPerks.cardmemberBenefits.description1": "Exclusive Access", "loyaltyHub.memberPerks.cardmemberBenefits.description2": "Early Access", "loyaltyHub.memberPerks.cardmemberBenefits.description3": "Family of Brands Discount", "loyaltyHub.memberPerks.cardmemberBenefits.description4": "5x points when using Card to make purchases", "loyaltyHub.memberPerks.cardmemberBenefits.learnMore.description": "New Cardmember Discount: Cardmembers get 20% off the first time you use our credit card at each of our brands in the first 14 days\n\nFamily or Brands Discount: Cardmembers get an additional 20% off the first purchase at each of our brands in the first 14 days.\n\nExclusive Access: Cardmembers get exclusive access to product drops and items.\n\nEarn 5 points for every $1 spent as a Cardmember across our family of brands.", "loyaltyHub.memberPerks.rewardsMarketplaceTile.header": "Rewards Marketplace", "loyaltyHub.memberPerks.rewardsMarketplaceTile.description": "Use your points to shop exclusive offers, products, and experiences just for our Rewards Members.", "loyaltyHub.memberPerks.visitMarketplace": "Visit Marketplace", "loyaltyHub.memberPerks.freeShipping.header": "Free Shipping On Purchases $50+", "loyaltyHub.memberPerks.freeShipping.description1": "Core Members (4-6 days)", "loyaltyHub.memberPerks.freeShipping.description2": "Enthusiast Members (3-5 days)", "loyaltyHub.memberPerks.freeShipping.description3": "Icon Members (3-5 days)", "loyaltyHub.memberPerks.earnAndRedeem.header": "Earn & Redeem", "loyaltyHub.memberPerks.earnAndRedeem.description1": "Earn 1 point for every dollar spent", "loyaltyHub.memberPerks.earnAndRedeem.description2": "Earn 5 points for every $1 spent as a Cardmember when using Gap Inc. credit card", "loyaltyHub.memberPerks.earnAndRedeem.learnMore.description": "Earn 1 point for every $1 spent at our family of brands as a Rewards Member\n\nGap Inc. Mastercard® Cardmembers earn 5 points for every $1 spent when using their Gap Inc. across our family of brands\n\nGap Inc. Mastercard® Cardmembers earn 1 point for every $1 spent outside our family of brands\n\nGap Inc. Mastercard® Cardmembers earn 1 point for every $1 spent outside our family of brands\n\nEvery 100 points = $1 reward redeemable across our family of brands, no merchandise restrictions", "loyaltyHub.memberPerks.earnAndRedeem.learnMoreTermsLink": "https://www.gap.com/customer-service/gap-good-rewards?cid=1099008", "loyaltyHub.memberPerks.brandExclusives.header": "Brand Exclusives", "loyaltyHub.memberPerks.brandExclusives.description": "Explore available exclusives you get as a member with our family of brands (Gap, Old Navy, Banana Republic, Athleta)", "loyaltyHub.memberPerks.brandExclusives.learnMore.description": "Gap: Convert Gapcash to points\n\nBanana Republic: Free basic alterations on Banana Republic merchandise (Icon Members)\n\nBanana Republic Convert unused Banana Republic Factory StyleCash to points\n\nOld Navy: Convert unused Old Navy Super Cash to points", "loyaltyHub.memberPerks.convertExpiredTile.header": "Convert Expired Bonus Cash to Points", "loyaltyHub.memberPerks.convertExpiredTile.description": "Convert unused Super Cash or GapCash to points. Add Bonus Cash code to add points to your Rewards account", "loyaltyHub.memberPerks.convertPointsForm.description": "Convert unused Super Cash, GapCash, or StyleCash to points. Add code of unused Bonus Cash within 30 days of expiration to convert.", "loyaltyHub.memberPerks.convertPointsForm.button": "Convert Cash To Points", "loyaltyHub.memberPerks.convertPointsForm.successMessage": "{{points}} Points converted from Bonus Cash.", "loyaltyHub.landingPage.rewards.SubHeadline": "Spend your points towards rewards", "loyaltyHub.landingPage.memberPerks.SubHeadline": "All your member offers, benefits, and exclusive access", "loyaltyHub.landingPage.tierProgressTracker.core": "Spend ${{amountToSpendToNextTier}} by the end of the year or become a Cardmember to get all the benefits of Enthusiast.", "loyaltyHub.landingPage.tierProgressTracker.enthusiast": "Spend ${{amountToSpendToNextTier}} by the end of the year to get all the benefits of ", "loyaltyHub.landingPage.tierProgressTracker.icon": "As an Icon Member, you now have access to exclusive benefits. Enjoy!", "loyaltyHub.landingPage.tierProgressTracker.cardHolder.enthusiast": "Spend ${{amountToSpendToNextTier}} in our family of brands or earn {{pointsToSpendToNextTier}} points by the end of the year to get all the benefits of ", "loyaltyHub.landingPage.tierProgressTracker.cardHolder.icon": "As an Icon Member, you now have access to exclusive benefits. Enjoy!", "loyaltyHub.landingPage.warningMessage": "Membership information currently not available. Please refresh the page or try again later.", "loyaltyHub.memberPerks.gp.viewTermUrl": "https://www.gap.com/customer-service/gap-good-rewards?cid=1099008", "loyaltyHub.memberPerks.on.viewTermUrl": "https://oldnavy.gap.com/customerService/info.do?cid=1095422&mlink=5151,1,FTR_NavyistRewardsLogo", "loyaltyHub.memberPerks.br.viewTermUrl": "https://bananarepublic.gap.com/customerService/info.do?cid=1098875&sitecode=BRSSUNIFTD&mlink=55298,28882930,footer_ILP_explore_benefits", "loyaltyHub.memberPerks.at.viewTermUrl": "https://athleta.gap.com/browse/info.do?cid=1098761&sitecode=ATSSUNIFTDE&mlink=,,UNIFT_0531_LoyaltyLP", "loyaltyHub.memberPerks.gpfs.viewTermUrl": "https://www.gapfactory.com/customerService/info.do?cid=1099133&sitecode=GPFSUNIFTILPD&mlink=1037267,d58814bd-263f-417a-94a8-97d0bb6ad02b,UNIFOOTER_GGR_LP_CARD_ACQ", "loyaltyHub.memberPerks.brfs.viewTermUrl": "https://bananarepublicfactory.gapfactory.com/customerService/info.do?cid=1098825&mlink=55298,28882930,footer_ILP_explore_benefits", "loyaltyHub.memberPerks.birthdayTile.birthdayOffer": "Enjoy 15% off from Gap for your birthday!", "loyaltyHub.myMembership.member": "Member", "loyaltyHub.myMembership.perks": "Perks", "loyaltyHub.rewardDetailPage.headerNav": "Rewards Marketplace", "loyaltyHub.rewardDetailPage.redeem": "Redeem", "loyaltyHub.rewardDetailPage.redeemed": "Redeemed", "loyaltyHub.rewardDetailPage.soldout": "Sold Out", "loyaltyHub.rewardDetailPage.available": "Available:", "loyaltyHub.rewardDetailPage.genericErrorMessage": "An error occurred. Please try again.", "loyaltyHub.rewardDetailPage.donations.dropdownAmount": "${{amount}}", "loyaltyHub.rewardDetailPage.donations.dropdownPoints": "({{points}} points)", "loyaltyHub.rewardDetailPage.donations.header": "Donate: ", "loyaltyHub.rewardDetailPage.donations.donationValue": "Donation Value ", "loyaltyHub.rewardDetailPage.donations.pointsNeeded": "Points Needed ", "loyaltyHub.rewardDetailPage.donations.startingAmount": "Starting at $10", "loyaltyHub.rewardDetailPage.donations.startingPoints": "Starting at 200", "loyaltyHub.rewardDetailPage.donations.100Points": "100 pts", "loyaltyHub.rewardDetailPage.donations.ReadyToDonate": "Ready to Donate", "loyaltyHub.rewardDetailPage.donations.modalDescription": "Donating your points for Marketplace Rewards is final and can’t be undone or refunded.", "loyaltyHub.rewardDetailPage.donations.confirm": "Confirm", "loyaltyHub.rewardDetailPage.donations.cancel": "Cancel", "loyaltyHub.rewardDetailPage.donations.successMessage": "Your points have been donated.", "loyaltyHub.rewardDetailPage.donations.successTitle": "You just made a difference! 🙌🏼", "loyaltyHub.rewardDetailPage.donations.done": "Done", "loyaltyHub.rewardDetailPage.donations.donateButton": "Donate", "loyaltyHub.rewardDetailPage.donations.selectAnAmount": "Select an amount", "loyaltyHub.rewardDetailPage.donations.donateAgainButton": "Donate Again", "loyaltyHub.rewardDetailPage.donations.warningMessage": "An error occurred. Please try again.", "loyaltyHub.rewardDetailPage.donations.snackbarMessage": "You’ve successfully donated your points!", "loyaltyHub.rewardDetailPage.pwp.successModalHeader": "Added to bag! 🎉", "loyaltyHub.rewardDetailPage.pwp.successModalDesc": "Go to bag then checkout as usual. A $0.01 processing fee and regular shipping fees may apply. Marketplace rewards can’t be returned or refunded.", "loyaltyHub.rewardDetailPage.pwp.successModalButton1": "Keep exploring", "loyaltyHub.rewardDetailPage.pwp.successModalButton2": "View bag", "loyaltyHub.rewardDetailPage.pwp.notEnoughModalHeader": "Keep earning!", "loyaltyHub.rewardDetailPage.pwp.notEnoughModalDesc": "You need more points to get this reward.  Earn points by shopping, then come back to enter!", "loyaltyHub.rewardDetailPage.pwp.notEnoughModalButton2": "Go back", "loyaltyHub.myMembership.youAreOnYourWay": "You are on your way to", "loyaltyHub.myMembership.tierProgressTrackerText": "Spend ${{amountToSpendToNextTier}} points by the end of the year to get all the benefits of ", "loyaltyHub.myMembership.amountSpent": "You've Spent", "loyaltyHub.myMembership.copyright_core_noProgress": "Spend {{amount}} by the end of the year or become a Cardmember to get all the ", "loyaltyHub.myMembership.copyright_core_progress": "Spend {{amount}} by the end of the year or become a Cardmember to get all the ", "loyaltyHub.myMembership.copyright_enthusiast_cardHolder": "Spend {{amount}} in our family of brands or earn {{points}} points by the end of the year to get all the ", "loyaltyHub.myMembership.copyright_enthusiast_noCardHolder": "Spend {{amount}} by the end of the year to get all the ", "loyaltyHub.myMembership.copyright_icon": "As an Icon Member, you now have access to exclusive benefits. Enjoy!", "loyaltyHub.myMembership.or": "OR", "loyaltyHub.myMembership.shopNow": "Start Shopping", "loyaltyHub.myMembership.earned": "You’ve Earned", "loyaltyHub.myMembership.manageCard": "Manage and Pay My Gap Inc. Rewards Credit Card", "loyaltyHub.myMembership.footerText.core": "Enjoy your benefits until you’re upgraded. Levels are upgraded based on dollars spent**", "loyaltyHub.myMembership.footerText.enthusiast": "Enjoy your current benefits until {{date}} or until you're upgraded. Levels are upgraded based on dollars spent.**", "loyaltyHub.myMembership.footerText.cardHolderEnthusiast": "Enjoy your benefits until you’re upgraded. Eligibility for level upgrade subject to change based on account activity.**", "loyaltyHub.myMembership.footerText.icon": "Enjoy your current benefits until {{date}} or until you're upgraded. Eligibility for level upgrade subject to change based on account activity.**", "loyaltyHub.myMembership.startShopping.copyright_next_core": "benefits of Enthusiast", "loyaltyHub.myMembership.startShopping.copyright_next_enthusiast": "benefits of Icon", "loyaltyHub.myMembership.startShopping.applyCard_gp": "Apply for a Gap Good Rewards Credit Card", "loyaltyHub.myMembership.startShopping.applyCard_gap": "Apply for a Gap Good Rewards Credit Card", "loyaltyHub.myMembership.startShopping.applyCard_on": "Apply for a Navyist Rewards Credit Card", "loyaltyHub.myMembership.startShopping.applyCard_br": "Apply for a Banana Republic Rewards Credit Card", "loyaltyHub.myMembership.startShopping.applyCard_at": "Apply for an Athleta Rewards Credit Card", "loyaltyHub.myMembership.startShopping.applyCard_gpfs": "Apply for a Gap Good Rewards Credit Card", "loyaltyHub.myMembership.startShopping.applyCard_gapfs": "Apply for a Gap Good Rewards Credit Card", "loyaltyHub.myMembership.startShopping.applyCard_brfs": "Apply for a Banana Republic Rewards Credit Card", "loyaltyHub.myMembership.startShopping.applyCard_CORE": " and get all the benefits of Enthusiast as soon as you’re approved—plus earn 5 points per $1 spent at our family of brands with your new card!*", "loyaltyHub.myMembership.startShopping.applyCard_ENTHUSIAST": " and earn 5 points per $1 spent at our family of brands with your new card!*", "loyaltyHub.myMembership.startShopping.applyCard_ICON": " and earn 5 points per $1 spent at our family of brands with your new card!*", "loyaltyHub.myMembership.featuredBenefits.header": "Your Featured Benefits", "loyaltyHub.myMembership.featuredBenefits.earnPoints": " 1 Point Per $1 Spent", "loyaltyHub.myMembership.featuredBenefits.earnPointsDescription1": "You earn across our family of brands.", "loyaltyHub.myMembership.featuredBenefits.earnPointsDescription2": "100 points = $1 in rewards", "loyaltyHub.myMembership.featuredBenefits.freeShipping": "Free Shipping", "loyaltyHub.myMembership.featuredBenefits.freeShippingDescription": "Online orders get to you fast. Get free 3-5 day shipping on all orders $50+.", "loyaltyHub.myMembership.featuredBenefits.birthdayBonus": "Birthday Bonus", "loyaltyHub.myMembership.featuredBenefits.birthdayBonusDescription": "Celebrate your birthday with an extra perk—it’s our treat!", "loyaltyHub.myMembership.pointsActivity.summaryText": "Will include up to 3 months of points activity", "loyaltyHub.myMembership.pointsActivity.zeroPoints": "Check back to track points as you earn them.", "loyaltyHub.myMembership.pointsActivity.paginator.page": "page", "loyaltyHub.myMembership.pointsActivity.paginator.of": "of", "loyaltyHub.myMembership.pointsActivity.paginator.left_arrow_aria_label": "Next Page", "loyaltyHub.myMembership.pointsActivity.paginator.right_arrow_aria_label": "Previous Page", "loyaltyHub.myMembership.pointsActivity.paginator.nav_aria_label": "Pagination Navigation", "loyaltyHub.myMembership.pointsActivity.activity": "activity", "loyaltyHub.myMembership.pointsActivity.points": "points", "loyaltyHub.myMembership.pointsActivity.footer.text1": "See ", "loyaltyHub.myMembership.pointsActivity.footer.text2": "Details", "loyaltyHub.myMembership.pointsActivity.footer.text3": " and Rewards Program ", "loyaltyHub.myMembership.pointsActivity.footer.text4": "Terms and Conditions", "loyaltyHub.myMembership.pointsActivity.youHave": "You have", "loyaltyHub.myMembership.pointsActivity.pendingPoints": "Excludes {{points}} pending points **", "loyaltyHub.myMembership.pointsActivity.inRewards": "in rewards", "loyaltyHub.myMembership.pointsActivity.earnPoints": "Earn points for every $1 spent", "loyaltyHub.myMembership.pointsActivity.earnPointsDescription": "100 points = $1 in rewards", "loyaltyHub.myMembership.pointsActivity.viewAndRedeem": "View and redeem all rewards in checkout**", "loyaltyHub.myMembership.pointsActivity.viewAll": "View All", "loyaltyHub.myMembership.pointsActivity.warningMessage": "Points activity not available . Please refresh the page or try again later.", "loyaltyHub.myMembership.pointsSummary.warningMessage": "Points amount not available . Please refresh the page or try again later.", "loyaltyHub.myMembership.tierProgress.warningMessage": "Member tier progress not available. Please refresh the page or try again later.", "loyaltyHub.myMembership.pointsActivityTierProgress.warningMessage": "Information regarding your points and tier status is unavailable . Please refresh the page or try again later.", "loyaltyHub.rewardDetailPage.sweepstakes.modalHeader": "Ready to redeem?", "loyaltyHub.rewardDetailPage.sweepstakes.modalDesc": "Redeeming your points for Marketplace Rewards is final and can’t be undone or refunded.", "loyaltyHub.rewardDetailPage.sweepstakes.confirm": "Confirm", "loyaltyHub.rewardDetailPage.sweepstakes.cancel": "Cancel", "loyaltyHub.rewardDetailPage.sweepstakes.successMessage.modalHeader": "Your points have been redeemed! 🎉", "loyaltyHub.rewardDetailPage.sweepstakes.successMessage.modalDesc": "You must complete the next step to finalize your entry."}