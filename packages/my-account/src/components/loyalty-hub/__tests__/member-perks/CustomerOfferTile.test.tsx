import { render, screen } from 'test-utils';
import LocalizationProvider from '@sitewide/providers/localization';
import { getLocaleSpecificTranslations } from '@sitewide/providers/localization/fetchTranslations';
import { LoyaltyHubProvider } from '../../../../context/loyalty-hub/LoyaltyHubContext';
import { CustomerOfferTile, CustomerOfferTileProps } from '../../member-perks/CustomerOfferTile';

const initProps: CustomerOfferTileProps = {
  promoId: '1077123',
  promoName: 'Test Promo',
  promoDescription: 'This is a promo description',
  promoCode: 'D7JP2L34HB61',
  barCode: '9X1D7JP2L34HB61K',
  brand: 'GP',
  startDate: '2025-07-24T00:00:00-07:00',
  endDate: '2025-12-31T23:59:00-08:00',
  offerType: 'Birthday',
  amount: 15,
  discountType: 'Percent off',
  legalTerms: 'Code valid for 15% off your purchase for one-day only...',
  tier: 'CORE',
  daysToRedeem: 0,
  expiresSoon: false,
  isReqInFlight: false,
  setIsReqInFlight: jest.fn(),
};

const renderComponent = (props: CustomerOfferTileProps) => {
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  return render(
    <LocalizationProvider locale='en-US' translations={translations} market='us'>
      <LoyaltyHubProvider isLoyaltyHubEnabled={true}>
        <CustomerOfferTile {...props} />
      </LoyaltyHubProvider>
    </LocalizationProvider>
  );
};

describe('CustomerOfferTile component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('renders CustomerOfferTile component properly', () => {
    renderComponent(initProps);
    expect(screen.getByText('Icon Members Only')).toBeInTheDocument();
    expect(screen.getByText('Test Promo')).toBeInTheDocument();
    expect(screen.getByText('This is a promo description')).toBeInTheDocument();
  });

  it('do not render icon-only header when user is tier ICON', () => {
    const modifiedProps = { ...initProps, tier: 'ICON' };
    renderComponent(modifiedProps);
    expect(screen.queryByText('Icon Members Only')).not.toBeInTheDocument();
    expect(screen.getByText('This is a promo description')).toBeInTheDocument();
  });
});
