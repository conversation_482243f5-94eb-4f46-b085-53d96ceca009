import { render, screen } from 'test-utils';
import LocalizationProvider from '@sitewide/providers/localization';
import { getLocaleSpecificTranslations } from '@sitewide/providers/localization/fetchTranslations';
import { BirthdayTile, BirthdayTileProps } from '../../member-perks/BirthdayTile';

const initProps: BirthdayTileProps = {
  birthdayType: 'promo',
  dob: 'April',
  promoId: '1077123',
  promoName: 'Birthday Discount',
  promoDescription: 'Get and additional 15% off from Gap for your birthday',
  promoCode: 'D7JP2L34HB61',
  barCode: '9X1D7JP2L34HB61K',
  brand: 'GP',
  startDate: '2025-07-24T00:00:00-07:00',
  endDate: '2025-12-31T23:59:00-08:00',
  offerType: 'Birthday',
  amount: 15,
  discountType: 'Percent off',
};

const renderComponent = (props: BirthdayTileProps) => {
  const translations = getLocaleSpecificTranslations('en_US', ['profileui']);
  return render(
    <LocalizationProvider locale='en-US' translations={translations} market='us'>
      <BirthdayTile {...props} />
    </LocalizationProvider>
  );
};

describe('BirthdayTile component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('renders BirthdayTile component properly', () => {
    renderComponent(initProps);
    expect(screen.getByText('Birthday Discount - Gap')).toBeInTheDocument();
    expect(screen.getByText('Get and additional 15% off from Gap for your birthday')).toBeInTheDocument();
  });
});
it('renders Add Birthday button and navigates to /my-account/name-and-email when clicked', () => {
  const props = {
    ...initProps,
    birthdayType: 'missing',
  };
  renderComponent(props);
  const addBirthdayButton = screen.getByText('Add Birthday');
  expect(addBirthdayButton).toBeInTheDocument();
  expect(addBirthdayButton).toHaveAttribute('href', '/my-account/name-and-email');
});
