import React, { useState, useEffect } from 'react';
import { useLocalize } from '@sitewide/providers/localization';
import { usePageContext } from '@sitewide/hooks/usePageContext';
import { Button } from '@ecom-next/core/migration/button';
import * as utils from '../../../utils/loyalty-hub/loyaltyHubUtils';
import { useLoyaltyHub } from '../../../hooks/useLoyaltyHub';
import { triggerLoyaltyHubApplyPromoReq } from '../../../requests/loyalty-hub/loyaltyHubRequests';
import { LearnMoreModal } from './LearnMoreModal';
import { ViewCodeModalDrawer } from './ViewCodeModalDrawer';

export type CustomerOfferTileProps = {
  amount: number;
  barCode: string;
  brand: string;
  daysToRedeem: number;
  discountType: string;
  endDate: string;
  expiresSoon: boolean;
  isReqInFlight: boolean;
  legalTerms: string;
  offerType: string;
  promoCode: string;
  promoDescription: string;
  promoId: string;
  promoName: string;
  setIsReqInFlight: (value: boolean) => void;
  startDate: string;
  tier: string;
};

export const CustomerOfferTile = (props: CustomerOfferTileProps) => {
  const {
    promoName,
    promoDescription,
    startDate,
    endDate,
    tier,
    daysToRedeem,
    expiresSoon,
    legalTerms,
    promoCode,
    barCode,
    brand,
    isReqInFlight,
    setIsReqInFlight,
  } = props;
  const { localize } = useLocalize();
  const { market, ecomApiBaseUrl } = usePageContext();
  const { loyaltyHubState, loyaltyHubDispatch } = useLoyaltyHub();
  const { loyaltyHubApplyPromoReq, appliedPromos } = loyaltyHubState;

  // setup variables
  const isIconTier = tier?.toUpperCase() === 'ICON';
  const isAvailableForUse = utils.checkAvailableForUse(startDate, endDate);
  const showError = loyaltyHubApplyPromoReq.isFail && appliedPromos.promoCode === promoCode;

  // setup local state
  const [isActive, setIsActive] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showLearnMoreModal, toggleLearnMoreModal] = useState(false);
  const [showViewCodeModal, toggleViewCodeModal] = React.useState(false);

  useEffect(() => {
    if (loyaltyHubApplyPromoReq.isSuccess && appliedPromos.promoCode === promoCode) {
      setIsActive(true);
    } else if (loyaltyHubApplyPromoReq.isFail && appliedPromos.promoCode === promoCode) {
      setIsActive(false);
    }
    if ((loyaltyHubApplyPromoReq.isSuccess || loyaltyHubApplyPromoReq.isFail) && appliedPromos.promoCode === promoCode) {
      setIsLoading(false);
    }
  }, [loyaltyHubApplyPromoReq, appliedPromos.promoCode]);

  // setup elements
  const renderIconOnlyHeader = () => {
    return !isIconTier ? (
      <h4 className='mb-4 font-semibold leading-none text-[0.875ren]'>{localize('loyaltyHub.memberPerks.customerOfferTile.iconMembersOnly')}</h4>
    ) : (
      <></>
    );
  };
  const renderDateRange = () => {
    const dateDescription = utils.setupDateRangeText(startDate, endDate, 'customerOfferTile', localize);
    return !expiresSoon ? <div className='mb-4 text-[0.875rem] leading-none text-[#666]'>{dateDescription}</div> : <></>;
  };
  const renderExpiresSoonMessage = (): JSX.Element => {
    const expiresDescription =
      daysToRedeem === 1
        ? localize('loyaltyHub.memberPerks.customerOfferTile.expiresDateSingular')
        : localize('loyaltyHub.memberPerks.customerOfferTile.expiresDatePlural', { num: daysToRedeem });
    return expiresSoon ? <div className='mb-4 text-[0.875rem] leading-none text-[red]'>{expiresDescription}</div> : <></>;
  };
  const renderViewCodeButton = () => {
    return isActive && isIconTier ? (
      <div className='flex flex-row'>
        <Button
          className='leading-none! font-crossbrand m-0 block h-[2.25rem] w-auto max-w-[120px] cursor-pointer items-center justify-center overflow-hidden text-ellipsis whitespace-nowrap rounded border border-solid border-black bg-transparent !px-[0.75rem] !py-[0.375rem] text-center align-middle text-[0.875rem] font-normal tracking-[0.03125rem] text-black hover:!border hover:border-[#4780ae] hover:bg-white hover:text-[#4780ae]'
          kind='secondary'
          onClick={() => {
            toggleViewCodeModal(true);
          }}
        >
          {localize('loyaltyHub.memberPerks.viewCode')}
        </Button>
      </div>
    ) : (
      <></>
    );
  };
  const renderActivateButton = () => {
    return isAvailableForUse && !isActive && isIconTier ? (
      <div className='flex flex-row'>
        <Button
          className='leading-none! font-crossbrand m-0 block h-[2.25rem] w-auto max-w-[120px] cursor-pointer items-center justify-center overflow-hidden text-ellipsis whitespace-nowrap rounded border border-solid border-black bg-transparent !px-[0.75rem] !py-[0.375rem] text-center align-middle text-[0.875rem] font-normal tracking-[0.03125rem] text-black hover:!border hover:border-[#4780ae] hover:bg-white hover:text-[#4780ae]'
          kind='secondary'
          loadingAnimationStatus={isLoading}
          isDisabled={isReqInFlight}
          onClick={() => {
            if (!isReqInFlight && !isLoading) {
              setIsLoading(true);
              setIsReqInFlight(true);
              triggerLoyaltyHubApplyPromoReq({
                loyaltyHubDispatch,
                ecomApiBaseUrl,
                brand,
                market,
                promoCode,
              });
            }
          }}
        >
          {localize('loyaltyHub.memberPerks.activate')}
        </Button>
      </div>
    ) : (
      <></>
    );
  };
  const renderLearnMoreButton = () => {
    return !isIconTier || (isIconTier && !isAvailableForUse && !isActive) ? (
      <div className='flex flex-row'>
        <Button
          className='leading-none! font-crossbrand m-0 block h-auto w-auto max-w-[120px] !p-0 text-center align-middle text-[0.875rem] font-normal tracking-[0.03125rem] text-black'
          kind='flat'
          onClick={() => toggleLearnMoreModal(true)}
        >
          {localize('loyaltyHub.memberPerks.learnMore')}
        </Button>
      </div>
    ) : (
      <></>
    );
  };
  const renderLearnMoreModal = (): JSX.Element => {
    return showLearnMoreModal ? (
      <LearnMoreModal isOpen={showLearnMoreModal} toggleModal={toggleLearnMoreModal} description={legalTerms} titleText={promoName} brand={brand} />
    ) : (
      <></>
    );
  };
  const renderViewCodeModal = (): JSX.Element => {
    return showViewCodeModal ? (
      <ViewCodeModalDrawer
        barCode={barCode}
        isOpen={showViewCodeModal}
        toggleModal={toggleViewCodeModal}
        expirationDate={endDate}
        promoCode={promoCode}
        promoDescription={promoDescription}
        title={promoName}
      />
    ) : (
      <></>
    );
  };
  const renderErrorMessage = (): React.ReactElement => {
    return showError ? (
      <p className='mt-4 text-sm font-normal leading-[1.125rem] text-[red]'>{localize('loyaltyHub.memberPerks.customerOfferTile.errorMessage')}</p>
    ) : (
      <></>
    );
  };

  return (
    <>
      <div className='mb-4 box-border flex flex-col rounded-[0.25rem] p-4 shadow-[0_0.125rem_0.375rem_rgba(0,0,0,0.12),0_0_0.125rem_rgba(0,0,0,0.15),0_0.0625rem_0.125rem_rgba(0,0,0,0.12)] '>
        {renderIconOnlyHeader()}
        <h3 className='mb-4 text-[1.25rem] font-semibold leading-none'>{promoName}</h3>
        {renderDateRange()}
        {renderExpiresSoonMessage()}
        <p className='leadig-none mb-4 text-[1rem] font-normal text-[#333]'>{promoDescription}</p>
        {renderViewCodeButton()}
        {renderActivateButton()}
        {renderLearnMoreButton()}
        {renderErrorMessage()}
      </div>
      {renderLearnMoreModal()}
      {renderViewCodeModal()}
    </>
  );
};
