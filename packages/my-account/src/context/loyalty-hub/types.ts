/* eslint-disable typescript-sort-keys/interface, typescript-sort-keys/string-enum */
import type { Dispatch } from 'react';
import type { Market, Locale } from '@ecom-next/utils/server';
import * as ActionTypes from '../../constants/loyalty-hub/loyaltyHubActionTypes';
import { LoyaltyHubDonationsPayload, LoyaltyHubSweepstakesPayload } from '../../requests/loyalty-hub/loyaltyHubRequests';

export type Tiers = '' | 'CORE' | 'ENTHUSIAST' | 'ICON';

export type LegalTerms = {
  text?: string;
  legal_terms?: string;
  locale_code: string;
}[];

export type PointsToValueList = {
  amount: number;
  points: number;
};

type User = {
  dob: string;
  tier: Tiers;
  points: number;
  nextTier: Tiers;
  loyaltyId: string;
  phoneNumber: string;
  pendingPoints: number;
  amountSpent: number;
  rewardsBalance: string;
  isCardHolder: boolean;
  isBirthdayMonth: boolean;
  amountToSpendToNextTier: number;
  pointsToSpendToNextTier: number;
  currentTierExpireDate: string;
  pointsAmount: number;
};

export type RewardInput = {
  brand: string;
  channel: string;
  reward_id: string;
  reward_type: string;
  reward_program_code: string;
  program_start_date: string;
  program_end_date: string;
  reward_name: string;
  reward_description: string;
  legal_terms: string;
  points_value: number;
  partner_name: string;
  navigation_rewards_url: string;
  navigation_marketplace_url: string;
  image_list_page_url: string;
  image_details_page_url: string;
  customer_reward_usage_maximum: number;
  points_value_display: string;
  user_confirmation_display_text: string;
  badges: Array<string>;
  claimable_by_customer: string;
  claim_disabled_reason: string;
  reward_activity_id_to_claim: string;
  redeemed_count: number;
  inventory: number;
  rewards_program_bag: {
    reward_activity_id: string;
    reward_activity_type: string;
    reward_activity_status: string;
    reward_activity_date: string; // ISO 8601 format
  }[];
};

export type RewardsItem = {
  brand: string;
  channel: string;
  rewardId: string;
  rewardType: string;
  rewardProgramCode: string;
  programStartDate: string;
  programEndDate: string;
  rewardName: string;
  rewardDescription: string;
  legalTerms: string;
  pointsValue: number;
  partnerName: string;
  navigationRewardsUrl: string;
  navigationMarketplaceUrl: string;
  imageListPageUrl: string;
  imageDetailsPageUrl: string;
  customerRewardUsageMaximum: number;
  pointsValueDisplay: string;
  userConfirmationDisplayText: string;
  badges: string[];
  claimableByCustomer: string;
  claimDisabledReason: string;
  rewardActivityIdToClaim: string;
  redeemedCount: number;
  inventory: number;
  rewardsProgramBag: {
    rewardActivityId: string;
    rewardActivityType: string;
    rewardActivityStatus: string;
    rewardActivityDate: string;
  }[];
};

export type PendingPoints = {
  pending_points: number;
  active_points: number;
  active_points_amount: number;
};

export type PointsActivityInput = {
  transaction_date: string;
  transaction_desc: string;
  transaction_number: string;
  points: { status: string; value: number }[];
};

export type PointsActivityOutput = {
  date: string; // ISO 8601 format
  status: string;
  points: number;
  transactionId: string;
  transactionDescription: string;
};

export type CustomerOfferItem = {
  promo_id: string;
  promo_name: string;
  promo_description: string;
  promo_code: string;
  barcode: string;
  market: string;
  applicable_brands: string[];
  assignment_start_date: string;
  assignment_expiration_date: string;
  awards: {
    type: string;
    amount: number;
  }[];
  legal_terms: LegalTerms;
  offerType: string;
  days_to_redeem: number | null;
};

export type CustomerOfferInput = {
  offer_type: string;
  offer_value: CustomerOfferItem[];
};

export type CustomerOfferOutput = {
  promoId: string;
  promoName: string;
  promoDescription: string;
  promoCode: string;
  barCode: string;
  market: string;
  brand: string;
  startDate: string; // ISO 8601 format format
  endDate: string; // ISO 8601 format format
  offerType: string;
  amount: number;
  discountType: string;
  legalTerms: string;
  daysToRedeem: number;
  expiresSoon: boolean;
};

export type BounceBackOfferInput = {
  brand_code: string;
  burn_period: {
    days_to_redeem: number;
    start_datetime: string; // ISO 8601 format
    end_datetime: string; // ISO 8601 format
  };
  discount: {
    type: string;
    amount: number;
    bar_code: string;
    promotion_code: string;
  };
  promotion_messages: {
    legal_terms: string;
    locale_code: Locale;
    marketing_description: string;
  }[];
};

export type BounceBackOfferOutput = {
  amount: number;
  barCode: string;
  brand: string;
  endDate: string;
  daysToRedeem: number;
  legalTerms: string;
  promotionCode: string;
  startDate: string;
  discountType: string;
  expiresSoon: boolean;
};

export type Meta = {
  overall_success: boolean;
  failedServices: string[];
};

type LoyaltyHubLandingRes = {
  loyalty_id: string;
  customer_info: {
    customer_tier: Tiers;
    is_card_holder: 'Y' | 'N';
    date_of_birth?: string;
    phone_number?: string;
  };
  tier_progress_info: {
    current_points: number;
    goal_spend_amount: number;
    current_spend_amount: number;
    current_tier_expire_date: string;
    next_tier: Tiers;
    goal_points: number;
  };
  meta: Meta;
};

type LoyaltyHubMemberPerksRes = {
  locale: Locale;
  market: Market;
  loyalty_id: string;
  customer_info: {
    customer_tier: Tiers;
    is_card_holder: 'Y' | 'N';
    date_of_birth?: string;
    phone_number?: string;
  };
  customer_offers: CustomerOfferInput[];
  bounceback_offers: BounceBackOfferInput[];
  meta: Meta;
};

type LoyaltyHubMyMembershipRes = Omit<LoyaltyHubMemberPerksRes, 'customer_offers' | 'bounceback_offers'> & {
  tier_progress_info: {
    goal_points: number;
    current_points: number;
    goal_spend_amount: number;
    current_spend_amount: number;
    current_tier_expire_date: string;
    next_tier: Tiers;
  };
  points_activity: PointsActivityInput[];
  points: PendingPoints;
  meta: Meta;
};

type LoyaltyHubRewardsMarketplaceRes = Omit<LoyaltyHubMemberPerksRes, 'customer_offers' | 'bounceback_offers'> & {
  tier_progress_info: {
    goal_points: number;
    current_points: number;
    goal_spend_amount: number;
    current_spend_amount: number;
    current_tier_expire_date: string;
    next_tier: Tiers;
  };
  marketplace_rewards: RewardInput[];
};

export type LoyaltyHubReqPayload = LoyaltyHubLandingRes | LoyaltyHubMemberPerksRes | LoyaltyHubMyMembershipRes | LoyaltyHubRewardsMarketplaceRes;

export type LoyaltyHubConvertPointsPayload = {
  response: Record<string, unknown>;
};

export type LoyaltyHubApplyPromoPayload = {
  promoCode: string;
};
export type OutOfStockItem = {
  productName: string;
  sku: string;
};

export type LoyaltyHubPWPAddToBagRes = {
  shoppingBagId?: string;
  outOfStockItems?: OutOfStockItem[];
  sku: string;
};

export type State = {
  loyaltyHubLandingReq: {
    isFail: boolean;
    isSuccess: boolean;
    isTierProgressFail: boolean;
    isCardsFail: boolean;
  };
  loyaltyHubMemberPerksReq: {
    isSuccess: boolean;
    isFail: boolean;
    isCardsFail: boolean;
    isBounceBackOffersFail: boolean;
    isCustomerOffersFail: boolean;
  };
  loyaltyHubMyMembershipReq: {
    isSuccess: boolean;
    isFail: boolean;
    isCardsFail: boolean;
    isTierProgressFail: boolean;
    isPointsActivityFail: boolean;
    isPendingPointsFail: boolean;
  };
  loyaltyHubRewardsMarketplaceReq: {
    isSuccess: boolean;
    isFail: boolean;
  };
  loyaltyHubAddToBagReq: {
    isSuccess: boolean;
    isFail: boolean;
  };
  user: User;
  rewards: RewardsItem[];
  offers: {
    customerOffers: CustomerOfferOutput[];
    bounceBackOffers: BounceBackOfferOutput[];
  };
  pointsActivity: PointsActivityOutput[];
  appliedPromos: {
    promoCode: string;
  };
  convertedPoints: {
    points: number;
  };
  pwpAddToBag: {
    isOutOfStock: boolean;
  };
  loyaltyHubConvertPointsReq: {
    isSuccess: boolean;
    isFail: boolean;
  };
  loyaltyHubApplyPromoReq: {
    isSuccess: boolean;
    isFail: boolean;
  };
  rewardsMarketplaceDonationsReq: {
    isSuccess: boolean;
    isFail: boolean;
  };
  rewardsMarketplaceSweepstakesReq: {
    isSuccess: boolean;
    isFail: boolean;
  };
};

export type Action =
  | { type: typeof ActionTypes.POST_LOYALTY_HUB_LANDING_SUCCESS; payload: LoyaltyHubLandingRes }
  | { type: typeof ActionTypes.POST_LOYALTY_HUB_LANDING_FAIL }
  | { type: typeof ActionTypes.POST_LOYALTY_HUB_MEMBER_PERKS_SUCCESS; payload: LoyaltyHubMemberPerksRes }
  | { type: typeof ActionTypes.POST_LOYALTY_HUB_MEMBER_PERKS_FAIL }
  | { type: typeof ActionTypes.POST_LOYALTY_HUB_MY_MEMBERSHIP_SUCCESS; payload: LoyaltyHubMyMembershipRes }
  | { type: typeof ActionTypes.POST_LOYALTY_HUB_MY_MEMBERSHIP_FAIL }
  | { type: typeof ActionTypes.POST_LOYALTY_HUB_REWARDS_MARKETPLACE_SUCCESS; payload: LoyaltyHubRewardsMarketplaceRes }
  | { type: typeof ActionTypes.POST_LOYALTY_HUB_REWARDS_MARKETPLACE_FAIL }
  | { type: typeof ActionTypes.POST_LOYALTY_HUB_CONVERT_POINTS_SUCCESS; payload: LoyaltyHubConvertPointsPayload }
  | { type: typeof ActionTypes.POST_LOYALTY_HUB_CONVERT_POINTS_FAIL }
  | { type: typeof ActionTypes.PUT_LOYALTY_HUB_APPLY_PROMO_SUCCESS; payload: LoyaltyHubApplyPromoPayload }
  | { type: typeof ActionTypes.PUT_LOYALTY_HUB_APPLY_PROMO_FAIL; payload: LoyaltyHubApplyPromoPayload }
  | { type: typeof ActionTypes.POST_LOYALTY_HUB_ADD_TO_BAG_SUCCESS; payload: LoyaltyHubPWPAddToBagRes }
  | { type: typeof ActionTypes.POST_LOYALTY_HUB_ADD_TO_BAG_FAIL; payload: LoyaltyHubPWPAddToBagRes }
  | { type: typeof ActionTypes.POST_REWARDS_MARKETPLACE_DONATIONS_SUCCESS; payload: LoyaltyHubDonationsPayload }
  | { type: typeof ActionTypes.POST_REWARDS_MARKETPLACE_DONATIONS_FAIL }
  | { type: typeof ActionTypes.RESET_REWARDS_MARKETPLACE_DETAILS_PAGE_REQS }
  | { type: typeof ActionTypes.POST_REWARDS_MARKETPLACE_SWEEPSTAKES_SUCCESS; payload: LoyaltyHubSweepstakesPayload }
  | { type: typeof ActionTypes.POST_REWARDS_MARKETPLACE_SWEEPSTAKES_FAIL }
  | { type: typeof ActionTypes.RESET_LOYALTY_HUB_REQS; payload: 'landing' | 'memberPerks' | 'myMembership' | 'rewardsMarketplace' }
  | { type: typeof ActionTypes.RESET_LOYALTY_HUB_STATE };

export type ContextProps = {
  isLoyaltyHubEnabled: boolean;
  loyaltyHubState: State;
  loyaltyHubDispatch: Dispatch<Action>;
};
