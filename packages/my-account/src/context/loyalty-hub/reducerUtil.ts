import { DateTime } from 'luxon';
import type { Locale } from '@ecom-next/utils/server';
import hatImg from '../../assets/rewards-marketplace/hat.png'; // temporary placeholder img until API is ready
import type {
  CustomerOfferInput,
  CustomerOfferOutput,
  BounceBackOfferInput,
  BounceBackOfferOutput,
  LegalTerms,
  RewardInput,
  RewardsItem,
  Meta,
  OutOfStockItem,
  State,
  LoyaltyHubReqPayload,
  Tiers,
} from './types';

type Market = keyof typeof BRAND_CODE_MAP;
type BrandCodeMap = (typeof BRAND_CODE_MAP)[Market];
type BrandCode = keyof BrandCodeMap;
type BrandName = BrandCodeMap[BrandCode];

const BRAND_CODE_MAP = {
  us: {
    '1': 'gp',
    '2': 'br',
    '3': 'on',
    '10': 'at',
    '5': 'gapfs',
    '34': 'gapfs',
    '6': 'brfs',
    '35': 'brfs',
  },
  ca: {
    '1': 'gp',
    '7': 'gp',
    '2': 'br',
    '8': 'br',
    '3': 'on',
    '9': 'on',
    '10': 'at',
    '39': 'at',
    '5': 'gapfs',
    '52': 'gapfs',
    '6': 'brfs',
    '50': 'brfs',
  },
} as const;

export const getBrandFromBrandCode = (brand_code: string, market: Market): BrandName | '' => {
  const brand = BRAND_CODE_MAP[market][brand_code as keyof (typeof BRAND_CODE_MAP)[typeof market]];
  return brand ?? '';
};

export const isExpiringSoon = (expirationDate: string): boolean => {
  const expiresAt = DateTime.fromISO(expirationDate);
  if (!expiresAt.isValid) return false;
  const now = DateTime.local();
  const diffInDays = expiresAt.diff(now, 'days').days;
  return diffInDays > 0 && diffInDays <= 30;
};

export const isBirthdayMonth = (dob: string): boolean => {
  if (!dob) return false;

  const dobParts = dob.split('-');
  if (dobParts.length === 2) {
    const month = parseInt(dobParts[0], 10);
    const currentMonth = new Date().getMonth() + 1;
    return month === currentMonth;
  }

  return false;
};

export const extractLegalTerms = (legalTerms: LegalTerms, locale: Locale): string => {
  const term = legalTerms.find(term => term.locale_code === locale);
  return term?.text || term?.legal_terms || '';
};

export const checkFailedServicesError = (type: string, meta: Meta): boolean => {
  let results = false;
  if (type && meta?.overall_success && Array.isArray(meta?.failedServices)) {
    results = meta.failedServices.includes(type);
  }
  return results;
};

export const isOutOfStock = (outOfStockItems?: OutOfStockItem[], sku?: string): boolean => {
  if (!Array.isArray(outOfStockItems) || outOfStockItems?.length === 0) return false;
  if (sku) {
    return outOfStockItems?.some(item => item?.sku === sku);
  }
  return false;
};

export const extractUserData = (state: State, payload: LoyaltyHubReqPayload) => {
  return {
    ...(state?.user || {}),
    dob: payload?.customer_info?.date_of_birth || '',
    tier: (payload?.customer_info?.customer_tier as Tiers) ?? '',
    points: 'points' in payload ? payload?.points?.active_points || 0 : 0,
    pointsAmount: 'points' in payload ? payload?.points?.active_points_amount || 0 : 0,
    pendingPoints: 'points' in payload ? payload?.points?.pending_points || 0 : 0,
    nextTier: 'tier_progress_info' in payload ? (payload?.tier_progress_info?.next_tier as Tiers) ?? '' : '',
    loyaltyId: payload?.loyalty_id || '',
    phoneNumber: payload?.customer_info?.phone_number || '',
    amountSpent: 'tier_progress_info' in payload ? payload?.tier_progress_info?.current_spend_amount || 0 : 0,
    isCardHolder: payload?.customer_info?.is_card_holder === 'Y',
    isBirthdayMonth: isBirthdayMonth(payload?.customer_info?.date_of_birth || ''),
    amountToSpendToNextTier: 'tier_progress_info' in payload ? payload?.tier_progress_info?.goal_spend_amount || 0 : 0,
    pointsToSpendToNextTier: 'tier_progress_info' in payload ? payload?.tier_progress_info?.goal_points || 0 : 0,
    currentTierExpireDate: 'tier_progress_info' in payload ? payload?.tier_progress_info?.current_tier_expire_date || '' : '',
  };
};

export const extractCustomerOffers = (customerOffers: CustomerOfferInput[], locale: Locale): CustomerOfferOutput[] => {
  const output: CustomerOfferOutput[] = [];
  if (customerOffers && Array.isArray(customerOffers) && customerOffers.length > 0) {
    customerOffers.forEach(offer => {
      const offerType = offer.offer_type;
      const offerValue = offer.offer_value;
      if (Array.isArray(offerValue) && offerValue.length > 0) {
        offerValue.forEach(item => {
          const {
            promo_id: promoId,
            promo_name: promoName,
            promo_description: promoDescription,
            promo_code: promoCode,
            barcode: barCode,
            market,
            applicable_brands: applicableBrands,
            assignment_start_date: startDate,
            assignment_expiration_date: endDate,
            awards,
            legal_terms: legalTerms,
            days_to_redeem,
          } = item;

          // setup variables
          const maxDaysExpireSoon = 5; // max # days until we show expire soon text
          const daysToRedeem = days_to_redeem ?? 0;

          output.push({
            promoId,
            promoName,
            promoDescription,
            promoCode,
            barCode,
            market,
            brand: Array.isArray(applicableBrands) && applicableBrands.length > 0 ? applicableBrands[0] : '',
            startDate,
            endDate,
            offerType,
            amount: Array.isArray(awards) && awards.length > 0 ? awards[0].amount : 0,
            discountType: Array.isArray(awards) && awards.length > 0 ? awards[0].type : '',
            legalTerms: extractLegalTerms(legalTerms, locale),
            daysToRedeem: daysToRedeem,
            expiresSoon: daysToRedeem > 0 && daysToRedeem <= maxDaysExpireSoon,
          });
        });
      }
    });
  }
  return output;
};

export const extractBounceBackOffers = (bounceBackOffers: BounceBackOfferInput[], locale: Locale, market: Market): BounceBackOfferOutput[] => {
  const output: BounceBackOfferOutput[] = [];
  if (bounceBackOffers && Array.isArray(bounceBackOffers) && bounceBackOffers.length > 0) {
    bounceBackOffers.forEach(offer => {
      const {
        brand_code,
        burn_period: { days_to_redeem: daysToRedeem, start_datetime: startDate, end_datetime: endDate },
        discount: { type, amount, bar_code: barCode, promotion_code: promotionCode },
        promotion_messages: legalTerms,
      } = offer;
      const brand = getBrandFromBrandCode(brand_code, market);
      output.push({
        amount,
        barCode,
        brand,
        endDate,
        startDate,
        daysToRedeem,
        discountType: type,
        legalTerms: extractLegalTerms(legalTerms, locale),
        promotionCode,
        expiresSoon: isExpiringSoon(endDate),
      });
    });
  }
  return output;
};

export const extractMarketplaceRewards = (marketplaceRewards: RewardInput[]): RewardsItem[] => {
  const output: RewardsItem[] = [];
  marketplaceRewards.forEach(reward => {
    output.push({
      brand: reward.brand || '',
      channel: reward.channel || '',
      rewardId: reward.reward_id || '',
      rewardType: reward.reward_type || '',
      rewardProgramCode: reward.reward_program_code || '',
      programStartDate: reward.program_start_date || '',
      programEndDate: reward.program_end_date || '',
      rewardName: reward.reward_name || '',
      rewardDescription: reward.reward_description || '',
      legalTerms: reward.legal_terms || '',
      pointsValue: reward.points_value || 0,
      partnerName: reward.partner_name || '',
      navigationRewardsUrl: reward.navigation_rewards_url || '',
      navigationMarketplaceUrl: reward.navigation_marketplace_url || '',
      imageListPageUrl: hatImg.src, // temporary placeholder image until API is ready
      imageDetailsPageUrl: hatImg.src, // temporary placeholder image until API is ready
      customerRewardUsageMaximum: reward.customer_reward_usage_maximum || 0,
      pointsValueDisplay: reward.points_value_display || '',
      userConfirmationDisplayText: reward.user_confirmation_display_text || '',
      badges: Array.isArray(reward.badges) ? reward.badges : [],
      claimableByCustomer: reward.claimable_by_customer || '',
      claimDisabledReason: reward.claim_disabled_reason || '',
      rewardActivityIdToClaim: reward.reward_activity_id_to_claim || '',
      redeemedCount: reward.redeemed_count || 0,
      inventory: reward.inventory || 0,
      rewardsProgramBag: Array.isArray(reward.rewards_program_bag)
        ? reward.rewards_program_bag.map(bag => ({
            rewardActivityId: bag.reward_activity_id || '',
            rewardActivityType: bag.reward_activity_type || '',
            rewardActivityStatus: bag.reward_activity_status || '',
            rewardActivityDate: bag.reward_activity_date || '',
          }))
        : [],
    });
  });
  return output;
};
