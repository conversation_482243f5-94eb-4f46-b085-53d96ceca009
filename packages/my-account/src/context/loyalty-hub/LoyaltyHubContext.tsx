'use client';
import { isEqual } from 'lodash';
import { usePathname } from 'next/navigation';
import React, { createContext, useReducer, ReactNode, useEffect } from 'react';
import * as ActionTypes from '../../constants/loyalty-hub/loyaltyHubActionTypes';
import type { State, Action, ContextProps } from './types';
import {
  extractCustomerOffers,
  extractBounceBackOffers,
  extractMarketplaceRewards,
  checkFailedServicesError,
  isOutOfStock,
  extractUserData,
} from './reducerUtil';

const initialState: State = {
  user: {
    dob: '',
    tier: '',
    points: 0,
    nextTier: '',
    loyaltyId: '',
    phoneNumber: '',
    amountSpent: 0,
    pendingPoints: 0,
    rewardsBalance: '',
    isCardHolder: false,
    isBirthdayMonth: false,
    amountToSpendToNextTier: 0,
    pointsToSpendToNextTier: 0,
    currentTierExpireDate: '',
    pointsAmount: 0,
  },
  offers: {
    customerOffers: [],
    bounceBackOffers: [],
  },
  rewards: Array.from({ length: 16 }, () => ({
    brand: '',
    channel: '',
    rewardId: '',
    rewardType: '',
    rewardProgramCode: '',
    programStartDate: '',
    programEndDate: '',
    rewardName: '',
    rewardDescription: '',
    legalTerms: '',
    pointsValue: 0,
    partnerName: '',
    navigationRewardsUrl: '',
    navigationMarketplaceUrl: '',
    imageListPageUrl: '',
    imageDetailsPageUrl: '',
    customerRewardUsageMaximum: 0,
    pointsValueDisplay: '',
    userConfirmationDisplayText: '',
    badges: [],
    claimableByCustomer: '',
    claimDisabledReason: '',
    rewardActivityIdToClaim: '',
    redeemedCount: 0,
    inventory: 0,
    rewardsProgramBag: [
      {
        rewardActivityId: '',
        rewardActivityType: '',
        rewardActivityStatus: '',
        rewardActivityDate: '',
      },
    ],
  })),
  pointsActivity: [],
  appliedPromos: {
    promoCode: '',
  },
  convertedPoints: {
    points: 0,
  },
  loyaltyHubLandingReq: {
    isSuccess: false,
    isFail: false,
    isTierProgressFail: false,
    isCardsFail: false,
  },
  pwpAddToBag: {
    isOutOfStock: false,
  },
  loyaltyHubMemberPerksReq: {
    isSuccess: false,
    isFail: false,
    isCardsFail: false,
    isBounceBackOffersFail: false,
    isCustomerOffersFail: false,
  },
  loyaltyHubMyMembershipReq: {
    isSuccess: false,
    isFail: false,
    isCardsFail: false,
    isTierProgressFail: false,
    isPointsActivityFail: false,
    isPendingPointsFail: false,
  },
  loyaltyHubRewardsMarketplaceReq: {
    isSuccess: false,
    isFail: false,
  },
  loyaltyHubConvertPointsReq: {
    isSuccess: false,
    isFail: false,
  },
  loyaltyHubApplyPromoReq: {
    isSuccess: false,
    isFail: false,
  },
  loyaltyHubAddToBagReq: {
    isSuccess: false,
    isFail: false,
  },
  rewardsMarketplaceDonationsReq: {
    isSuccess: false,
    isFail: false,
  },
  rewardsMarketplaceSweepstakesReq: {
    isSuccess: false,
    isFail: false,
  },
};

const LoyaltyHubContext = createContext<ContextProps | undefined>(undefined);
const reducer = (state: State, action: Action): State => {
  switch (action.type) {
    case ActionTypes.POST_LOYALTY_HUB_LANDING_SUCCESS: {
      return {
        ...state,
        loyaltyHubLandingReq: {
          isSuccess: true,
          isFail: false,
          isCardsFail: checkFailedServicesError('Cards', action.payload?.meta),
          isTierProgressFail: checkFailedServicesError('TierProgress', action.payload?.meta),
        },
        user: extractUserData(state, action?.payload),
      };
    }
    case ActionTypes.POST_LOYALTY_HUB_LANDING_FAIL: {
      return {
        ...state,
        loyaltyHubLandingReq: {
          isSuccess: false,
          isFail: true,
          isTierProgressFail: false,
          isCardsFail: false,
        },
      };
    }
    case ActionTypes.POST_LOYALTY_HUB_MEMBER_PERKS_SUCCESS: {
      return {
        ...state,
        loyaltyHubMemberPerksReq: {
          isSuccess: true,
          isFail: false,
          isCardsFail: checkFailedServicesError('Cards', action.payload?.meta),
          isBounceBackOffersFail: checkFailedServicesError('BounceBackOffers', action.payload?.meta),
          isCustomerOffersFail: checkFailedServicesError('CustomerOffers', action.payload?.meta),
        },
        user: extractUserData(state, action?.payload),
        offers: {
          customerOffers: extractCustomerOffers(Array.isArray(action.payload?.customer_offers) ? action.payload?.customer_offers : [], action.payload.locale),
          bounceBackOffers: extractBounceBackOffers(
            Array.isArray(action.payload?.bounceback_offers) ? action.payload?.bounceback_offers : [],
            action.payload.locale,
            action.payload.market
          ),
        },
      };
    }
    case ActionTypes.POST_LOYALTY_HUB_MEMBER_PERKS_FAIL: {
      return {
        ...state,
        loyaltyHubMemberPerksReq: {
          isSuccess: false,
          isFail: true,
          isCardsFail: false,
          isBounceBackOffersFail: false,
          isCustomerOffersFail: false,
        },
      };
    }
    case ActionTypes.POST_LOYALTY_HUB_ADD_TO_BAG_SUCCESS: {
      return {
        ...state,
        loyaltyHubAddToBagReq: {
          isSuccess: true,
          isFail: false,
        },
      };
    }
    case ActionTypes.POST_LOYALTY_HUB_ADD_TO_BAG_FAIL: {
      return {
        ...state,
        loyaltyHubAddToBagReq: {
          isSuccess: false,
          isFail: true,
        },
        pwpAddToBag: {
          isOutOfStock: isOutOfStock(action.payload?.outOfStockItems, action.payload?.sku),
        },
      };
    }
    case ActionTypes.POST_LOYALTY_HUB_MY_MEMBERSHIP_SUCCESS: {
      return {
        ...state,
        loyaltyHubMyMembershipReq: {
          isSuccess: true,
          isFail: false,
          isCardsFail: checkFailedServicesError('Cards', action.payload?.meta),
          isTierProgressFail: checkFailedServicesError('TierProgress', action.payload?.meta),
          isPointsActivityFail: checkFailedServicesError('PointsActivity', action.payload?.meta),
          isPendingPointsFail: checkFailedServicesError('Points', action.payload?.meta),
        },
        user: extractUserData(state, action?.payload),
        pointsActivity:
          action.payload?.points_activity?.map(activity => ({
            date: activity?.transaction_date,
            status: activity?.points[0]?.status || '',
            points: activity?.points[0]?.value || 0,
            transactionId: activity?.transaction_number,
            transactionDescription: activity?.transaction_desc,
          })) || [],
      };
    }
    case ActionTypes.POST_LOYALTY_HUB_MY_MEMBERSHIP_FAIL: {
      return {
        ...state,
        loyaltyHubMyMembershipReq: {
          isSuccess: false,
          isFail: true,
          isCardsFail: false,
          isTierProgressFail: false,
          isPointsActivityFail: false,
          isPendingPointsFail: false,
        },
      };
    }
    case ActionTypes.POST_LOYALTY_HUB_REWARDS_MARKETPLACE_SUCCESS: {
      return {
        ...state,
        loyaltyHubRewardsMarketplaceReq: {
          isSuccess: true,
          isFail: false,
        },
        user: extractUserData(state, action?.payload),
        rewards: extractMarketplaceRewards(Array.isArray(action.payload?.marketplace_rewards) ? action.payload.marketplace_rewards : []),
      };
    }
    case ActionTypes.POST_LOYALTY_HUB_REWARDS_MARKETPLACE_FAIL: {
      return {
        ...state,
        loyaltyHubRewardsMarketplaceReq: {
          isSuccess: false,
          isFail: true,
        },
      };
    }
    case ActionTypes.POST_LOYALTY_HUB_CONVERT_POINTS_SUCCESS: {
      const newPoints = action?.payload?.response?.addedPoints ? Number(action.payload.response.addedPoints) : 0;
      return {
        ...state,
        convertedPoints: {
          points: newPoints,
        },
        loyaltyHubConvertPointsReq: {
          isSuccess: true,
          isFail: false,
        },
      };
    }
    case ActionTypes.POST_LOYALTY_HUB_CONVERT_POINTS_FAIL: {
      return {
        ...state,
        convertedPoints: {
          points: 0,
        },
        loyaltyHubConvertPointsReq: {
          isSuccess: false,
          isFail: true,
        },
      };
    }
    case ActionTypes.PUT_LOYALTY_HUB_APPLY_PROMO_SUCCESS: {
      return {
        ...state,
        appliedPromos: {
          promoCode: action?.payload?.promoCode || '',
        },
        loyaltyHubApplyPromoReq: {
          isSuccess: true,
          isFail: false,
        },
      };
    }
    case ActionTypes.PUT_LOYALTY_HUB_APPLY_PROMO_FAIL: {
      return {
        ...state,
        appliedPromos: {
          promoCode: action?.payload?.promoCode || '',
        },
        loyaltyHubApplyPromoReq: {
          isSuccess: false,
          isFail: true,
        },
      };
    }
    case ActionTypes.POST_REWARDS_MARKETPLACE_DONATIONS_SUCCESS: {
      return {
        ...state,
        rewardsMarketplaceDonationsReq: {
          isSuccess: true,
          isFail: false,
        },
      };
    }
    case ActionTypes.POST_REWARDS_MARKETPLACE_DONATIONS_FAIL: {
      return {
        ...state,
        rewardsMarketplaceDonationsReq: {
          isSuccess: false,
          isFail: true,
        },
      };
    }
    case ActionTypes.RESET_REWARDS_MARKETPLACE_DETAILS_PAGE_REQS: {
      return {
        ...state,
        rewardsMarketplaceDonationsReq: {
          isSuccess: false,
          isFail: false,
        },
      };
    }

    case ActionTypes.POST_REWARDS_MARKETPLACE_SWEEPSTAKES_SUCCESS: {
      return {
        ...state,
        rewardsMarketplaceSweepstakesReq: {
          isSuccess: true,
          isFail: false,
        },
      };
    }
    case ActionTypes.POST_REWARDS_MARKETPLACE_SWEEPSTAKES_FAIL: {
      return {
        ...state,
        rewardsMarketplaceSweepstakesReq: {
          isSuccess: false,
          isFail: true,
        },
      };
    }

    case ActionTypes.RESET_LOYALTY_HUB_REQS: {
      const isMyMembership = action.payload === 'myMembership';
      const isMemberPerks = action.payload === 'memberPerks';
      const isRewardsMarketplace = action.payload === 'rewardsMarketplace';
      if (isMyMembership) {
        return {
          ...state,
          loyaltyHubMyMembershipReq: {
            isSuccess: false,
            isFail: false,
            isCardsFail: false,
            isTierProgressFail: false,
            isPointsActivityFail: false,
            isPendingPointsFail: false,
          },
        };
      } else if (isMemberPerks) {
        return {
          ...state,
          loyaltyHubMemberPerksReq: {
            isSuccess: false,
            isFail: false,
            isCardsFail: false,
            isBounceBackOffersFail: false,
            isCustomerOffersFail: false,
          },
          loyaltyHubConvertPointsReq: {
            isSuccess: false,
            isFail: false,
          },
          loyaltyHubApplyPromoReq: {
            isSuccess: false,
            isFail: false,
          },
          appliedPromos: {
            promoCode: '',
          },
          convertedPoints: {
            points: 0,
          },
        };
      } else if (isRewardsMarketplace) {
        return {
          ...state,
          loyaltyHubRewardsMarketplaceReq: {
            isSuccess: false,
            isFail: false,
          },
          rewardsMarketplaceDonationsReq: {
            isSuccess: false,
            isFail: false,
          },
        };
      }
      return {
        ...state,
        loyaltyHubLandingReq: {
          isSuccess: false,
          isFail: false,
          isTierProgressFail: false,
          isCardsFail: false,
        },
      };
    }
    case ActionTypes.RESET_LOYALTY_HUB_STATE: {
      return {
        ...initialState,
      };
    }
    default:
      throw new Error(`Unknown action: ${action}`);
  }
};

const LoyaltyHubProvider: React.FC<{ children: ReactNode; isLoyaltyHubEnabled: boolean }> = ({ children, isLoyaltyHubEnabled }) => {
  const pathname = usePathname();
  const [loyaltyHubState, loyaltyHubDispatch] = useReducer(reducer, initialState);

  useEffect(() => {
    const isLoyaltyHubRoute = typeof pathname === 'string' && pathname.startsWith('/my-account/loyalty-hub');
    const isStateModified = !isEqual(loyaltyHubState, initialState);
    if (isLoyaltyHubEnabled && !isLoyaltyHubRoute && isStateModified) {
      loyaltyHubDispatch({ type: ActionTypes.RESET_LOYALTY_HUB_STATE });
    }
  }, [pathname]);

  return <LoyaltyHubContext.Provider value={{ loyaltyHubState, loyaltyHubDispatch, isLoyaltyHubEnabled }}>{children}</LoyaltyHubContext.Provider>;
};

export { LoyaltyHubProvider, LoyaltyHubContext };
