import type { Locale } from '@ecom-next/utils/server';
import { clientFetch } from '@ecom-next/utils/clientFetch';
import { triggerLoyaltyHubLandingReq, triggerLoyaltyHubConvertPointsReq, triggerLoyaltyHubApplyPromoReq, triggerSweepstakesReq } from '../loyaltyHubRequests';

jest.mock('@ecom-next/utils/clientFetch');

jest.mock('js-cookie', () => ({
  get: jest.fn(),
}));

const mockResponse = {
  external_customer_id: '6538e8d0b6864d979ecc9935175063b6',
  market: 'US',
  loyalty_id: 'ef1d6b66-9a8f-4fd6-bd85-85362147aac7',
  customer_info: {
    customer_tier: 'CORE',
    is_card_holder: 'N',
    enrollment_brand: 'GP',
  },
  tier_progress_info: {
    current_tier: 'CORE',
    current_tier_expire_date: '31/12/2022',
    next_tier: 'ENTHUSIAST',
    current_spend_amount: 100,
    goal_spend_amount: 500,
    difference_spend_amount: 400,
    current_points: 50,
    goal_points: 100,
    difference_points: 50,
  },
  quarters_shopped: [
    {
      brand: ['ON', 'GP'],
      quarter_shopped: [
        {
          quarter: 1,
          start_date: '2025-01-01',
          end_date: '2025-03-31',
          shopped: true,
        },
        {
          quarter: 2,
          start_date: '2025-04-01',
          end_date: '2025-06-30',
          shopped: false,
        },
      ],
    },
  ],
  points: {
    active_points: 120,
    active_points_amount: 15.5,
    conversion_rate: 0.01,
    discount_reason_code: 12345,
    max_points: 1000,
    min_points: 100,
    pending_points: 50,
    pending_points_amount: 5,
    points_incremental_value: 10,
    points_until_next_reward: 90,
    program_event_type: 'PayWithPoints',
    program_identifier: 'VARIABLEPT1',
    program_name: 'Single Reward Day',
    total_points: 250,
    total_points_amount: 25,
  },
  marketplace_rewards: [
    {
      brand: 'ON',
      channel: 'ALL',
      reward_type: 'Sweepstakes',
      reward_program_code: 'Sweeps_Gift',
      program_start_date: '2025-09-17T23:59:59Z',
      program_end_date: '2025-01-10T23:59:59Z',
      reward_name: 'SWEEP_GIFTCARD',
      reward_description: 'Old Navy $100 Gift Card Sweeps',
      legal_terms: 'Legal terms for the reward',
      points_value: 100,
      partner_name: 'Merkle',
      navigation_rewards_url: 'rewardurl',
      navigation_marketplace_url: 'marketplaceurl',
      image_list_page_url: 'imagelistpageurl',
      image_details_page_url: 'imagedetailspageurl',
      customer_reward_usage_maximum: 3,
      points_value_display: '100 points && 100 points for Cardmembers',
      user_confirmation_display_text: 'Yes, I want to spend 100 points to redeem the reward',
      badges: ['Card Exclusive', 'Ending Soon'],
      claimable_by_customer: 'Y',
      claim_disabled_reason: '',
      reward_activity_id_to_claim: 'abc124',
      redeemed_count: 3,
      inventory: 5,
      rewards_program_bag: [
        {
          reward_activity_id: 'abc123',
          partner_reward_code: '',
          reward_claimed_date: '2025-09-17T23:59:59Z',
          reward_redeemed_date: '2025-01-10T23:59:59Z',
          reward_status: 'redeemed',
        },
        {
          reward_activity_id: 'abc122',
          partner_reward_code: '',
          reward_claimed_date: '2025-09-17T23:59:59Z',
          reward_redeemed_date: '2025-01-10T23:59:59Z',
          reward_status: 'redeemed',
        },
        {
          reward_activity_id: 'abc121',
          partner_reward_code: '',
          reward_claimed_date: '2025-09-17T23:59:59Z',
          reward_redeemed_date: '2025-01-10T23:59:59Z',
          reward_status: 'redeemed',
        },
      ],
    },
    {
      brand: 'GAP',
      channel: 'ALL',
      reward_type: 'BrandExperience',
      reward_program_code: 'BEXP_DOEN',
      program_start_date: '2025-09-17T23:59:59Z',
      program_end_date: '2025-01-10T23:59:59Z',
      reward_name: 'SWEEP_GIFTCARD',
      reward_description: 'Shop Doen Experience in Times Square',
      legal_terms: 'Legal terms for the reward',
      points_value: 2000,
      partner_name: 'Merkle',
      navigation_rewards_url: 'rewardurl',
      navigation_marketplace_url: 'marketplaceurl',
      image_list_page_url: 'imagelistpageurl',
      image_details_page_url: 'imagedetailspageurl',
      customer_reward_usage_maximum: 1,
      points_value_display: '2000 points && 2000 points for Cardmembers',
      user_confirmation_display_text: 'Yes, I want to spend 2000 points to redeem the reward',
      badges: ['Card Exclusive', 'Ending Soon'],
      claimable_by_customer: 'Y',
      claim_disabled_reason: '',
      reward_activity_id_to_claim: 'abc124',
      redeemed_count: 3,
      inventory: 5,
      rewards_program_bag: [
        {
          reward_activity_id: 'abc123',
          partner_reward_code: '',
          reward_claimed_date: '2025-09-17T23:59:59Z',
          reward_redeemed_date: '2025-01-10T23:59:59Z',
          reward_status: 'redeemed',
        },
        {
          reward_activity_id: 'abc122',
          partner_reward_code: '',
          reward_claimed_date: '2025-09-17T23:59:59Z',
          reward_redeemed_date: '2025-01-10T23:59:59Z',
          reward_status: 'redeemed',
        },
        {
          reward_activity_id: 'abc121',
          partner_reward_code: '',
          reward_claimed_date: '2025-09-17T23:59:59Z',
          reward_redeemed_date: '2025-01-10T23:59:59Z',
          reward_status: 'redeemed',
        },
      ],
    },
    {
      brand: 'ALL',
      channel: 'ALL',
      reward_type: 'DigitalPartnerReward',
      reward_program_code: 'PARTNER_DH299',
      program_start_date: '2025-09-17T23:59:59Z',
      program_end_date: '2025-01-10T23:59:59Z',
      reward_name: 'DISNEY_HULU_299',
      reward_description: 'Disney+ Hulu $2.99/mo for 3 Months',
      legal_terms: 'Legal terms for the reward',
      points_value: 2000,
      partner_name: 'Disney',
      navigation_rewards_url: 'https://web.disneyplus.com/redeem',
      navigation_marketplace_url: 'https://wwww.gap.com/marketplace-rewards',
      image_list_page_url: 'https://wwww.gap.com/images/reward-list.jpg',
      image_details_page_url: 'https://wwww.gap.com/images/reward-detail.jpg',
      customer_reward_usage_maximum: 5,
      points_value_display: '100 points && Free for Cardmembers',
      user_confirmation_display_text: 'Yes, I want to spend 100 points to redeem the reward',
      badges: ['Card Exclusive', 'Ending Soon'],
      claimable_by_customer: 'Y',
      claim_disabled_reason: '',
      reward_activity_id_to_claim: 'abc124',
      redeemed_count: 3,
      inventory: 5,
      rewards_program_bag: [
        {
          reward_activity_id: 'abc123',
          partner_reward_code: 'DISTRIAL-202505',
          reward_claimed_date: '2025-09-17T23:59:59Z',
          reward_redeemed_date: '2025-01-10T23:59:59Z',
          reward_status: 'redeemed',
        },
        {
          reward_activity_id: 'abc122',
          partner_reward_code: 'DISTRIAL-202505',
          reward_claimed_date: '2025-09-17T23:59:59Z',
          reward_redeemed_date: '2025-01-10T23:59:59Z',
          reward_status: 'redeemed',
        },
        {
          reward_activity_id: 'abc121',
          partner_reward_code: 'DISTRIAL-202505',
          reward_claimed_date: '2025-09-17T23:59:59Z',
          reward_redeemed_date: '2025-01-10T23:59:59Z',
          reward_status: 'redeemed',
        },
      ],
    },
    {
      brand: 'ON',
      channel: 'ALL',
      reward_type: 'Donations',
      reward_program_code: 'IMAGINE_MISSION_FUND',
      program_start_date: '2025-09-17T23:59:59Z',
      program_end_date: '2025-01-10T23:59:59Z',
      reward_name: 'IMAGINE_MISSION_FUND',
      reward_description: 'Imagine Mission Funds',
      legal_terms: 'Legal terms for the reward',
      points_value: 0,
      partner_name: '',
      navigation_rewards_url: 'navigationRewrdsUrl',
      navigation_marketplace_url: 'marketplaceurl',
      image_list_page_url: 'imagelistpageurl',
      image_details_page_url: 'imagedetailspageurl',
      customer_reward_usage_maximum: 0,
      points_value_display: "'2000 points', '2000 points && Free for Cardmembers'",
      user_confirmation_display_text: 'Yes, I want to spend 2000 points to redeem the reward',
      badges: ['Donation'],
      claimable_by_customer: 'Y',
      claim_disabled_reason: '',
      reward_activity_id_to_claim: 'abc124',
      redeemed_count: 3,
      inventory: 5,
      rewards_program_bag: [
        {
          reward_activity_id: 'abc123',
          partner_reward_code: '',
          reward_claimed_date: '2025-09-17T23:59:59Z',
          reward_redeemed_date: '2025-01-10T23:59:59Z',
          reward_status: 'redeemed',
        },
        {
          reward_activity_id: 'abc122',
          partner_reward_code: '',
          reward_claimed_date: '2025-09-17T23:59:59Z',
          reward_redeemed_date: '2025-01-10T23:59:59Z',
          reward_status: 'redeemed',
        },
        {
          reward_activity_id: 'abc121',
          partner_reward_code: '',
          reward_claimed_date: '2025-09-17T23:59:59Z',
          reward_redeemed_date: '2025-01-10T23:59:59Z',
          reward_status: 'redeemed',
        },
      ],
    },
    {
      brand: 'GAP',
      channel: 'ALL',
      reward_type: 'PwPProduct',
      reward_program_code: 'Gap_Bottle',
      program_start_date: '2025-09-17T23:59:59Z',
      program_end_date: '2025-01-10T23:59:59Z',
      reward_name: 'GAP_WATER_BOTTLE',
      reward_description: 'Gap Water Bottle',
      legal_terms: 'Legal terms for the reward',
      points_value: 3000,
      navigation_rewards_url: 'navigationRewrdsUrl',
      navigation_marketplace_url: 'marketplaceurl',
      image_list_page_url: 'imagelistpageurl',
      image_details_page_url: 'imagedetailspageurl',
      customer_reward_usage_maximum: 0,
      points_value_display: "'3000 points', '3000 points && Free for Cardmembers'",
      user_confirmation_display_text: 'Yes, I want to spend 3000 points to redeem the reward',
      badges: ['Card Exclusive'],
      claimable_by_customer: 'N',
      claim_disabled_reason: 'Customer Not Cardmember',
      reward_activity_id_to_claim: 'abc124',
      redeemed_count: 3,
      inventory: 5,
      rewards_program_bag: [
        {
          reward_activity_id: 'abc123',
          partner_reward_code: '',
          reward_claimed_date: '2025-09-17T23:59:59Z',
          reward_redeemed_date: '2025-01-10T23:59:59Z',
          reward_status: 'claimed',
        },
        {
          reward_activity_id: 'abc122',
          partner_reward_code: '',
          reward_claimed_date: '2025-09-17T23:59:59Z',
          reward_redeemed_date: '2025-01-10T23:59:59Z',
          reward_status: 'claimed',
        },
        {
          reward_activity_id: 'abc121',
          partner_reward_code: '',
          reward_claimed_date: '2025-09-17T23:59:59Z',
          reward_redeemed_date: '2025-01-10T23:59:59Z',
          reward_status: 'claimed',
        },
      ],
    },
  ],
  bounceback_offers: [
    {
      applicable_channels: ['Retail'],
      brand_code: '1',
      burn_period: {
        days_to_redeem: 14,
        start_datetime: '2023-10-02T03:00:00.000-04:00',
        end_datetime: '2023-10-02T03:00:00.000-04:00',
      },
      discount: {
        amount: 15,
        bar_code: '9X3PWCVQLKV4F1YY',
        legal_terms: 'Redeem Gap Cash 20 dollar',
        promotion_code: '20OFF',
        promotion_id: '268550',
        type: 'Dollar Off',
      },
      promotion_messages: [
        {
          legal_terms: 'Redeem Gap Cash 20 dollar',
          locale_code: 'en_US',
          marketing_description: 'Redeem Gap Cash 20 dollar',
        },
      ],
    },
  ],
  meta: {
    overall_success: false,
    failed_services: ['Cards', 'MarketplaceRewards', 'ExpiredBouncebackOffers'],
    more_info: '/swagger-ui.html',
  },
};

describe('Loyalty Hub Requests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should handle success correctly when triggerLoyaltyHubLandingReq() is called', async () => {
    (clientFetch as jest.Mock).mockReturnValueOnce(mockResponse);
    const payload = {
      brand: 'ON',
      market: 'us' as const,
      locale: 'en_US' as Locale,
      ecomApiBaseUrl: 'https://ecom-api.stage.gaptechol.com',
      loyaltyHubDispatch: jest.fn().mockResolvedValue((data: unknown) => data),
    };
    const spy = jest.spyOn(payload, 'loyaltyHubDispatch');
    await triggerLoyaltyHubLandingReq(payload);
    expect(spy).toHaveBeenCalledTimes(1);
    expect(spy).toHaveBeenCalledWith({
      type: 'POST_LOYALTY_HUB_LANDING_SUCCESS',
      payload: mockResponse,
    });
  });

  it('should handle fail correctly when triggerLoyaltyHubLandingReq() is called', async () => {
    (clientFetch as jest.Mock).mockRejectedValueOnce(new Error('Network Error'));
    const payload = {
      brand: 'ON',
      market: 'us' as const,
      locale: 'en_US' as Locale,
      ecomApiBaseUrl: 'https://ecom-api.stage.gaptechol.com',
      loyaltyHubDispatch: jest.fn(),
    };
    const spy = jest.spyOn(payload, 'loyaltyHubDispatch');
    await triggerLoyaltyHubLandingReq(payload);
    expect(spy).toHaveBeenCalledTimes(1);
    expect(spy).toHaveBeenCalledWith({
      type: 'POST_LOYALTY_HUB_LANDING_FAIL',
    });
  });

  it('should handle success correctly when triggerLoyaltyHubConvertPointsReq() is called', async () => {
    (clientFetch as jest.Mock).mockReturnValueOnce({ statusCode: 200 });
    const payload = {
      market: 'us' as const,
      promoCode: '123456789012',
      ecomApiBaseUrl: 'https://ecom-api.stage.gaptechol.com',
      loyaltyHubDispatch: jest.fn().mockResolvedValue((data: unknown) => data),
    };
    const spy = jest.spyOn(payload, 'loyaltyHubDispatch');
    await triggerLoyaltyHubConvertPointsReq(payload);
    expect(spy).toHaveBeenCalledTimes(1);
    expect(spy).toHaveBeenCalledWith({
      type: 'POST_LOYALTY_HUB_CONVERT_POINTS_SUCCESS',
      payload: { response: { statusCode: 200 } },
    });
  });

  it('should handle fail correctly when triggerLoyaltyHubConvertPointsReq() is called', async () => {
    (clientFetch as jest.Mock).mockRejectedValueOnce(new Error('Network Error'));
    const payload = {
      market: 'us' as const,
      promoCode: '123456789012',
      ecomApiBaseUrl: 'https://ecom-api.stage.gaptechol.com',
      loyaltyHubDispatch: jest.fn().mockResolvedValue((data: unknown) => data),
    };
    const spy = jest.spyOn(payload, 'loyaltyHubDispatch');
    await triggerLoyaltyHubConvertPointsReq(payload);
    expect(spy).toHaveBeenCalledTimes(1);
    expect(spy).toHaveBeenCalledWith({
      type: 'POST_LOYALTY_HUB_CONVERT_POINTS_FAIL',
    });
  });

  it('should handle success correctly when triggerLoyaltyHubApplyPromoReq() is called', async () => {
    (clientFetch as jest.Mock).mockReturnValueOnce({ statusCode: 200 });
    const payload = {
      brand: 'GP',
      market: 'us' as const,
      promoCode: '123456789012',
      ecomApiBaseUrl: 'https://ecom-api.stage.gaptechol.com',
      loyaltyHubDispatch: jest.fn().mockResolvedValue((data: unknown) => data),
    };
    const spy = jest.spyOn(payload, 'loyaltyHubDispatch');
    await triggerLoyaltyHubApplyPromoReq(payload);
    expect(spy).toHaveBeenCalledTimes(1);
    expect(spy).toHaveBeenCalledWith({
      type: 'PUT_LOYALTY_HUB_APPLY_PROMO_SUCCESS',
      payload: { promoCode: '123456789012' },
    });
  });

  it('should handle fail correctly when triggerLoyaltyHubApplyPromoReq() is called', async () => {
    (clientFetch as jest.Mock).mockRejectedValueOnce(new Error('Network Error'));
    const payload = {
      brand: 'GP',
      market: 'us' as const,
      promoCode: '123456789012',
      ecomApiBaseUrl: 'https://ecom-api.stage.gaptechol.com',
      loyaltyHubDispatch: jest.fn().mockResolvedValue((data: unknown) => data),
    };
    const spy = jest.spyOn(payload, 'loyaltyHubDispatch');
    await triggerLoyaltyHubApplyPromoReq(payload);
    expect(spy).toHaveBeenCalledTimes(1);
    expect(spy).toHaveBeenCalledWith({
      type: 'PUT_LOYALTY_HUB_APPLY_PROMO_FAIL',
      payload: { promoCode: '123456789012' },
    });
  });

  it('should handle success when triggerLoyaltyHubSweepstakesReq() is called', async () => {
    (clientFetch as jest.Mock).mockReturnValueOnce({ reward_activity_id: 'some_id' });
    const payload = {
      brand: 'ON',
      market: 'us' as const,
      locale: 'en_US' as const,
      ecomApiBaseUrl: 'https://ecom-api.stage.gaptechol.com',
      loyaltyHubDispatch: jest.fn().mockResolvedValue((data: unknown) => data),
      loyalty_member_id: 'member123',
      reward_program_code: 'program123',
      reward_activity_id: 'activity123',
      points_value: '100',
    };

    const spy = jest.spyOn(payload, 'loyaltyHubDispatch');
    await triggerSweepstakesReq(payload);
    expect(spy).toHaveBeenCalledWith({
      type: 'POST_REWARDS_MARKETPLACE_SWEEPSTAKES_SUCCESS',
      payload: {
        reward_activity_id: 'some_id',
      },
    });
    expect(spy).toHaveBeenCalledWith({
      type: 'POST_LOYALTY_HUB_REWARDS_MARKETPLACE_SUCCESS',
      payload: {
        market: 'us',
        locale: 'en_US',
      },
    });
  });
});
