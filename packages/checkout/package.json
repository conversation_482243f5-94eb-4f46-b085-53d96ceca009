{"name": "@ecom-next/checkout", "version": "1.0.0", "description": "", "exports": {"./pages/Checkout": "./src/pages/Checkout.tsx", "./layouts/Checkout": "./src/layouts/Checkout.tsx", "./components/*": "./src/components/*.tsx", "./overlay/*": ["./src/components/layout/FormOverlay/*.tsx", "./src/components/layout/FormOverlay/*.ts"], "./modules/*": "./src/components/modules/*.tsx", "./contexts/*": ["./src/contexts/*.ts", "./src/contexts/*.tsx"], "./api/*": "./src/api/*.ts", "./layouts/*": "./src/layouts/*.tsx", "./translations": "./src/translations/index.ts", "./CheckoutProvider": "./src/contexts/CheckoutProvider.tsx", "./CheckoutUiStateProvider": "./src/contexts/CheckoutUiStateProvider.tsx", "./CheckoutProviderWrapper": "./src/contexts/CheckoutProviderWrapper.tsx", "./useCheckoutFeatures": "./src/contexts/checkoutFeatures/useCheckoutFeatures.tsx", "./PaymentsProvider": "./src/contexts/PaymentsProvider.tsx", "./ProviderWrapper": "./src/contexts/CheckoutProviderWrapper.tsx", "./tealium-utils": "./src/utils/tealium-utils.ts", "./utils/*": "./src/utils/*.ts", "./compositeAPI/*": ["./src/utils/compositeAPI/*.ts", "./src/utils/compositeAPI/*.tsx"]}, "scripts": {"test:changedsince": "jest --changedSince=origin/main", "clean": "rm -rf dist", "lint": "eslint ./src", "format": "prettier . --write --ignore-unknown", "test": "jest", "test:watch": "jest --watch"}, "keywords": [], "dependencies": {"next": "14.2.25", "@ecom-next/marketing-ui": "1.0.0", "@ecom-next/shopping-bag": "1.0.0", "@ecom-next/sitewide": "1.0.0", "@ecom-next/my-account": "1.0.0", "@ecom-next/utils": "1.0.0", "@hubbox/single-widget-manager": "^2.7.0", "@hubbox/web-components": "^3.18.12"}, "devDependencies": {"react": "18.3.1", "react-dom": "18.3.1", "tailwindcss": "^3.4.3"}, "author": "", "license": "ISC"}