import { useEffect } from 'react';
import { getCookie, setSessionCookie, generateSessionId } from '@ecom-next/shopping-bag/utils';

const COOKIE_NAME = 'sc';
const SESSION_DURATION_MS = 24 * 60 * 60 * 1000; // 24 hours

export const Signifyd = () => {
  useEffect(() => {
    let sessionId = getCookie(COOKIE_NAME);

    if (!sessionId) {
      sessionId = generateSessionId();
      setSessionCookie(COOKIE_NAME, sessionId, SESSION_DURATION_MS);
    }

    // Avoid duplicate injection
    if (document.getElementById('sig-api')) return;

    const script = document.createElement('script');
    script.id = 'sig-api';
    script.src = 'https://cdn-scripts.signifyd.com/api/script-tag.js';
    script.type = 'text/javascript';
    script.defer = true;
    script.setAttribute('data-order-session-id', sessionId);

    document.head.appendChild(script);
  }, []);

  return null;
};
