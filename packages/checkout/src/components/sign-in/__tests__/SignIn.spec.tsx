import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { setSessionCookie } from '@ecom-next/shopping-bag/utils';
import SignIn from '../SignIn';
import { validateAccount } from '../validateAccount';
import { firePageViewTag, fireTealiumLinkTag } from '../../../utils/tealium-utils';

jest.mock('@ecom-next/shopping-bag/utils');
jest.mock('../validateAccount');
jest.mock('../../../utils/tealium-utils', () => ({
  fireTealiumLinkTag: jest.fn(),
  firePageViewTag: jest.fn(),
}));

// Mock the required components and hooks
jest.mock('@ecom-next/core/migration/modal', () => ({
  ModalDrawerWrapper: ({ children, title, onClose }) => (
    <div data-testid='modal-drawer-wrapper'>
      <button onClick={onClose} data-testid='close-modal-button'>
        Close
      </button>
      <h2>{title}</h2>
      <div>{children}</div>
    </div>
  ),
}));

jest.mock('@ecom-next/my-account/components/sign-in/banner', () => ({
  Banner: () => <div data-testid='banner'>Banner Content</div>,
}));

jest.mock('@ecom-next/core/migration/checkbox', () => ({
  Checkbox: ({ labelText, isChecked, onChange }) => (
    <label>
      <input type='checkbox' checked={isChecked} onChange={onChange} data-testid='checkout-checkbox' />
      {labelText}
    </label>
  ),
}));

jest.mock('@ecom-next/my-account/components/common/EmailInput', () => ({
  EmailInput: props => (
    <input
      type='email'
      data-testid='checkout-signin-email'
      value={props.value || ''}
      onChange={e => props.onChange && props.onChange(e.target.value)}
      onBlur={e => props.onBlur && props.onBlur(e.target.value)}
    />
  ),
}));

jest.mock('@ecom-next/my-account/components/common/PasswordInput', () => ({
  PasswordInput: props => (
    <input
      type='password'
      data-testid='checkout-sigin-password'
      value={props.value || ''}
      onChange={e => props.onChange && props.onChange(e.target.value)}
      onBlur={e => props.onBlur && props.onBlur(e.target.value)}
    />
  ),
}));

jest.mock('@ecom-next/sitewide/localization-provider', () => ({
  useLocalize: () => ({
    localize: jest.fn(key => {
      switch (key) {
        case 'checkout.resetPassword':
          return 'Reset Password';
        case 'checkout.signin':
          return 'Sign In';
        case 'checkout.checkboxText':
          return 'Keep me signed in on this device.';
        case 'checkout.password':
          return 'Password';
        default:
          return key;
      }
    }),
  }),
}));

jest.mock('@ecom-next/core/migration/button', () => ({
  Button: ({ children, type, onClick }) => (
    <button type={type} onClick={onClick} data-testid='submit-button'>
      {children}
    </button>
  ),
}));

jest.mock('../../utils/useBreakPoint', () => ({
  useBreakpoint: () => ({ isMobile: false }),
}));

jest.mock('../createFlowID', () => ({
  createFlowID: () => ({ uniqueIDResponse: { payload: { id: 'MOCK_ID' } } }),
}));

describe('SignIn', () => {
  beforeEach(() => {
    (validateAccount as jest.Mock).mockReset();
    (setSessionCookie as jest.Mock).mockReset();
    (fireTealiumLinkTag as jest.Mock).mockReset();
    (firePageViewTag as jest.Mock).mockReset();
  });
  it('renders modal drawer and banner', () => {
    render(<SignIn />);
    expect(screen.getByTestId('modal-drawer-wrapper')).toBeInTheDocument();
    expect(screen.getByTestId('sign-in-banner')).toBeInTheDocument();
    expect(screen.getByTestId('banner')).toBeInTheDocument();
  });

  it('renders email and password inputs', () => {
    render(<SignIn />);
    expect(screen.getByTestId('checkout-signin-email')).toBeInTheDocument();
    expect(screen.getByTestId('checkout-sigin-password')).toBeInTheDocument();
  });

  it('renders reset password link', () => {
    render(<SignIn />);
    expect(screen.getByText('Reset Password')).toBeInTheDocument();
  });

  it('renders checkbox and toggles its state', () => {
    render(<SignIn />);
    const checkbox = screen.getByTestId('checkout-checkbox') as HTMLInputElement;
    expect(checkbox).toBeChecked();
    fireEvent.click(checkbox);
    expect(checkbox).not.toBeChecked();
  });

  it('renders submit button', () => {
    render(<SignIn />);
    expect(screen.getByTestId('submit-button')).toHaveTextContent('Sign In');
  });

  it('updates email and password fields on change', () => {
    render(<SignIn />);
    const emailInput = screen.getByTestId('checkout-signin-email') as HTMLInputElement;
    const passwordInput = screen.getByTestId('checkout-sigin-password') as HTMLInputElement;

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    expect(emailInput.value).toBe('<EMAIL>');

    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    expect(passwordInput.value).toBe('password123');
  });

  it('closes modal when close button is clicked', () => {
    render(<SignIn />);
    const closeButton = screen.getByTestId('close-modal-button');
    fireEvent.click(closeButton);
    expect(closeButton).toBeInTheDocument();
  });

  it('handles checkbox logic correctly', () => {
    render(<SignIn />);
    const checkbox = screen.getByTestId('checkout-checkbox') as HTMLInputElement;

    expect(checkbox).toBeChecked();

    fireEvent.click(checkbox);
    expect(checkbox).not.toBeChecked();

    fireEvent.click(checkbox);
    expect(checkbox).toBeChecked();
  });

  it('submits email and password to sign in', async () => {
    (validateAccount as jest.Mock).mockReturnValue({ pingResponse: { payload: { resumeUrl: 'https://MOCK_URL.com' } } });

    const { container } = render(<SignIn />);
    const emailInput = screen.getByTestId('checkout-signin-email');
    const passwordInput = screen.getByTestId('checkout-sigin-password');
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'Password123!' } });
    const form = container.querySelector('form');
    fireEvent.submit(form!);

    await waitFor(() => {
      expect(validateAccount).toHaveBeenCalled();
      expect(setSessionCookie).toHaveBeenCalled();
    });
  });

  it('does not sign in if error on validation', async () => {
    (validateAccount as jest.Mock).mockReturnValue({ error: { payload: { code: 'VALIDATION_ERROR' } } });

    const { container } = render(<SignIn />);
    const emailInput = screen.getByTestId('checkout-signin-email');
    const passwordInput = screen.getByTestId('checkout-sigin-password');
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'Password123!' } });
    const form = container.querySelector('form');
    fireEvent.submit(form!);

    await waitFor(() => {
      expect(validateAccount).toHaveBeenCalled();
      expect(setSessionCookie).not.toHaveBeenCalled();
    });
  });

  it('calls fireTealiumLinkTag on successful sign in', async () => {
    (validateAccount as jest.Mock).mockReturnValue({ pingResponse: { payload: { resumeUrl: 'https://MOCK_URL.com' } } });

    const { container } = render(<SignIn />);
    fireEvent.change(screen.getByTestId('checkout-signin-email'), { target: { value: '<EMAIL>' } });
    fireEvent.change(screen.getByTestId('checkout-sigin-password'), { target: { value: 'Password123!' } });
    const form = container.querySelector('form');
    fireEvent.submit(form!);

    await waitFor(() => {
      expect(firePageViewTag).toHaveBeenCalledWith('signin', { event_name: 'long_live_signin', page_type: 'checkout' });
      expect(fireTealiumLinkTag).toHaveBeenCalledWith(expect.objectContaining({ event_name: 'long_live_signin_click', module: 'signin' }));
    });
  });

  it('calls fireTealiumLinkTag on failed sign in', async () => {
    (validateAccount as jest.Mock).mockReturnValue({ pingResponse: { payload: { code: 'VALIDATION_ERROR' } } });

    const { container } = render(<SignIn />);
    fireEvent.change(screen.getByTestId('checkout-signin-email'), { target: { value: '<EMAIL>' } });
    fireEvent.change(screen.getByTestId('checkout-sigin-password'), { target: { value: 'Password123!' } });
    const form = container.querySelector('form');
    fireEvent.submit(form!);

    await waitFor(() => {
      expect(firePageViewTag).toHaveBeenCalledWith('signin', { event_name: 'long_live_signin', page_type: 'checkout' });
      expect(fireTealiumLinkTag).toHaveBeenCalledWith(expect.objectContaining({ event_name: 'sign_in_failure', module: 'signin' }));
    });
  });

  it('calls fireTealiumLinkTag on reset password', async () => {
    (validateAccount as jest.Mock).mockReturnValue({ pingResponse: { payload: { resumeUrl: 'https://MOCK_URL.com' } } });

    render(<SignIn />);
    fireEvent.click(screen.getByText('Reset Password'));

    await waitFor(() => {
      expect(firePageViewTag).toHaveBeenCalledWith('signin', { event_name: 'long_live_signin', page_type: 'checkout' });
      expect(fireTealiumLinkTag).toHaveBeenCalledWith(expect.objectContaining({ event_name: 'forgot_password', module: 'signin' }));
    });
  });
});
