import React, { useState, useEffect } from 'react';
import { ModalDrawerWrapper } from '@ecom-next/core/migration/modal';
import { Banner } from '@ecom-next/my-account/components/sign-in/banner';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { Button } from '@ecom-next/core/migration/button';
import { DrawerSlidePosition } from '@ecom-next/core/migration/drawer';
import { Checkbox } from '@ecom-next/core/migration/checkbox';
import { PasswordInput } from '@ecom-next/my-account/components/common/PasswordInput';
import { EmailInput } from '@ecom-next/my-account/components/common/EmailInput';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { setSessionCookie } from '@ecom-next/shopping-bag/utils';
import { useCheckoutFeatures } from '../../contexts/checkoutFeatures/useCheckoutFeatures';
import { firePageViewTag as firePageViewTagOld, fireTealiumLinkTag as fireTealiumLinkTagOld } from '../../utils/tealium-utils';
import { useDatalayer } from '../../hooks/useDatalayer';
import { useBreakpoint } from '../utils/useBreakPoint';
import { createFlowID } from './createFlowID';
import { validateAccount } from './validateAccount';

type FieldState = {
  errorMessage: string;
  hasError: boolean;
  value: string;
};

interface FormFields {
  email: FieldState;
  password: FieldState;
}

type SignInProps = {
  onClose?: () => void;
};

// Prakash suggested a 10 minute timeout for this cookie
// https://gapinc-hq.slack.com/archives/C03E1UQ9CDT/p1752192788005679?thread_ts=**********.946499&cid=C03E1UQ9CDT
const REDIRECT_COOKIE_EXPIRY = 600000;

const SignIn = ({ onClose }: SignInProps) => {
  const { localize } = useLocalize();
  const { isMobile = false } = useBreakpoint();

  const [isOpen, setIsOpen] = useState(true);
  const [isCheckBoxChecked, setIsCheckBoxChecked] = useState(true);
  const [isButtonLoading, setIsButtonLoading] = useState(false);

  const { isTealiumMigrationEnabled } = useCheckoutFeatures();
  const { firePageViewTag: firePageViewTagNew, fireTealiumLinkTag: fireTealiumLinkTagNew } = useDatalayer();
  const firePageViewTag = isTealiumMigrationEnabled ? firePageViewTagNew : firePageViewTagOld;
  const fireTealiumLinkTag = isTealiumMigrationEnabled ? fireTealiumLinkTagNew : fireTealiumLinkTagOld;

  const [formFields, setFormFields] = useState<FormFields>({
    email: {
      value: '',
      errorMessage: '',
      hasError: false,
    },
    password: {
      value: '',
      errorMessage: '',
      hasError: false,
    },
  });

  const handleEmailChange = (value: string, errorMessage?: string) => {
    setFormFields(prev => ({
      ...prev,
      email: {
        value: value,
        errorMessage: errorMessage || '',
        hasError: !!errorMessage,
      },
    }));
  };

  const handlePasswordChange = (value: string, errorMessage?: string) => {
    setFormFields(prev => ({
      ...prev,
      password: {
        value: value,
        errorMessage: errorMessage || '',
        hasError: !!errorMessage,
      },
    }));
  };

  const handleResetPassword = (event?: React.FormEvent<HTMLButtonElement>) => {
    event?.preventDefault();

    fireTealiumLinkTag({
      event_name: 'forgot_password',
      brandShortName: brandAbbr,
      countryCode: market,
      module: 'signin',
    });
  };

  const pageContext = usePageContext();
  const { market = 'us', brandAbbr, locale, ecomApiBaseUrl } = pageContext;

  const handleSubmit = (event?: React.FormEvent<HTMLFormElement>) => {
    event?.preventDefault();

    const init = async () => {
      try {
        setIsButtonLoading(true);
        const { email, password } = formFields;

        const { uniqueIDResponse } = await createFlowID({ brandAbbr, ecomApiBaseUrl, locale, market, uiv: 'checkoutUI', email: email.value });
        if (!uniqueIDResponse) throw new Error('Error creating unique ID');

        const { pingResponse, error } = await validateAccount({
          flowId: uniqueIDResponse.payload.id,
          email: email.value,
          password: password.value,
          isLLSIEnabled: isCheckBoxChecked,
          thisIsMyDevice: isCheckBoxChecked,
          ecomApiBaseUrl,
        });

        if (!pingResponse?.payload?.resumeUrl) {
          if (error?.payload?.code === 'VALIDATION_ERROR') handlePasswordChange(password.value, localize('accountLogin.createAccount.passwordPreExistError'));
          throw new Error('Error signing in user');
        }

        fireTealiumLinkTag({
          event_name: 'long_live_signin_click',
          brandShortName: brandAbbr,
          countryCode: market,
          module: 'signin',
        });
        setSessionCookie('triage_params', JSON.stringify({ targetURL: `https://${window.location.host}/checkout` }), REDIRECT_COOKIE_EXPIRY);
        window.location.href = pingResponse.payload.resumeUrl;
      } catch (e) {
        fireTealiumLinkTag({
          event_name: 'sign_in_failure',
          brandShortName: brandAbbr,
          countryCode: market,
          module: 'signin',
        });
        setIsButtonLoading(false);
      }
    };

    init();
  };

  useEffect(() => {
    firePageViewTag('signin', { event_name: 'long_live_signin', page_type: 'checkout' });
  }, []);

  return (
    <ModalDrawerWrapper
      position={DrawerSlidePosition.bottom}
      isLengthyContent={false}
      isOpen={isOpen}
      onClose={() => {
        setIsOpen(false);
        onClose?.();
      }}
      title={localize('checkout.signin')}
      closeButtonAriaLabel='close-modal'
      isMobile={isMobile}
      closeIcon
      hasRoundedCorners={true}
    >
      <div className='mb-4' data-testid='sign-in-banner'>
        <Banner isCheckout={true} />
      </div>
      <form onSubmit={handleSubmit}>
        <div className='flex flex-col gap-4'>
          <div>
            <EmailInput
              data-testid='checkout-signin-email'
              id='sign-in-email'
              name='sign-in-email'
              label={localize('email')}
              autoComplete='email'
              value={formFields.email.value}
              hasError={formFields.email.hasError}
              errorMessage={formFields.email.errorMessage}
              onChange={handleEmailChange}
              onBlur={handleEmailChange}
            />
          </div>
          <div>
            <PasswordInput
              data-testid='checkout-sigin-password'
              id='sign-in-password'
              name='sign-in-password'
              label={localize('checkout.password')}
              autoComplete='current-password'
              value={formFields.password.value}
              hasError={formFields.password.hasError}
              errorMessage={formFields.password.errorMessage}
              onChange={handlePasswordChange}
              onBlur={handlePasswordChange}
            />
          </div>
          <p className='cb-base-compact items-center'>
            <button className='underline' onClick={handleResetPassword}>
              {localize('checkout.resetPassword')}
            </button>
          </p>
          <div className='cb-base-compact'>
            <Checkbox
              labelText={localize('checkout.checkboxText')}
              isChecked={isCheckBoxChecked}
              onChange={() => {
                setIsCheckBoxChecked(!isCheckBoxChecked);
              }}
            />
          </div>
          <div>
            <Button type='submit' fullWidth={true} kind='primary' isDisabled={false} loadingAnimationStatus={isButtonLoading}>
              {localize('checkout.signin')}
            </Button>
          </div>
        </div>
      </form>
    </ModalDrawerWrapper>
  );
};

export default SignIn;
