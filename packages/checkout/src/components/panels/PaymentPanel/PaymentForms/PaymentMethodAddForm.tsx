import React, { useMemo, useEffect } from 'react';
import classNames from 'classnames';
import { Checkbox } from '@ecom-next/core/migration/checkbox';
import { Button } from '@ecom-next/core/migration/button';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { FormValidation, SubmitValidation, useFormValidation } from '@ecom-next/my-account/validation';
import { getStateByAbbrWithLocale } from '@ecom-next/my-account/addressUtil';
import { Notification } from '@ecom-next/core/migration/notification';
import { Kind } from '@ecom-next/core/migration/notification/types';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { usePayments } from '../../../../contexts/PaymentsProvider';
import { useCheckout } from '../../../../contexts/CheckoutProvider';
import { CreditCardInputField } from '../PaymentFormFields/CreditCardInputField';
import { CvvInputField } from '../PaymentFormFields/CvvInputField';
import { ExpiryInputField } from '../PaymentFormFields/ExpiryInputField';
import { BillingAddressSameAsShipping } from '../BillingAddress/BillingAddressSameAsShipping';
import { filterSelectedPaymentMethod } from '../utils/filterSelectedPaymentMethod';
import { getCardType, isBarclaysPLCC, isTempCard } from '../utils/utils';
import { usePaymentError } from '../utils/usePaymentError';
import { CREDIT_CARD_429_ERROR } from '../utils/errorCodeConstants';
import { normalizePhone } from '../../../../utils/normalize';
import { usePanelModalDrawer } from '../../../layout/PanelModalDrawer';
import { USER_STATES } from '../../../../utils/constants';
import { AMEX_ID } from '../cardIds';
import { useAddPaymentForm } from './useAddPaymentForm';
import { PaymentMethodAddState } from './types';

export const PaymentMethodAddFormInternal = () => {
  const { draftOrder } = useCheckout();
  const isGuestCustomer = draftOrder?.session?.recognition_status === USER_STATES.GUEST;
  const { paymentMethods } = draftOrder?.paymentPanel || {};
  const { shippingAddressList } = draftOrder?.shippingAddressPanel || {};
  const { bopisOnlyBag } = draftOrder?.paymentPanel?.conditionals || {};

  const { paymentsState, setPaymentsState } = usePayments() || {};
  const { modalOpen } = usePanelModalDrawer();
  // Below value will be accessed from paymentsState.
  const { editClickState, selectedPaymentMethodState, disabledPaymentMethod, disabledPaymentMethodId, actionSource } = paymentsState || {};
  const selectedCreditCardId = selectedPaymentMethodState.id;
  const isUpsAccessPoint = shippingAddressList?.some(address => address.addressLocationType === 'UPS' && address.isSelected);
  const selectedAddress = shippingAddressList?.find(address => address.isSelected);
  const selectedPaymentBillingAddress = filterSelectedPaymentMethod({ paymentMethods, currentSelectedPaymentId: selectedCreditCardId })?.billingAddress;
  const { firstName, lastName, phone, addressLine1, addressLine2, state = '', city, postalCode } = selectedPaymentBillingAddress || {};

  const { formData, setFormValue, setFormValues, setOnSubmit } = useFormValidation<PaymentMethodAddState>();
  const isBCSPLCC = useMemo(() => isBarclaysPLCC(formData.cardNumber?.value, formData.cardType?.value), [formData]);
  const isTemporaryCard = useMemo(() => isTempCard(formData.cardNumber?.value, formData.temporaryCardIndicator?.value), [formData]);
  const isAmericanExpress = useMemo(() => {
    const cardType = formData.cardType?.value || getCardType(formData.cardNumber?.value?.replace(/\s/g, ''));
    return cardType === AMEX_ID;
  }, [formData]);

  const showCVVField = isTemporaryCard ? false : editClickState || (formData.cvvRequired?.value ?? true);

  const cardInfoHeaderTextClass = classNames('cb-display-sm h-[19px] font-bold leading-[19px]');

  const handleSetAsDefaultCard = () => {
    setFormValue('creditCardDefaultCheckbox', !formData?.creditCardDefaultCheckbox?.value);
  };

  const { locale = 'en_US', market = 'us' } = usePageContext();
  const { localize } = useLocalize();
  const defaultCardCheckBoxlabel = localize('payment.paymentMethodAddForm.creditCard.checkBox.labelText');
  const saveText = localize('payment.paymentMethodAddForm.button.saveText');
  const cardInfoText = localize('payment.paymentMethodAddForm.labelText');
  const continueText = localize('payment.paymentInlineForm.continue');
  const hasPaymentMethods = paymentMethods && paymentMethods?.length > 0;
  const selectedCreditCardFromDraftOrder = paymentMethods?.find(payment => payment.isSelected);

  useEffect(() => {
    return () => {
      setPaymentsState({
        editClickState: false,
        selectedPaymentMethodState: { entityType: 'creditCard', id: selectedCreditCardFromDraftOrder?.cardInfo?.cardId || null },
      });
      // Preserve 429 error when modal closes, clear other errors
      if (paymentsState?.uiError && paymentsState.uiError !== CREDIT_CARD_429_ERROR) {
        setPaymentsState({
          uiError: null,
        });
      }
    };
  }, []);

  const { onAddPayment } = useAddPaymentForm({
    formType: editClickState ? 'edit' : 'add',
    paymentMethodId: editClickState ? selectedCreditCardId : null,
    selectedCreditCardId,
    selectedShippingAddress: selectedAddress,
    isBillingAddressDifferent: isUpsAccessPoint || bopisOnlyBag || !formData?.billingAddressCheckbox?.value,
  });

  const { errorMessage, bannerType } = usePaymentError();

  const editingPaymentMethodId = editClickState ? selectedCreditCardId : null;
  const isCurrentPaymentMethodDisabled = disabledPaymentMethod === 'edit' && disabledPaymentMethodId === editingPaymentMethodId;

  const localized429Message = localize('payment.addCard.statusCode.429');
  
  let extendedErrMessage = '';
  if ((disabledPaymentMethod === 'add' && !editClickState) || isCurrentPaymentMethodDisabled) {
    extendedErrMessage = localized429Message;
  } else if (errorMessage && errorMessage !== localized429Message) {
    extendedErrMessage = errorMessage;
  }

  const errMsg =
    extendedErrMessage ||
    (editClickState && !isCurrentPaymentMethodDisabled && !isTemporaryCard
      ? localize('payment.paymentErrorMessage.710.716')
      : '');
  const modalBannerType =
    extendedErrMessage
      ? (errMsg === localized429Message ? 'error' : bannerType)
      : 'warning';

  useEffect(() => {
    if (!editClickState || formData.billingAddressCheckbox?.value) return;
    setFormValues({
      fullName: `${firstName} ${lastName}`,
      addressLine1,
      addressLine2,
      city,
      state: getStateByAbbrWithLocale(state, market, locale),
      postalCode,
      phone: normalizePhone(phone || ''),
    });
  }, [formData.billingAddressCheckbox?.value]);

  useEffect(() => setOnSubmit(onAddPayment), [formData]);

  const isCardNumberEntered = (formData.cardNumber?.value?.length ?? 0) > 7;

  return (
    <>
      {errMsg && modalOpen && (
        <div className='pb-4'>
          <Notification isDismissible={false} kind={modalBannerType as Kind} showIcon={true} inline={false} dismissButtonLabel=''>
            {errMsg}
          </Notification>{' '}
        </div>
      )}

      <>
        {hasPaymentMethods && (
          <div className='h-[19px] w-[480px] gap-8'>
            <p className={cardInfoHeaderTextClass}>{cardInfoText}</p>
          </div>
        )}
        <div className='flex flex-col gap-4 pb-4 pt-4'>
          <div className='max-h-[164px] gap-8'>
            <CreditCardInputField />
            <div>
              <div className='flex w-full'>
                {!isBCSPLCC && (
                  <div className='w-1/2 pt-4'>
                    <ExpiryInputField
                      validateOnRender={editClickState}
                      disableAfterBlurValidation={editClickState}
                      isBCSPLCC={isBCSPLCC}
                      isTemporaryCard={isTemporaryCard}
                    />
                  </div>
                )}
                {/* Below right margin is unset when either EXP or CVV is shown */}
                <div
                  className={classNames({
                    'pr-0': isBCSPLCC && isTemporaryCard,
                    'pr-4': !isBCSPLCC && !isTemporaryCard,
                  })}
                ></div>
                {showCVVField && (
                  <div className='w-1/2 pt-4'>
                    <CvvInputField
                      isAmericanExpress={isAmericanExpress}
                      validateOnRender={editClickState}
                      disableAfterBlurValidation={editClickState}
                      isTemporaryCard={isTemporaryCard}
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
          {!isGuestCustomer && ( //If guest user, Form will not show set as default card checkbox.
            <div className='flex flex-col gap-4'>
              <div className='cb-base-compact' data-testid='default-card-checkbox'>
                <Checkbox
                  onKeyDown={e => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      handleSetAsDefaultCard();
                    }
                  }}
                  onChange={handleSetAsDefaultCard}
                  labelText={defaultCardCheckBoxlabel}
                  isChecked={formData?.creditCardDefaultCheckbox?.value}
                />
              </div>
            </div>
          )}
        </div>
        {isCardNumberEntered &&
          (selectedAddress || bopisOnlyBag || isUpsAccessPoint || shippingAddressList?.length) && ( //If credit card input is more than 7 character, decide on showing Billing address and checkbox.
            <div
              className={classNames({
                'pb-4 pt-4': !isTemporaryCard,
                'pb-4 pr-0 pt-4': isTemporaryCard && !isGuestCustomer,
                'pt-0': isGuestCustomer && !isTemporaryCard,
              })}
            >
              <BillingAddressSameAsShipping selectedAddress={selectedAddress!} editClickState={editClickState} />
            </div>
          )}
        <div className='gap[10px] h-[44px]'>
          <SubmitValidation keepEnabledButValidateFirst>
            {props => {
              let isDisabled = false;
              
              if (disabledPaymentMethod !== undefined && actionSource !== undefined) {
                isDisabled = editClickState
                  ? disabledPaymentMethod === 'edit' && disabledPaymentMethodId === selectedCreditCardId
                  : disabledPaymentMethod === actionSource;
              }

              if(paymentMethods.length === 0) {
                isDisabled = disabledPaymentMethod === 'add';
              }

              return (
                <Button data-testid='card-save-button' fullWidth={true} kind='primary' {...props} isDisabled={isDisabled}>
                  {isGuestCustomer ? continueText : saveText}
                </Button>
              );
            }}
          </SubmitValidation>
        </div>
      </>
    </>
  );
};

export const PaymentMethodAddForm = () => {
  const { draftOrder } = useCheckout();
  const { paymentMethods } = draftOrder?.paymentPanel || {};
  // ***************************************
  // TODO: check this logic here and fix it.
  // ***************************************
  const { paymentsState } = usePayments() || {};
  const { closeModal } = usePanelModalDrawer();
  const { localize } = useLocalize();
  const cancelText = localize('payment.paymentMethodAddForm.button.cancelText');
  const selectedCreditCardId = paymentsState.selectedPaymentMethodState.id;
  const editPaymentItem = paymentMethods?.find(payment => payment.cardInfo?.cardId === selectedCreditCardId);
  const {
    cardNumber = '',
    cardType,
    cvvRequired,
    temporaryCardIndicator = false,
    expirationMonth = '',
    expirationYear = '',
    defaultCard = false,
  } = editPaymentItem?.cardInfo || {};
  const expiration = expirationMonth && expirationYear ? `${expirationMonth}/${expirationYear?.substring(2)}` : '';
  const bopisOnlyBag = true;

  const handleCancelButton = () => {
    closeModal();
  };

  const initialData: PaymentMethodAddState = {
    cardNumber,
    cardType,
    cvvRequired,
    expiration,
    cvv: '',
    temporaryCardIndicator,
    bopisOnlyBag,
    creditCardDefaultCheckbox: defaultCard,
    billingAddressCheckbox: !editPaymentItem, // true when creating new card, false when editing existing card.
  };

  return (
    <div role='form'>
      <FormValidation initialData={initialData}>
        <PaymentMethodAddFormInternal />
      </FormValidation>
      {paymentMethods?.length > 0 && (
        <div className='pt-4'>
          <Button data-testid='card-details-cancel-button' fullWidth={true} kind='flat' onClick={handleCancelButton}>
            {cancelText}
          </Button>
        </div>
      )}
    </div>
  );
};
