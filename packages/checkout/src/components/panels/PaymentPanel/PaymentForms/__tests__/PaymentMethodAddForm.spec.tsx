import React from 'react';
import { waitFor } from '@testing-library/react';
import { render, fireEvent } from '../../../../../utils/test-utils';
import { useCheckout } from '../../../../../contexts/CheckoutProvider';
import { usePaymentError } from '../../utils/usePaymentError';
import { PaymentMethod } from '../../../../../contexts/types';
import { PanelModalDrawer } from '../../../../layout/PanelModalDrawer';
import { PaymentMethodAddForm } from '../PaymentMethodAddForm';
import { useAddPaymentForm } from '../useAddPaymentForm';

jest.mock('@ecom-next/sitewide/hooks/usePageContext', () => ({
  usePageContext: jest.fn(() =>
    Promise.resolve({
      pageContext: {
        ecomApiBaseUrl: 'https://ecom-api.stage.gaptechol.com/commerce/vault/vault-entries',
      },
    })
  ),
}));

jest.mock('@ecom-next/checkout/PaymentsProvider', () => ({
  usePayments: jest.fn().mockReturnValue({
    paymentsState: { selectedPaymentMethodState: { entityType: 'creditCard' }, id: '123', editClickState: true },
    setPaymentsState: jest.fn(),
  }),
}));

jest.mock('../../../../../contexts/CheckoutProvider', () => ({
  ...jest.requireActual('../../../../../contexts/CheckoutProvider'),
  useCheckout: jest.fn().mockReturnValue({}),
}));

jest.mock('../../utils/usePaymentError', () => ({
  ...jest.requireActual('../../utils/usePaymentError'),
  usePaymentError: jest.fn().mockReturnValue({
    errorMessage: '',
    bannerType: '',
  }),
}));

jest.mock('../../../../layout/PanelModalDrawer', () => ({
  ...jest.requireActual('../../../../layout/PanelModalDrawer'),
}));

const mockSetUiStates = jest.fn();
jest.mock('../../../../../contexts/CheckoutUiStateProvider', () => ({
  ...jest.requireActual('../../../../../contexts/CheckoutUiStateProvider'),
  useCheckoutUiStates: jest.fn(() => ({
    setUiStates: mockSetUiStates,
    overlayDetails: { isOpen: true },
  })),
}));

const mockOnAddPayment = jest.fn();

jest.mock('../../PaymentForms/useAddPaymentForm', () => ({
  useAddPaymentForm: jest.fn(() => ({
    onAddPayment: mockOnAddPayment,
  })),
}));

describe('PaymentMethodAddForm', () => {
  const paymentMethods = [
    {
      isSelected: true,
      cardInfo: { cardNumber: 'XXXX-XXXX-XXXX-1234', cardType: 1, cvvRequired: true, lastFourDigits: '1234', expirationMonth: '12', expirationYear: '2034' },
      billingAddress: {
        addressId: '56ef6cb806bd42c9a69e0b595487b0ff',
        firstName: 'Darrick',
        lastName: 'Satterfield',
        addressLine1: '79764 Rath Vista',
        postalCode: '94123',
        city: 'Genabury',
        state: 'KS',
        country: 'US',
        phone: '**********',
      },
    },
    {
      isSelected: false,
      cardInfo: { cardNumber: 'XXXX-XXXX-XXXX-5678', cardType: 2, lastFourDigits: '5678' },
      billingAddress: {
        addressId: '2f83bd6f505e4cf8aa8315cc54a21f7e',
        firstName: 'N',
        lastName: 'K',
        addressLine1: '118 N State St',
        postalCode: '60602',
        city: 'Chicago',
        state: 'IL',
        country: 'US',
        phone: '8454958498',
      },
    },
  ] as unknown as PaymentMethod[];

  (useCheckout as jest.Mock).mockReturnValue({
    draftOrder: {
      paymentPanel: {
        conditionals: { bopisOnlyBag: true },
        paymentMethods,
        errors: [],
      },
      shippingAddressPanel: {
        shippingAddressList: [{
          addressId: '145267',
          addressLine1: '123 Main St',
          city: 'New York',
          country: 'USA',
          postalCode: '12345',
          state: 'NY',
          phone: '************',
          isSelected: true,
          addressLocationType: 'UPS'
        }]
      },
    },
  });

  afterAll(() => {
    jest.restoreAllMocks();
  });

  it('should render the Add form with only CC field', () => {
    const { getByLabelText, getByText } = render(
      <PanelModalDrawer.Provider>
        <PaymentMethodAddForm />
      </PanelModalDrawer.Provider>
    );

    expect(getByLabelText('Card Number')).toBeInTheDocument();
    expect(getByText('Save')).toBeInTheDocument();
    expect(getByText('Cancel')).toBeInTheDocument();
  });

  describe("Temp Card", () => {
    it('should hide CVV field when user adds CBCC Temp card', () => {
      const { getByLabelText, getByText, queryByText } = render(
        <PanelModalDrawer.Provider>
          <PaymentMethodAddForm />
        </PanelModalDrawer.Provider>
      );
      const cardNumInput = getByLabelText('Card Number') as HTMLInputElement;

      fireEvent.input(cardNumInput, { target: { value: '5598-3204-5376-0540' } });

      expect(cardNumInput.value).toBe('5598-3204-5376-0540');
      expect(getByText('MM/YY')).toBeInTheDocument();
      expect(getByLabelText('Set as Default Card')).toBeInTheDocument();
      expect(getByText('Billing Address')).toBeInTheDocument();
      expect(getByText('Save')).toBeInTheDocument();
      expect(getByText('Cancel')).toBeInTheDocument();
      expect(queryByText('Security Code')).not.toBeInTheDocument();
    });

    it('should hide CVV field when user adds AT CBCC Temp card', () => {
      const { getByLabelText, getByText } = render(
        <PanelModalDrawer.Provider>
          <PaymentMethodAddForm />
        </PanelModalDrawer.Provider>
      );
      const cardNumInput = getByLabelText('Card Number') as HTMLInputElement;

      fireEvent.input(cardNumInput, { target: { value: '5598-3204-5376-0540' } });

      expect(cardNumInput.value).toBe('5598-3204-5376-0540');
      expect(getByText('MM/YY')).toBeInTheDocument();
      expect(getByLabelText('Set as Default Card')).toBeInTheDocument();
      expect(getByText('Billing Address')).toBeInTheDocument();
      expect(getByText('Save')).toBeInTheDocument();
      expect(getByText('Cancel')).toBeInTheDocument();
    });
  });

  it('should hide EXP field when user adds ON PLCC Permanent card', () => {
    const { getByLabelText, getByText } = render(
      <PanelModalDrawer.Provider>
        <PaymentMethodAddForm />
      </PanelModalDrawer.Provider>
    );
    const cardNumInput = getByLabelText('Card Number') as HTMLInputElement;

    fireEvent.input(cardNumInput, { target: { value: '5397-7200-3668-5080' } });
    expect(cardNumInput.value).toBe('5397-7200-3668-5080');
    expect(getByText('Security Code')).toBeInTheDocument();
    expect(getByLabelText('Set as Default Card')).toBeInTheDocument();
    expect(getByText('Billing Address')).toBeInTheDocument();
    expect(getByText('Save')).toBeInTheDocument();
    expect(getByText('Cancel')).toBeInTheDocument();
  });

  it('should show all fields When user adds Visa CC', () => {
    const { getByLabelText, getByText } = render(
      <PanelModalDrawer.Provider>
        <PaymentMethodAddForm />
      </PanelModalDrawer.Provider>
    );
    const cardNumInput = getByLabelText('Card Number') as HTMLInputElement;

    fireEvent.input(cardNumInput, { target: { value: '4111-1111-1111-1111' } });
    expect(cardNumInput.value).toBe('4111-1111-1111-1111');
    expect(getByText('MM/YY')).toBeInTheDocument();
    expect(getByText('Security Code')).toBeInTheDocument();
    expect(getByLabelText('Set as Default Card')).toBeInTheDocument();
    expect(getByText('Billing Address')).toBeInTheDocument();
    expect(getByText('Save')).toBeInTheDocument();
    expect(getByText('Cancel')).toBeInTheDocument();
  });

  it('should not wipe out billingAddress for guest customer', () => {
    (useCheckout as jest.Mock).mockReset();
    (useCheckout as jest.Mock).mockReturnValue({
      draftOrder: {
        session: { recognition_status: 'guest' },
        paymentPanel: {
          conditionals: { bopisOnlyBag: true },
          paymentMethods,
          errors: [],
        },
        shippingAddressPanel: {
          shippingAddressList: [{
            addressId: '145267',
            addressLine1: '123 Main St',
            city: 'New York',
            country: 'USA',
            postalCode: '12345',
            state: 'NY',
            phone: '************',
            isSelected: true,
            addressLocationType: 'UPS'
          }]
        },
      },
    });

    const { getByLabelText } = render(
      <PanelModalDrawer.Provider>
        <PaymentMethodAddForm />
      </PanelModalDrawer.Provider>
    );

    const cardNumInput = getByLabelText('Card Number') as HTMLInputElement;

    fireEvent.input(cardNumInput, { target: { value: '4111-1111-1111-1111' } });
    expect(cardNumInput.value).toBe('4111-1111-1111-1111');

    const streetAddress = getByLabelText('Street Address') as HTMLInputElement;

    expect(streetAddress.value).toBe('79764 Rath Vista');
  });

  it('should hide checkbox if bopisOnlyBag', () => {
    (useCheckout as jest.Mock).mockReset();
    (useCheckout as jest.Mock).mockReturnValue({
      draftOrder: {
        paymentPanel: {
          conditionals: { bopisOnlyBag: true },
          paymentMethods,
          errors: [],
        },
        shippingAddressPanel: {
          shippingAddressList: [{
            addressId: '145267',
            addressLine1: '123 Main St',
            city: 'New York',
            country: 'USA',
            postalCode: '12345',
            state: 'NY',
            phone: '************',
            isSelected: true,
            addressLocationType: 'UPS'
          }]
        },
      },
    });

    const { getByLabelText, getByText } = render(
      <PanelModalDrawer.Provider>
        <PaymentMethodAddForm />
      </PanelModalDrawer.Provider>
    );
    const cardNumInput = getByLabelText('Card Number') as HTMLInputElement;

    fireEvent.input(cardNumInput, { target: { value: '4111-1111-1111-1111' } });
    expect(cardNumInput.value).toBe('4111-1111-1111-1111');
    expect(getByText('MM/YY')).toBeInTheDocument();
    expect(getByText('Security Code')).toBeInTheDocument();
    expect(getByLabelText('Set as Default Card')).toBeInTheDocument();
    expect(getByText('Billing Address')).toBeInTheDocument();
    expect(getByText('Full Name')).toBeInTheDocument();
    expect(getByText('Street Address')).toBeInTheDocument();
    expect(getByText('Street Address')).toBeInTheDocument();
    expect(getByText('Apt #')).toBeInTheDocument();
    expect(getByText('Town/City')).toBeInTheDocument();
    expect(getByText('Town/City')).toBeInTheDocument();
    expect(getByText('State')).toBeInTheDocument();
    expect(getByText('Phone Number')).toBeInTheDocument();
    expect(getByText('Save')).toBeInTheDocument();
    expect(getByText('Cancel')).toBeInTheDocument();
  });

  it('should hide checkbox AND show billing address form if multi store bopis only bag', () => {
    (useCheckout as jest.Mock).mockReset();
    (useCheckout as jest.Mock).mockReturnValue({
      draftOrder: {
        paymentPanel: {
          conditionals: { bopisOnlyBag: true },
          paymentMethods,
          errors: [],
        },
        pickupPanel: { storePickupInfoList: [{ storeId: '123' }, { storeId: '456' }] },
      },
    });

    const { getByLabelText, getByText } = render(
      <PanelModalDrawer.Provider>
        <PaymentMethodAddForm />
      </PanelModalDrawer.Provider>
    );
    const cardNumInput = getByLabelText('Card Number') as HTMLInputElement;

    fireEvent.input(cardNumInput, { target: { value: '4111-1111-1111-1111' } });
    expect(cardNumInput.value).toBe('4111-1111-1111-1111');
    expect(getByText('MM/YY')).toBeInTheDocument();
    expect(getByText('Security Code')).toBeInTheDocument();
    expect(getByLabelText('Set as Default Card')).toBeInTheDocument();
    expect(getByText('Billing Address')).toBeInTheDocument();
    expect(getByText('Full Name')).toBeInTheDocument();
    expect(getByText('Street Address')).toBeInTheDocument();
    expect(getByText('Street Address')).toBeInTheDocument();
    expect(getByText('Apt #')).toBeInTheDocument();
    expect(getByText('Town/City')).toBeInTheDocument();
    expect(getByText('Town/City')).toBeInTheDocument();
    expect(getByText('State')).toBeInTheDocument();
    expect(getByText('Phone Number')).toBeInTheDocument();
    expect(getByText('Save')).toBeInTheDocument();
    expect(getByText('Cancel')).toBeInTheDocument();
  });

  it('should hide checkbox if isUpsAccessPoint', () => {
    (useCheckout as jest.Mock).mockReset();
    (useCheckout as jest.Mock).mockReturnValue({
      draftOrder: {
        paymentPanel: {
          conditionals: {},
          paymentMethods,
          errors: [],
        },
        shippingAddressPanel: {
          shippingAddressList: [{
            addressId: '145267',
            addressLine1: '123 Main St',
            city: 'New York',
            country: 'USA',
            postalCode: '12345',
            state: 'NY',
            phone: '************',
            isSelected: true,
            addressLocationType: 'UPS'
          }]
        },
      },
    });

    const { getByLabelText, getByText } = render(
      <PanelModalDrawer.Provider>
        <PaymentMethodAddForm />
      </PanelModalDrawer.Provider>
    );
    const cardNumInput = getByLabelText('Card Number') as HTMLInputElement;

    fireEvent.input(cardNumInput, { target: { value: '4111-1111-1111-1111' } });
    expect(cardNumInput.value).toBe('4111-1111-1111-1111');
    expect(getByText('MM/YY')).toBeInTheDocument();
    expect(getByText('Security Code')).toBeInTheDocument();
    expect(getByLabelText('Set as Default Card')).toBeInTheDocument();
    expect(getByText('Billing Address')).toBeInTheDocument();
    expect(getByText('Full Name')).toBeInTheDocument();
    expect(getByText('Street Address')).toBeInTheDocument();
    expect(getByText('Street Address')).toBeInTheDocument();
    expect(getByText('Apt #')).toBeInTheDocument();
    expect(getByText('Town/City')).toBeInTheDocument();
    expect(getByText('Town/City')).toBeInTheDocument();
    expect(getByText('State')).toBeInTheDocument();
    expect(getByText('Phone Number')).toBeInTheDocument();
    expect(getByText('Save')).toBeInTheDocument();
    expect(getByText('Cancel')).toBeInTheDocument();
  });

  describe('When user clicks edit on Payment method', () => {
    it('should show card number and fields as XXXX with last 4 digit.', () => {
      const { getByLabelText, getByText } = render(
        <PanelModalDrawer.Provider>
          <PaymentMethodAddForm />
        </PanelModalDrawer.Provider>
      );
      const cardNumInput = getByLabelText('Card Number') as HTMLInputElement;

      const expDateInput = getByLabelText('MM/YY', { exact: false }) as HTMLInputElement;

      expect(cardNumInput.value).toBe('XXXX-XXXX-XXXX-1234');
      expect(expDateInput.value).toBe('12/34');
      expect(getByText('MM/YY')).toBeInTheDocument();
      expect(getByText('Security Code')).toBeInTheDocument();
      expect(getByLabelText('Set as Default Card')).toBeInTheDocument();
      expect(getByText('Billing Address')).toBeInTheDocument();
      expect(getByText('Save')).toBeInTheDocument();
      expect(getByText('Cancel')).toBeInTheDocument();
      expect(getByText('Security Code')).toBeInTheDocument();
    });

    it('should hide CVV field if it is Temp card', () => {
      const { getByLabelText, getByText, queryByText } = render(
        <PanelModalDrawer.Provider>
          <PaymentMethodAddForm />
        </PanelModalDrawer.Provider>
      );
      const cardNumInput = getByLabelText('Card Number') as HTMLInputElement;

      fireEvent.input(cardNumInput, { target: { value: '5598-3204-5376-0540' } });

      expect(cardNumInput.value).toBe('5598-3204-5376-0540');
      expect(getByText('MM/YY')).toBeInTheDocument();
      expect(getByLabelText('Set as Default Card')).toBeInTheDocument();
      expect(getByText('Billing Address')).toBeInTheDocument();
      expect(getByText('Save')).toBeInTheDocument();
      expect(getByText('Cancel')).toBeInTheDocument();
      expect(queryByText('Security Code')).not.toBeInTheDocument();
    });
  });



  it('calls onAddPayment on form submission', async () => {
    const { getByText } = render(
      <PanelModalDrawer.Provider>
        <PaymentMethodAddForm />
      </PanelModalDrawer.Provider>
    );
    fireEvent.submit(getByText('Save'));
    expect(useAddPaymentForm).toHaveBeenCalled();
    await waitFor(() => expect(mockOnAddPayment).toHaveBeenCalled());
    expect(mockOnAddPayment).toHaveBeenCalled();
  });

  it('calls setUiStates and resets form on cancel', () => {
    const { getByText } = render(
      <PanelModalDrawer.Provider>
        <PaymentMethodAddForm />
      </PanelModalDrawer.Provider>
    );
    const cancelButton = getByText('Cancel');
    fireEvent.click(cancelButton);
    expect(mockSetUiStates).not.toHaveBeenCalled();
  });

  it('should pre-populate billing address from payment when edit is clicked and use delivery address is unchecked.', async () => {
    (useCheckout as jest.Mock).mockReturnValue({
      draftOrder: {
        paymentPanel: {
          conditionals: { bopisOnlyBag: false },
          paymentMethods,
          errors: [],
        },
        shippingAddressPanel: {
          shippingAddressList: [{
            addressId: '145267',
            addressLine1: '123 Main St',
            city: 'New York',
            country: 'USA',
            postalCode: '12345',
            state: 'NY',
            phone: '************',
            isSelected: true,
          }]
        },
      },
    });
    const { getByLabelText } = render(
      <PanelModalDrawer.Provider>
        <PaymentMethodAddForm />
      </PanelModalDrawer.Provider>
    );

    const useDeliveryAddress = getByLabelText('Use Delivery Address') as HTMLInputElement;
    expect(useDeliveryAddress).toBeInTheDocument();
    expect(useDeliveryAddress).not.toBeChecked();

    const fullName = getByLabelText('Full Name') as HTMLInputElement;
    const addressLine1 = getByLabelText('Street Address') as HTMLInputElement;
    const postalCode = getByLabelText('Zip Code') as HTMLInputElement;
    const city = getByLabelText('Town/City') as HTMLInputElement;
    const phone = getByLabelText('Phone Number') as HTMLInputElement;

    expect(fullName.value).toBe('Darrick Satterfield');
    expect(addressLine1.value).toBe('79764 Rath Vista');
    expect(postalCode.value).toBe('94123');
    expect(city.value).toBe('Genabury');
    expect(phone.value).toBe('(*************');
  });

  it('should select shipping address as billing address when creating a payment method', () => {
    (useCheckout as jest.Mock).mockReturnValue({
      draftOrder: {
        shippingAddressPanel: {
          shippingAddressList: [
            {
              addressId: '145267',
              addressLine1: '123 Main St',
              city: 'New York',
              country: 'USA',
              postalCode: '12345',
              state: 'NY',
              phone: '************',
              isDefault: false,
              isSelected: true,
              firstName: 'John',
              lastName: 'Doe',
            },
          ],
        },
        paymentPanel: {
          conditionals: { bopisOnlyBag: false },
          paymentMethods,
          errors: [],
        },
      },
    });

    const { getByLabelText } = render(
      <PanelModalDrawer.Provider>
        <PaymentMethodAddForm />
      </PanelModalDrawer.Provider>
    );

    const useDeliveryAddress = getByLabelText('Use Delivery Address') as HTMLInputElement;
    expect(useDeliveryAddress).toBeInTheDocument();
    fireEvent.click(useDeliveryAddress);
    expect(useDeliveryAddress).toBeChecked();
  });

  it('should hide cancel button if there are no payments', () => {
    (useCheckout as jest.Mock).mockReset();
    (useCheckout as jest.Mock).mockReturnValue({
      draftOrder: {
        paymentPanel: {
          conditionals: { bopisOnlyBag: true },
          paymentMethods: [],
          errors: [],
        },
        shippingAddressPanel: {
          shippingAddressList: [{
            addressId: '145267',
            addressLine1: '123 Main St',
            city: 'New York',
            country: 'USA',
            postalCode: '12345',
            state: 'NY',
            phone: '************',
            isSelected: true,
            addressLocationType: 'UPS'
          }]
        },
      },
    });

    const { getByText, queryByText } = render(
      <PanelModalDrawer.Provider>
        <PaymentMethodAddForm />
      </PanelModalDrawer.Provider>
    );

    expect(getByText('Save')).toBeInTheDocument();
    const cancelButton = queryByText('Cancel');
    expect(cancelButton).not.toBeInTheDocument();
  });

  it('renders Notification with correct error message and banner type when errMsg and modalOpen are true', () => {
    const panelModalDrawerSpy = jest.spyOn(require('../../../../layout/PanelModalDrawer'), 'usePanelModalDrawer');
    panelModalDrawerSpy.mockReturnValue({
      modalOpen: true,
      setModalOpen: jest.fn(),
      closeModal: jest.fn(),
      openModal: jest.fn(),
      setTitle: jest.fn(),
      title: '',
      isMobile: false,
    });
    (usePaymentError as jest.Mock).mockReturnValue({ errorMessage: 'Test error', bannerType: 'warning' });
    (useCheckout as jest.Mock).mockReturnValue({
      draftOrder: {
        paymentPanel: {
          conditionals: { bopisOnlyBag: true },
          paymentMethods,
          errors: [],
        },
        shippingAddressPanel: {
          shippingAddressList: [
            {
              addressId: '145267',
              addressLine1: '123 Main St',
              city: 'New York',
              country: 'USA',
              postalCode: '12345',
              state: 'NY',
              phone: '************',
              isSelected: true,
              addressLocationType: 'UPS',
            },
          ],
        },
      },
    });
    jest.doMock('@ecom-next/checkout/PaymentsProvider', () => ({
      usePayments: jest.fn().mockReturnValue({
        paymentsState: {
          selectedPaymentMethodState: { entityType: 'creditCard', id: '123' },
          disabledPaymentMethod: undefined,
          editClickState: false,
        },
        setPaymentsState: jest.fn(),
      }),
    }));

    const { getByText } = render(
      <PanelModalDrawer.Provider>
        <PaymentMethodAddForm />
      </PanelModalDrawer.Provider>
    );

    expect(getByText('Test error')).toBeInTheDocument();
    panelModalDrawerSpy.mockRestore();
  });

  it('should disable adding payment method for users who doesn\'t have any payment methods if add returns 429', () => {
    (usePaymentError as jest.Mock).mockReturnValue({
      errorMessage: "We're unable to complete this card update. Please try again later.",
      bannerType: 'error',
    });

    (useCheckout as jest.Mock).mockReturnValue({
      draftOrder: {
        paymentPanel: {
          conditionals: { bopisOnlyBag: true },
          paymentMethods: [],
          errors: [],
        },
      },
    });

    jest.mocked(require('@ecom-next/checkout/PaymentsProvider')).usePayments.mockReturnValue({
      paymentsState: {
        selectedPaymentMethodState: { entityType: 'creditCard', id: '123' },
        disabledPaymentMethod: 'add',
        editClickState: false,
      },
      setPaymentsState: jest.fn(),
    });

    const { getByTestId } = render(
      <PanelModalDrawer.Provider>
        <PaymentMethodAddForm />
      </PanelModalDrawer.Provider>
    );

    const saveButton = getByTestId('card-save-button');
    expect(saveButton).toBeInTheDocument();
    expect(saveButton).toBeDisabled();
  });
});
