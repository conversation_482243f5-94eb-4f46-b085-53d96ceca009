import React from 'react';
import { useRouter } from 'next/navigation';
import { Modal } from '@ecom-next/core/migration/modal';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { Button } from '@ecom-next/core/migration/button';
import { ApiError } from '../../../contexts/types';

export const ErrorModal = ({ apiError }: { apiError: ApiError }) => {
  const { localize } = useLocalize();
  const { push } = useRouter();
  const is401or403 = apiError.status === 401 || apiError.status === 403;

  const isItemOOS = apiError.errorDetails?.some(({ errorCode }) => errorCode === '8020' || errorCode === '8021');

  const errorConditions = [
    { condition: is401or403, message: 'error.timeoutMessage' },
    { condition: isItemOOS, message: 'error.itemsOOS' },
  ];

  const errorMessage = errorConditions.find(e => e.condition)?.message || 'error.generalMessage';

  return (
    <Modal
      className='hui-general-error !mx-6 !h-auto w-full self-center sm:!mx-auto sm:w-[480px]'
      hasRoundedCorners
      noHeader
      closeIcon={false}
      mobileFitSizeToContent
      isOpen
      onClose={() => {}}
    >
      {
        <>
          <div className='cb-base-default'>{localize(errorMessage)}</div>
          <div className='cb-base-default-emphasis pt-4 text-center'>
            <Button fullWidth kind='primary' className='px-[50px]' onClick={() => push('/shopping-bag')}>
              {localize('error.backToBag')}
            </Button>
          </div>
        </>
      }
    </Modal>
  );
};
