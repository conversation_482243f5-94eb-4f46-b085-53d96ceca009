/**
 * @jest-environment jsdom
 */

import { getPreviewHeaders } from '../getPreviewHeaders';
import { convertToISO8601 } from '../../convertToISO8601';

jest.mock('../../convertToISO8601', () => ({
  convertToISO8601: jest.fn(),
}));

describe('getPreviewHeaders', () => {
  beforeEach(() => {
    // Reset window.location
    window.location = new URL('https://example.com') as unknown as Location;
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('returns an empty object if isPreview is false', () => {
    const result = getPreviewHeaders(false, '2025-08-01');
    expect(result).toEqual({});
  });

  it('returns correct headers when previewMode=app', () => {
    window.location.search = '?previewMode=app&previewDate=2025-08-01';
    (convertToISO8601 as jest.Mock).mockReturnValue('2025-08-01T00:00:00.000Z');

    const result = getPreviewHeaders(true, '2025-08-01');
    expect(result).toEqual({
      'request-date': '2025-08-01T00:00:00.000Z',
      'request-type': 'APP',
    });
  });

  it('returns correct headers when previewMode is another value', () => {
    window.location.search = '?previewMode=previewX&previewDate=2025-08-01';
    (convertToISO8601 as jest.Mock).mockReturnValue('2025-08-01T00:00:00.000Z');

    const result = getPreviewHeaders(true, '2025-08-01');
    expect(result).toEqual({
      'request-date': '2025-08-01T00:00:00.000Z',
      'request-type': 'PREVIEWX',
    });
  });

  it('handles missing previewMode gracefully', () => {
    window.location.search = '?previewDate=2025-08-01';
    (convertToISO8601 as jest.Mock).mockReturnValue('2025-08-01T00:00:00.000Z');

    const result = getPreviewHeaders(true, '2025-08-01');
    expect(result).toEqual({
      'request-date': '2025-08-01T00:00:00.000Z',
      'request-type': '',
    });
  });
});
