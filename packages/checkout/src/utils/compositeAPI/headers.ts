import { getCookie, removePipes } from '@ecom-next/shopping-bag/utils';
import { RequestHeaders } from './types';
import { getPreviewHeaders } from './getPreviewHeaders';

export const getCompositeApiHeaders = (requestHeaders: RequestHeaders) => {
  const { brand, locale, market, previewDate = '', targetEnv } = requestHeaders;
  const isPreview = targetEnv === 'preview';

  return {
    brand: brand.toUpperCase(),
    channel: 'WEB',
    'client-id': 'WEB-UI',
    'external-customer-id': removePipes(getCookie('cam')) || '',
    locale: locale,
    market: market.toUpperCase(),
    'unknown-shopper-id': removePipes(getCookie('unknownShopperId')) || '',
    ...getPreviewHeaders(isPreview, previewDate),
  };
};
