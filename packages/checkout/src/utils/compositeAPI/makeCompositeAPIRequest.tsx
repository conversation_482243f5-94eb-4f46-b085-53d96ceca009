import type { DraftOrder } from '../../contexts/types';
import type { BaseCompositeAPIRequestParams } from './types';
import { selectShippingAddress } from './selectShippingAddress';
import { createShippingAddress } from './createShippingAddress';
import { getCompositeOrder } from './getCompositeOrder';
import { applyRewards } from './applyRewards';
import { removeRewards } from './removeRewards';
import { applyPromos } from './applyPromos';
import { removePromos } from './removePromos';
import { selectShippingMethod } from './selectShippingMethod';
import donations from './donations';
import { updatePickupInfo } from './updatePickupInfo';
import { applyGiftCard } from './applyGiftCard';
import { removeGiftCard } from './removeGiftCard';
import { placeOrderAction } from './placeOrderAction';
import { createPaymentMethod } from './createPaymentMethod';
import { updatePaymentMethod } from './updatePaymentMethod';
import { updateGiftMessage } from './updateGiftMessage';

// TODO: Add correct type for the multiple bodies we'll be using
// @ts-ignore
export const makeCompositeAPIRequest = async ({ ecomApiBaseUrl, context, endpointConfig, body }: BaseCompositeAPIRequestParams): Promise<DraftOrder> => {
  // Please do not add any logic to the cases if possible.
  // This is just to have better organization of functions we'll be calling.
  switch (endpointConfig) {
    case 'fetchDraftOrder':
      return await getCompositeOrder({ ecomApiBaseUrl, context });

    case 'createShippingAddress':
      return await createShippingAddress({ ecomApiBaseUrl, context, body });

    case 'selectShippingAddress':
      return await selectShippingAddress({ ecomApiBaseUrl, context, body });

    case 'applyRewards':
      return await applyRewards({ ecomApiBaseUrl, context, body });

    case 'removeRewards':
      return await removeRewards({ ecomApiBaseUrl, context, body });

    case 'applyPromos':
      return await applyPromos({ ecomApiBaseUrl, context, body });

    case 'removePromos':
      return await removePromos({ ecomApiBaseUrl, context, body });

    case 'applyDonation':
      return await donations({ ecomApiBaseUrl, context, body });

    case 'applyGiftCard':
      return await applyGiftCard({ ecomApiBaseUrl, context, body });

    case 'removeGiftCard':
      return await removeGiftCard({ ecomApiBaseUrl, context, body });

    case 'selectShippingMethod':
      return await selectShippingMethod({ ecomApiBaseUrl, context, body });

    case 'updatePickupInfoAction':
      return await updatePickupInfo({ ecomApiBaseUrl, context, body });

    case 'placeOrderAction':
      return await placeOrderAction({ ecomApiBaseUrl, context, body });

    case 'createPaymentMethod':
    case 'updatePaymentMethod':
    case 'selectPaymentMethod':
      if (body?.paymentId) {
        return await updatePaymentMethod({ ecomApiBaseUrl, context, body });
      }
      return await createPaymentMethod({ ecomApiBaseUrl, context, body });

    case 'updateGiftMessage':
      return await updateGiftMessage({ ecomApiBaseUrl, context, body });

    default:
      // If the endpointConfig does not match any case, throw an error
      throw new Error(`Invalid endpointConfig: ${String(endpointConfig)}`);
  }
};
