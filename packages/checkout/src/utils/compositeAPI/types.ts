import { Brand, Locale, Market } from '@ecom-next/utils/server';
import type { BrandCode } from '@mfe/brand-info';
import type {
  CreatePaymentMethodPayload,
  MakeCheckoutXapiConfig,
  CreateShippingAddressPayload,
  ApplyRewardsPayload,
  PromosPayload,
  AddGiftCardPayload,
  RemoveGiftCardPayload,
  SelectShippingMethodPayload,
  UpdatePaymentMethodPayload,
  UpdatePickupInfoPayload,
  PlaceOrderPayload,
  UpdateGiftMessagePayload,
} from '../../contexts/types';
import { UserState, DonationId } from '../../contexts/types';

export interface RequestHeaders {
  brand: Brand;
  locale: Locale;
  market: Market;
  previewDate?: string;
  targetEnv?: string;
}

export type ItemBrand = Brand | 'gp' | 'gpfs';
export type PaymentType = 'CREDIT_CARD' | 'GIFT_CARD' | 'PAYPAL' | 'AFTERPAY' | 'APPLE_PAY' | 'KLARNA';

export type BaseCompositeAPIRequestParams = {
  context: RequestHeaders;
  ecomApiBaseUrl: string;
  endpointConfig?: MakeCheckoutXapiConfig['endpoint'];
};

export type AddressCompositeAPIRequestParams = {
  body: CreateShippingAddressPayload;
} & BaseCompositeAPIRequestParams;

export type PlaceOrderCompositeAPIRequestParams = {
  body: PlaceOrderPayload;
} & BaseCompositeAPIRequestParams;

export type RewardsCompositeAPIRequestParams = {
  body: ApplyRewardsPayload;
} & BaseCompositeAPIRequestParams;

export type PromosCompositeAPIRequestParams = {
  body: PromosPayload;
} & BaseCompositeAPIRequestParams;

export type GiftCardCompositeAPIRequestParams = {
  body: AddGiftCardPayload;
} & BaseCompositeAPIRequestParams;

export type RemoveGiftCardCompositeAPIRequestParams = {
  body: RemoveGiftCardPayload;
} & BaseCompositeAPIRequestParams;

export type DonationCompositeAPIRequestParams = {
  body: DonationId & {
    donation: DonationCompositeParam;
    draftOrderId: string;
  };
} & BaseCompositeAPIRequestParams;

export type ShippingMethodCompositeAPIRequestParams = {
  body: SelectShippingMethodPayload;
} & BaseCompositeAPIRequestParams;

export type PickupCompositeAPIRequestParams = {
  body: UpdatePickupInfoPayload;
} & BaseCompositeAPIRequestParams;

export type CreatePaymentCompositeAPIRequestParams = {
  body: CreatePaymentMethodPayload;
} & BaseCompositeAPIRequestParams;

export type UpdatePaymentCompositeAPIRequestParams = {
  body: UpdatePaymentMethodPayload;
} & BaseCompositeAPIRequestParams;

export type UpdateGiftMessageCompositeAPIRequestParams = {
  body: UpdateGiftMessagePayload;
} & BaseCompositeAPIRequestParams;

interface DonationCompositeParam extends DonationId {
  brand: string;
  charityName: string;
  price: number;
  selected_donation_option: number;
}

interface Donation extends DonationId {
  available_donation_amounts: {
    amount: number;
  }[];
  brand: string;
  charity_info: string;
  charity_name: string;
  donation_id: string;
  image_uri: string;
  price: number;
  selected_donation_option: number;
  tax_id: string;
}

interface Promotion {
  arbitration_message: string;
  associate: boolean;
  automatic: boolean;
  code: string;
  description: string;
  display_name: string;
  flags: {
    code: string;
    message: string;
    name: string;
    type: 'INFO' | 'ERROR' | 'WARNING';
  }[];
  id: string;
  promotion_description: string;
  promotion_total: number;
  promotion_type: string;
}

export interface ShippingAddress {
  address_id: string;
  address_line_1: string;
  address_line_2?: string;
  address_type: string;
  address_verified_status: 'NOT_VERIFIED' | 'USER_OVERRIDDEN' | 'VERIFIED' | 'AVS_VERIFIED';
  city: string;
  country: string;
  default: boolean;
  delivery_point_validation: 'CONFIRMED_DELIVERY_POINT' | 'INVALID_DELIVERY_POINT' | 'INDETERMINATE';
  first_name: string;
  guest?: boolean;
  last_name: string;
  phone: string;
  postal_code: string;
  selected: boolean;
  state: string;
}

export interface BillingAddress {
  address_id?: string;
  address_line_1: string;
  address_line_2?: string;
  address_verified_status: 'NOT_VERIFIED' | 'USER_OVERRIDDEN' | 'VERIFIED';
  city: string;
  country: string;
  first_name: string;
  last_name: string;
  phone: string;
  postal_code: string;
  state: string;
}

export interface ShippingMethod {
  description: string;
  enabled: boolean;
  get_it_by_date: string; // Date in ISO format with timezone
  id: string;
  max_delivery_date: number;
  min_delivery_date: number;
  name: string;
  offer_info: {
    description: string;
    link_label: string;
    url: string;
  }[];
  price: number;
  selected: boolean;
  type_id: '7' | '1' | '3' | '5';
}

export interface StorePickUpInfo {
  phone: string;
  store_address: {
    address_line_1: string;
    address_line_2: string;
    city: string;
    country: string;
    phone: string;
    postal_code: string;
    state: string;
    state_province_code: string;
  };
  store_name: string;
  store_number: string;
  store_timezone: string;
}

export interface OrderSummary {
  donations_total: number;
  estimated_tax: number;
  gift_cards_total: number;
  merchandise_subtotal_after_savings: number;
  retail_delivery_fee: number;
  rewards_sub_total: number;
  savings_summary: {
    markdown_promo_savings: number;
    rewards_savings: number;
    total_savings: number;
  };
  shipping_items_count: string;
  shipping_method: string;
  shipping_price: number;
  subtotal: number;
  subtotal_after_savings: number;
  total_price: number;
}

export type FulfillmentType = 'BOPIS' | 'SHIP' | 'UPS';

export interface DeliveryGroup {
  gift_message: {
    message_text: string;
  };
  group_id: string;
  group_type: FulfillmentType;
  pickup_person: {
    first_name: string;
    last_name: string;
    phone: string;
  };
  shipping_address: ShippingAddress;
  shipping_methods: ShippingMethod[];
  shopping_bag_item_ids: string[];
  store_pickup_info: StorePickUpInfo;
}

interface ThirdPartyPayment {
  billing_address: BillingAddress;
  card_brand: string;
  card_type: string;
  device_data: string;
  order_identifier: string;
  payment_token: string;
}

export type CreditCardBrand = 'GAP' | 'GP' | 'BR' | 'ON' | 'AT'; // TODO: Either keep GP or GAP. xAPI needs GAP and composite needs GP

interface PaymentMethod {
  afterpay?: ThirdPartyPayment;
  apple_pay?: ThirdPartyPayment;
  credit_card?: {
    billing_address: BillingAddress;
    card_brand?: CreditCardBrand;
    card_token: string;
    card_type: 'VISA' | 'MASTERCARD' | 'AE' | 'DISCOVER' | 'JCB' | 'DINERS' | 'BR_PLCC' | 'GAP_PLCC' | 'ON_PLCC' | 'AT_PLCC';
    cvv_required: boolean;
    expiration_month: string;
    expiration_year: string;
    expired: boolean;
    last_four: string;
    plcc: boolean;
    temporary: boolean;
    token_format: 'VAULT_ID';
    validated_cvv: boolean;
  };
  default: boolean;
  gift_card: {
    amount_redeemed: number;
    balance_amount: number;
    card_token: string;
    last_four: string;
    pin_format: 'VAULT_ID';
    pin_token: string;
    token_format: 'VAULT_ID';
  };
  index?: number;
  klarna?: ThirdPartyPayment;
  payment_id: string;
  payment_type: PaymentType;
  paypal?: ThirdPartyPayment;
  selected?: boolean;
}

interface ShoppingBagItem {
  back_order_date: string;
  brand: {
    abbr_name: ItemBrand;
    id: `${BrandCode}`;
  };
  delivery_group_id: string;
  id: string;
  index: number;
  inventory_status: 'IN_STOCK' | 'OUT_OF_STOCK' | 'LOW_STOCK' | 'ON_ORDER' | 'RESERVED';
  price: {
    discounted_price: number;
    regular_price: number;
    sale_price: number;
    total_price: number;
  };
  product: {
    auto_added: boolean;
    color: string;
    customer_choice_number: string;
    description: string;
    image_resources: {
      url: string;
      usage: string;
    }[];
    image_url: string;
    merge_type: string;
    name: string;
    page_type: string;
    primary_category_name: string;
    product_type: string;
    return_by_mail_item: boolean;
    returnable: boolean;
    reward_points?: number;
    rewards_item?: boolean;
    seller_attributes: {
      estimated_days_to_ship: number;
      excluded_from_promotions: boolean;
      free_shipping: boolean;
      made_to_order: boolean;
      show_seller_name: boolean;
    };
    shippable: boolean;
    size: string;
    sku: string;
    style_id: string;
    url: string;
    vendor_info?: {
      // Doesn't exist for all products, a gift card for an example
      id?: string;
      name?: string;
      style_number?: string;
      upc_code?: string;
    };
  };
  promotions: Promotion[];
  quantity: number;
  sku: string;
  store_id: string;
  restrictions: {
    allowed_customer_types: AllowedCustomerType[];
  }[];
}

type AllowedCustomerType = 'GAPINC_CARDHOLDER' | 'GAPINC_NON_CARDHOLDER' | 'GAPINC_NON_MEMBER' | 'GAPINC_MEMBER';

type ErrorDetails = {
  code: string;
  message: string;
  type: string;
};

export type ErrorMessage = {
  error_details: ErrorDetails[];
  message: string;
};

//Contract: https://developer.gapinc.com/catalog/default/api/ui_composite_checkouts-v1/definition#/Checkout/post_
export interface CompositeApiResponse {
  customer: {
    email: string;
    first_name: string;
    last_name: string;
    login_type: UserState;
    loyalty_details: {
      program_event_type: string;
      reason_code: string;
      tier: 'CORE' | 'ENTHUSIAST' | 'ICON';
    };
    shopper_id: string;
  };
  delivery_groups: DeliveryGroup[];
  donations: Donation[];
  draft_order_id: string;
  error_messages?: ErrorMessage[];
  order?: { order_number: string }; //placeOrder API returns order_number
  order_details: {
    contains_made_to_order: boolean;
    gift_card_covers_order_payment: boolean;
    items_count: number;
    order_type: 'SHIP' | 'BOPIS' | 'OMNI';
    reward_points_summary: {
      item_reward_points: {
        points_redeemed_amount: number;
        product_description: string;
        sku: string;
      }[];
      total_reward_points: number;
      balance_reward_points?: number;
    };
  };
  order_summary: OrderSummary;
  payment_methods: PaymentMethod[];
  promotions: Promotion[];
  rewards: {
    available_points: number;
    max_points: number;
    min_points: number;
    points_redeemed: number;
    points_to_amount: {
      amount: number;
      points: number;
    }[];
    rewards_amount: number;
  };
  shipping_addresses: ShippingAddress[];
  shopping_bag: {
    bag_id: string;
    items: ShoppingBagItem[];
    shopper_id: string; // Shopper id/External Customer Id
  };
}
