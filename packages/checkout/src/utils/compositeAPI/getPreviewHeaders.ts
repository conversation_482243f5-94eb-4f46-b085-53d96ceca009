import { isWindowDefined } from '@ecom-next/shopping-bag/utils';
import { convertToISO8601 } from '../convertToISO8601';

export const getPreviewHeaders = (isPreview: boolean, previewDate: string): Record<string, string> => {
  if (!isPreview || !isWindowDefined()) {
    return {};
  }

  const urlParams = new URLSearchParams(window.location.search);
  const previewMode = urlParams.get('previewMode') || '';
  const previewType = previewMode === 'app' ? 'APP' : previewMode.toUpperCase();

  const parsedPreviewDate = convertToISO8601(previewDate);

  return {
    'request-date': parsedPreviewDate,
    'request-type': previewType,
  };
};
