import {
  buildBagPanel,
  buildSession,
  buildShippingAddress,
  buildOrderSummary,
  buildRewards,
  buildDonations,
  buildDeliveryGroup,
  buildGiftCard,
  buildPickup,
  buildCheckoutPanel,
  buildPayments,
} from '../../builders';
import { EMPTY_BAG_ERROR, mapErrorCodesByPanel } from '../../builders/mapErrorCodes';
import type { DraftOrder } from '../../contexts/types';
import type { CompositeApiResponse, RequestHeaders } from './types';

export const formatData = ({ context, data }: { context?: RequestHeaders; data: CompositeApiResponse }) => {
  const errorMessages = data.error_messages ?? [];
  for (const { error_details: details = [] } of errorMessages) {
    const emptyBagError = details.find(detail => detail.code === 'EMPTY_BAG');
    if (emptyBagError) {
      throw { errorDetails: [EMPTY_BAG_ERROR] };
    }
  }

  if (data.order?.order_number) {
    return { orderNumber: data.order?.order_number } as DraftOrder;
  }

  const { deliveryGroupErrors, giftCardErrors, promoRewardsError, paymentsErrors } = mapErrorCodesByPanel(data?.error_messages);

  const updatedResponse: DraftOrder = {
    draftOrderId: data.draft_order_id,
    bagId: data?.shopping_bag?.bag_id,
    panels: {
      bagPanel: buildBagPanel({ data }),
      checkoutPanel: buildCheckoutPanel({ data }),
      deliveryGroupPanel: buildDeliveryGroup({ data, context, deliveryGroupErrors }),
      donationPanel: buildDonations({ data }),
      giftCardPanel: buildGiftCard({ data, giftCardErrors }),
      orderSummaryPanel: buildOrderSummary({ data }),
      paymentPanel: buildPayments({ data, paymentsErrors }),
      pickupPanel: buildPickup({ data }),
      rewardsPanel: buildRewards({ data, promoRewardsError }),
      shippingAddressPanel: buildShippingAddress({ data }),
    },
    session: buildSession(data),
  };

  return updatedResponse;
};
