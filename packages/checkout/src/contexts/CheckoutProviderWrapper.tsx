'use client';
import { type ReactNode, useContext, useEffect, useState } from 'react';
import { usePageContext } from '@ecom-next/sitewide/hooks/usePageContext';
import { PersonalizationContext } from '@ecom-next/sitewide/personalization-provider';
import { clientFetch } from '@ecom-next/utils/clientFetch';
import { useDatalayer } from '../hooks/useDatalayer';
import { getViewTagData as getViewTagDataOld, firePageViewTag as firePageViewTagOld } from '../utils/tealium-utils';
import { logNewRelicError, Feature } from '../utils/newrelic-logger';
import { makeCompositeAPIRequest } from '../utils/compositeAPI/makeCompositeAPIRequest';
import { doesArrayContainString } from '../utils/doesArrayContainString';
import { CheckoutProvider } from './CheckoutProvider';
import { CheckoutUiStateProvider } from './CheckoutUiStateProvider';
import { CHECKOUT_XAPI_ENDPOINTS, CheckoutEndpoint, initialDraftOrder, BOT_PREVENTION_HTTP_CODE } from './constants';
import { ApiError, CheckoutXapiEndpoint, DraftOrder, MakeCheckoutXapiCall, PageContextData } from './types';
import { useCheckoutFeatures } from './checkoutFeatures/useCheckoutFeatures';
import { formatProductFlags } from './formatProductFlags';

export const CheckoutProviderWrapper = (props: { children: ReactNode; initialDraftOrder?: DraftOrder; onInitialLoad?: () => void }) => {
  const pageContext = usePageContext();
  const { market, brand, brandAbbr, locale, ecomApiBaseUrl, previewDate, targetEnv } = pageContext;
  const personalizationData = useContext(PersonalizationContext);
  const [loading, setLoading] = useState<boolean>(false);
  const [draftOrder, setDraftOrder] = useState<DraftOrder>(props.initialDraftOrder || initialDraftOrder);
  const [apiError, setApiError] = useState<ApiError | null>(null);
  const [endpoint, setEndpoint] = useState<string>('');
  const [isTealiumFiredOnPageLoad, setIsTealiumFiredOnPageLoad] = useState(false);
  const { isCompositeAPIEnabled, isTealiumMigrationEnabled } = useCheckoutFeatures();

  const { firePageViewTag: firePageViewTagNew, getViewTagData: getViewTagDataNew } = useDatalayer();
  const firePageViewTag = isTealiumMigrationEnabled ? firePageViewTagNew : firePageViewTagOld;
  const getViewTagData = isTealiumMigrationEnabled ? getViewTagDataNew : getViewTagDataOld;

  const makeCheckoutXapiCall: MakeCheckoutXapiCall = async config => {
    const { endpoint: endpointConfig } = config;
    const apiEndpoint = CHECKOUT_XAPI_ENDPOINTS[endpointConfig as CheckoutXapiEndpoint];
    if (!apiEndpoint) {
      logNewRelicError(`${String(endpointConfig)} is not a valid action.`, { feature: Feature.CHECKOUT_PROVIDER_WRAPPER, caller: 'makeCheckoutXapiCall()' });
      return;
    }

    setLoading(true);
    setEndpoint(endpointConfig as string);

    const { path, method } = apiEndpoint;
    const headers = {
      locale,
    };

    try {
      let response: DraftOrder;
      let updatedResponse: DraftOrder;

      if (isCompositeAPIEnabled) {
        updatedResponse = await makeCompositeAPIRequest({
          ecomApiBaseUrl,
          context: { brand, locale, market, previewDate, targetEnv },
          endpointConfig,
          // TODO: Add correct type for the multiple bodies we'll be using
          // @ts-ignore
          body: 'body' in config && config.body ? config.body : undefined,
        });

        if (updatedResponse?.orderNumber) {
          setDraftOrder(updatedResponse);
          const pageContextData = { market, brandAbbr, locale };
          callTealiumViewTag(draftOrder, pageContextData, apiEndpoint);
          if (typeof window !== 'undefined') {
            const host = window.location.hostname;
            const redirectUrl = `https://${host}/checkout/thankyou/${updatedResponse.orderNumber}?initialLoad=true`;
            window.location.replace(redirectUrl);
          }
        } else if (updatedResponse?.panels) {
          formatProductFlags(updatedResponse);
          setDraftOrder(updatedResponse);
          const pageContextData = { market, brandAbbr, locale };
          if (apiEndpoint.path !== '/get-checkout-page') {
            let shipToUpsIndicator = false;
            if (config.endpoint === 'createShippingAddress') {
              shipToUpsIndicator = config.body.shippingAddress.shipToUpsIndicator || false;
            }
            callTealiumViewTag(updatedResponse, pageContextData, apiEndpoint, shipToUpsIndicator);
          }
        }
      } else {
        if (method === 'POST' && 'body' in config) {
          response = await clientFetch<DraftOrder>(`checkout/place-order/xapi${path}`, {
            body: JSON.stringify(config.body),
            method,
            headers,
          });
        } else {
          response = await clientFetch<DraftOrder>(`checkout/place-order/xapi${path}`, { method, headers });
        }
        if (response?.orderNumber) {
          setDraftOrder(response);
          const pageContextData = { market, brandAbbr, locale };
          callTealiumViewTag(draftOrder, pageContextData, apiEndpoint);
          if (typeof window !== 'undefined') {
            const host = window.location.hostname;
            const redirectUrl = `https://${host}/checkout/thankyou/${response.orderNumber}?initialLoad=true`;
            window.location.replace(redirectUrl);
          }
        } else if (response?.panels) {
          // Only change the draftOrder when the response seems to
          // be a draft order. We also use GET responses with
          // other requests like obtaining payment tokens.
          formatProductFlags(response);
          setDraftOrder(response);
          const pageContextData = { market, brandAbbr, locale };
          if (apiEndpoint.path !== '/get-checkout-page') {
            let shipToUpsIndicator = false;
            if (config.endpoint === 'createShippingAddress') {
              shipToUpsIndicator = config.body.shippingAddress.shipToUpsIndicator || false;
            }
            callTealiumViewTag(response, pageContextData, apiEndpoint, shipToUpsIndicator);
          }
        }
        setApiError(null);
        return response; // eslint-disable-line consistent-return
      }
    } catch (error) {
      if ((error as { status?: number })?.status === BOT_PREVENTION_HTTP_CODE) return;
      setApiError({ ...(error as ApiError), path });

      if (doesArrayContainString(path, [CHECKOUT_XAPI_ENDPOINTS.createPaymentMethod.path, CHECKOUT_XAPI_ENDPOINTS.updatePaymentMethod.path])) {
        throw error;
      }
    } finally {
      setLoading(false);
    }
  };

  const callTealiumViewTag = async (response: DraftOrder, pageContextData: PageContextData, apiEndpoint: CheckoutEndpoint, isUPS: boolean = false) => {
    const {
      panels: { checkoutPanel, deliveryGroupPanel },
    } = response;
    const checkoutModuleStatus = '';
    const { pageName } = apiEndpoint;
    if (pageName) {
      const viewTagData = getViewTagData({
        personalizationData,
        pageContextData,
        checkoutPanel,
        deliveryGroupLists: deliveryGroupPanel?.deliveryGroupLists,
      });
      const checkoutCTAName = isUPS ? 'change:ups access point' : apiEndpoint.checkoutCTAName || '';
      const checkoutModuleStatusData = pageName === 'pageLoad' ? checkoutModuleStatus : '';
      firePageViewTag(pageName, viewTagData, checkoutCTAName, checkoutModuleStatusData);
    }
  };

  useEffect(() => {
    void makeCheckoutXapiCall({ endpoint: 'fetchDraftOrder' }).then(props.onInitialLoad);
  }, [props.onInitialLoad]);

  useEffect(
    function handleTealiumEventForFirstLoad() {
      if (!draftOrder.draftOrderId || !personalizationData.customerUUID || isTealiumFiredOnPageLoad) return;

      const pageContextData = { market, brandAbbr, locale };
      callTealiumViewTag(draftOrder, pageContextData, CHECKOUT_XAPI_ENDPOINTS['fetchDraftOrder']);
      setIsTealiumFiredOnPageLoad(true);
    },
    [personalizationData.customerUUID, draftOrder.draftOrderId, isTealiumFiredOnPageLoad]
  );

  return (
    <CheckoutProvider draftOrder={draftOrder} apiError={apiError} endpoint={endpoint} makeCheckoutXapiCall={makeCheckoutXapiCall} loading={loading}>
      <CheckoutUiStateProvider>{props.children}</CheckoutUiStateProvider>
    </CheckoutProvider>
  );
};
