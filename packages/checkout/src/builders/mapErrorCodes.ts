import type { ErrorMessage } from '../utils/compositeAPI/types';

export type DELIVERY_GROUP_ERROR_CODES = '4101' | '4102' | '6507' | '6508';
export type PROMO_REWARDS_ERROR_CODES = '5003' | '5005' | '5015' | '5019' | '501520' | '501530' | '501540' | '6564' | '6566' | '5017' | '6567';
export type GIFTCARD_ERROR_CODES = '400' | '802' | '804' | '811' | '814' | '815';
export type PAYMENTS_ERROR_CODES = '710' | '801' | '807' | '810' | '813' | '819' | '860' | '861' | '862' | '864' | '871' | '899';

type DELIVERY_GROUP_CODE =
  | 'SHIPPING_SPEED_DEFAULTED'
  | 'SHIPPING_SPEED_UNSUPPORTED'
  | 'DROPSHIP_ERROR_SHIPPING_UNAVAILABLE_TO_ADDRESS'
  | 'DROPSHIP_ERROR_SHIPPING_OPTIONS_UNAVAILABLE';

type GIFTCARDCODE =
  | 'SYNTHETIC_ORDERS_NOT_ALLOWED'
  | 'INVALID_GIFT_CARD'
  | 'INVALID_PIN'
  | 'GIFTCARD_DECLINED'
  | 'NO_CARD_FOUND'
  | 'GENERAL_ERROR'
  | 'INVALID_CARDS_REMOVED'
  | 'ZERO_BALANCE_CARDS_REMOVED'
  | 'GIFTCARD_NOT_ATTEMPTED';

type CVV_INVALID_ERRORS = 'CVV_ERROR';
type CARD_PROCESSING_ERRORS =
  | 'INVALID_CREDIT_CARD'
  | 'INVALID_CREDIT_CARD_EXPIRY'
  | 'CREDITCARD_DECLINED'
  | 'INVALID_CVV'
  | 'PAYMENT_AUTH_ERROR'
  | 'PAYMENT_NOT_ELIGIBLE';

type KLARNA_PAYMENT_ERRORS = 'KLARNA_PAYMENT_DECLINE' | 'KLARNA_NOT_ALLOWED' | 'KLARNA_PAYMENT_UNSUPPORTED';
type AFTERPAY_PAYMENT_ERRORS = 'AFTERPAY_PAYMENT_DECLINE' | 'AFTERPAY_NOT_ALLOWED';
type PAYPAL_PAYMENT_ERRORS = 'PAYPAL_PAYMENT_DECLINE' | 'PAYPAL_OMNI_BOPIS_ERROR';

type THIRD_PARTY_PAYMENT_ERRORS = PAYPAL_PAYMENT_ERRORS | AFTERPAY_PAYMENT_ERRORS | KLARNA_PAYMENT_ERRORS;

type PAYMENTS_CODE = CVV_INVALID_ERRORS | CARD_PROCESSING_ERRORS | THIRD_PARTY_PAYMENT_ERRORS;
type ERRORCODEKEY = DELIVERY_GROUP_CODE | PROMO_REWARDS_ERROR_CODES | GIFTCARDCODE | PAYMENTS_CODE;

type ErrorMapping =
  | { code: DELIVERY_GROUP_ERROR_CODES; type: 'deliveryGroup' }
  | { code: PROMO_REWARDS_ERROR_CODES; type: 'promoRewards' }
  | { code: PAYMENTS_ERROR_CODES; type: 'payments' }
  | { code: GIFTCARD_ERROR_CODES; type: 'giftCard' };

export const ERROR_CODE_MAP = new Map<ERRORCODEKEY, ErrorMapping>([
  // Delivery Group Codes
  ['SHIPPING_SPEED_DEFAULTED', { code: '6507', type: 'deliveryGroup' }],
  ['SHIPPING_SPEED_UNSUPPORTED', { code: '6508', type: 'deliveryGroup' }],
  ['DROPSHIP_ERROR_SHIPPING_UNAVAILABLE_TO_ADDRESS', { code: '4101', type: 'deliveryGroup' }],
  ['DROPSHIP_ERROR_SHIPPING_OPTIONS_UNAVAILABLE', { code: '4102', type: 'deliveryGroup' }],

  // Promo/Rewards Codes
  ['5003', { code: '5003', type: 'promoRewards' }],
  ['6564', { code: '6564', type: 'promoRewards' }],
  ['6566', { code: '6566', type: 'promoRewards' }],
  ['5015', { code: '5015', type: 'promoRewards' }],
  ['501520', { code: '501520', type: 'promoRewards' }],
  ['501530', { code: '501530', type: 'promoRewards' }],
  ['501540', { code: '501540', type: 'promoRewards' }],
  ['5005', { code: '5005', type: 'promoRewards' }],
  ['5019', { code: '5019', type: 'promoRewards' }],
  ['5017', { code: '5017', type: 'promoRewards' }],
  ['6567', { code: '6567', type: 'promoRewards' }],

  // Payments Codes
  ['CVV_ERROR', { code: '710', type: 'payments' }],
  ['INVALID_CREDIT_CARD_EXPIRY', { code: '801', type: 'payments' }],
  ['INVALID_CVV', { code: '807', type: 'payments' }],
  ['CREDITCARD_DECLINED', { code: '810', type: 'payments' }],
  ['INVALID_CREDIT_CARD', { code: '813', type: 'payments' }],
  ['PAYPAL_PAYMENT_DECLINE', { code: '860', type: 'payments' }],
  ['PAYPAL_OMNI_BOPIS_ERROR', { code: '861', type: 'payments' }],
  ['AFTERPAY_PAYMENT_DECLINE', { code: '862', type: 'payments' }],
  ['AFTERPAY_NOT_ALLOWED', { code: '862', type: 'payments' }],
  ['PAYMENT_NOT_ELIGIBLE', { code: '871', type: 'payments' }],
  ['PAYMENT_AUTH_ERROR', { code: '899', type: 'payments' }],
  ['GIFTCARD_NOT_ATTEMPTED', { code: '819', type: 'payments' }],
  ['KLARNA_PAYMENT_DECLINE', { code: '864', type: 'payments' }],
  ['KLARNA_NOT_ALLOWED', { code: '864', type: 'payments' }],
  ['KLARNA_PAYMENT_UNSUPPORTED', { code: '864', type: 'payments' }],

  // Gift Card Codes
  ['SYNTHETIC_ORDERS_NOT_ALLOWED', { code: '400', type: 'giftCard' }],
  ['INVALID_GIFT_CARD', { code: '802', type: 'giftCard' }],
  ['INVALID_PIN', { code: '804', type: 'giftCard' }],
  ['GIFTCARD_DECLINED', { code: '811', type: 'giftCard' }],
  ['NO_CARD_FOUND', { code: '811', type: 'giftCard' }],
  ['GENERAL_ERROR', { code: '811', type: 'giftCard' }],
  ['INVALID_CARDS_REMOVED', { code: '814', type: 'giftCard' }],
  ['ZERO_BALANCE_CARDS_REMOVED', { code: '815', type: 'giftCard' }],
]);

export const EMPTY_BAG_ERROR = { errorCode: '8021' };

export const mapErrorCodesByPanel = (
  errorMessages: ErrorMessage[] = []
): {
  deliveryGroupErrors: DELIVERY_GROUP_ERROR_CODES[];
  giftCardErrors: GIFTCARD_ERROR_CODES[];
  paymentsErrors: Set<PAYMENTS_ERROR_CODES>;
  promoRewardsError: PROMO_REWARDS_ERROR_CODES | '';
} => {
  const deliveryGroupsSet = new Set<DELIVERY_GROUP_ERROR_CODES>();
  const giftCardsSet = new Set<GIFTCARD_ERROR_CODES>();
  const paymentsErrorsSet = new Set<PAYMENTS_ERROR_CODES>();
  let promoRewardsError: PROMO_REWARDS_ERROR_CODES | '' = '';

  if (errorMessages.length === 0) {
    return {
      deliveryGroupErrors: [],
      giftCardErrors: [],
      paymentsErrors: new Set(),
      promoRewardsError: '',
    };
  }

  for (const { error_details } of errorMessages) {
    for (const { code } of error_details) {
      const mapping = ERROR_CODE_MAP.get(code as ERRORCODEKEY);

      if (!mapping) {
        continue;
      }

      switch (mapping.type) {
        case 'deliveryGroup':
          deliveryGroupsSet.add(mapping.code);
          break;
        case 'giftCard':
          giftCardsSet.add(mapping.code);
          break;
        case 'payments':
          paymentsErrorsSet.add(mapping.code);
          break;
        case 'promoRewards':
          if (promoRewardsError === '') {
            promoRewardsError = mapping.code;
          }
          break;
        default:
          // Handle any future mapping types if needed
          break;
      }
    }
  }

  return {
    deliveryGroupErrors: Array.from(deliveryGroupsSet),
    giftCardErrors: Array.from(giftCardsSet),
    paymentsErrors: paymentsErrorsSet,
    promoRewardsError,
  };
};
