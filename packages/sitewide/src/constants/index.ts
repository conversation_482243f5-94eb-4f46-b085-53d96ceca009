/**
 * Obtained object structure from sitewide-app.
 * @see https://github.gapinc.com/ecomfrontend/sitewide-app/blob/bdda0b0a6ec277faa241dc8d755854a1d59379db/content/prod/ca/en_CA/at.json#L2944-L2970
 * @todo Replace this with proper marketing configuration when the marketing package is implemented.
 */
export const UTILITY_LINKS_ABOVE_SEARCH_BAR = {
  type: 'sitewide',
  name: 'UtilityLinks',
  desc: 'amplience CA EN utility links',
  data: {
    style: {
      textTransform: 'capitalize',
      fontSize: '12px',
      fontWeight: 'normal!important',
    },
    brandBarShortcutLinks: [
      {
        link: '/stores',
        text: 'Find A Store',
      },
      {
        link: '/browse/info.do?cid=1184799&mlink=1,1,UtilityLink,1',
        text: 'Athleta Rewards',
      },
      {
        link: '/browse/info.do?cid=52564',
        text: 'Gift Card',
      },
    ],
  },
};

export const BRAND_MAP: Record<string, string> = {
  at: 'https://athleta.gap.com',
  br: 'https://bananarepublic.gap.com',
};

export const features = {
  CHECKOUT_UI_BARCLAYS_US: 'checkout-ui-barclays-us',
  SWF_HUI_REWRITE_NAVIGATION_PROVIDER: 'swf-hui-rewrite-navigation-provider',
  SWF_HUI_REWRITE_SISTER_BRANDS_BAR: 'swf-hui-rewrite-sister-brands-bar',
  SWF_HUI_REWRITE_ACCOUNT_DROPDOWN: 'swf-hui-rewrite-account-dropdown',
  SWF_HUI_REWRITE_ANIMATED_HEADLINE: 'swf-hui-rewrite-animated-headline',
  SWF_HUI_REWRITE_COUNTDOWN: 'swf-hui-rewrite-countdown',
  SWF_HUI_REWRITE_BRAND_LOGO: 'swf-hui-rewrite-brand-logo',
  SWF_HUI_REWRITE_MEGANAV: 'swf-hui-rewrite-meganav',
  SWF_HUI_DISABLE_REWRITE_MEGANAV_INJECTED_STYLES: 'swf-hui-disable-rewrite-meganav-injected-styles',
  SWF_HUI_REWRITE_HAMNAV: 'swf-hui-rewrite-hamnav',
  SWF_HUI_REWRITE_HAMNAV_V2: 'swf-hui-rewrite-hamnav-v2',
  SWF_HUI_REWRITE_HEADLINE: 'swf-hui-rewrite-headline',
  SWF_SHOPPING_BAG_REMINDER: 'swf-shopping-bag-reminder',
  SWF_HUI_REWRITE_FLEX_HEADLINE: 'swf-hui-rewrite-flex-headline',
  SWF_HUI_REWRITE_SHOPPING_BAG: 'swf-hui-rewrite-shopping-bag',
  SWF_SMART_BANNER: 'swf-smart-banner',
  SWF_HUI_REWRITE_ACQUISITION_BANNER: 'swf-hui-rewrite-acquisition-banner',
  SWF_REMOVE_AT_FR_CA: 'swf-remove-at-fr-ca',
  SWF_HUI_REWRITE_EDFS_LARGE: 'swf-hui-rewrite-edfs-large',
  SWF_HUI_REWRITE_EDFS_SMALL: 'swf-hui-rewrite-edfs-small',
  SWF_HUI_REWRITE_NAVIGATION_BAR: 'swf-hui-rewrite-navigation-bar',
  SWF_MOBILE_ACCOUNT_BUTTON: 'swf-mobile-account-button',
  SWF_HIDE_REWARDS_INFO: 'swf-hide-rewards-info',
  SWF_CASH_BASED_REWARDS_INFO: 'swf-cash-based-rewards-info',
  SWF_HUI_RETHEMING_SISTER_BRAND_BAR: 'swf-hui-retheming-sister-brand-bar',
  SWF_HUI_RETHEMING_ACCOUNT_DROPDOWN: 'swf-hui-retheming-account-dropdown',
  SWF_APPSFLYER_SCRIPT_DEBUG: 'swf-appsflyer-script-debug',
  SWF_HUI_Q2_SEARCH_REWRITE: 'plp-hui-q2-search-rewrite',
  SWF_AT_MEGANAV_ADJUSTMENTS: 'swf-at-meganav-adjustments',
};

export enum Brands {
  Athleta = 'at',
  BananaRepublic = 'br',
  BananaRepublicFactoryStore = 'brfs',
  Gap = 'gap',
  GapFactoryStore = 'gapfs',
  OldNavy = 'on',
}

export const BR_2024_HEADER_COLOR = '#FFF';
export const BR_2024_BACKGROUND_COLOR = '#FFFFFF';
export const BR_2023_BACKGROUND_COLOR = '#F6F4EB';
export const ENGLISH_CA_LOCALE = 'en_CA';
export const FRENCH_CA_LOCALE = 'fr_CA';
