'use client';

import dynamic from 'next/dynamic';
import { features } from '@sitewide/constants';
import { useFeature } from '../legacy/experiments';

type ExclusionIds = { exclusionIds?: string[] };

const RewriteHamNavV1 = dynamic<ExclusionIds>(() => import('./RewriteHamNav'), { ssr: true });
const RewriteHamNavV2 = dynamic<ExclusionIds>(() => import('./RewriteHamNavV2'), { ssr: true });
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const LegacyHamNav = dynamic<any>(() => import('@sitewide/components/legacy/navigation/hamburger-nav/HamburgerNavWrapper'), { ssr: true });

// eslint-disable-next-line react/display-name
export default ({ data }: { data?: { exclusionIds: string[] } }) => {
  const isRewriteHamNavV1 = useFeature(features.SWF_HUI_REWRITE_HAMNAV);
  const isRewriteHamNavV2 = useFeature(features.SWF_HUI_REWRITE_HAMNAV_V2);

  if (isRewriteHamNavV2) {
    return <RewriteHamNavV2 {...data} />;
  } else if (isRewriteHamNavV1) {
    return <RewriteHamNavV1 {...data} />;
  }
  return <LegacyHamNav {...data} />;
};
