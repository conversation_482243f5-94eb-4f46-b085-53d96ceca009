// @ts-nocheck
'use client';
import { useState } from 'react';
import { getCookie, removePipes } from '@ecom-next/core/legacy/utility';
import { Color, Size, Variant } from '@ecom-next/core/legacy/fixed-button';
import { Notification } from '@ecom-next/core/legacy/notification';
import { Formik, FormikConfig } from 'formik';
import * as yup from 'yup';
import { mapDataToProps } from '@mui/components/legacy/helper';
import { useAppState } from '@ecom-next/sitewide/app-state-provider';
import PhoneInput from './PhoneInput';
import { ResponseMessages, SMSFormProps, SMSFormValues } from './types';
import { features, useFeature } from '@sitewide/components/legacy/sitewide-constants';
import { ComposableButton as FixedOrComposableButton } from '@mui/components/legacy/components/ComposableButton';
import { getTealiumBrandCode } from '@sitewide/components/legacy/utils';
import { css } from '@emotion/react';

const successMessage = 'Almost done! A text is on the way. Reply Y to confirm.';
const errorMessage = 'Oops... Something went wrong. Please try again shortly.';
const invalidMessage = 'Oops! Invalid number. Please check your entry to confirm this is a valid mobile number.';
const duplicateMessage = 'You’re already on the list! No need to register again.';

export const responseMessages: ResponseMessages = {
  200: successMessage,
  201: successMessage,
  202: successMessage,
  400: errorMessage,
  404: errorMessage,
  409: duplicateMessage,
  422: invalidMessage,
};

const isSuccess = (status: number): boolean => status >= 200 && status <= 299;

const SMSForm = ({
                   phoneInputData,
                   apiParams,
                   buttonText = 'Submit',
                   styles: { formStyles, buttonStyles, phoneInputStyles },
                 }: SMSFormProps): JSX.Element => {
  const {
    apis: { smsRegistrationApi, newSmsRegistrationApi },
    targetEnv,
    brandName,
    locale,
    market,
  } = useAppState();

  const [notificationVisible, setNotificationVisible] = useState(false);
  const isUseCookiePackage = useFeature(features.SWF_USE_COOKIE_PACKAGE);
  const isUseNewSmsApi = useFeature(features.SWF_NEW_REGISTRATION_ENDPOINTS);

  const onSubmit: FormikConfig<SMSFormValues>['onSubmit'] = ({ phoneNumber, ...values }, { setStatus }) => {
    setNotificationVisible(true);
    const cookieUnknownShopperId = removePipes(getCookie('unknownShopperId', isUseCookiePackage));
    const cookieCustomerId = removePipes(getCookie('customerId', isUseCookiePackage));

    const shopperId = cookieUnknownShopperId ? `UN:${cookieUnknownShopperId}` : `CS:${cookieCustomerId}`;

    const params: { [key: string]: string } = isUseNewSmsApi
      ? {
        brand: getTealiumBrandCode(brandName),
        market: market.toLocaleUpperCase(),
        locale,
        phoneNumber: phoneNumber.replace(/\D/g, ''),
        program: 'BRAND',
        mainSource: values.src_gnrc_sms,
        subSource: values.src_spfc_sms,
        externalCustomerId: shopperId,
      }
      : {
        ...values,
        phoneNumber: phoneNumber.replace(/\D/g, ''),
        shopper_id: shopperId,
      };

    const stageReferrer = `https://www.gap.com/origin-when-cross-origin`;

    const requestOptions = {
      method: 'post',
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
      },
      body: JSON.stringify(params),
      ...(targetEnv === 'stage' && { referrer: stageReferrer }),
    };
    const smsApi = isUseNewSmsApi ? (newSmsRegistrationApi as string) : smsRegistrationApi;

    fetch(smsApi, requestOptions)
      .then(({ status }) => {
        setStatus(status || 404);
      })
      .catch(() => {
        setStatus(404);
      });
  };

  const notificationStyles = css`
    span {
      height: auto !important;
    }
  `;

  return (
    <Formik
      initialValues={{
        phoneNumber: '',
        ...apiParams,
      }}
      onSubmit={onSubmit}
      validationSchema={yup.object().shape({
        phoneNumber: yup
          .string()
          .required('A 10-digit mobile number is required.')
          .matches(/^\(?(\d{3})\)?[\s.●-]?(\d{3})[.●-]?(\d{4})$/, 'Enter a 10-digit mobile number.'),
      })}
    >
      {({ status, setStatus, handleSubmit }) => {
        const closeNotification = () => {
          setNotificationVisible(false);
        };

        return (
          <div css={{ display: 'flex', flexDirection: 'column' }}>
            <form css={[formStyles, { alignItems: 'center' }]} data-testid="sms-form" onSubmit={handleSubmit}>
              <PhoneInput
                {...phoneInputData}
                css={{
                  minHeight: '75px',
                  margin: '0 2px',
                  width: '147px',
                  ...phoneInputStyles,
                }}
              />
              <FixedOrComposableButton color={Color.primary} css={buttonStyles} fullWidth={false} size={Size.small}
                                       type="submit" variant={Variant.solid}>
                {buttonText}
              </FixedOrComposableButton>
            </form>
            <div style={{ display: 'flex', paddingTop: '10px' }}>
              {notificationVisible && (
                <Notification
                  css={notificationStyles}
                  dismissButtonLabel="dismiss"
                  isDismissible
                  isVisible={Boolean(status)}
                  inverse={false}
                  kind={isSuccess(status) ? 'success' : 'error'}
                  onDismiss={closeNotification}
                >
                  {responseMessages[status]}
                </Notification>
              )}
            </div>
          </div>
        );
      }}
    </Formik>
  );
};

export default mapDataToProps(SMSForm);
export * from './types';


