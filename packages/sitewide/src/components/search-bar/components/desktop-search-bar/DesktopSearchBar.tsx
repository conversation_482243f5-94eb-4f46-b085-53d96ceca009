import React, { useState, useEffect, useRef } from 'react';
import { TextSearchInput } from '@ecom-next/core/components/fabric/text-search-input';
import { useAppState } from '@ecom-next/sitewide/app-state-provider';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { useFeature } from '@sitewide/components/legacy/sitewide-constants';
import { useKeyboardNavigation } from '../../hooks/useKeyboardNavigation';
import { RecentSearchesWrapper } from '../recent-searches/RecentSearchesWrapper';
import { TrendingSearchesWrapper } from '../top-search-terms/TrendingSearchesWrapper';
import { AutoSuggestWrapper } from '../autosuggest/AutoSuggestWrapper';
import { ProductRecommendationsWrapper } from '../product-recommendations/ProductRecommendations';
import { useRecentSearches } from '../../hooks/useRecentSearches';
import { redirectSearch } from '../../utils/redirectSearch';
import { reportPageActionToNewRelic } from '../../utils/reportPageActionToNewRelic';
import { SEARCH_PLACEHOLDER_GAP, SEARCH_PLACEHOLDER_OLD_NAVY, SEARCH_PLACEHOLDER, VISUAL_AUTOSUGGEST_HEADER } from '../../localization-tokens';
import { features } from '../../constants';
import { useSuggestions } from '../../hooks/useSuggestions';
import { DesktopSearchProps } from '../../types';
import { reportToTealium } from '../../../../datalayer';
import { TEALIUM_EVENT } from '../../../ham-nav/v2/utils/constants';

const DesktopSearchBar = (props: DesktopSearchProps) => {
  const { topSearchTerms } = props;
  const { localize } = useLocalize();
  const [searchState, setSearchState] = useState({
    searchValue: '',
    isFocusOnSearch: false,
  });
  const [showSearchFlyout, setShowSearchFlyout] = useState(false);
  const [placeholderTitle, setPlaceholderTitle] = useState(localize(SEARCH_PLACEHOLDER));
  const inputRef = useRef<HTMLInputElement>(null);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const searchLabelGap = localize(SEARCH_PLACEHOLDER_GAP);
  const searchLabelOldNavy = localize(SEARCH_PLACEHOLDER_OLD_NAVY);
  const { brandName, abSeg, locale, pageType, datalayer } = useAppState();
  const isDesktop = true;
  const { getRecentSearches, addRecentSearch, removeSingleSearch, clearAllRecentSearch } = useRecentSearches();
  const [recentSearches, setRecentSearches] = useState(getRecentSearches()[locale] || []);
  const isRecentSearchesEnabled = useFeature(features.SEARCH_HUI_RECENT_SEARCHES);
  const isTrendingSearchesActive = useFeature(features.SEARCH_HUI_TRENDING_SEARCHES);
  const shouldRenderRecentSearches = isRecentSearchesEnabled && recentSearches?.length > 0;
  const shouldRenderTrendingSearches = isTrendingSearchesActive && topSearchTerms?.length > 0;
  const { suggestions, isSuggestionHighlighted, cycleSuggestions, selectedSuggestionIndex, setSelectedSuggestionIndex, departmentsList, visualSearchProducts } =
    useSuggestions(searchState.searchValue, showSearchFlyout, false, recentSearches, isRecentSearchesEnabled, topSearchTerms, isDesktop);
  const autoSuggestions = { suggestionsList: suggestions, departmentsList, visualSearchProducts };

  const handleClear = () => {
    setShowSearchFlyout(true);
    updateSearchValue('');
    inputRef.current?.focus();
  };

  const { handleSearch, handleKeyEvent, setIsFocusOnSearch } = useKeyboardNavigation(
    isDesktop,
    searchState.searchValue,
    suggestions,
    topSearchTerms,
    departmentsList,
    recentSearches,
    [], //visual autosuggest still is not ready
    selectedSuggestionIndex,
    isSuggestionHighlighted,
    isTrendingSearchesActive,
    abSeg ?? {},
    cycleSuggestions,
    handleClear,
    setSelectedSuggestionIndex,
    addRecentSearch
  );
  useEffect(() => {
    if (brandName === 'gap') {
      setPlaceholderTitle(`${searchLabelGap}`);
    } else if (brandName === 'on') {
      setPlaceholderTitle(`${searchLabelOldNavy}`);
    }
    const url = new URL(window.location.href);
    const params = new URLSearchParams(url.search);
    if (params.has('searchText') && pageType === 'search') {
      const searchText = params.get('searchText') || '';
      setSearchState(prev => ({ ...prev, searchValue: searchText }));
    } else {
      setSearchState(prev => ({ ...prev, searchValue: '' }));
    }
  }, [brandName]);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target as Node)) {
        setShowSearchFlyout(false);
        setSearchState(prev => ({ ...prev, isFocusOnSearch: false }));
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const updateSearchValue = (value: string) => {
    setSearchState(prev => ({ ...prev, searchValue: value }));
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchBoxValue = e.target.value;
    updateSearchValue(searchBoxValue);
  };

  const handleOnFocus = () => {
    if (datalayer) {
      reportToTealium(datalayer, TEALIUM_EVENT.SEARCH_BAR_CLICK);
    }
    setShowSearchFlyout(true);
    setSearchState(prev => ({ ...prev, isFocusOnSearch: true }));
    setIsFocusOnSearch(true);
  };

  const handleSubmit = () => {
    if (searchState.searchValue.trim() !== '') {
      addRecentSearch(searchState.searchValue, locale);
      redirectSearch(searchState.searchValue);
    }
  };

  const handleClickRecentSearch = (searchTerm: string) => {
    addRecentSearch(searchTerm, locale);
    redirectSearch(searchTerm, false, '', true, false);
  };

  const handleClickTrendingSearch = (searchTerm: string) => {
    addRecentSearch(searchTerm, locale);
    reportPageActionToNewRelic('topSearchTermClick');
    redirectSearch(searchTerm, false, '', false, true);
  };

  const handleClickAutoDeptSearch = (searchTerm: string, department: string) => {
    addRecentSearch(searchTerm, locale);
    redirectSearch(searchTerm, false, department, false, false);
  };

  const handleClickAutoSuggestSearch = (searchTerm: string) => {
    addRecentSearch(searchTerm, locale);
    redirectSearch(searchTerm, true, '', false, false);
  };

  const handleClearAllRecentSearches = () => {
    clearAllRecentSearch(locale);
    setRecentSearches([]);
    setShowSearchFlyout(true);
    inputRef.current?.focus();
  };

  const handleRemoveSingleSearch = (searchTerm: string) => {
    removeSingleSearch(searchTerm, locale);
    const newSearchTermList = recentSearches.filter(term => term.searchTerm !== searchTerm);
    setRecentSearches(newSearchTermList);
  };

  const handleFormSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    handleSearch();
  };
  return (
    <div className='desktop-search-bar' data-testid='desktop-search-bar' ref={wrapperRef}>
      <form aria-label='search form' onSubmit={handleFormSubmit} data-testid='search-hui-form'>
        <TextSearchInput
          id='search-input'
          className={'search-bar-input'}
          value={searchState.searchValue}
          onChange={handleChange}
          onClear={handleClear}
          onFocus={handleOnFocus}
          onKeyDown={handleKeyEvent}
          ariaLabel='Search input'
          autoComplete='off'
          placeholder={placeholderTitle}
          onSearch={handleSubmit}
          ref={inputRef}
        />
      </form>
      {showSearchFlyout && searchState.searchValue === '' && searchState.isFocusOnSearch && (shouldRenderRecentSearches || shouldRenderTrendingSearches) && (
        <div className='search-flyout' data-testid='search-flyout-initial'>
          {shouldRenderRecentSearches && (
            <RecentSearchesWrapper
              displayRecentSearches={shouldRenderRecentSearches}
              recentSearchesList={recentSearches}
              clearAllRecentSearches={handleClearAllRecentSearches}
              removeSingleSearch={handleRemoveSingleSearch}
              handleClickRecentSearch={handleClickRecentSearch}
            />
          )}
          {shouldRenderTrendingSearches && <TrendingSearchesWrapper topSearchTerms={topSearchTerms} handleClickTrendingSearch={handleClickTrendingSearch} />}
        </div>
      )}
      {showSearchFlyout && searchState.searchValue !== '' && suggestions.length > 0 && (
        <div className='search-flyout search-flyout-suggestions' data-testid='search-flyout-with_search_term'>
          <h4 className='placeholder-title'>{localize(VISUAL_AUTOSUGGEST_HEADER)}</h4>
          <div className='search-suggestions-wrapper'>
            <ProductRecommendationsWrapper searchText={searchState.searchValue} visualSearchProducts={visualSearchProducts} />
            <AutoSuggestWrapper
              searchText={searchState.searchValue}
              suggestions={autoSuggestions}
              handleClickAutoDeptSearch={handleClickAutoDeptSearch}
              handleClickAutoSuggestSearch={handleClickAutoSuggestSearch}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export { DesktopSearchBar };
