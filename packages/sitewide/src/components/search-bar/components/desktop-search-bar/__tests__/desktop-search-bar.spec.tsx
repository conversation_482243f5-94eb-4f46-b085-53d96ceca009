import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import React from 'react';
import { BreakpointContext } from '@ecom-next/core/breakpoint-provider';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { useAppState } from '@sitewide/providers/PageContextProvider';
import { useFeature } from '@sitewide/components/legacy/sitewide-constants';
import { useStickyStatus } from '@sitewide/components/legacy/sticky-manager-new';
import { DesktopSearchBar } from '../DesktopSearchBar';
import { topTrendingSearches, mockTranslation, mockAutoSuggestData } from '../fixtures';
import { RECENT_SEARCHES_CLEAR_ALL_BUTTON, RECENT_SEARCHES_HEADER } from '../../recent-searches/localizationTokens';
import { TEALIUM_EVENT } from '../../../../ham-nav/v2/utils/constants';
import { reportToTealium } from '../../../../../datalayer';
import { features } from '../../../constants';
import { reportPageActionToNewRelic } from '../../../utils/reportPageActionToNewRelic';

jest.mock('@ecom-next/sitewide/localization-provider');
jest.mock('@sitewide/providers/PageContextProvider');
jest.mock('@sitewide/components/legacy/sitewide-constants');
jest.mock('../../../../../datalayer');
jest.mock('../../../../legacy/sticky-manager-new', () => ({
  useStickyStatus: jest.fn(() => true),
}));
jest.mock('../../../hooks/useAutoSuggest', () => ({
  useAutosuggest: () => {
    return {
      departmentsList: [
        { key: 'department', name: 'Women', value: 'Women' },
        { key: 'department', name: 'Men', value: 'Men' },
      ],
      suggestionsList: mockAutoSuggestData,
      visualSearchProducts: [],
    };
  },
}));

jest.mock('../../../hooks/useRecentSearches', () => ({
  useRecentSearches: () => {
    const [searches, setSearches] = React.useState({
      en_US: [{ searchTerm: 'jeans' }, { searchTerm: 'shirts' }],
    });

    const clearAllRecentSearch = () => {
      setSearches({ en_US: [] });
    };

    const removeSingleSearch = (termToRemove: string) => {
      setSearches(prev => ({
        ...prev,
        en_US: prev.en_US.filter(s => s.searchTerm !== termToRemove),
      }));
    };

    return {
      clearAllRecentSearch,
      removeSingleSearch,
      getRecentSearches: () => searches,
      addRecentSearch: jest.fn(),
    };
  },
}));

jest.mock('../../../utils/reportPageActionToNewRelic', () => ({
  reportPageActionToNewRelic: jest.fn(),
}));

describe('DesktopSearchBar component', () => {
  const mockUseLocalize = useLocalize as jest.Mock;
  const mockUseAppState = useAppState as jest.Mock;
  const mockUseFeature = useFeature as jest.Mock;
  const mockUseStickyStatus = useStickyStatus as jest.Mock;
  mockUseStickyStatus.mockReturnValue((_stickyId: string) => {
    return undefined;
  });
  const desktopSearchProps = {
    topSearchTerms: [...topTrendingSearches],
    useStickyStatus: mockUseStickyStatus(),
  };

  const mockReportPageActionToNewRelic = reportPageActionToNewRelic as jest.MockedFunction<typeof reportPageActionToNewRelic>;

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseLocalize.mockReturnValue({
      localize: mockTranslation,
    });
    mockUseAppState.mockReturnValue({
      topSearchTerms: topTrendingSearches,
      locale: 'en_US',
    });
    mockUseFeature.mockReturnValue(true);
  });

  const renderWithBreakpoint = () => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const mockBreakpointContext: any = {
      smallerThan: () => false,
    };

    return render(
      <BreakpointContext.Provider value={mockBreakpointContext}>
        <DesktopSearchBar {...desktopSearchProps} />
      </BreakpointContext.Provider>
    );
  };

  test('renders desktop search bar with the text input box', () => {
    renderWithBreakpoint();
    expect(screen.getByTestId('desktop-search-bar')).toBeInTheDocument();
    expect(screen.getByRole('textbox')).toBeInTheDocument();
  });

  test('renders flyout with trending and recent search suggestions when search box is selected', async () => {
    const user = userEvent.setup();
    renderWithBreakpoint();

    const searchInputBox = screen.getByRole('textbox');
    await user.click(searchInputBox);

    expect(screen.getByTestId('search-flyout-initial')).toBeInTheDocument();

    expect(screen.getAllByRole('heading', { level: 4 })[0]).toHaveTextContent(RECENT_SEARCHES_HEADER);
    expect(screen.getByTestId('recent-search-terms')).toBeInTheDocument();

    expect(screen.getAllByRole('heading', { level: 4 })[1]).toHaveTextContent('Trending');
    expect(screen.getByTestId('trending-search-terms')).toBeInTheDocument();
  });

  test('renders flyout with search suggestions when value is entered in search input field', async () => {
    const user = userEvent.setup();
    renderWithBreakpoint();

    const searchInputBox = screen.getByRole('textbox');
    await user.type(searchInputBox, 'jeans slim fit');

    expect(screen.getByTestId('search-flyout-with_search_term')).toBeInTheDocument();
    expect(screen.getByTestId('search-product-recommendations')).toBeInTheDocument();
    expect(screen.getByTestId('autosuggest-search-terms')).toBeInTheDocument();
    expect(screen.getByTestId('department-suggestions')).toBeInTheDocument();
  });

  test('clears recent searches when clear all button in recent search is clicked', async () => {
    const user = userEvent.setup();
    renderWithBreakpoint();
    const searchInputBox = screen.getByRole('textbox');
    await user.click(searchInputBox);

    expect(screen.getByTestId('recent-search-terms')).toBeInTheDocument();

    const clearRecentSearchesButton = screen.getByRole('button', {
      name: RECENT_SEARCHES_CLEAR_ALL_BUTTON,
    });
    expect(clearRecentSearchesButton).toBeInTheDocument();

    await user.click(clearRecentSearchesButton);
    expect(screen.queryByTestId('recent-search-terms')).not.toBeInTheDocument();
  });

  test('should not render recent searches when feature flag is disabled', async () => {
    mockUseFeature.mockImplementation(feature => feature !== features.SEARCH_HUI_RECENT_SEARCHES);

    const user = userEvent.setup();
    renderWithBreakpoint();

    const searchInputBox = screen.getByRole('textbox');
    await user.click(searchInputBox);

    expect(screen.queryByRole('heading', { name: RECENT_SEARCHES_HEADER })).not.toBeInTheDocument();
    expect(screen.queryByTestId('recent-search-terms')).not.toBeInTheDocument();
  });

  test('clears search value and closes flyout when clear button is clicked', async () => {
    const user = userEvent.setup();
    renderWithBreakpoint();

    const searchInputBox = screen.getByRole('textbox');
    await user.type(searchInputBox, 'jeans slim fit');

    expect(screen.getByTestId('search-flyout-with_search_term')).toBeInTheDocument();

    const clearButton = screen.getByRole('button', { name: /Clear search input/i });
    await user.click(clearButton);

    expect(searchInputBox).toHaveValue('');
    expect(screen.queryByTestId('search-flyout-with_search_term')).not.toBeInTheDocument();
  });

  test('should not render flyout when recent and trending searches feature flags are disabled', async () => {
    mockUseFeature.mockReturnValue(false);

    const user = userEvent.setup();
    renderWithBreakpoint();

    const searchInputBox = screen.getByRole('textbox');
    await user.click(searchInputBox);

    expect(screen.queryByTestId('search-flyout-initial')).not.toBeInTheDocument();
  });

  test('renders correct placeholder based on GAP brand', () => {
    mockUseAppState.mockReturnValue({
      brandName: 'gap',
    });
    mockUseLocalize.mockReturnValue({
      localize: mockTranslation,
    });
    const { getByPlaceholderText } = renderWithBreakpoint();
    expect(getByPlaceholderText('Search Gap')).toBeInTheDocument();
  });

  test('renders correct placeholder based on Old Navy brand', () => {
    mockUseAppState.mockReturnValue({
      brandName: 'on',
    });
    mockUseLocalize.mockReturnValue({
      localize: mockTranslation,
    });
    const { getByPlaceholderText } = renderWithBreakpoint();
    expect(getByPlaceholderText('Search Old Navy')).toBeInTheDocument();
  });

  test('renders correct placeholder based on AT brand', () => {
    mockUseAppState.mockReturnValue({
      brandName: 'at',
    });
    mockUseLocalize.mockReturnValue({
      localize: mockTranslation,
    });
    const { getByPlaceholderText } = renderWithBreakpoint();
    expect(getByPlaceholderText('Search')).toBeInTheDocument();
  });

  test('renders correct placeholder based on BR brand', () => {
    mockUseAppState.mockReturnValue({
      brandName: 'br',
    });
    mockUseLocalize.mockReturnValue({
      localize: mockTranslation,
    });
    const { getByPlaceholderText } = renderWithBreakpoint();
    expect(getByPlaceholderText('Search')).toBeInTheDocument();
  });

  test('initializes search value from URL parameter when pageType is search', () => {
    const searchValue = 'shorts';
    const originalLocation = window.location;
    const mockLocation = {
      ...originalLocation,
      href: `http://localhost/?searchText=${searchValue}`,
      search: `?searchText=${searchValue}`,
    } as Location;

    Object.defineProperty(window, 'location', {
      configurable: true,
      value: mockLocation,
    });
    mockUseAppState.mockReturnValue({
      pageType: 'search',
      locale: 'en_US',
    });

    const { getByDisplayValue } = renderWithBreakpoint();
    expect(getByDisplayValue(searchValue)).toBeInTheDocument();

    Object.defineProperty(window, 'location', {
      configurable: true,
      value: originalLocation,
    });
  });
  test('does not initialize search value from URL parameter when pageType is not search', () => {
    const originalLocation = window.location;
    const mockLocation = {
      ...originalLocation,
      href: 'http://localhost/?searchText=shorts',
      search: '?searchText=shorts',
    } as Location;

    Object.defineProperty(window, 'location', {
      configurable: true,
      value: mockLocation,
    });
    mockUseAppState.mockReturnValue({
      pageType: 'home',
      locale: 'en_US',
    });

    const { getByDisplayValue } = renderWithBreakpoint();
    expect(getByDisplayValue('')).toBeInTheDocument();

    Object.defineProperty(window, 'location', {
      configurable: true,
      value: originalLocation,
    });
  });

  describe('Redirect Search', () => {
    it('should redirect to search results page when a search term is submitted', async () => {
      const originalAssign = window.location.assign;
      window.location.assign = jest.fn();
      const user = userEvent.setup();
      renderWithBreakpoint();

      const searchInput = screen.getByRole('textbox');
      await user.type(searchInput, 'jean{enter}');

      expect(window.location.assign).toHaveBeenCalledTimes(1);
      expect(window.location.assign).toHaveBeenCalledWith('/browse/search.do?searchText=jean');
      window.location.assign = originalAssign;
    });

    test('should redirect to search results page with correct query param when a recent search term is clicked', async () => {
      const originalAssign = window.location.assign;
      window.location.assign = jest.fn();
      const user = userEvent.setup();
      renderWithBreakpoint();
      const searchInputBox = screen.getByRole('textbox');
      await user.click(searchInputBox);

      const recentSearch = screen.getByText('jeans');
      await user.click(recentSearch);

      expect(window.location.assign).toHaveBeenCalledTimes(1);
      expect(window.location.assign).toHaveBeenCalledWith('/browse/search.do?searchText=jeans&recentSearch=true');
      window.location.assign = originalAssign;
    });

    test('should redirect to search results page with correct query param when a trending search term is clicked', async () => {
      const [firstTrendingSearchText] = topTrendingSearches;
      const originalAssign = window.location.assign;
      window.location.assign = jest.fn();
      const user = userEvent.setup();
      renderWithBreakpoint();
      const searchInputBox = screen.getByRole('textbox');
      await user.click(searchInputBox);
      const trendingSearch = screen.getByTestId('trending-search-terms').querySelector('button, [role="button"]');
      expect(trendingSearch).toBeInTheDocument();
      expect(trendingSearch?.textContent?.trim()).toBe(firstTrendingSearchText);

      const firstTrendingSearch = await screen.findByRole('button', { name: firstTrendingSearchText });
      await user.click(firstTrendingSearch);

      expect(mockReportPageActionToNewRelic).toHaveBeenCalledWith('topSearchTermClick');
      expect(mockReportPageActionToNewRelic).toHaveBeenCalledTimes(1);
      expect(window.location.assign).toHaveBeenCalledTimes(1);
      expect(window.location.assign).toHaveBeenCalledWith(`/browse/search.do?searchText=${firstTrendingSearchText}&trendingSearch=true`);
      window.location.assign = originalAssign;
    });

    test('should redirect to search results page with correct query param when an autosuggest term is clicked', async () => {
      const [expectedFirstAutoSuggestText] = mockAutoSuggestData;
      const originalAssign = window.location.assign;
      window.location.assign = jest.fn();
      const user = userEvent.setup();
      renderWithBreakpoint();
      // Type to trigger autosuggest
      const searchInput = screen.getByRole('textbox');
      await user.type(searchInput, 'jea');
      const autosuggestWrapper = screen.getByTestId('autosuggest-search-terms');
      const firstAutoSuggestTerm = autosuggestWrapper.querySelector('.suggested-search-term');
      expect(firstAutoSuggestTerm).toBeInTheDocument();
      expect(firstAutoSuggestTerm?.textContent?.trim()).toBe(expectedFirstAutoSuggestText);

      await user.click(firstAutoSuggestTerm);

      expect(window.location.assign).toHaveBeenCalledTimes(1);
      expect(window.location.assign).toHaveBeenCalledWith(`/browse/search.do?searchText=${encodeURIComponent(expectedFirstAutoSuggestText)}&autosuggest=true`);
      window.location.assign = originalAssign;
    });

    test('should redirect to search results page with correct query param when a department suggestion is clicked', async () => {
      const originalAssign = window.location.assign;
      window.location.assign = jest.fn();
      const user = userEvent.setup();
      renderWithBreakpoint();
      // Type to trigger department suggestions
      const searchInput = screen.getByRole('textbox');
      await user.type(searchInput, 'jea');
      const departmentSuggestions = await screen.findByTestId('department-suggestions');
      const departmentButton = departmentSuggestions.querySelector('button, [role="button"]');
      expect(departmentButton).toBeInTheDocument();
      const departmentName = departmentButton?.textContent;

      await user.click(departmentButton as HTMLElement);

      expect(window.location.assign).toHaveBeenCalledTimes(1);
      expect(window.location.assign).toHaveBeenCalledWith(`/browse/search.do?searchText=jeans#department=${departmentName}`);
      window.location.assign = originalAssign;
    });
  });
  describe('WHEN: the user clicks the search input, putting it into focus,', () => {
    test('THEN: it reports that event to Tealium', async () => {
      (reportToTealium as jest.Mock).mockImplementationOnce(jest.fn());
      const mockDatalayer = {
        build: jest.fn(),
        link: jest.fn(),
      };
      mockUseAppState.mockReturnValue({
        topSearchTerms: topTrendingSearches,
        locale: 'en_US',
        datalayer: mockDatalayer,
      });

      const user = userEvent.setup();
      renderWithBreakpoint();

      const searchInput = screen.getByRole('textbox');
      await user.click(searchInput);

      expect(reportToTealium).toHaveBeenCalledTimes(1);
      expect(reportToTealium).toHaveBeenCalledWith(mockDatalayer, TEALIUM_EVENT.SEARCH_BAR_CLICK);
    });
  });
});
