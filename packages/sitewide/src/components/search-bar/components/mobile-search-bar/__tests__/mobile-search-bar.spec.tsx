import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { BreakpointContext } from '@ecom-next/core/breakpoint-provider';
import { useLocalize } from '@ecom-next/sitewide/localization-provider';
import { useAppState } from '@sitewide/providers/PageContextProvider';
import { useFeature } from '@sitewide/components/legacy/sitewide-constants';
import { useStickyStatus } from '@sitewide/components/legacy/sticky-manager-new';
import { MobileSearchBar } from '../MobileSearchBar';
import { topTrendingSearches, mockTranslation, mockAutoSuggestData } from '../../desktop-search-bar/fixtures';
import { RECENT_SEARCHES_CLEAR_ALL_BUTTON, RECENT_SEARCHES_HEADER } from '../../recent-searches/localizationTokens';
import { features } from '../../../constants';
import { reportToTealium } from '../../../../../datalayer';
import { reportPageActionToNewRelic } from '../../../utils/reportPageActionToNewRelic';

jest.mock('@ecom-next/sitewide/localization-provider');
jest.mock('@sitewide/providers/PageContextProvider');
jest.mock('@sitewide/components/legacy/sitewide-constants');
jest.mock('../../../../legacy/sticky-manager-new', () => ({
  useStickyStatus: jest.fn(() => true),
}));
jest.mock('../../../../../datalayer');

jest.mock('../../../hooks/useAutoSuggest', () => ({
  useAutosuggest: () => {
    return {
      departmentsList: [
        { key: 'department', name: 'Women', value: 'Women' },
        { key: 'department', name: 'Men', value: 'Men' },
      ],
      suggestionsList: mockAutoSuggestData,
      visualSearchProducts: [],
    };
  },
}));

jest.mock('../../../hooks/useRecentSearches', () => ({
  useRecentSearches: () => {
    const [searches, setSearches] = React.useState({
      en_US: [{ searchTerm: 'jeans' }, { searchTerm: 'shirts' }],
    });

    const clearAllRecentSearch = () => {
      setSearches({ en_US: [] });
    };

    const removeSingleSearch = (termToRemove: string) => {
      setSearches(prev => ({
        ...prev,
        en_US: prev.en_US.filter(s => s.searchTerm !== termToRemove),
      }));
    };

    return {
      clearAllRecentSearch,
      removeSingleSearch,
      getRecentSearches: () => searches,
      addRecentSearch: jest.fn(),
    };
  },
}));
jest.mock('../../../utils/reportPageActionToNewRelic', () => ({
  reportPageActionToNewRelic: jest.fn(),
}));

describe('MobileSearchBar component', () => {
  const mockUseLocalize = useLocalize as jest.Mock;
  const mockUseAppState = useAppState as jest.Mock;
  const mockUseFeature = useFeature as jest.Mock;
  const mockUseStickyStatus = useStickyStatus as jest.Mock;
  mockUseStickyStatus.mockReturnValue((_stickyId: string) => {
    return undefined;
  });
  const mockReportPageActionToNewRelic = reportPageActionToNewRelic as jest.MockedFunction<typeof reportPageActionToNewRelic>;

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseLocalize.mockReturnValue({
      localize: mockTranslation,
    });
    mockUseAppState.mockReturnValue({
      topSearchTerms: topTrendingSearches,
      locale: 'en_US',
    });
    mockUseFeature.mockReturnValue(true);
  });
  const mobileSearchProps = {
    topSearchTerms: [...topTrendingSearches],
    useStickyStatus: mockUseStickyStatus(),
  };

  const renderWithBreakpoint = () => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const mockBreakpointContext: any = {
      size: 'sm',
      orientation: 'portrait',
      media: 'mobile',
      smallerThan: () => true,
    };
    return render(
      <BreakpointContext.Provider value={mockBreakpointContext}>
        <MobileSearchBar {...mobileSearchProps} />
      </BreakpointContext.Provider>
    );
  };

  const openSearchBar = async () => {
    const user = userEvent.setup();
    const searchButton = screen.getByRole('button', { name: /open search bar/i });
    await user.click(searchButton);
  };

  test('renders mobile search bar with the search button available', () => {
    renderWithBreakpoint();
    expect(screen.getByTestId('mobile-search-bar')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /open search bar/i })).toBeInTheDocument();
  });

  test('renders modal with search input box when search button is clicked', async () => {
    renderWithBreakpoint();
    await openSearchBar();
    expect(mockReportPageActionToNewRelic).toHaveBeenCalledWith('openMobileSearch');
    expect(screen.getByRole('dialog')).toBeInTheDocument();
    expect(screen.getByRole('textbox')).toBeInTheDocument();
  });

  test('renders mobile flyout with trending and recent search suggestions when search box is selected', async () => {
    const user = userEvent.setup();
    renderWithBreakpoint();
    await openSearchBar();

    expect(screen.getByTestId('mobile-search-flyout-wrapper')).toBeInTheDocument();
    await user.click(screen.getByRole('textbox'));

    expect(screen.getAllByRole('heading', { level: 4 })[0]).toHaveTextContent(RECENT_SEARCHES_HEADER);
    expect(screen.getByTestId('recent-search-terms')).toBeInTheDocument();

    expect(screen.getAllByRole('heading', { level: 4 })[1]).toHaveTextContent('Trending');
    expect(screen.getByTestId('trending-search-terms')).toBeInTheDocument();
  });

  test('renders mobile flyout with department suggestions and search terms wrapper when value is entered', async () => {
    const user = userEvent.setup();
    renderWithBreakpoint();
    await openSearchBar();

    expect(screen.getByTestId('mobile-search-flyout-wrapper')).toBeInTheDocument();
    await user.type(screen.getByRole('textbox'), 'jeans slim fit');

    expect(screen.getByTestId('mobile-search-flyout-with_search_term')).toBeInTheDocument();
    expect(screen.getByTestId('department-suggestions')).toBeInTheDocument();
    expect(screen.getByTestId('autosuggest-search-terms')).toBeInTheDocument();
    expect(screen.getByTestId('search-product-recommendations')).toBeInTheDocument();
  });
  test.each`
    pageType     | expected
    ${'search'}  | ${true}
    ${'product'} | ${false}
  `('persists search input value from URL query params  based on pageType', async ({ pageType, expected }) => {
    mockUseAppState.mockReturnValue({
      topSearchTerms: topTrendingSearches,
      locale: 'en_US',
      pageType,
    });
    const searchValue = 'shorts';
    const originalLocation = window.location;
    const mockLocation = {
      ...originalLocation,
      href: 'http://localhost/?searchText=shorts',
      search: '?searchText=shorts',
    } as Location;

    Object.defineProperty(window, 'location', {
      configurable: true,
      value: mockLocation,
    });
    renderWithBreakpoint();
    await openSearchBar();
    const searchInput = screen.getByRole('textbox');
    expect(searchInput).toBeInTheDocument();
    expect(screen.getByRole('textbox')).toHaveValue(expected ? searchValue : '');
    Object.defineProperty(window, 'location', {
      configurable: true,
      value: originalLocation,
    });
  });

  test('should not render flyout when recent and trending searches feature flags are disabled', async () => {
    mockUseFeature.mockReturnValue(false);

    const user = userEvent.setup();
    renderWithBreakpoint();
    await openSearchBar();

    const searchInputBox = screen.getByRole('textbox');
    await user.click(searchInputBox);

    expect(screen.queryByTestId('mobile-search-flyout-initial')).not.toBeInTheDocument();
  });

  test('removes a single recent search when the x icon is clicked', async () => {
    const user = userEvent.setup();
    renderWithBreakpoint();
    await openSearchBar();
    await user.click(screen.getByRole('textbox'));

    expect(screen.getByText('jeans')).toBeInTheDocument();
    expect(screen.getByText('shirts')).toBeInTheDocument();

    await user.click(screen.getByRole('button', { name: /Remove jeans/i }));

    expect(screen.queryByText('jeans')).not.toBeInTheDocument();
    expect(screen.getByText('shirts')).toBeInTheDocument();
  });

  test('clears recent searches when clear all button in recent search is clicked', async () => {
    const user = userEvent.setup();
    renderWithBreakpoint();
    await openSearchBar();
    await user.click(screen.getByRole('textbox'));

    expect(screen.getByTestId('recent-search-terms')).toBeInTheDocument();

    const clearButton = screen.getByRole('button', { name: RECENT_SEARCHES_CLEAR_ALL_BUTTON });
    await user.click(clearButton);

    expect(screen.queryByTestId('recent-search-terms')).not.toBeInTheDocument();
  });

  test('should not render recent searches when feature flag is disabled', async () => {
    mockUseFeature.mockImplementation(feature => feature !== features.SEARCH_HUI_RECENT_SEARCHES);

    const user = userEvent.setup();
    renderWithBreakpoint();
    await openSearchBar();
    await user.click(screen.getByRole('textbox'));

    expect(screen.queryByRole('heading', { name: RECENT_SEARCHES_HEADER })).not.toBeInTheDocument();
    expect(screen.queryByTestId('recent-search-terms')).not.toBeInTheDocument();
  });

  test('search input gains focus after modal opens', async () => {
    renderWithBreakpoint();
    await openSearchBar();

    await waitFor(
      () => {
        expect(document.activeElement).toBe(screen.getByRole('textbox'));
      },
      { timeout: 1000 }
    );
  });

  test('clicking clear button clears the input and shows initial flyout', async () => {
    const user = userEvent.setup();
    renderWithBreakpoint();
    await openSearchBar();
    const searchInput = screen.getByRole('textbox');
    await user.type(searchInput, 'some search term');
    expect(searchInput).toHaveValue('some search term');

    expect(screen.queryByTestId('mobile-search-flyout-initial')).not.toBeInTheDocument();

    const clearButton = screen.getByRole('button', { name: /clear/i });
    await user.click(clearButton);

    expect(searchInput).toHaveValue('');
    expect(screen.getByTestId('mobile-search-flyout-initial')).toBeInTheDocument();
  });

  test('clicking "Go Back" button closes the modal and clears the input', async () => {
    const user = userEvent.setup();
    renderWithBreakpoint();
    await openSearchBar();
    const searchInput = screen.getByRole('textbox');
    await user.type(searchInput, 'test value');
    expect(searchInput).toHaveValue('test value');

    const goBackButton = screen.getByRole('button', { name: /Close search/i });
    await user.click(goBackButton);
    expect(mockReportPageActionToNewRelic).toHaveBeenCalledWith('closeMobileSearch');
    expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    expect(searchInput).not.toBeInTheDocument();
  });

  describe('Redirect Search', () => {
    it('should redirect to search results page when a search term is submitted', async () => {
      const originalAssign = window.location.assign;
      window.location.assign = jest.fn();
      const user = userEvent.setup();
      renderWithBreakpoint();
      await openSearchBar();

      const searchInput = screen.getByRole('textbox');
      await user.type(searchInput, 'jean{enter}');

      expect(window.location.assign).toHaveBeenCalledTimes(1);
      expect(window.location.assign).toHaveBeenCalledWith('/browse/search.do?searchText=jean');
      window.location.assign = originalAssign;
    });

    test('should redirect to search results page with correct query param when a recent search term is clicked', async () => {
      const originalAssign = window.location.assign;
      window.location.assign = jest.fn();
      const user = userEvent.setup();
      renderWithBreakpoint();
      await openSearchBar();
      await user.click(screen.getByRole('textbox'));

      const recentSearch = screen.getByText('jeans');
      await user.click(recentSearch);

      expect(window.location.assign).toHaveBeenCalledTimes(1);
      expect(window.location.assign).toHaveBeenCalledWith('/browse/search.do?searchText=jeans&recentSearch=true');
      window.location.assign = originalAssign;
    });

    test('should redirect to search results page with correct query param when a trending search term is clicked', async () => {
      const [firstTrendingSearchText] = topTrendingSearches;
      const originalAssign = window.location.assign;
      window.location.assign = jest.fn();
      const user = userEvent.setup();
      renderWithBreakpoint();
      await openSearchBar();
      await user.click(screen.getByRole('textbox'));
      const trendingSearch = screen.getByTestId('trending-search-terms').querySelector('button, [role="button"]');
      expect(trendingSearch).toBeInTheDocument();
      expect(trendingSearch?.textContent?.trim()).toBe(firstTrendingSearchText);

      const firstTrendingSearch = await screen.findByRole('button', { name: firstTrendingSearchText });
      await user.click(firstTrendingSearch);

      expect(mockReportPageActionToNewRelic).toHaveBeenCalledWith('openMobileSearch');
      expect(mockReportPageActionToNewRelic).toHaveBeenCalledWith('topSearchTermClick');
      expect(mockReportPageActionToNewRelic).toHaveBeenCalledTimes(2);
      expect(window.location.assign).toHaveBeenCalledTimes(1);
      expect(window.location.assign).toHaveBeenCalledWith(`/browse/search.do?searchText=${firstTrendingSearchText}&trendingSearch=true`);
      window.location.assign = originalAssign;
    });

    test('should redirect to search results page with correct query param when an autosuggest term is clicked', async () => {
      const [expectedFirstAutoSuggestText] = mockAutoSuggestData;
      const originalAssign = window.location.assign;
      window.location.assign = jest.fn();
      const user = userEvent.setup();
      renderWithBreakpoint();
      await openSearchBar();
      const searchInput = screen.getByRole('textbox');
      await user.type(searchInput, 'sh');
      const autosuggestWrapper = screen.getByTestId('autosuggest-search-terms');
      const firstAutoSuggestTerm = autosuggestWrapper.querySelector('.suggested-search-term');
      expect(firstAutoSuggestTerm).toBeInTheDocument();
      expect(firstAutoSuggestTerm?.textContent?.trim()).toBe(expectedFirstAutoSuggestText);

      const firstAutoSuggestElement = await screen.findByText(expectedFirstAutoSuggestText);
      await user.click(firstAutoSuggestElement);

      expect(window.location.assign).toHaveBeenCalledTimes(1);
      expect(window.location.assign).toHaveBeenCalledWith(`/browse/search.do?searchText=${encodeURIComponent(expectedFirstAutoSuggestText)}&autosuggest=true`);
      window.location.assign = originalAssign;
    });

    test('should redirect to search results page with correct query param when a department suggestion is clicked', async () => {
      const originalAssign = window.location.assign;
      window.location.assign = jest.fn();
      const user = userEvent.setup();
      renderWithBreakpoint();
      await openSearchBar();
      const searchInput = screen.getByRole('textbox');
      await user.type(searchInput, 'jea');
      const departmentSuggestions = await screen.findByTestId('department-suggestions');
      const departmentButton = departmentSuggestions.querySelector('button, [role="button"]');
      expect(departmentButton).toBeInTheDocument();
      const departmentName = departmentButton?.textContent;

      await user.click(departmentButton as HTMLElement);

      expect(window.location.assign).toHaveBeenCalledTimes(1);
      expect(window.location.assign).toHaveBeenCalledWith(`/browse/search.do?searchText=jeans#department=${departmentName}`);
      window.location.assign = originalAssign;
    });
    describe('WHEN: the user clicks the search input, putting it into focus,', () => {
      test('THEN: it reports that event to Tealium', async () => {
        (reportToTealium as jest.Mock).mockImplementationOnce(jest.fn());
        const mockDatalayer = {
          build: jest.fn(),
          link: jest.fn(),
        };
        mockUseAppState.mockReturnValue({
          datalayer: mockDatalayer,
        });
        const user = userEvent.setup();

        renderWithBreakpoint();
        await openSearchBar();
        const searchInput = screen.getByRole('textbox');

        await user.click(searchInput);

        expect(reportToTealium).toHaveBeenCalledTimes(1);
      });
    });
  });
});
