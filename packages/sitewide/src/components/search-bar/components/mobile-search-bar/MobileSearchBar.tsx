import React, { useState, useEffect, useRef } from 'react';
import { TextSearchInput } from '@ecom-next/core/components/fabric/text-search-input';
import { useAppState } from '@ecom-next/sitewide/app-state-provider';
import { useFeature } from '@sitewide/components/legacy/sitewide-constants';
import { Modal as ModalComponent } from '@ecom-next/core/components/fabric/modal';
import { useRecentSearches } from '../../hooks/useRecentSearches';
import { RecentSearchesWrapper } from '../recent-searches/RecentSearchesWrapper';
import { TrendingSearchesWrapper } from '../top-search-terms/TrendingSearchesWrapper';
import { AutoSuggestWrapper } from '../autosuggest/AutoSuggestWrapper';
import { ProductRecommendationsWrapper } from '../product-recommendations/ProductRecommendations';
import { redirectSearch } from '../../utils/redirectSearch';
import { reportPageActionToNewRelic } from '../../utils/reportPageActionToNewRelic';
import { MobileSearchProps } from '../../types';
import { features } from '../../constants';
import { useSuggestions } from '../../hooks/useSuggestions';
import { reportToTealium } from '../../../../datalayer';
import { TEALIUM_EVENT } from '../../../ham-nav/v2/utils/constants';
import { GoBackButton } from './components/go-back-button';
import { OpenSearchButton } from './components/open-mobile-search';

const MobileSearchBar = (props: MobileSearchProps) => {
  const { topSearchTerms } = props;
  const [searchState, setSearchState] = useState({
    searchValue: '',
    isFocusOnSearch: false,
  });

  const [showSearchFlyout, setShowSearchFlyout] = useState(false);
  const [showSearchBar, setShowSearchBar] = useState(false);
  const { locale, pageType, datalayer } = useAppState();
  const { getRecentSearches, addRecentSearch, removeSingleSearch, clearAllRecentSearch } = useRecentSearches();
  const [recentSearches, setRecentSearches] = useState(getRecentSearches()[locale] || []);
  const isRecentSearchesEnabled = useFeature(features.SEARCH_HUI_RECENT_SEARCHES);
  const isTrendingSearchesActive = useFeature(features.SEARCH_HUI_TRENDING_SEARCHES);
  const shouldRenderRecentSearches = isRecentSearchesEnabled && recentSearches?.length > 0;
  const inputRef = useRef<HTMLInputElement>(null);
  const shouldRenderTrendingSearches = isTrendingSearchesActive && topSearchTerms?.length > 0;
  const isDesktop = false;
  const { suggestions, departmentsList, visualSearchProducts } = useSuggestions(
    searchState.searchValue,
    showSearchFlyout,
    false,
    recentSearches,
    isRecentSearchesEnabled,
    topSearchTerms,
    isDesktop
  );
  const autoSuggestions = { suggestionsList: suggestions, departmentsList, visualSearchProducts };

  const updateSearchValue = (value: string) => {
    setSearchState(prev => ({ ...prev, searchValue: value }));
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchBoxValue = e.target.value;
    updateSearchValue(searchBoxValue);
  };

  const handleSearchTermPersistence = () => {
    const url = new URL(window.location.href);
    const params = new URLSearchParams(url.search);
    if (params.has('searchText') && pageType === 'search') {
      const searchText = params.get('searchText') || '';
      setSearchState(prev => ({ ...prev, searchValue: searchText }));
    }
  };

  const handleClear = () => {
    setShowSearchFlyout(true);
    updateSearchValue('');
  };

  const handleBackButton = () => {
    setShowSearchBar(false);
    setShowSearchFlyout(false);
    updateSearchValue('');
    reportPageActionToNewRelic('closeMobileSearch');
  };

  const handleOnFocus = () => {
    if (datalayer) {
      reportToTealium(datalayer, TEALIUM_EVENT.SEARCH_BAR_CLICK);
    }
    setShowSearchFlyout(true);
    setSearchState(prev => ({ ...prev, isFocusOnSearch: true }));
  };

  const openModal = () => {
    if (showSearchBar) return;

    setShowSearchBar(true);
    setShowSearchFlyout(true);
    handleSearchTermPersistence();
    reportPageActionToNewRelic('openMobileSearch');
  };

  const handleSubmit = () => {
    if (searchState.searchValue.trim() !== '') {
      addRecentSearch(searchState.searchValue, locale);
      redirectSearch(searchState.searchValue);
    }
  };

  const handleClickRecentSearch = (searchTerm: string) => {
    addRecentSearch(searchTerm, locale);
    redirectSearch(searchTerm, false, '', true, false);
  };

  const handleClickTrendingSearch = (searchTerm: string) => {
    addRecentSearch(searchTerm, locale);
    reportPageActionToNewRelic('topSearchTermClick');
    redirectSearch(searchTerm, false, '', false, true);
  };
  const handleClickAutoDeptSearch = (searchTerm: string, department: string) => {
    addRecentSearch(searchTerm, locale);
    redirectSearch(searchTerm, false, department, false, false);
  };

  const handleClickAutoSuggestSearch = (searchTerm: string) => {
    addRecentSearch(searchTerm, locale);
    redirectSearch(searchTerm, true, '', false, false);
  };
  const handleClearAllRecentSearches = () => {
    clearAllRecentSearch(locale);
    setRecentSearches([]);
  };

  const handleRemoveSingleSearch = (searchTerm: string) => {
    removeSingleSearch(searchTerm, locale);
    const newSearchTermList = recentSearches.filter(term => term.searchTerm !== searchTerm);
    setRecentSearches(newSearchTermList);
  };

  useEffect(() => {
    handleSearchTermPersistence();
    if (showSearchBar) {
      const timer = setTimeout(() => {
        inputRef.current?.focus();
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [showSearchBar]);

  return (
    <div className='mobile-search-bar-wrapper' data-testid='mobile-search-bar'>
      <OpenSearchButton onClick={openModal} />
      {showSearchBar && (
        <ModalComponent
          portalRoot='#mobile-search-modal-root'
          className='mobile-search-modal'
          isOpen={showSearchBar}
          isFullScreenOnMobile={true}
          withHeader={false}
          withCloseIcon={false}
        >
          <div className='search-box-modal-wrapper' data-testid='mobile-search-flyout-wrapper'>
            <GoBackButton onClick={handleBackButton} />
            <TextSearchInput
              id='search-input'
              className='search-bar-input'
              ref={inputRef}
              value={searchState.searchValue}
              onChange={handleChange}
              onClear={handleClear}
              onFocus={handleOnFocus}
              onSearch={handleSubmit}
              ariaLabel='Search input'
              autoComplete='off'
              placeholder='Search'
            />
          </div>

          {showSearchFlyout && searchState.searchValue === '' && (shouldRenderRecentSearches || shouldRenderTrendingSearches) && (
            <div className='mobile-search-flyout' data-testid='mobile-search-flyout-initial'>
              {shouldRenderRecentSearches && (
                <RecentSearchesWrapper
                  displayRecentSearches={shouldRenderRecentSearches}
                  recentSearchesList={recentSearches}
                  clearAllRecentSearches={handleClearAllRecentSearches}
                  removeSingleSearch={handleRemoveSingleSearch}
                  handleClickRecentSearch={handleClickRecentSearch}
                />
              )}
              {shouldRenderTrendingSearches && (
                <TrendingSearchesWrapper topSearchTerms={topSearchTerms} handleClickTrendingSearch={handleClickTrendingSearch} />
              )}
            </div>
          )}
          {showSearchFlyout && searchState.searchValue !== '' && (
            <div className='mobile-search-flyout search-flyout-suggestions' data-testid='mobile-search-flyout-with_search_term'>
              <div className='mobile-suggestions-wrapper'>
                <AutoSuggestWrapper
                  searchText={searchState.searchValue}
                  suggestions={autoSuggestions}
                  handleClickAutoDeptSearch={handleClickAutoDeptSearch}
                  handleClickAutoSuggestSearch={handleClickAutoSuggestSearch}
                />
                <ProductRecommendationsWrapper searchText={searchState.searchValue} visualSearchProducts={visualSearchProducts} />
              </div>
            </div>
          )}
        </ModalComponent>
      )}
    </div>
  );
};

export { MobileSearchBar };
