'use client';

import { AutoSuggestResponse, ProductSearchAutoSuggestResponse } from '../types';
import { reportPageActionToNewRelic } from '../../utils/reportPageActionToNewRelic';
export const productSearchMapper = ({ suggestions = [], products = [] }: ProductSearchAutoSuggestResponse): AutoSuggestResponse => {
  const suggestionsList = suggestions.map(({ displayQuery }) => displayQuery);
  const [suggestion] = suggestions;
  const { filters: departmentsList = [] } = suggestion ?? {};

  if (products.length === 0) {
    reportPageActionToNewRelic('productsWithZeroItems');
  }

  if (suggestionsList.length === 0) {
    reportPageActionToNewRelic('suggestionsWithZeroItems');
  }

  return {
    departmentsList,
    suggestionsList,
    visualSearchProducts: products,
  };
};
