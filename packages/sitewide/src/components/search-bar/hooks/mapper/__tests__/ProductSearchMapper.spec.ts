import { productSearchMapper } from '../ProductSearchMapper';
import { reportPageActionToNewRelic } from '../../../utils/reportPageActionToNewRelic';
import type { ProductSearchAutoSuggestResponse, AutoSuggestResponse, VisualSearchProduct, ProductSearchSuggestions, Department } from '../../types';

jest.mock('../../../utils/reportPageActionToNewRelic', () => ({
  reportPageActionToNewRelic: jest.fn(),
}));

describe('productSearchMapper', () => {
  const mockReportPageActionToNewRelic = reportPageActionToNewRelic as jest.MockedFunction<typeof reportPageActionToNewRelic>;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic functionality', () => {
    it('should map complete response with products and suggestions correctly', () => {
      const mockProducts: VisualSearchProduct[] = [
        {
          pid: '1234567',
          price: 29.99,
          price_type: 'regular',
          sale_price: 24.99,
          sale_price_range: [20, 30],
          thumb_image: 'image1.jpg',
          title: 'Product 1',
          url: '/product1',
          variants: [
            {
              sku_color_group: 'blue',
              sku_thumb_images: ['thumb1.jpg', 'thumb2.jpg'],
            },
          ],
        },
        {
          pid: '1223455',
          price: 39.99,
          price_type: 'sale',
          sale_price: 29.99,
          sale_price_range: [25, 35],
          thumb_image: 'image2.jpg',
          title: 'Product 2',
          url: '/product2',
        },
      ];

      const mockSuggestions: ProductSearchSuggestions[] = [
        {
          displayQuery: 'blue shirts',
          query: 'blue shirts',
          filters: [
            { key: 'dept1', name: 'Men', value: 'men' },
            { key: 'dept2', name: 'Women', value: 'women' },
          ],
        },
        {
          displayQuery: 'casual wear',
          query: 'casual wear',
          filters: [{ key: 'dept3', name: 'Kids', value: 'kids' }],
        },
      ];

      const input: ProductSearchAutoSuggestResponse = {
        products: mockProducts,
        suggestions: mockSuggestions,
      };

      const result = productSearchMapper(input);

      const expectedResult: AutoSuggestResponse = {
        departmentsList: [
          { key: 'dept1', name: 'Men', value: 'men' },
          { key: 'dept2', name: 'Women', value: 'women' },
        ],
        suggestionsList: ['blue shirts', 'casual wear'],
        visualSearchProducts: mockProducts,
      };

      expect(result).toEqual(expectedResult);
      expect(mockReportPageActionToNewRelic).not.toHaveBeenCalled();
    });

    it('should handle empty input gracefully', () => {
      const input: ProductSearchAutoSuggestResponse = {};

      const result = productSearchMapper(input);

      expect(result).toEqual({
        departmentsList: [],
        suggestionsList: [],
        visualSearchProducts: [],
      });

      expect(mockReportPageActionToNewRelic).toHaveBeenCalledTimes(2);
      expect(mockReportPageActionToNewRelic).toHaveBeenCalledWith('productsWithZeroItems');
      expect(mockReportPageActionToNewRelic).toHaveBeenCalledWith('suggestionsWithZeroItems');
    });

    it('should use default empty arrays when properties are undefined', () => {
      const input: ProductSearchAutoSuggestResponse = {
        products: undefined,
        suggestions: undefined,
      };

      const result = productSearchMapper(input);

      expect(result).toEqual({
        departmentsList: [],
        suggestionsList: [],
        visualSearchProducts: [],
      });

      expect(mockReportPageActionToNewRelic).toHaveBeenCalledTimes(2);
      expect(mockReportPageActionToNewRelic).toHaveBeenCalledWith('productsWithZeroItems');
      expect(mockReportPageActionToNewRelic).toHaveBeenCalledWith('suggestionsWithZeroItems');
    });
  });

  describe('Products handling', () => {
    it('should pass through products array unchanged', () => {
      const mockProducts: VisualSearchProduct[] = [
        {
          pid: '199299',
          price: 50.0,
          price_type: 'regular',
          sale_price: 40.0,
          sale_price_range: [35, 45],
          thumb_image: 'test-image.jpg',
          title: 'Test Product',
          url: '/test-product',
        },
      ];

      const input: ProductSearchAutoSuggestResponse = {
        products: mockProducts,
        suggestions: [{ displayQuery: 'test', query: 'test' }],
      };

      const result = productSearchMapper(input);

      expect(result.visualSearchProducts).toEqual(mockProducts);
      expect(result.visualSearchProducts).toBe(mockProducts); // Reference equality
      expect(mockReportPageActionToNewRelic).not.toHaveBeenCalled();
    });

    it('should report when products array is empty', () => {
      const input: ProductSearchAutoSuggestResponse = {
        products: [],
        suggestions: [{ displayQuery: 'test', query: 'test' }],
      };

      const result = productSearchMapper(input);

      expect(result.visualSearchProducts).toEqual([]);
      expect(mockReportPageActionToNewRelic).toHaveBeenCalledWith('productsWithZeroItems');
      expect(mockReportPageActionToNewRelic).toHaveBeenCalledTimes(1);
    });

    it('should report when products array is missing (undefined)', () => {
      const input: ProductSearchAutoSuggestResponse = {
        suggestions: [{ displayQuery: 'test', query: 'test' }],
      };

      const result = productSearchMapper(input);

      expect(result.visualSearchProducts).toEqual([]);
      expect(mockReportPageActionToNewRelic).toHaveBeenCalledWith('productsWithZeroItems');
    });

    it('should handle products with minimal properties', () => {
      const mockProducts: VisualSearchProduct[] = [
        {
          pid: '122',
          price: 0,
          price_type: '',
          sale_price: 0,
          sale_price_range: [],
          thumb_image: '',
          title: '',
          url: '',
        },
      ];

      const input: ProductSearchAutoSuggestResponse = {
        products: mockProducts,
        suggestions: [{ displayQuery: 'test', query: 'test' }],
      };

      const result = productSearchMapper(input);

      expect(result.visualSearchProducts).toEqual(mockProducts);
      expect(mockReportPageActionToNewRelic).not.toHaveBeenCalled();
    });

    it('should handle products with variants', () => {
      const mockProducts: VisualSearchProduct[] = [
        {
          pid: '123098',
          price: 25.99,
          price_type: 'sale',
          sale_price: 19.99,
          sale_price_range: [15, 25],
          thumb_image: 'variant-image.jpg',
          title: 'Variant Product',
          url: '/variant-product',
          variants: [
            {
              sku_color_group: 'red',
              sku_thumb_images: ['red1.jpg', 'red2.jpg'],
            },
            {
              sku_color_group: 'blue',
              sku_thumb_images: ['blue1.jpg'],
            },
          ],
        },
      ];

      const input: ProductSearchAutoSuggestResponse = {
        products: mockProducts,
        suggestions: [{ displayQuery: 'test', query: 'test' }],
      };

      const result = productSearchMapper(input);

      expect(result.visualSearchProducts).toEqual(mockProducts);
      expect(result.visualSearchProducts[0].variants).toHaveLength(2);
    });
  });

  describe('Suggestions handling', () => {
    it('should extract displayQuery from suggestions correctly', () => {
      const mockSuggestions: ProductSearchSuggestions[] = [
        { displayQuery: 'summer dress', query: 'summer-dress' },
        { displayQuery: 'winter coat', query: 'winter-coat' },
        { displayQuery: 'casual shoes', query: 'casual-shoes' },
      ];

      const input: ProductSearchAutoSuggestResponse = {
        products: [
          {
            pid: 'test',
            price: 10,
            price_type: 'regular',
            sale_price: 10,
            sale_price_range: [],
            thumb_image: '',
            title: '',
            url: '',
          },
        ],
        suggestions: mockSuggestions,
      };

      const result = productSearchMapper(input);

      expect(result.suggestionsList).toEqual(['summer dress', 'winter coat', 'casual shoes']);
      expect(mockReportPageActionToNewRelic).not.toHaveBeenCalled();
    });

    it('should report when suggestions array is empty', () => {
      const input: ProductSearchAutoSuggestResponse = {
        products: [
          {
            pid: 'test',
            price: 10,
            price_type: 'regular',
            sale_price: 10,
            sale_price_range: [],
            thumb_image: '',
            title: '',
            url: '',
          },
        ],
        suggestions: [],
      };

      const result = productSearchMapper(input);

      expect(result.suggestionsList).toEqual([]);
      expect(mockReportPageActionToNewRelic).toHaveBeenCalledWith('suggestionsWithZeroItems');
      expect(mockReportPageActionToNewRelic).toHaveBeenCalledTimes(1);
    });

    it('should report when suggestions array is missing (undefined)', () => {
      const input: ProductSearchAutoSuggestResponse = {
        products: [
          {
            pid: 'test',
            price: 10,
            price_type: 'regular',
            sale_price: 10,
            sale_price_range: [],
            thumb_image: '',
            title: '',
            url: '',
          },
        ],
      };

      const result = productSearchMapper(input);

      expect(result.suggestionsList).toEqual([]);
      expect(mockReportPageActionToNewRelic).toHaveBeenCalledWith('suggestionsWithZeroItems');
    });

    it('should handle suggestions with empty displayQuery', () => {
      const mockSuggestions: ProductSearchSuggestions[] = [
        { displayQuery: '', query: 'empty-display' },
        { displayQuery: 'valid suggestion', query: 'valid' },
        { displayQuery: '', query: 'another-empty' },
      ];

      const input: ProductSearchAutoSuggestResponse = {
        products: [
          {
            pid: 'test',
            price: 10,
            price_type: 'regular',
            sale_price: 10,
            sale_price_range: [],
            thumb_image: '',
            title: '',
            url: '',
          },
        ],
        suggestions: mockSuggestions,
      };

      const result = productSearchMapper(input);

      expect(result.suggestionsList).toEqual(['', 'valid suggestion', '']);
    });
  });

  describe('Departments handling', () => {
    it('should extract departments from first suggestion filters', () => {
      const mockDepartments: Department[] = [
        { key: 'dept1', name: "Men's Clothing", value: 'mens' },
        { key: 'dept2', name: "Women's Clothing", value: 'womens' },
        { key: 'dept3', name: 'Kids & Baby', value: 'kids' },
      ];

      const mockSuggestions: ProductSearchSuggestions[] = [
        {
          displayQuery: 'clothing',
          query: 'clothing',
          filters: mockDepartments,
        },
        {
          displayQuery: 'accessories',
          query: 'accessories',
          filters: [{ key: 'dept4', name: 'Accessories', value: 'accessories' }],
        },
      ];

      const input: ProductSearchAutoSuggestResponse = {
        products: [
          {
            pid: 'test',
            price: 10,
            price_type: 'regular',
            sale_price: 10,
            sale_price_range: [],
            thumb_image: '',
            title: '',
            url: '',
          },
        ],
        suggestions: mockSuggestions,
      };

      const result = productSearchMapper(input);

      expect(result.departmentsList).toEqual(mockDepartments);
      // Should only use filters from first suggestion, not second
      expect(result.departmentsList).not.toContainEqual({ key: 'dept4', name: 'Accessories', value: 'accessories' });
    });

    it('should return empty departments when first suggestion has no filters', () => {
      const mockSuggestions: ProductSearchSuggestions[] = [
        {
          displayQuery: 'no filters',
          query: 'no-filters',
          // No filters property
        },
        {
          displayQuery: 'has filters',
          query: 'has-filters',
          filters: [{ key: 'dept1', name: 'Some Department', value: 'some' }],
        },
      ];

      const input: ProductSearchAutoSuggestResponse = {
        products: [
          {
            pid: 'test',
            price: 10,
            price_type: 'regular',
            sale_price: 10,
            sale_price_range: [],
            thumb_image: '',
            title: '',
            url: '',
          },
        ],
        suggestions: mockSuggestions,
      };

      const result = productSearchMapper(input);

      expect(result.departmentsList).toEqual([]);
    });

    it('should return empty departments when first suggestion filters is undefined', () => {
      const mockSuggestions: ProductSearchSuggestions[] = [
        {
          displayQuery: 'undefined filters',
          query: 'undefined-filters',
          filters: undefined,
        },
      ];

      const input: ProductSearchAutoSuggestResponse = {
        products: [
          {
            pid: 'test',
            price: 10,
            price_type: 'regular',
            sale_price: 10,
            sale_price_range: [],
            thumb_image: '',
            title: '',
            url: '',
          },
        ],
        suggestions: mockSuggestions,
      };

      const result = productSearchMapper(input);

      expect(result.departmentsList).toEqual([]);
    });

    it('should return empty departments when suggestions array is empty', () => {
      const input: ProductSearchAutoSuggestResponse = {
        products: [
          {
            pid: 'test',
            price: 10,
            price_type: 'regular',
            sale_price: 10,
            sale_price_range: [],
            thumb_image: '',
            title: '',
            url: '',
          },
        ],
        suggestions: [],
      };

      const result = productSearchMapper(input);

      expect(result.departmentsList).toEqual([]);
    });
  });

  describe('Reporting scenarios', () => {
    it('should report both conditions when both products and suggestions are empty', () => {
      const input: ProductSearchAutoSuggestResponse = {
        products: [],
        suggestions: [],
      };

      productSearchMapper(input);

      expect(mockReportPageActionToNewRelic).toHaveBeenCalledTimes(2);
      expect(mockReportPageActionToNewRelic).toHaveBeenCalledWith('productsWithZeroItems');
      expect(mockReportPageActionToNewRelic).toHaveBeenCalledWith('suggestionsWithZeroItems');
    });

    it('should only report products when only products are empty', () => {
      const input: ProductSearchAutoSuggestResponse = {
        products: [],
        suggestions: [{ displayQuery: 'test', query: 'test' }],
      };

      productSearchMapper(input);

      expect(mockReportPageActionToNewRelic).toHaveBeenCalledTimes(1);
      expect(mockReportPageActionToNewRelic).toHaveBeenCalledWith('productsWithZeroItems');
      expect(mockReportPageActionToNewRelic).not.toHaveBeenCalledWith('suggestionsWithZeroItems');
    });

    it('should only report suggestions when only suggestions are empty', () => {
      const input: ProductSearchAutoSuggestResponse = {
        products: [
          {
            pid: 'test',
            price: 10,
            price_type: 'regular',
            sale_price: 10,
            sale_price_range: [],
            thumb_image: '',
            title: '',
            url: '',
          },
        ],
        suggestions: [],
      };

      productSearchMapper(input);

      expect(mockReportPageActionToNewRelic).toHaveBeenCalledTimes(1);
      expect(mockReportPageActionToNewRelic).toHaveBeenCalledWith('suggestionsWithZeroItems');
      expect(mockReportPageActionToNewRelic).not.toHaveBeenCalledWith('productsWithZeroItems');
    });

    it('should not report anything when both arrays have content', () => {
      const input: ProductSearchAutoSuggestResponse = {
        products: [
          {
            pid: 'test',
            price: 10,
            price_type: 'regular',
            sale_price: 10,
            sale_price_range: [],
            thumb_image: '',
            title: '',
            url: '',
          },
        ],
        suggestions: [{ displayQuery: 'test', query: 'test' }],
      };

      productSearchMapper(input);

      expect(mockReportPageActionToNewRelic).not.toHaveBeenCalled();
    });
  });
});
