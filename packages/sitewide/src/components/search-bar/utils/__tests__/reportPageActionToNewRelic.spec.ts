import { reportPageActionToNewRelic } from '../reportPageActionToNewRelic';
import { addPageAction } from '@ecom-next/core/reporting';

jest.mock('@ecom-next/core/reporting', () => ({
  addPageAction: jest.fn(),
}));

describe('reportPageActionToNewRelic', () => {
  const mockAddPageAction = addPageAction as jest.MockedFunction<typeof addPageAction>;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Valid event names', () => {
    describe('openMobileSearch event', () => {
      it('should call addPageAction with correct action and attributes for openMobileSearch', () => {
        reportPageActionToNewRelic('openMobileSearch');

        expect(mockAddPageAction).toHaveBeenCalledTimes(1);
        expect(mockAddPageAction).toHaveBeenCalledWith('hui-openMobileSearch', { exposedSearchExperiment: 'false' });
      });
    });

    describe('closeMobileSearch event', () => {
      it('should call addPageAction with correct action and attributes for closeMobileSearch', () => {
        reportPageActionToNewRelic('closeMobileSearch');

        expect(mockAddPageAction).toHaveBeenCalledTimes(1);
        expect(mockAddPageAction).toHaveBeenCalledWith('hui-closeMobileSearch', { exposedSearchExperiment: 'false' });
      });
    });

    describe('topSearchTermClick event', () => {
      it('should call addPageAction with correct action and attributes for topSearchTermClick', () => {
        reportPageActionToNewRelic('topSearchTermClick');

        expect(mockAddPageAction).toHaveBeenCalledTimes(1);
        expect(mockAddPageAction).toHaveBeenCalledWith('hui-topSearchTermsDivisionPreference', { departmentName: '' });
      });
    });

    describe('productsWithZeroItems event', () => {
      it('should call addPageAction with correct action and empty attributes for productsWithZeroItems', () => {
        reportPageActionToNewRelic('productsWithZeroItems');

        expect(mockAddPageAction).toHaveBeenCalledTimes(1);
        expect(mockAddPageAction).toHaveBeenCalledWith('hui-psApiAutosuggestProductsWithZeroItems', {});
      });
    });

    describe('suggestionsWithZeroItems event', () => {
      it('should call addPageAction with correct action and empty attributes for suggestionsWithZeroItems', () => {
        reportPageActionToNewRelic('suggestionsWithZeroItems');

        expect(mockAddPageAction).toHaveBeenCalledTimes(1);
        expect(mockAddPageAction).toHaveBeenCalledWith('hui-psApiAutosuggestSuggestionsWithZeroItems', {});
      });
    });
  });

  describe('Invalid event names and edge cases', () => {
    it('should not call addPageAction for unknown event name', () => {
      reportPageActionToNewRelic('unknownEvent');

      expect(mockAddPageAction).not.toHaveBeenCalled();
    });

    it('should not call addPageAction for empty string event name', () => {
      reportPageActionToNewRelic('');

      expect(mockAddPageAction).not.toHaveBeenCalled();
    });

    it('should not call addPageAction for null event name', () => {
      reportPageActionToNewRelic(null as any);

      expect(mockAddPageAction).not.toHaveBeenCalled();
    });

    it('should not call addPageAction for undefined event name', () => {
      reportPageActionToNewRelic(undefined as any);

      expect(mockAddPageAction).not.toHaveBeenCalled();
    });

    it('should handle case-sensitive event names correctly', () => {
      // These should not match because the function is case-sensitive
      const caseMismatchEvents = ['OpenMobileSearch', 'OPENMOBILESEARCH', 'openMobileSearch ', ' openMobileSearch', 'openMobileSearch\n', 'openMobileSearch\t'];

      caseMismatchEvents.forEach(eventName => {
        reportPageActionToNewRelic(eventName);
      });

      expect(mockAddPageAction).not.toHaveBeenCalled();
    });
  });

  describe('Multiple calls and state management', () => {
    it('should handle multiple consecutive calls with different events', () => {
      reportPageActionToNewRelic('openMobileSearch');
      reportPageActionToNewRelic('closeMobileSearch');
      reportPageActionToNewRelic('topSearchTermClick');

      expect(mockAddPageAction).toHaveBeenCalledTimes(3);
      expect(mockAddPageAction).toHaveBeenNthCalledWith(1, 'hui-openMobileSearch', {
        exposedSearchExperiment: 'false',
      });
      expect(mockAddPageAction).toHaveBeenNthCalledWith(2, 'hui-closeMobileSearch', {
        exposedSearchExperiment: 'false',
      });
      expect(mockAddPageAction).toHaveBeenNthCalledWith(3, 'hui-topSearchTermsDivisionPreference', {
        departmentName: '',
      });
    });

    it('should handle multiple calls with same event', () => {
      reportPageActionToNewRelic('openMobileSearch');
      reportPageActionToNewRelic('openMobileSearch');

      expect(mockAddPageAction).toHaveBeenCalledTimes(2);
      expect(mockAddPageAction).toHaveBeenNthCalledWith(1, 'hui-openMobileSearch', {
        exposedSearchExperiment: 'false',
      });
      expect(mockAddPageAction).toHaveBeenNthCalledWith(2, 'hui-openMobileSearch', {
        exposedSearchExperiment: 'false',
      });
    });

    it('should handle mix of valid and invalid events', () => {
      reportPageActionToNewRelic('openMobileSearch');
      reportPageActionToNewRelic('invalidEvent');
      reportPageActionToNewRelic('closeMobileSearch');
      reportPageActionToNewRelic('');
      reportPageActionToNewRelic('topSearchTermClick');

      expect(mockAddPageAction).toHaveBeenCalledTimes(3);
      expect(mockAddPageAction).toHaveBeenNthCalledWith(1, 'hui-openMobileSearch', {
        exposedSearchExperiment: 'false',
      });
      expect(mockAddPageAction).toHaveBeenNthCalledWith(2, 'hui-closeMobileSearch', {
        exposedSearchExperiment: 'false',
      });
      expect(mockAddPageAction).toHaveBeenNthCalledWith(3, 'hui-topSearchTermsDivisionPreference', {
        departmentName: '',
      });
    });
  });

  describe('Error handling', () => {
    it('should catch and handle errors when addPageAction throws', () => {
      mockAddPageAction.mockImplementation(() => {
        throw new Error('New Relic error');
      });

      expect(() => {
        reportPageActionToNewRelic('openMobileSearch');
      }).not.toThrow();

      expect(mockAddPageAction).toHaveBeenCalledTimes(1);
    });

    it('should continue working after an error occurs', () => {
      // First call throws an error
      mockAddPageAction.mockImplementationOnce(() => {
        throw new Error('First call error');
      });

      // Second call should work normally
      mockAddPageAction.mockImplementationOnce(() => {});

      expect(() => {
        reportPageActionToNewRelic('openMobileSearch');
        reportPageActionToNewRelic('closeMobileSearch');
      }).not.toThrow();

      expect(mockAddPageAction).toHaveBeenCalledTimes(2);
    });
  });
});
