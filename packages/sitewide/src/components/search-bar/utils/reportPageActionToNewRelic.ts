import { addPageAction } from '@ecom-next/core/reporting';

const NEW_RELIC_OPEN_MOBILE_SEARCH = 'hui-openMobileSearch';
const NEW_RELIC_CLOSE_MOBILE_SEARCH = 'hui-closeMobileSearch';
const NEW_RELIC_TOPSEARCHTERMS_DIVISION_PREFERENCE = 'hui-topSearchTermsDivisionPreference';
const NEW_RELIC_ZERO_ITEMS = 'hui-psApiAutosuggestProductsWithZeroItems';
const NEW_RELIC_ZERO_ITEMS_SUGGESTIONS = 'hui-psApiAutosuggestSuggestionsWithZeroItems';

export const reportPageActionToNewRelic = (eventName: string) => {
  try {
    let attributes: Record<string, any> | undefined;
    let action: string | undefined = undefined;
    switch (eventName) {
      case 'openMobileSearch':
        action = NEW_RELIC_OPEN_MOBILE_SEARCH;
        attributes = { exposedSearchExperiment: 'false' };
        break;
      case 'closeMobileSearch':
        action = NEW_RELIC_CLOSE_MOBILE_SEARCH;
        attributes = { exposedSearchExperiment: 'false' };
        break;
      case 'topSearchTermClick':
        action = NEW_RELIC_TOPSEARCHTERMS_DIVISION_PREFERENCE;
        attributes = { departmentName: '' };
        break;
      case 'productsWithZeroItems':
        action = NEW_RELIC_ZERO_ITEMS;
        break;
      case 'suggestionsWithZeroItems':
        action = NEW_RELIC_ZERO_ITEMS_SUGGESTIONS;
        break;
      default:
        break;
    }
    if (action) {
      addPageAction(action, attributes ?? {});
    }
  } catch (error) {
    // log the error or handle it as needed
  }
};
