'use client';
import { useContext, useState } from 'react';
import Division from './Division';
import { NavigationContext } from '@sitewide/components/legacy/navigation/navigation-provider';
import { mapChildrenToContent } from './utils/mapChildrenToContent';
import { features } from '@sitewide/constants';
import { useFeature } from '../legacy/experiments';

const WcdInjectedStyles = ({ classStyles }: { classStyles: Record<string, string> }): JSX.Element => {
  const cssStyles = Object.entries(classStyles).reduce(
    (cssAccumulator: string, [newClass, newCss]: string[]) => `${cssAccumulator}#topNavWrapper .${newClass} {${newCss}}`,
    ''
  );
  return <style dangerouslySetInnerHTML={{ __html: cssStyles }} data-testid='mega-nav-wcd-injected-styles' />;
};

type ClassStylesContentData = Record<string, string>;
export type ActiveDivisionsContentData = {
  customStyles?: {
    [key: string]: {
      inlineStyle: Record<string, string>;
    };
  };
  divisionId: string[];
  exclusionIds: string[];
  megaNavOrder: string[][];
  name: string;
  numberOfColumns?: Record<string, number>;
  subtitle?: string;
};
type TopNavContentData = {
  data: {
    classStyles: ClassStylesContentData;
    activeDivisions: ActiveDivisionsContentData[];
  };
};

export default function MegaNav({ data: { classStyles, activeDivisions } }: TopNavContentData) {
  const shouldDisableInjectedStyles = useFeature(features.SWF_HUI_DISABLE_REWRITE_MEGANAV_INJECTED_STYLES); // to facilitate ux/ui and brand qa
  const [showIsolationLayer, setShowIsolationLayer] = useState<boolean>(false);
  const navData = useContext(NavigationContext)?.webHierarchy || [];
  const mappedNavData = mapChildrenToContent(activeDivisions, navData);

  const topnavContainerClasses = 'topnavContainer sw_mega-nav__topnav-container';
  const divisionListClasses = 'topnav sw_mega-nav__division-list';

  return (
    <>
      {classStyles && !shouldDisableInjectedStyles && <WcdInjectedStyles classStyles={classStyles} />}
      <nav className={topnavContainerClasses} aria-label='Top Bar' id='topNavWrapper' data-testid='topNavWrapper' data-hui-rewrite='mega-nav'>
        <div className='topnav-container'>
          <ul className={divisionListClasses} data-testid='topnav-navdivisions' role='menu'>
            {Boolean(mappedNavData.length) &&
              mappedNavData.map(division => <Division division={division} key={division.name} setShowIsolationLayer={setShowIsolationLayer} />)}
          </ul>
        </div>
      </nav>
      {showIsolationLayer && <div className='sw_mega-nav__isolation-layer' />}
    </>
  );
}
