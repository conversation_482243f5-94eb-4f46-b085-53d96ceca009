@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@layer components {
  .sw_mega-nav__topnav-container {
    display: block;
    width: 100%;
    background-color: transparent;
    padding: 0;
  }

  .sw_mega-nav__division-list {
    display: flex;
    list-style-type: none;
    max-width: 1280px;
    text-align: center;
    text-transform: none;
    width: 100%;
  }

  .sw_mega-nav__division {
    box-sizing: border-box;
    display: inline-block;
    flex: auto;
    list-style-type: none;
    height: 100%;
    margin: 0 auto;
    padding: 35px 0;
    position: static;
    text-align: center;
    vertical-align: middle;
    white-space: nowrap;
    z-index: 401;

    &:hover {
      z-index: 400;

      .sw_mega-nav__division-link {
        text-decoration: underline 1px theme('colors.g2');
      }

      .sw_mega-nav__division-subtitle {
        visibility: visible;
      }
    }
  }

  .sw_mega-nav__division-link {
    background: transparent;
    border: 0;
    color: theme('colors.color-type-accent');
    width: 100%;
    font-family: theme('fontFamily.style-mega-nav-font-family');
    font-size: theme('fontSize.style-mega-nav-font-size');
    font-weight: theme('fontWeight.style-mega-nav-font-weight');
  }

  .sw_mega-nav__division-subtitle {
    font-weight: normal;
    display: inline-block;
    margin-bottom: 5px;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    visibility: hidden;
  }

  .sw_mega-nav__flyout {
    background-color: white;
    justify-content: space-around;
    text-align: center;
    overflow: auto;
    white-space: normal;
    width: 100%;
    padding-top: 16px;
    position: absolute;
    left: 0;
    top: 95%;
    z-index: 401;
    visibility: hidden;
    transition: 0s visibility;
    transition-delay: 200ms;
    display: flex;
    flex-direction: column;

    &.sw_mega-nav__flyout-visible {
      visibility: visible;
    }
  }

  .sw_mega-nav__flyout__stacked-columns-with-marketing-images {
    flex-direction: row;
    align-items: flex-start;
    justify-content: center;
  }

  .sw_mega-nav__column-list-container {
    width: min-content;
  }

  .sw_mega-nav__column-list {
    display: inline-flex;
    justify-content: center;
    text-align: left;
    padding: 0 16px 24px 16px;

    &:last-of-type {
      padding-bottom: 32px;
    }
  }

  .sw_mega-nav__column {
    /* enforcing max width for pictures */
    max-width: 208px;

    &:not(:first-of-type) {
      margin-left: 48px;
    }

    &.sw_mega-nav__column--double-width {
      /* Reference legacy formula: columnWidth * 2 + gutterWidth */
      width: calc((208px * 2) + 32px);
    }
  }

  .sw_mega-nav__category-group {
    &:not(:last-child) {
      margin-bottom: 24px;
    }
  }

  .sw_mega-nav__category-group-inner {
    line-height: 15px;
    display: block;
    letter-spacing: 0;
    text-align: left;
  }

  .sw_mega-nav__category-group-header {
    color: theme('colors.color-type-copy');
    font-family: theme('fontFamily.font-family-base');
    font-size: theme('fontSize.font-size-0');
    font-weight: theme('fontWeight.font-weight-base-heavier');
    letter-spacing: theme('letterSpacing.style-mega-nav-letter-spacing');
    display: block;
    margin-bottom: 12px;

    &:hover:is(a) {
      color: theme('colors.color-type-link');
      text-decoration: underline;
    }
  }

  .sw_mega-nav__category-list {
    &.sw_mega-nav__category-list--split-column {
      float: left;
      /* Reference legacy formula: calc(50% - ${gutterWidth / 2}px) */
      width: calc(50% - (32px / 2));

      &:not(:first-of-type) {
        margin-left: 32px;
      }
    }
  }

  .sw_mega-nav__category {
    color: theme('colors.bk');
    line-height: 15px;
    padding: 5px 0;
    text-transform: none;

    &.sw_mega-nav__category--selected {
      background: #f2f2f2;
    }
  }

  .sw_mega-nav__category-link {
    display: inline;
    color: theme('colors.color-type-subtle');
    font-size: theme('fontSize.font-size--1');
    letter-spacing: theme('letterSpacing.style-mega-nav-letter-spacing');

    &:hover {
      color: theme('colors.color-type-link');
      text-decoration: underline 1px theme('colors.color-type-link');
      text-underline-offset: theme('spacing.utk-spacing-2xs');
    }
  }

  .sw_mega-nav__isolation-layer {
    width: 100%;
    height: calc(calc(100vh + 100%) * 2);
    background-color: #000;
    opacity: 50%;
    position: absolute;
    left: 0;
    top: 95%;
    z-index: 399;
  }

  .sw_mega-nav__title-banner-children {
    display: flex;
  }

  .sw_mega-nav__title-banner-components-container {
    display: flex;
    justify-content: center;
    padding: 16px 16px 32px 16px;
  }

  .sw_mega-nav__title-banner-component-container {
    &:not(:last-child) {
      margin-right: 48px;
    }

    .division-header {
      background: theme('colors.color-surface-info');
      color: theme('colors.color-type-copy');
      font-size: theme('fontSize.font-size-0');
      font-weight: theme('fontWeight.font-weight-base-heavier');
      text-align: start;
      padding: 8px;
      margin-bottom: 12px;

      span {
        font-weight: theme('fontWeight.font-weight-base-default');
      }
    }
  }

  .gap,
  .gapfs {
    .sw_mega-nav__topnav-container {
      margin: auto;
      max-width: 1248px;
    }

    .sw_mega-nav__division {
      &:hover {
        color: theme('colors.color-type-link');
        border-color: theme('colors.color-type-link');

        .sw_mega-nav__division-link {
          text-decoration: underline 1px theme('colors.color-type-link');
          text-underline-offset: 3px;
        }
      }
    }

    .sw_mega-nav__division-link {
      letter-spacing: theme('letterSpacing.style-mega-nav-letter-spacing');
      box-shadow: none;

      &::before {
        content: '';
        border-style: solid;
        border-width: 0 0 1px;
        border-color: theme('colors.alpha00');
      }

      &.sw_mega-nav__division-link--selected::before {
        border-color: theme('colors.b1');
      }
    }

    .sw_mega-nav__flyout {
      border-top: 1px solid theme('colors.g3');
    }

    .sw_mega-nav__category {
      color: theme('colors.b1');
      letter-spacing: theme('letterSpacing.style-mega-nav-letter-spacing');
    }
  }

  .on {
    .sw_mega-nav__topnav-container {
      margin-top: 1rem;
    }

    .sw_mega-nav__division {
      padding-top: 40px;

      .sw_mega-nav__division-link {
        text-decoration-color: theme('colors.b1');
      }
    }

    .sw_mega-nav__division-link {
      box-sizing: border-box;
      color: theme('colors.b1');
      display: inline-flex;
      position: relative;
      flex-direction: column;
      justify-content: space-between;
      letter-spacing: 0.1px;
      line-height: 1.2em;
      text-align: center;
      text-decoration: none;
      white-space: nowrap;
      padding-bottom: 24px;

      &.sw_mega-nav__division-link--selected {
        font-weight: bold;
        color: theme('colors.b1');
      }

      @media (min-width: theme('screens.lg')) {
        display: flex;
        justify-content: center;
        line-height: 0.8;
      }
    }

    .sw_mega-nav__flyout {
      margin-top: 1px;
    }

    .sw_mega-nav__column {
      text-align: left;
    }

    .sw_mega-nav__category-group-inner {
      display: block;
      letter-spacing: theme('letterSpacing.style-mega-nav-letter-spacing');
      text-align: left;
    }

    .sw_mega-nav__category-group-header {
      color: theme('colors.g1');
    }

    .sw_mega-nav__category {
      letter-spacing: theme('letterSpacing.style-mega-nav-letter-spacing');
    }
  }

  .br,
  .brfs {
    .division-header {
      background-color: rgb(242, 242, 242);
      letter-spacing: 0.2px;
    }

    .sw_mega-nav__topnav-container {
      position: static;
      letter-spacing: 2px;
      z-index: 401;
    }

    .sw_mega-nav__division-link {
      letter-spacing: inherit;
      line-height: normal;
      padding-bottom: 2px;
      text-transform: uppercase;
      width: unset;

      &.sw_mega-nav__division-link--selected {
        color: theme('colors.g3');
      }
    }

    .sw_mega-nav__category-group-header {
      letter-spacing: 0.2px;
    }
  }

  .at {
    .sw_mega-nav__topnav-container {
      letter-spacing: 0.5px;
    }

    /* for some reason it's not picking up these values from the brand agnostic block 🤷‍♂️ */
    .sw_mega-nav__division-link {
      background: transparent;
      border: 0;
      color: theme('colors.color-type-accent');
      width: 100%;
      font-family: theme('fontFamily.style-mega-nav-font-family');
      font-size: theme('fontSize.style-mega-nav-font-size');
      font-weight: theme('fontWeight.style-mega-nav-font-weight');
    }

    .sw_mega-nav__category-group-header {
      border-bottom: 0;
    }

    .sw_mega-nav__category {
      color: theme('colors.g2');
      font-size: 15px;
    }

    .division-header {
      letter-spacing: 0px;
    }
  }
}
