import { renderHook } from '@testing-library/react-hooks';
import { useAppState } from '@ecom-next/sitewide/app-state-provider';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { rest } from 'msw';
import { setupServer } from 'msw/node';
import { ReactNode } from 'react';
import { appliedFacetsToQueryParameters, sortByToQueryParameters, storeIdToQueryParameters, fetchProducts, usePsData } from '../usePsData';
import { CATEGORY_PAGE, SEARCH_PAGE, PageType } from '../../types';
import { Direction, Field } from '../../../../types/plp-data';
import { PLPStateProvider, PLPStateProviderProps } from '../../../../data/plp-state-provider';
import { Brands } from '../../../../types/generics';
import { getCookieValue, getURLSearchParam } from '../../../../utils/window-helper';
import { getAdaptedData } from '../../../ps-api-mappers';
import productsJson from '../../fixtures/products.json';
import { hasSizeParamForSearchPageInterim, mapSizeFacetForSearchPageInterim } from '../../hooks/helpers/interimForSearchPage/interimSizeHashMapper';

jest.mock('@ecom-next/sitewide/app-state-provider');
const mockUseAppState = useAppState as jest.Mock;

jest.mock('../../../ps-api-mappers');
(getAdaptedData as jest.Mock).mockImplementation(() => {});

jest.mock('../../hooks/helpers/interimForSearchPage/interimSizeHashMapper');
const mockHasSizeParamForSearchPageInterim = hasSizeParamForSearchPageInterim as jest.Mock;
const mockMapSizeFacetForSearchPageInterim = mapSizeFacetForSearchPageInterim as jest.Mock;

jest.mock('../../../../utils/window-helper', () => ({
  getCookieValue: jest.fn(),
  getURLSearchParam: jest.fn(),
}));

jest.mock('../../../../data/plp-data-layer/new-relic/report-to-new-relic', () => ({
  reportToNewRelic: jest.fn(),
  logErrorToNewRelic: jest.fn(),
}));

jest.mock('../../../../utils/window-helper', () => ({
  getCookieValue: jest.fn(),
  getURLSearchParam: jest.fn(),
}));

const fakeUrlCategory = 'http://fakeurl.com/commerce/search/products/v2/cc';
const fakeUrlSearch = 'http://fakeurl.com/commerce/search/products/v2/style';
const fakeUrlParams = 'pageSize=200&pageNumber=0&keyword=jeans&&brand=gap&locale=en-US&market=us';
const restHandlers = [
  rest.get(fakeUrlCategory, async (req, res, context) => {
    if (req.url.searchParams.get('cid') === '5664') {
      return res(context.status(200), context.json(productsJson));
    }
    if (req.url.searchParams.get('cid') === 'xxxx') {
      return res(context.status(404), context.json({}));
    }
    return res(context.status(500), context.json({}));
  }),
  rest.get(fakeUrlSearch, async (req, res, context) => {
    if (req.url.searchParams.get('keyword') === 'jeans') {
      return res(context.status(200), context.json(productsJson));
    }
    if (req.url.searchParams.get('keyword') === 'xxxx') {
      return res(context.status(404), context.json({}));
    }
    return res(context.status(500), context.json({}));
  }),
];

const mockedAppliedFacets = {
  style: [
    {
      id: '4564',
      name: 'Mid Rise',
      facetName: 'style',
      isActive: true,
      applied: true,
      value: '4564',
    },
  ],
  price: [{ facetName: 'price', name: 'Price', value: '10', id: '10', isActive: true, applied: true }],
  color: [
    { facetName: 'color', name: 'Color', value: 'red', id: 'red', isActive: true, applied: true },
    { facetName: 'color', name: 'Color', value: 'blue', id: 'blue', isActive: true, applied: true },
  ],
};

const mockedFacetOrder = {
  facetsOrder: {
    isDynamicFacetsEnabled: false,
    isFacetsOrderingEnabled: false,
    isManualFacetsEnabled: false,
    manualFacetConfig: undefined,
  },
};

const client = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

const wrapper = (plpState: PLPStateProviderProps) => {
  const WrapperComponent = ({ children }: { children: ReactNode }) => (
    <PLPStateProvider {...plpState}>
      <QueryClientProvider client={client}>{children}</QueryClientProvider>
    </PLPStateProvider>
  );
  WrapperComponent.displayName = 'WrapperComponent';
  return WrapperComponent;
};

const categoryPageType: PageType = 'category';
const searchPageType: PageType = 'search';
const facetsAndSortingOptions = {
  appliedFacets: {
    department: [
      {
        id: '1',
        name: 'a',
        facetName: 'a',
        isActive: true,
        applied: true,
        value: '1',
      },
    ],
    style: [
      {
        id: '2',
        name: 'b',
        facetName: 'b',
        isActive: true,
        applied: true,
        value: '2',
      },
    ],
  },
  sortByOptions: {
    sortByDir: 'desc',
    sortByField: 'style',
  },
};

const styleFacets = {
  appliedFacets: {
    department: [
      {
        id: '1',
        name: 'department',
        facetName: 'department',
        isActive: true,
        applied: true,
        value: 'Women',
      },
    ],
    style: [
      {
        id: '2',
        name: 'style',
        facetName: 'style',
        isActive: true,
        applied: true,
        value: 'Shirts & Blouses',
      },
      {
        id: '3',
        name: 'occasion',
        facetName: 'occasion',
        isActive: true,
        applied: true,
        value: 'Party & Date Night',
      },
    ],
  },
};

const successData = {
  wrapper: wrapper({
    abSeg: { pgGP: 'x' },
    brand: Brands.Gap,
    locale: 'en-US',
    market: 'us',
    children: undefined,
    isPreviewEnabled: false,
    pageType: 'category',
    enabledFeatures: {},
    marketAwareBrandCode: 0,
    selectedNodes: [],
  }),
};

describe('usePsData', () => {
  const server = setupServer(...restHandlers);

  beforeAll(() => {
    jest.spyOn(console, 'error').mockImplementation(jest.fn());
    server.listen({ onUnhandledRequest: 'error' });
  });

  afterAll(() => {
    server.close();
    server.resetHandlers();
    jest.clearAllMocks();
  });

  beforeEach(() => {
    (getCookieValue as jest.Mock).mockImplementation(name => {
      if (name === 'RES_TRACKINGID') {
        return 'y';
      } else if (name === 'ConstructorioID_client_id') {
        return '1';
      } else if (name === 'ConstructorioID_session_id') {
        return '2';
      } else if (name === 'cam') {
        return '3|4';
      } else if (name === 'pgGP') {
        return 'p';
      } else if (name === 'pgGPFS') {
        return 'x';
      }
      return '';
    });
    (getURLSearchParam as jest.Mock).mockImplementation(() => null);
    mockUseAppState.mockReturnValue({ previewDate: '' });
    mockHasSizeParamForSearchPageInterim.mockImplementation(() => false);
    mockHasSizeParamForSearchPageInterim.mockImplementation(() => { });
  });

  describe('Category', () => {
    describe('Success', () => {
      describe('GIVEN: It is a hybrid page,', () => {
        describe('WHEN: the hook is passed only a cid as a query param', () => {
          it('THEN: it should make a fetch request to the PS API successfully with that cid in the URI', async () => {
            const isHybrid = true;
            const { result, waitFor } = renderHook(() => usePsData(fakeUrlCategory, categoryPageType, { cid: '5664' }, isHybrid), successData);

            const expectedUrl =
              'pageSize=40&pageNumber=0&ignoreInventory=false&cid=5664&trackingid=y&includeMarketingFlagsDetails=true&brand=gap&locale=en-US&market=us';

            await waitFor(() => result?.current?.isSuccess);

            expect(result?.current?.isSuccess).toBeTruthy();
            expect(getAdaptedData).toHaveBeenCalled();
            expect(result?.current.data?.queryParameters).toBe(expectedUrl);
          });
        });
      });

      describe('GIVEN: it is NOT a hybrid page', () => {
        describe('WHEN: when the hook is given only a cid as a query param', () => {
          it('THEN: it should make a fetch request to the PS API successfully with that cid in the URI', async () => {
            const isHybrid = false;
            const { result, waitFor } = renderHook(() => usePsData(fakeUrlCategory, categoryPageType, { cid: '5664' }, isHybrid), successData);
            const expectedUrl =
              'pageSize=200&pageNumber=0&ignoreInventory=false&cid=5664&trackingid=y&includeMarketingFlagsDetails=true&brand=gap&locale=en-US&market=us';

            await waitFor(() => result?.current?.isSuccess);

            expect(result?.current?.isSuccess).toBeTruthy();
            expect(getAdaptedData).toHaveBeenCalled();
            expect(result?.current.data?.queryParameters).toBe(expectedUrl);
          });
        });
      });

      describe('GIVEN: a valid cid is passed', () => {
        describe('GIVEN: Preview date is present', () => {
          it('WHEN: previewDate is present in the state, THEN: should make a fetch request to the PS API with the cid and preview date as query params', async () => {
            const [cid, previewDate] = ['5664', '01/01/2015'];
            (mockUseAppState as jest.Mock).mockReturnValue({ previewDate });
            const { result, waitFor } = renderHook(() => usePsData(fakeUrlCategory, categoryPageType, { cid }, false), successData);
            const expectedUrl =
              'pageSize=200&pageNumber=0&ignoreInventory=false&cid=5664&trackingid=y&includeMarketingFlagsDetails=true&brand=gap&locale=en-US&market=us&previewDate=01%2F01%2F2015&mode=wip';

            await waitFor(() => result?.current?.isSuccess);

            expect(result?.current?.isSuccess).toBeTruthy();
            expect(getAdaptedData).toHaveBeenCalled();
            expect(result?.current.data?.queryParameters).toBe(expectedUrl);
            expect(window.__CATEGORY_PAGE_RESPONSE__).toEqual(productsJson);
          });
          it('WHEN: previewDate is NOT present in the state but on the URL, THEN: should make a fetch request to the PS API with the cid and preview date as query params', async () => {
            const [cid, previewDate] = ['5664', '02/02/2015'];
            (mockUseAppState as jest.Mock).mockReturnValue({ previewDate: null });
            function mockGetURLSearchParamForPreviewDate() {
              (getURLSearchParam as jest.Mock).mockImplementation((name: string) => (name === 'previewDate' ? previewDate : null));
            }
            mockGetURLSearchParamForPreviewDate();
            const { result, waitFor } = renderHook(() => usePsData(fakeUrlCategory, categoryPageType, { cid }, false), successData);
            const expectedUrl =
              'pageSize=200&pageNumber=0&ignoreInventory=false&cid=5664&trackingid=y&includeMarketingFlagsDetails=true&brand=gap&locale=en-US&market=us&previewDate=02%2F02%2F2015&mode=wip';

            await waitFor(() => result?.current?.isSuccess);

            expect(result?.current?.isSuccess).toBeTruthy();
            expect(getAdaptedData).toHaveBeenCalled();
            expect(result?.current.data?.queryParameters).toBe(expectedUrl);
            expect(window.__CATEGORY_PAGE_RESPONSE__).toEqual(productsJson);
          });
          it('WHEN: adAware is present in the URL with a value of true and page type is "category", THEN: should make a fetch request to the PS API passing query param "autoDiscovery" as true', async () => {
            const [cid, previewDate] = ['5664', '01/01/2015'];
            (mockUseAppState as jest.Mock).mockReturnValue({ previewDate });
            function mockGetURLSearchParamForAdAwareTrue() {
              (getURLSearchParam as jest.Mock).mockImplementation((name: string) => (name === 'adAware' ? 'true' : null));
            }
            mockGetURLSearchParamForAdAwareTrue();
            const { result, waitFor } = renderHook(() => usePsData(fakeUrlCategory, categoryPageType, { cid }, false), successData);
            const expectedUrl =
              'pageSize=200&pageNumber=0&ignoreInventory=false&cid=5664&trackingid=y&includeMarketingFlagsDetails=true&brand=gap&locale=en-US&market=us&previewDate=01%2F01%2F2015&mode=wip&autoDiscovery=true';

            await waitFor(() => result?.current?.isSuccess);

            expect(result?.current?.isSuccess).toBeTruthy();
            expect(getAdaptedData).toHaveBeenCalled();
            expect(result?.current.data?.queryParameters).toBe(expectedUrl);
          });
          it('WHEN: adAware is present in the URL with a value of false and page type is "category", THEN: should make a fetch request to the PS API passing query param "autoDiscovery" as false', async () => {
            const [cid, previewDate] = ['5664', '01/01/2015'];
            (mockUseAppState as jest.Mock).mockReturnValue({ previewDate });
            function mockGetURLSearchParamForAdAwareFalse() {
              (getURLSearchParam as jest.Mock).mockImplementation((name: string) => (name === 'adAware' ? 'false' : null));
            }
            mockGetURLSearchParamForAdAwareFalse();
            const { result, waitFor } = renderHook(() => usePsData(fakeUrlCategory, categoryPageType, { cid }, false), successData);
            const expectedUrl =
              'pageSize=200&pageNumber=0&ignoreInventory=false&cid=5664&trackingid=y&includeMarketingFlagsDetails=true&brand=gap&locale=en-US&market=us&previewDate=01%2F01%2F2015&mode=wip&autoDiscovery=false';

            await waitFor(() => result?.current?.isSuccess);

            expect(result?.current?.isSuccess).toBeTruthy();
            expect(getAdaptedData).toHaveBeenCalled();
            expect(result?.current.data?.queryParameters).toBe(expectedUrl);
          });
          it('WHEN: inventoryAware is present in the URL with a value of true and page type is "category", THEN: should make a fetch request to the PS API passing query param "ignoreInventory" as false', async () => {
            const [cid, previewDate] = ['5664', '05/05/2015'];
            (mockUseAppState as jest.Mock).mockReturnValue({ previewDate });
            function mockGetURLSearchParamForInventoryAwareTrue() {
              (getURLSearchParam as jest.Mock).mockImplementation((name: string) => (name === 'inventoryAware' ? 'true' : null));
            }
            mockGetURLSearchParamForInventoryAwareTrue();
            const { result, waitFor } = renderHook(() => usePsData(fakeUrlCategory, categoryPageType, { cid }, false), successData);
            const expectedUrl =
              'pageSize=200&pageNumber=0&ignoreInventory=false&cid=5664&trackingid=y&includeMarketingFlagsDetails=true&brand=gap&locale=en-US&market=us&previewDate=05%2F05%2F2015&mode=wip';

            await waitFor(() => result?.current?.isSuccess);

            expect(result?.current?.isSuccess).toBeTruthy();
            expect(getAdaptedData).toHaveBeenCalled();
            expect(result?.current.data?.queryParameters).toBe(expectedUrl);
          });
          it('WHEN: inventoryAware is present in the URL with a value of false and page type is "category", THEN: should make a fetch request to the PS API passing query param "ignoreInventory" as true', async () => {
            const [cid, previewDate] = ['5664', '03/03/2015'];
            (mockUseAppState as jest.Mock).mockReturnValue({ previewDate });
            function mockGetURLSearchParamForInventoryAwareFalse() {
              (getURLSearchParam as jest.Mock).mockImplementation((name: string) => (name === 'inventoryAware' ? 'false' : null));
            }
            mockGetURLSearchParamForInventoryAwareFalse();
            const { result, waitFor } = renderHook(() => usePsData(fakeUrlCategory, categoryPageType, { cid }, false), successData);
            const expectedUrl =
              'pageSize=200&pageNumber=0&ignoreInventory=true&cid=5664&trackingid=y&includeMarketingFlagsDetails=true&brand=gap&locale=en-US&market=us&previewDate=03%2F03%2F2015&mode=wip';

            await waitFor(() => result?.current?.isSuccess);

            expect(result?.current?.isSuccess).toBeTruthy();
            expect(getAdaptedData).toHaveBeenCalled();
            expect(result?.current.data?.queryParameters).toBe(expectedUrl);
          });
        });

        describe('GIVEN: Preview date is not present', () => {
          it('WHEN: previewDate is missing from both state and URL, THEN: it should make a fetch request to the PS API without previewDate or mode=wip in the query params', async () => {
            const cid = '5664';
            (mockUseAppState as jest.Mock).mockReturnValue({ previewDate: null });
            function mockGetURLSearchParamForNoPreviewDate() {
              (getURLSearchParam as jest.Mock).mockReturnValue(null);
            }
            mockGetURLSearchParamForNoPreviewDate();
            const { result, waitFor } = renderHook(() => usePsData(fakeUrlCategory, categoryPageType, { cid }, false), successData);
            const expectedUrl =
              'pageSize=200&pageNumber=0&ignoreInventory=false&cid=5664&trackingid=y&includeMarketingFlagsDetails=true&brand=gap&locale=en-US&market=us';

            await waitFor(() => result?.current?.isSuccess);

            expect(result?.current?.isSuccess).toBeTruthy();
            expect(result?.current.data?.queryParameters).toBe(expectedUrl);
          });
          it.each([['true'], ['false']])(
            'WHEN: adAware is present in the URL with a value of %s, THEN: should make a fetch request to the PS API without passing query param "autoDiscovery"',
            async adAwareValue => {
              (mockUseAppState as jest.Mock).mockReturnValue({ previewDate: null });
              function mockGetURLSearchParamForAdAwareWithValueButNoPreviewDate() {
                (getURLSearchParam as jest.Mock).mockImplementation((name: string) => {
                  if (name === 'adAware') {
                    return adAwareValue;
                  }
                  if (name === 'previewDate') {
                    return null;
                  }
                  return null;
                });
              }
              mockGetURLSearchParamForAdAwareWithValueButNoPreviewDate();
              const cid = '5664';
              const { result, waitFor } = renderHook(() => usePsData(fakeUrlCategory, categoryPageType, { cid }, false), successData);
              const expectedUrl =
                'pageSize=200&pageNumber=0&ignoreInventory=false&cid=5664&trackingid=y&includeMarketingFlagsDetails=true&brand=gap&locale=en-US&market=us';

              await waitFor(() => result?.current?.isSuccess);

              expect(result?.current?.isSuccess).toBeTruthy();
              expect(result?.current.data?.queryParameters).toBe(expectedUrl);
            }
          );
          it.each([['true'], ['false']])(
            'WHEN: inventoryAware is present in the URL with a value of %s and previewDate is not present, THEN: should make a fetch request to the PS API with ignoreInventory=false',
            async inventoryAwareValue => {
              (mockUseAppState as jest.Mock).mockReturnValue({ previewDate: null });
              function mockGetURLSearchParamForInventoryAwareWithValueButNoPreviewDate() {
                (getURLSearchParam as jest.Mock).mockImplementation((name: string) => {
                  if (name === 'inventoryAware') {
                    return inventoryAwareValue;
                  }
                  if (name === 'previewDate') {
                    return null;
                  }
                  return null;
                });
              }
              mockGetURLSearchParamForInventoryAwareWithValueButNoPreviewDate();
              const cid = '5664';
              const { result, waitFor } = renderHook(() => usePsData(fakeUrlCategory, categoryPageType, { cid }, false), successData);
              const expectedUrl = `pageSize=200&pageNumber=0&ignoreInventory=false&cid=5664&trackingid=y&includeMarketingFlagsDetails=true&brand=gap&locale=en-US&market=us`;

              await waitFor(() => result?.current?.isSuccess);

              expect(result?.current?.isSuccess).toBeTruthy();
              expect(result?.current.data?.queryParameters).toBe(expectedUrl);
            }
          );
        });
      });

      describe('GIVEN: multiple successful query params,', () => {
        const dataForMultipleSuccessfulParams = {
          wrapper: wrapper({
            abSeg: { pgGP: 'p' },
            brand: Brands.Gap,
            locale: 'en-US',
            market: 'us',
            children: undefined,
            isPreviewEnabled: false,
            pageType: 'category',
            enabledFeatures: {},
            marketAwareBrandCode: 0,
            selectedNodes: [],
          }),
        };

        describe('WHEN: the hook is passed a cid, some facets, and sorting options', () => {
          it('THEN: should make a fetch request to the PS API with those query params.', async () => {
            const cid = '5664';

            const { result, waitFor } = renderHook(
              () => usePsData(fakeUrlCategory, categoryPageType, { cid, ...facetsAndSortingOptions }, false),
              dataForMultipleSuccessfulParams
            );
            const expectedUrl =
              'pageSize=200&pageNumber=0&ignoreInventory=false&cid=5664&vendor=Certona&trackingid=y&includeMarketingFlagsDetails=true&brand=gap&locale=en-US&market=us&a=1&b=2&sortByField=style&sortByDir=desc';

            await waitFor(() => result?.current?.isSuccess);

            expect(result?.current?.isSuccess).toBeTruthy();
            expect(getAdaptedData).toHaveBeenCalled();
            expect(result?.current.data?.queryParameters).toBe(expectedUrl);
          });
        });

        describe('WHEN: the hook is passed a cid, facet, sorting options and a storeId', () => {
          it('THEN: should make a fetch request to the PS API with those query params.', async () => {
            const [cid, storeId] = ['5664', '1234'];

            const { result, waitFor } = renderHook(
              () => usePsData(fakeUrlCategory, categoryPageType, { cid, storeId, ...facetsAndSortingOptions }, false),
              dataForMultipleSuccessfulParams
            );
            const expectedUrl =
              'pageSize=200&pageNumber=0&ignoreInventory=false&cid=5664&vendor=Certona&trackingid=y&includeMarketingFlagsDetails=true&brand=gap&locale=en-US&market=us&a=1&b=2&sortByField=style&sortByDir=desc&storeId=1234';

            await waitFor(() => result?.current?.isSuccess);

            expect(result?.current?.isSuccess).toBeTruthy();
            expect(getAdaptedData).toHaveBeenCalled();
            expect(result?.current.data?.queryParameters).toBe(expectedUrl);
          });
        });
      });

      describe('GIVEN: Indexable flex facets,', () => {
        it('THEN: should handle pre selected facets when params contains style and indexableFacets and add to hash params', async () => {
          const cid = '5664';
          const dataForMultipleSuccessfulParams = {
            wrapper: wrapper({
              abSeg: { pgGP: 'p' },
              brand: Brands.Gap,
              locale: 'en-US',
              market: 'us',
              children: undefined,
              isPreviewEnabled: false,
              pageType: 'category',
              enabledFeatures: {},
              customUrl: '/browse/category.do?cid=5664#pageId=0&department=136',
              params: {
                indexableFacets: [
                  { facetName: 'color', facetData: { id: 'red' } },
                  { facetName: 'size', facetData: { id: 'M' } },
                ],
                style: '999',
                cid: '5664',
              },
            }),
          };

          const pushStateSpy = jest.spyOn(window.history, 'pushState');
          renderHook(() => usePsData(fakeUrlCategory, categoryPageType, { cid, ...facetsAndSortingOptions }, false), dataForMultipleSuccessfulParams);

          expect(pushStateSpy).toHaveBeenCalledWith(null, '', '#color=red&size=M&style=999&department=136');
        });

        it('THEN: should not add to hash params when params is undefined', async () => {
          const cid = '5664';
          const dataForMultipleSuccessfulParams = {
            wrapper: wrapper({
              abSeg: { pgGP: 'p' },
              brand: Brands.Gap,
              locale: 'en-US',
              market: 'us',
              children: undefined,
              isPreviewEnabled: false,
              pageType: 'category',
              enabledFeatures: {},
              marketAwareBrandCode: 0,
              selectedNodes: [],
            }),
          };

          const pushStateSpy = jest.spyOn(window.history, 'pushState');
          renderHook(() => usePsData(fakeUrlCategory, categoryPageType, { cid, ...facetsAndSortingOptions }, false), dataForMultipleSuccessfulParams);

          expect(pushStateSpy).not.toHaveBeenCalled();
        });
      });

      it('THEN: should not add hash params if hash is not empty', async () => {
        const cid = '5664';
        const dataForMultipleSuccessfulParams = {
          wrapper: wrapper({
            abSeg: { pgGP: 'p' },
            brand: Brands.Gap,
            locale: 'en-US',
            market: 'us',
            children: undefined,
            isPreviewEnabled: false,
            pageType: 'category',
            enabledFeatures: {},
            customUrl: '/browse/category.do?cid=5664#pageId=0&department=136',
            params: {
              indexableFacets: [
                { facetName: 'color', facetData: { id: 'red' } },
                { facetName: 'size', facetData: { id: 'M' } },
              ],
              style: '999',
              cid: '5664',
            },
          }),
        };

        window.location.hash = '#style=3046721,3042299,3029982,3032528,3034522&department=136&color=1020';
        const pushStateSpy = jest.spyOn(window.history, 'pushState');
        renderHook(() => usePsData(fakeUrlCategory, categoryPageType, { cid, ...facetsAndSortingOptions }, false), dataForMultipleSuccessfulParams);

        expect(pushStateSpy).not.toHaveBeenCalled();
        window.location.hash = '';
      });

      describe('WHEN: the 218 experiment is active,', () => {
        it('THEN: should make a fetch request to the PS API with ConstructorIO params', async () => {
          const abSeg = {
            gap224: 'a',
          };

          const data = {
            wrapper: wrapper({
              abSeg,
              brand: Brands.Gap,
              locale: 'en-US',
              market: 'us',
              children: undefined,
              isPreviewEnabled: false,
              pageType: 'category',
              enabledFeatures: {
                'plp-constructor': true,
                'enable-dynamic-photo': true,
              },
              customUrl: '/browse/category.do?cid=5664#pageId=0&department=136',
              marketAwareBrandCode: 0,
              selectedNodes: [],
            }),
          };
          const { result, waitFor } = renderHook(() => usePsData(fakeUrlCategory, categoryPageType, { cid: '5664', ...facetsAndSortingOptions }, false), data);
          const expectedUrl =
            'pageSize=200&pageNumber=0&ignoreInventory=false&cid=5664&vendor=constructorio&client_id=1&session_id=2&uid=3&includeMarketingFlagsDetails=true&enableDynamicPhoto=true&brand=gap&locale=en-US&market=us&a=1&b=2&sortByField=style&sortByDir=desc';

          await waitFor(() => result?.current?.isSuccess);

          expect(result?.current?.isSuccess).toBeTruthy();
          expect(getAdaptedData).toHaveBeenCalled();
          expect(result?.current.data?.queryParameters).toBe(expectedUrl);
        });
      });

      describe('WHEN: client_id and session_id are not available in the ABSeg cookie,', () => {
        it('THEN: should make a fetch request to the PS API with client_id and session_id as query params each equal to 0', async () => {
          (getCookieValue as jest.Mock).mockImplementation(() => undefined);
          const abSeg = {
            gap224: 'a',
          };
          const data = {
            wrapper: wrapper({
              abSeg,
              brand: Brands.Gap,
              locale: 'en-US',
              market: 'us',
              children: undefined,
              isPreviewEnabled: false,
              pageType: 'category',
              enabledFeatures: { 'plp-constructor': true },
              marketAwareBrandCode: 0,
              selectedNodes: [],
            }),
          };
          const { result, waitFor } = renderHook(() => usePsData(fakeUrlCategory, categoryPageType, { cid: '5664', ...facetsAndSortingOptions }, false), data);
          const expectedUrl =
            'pageSize=200&pageNumber=0&ignoreInventory=false&cid=5664&vendor=constructorio&client_id=0&session_id=0&includeMarketingFlagsDetails=true&brand=gap&locale=en-US&market=us&a=1&b=2&sortByField=style&sortByDir=desc';

          await waitFor(() => result?.current?.isSuccess);

          expect(result?.current?.isSuccess).toBeTruthy();
          expect(getAdaptedData).toHaveBeenCalled();
          expect(result?.current.data?.queryParameters).toBe(expectedUrl);
        });
      });
      describe('WHEN: the 218 experiment is off', () => {
        it('THEN: should make a fetch request to the PS API with constructorio params', async () => {
          const abSeg = { gap218: 'x' };
          const data = {
            wrapper: wrapper({
              abSeg,
              brand: Brands.Gap,
              locale: 'en-US',
              market: 'us',
              children: undefined,
              isPreviewEnabled: false,
              pageType: 'category',
              enabledFeatures: {},
              marketAwareBrandCode: 0,
              selectedNodes: [],
            }),
          };
          const { result, waitFor } = renderHook(() => usePsData(fakeUrlCategory, categoryPageType, { cid: '5664', ...facetsAndSortingOptions }, false), data);
          const expectedUrl =
            'pageSize=200&pageNumber=0&ignoreInventory=false&cid=5664&trackingid=y&includeMarketingFlagsDetails=true&brand=gap&locale=en-US&market=us&a=1&b=2&sortByField=style&sortByDir=desc';

          await waitFor(() => result?.current?.isSuccess);

          expect(result?.current?.isSuccess).toBeTruthy();
          expect(getAdaptedData).toHaveBeenCalled();
          expect(result?.current.data?.queryParameters).toBe(expectedUrl);
        });
      });

      describe('GIVEN: scenarios where ABSeg cookie is { pgGP: `x`}', () => {
        const dataForPggpExperimentTurnedOff = {
          wrapper: wrapper({
            abSeg: { pgGP: 'x' },
            brand: Brands.Gap,
            locale: 'en-US',
            market: 'us',
            children: undefined,
            isPreviewEnabled: false,
            pageType: 'category',
            enabledFeatures: {},
            marketAwareBrandCode: 0,
            selectedNodes: [],
          }),
        };
        it('should make a fetch request to the PS API with with empty sorting options', async () => {
          const [sortByDir, sortByField] = ['', ''];
          const { result, waitFor } = renderHook(
            () =>
              usePsData(
                fakeUrlCategory,
                categoryPageType,
                {
                  cid: '5664',
                  ...facetsAndSortingOptions,
                  sortByOptions: { sortByDir, sortByField },
                },
                false
              ),
            dataForPggpExperimentTurnedOff
          );
          const expectedUrl =
            'pageSize=200&pageNumber=0&ignoreInventory=false&cid=5664&trackingid=y&includeMarketingFlagsDetails=true&brand=gap&locale=en-US&market=us&a=1&b=2';

          await waitFor(() => result?.current?.isSuccess);

          expect(result?.current?.isSuccess).toBeTruthy();
          expect(getAdaptedData).toHaveBeenCalled();
          expect(result?.current.data?.queryParameters).toBe(expectedUrl);
        });
        it('should make a fetch request to the PS API with already doubled values', async () => {
          const { result, waitFor } = renderHook(
            () =>
              usePsData(
                fakeUrlCategory,
                categoryPageType,
                {
                  cid: '5664',
                  ...facetsAndSortingOptions,
                  appliedFacets: {
                    style: [
                      {
                        id: '2',
                        name: 'b',
                        facetName: 'b',
                        isActive: true,
                        applied: true,
                        value: '2',
                      },
                      {
                        id: '3',
                        name: 'b',
                        facetName: 'b',
                        isActive: true,
                        applied: true,
                        value: '3',
                      },
                    ],
                  },
                },
                false
              ),
            dataForPggpExperimentTurnedOff
          );
          const expectedUrl =
            'pageSize=200&pageNumber=0&ignoreInventory=false&cid=5664&trackingid=y&includeMarketingFlagsDetails=true&brand=gap&locale=en-US&market=us&b=2%2C3&sortByField=style&sortByDir=desc';

          await waitFor(() => result?.current?.isSuccess);

          expect(result?.current?.isSuccess).toBeTruthy();
          expect(getAdaptedData).toHaveBeenCalled();
          expect(result?.current.data?.queryParameters).toBe(expectedUrl);
        });
      });

      describe('WHEN: the FF and exp for dynamic facet ordering is active', () => {
        it('THEN: should make a fetch request to the PS API with enableDynamicFacets params as true', async () => {
          const abSeg = {
            gap237: 'a',
          };

          const data = {
            wrapper: wrapper({
              abSeg,
              brand: Brands.Gap,
              locale: 'en-US',
              market: 'us',
              children: undefined,
              isPreviewEnabled: false,
              pageType: 'category',
              enabledFeatures: { 'plp-facets-order': true },
              customUrl: '/browse/category.do?cid=5664#pageId=0&department=136',
              marketAwareBrandCode: 0,
              selectedNodes: [],
            }),
          };
          const { result, waitFor } = renderHook(() => usePsData(fakeUrlCategory, categoryPageType, { cid: '5664', ...facetsAndSortingOptions }, false), data);
          const expectedUrl =
            'pageSize=200&pageNumber=0&ignoreInventory=false&cid=5664&trackingid=y&includeMarketingFlagsDetails=true&enableDynamicFacets=true&brand=gap&locale=en-US&market=us&a=1&b=2&sortByField=style&sortByDir=desc';

          await waitFor(() => result?.current?.isSuccess);

          expect(result?.current?.isSuccess).toBeTruthy();
          expect(getAdaptedData).toHaveBeenCalled();
          expect(result?.current.data?.queryParameters).toBe(expectedUrl);
        });
      });

      // TODO: remove after migration to new size hash params on search page
      describe('ForSearchPageInterim', () => {
        it('should not make calls for size hash params interim search page logic when on a category page', async () => {
          const { result, waitFor } = renderHook(() => usePsData(fakeUrlCategory, categoryPageType, { cid: '5664' }, false), successData);
          const expectedUrl =
            'pageSize=200&pageNumber=0&ignoreInventory=false&cid=5664&trackingid=y&includeMarketingFlagsDetails=true&brand=gap&locale=en-US&market=us';

          await waitFor(() => result?.current?.isSuccess);

          expect(result?.current?.isSuccess).toBeTruthy();
          expect(result?.current.data?.queryParameters).toBe(expectedUrl);
          expect(mockHasSizeParamForSearchPageInterim).not.toHaveBeenCalled();
          expect(mockMapSizeFacetForSearchPageInterim).not.toHaveBeenCalled();
        });
      });
    });
  });

  describe('Search', () => {
    describe('Success', () => {
      it('should make a fetch request to the PS API with only keyword', async () => {
        const data = {
          wrapper: wrapper({
            abSeg: { pgGP: 'x' },
            brand: Brands.Gap,
            locale: 'en-US',
            market: 'us',
            children: undefined,
            isPreviewEnabled: false,
            pageType: 'category',
            enabledFeatures: {},
            marketAwareBrandCode: 0,
            selectedNodes: [],
          }),
        };
        const expectedUrl =
          'pageSize=200&pageNumber=0&keyword=jeans&searchVendor=constructorio&client_id=1&session_id=2&uid=3&brand=gap&locale=en-US&market=us';

        const { result, waitFor } = renderHook(() => usePsData(fakeUrlSearch, searchPageType, { searchText: 'jeans' }, false), data);
        await waitFor(() => result?.current?.isSuccess);

        expect(result?.current?.isSuccess).toBeTruthy();
        expect(getAdaptedData).toHaveBeenCalled();
        expect(result?.current.data?.queryParameters).toBe(expectedUrl);
        expect(window.__SEARCH_PAGE_RESPONSE__).toEqual(productsJson);
      });

      it('should client_id and session_id as 0 if they are not available in cookie', async () => {
        (getCookieValue as jest.Mock).mockImplementation(() => undefined);
        const data = {
          wrapper: wrapper({
            abSeg: { pgGP: 'x' },
            brand: Brands.Gap,
            locale: 'en-US',
            market: 'us',
            children: undefined,
            isPreviewEnabled: false,
            pageType: 'search',
            enabledFeatures: {},
            marketAwareBrandCode: 0,
            selectedNodes: [],
          }),
        };
        const expectedUrl = 'pageSize=200&pageNumber=0&keyword=jeans&searchVendor=constructorio&client_id=0&session_id=0&brand=gap&locale=en-US&market=us';

        const { result, waitFor } = renderHook(() => usePsData(fakeUrlSearch, searchPageType, { searchText: 'jeans' }, false), data);
        await waitFor(() => result?.current?.isSuccess);

        expect(result?.current?.isSuccess).toBeTruthy();
        expect(getAdaptedData).toHaveBeenCalled();
        expect(result?.current.data?.queryParameters).toBe(expectedUrl);
      });

      it('should make a fetch request to the PS API with facet and sort by values', async () => {
        const data = {
          wrapper: wrapper({
            abSeg: { pgGP: 'p' },
            brand: Brands.Gap,
            locale: 'en-US',
            market: 'us',
            children: undefined,
            isPreviewEnabled: false,
            pageType: 'category',
            enabledFeatures: {},
            marketAwareBrandCode: 0,
            selectedNodes: [],
          }),
        };
        const { result, waitFor } = renderHook(
          () => usePsData(fakeUrlSearch, searchPageType, { searchText: 'jeans', ...facetsAndSortingOptions }, false),
          data
        );
        const expectedUrl =
          'pageSize=200&pageNumber=0&keyword=jeans&searchVendor=constructorio&client_id=1&session_id=2&uid=3&brand=gap&locale=en-US&market=us&a=1&b=2&sortByField=style&sortByDir=desc';

        await waitFor(() => result?.current?.isSuccess);

        expect(result?.current?.isSuccess).toBeTruthy();
        expect(getAdaptedData).toHaveBeenCalled();
        expect(result?.current.data?.queryParameters).toBe(expectedUrl);
      });

      it('should make a fetch request to the PS API with style and occasion facets', async () => {
        const data = {
          wrapper: wrapper({
            abSeg: { pgGP: 'p' },
            brand: Brands.Gap,
            locale: 'en-US',
            market: 'us',
            children: undefined,
            isPreviewEnabled: false,
            pageType: 'search',
            enabledFeatures: {},
            marketAwareBrandCode: 0,
            selectedNodes: [],
          }),
        };
        const { result, waitFor } = renderHook(
          () => usePsData(fakeUrlSearch, searchPageType, { searchText: 'jeans', ...styleFacets }, false),
          data
        );
        const expectedUrl =
          'pageSize=200&pageNumber=0&keyword=jeans&searchVendor=constructorio&client_id=1&session_id=2&uid=3&brand=gap&locale=en-US&market=us&department=Women&style=Shirts+%26+Blouses&occasion=Party+%26+Date+Night';

        await waitFor(() => result?.current?.isSuccess);

        expect(result?.current?.isSuccess).toBeTruthy();
        expect(getAdaptedData).toHaveBeenCalled();
        expect(result?.current.data?.queryParameters).toBe(expectedUrl);
      });

      describe('GIVEN: Preview date is present', () => {
        it.each([['true'], ['false']])(
          'WHEN: adAware is present in the URL with a value of %s and page type is NOT "category", THEN: should make a fetch request to the PS API without passing query param "autoDiscovery"',
          async adAwareValue => {
            const previewDate = '01/01/2015';
            (mockUseAppState as jest.Mock).mockReturnValue({ previewDate });
            function mockGetURLSearchParamForAdAwareWithValue() {
              (getURLSearchParam as jest.Mock).mockImplementation((name: string) => (name === 'adAware' ? adAwareValue : null));
            }
            mockGetURLSearchParamForAdAwareWithValue();
            const data = {
              wrapper: wrapper({
                abSeg: { pgGP: 'x' },
                brand: Brands.Gap,
                locale: 'en-US',
                market: 'us',
                children: undefined,
                isPreviewEnabled: true,
                pageType: 'search',
                enabledFeatures: {},
                marketAwareBrandCode: 1,
                selectedNodes: [],
              }),
            };
            const expectedUrl =
              'pageSize=200&pageNumber=0&keyword=jeans&searchVendor=constructorio&client_id=1&session_id=2&uid=3&brand=gap&locale=en-US&market=us&previewDate=01%2F01%2F2015&mode=wip';

            const { result, waitFor } = renderHook(() => usePsData(fakeUrlSearch, searchPageType, { searchText: 'jeans' }, false), data);
            await waitFor(() => result?.current?.isSuccess);

            expect(result?.current?.isSuccess).toBeTruthy();
            expect(result?.current.data?.queryParameters).toBe(expectedUrl);
            expect(window.__SEARCH_PAGE_RESPONSE__).toEqual(productsJson);
          }
        );
      });

      // TODO: remove after migration to new size hash params on search page
      describe('ForSearchPageInterim', () => {
        const data = {
          wrapper: wrapper({
            abSeg: { pgGP: 'p' },
            brand: Brands.Gap,
            locale: 'en-US',
            market: 'us',
            children: undefined,
            isPreviewEnabled: false,
            pageType: 'search',
            enabledFeatures: {},
            marketAwareBrandCode: 1,
            selectedNodes: [],
          }),
        };

        it('should make a fetch request to the PS API with mapped size hash params when hasSizeParamForSearchPageInterim is true', async () => {
          mockHasSizeParamForSearchPageInterim.mockImplementation(() => true);
          mockMapSizeFacetForSearchPageInterim.mockImplementation(() => ['someMappedSize=abc', 'anotherMappedSize=xyz']);
          const { result, waitFor } = renderHook(
            () => usePsData(fakeUrlSearch, searchPageType, { searchText: 'jeans', ...facetsAndSortingOptions }, false),
            data
          );
          const expectedUrl =
            'pageSize=200&pageNumber=0&keyword=jeans&searchVendor=constructorio&client_id=1&session_id=2&uid=3&brand=gap&locale=en-US&market=us&a=1&b=2&size=someMappedSize%3Dabc%2CanotherMappedSize%3Dxyz&sortByField=style&sortByDir=desc';

          await waitFor(() => result?.current?.isSuccess);

          expect(mockHasSizeParamForSearchPageInterim).toHaveBeenCalled();
          expect(mockMapSizeFacetForSearchPageInterim).toHaveBeenCalled();
          expect(result?.current?.isSuccess).toBeTruthy();
          expect(getAdaptedData).toHaveBeenCalled();
          expect(result?.current.data?.queryParameters).toBe(expectedUrl);
        });

        it('should not map size hash params when hasSizeParamForSearchPageInterim is false', async () => {
          mockHasSizeParamForSearchPageInterim.mockImplementation(() => false);
          const { result, waitFor } = renderHook(
            () => usePsData(fakeUrlSearch, searchPageType, { searchText: 'jeans', ...facetsAndSortingOptions }, false),
            data
          );
          const expectedUrl =
            'pageSize=200&pageNumber=0&keyword=jeans&searchVendor=constructorio&client_id=1&session_id=2&uid=3&brand=gap&locale=en-US&market=us&a=1&b=2&sortByField=style&sortByDir=desc';

          await waitFor(() => result?.current?.isSuccess);

          expect(mockHasSizeParamForSearchPageInterim).toHaveBeenCalled();
          expect(mockMapSizeFacetForSearchPageInterim).not.toHaveBeenCalled();
          expect(result?.current?.isSuccess).toBeTruthy();
          expect(result?.current.data?.queryParameters).toBe(expectedUrl);
        });
      });
    });
  });

  describe('Failure', () => {
    const failureData = {
      wrapper: wrapper({
        abSeg: { pgGP: 'x' },
        brand: Brands.Gap,
        locale: 'en-US',
        market: 'us',
        children: undefined,
        isPreviewEnabled: false,
        pageType: 'category',
        enabledFeatures: {},
        marketAwareBrandCode: 0,
        selectedNodes: [],
      }),
    };
    describe('WHEN: passed an invalid cid', () => {
      it('THEN: does not fetch any data', async () => {
        const { result, waitFor } = renderHook(() => usePsData(fakeUrlSearch, categoryPageType, { cid: 'xxxx' }, false), failureData);
        await waitFor(() => result?.current?.error);

        expect(result?.current?.error).toBeTruthy();

        expect(getAdaptedData).not.toHaveBeenCalled();
        expect(result?.current.data?.queryParameters).toBeUndefined();
      });
    });

    describe('WHEN: passed a valid cid but there is a problem with the PS API', () => {
      it('THEN: does not fetch any data and also returns an error', async () => {
        (getCookieValue as jest.Mock).mockImplementation(() => '');
        const { result, waitFor } = renderHook(() => usePsData(fakeUrlSearch, categoryPageType, { cid: '9999' }, false), failureData);

        await waitFor(() => result?.current?.error);

        expect(result?.current?.error).toBeTruthy();
        expect(getAdaptedData).not.toHaveBeenCalled();
        expect(result?.current.data?.queryParameters).toBeUndefined();
      });
    });
  });

  describe('usePsData - parallel queries', () => {
    const url = 'http://fakeurl.com/api';
    const pageType = 'category';
    const cid = '1234';

    const mockedProductFetchQueriesData = {
      data: {
        cid: '5664',
        facets: {},
        pagination: {},
        productImages: {},
        productInfos: {},
        productPrices: {},
        productReview: {},
        productSwatches: {},
        productsSubcategories: [],
        queryParameters:
          'pageSize=200&pageNumber=0&ignoreInventory=false&cid=5664&trackingid=y&includeMarketingFlagsDetails=true&brand=gap&locale=en-US&market=us',
        quickAdd: {},
        searchText: '',
      },
      error: null,
      isLoading: false,
      isSuccess: true,
      isError: false,
      isFetching: false,
    };

    const mockedSingleQueryData = {
      data: {
        cid: '5664',
        queryParameters:
          'pageSize=200&pageNumber=0&ignoreInventory=false&cid=5664&trackingid=y&includeMarketingFlagsDetails=true&brand=gap&locale=en-US&market=us',
        searchText: '',
      },
      error: null,
      isLoading: false,
      isSuccess: true,
      isError: false,
      isFetching: false,
    };

    beforeEach(() => {
      client.clear();
      jest.clearAllMocks();
    });

    it('should have query data if queryKey is correct', () => {
      const wrapperData = {
        wrapper: wrapper({
          abSeg: { pgGP: 'x' },
          brand: Brands.Gap,
          locale: 'en-US',
          market: 'us',
          children: undefined,
          isPreviewEnabled: false,
          pageType: 'category',
          enabledFeatures: {},
          marketAwareBrandCode: 0,
          selectedNodes: [],
        }),
      };

      const isPaginationRewriteEnabled = false;

      const { result, waitFor } = renderHook(() => usePsData(url, pageType, { cid }, false, isPaginationRewriteEnabled), wrapperData);

      waitFor(() => result.current.isSuccess);
      const plainFormattedObjectParams = {
        pageSize: '200',
        pageNumber: '0',
        ignoreInventory: 'false',
        cid: '1234',
        trackingid: 'y',
        includeMarketingFlagsDetails: 'true',
        brand: 'gap',
        locale: 'en-US',
        market: 'us',
      };

      const query = client.getQueryCache().find({ queryKey: ['psData', url, plainFormattedObjectParams] });
      expect(query).not.toBeUndefined();
      expect(query?.options.gcTime).toBe(undefined);
    });

    it('should not have query data if queryKey is incorrect', () => {
      const wrapperData = {
        wrapper: wrapper({
          abSeg: { pgGP: 'x' },
          brand: Brands.Gap,
          locale: 'en-US',
          market: 'us',
          children: undefined,
          isPreviewEnabled: false,
          pageType: 'category',
          enabledFeatures: {},
          marketAwareBrandCode: 0,
          selectedNodes: [],
        }),
      };

      const isPaginationRewriteEnabled = false;

      const { result, waitFor } = renderHook(() => usePsData(url, pageType, { cid }, false, isPaginationRewriteEnabled), wrapperData);

      waitFor(() => result.current.isSuccess);
      const plainFormattedObjectParams = {
        pageSize: '200',
        pageNumber: '0',
        ignoreInventory: 'false',
        cid: '1234',
        trackingid: 'y',
        includeMarketingFlagsDetails: 'true',
        brand: 'gap',
        locale: 'en-US',
        market: 'us',
      };

      const query = client.getQueryCache().find({ queryKey: ['psData', url, { ...plainFormattedObjectParams, anotherKey: 'anotherValue' }] });
      expect(query).not.toBeDefined();
      expect(query?.options.gcTime).toBe(undefined);
    });

    it('should call useQuery when isPaginationRewriteEnabled is false', async () => {
      const isPaginationRewriteEnabled = false;
      const { result, waitForValueToChange } = renderHook(
        () => usePsData(fakeUrlCategory, categoryPageType, { cid: '5664', appliedFacets: {}, pageNumber: 0 }, false, isPaginationRewriteEnabled),
        successData
      );

      await waitForValueToChange(() => result.current.isSuccess);
      expect(result.current).toEqual(mockedSingleQueryData);
    });

    it('should call useProductFetchQuery when isPaginationRewriteEnabled is true', async () => {
      const isPaginationRewriteEnabled = true;
      const { result, waitForValueToChange } = renderHook(
        () => usePsData(fakeUrlCategory, categoryPageType, { cid: '5664', appliedFacets: {}, pageNumber: 0 }, false, isPaginationRewriteEnabled),
        successData
      );

      await waitForValueToChange(() => result.current.isSuccess);
      expect(result.current).toEqual(mockedProductFetchQueriesData);
    });

    it('should call the api when waiting for new values to change', async () => {
      const isPaginationRewriteEnabled = false;
      const { result, waitForValueToChange } = renderHook(
        () => usePsData(fakeUrlCategory, categoryPageType, { cid: '5664', appliedFacets: {}, pageNumber: 0 }, false, isPaginationRewriteEnabled),
        successData
      );
      await waitForValueToChange(() => result.current.isSuccess);
      expect(result.current.isSuccess).toEqual(true);
    });

    it('should not call the api without waiting for new values to change', () => {
      const isPaginationRewriteEnabled = false;
      const { result: resultWithoutWaiting } = renderHook(
        () => usePsData(fakeUrlCategory, categoryPageType, { cid: '5664', appliedFacets: {}, pageNumber: 0 }, false, isPaginationRewriteEnabled),
        successData
      );
      expect(resultWithoutWaiting.current.isSuccess).toEqual(false);
    });
  });
});

describe('appliedFacetsToQueryParameters', () => {
  it('returns empty string if no facets', () => {
    expect(appliedFacetsToQueryParameters()).toBe('');
  });
  it('serializes single and multi facets', () => {
    expect(appliedFacetsToQueryParameters(mockedAppliedFacets)).toContain('color=red%2Cblue');
    expect(appliedFacetsToQueryParameters(mockedAppliedFacets)).toContain('style=4564');
    expect(appliedFacetsToQueryParameters(mockedAppliedFacets)).toContain('price=10');
  });
});

describe('sortByToQueryParameters', () => {
  it('returns correct params for sortByOptions', () => {
    expect(sortByToQueryParameters({ sortByDir: Direction.Ascending, sortByField: Field.Price })).toContain('sortByField=price');
    expect(sortByToQueryParameters({ sortByDir: Direction.Ascending, sortByField: Field.Featured })).toContain('sortByField=featured');
  });
  it('returns empty string if no options', () => {
    expect(sortByToQueryParameters()).toBe('');
  });
});

describe('storeIdToQueryParameters', () => {
  it('returns storeId param if present', () => {
    expect(storeIdToQueryParameters('123')).toContain('storeId=123');
  });
  it('returns empty string if not present', () => {
    expect(storeIdToQueryParameters()).toBe('');
  });
});

describe('fetchProducts', () => {
  beforeEach(() => {
    global.fetch = jest.fn(() =>
      Promise.resolve(
        new Response(JSON.stringify({ products: [{}] }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' },
        })
      )
    );
  });

  it('calls fetch and returns adapted data for category', async () => {
    const requestedUrl = `${fakeUrlCategory}?${fakeUrlParams}`;
    const result = await fetchProducts(fakeUrlCategory, CATEGORY_PAGE, fakeUrlParams, '5664', '', mockedFacetOrder);

    expect(global.fetch).toHaveBeenCalledWith(requestedUrl, expect.anything());
    expect(result).toHaveProperty('cid', '5664');
    expect(result).toHaveProperty('searchText', '');
  });
  it('calls fetch and returns adapted data for search', async () => {
    const requestedUrl = `${fakeUrlSearch}?${fakeUrlParams}`;
    const result = await fetchProducts(fakeUrlSearch, SEARCH_PAGE, fakeUrlParams, '', 'jeans', mockedFacetOrder);

    expect(global.fetch).toHaveBeenCalledWith(requestedUrl, expect.anything());
    expect(result).toHaveProperty('searchText', 'jeans');
    expect(result).toHaveProperty('cid', '');
  });
  it('reports error if response not ok', async () => {
    (global.fetch as jest.Mock).mockImplementationOnce(() => Promise.resolve({ ok: false, statusText: 'fail', json: () => Promise.resolve({ products: [] }) }));
    await expect(fetchProducts(fakeUrlCategory, CATEGORY_PAGE, '', '', '', mockedFacetOrder)).rejects.toThrow('fail');
  });
});
