import { PAGE_ID_PARAM } from '../constants';

export function pageNumberFromHash(): number {
  if (typeof window === 'undefined') return 0;

  const hashString = window.location.hash;
  const hashParams = new URLSearchParams(hashString.replace(/^#/, ''));
  const pageIdValue = hashParams.get(PAGE_ID_PARAM);

  if (!pageIdValue) return 0;

  return Number(pageIdValue) || 0;
}

export const fixMalformedAmpersandEncoding = (hash: string) => {
  const encodedAmpersand = `%26`;
  return hash.replace(/%20&%20/g, `%20${encodedAmpersand}%20`);
};
