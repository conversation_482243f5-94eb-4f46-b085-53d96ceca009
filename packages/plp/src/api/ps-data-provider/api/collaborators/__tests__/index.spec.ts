import { pageNumberFromHash, fixMalformedAmpersandEncoding } from '../index';
import { PAGE_ID_PARAM } from '../../constants';

describe('pageNumberFromHash', () => {
  const originalWindow = global.window;

  afterEach(() => {
    global.window = originalWindow;
    jest.resetModules();
    jest.clearAllMocks();
  });

  it('returns 0 if window is undefined (server-side)', () => {
    // @ts-ignore
    delete global.window;
    expect(pageNumberFromHash()).toBe(0);
  });

  it('returns 0 if location.hash is empty', () => {
    // @ts-ignore
    global.window = { location: { hash: '' } };
    expect(pageNumberFromHash()).toBe(0);
  });

  it('returns 0 if pageId is not present in hash', () => {
    // @ts-ignore
    global.window = { location: { hash: '#style=2' } };
    expect(pageNumberFromHash()).toBe(0);
  });

  it('returns the number if pageId is present and valid', () => {
    // @ts-ignore
    global.window = { location: { hash: `#${PAGE_ID_PARAM}=5` } };
    expect(pageNumberFromHash()).toBe(5);
  });

  it('returns 0 if pageId is present but value is not a number', () => {
    // @ts-ignore
    global.window = { location: { hash: `#${PAGE_ID_PARAM}=abc` } };
    expect(pageNumberFromHash()).toBe(0);
  });

  it('returns 0 if pageId is present and value is "0"', () => {
    // @ts-ignore
    global.window = { location: { hash: `#${PAGE_ID_PARAM}=0` } };
    expect(pageNumberFromHash()).toBe(0);
  });
});

describe('fixMalformedAmpersandEncoding', () => {
  it('should encode an ampersand in a URL with an unencoded ampersand surrounded by two properly encoded spaces', () => {
    const input = '%20&%20';
    const expected = '%20%26%20';
    expect(fixMalformedAmpersandEncoding(input)).toBe(expected);
  });
  it('should encode multiple ampersands in a URL that includes multiple unencoded ampersands, each surrounded by two properly encoded spaces', () => {
    const input = '%20&%20%20&%20';
    const expected = '%20%26%20%20%26%20';
    expect(fixMalformedAmpersandEncoding(input)).toBe(expected);
  });
  it('should encode malformed ampersands in both style and occasion', () => {
    const input =    'style=modern%20&%20classic&occasion=party%20&%20formal';
    const expected = 'style=modern%20%26%20classic&occasion=party%20%26%20formal';
    expect(fixMalformedAmpersandEncoding(input)).toBe(expected);
  });

  it('should return unchanged string if no malformed ampersands are present', () => {
    const input = 'style=modern%20%26%20classic&occasion=party%20%26%20formal';
    const expected = input;
    expect(fixMalformedAmpersandEncoding(input)).toBe(expected);
  });

  it('should handle unrelated parameters without changes', () => {
    const input = 'color=red&size=large';
    const expected = input;
    expect(fixMalformedAmpersandEncoding(input)).toBe(expected);
  });

  it('should not alter proper encoding with multiple ampersands elsewhere', () => {
    const input = 'style=urban%20&%20casual&occasion=beach%20&%20wedding&extra=value';
    const expected = 'style=urban%20%26%20casual&occasion=beach%20%26%20wedding&extra=value';
    expect(fixMalformedAmpersandEncoding(input)).toBe(expected);
  });
});

