import { useRef, useEffect, useState } from 'react';
import { useAppState } from '@ecom-next/sitewide/app-state-provider';
import { useConstructor, useDynamicPhotography, useFacetsOrder, useOutOfStock, usePLPState, useProductFetchQuery } from '@ecom-next/plp';
import { useQuery } from '@tanstack/react-query';
import { getAdaptedData } from '../../ps-api-mappers';
import { AppliedFacets, Direction, Field, SortByAdapterResult, SortByDir, SortByField } from '../../../types/plp-data';
import { PsData } from '../../../types/ps-api-response';
import { getCookieValue, getURLSearchParam } from '../../../utils/window-helper';
import { AbSeg, Brands, Market, PLPFeaturesForApi } from '../../../types';
import { removeInvalidHashParams } from '../hooks/helpers/facets-query-builder';
import { IndexableFacet } from '../../../data/plp-data-layer/types';
import { CATEGORY_PAGE, PageType, PsApiQueryParameters, SEARCH_PAGE } from '../types';
import { hasSizeParamForSearchPageInterim, mapSizeFacetForSearchPageInterim } from '../hooks/helpers/interimForSearchPage/interimSizeHashMapper';
import { reportToNewRelic, logErrorToNewRelic } from '../../../data/plp-data-layer/new-relic/report-to-new-relic';
import { PLP_API_RESPONSE_ERROR, PLP_API_RESPONSE_TIME_EVENT, PLP_NO_PRODUCTS_FOUND_ERROR } from '../../../data/plp-data-layer/new-relic/constants';
import { PsDataProviderContext } from '../reducer/types';
import { brandObjectAbbreviation, HYBRID_PAGE_LIMIT, PAGE_SIZE_LIMIT } from './constants';
import { pageNumberFromHash, fixMalformedAmpersandEncoding } from './collaborators';

export const appliedFacetsToQueryParameters = (appliedFacets?: AppliedFacets, pageType?: string) => {
  if (!appliedFacets) {
    return '';
  }
  const searchParams = new URLSearchParams();

  const singleFacets = (facetName: string) => facetName === 'price' || facetName === 'department';

  Object.values(appliedFacets).forEach(options => {
    options?.forEach(({ facetName = '', value, id }) => {
      const facetValue = pageType === CATEGORY_PAGE ? id : value;
      if (searchParams.has(facetName) && !singleFacets(facetName)) {
        searchParams.set(facetName, `${searchParams.get(facetName)},${facetValue}`);
      } else {
        searchParams.set(facetName, facetValue as string);
      }
    });
  });
  return searchParams.toString();
};

const setDirection = (appliedDirection: SortByDir) => {
  const direction: SortByDir = appliedDirection === Direction.Unset ? '' : appliedDirection;
  const fixedDirection: string = direction ?? Direction.Ascending;
  return fixedDirection;
};

const setField = (appliedDirection: SortByDir, appliedField: SortByField) => {
  const field: SortByField = appliedDirection === '' ? '' : appliedField;
  const fixedField: string = field ?? Field.Price;
  return fixedField;
};

export const sortByToQueryParameters = (sortByOptions?: SortByAdapterResult) => {
  if (sortByOptions && sortByOptions.sortByDir && sortByOptions.sortByField) {
    const searchParams = new URLSearchParams();
    const direction = setDirection(sortByOptions?.sortByDir);
    const field = setField(direction, sortByOptions?.sortByField);
    if (field) {
      field === 'new' ? searchParams.set('sortByField', 'newness') : searchParams.set('sortByField', field);
    }
    if (direction) {
      searchParams.set('sortByDir', direction);
    }
    return searchParams.toString();
  }
  return '';
};

export const storeIdToQueryParameters = (storeId?: string | null) => {
  if (storeId) {
    const searchParams = new URLSearchParams();
    searchParams.set('storeId', storeId);
    return searchParams.toString();
  }
  return '';
};

const checkIfIsNotFactory = (ABSegIndex: string): boolean => ABSegIndex !== 'pgGPFS' && ABSegIndex !== 'pgBRFS';

const checkMarket = (market: string, ABSegIndex: string): string => {
  return market === 'ca' && checkIfIsNotFactory(ABSegIndex) ? `${ABSegIndex}CA` : ABSegIndex;
};

const getVendor = (brandName: Brands, market: Market, abSeg: AbSeg) => {
  const ABSegIndex = brandObjectAbbreviation[brandName];
  const formattedABSegIndex = checkMarket(market, ABSegIndex);
  const ABSegValue = abSeg?.[formattedABSegIndex];

  return ABSegValue === 'p' ? 'Certona' : '';
};

const parseResIdFromCookie = () => {
  const resIdCookie = getCookieValue('RES_TRACKINGID');
  return resIdCookie;
};

const getDepartmentIdFromUrl = (url = ''): string => {
  const urlHash = url.indexOf('#') !== -1 ? url.slice(url.indexOf('#')) : '';
  const departmentId = urlHash?.includes('department=') ? urlHash.split('department=')[1]?.split('&')[0] || '' : '';
  return departmentId;
};

const getCategoryVendorParams = () => {
  const categoryVendor = 'constructorio';
  const clientId = getCookieValue('ConstructorioID_client_id') || '0';
  const sessionId = getCookieValue('ConstructorioID_session_id') || '0';
  const camCookie = getCookieValue('cam');
  const externalCustomerId = camCookie?.split('|')?.[0];
  const clientUid = camCookie ? externalCustomerId : '';

  return { categoryVendor, clientId, sessionId, clientUid };
};

const getSearchVendorParams = () => {
  const searchVendor = 'constructorio';
  const clientId = getCookieValue('ConstructorioID_client_id') || '0';
  const sessionId = getCookieValue('ConstructorioID_session_id') || '0';
  const camCookie = getCookieValue('cam');
  const externalCustomerId = camCookie?.split('|')?.[0];
  const clientUid = camCookie ? externalCustomerId : '';

  return { searchVendor, clientId, sessionId, clientUid };
};

export const fetchProducts = async (
  url: string,
  pageType: PageType,
  queryParameters: string,
  cid = '',
  searchText = '',
  plpFeaturesForApi: PLPFeaturesForApi
) => {
  const mountedUrl = `${url}?${queryParameters}`;
  const options = {
    headers: {
      'x-client-application-name': 'Browse',
    },
  };
  const response = await fetch(mountedUrl, options);
  const data: PsData = await response.json();

  if (data.products.length < 1) {
    logErrorToNewRelic(new Error(PLP_NO_PRODUCTS_FOUND_ERROR), { msg: `HUI - PS API returned empty "products" array for cid: ${cid}.` });
  }

  const apiResponseTimeMs = new Date().getTime();
  reportToNewRelic(PLP_API_RESPONSE_TIME_EVENT, apiResponseTimeMs.toString());

  if (!response.ok) {
    logErrorToNewRelic(new Error(PLP_API_RESPONSE_ERROR), { msg: response.statusText });
    throw new Error(response.statusText);
  }
  if ([CATEGORY_PAGE, SEARCH_PAGE].includes(pageType) && typeof window !== 'undefined') {
    const responseKey = pageType === CATEGORY_PAGE ? '__CATEGORY_PAGE_RESPONSE__' : '__SEARCH_PAGE_RESPONSE__';
    window[responseKey] = data;
  }
  return { ...getAdaptedData(data, pageType, plpFeaturesForApi), cid, searchText, queryParameters };
};

const getHashQueryString = () => {
  if (typeof window !== 'undefined') {
    let hashQueryString = window.location.hash;
    const newParameterCheck = hashQueryString.includes('sortByField=new');
    if (newParameterCheck) {
      hashQueryString = hashQueryString.replace('sortByField=new', 'sortByField=newness');
    }
    return hashQueryString;
  }
  return '';
};

const handlePreSelectedFacets = (
  indexableFacets: IndexableFacet[] = [],
  style: string = '',
  departmentId: string,
  setIsAfterPreSelectedFacets: (isAfterPreSelectedFacets: boolean) => void
) => {
  if (typeof window !== 'undefined') {
    const indexableFacetsQuery: string[] = indexableFacets
      .filter(({ facetData }) => facetData?.id)
      .map(({ facetName, facetData }) => `${facetName}=${facetData?.id}`);
    const styleQuery = style ? [`style=${style}`] : [];
    const departmentQuery = departmentId ? [`department=${departmentId}`] : [];
    const preSelectedFacetsQueryString = [...indexableFacetsQuery, ...styleQuery, ...departmentQuery].join('&');
    if (preSelectedFacetsQueryString) {
      history.pushState(null, '', `#${preSelectedFacetsQueryString}`);
    }
    setIsAfterPreSelectedFacets(true);
  }
};

const accountForAmpersands = (appliedFacets: URLSearchParams) => {
  const hashQueryString = getHashQueryString();
  const fixedHashQueryString = fixMalformedAmpersandEncoding(hashQueryString);
  return (appliedFacets.size === 0 && typeof window !== 'undefined' && fixedHashQueryString)
      ? new URLSearchParams(removeInvalidHashParams(fixedHashQueryString))
      : new URLSearchParams();
};

export const usePsData = (
  url: string,
  pageType: PageType,
  { cid, searchText, appliedFacets, sortByOptions, pageNumber, storeId }: PsApiQueryParameters,
  isHybrid = false,
  isPaginationRewriteEnabled = false
) => {
  const searchParams = new URLSearchParams();
  const { previewDate } = useAppState();

  if (appliedFacets) {
    const { sortByDir, sortByField, storeId } = appliedFacets;

    if (sortByDir || sortByField || storeId) {
      delete appliedFacets.sortByDir;
      delete appliedFacets.sortByField;
      delete appliedFacets.storeId;
    }
  }

  const appliedFacetsQueryParameters = new URLSearchParams(appliedFacetsToQueryParameters(appliedFacets, pageType));
  const hashParametersToQueryParameters = accountForAmpersands(appliedFacetsQueryParameters);

  if (hashParametersToQueryParameters.get('sortByDir') === 'unset') {
    hashParametersToQueryParameters.delete('sortByDir');
    hashParametersToQueryParameters.delete('sortByField');
  }
  const storeIdQueryParameters = !hashParametersToQueryParameters.get('storeId') ? new URLSearchParams(storeIdToQueryParameters(storeId)) : '';
  const sortByQueryParameters =
    !hashParametersToQueryParameters.get('sortByDir') && !hashParametersToQueryParameters.get('sortByField')
      ? new URLSearchParams(sortByToQueryParameters(sortByOptions))
      : '';

  const { locale, brand, market, abSeg, customUrl, params } = usePLPState();
  const departmentId = getDepartmentIdFromUrl(customUrl);
  const isStateAppliedOnInitialLoad = useRef(false);
  const [isAfterPreSelectedFacets, setIsAfterPreSelectedFacets] = useState(false);

  useEffect(() => {
    if (!isStateAppliedOnInitialLoad.current && typeof window !== 'undefined') {
      params && !window.location.hash
        ? handlePreSelectedFacets(params?.indexableFacets, params?.style, departmentId, setIsAfterPreSelectedFacets)
        : setIsAfterPreSelectedFacets(true);
      isStateAppliedOnInitialLoad.current = true;
    }
  }, []);

  const isCatConstructorEnabled = useConstructor();
  const isSoldOut = useOutOfStock();
  const facetsOrder = useFacetsOrder();
  const isDynamicPhotographyEnabled = useDynamicPhotography();

  const plpFeaturesForApi = { facetsOrder };

  const pageNumberToUse = pageNumber || pageNumberFromHash();

  searchParams.set('pageSize', PAGE_SIZE_LIMIT);
  searchParams.set('pageNumber', `${pageNumberToUse}`);

  hashParametersToQueryParameters.delete('pageId');

  if (pageType === CATEGORY_PAGE) {
    searchParams.set('ignoreInventory', `${isSoldOut}`);
    cid && searchParams.set('cid', cid);
    if (isCatConstructorEnabled) {
      const { categoryVendor, clientId, sessionId, clientUid } = getCategoryVendorParams();
      categoryVendor && searchParams.set('vendor', categoryVendor);
      clientId && searchParams.set('client_id', clientId);
      sessionId && searchParams.set('session_id', sessionId);
      clientUid && searchParams.set('uid', clientUid);
    } else {
      const vendor = getVendor(brand, market, abSeg);
      const trackingId = parseResIdFromCookie();
      vendor && searchParams.set('vendor', vendor);
      trackingId && searchParams.set('trackingid', trackingId);
    }
    searchParams.set('includeMarketingFlagsDetails', 'true');

    if (facetsOrder.isDynamicFacetsEnabled) {
      searchParams.set('enableDynamicFacets', 'true');
    }

    if (isHybrid) {
      searchParams.set('pageSize', HYBRID_PAGE_LIMIT);
    }
    if (isDynamicPhotographyEnabled) {
      searchParams.set('enableDynamicPhoto', 'true');
    }
  }

  if (pageType === SEARCH_PAGE) {
    const { searchVendor, clientId, sessionId, clientUid } = getSearchVendorParams();
    searchText && searchParams.set('keyword', searchText);
    searchVendor && searchParams.set('searchVendor', searchVendor);
    clientId && searchParams.set('client_id', clientId);
    sessionId && searchParams.set('session_id', sessionId);
    clientUid && searchParams.set('uid', clientUid);
    // TODO: remove after migration to new size hash params on search page
    if (hasSizeParamForSearchPageInterim(hashParametersToQueryParameters, pageType)) {
      const sizeHash = hashParametersToQueryParameters.get('size');
      const mappedHashSize = mapSizeFacetForSearchPageInterim(sizeHash).join(',');
      hashParametersToQueryParameters.set('size', mappedHashSize);
    }
  }

  const previewDateFromUrl = getURLSearchParam('previewDate');
  const resolvedPreviewDate = previewDate || previewDateFromUrl;

  if (resolvedPreviewDate) {
    appliedFacetsQueryParameters.set('previewDate', resolvedPreviewDate);
    appliedFacetsQueryParameters.set('mode', 'wip');
    const adAwareFromUrl = getURLSearchParam('adAware');
    if (adAwareFromUrl !== null && pageType === CATEGORY_PAGE) {
      appliedFacetsQueryParameters.set('autoDiscovery', adAwareFromUrl);
    }
    const inventoryAwareFromUrl = getURLSearchParam('inventoryAware');
    if (inventoryAwareFromUrl !== null && pageType === CATEGORY_PAGE) {
      const isInventoryAware = inventoryAwareFromUrl === 'true';
      searchParams.set('ignoreInventory', (!isInventoryAware).toString());
    }
  }

  searchParams.set('brand', brand);
  searchParams.set('locale', locale);
  searchParams.set('market', market);

  const convertURlSearchParamsToPlainObject = (formattedURLParams: URLSearchParams) => {
    const plainParams: Record<string, string> = {};
    for (const [key, value] of formattedURLParams.entries()) {
      const formattedValue = !value ? '' : value;
      plainParams[key] = formattedValue.split(',').sort().join();
    }
    return plainParams;
  };

  const storeIdQuery = storeId ? `&${storeIdQueryParameters}` : '';
  const queryParameters = `${searchParams}&${appliedFacetsQueryParameters}&${hashParametersToQueryParameters}&${sortByQueryParameters}${storeIdQuery}`;
  const cleanedQueryParameters = queryParameters.replace(/&{2,}/g, '&');
  const formattedQueryParams = cleanedQueryParameters.endsWith('&') ? cleanedQueryParameters.slice(0, -1) : cleanedQueryParameters;
  const formattedURLParams = new URLSearchParams(formattedQueryParams);
  const plainFormattedParamsObject = convertURlSearchParamsToPlainObject(formattedURLParams);

  const { data, error, isLoading, isFetching, isSuccess, isError } = useQuery<PsDataProviderContext>({
    queryKey: ['psData', url, plainFormattedParamsObject],
    queryFn: () => fetchProducts(url, pageType, formattedQueryParams, cid, searchText, plpFeaturesForApi),
    retry: false,
    enabled: !isPaginationRewriteEnabled && isAfterPreSelectedFacets,
  });

  const multiQueriesData = useProductFetchQuery({
    cid,
    url,
    pageType,
    queryParams: formattedQueryParams,
    queryParamsKey: plainFormattedParamsObject,
    searchText,
    plpFeaturesForApi,
    isPaginationRewriteEnabled,
    pageNumberToUse,
    isAfterPreSelectedFacets,
  });

  if (isPaginationRewriteEnabled) {
    return {
      data: multiQueriesData.data,
      error: multiQueriesData.error,
      isLoading: multiQueriesData.isLoading,
      isFetching: multiQueriesData.isFetching,
      isSuccess: multiQueriesData.isSuccess,
      isError: multiQueriesData.isError,
    };
  }

  return {
    data,
    error,
    isLoading,
    isFetching,
    isSuccess,
    isError,
  };
};
