import React from 'react';
import { render } from '@testing-library/react';
import { useSeoComponents } from '@ecom-next/category/pages/SeoComponentsProvider';
import { useIsBreadcrumbRewrite, usePLPState, brandIsBr } from '@ecom-next/plp';
import { TopBreadcrumbs, BottomBreadcrumbs } from '../ProductListBreadcrumbs';

jest.mock('@ecom-next/plp');
jest.mock('@ecom-next/category/pages/SeoComponentsProvider', () => ({
  useSeoComponents: jest.fn(),
}));

describe('ProductListBreadcrumbs', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('TopBreadcrumbs', () => {
    beforeEach(() => {
      (usePLPState as jest.Mock).mockReturnValue({ brand: 'gap' });
      (brandIsBr as unknown as jest.Mock).mockReturnValue(false);
    });
    it('renders the top breadcrumb correctly', () => {
      (useSeoComponents as jest.Mock).mockReturnValue({
        topBreadcrumb: 'Top Breadcrumb Content',
      });

      const { getByText } = render(<TopBreadcrumbs />);
      expect(getByText('Top Breadcrumb Content')).toBeInTheDocument();
    });

    it('applies the correct className', () => {
      (useSeoComponents as jest.Mock).mockReturnValue({
        topBreadcrumb: 'Top Breadcrumb Content',
      });

      const { container } = render(<TopBreadcrumbs />);
      expect(container.firstChild).toHaveClass('plp_breadcrumbs-top-wrapper');
      expect(container.firstChild?.firstChild).toHaveClass('plp_breadcrumbs');
    });
  });

  describe('BottomBreadcrumbs', () => {
    beforeEach(() => {
      (useIsBreadcrumbRewrite as jest.Mock).mockReturnValue(false);
      (usePLPState as jest.Mock).mockReturnValue({ brand: 'gap' });
      (brandIsBr as unknown as jest.Mock).mockReturnValue(false);
    });

    it('renders the bottom breadcrumb correctly', () => {
      (useSeoComponents as jest.Mock).mockReturnValue({
        bottomBreadcrumb: 'Bottom Breadcrumb Content',
      });

      const { getByText } = render(<BottomBreadcrumbs />);
      expect(getByText('Bottom Breadcrumb Content')).toBeInTheDocument();
    });

    it('applies the correct className', () => {
      (useSeoComponents as jest.Mock).mockReturnValue({
        bottomBreadcrumb: 'Bottom Breadcrumb Content',
      });

      const { container } = render(<BottomBreadcrumbs />);
      expect(container.firstChild).toHaveClass('plp_breadcrumbs-bottom-wrapper');
      expect(container.firstChild?.firstChild).toHaveClass('plp_breadcrumbs');
    });
    it('applies the correct className for BR brands when breadcrumb rewrite is enabled', () => {
      (useIsBreadcrumbRewrite as jest.Mock).mockReturnValue(true);
      (usePLPState as jest.Mock).mockReturnValue({ brand: 'br' });
      (brandIsBr as unknown as jest.Mock).mockReturnValue(true);
      const { container } = render(<BottomBreadcrumbs />);
      expect(container.firstChild).toHaveClass('plp_breadcrumbs-bottom-wrapper--br');
    });
  });
});
