import { useSeoComponents } from '@ecom-next/category/pages/SeoComponentsProvider';
import { useIsBreadcrumbRewrite, usePLPState, brandIsBr } from '@ecom-next/plp';

const TopBreadcrumbs = () => {
  const { topBreadcrumb } = useSeoComponents();
  const { brand } = usePLPState();

  if (brandIsBr(brand)) return null;

  return (
    <div className='plp_breadcrumbs-top-wrapper'>
      <div className='plp_breadcrumbs'>{topBreadcrumb}</div>
    </div>
  );
};

const BottomBreadcrumbs = () => {
  const { bottomBreadcrumb } = useSeoComponents();
  const isBreadcrumbRewrite = useIsBreadcrumbRewrite();
  const { brand } = usePLPState();
  const bottomWrapperClass = isBreadcrumbRewrite && brandIsBr(brand) ? 'plp_breadcrumbs-bottom-wrapper--br' : 'plp_breadcrumbs-bottom-wrapper';
  return (
    <div className={bottomWrapperClass}>
      <div className='plp_breadcrumbs'>{bottomBreadcrumb}</div>
    </div>
  );
};

export { TopBreadcrumbs, BottomBreadcrumbs };
