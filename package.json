{"name": "ecom-next-monorepo", "version": "1.0.0", "private": true, "workspaces": ["packages/*"], "scripts": {"cm": "cz", "test:changedsince": "npm run test:changedsince --workspaces --if-present", "test": "turbo test --log-order=grouped --concurrency=1 --api='https://turbo-remote-cache.aks.stage.azeus.gaptech.com' --team='monorepo-turbo' --token='dummy'", "test:legacy": "turbo test:legacy --log-order=grouped --concurrency=1 --api='https://turbo-remote-cache.aks.stage.azeus.gaptech.com' --team='monorepo-turbo' --token='dummy'", "test:watch": "turbo test:watch", "spellcheck:ci": "npm run spellcheck --workspaces --if-present", "knip:ci": "npm run knip --workspaces --if-present", "dev": "export NODE_TLS_REJECT_UNAUTHORIZED=0 && turbo dev --env-mode=loose", "dev:product": "export NODE_TLS_REJECT_UNAUTHORIZED=0 && export NODE_OPTIONS='--max-old-space-size=4096' && turbo dev --filter=@ecom-next/core --filter=@ecom-next/product --filter=@ecom-next/app --env-mode=loose", "dev:gapfsca": "export SECURE_ECOM_CLIENT_API_APP_BASE_URL_FACTORY_CA=https://local.www.stage.factory-gaptechol.ca && export ECOM_SERVER_API_BASE_URL=https://local.www.stage.factory-gaptechol.ca && export CATALOG_OMNI_PRODUCT_SERVICE_BASE_URL=https://catalog-apis-omni-product-service.aks.stage.azeus.gaptech.com && export SECURE_ECOM_CLIENT_API_BASE_URL_FACTORY_CA=https://local.www.stage.factory-gaptechol.ca && export SHOPPING_BAG_SERVICE_URL=https://local.secure-www.stage.factory-gaptechol.ca/commerce/shopping-bags && export NEW_EMAIL_REGISTRATION_SERVICE_URL=https://local.secure-www.stage.factory-gaptechol.ca/commerce/communication-preference/v2/subscriptions/email && export NEW_SMS_REGISTRATION_SERVICE_URL=https://local.www.stage.factory-gaptechol.ca/commerce/communication-preference/v2/subscriptions/sms && export ASSET_CLUSTER_URL=https://azeus-brol.live.stage.gaptechol.com && export PMCS_SERVICE_URL=https://browse-api-nginx-cache.aks.stage.azeus.gaptech.com/pmcs && NODE_TLS_REJECT_UNAUTHORIZED=0 && turbo dev --env-mode=loose", "dev:sitewide:navpoc": "export NODE_TLS_REJECT_UNAUTHORIZED=0 && TARGET_ENV=stage CATALOG_INTERNAL_API_BASE_URL=https://ws-catalog-api-service.stage.azeus.gaptech.com turbo dev --env-mode=loose", "dev:https": "export NODE_TLS_REJECT_UNAUTHORIZED=0 && turbo dev:https --env-mode=loose", "build": "turbo build --env-mode=loose", "build:ci": "cd packages/ecom-next && npm run build", "build:analyze": "ANALYZE=true turbo build --env-mode=loose", "start": "turbo start --env-mode=loose", "storybook:core": "npm run storybook --workspace=@ecom-next/core", "storybook:mui": "npm run storybook --workspace=@ecom-next/marketing-ui", "sync:seo": "rm -rf packages/seo/src/legacy && node scripts/fui-sync/port-seo.js", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "build-storybook:ci": "node --max-old-space-size=4096 node_modules/.bin/storybook build", "build-storybook:core": "npm run build-storybook:core --workspaces --if-present", "build-storybook:mui": "npm run build-storybook:mui --workspaces --if-present", "prepare": "husky", "lint": "turbo lint --log-order=grouped --api='https://turbo-remote-cache.aks.stage.azeus.gaptech.com' --team='monorepo-turbo' --token='dummy' && npm run lint:root", "lint:root": "eslint --ignore-pattern '**/{react-stitch,playwright-report,test_data}/*' ./stories ./user-journeys", "lint:fix": "turbo lint --log-order=grouped --continue -- --fix && npm run lint:root -- --fix", "lint:force": "turbo lint --force --continue --log-order=grouped --api='https://turbo-remote-cache.aks.stage.azeus.gaptech.com' --team='monorepo-turbo' --token='dummy' -- --fix && npm run lint:root -- --fix", "lint:check-for-v0": "stylelint 'packages/**/*.css' --config .stylelintrc.js", "format": "turbo format --log-order=grouped --api='https://turbo-remote-cache.aks.stage.azeus.gaptech.com' --team='monorepo-turbo' --token='dummy' && npm run format:root", "format:root": "prettier ./*.* ./.github ./.storybook ./.storybook-static ./chartis ./docs ./scripts ./stories ./types ./user-journeys -u --write", "type-check": "turbo type-check --log-order=grouped --api='https://turbo-remote-cache.aks.stage.azeus.gaptech.com' --team='monorepo-turbo' --token='dummy'", "figma:extract-all": "npm run figma:extract --workspaces --if-present && npm run figma:text-styles --workspaces --if-present", "figma:extract": "npm run figma:extract --workspaces --if-present", "figma:text-styles": "npm run figma:text-styles --workspaces --if-present", "figma:validate": "npm run figma:validate --workspaces --if-present", "figma:validate:v0": "npm run figma:validate:v0 --workspaces --if-present", "merge-figma-custom-variables": "npm run merge-figma-custom-variables --workspaces --if-present", "plop": "plop", "visual-regression-test:update-snapshots": "npx cross-env STORYBOOK_URL=https://ecom-next-storybook.aks.test.azeus.gaptech.com npm run visual-regression-test --workspaces --if-present -- --update-snapshots", "visual-regression-test:local-test": "npm run visual-regression-test --workspaces --if-present --", "visual-regression-test:ci": "npm run visual-regression-test:update-snapshots && npm run visual-regression-test:local-test", "visual-regression-test:fabric:ci": "npm run visual-regression-test:fabric:ci --workspaces --if-present", "visual-regression-test:mui:ci": "npm run visual-regression-test:mui:ci --workspaces --if-present", "visual-regression-test:ci-update-snapshots": "npm run visual-regression-test:update-snapshots && npm run visual-regression-test:local-test -- --update-snapshots"}, "devDependencies": {"@apidevtools/json-schema-ref-parser": "^9.1.2", "@babel/preset-react": "^7.23.3", "@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^17.8.1", "@commitlint/format": "^19.3.0", "@emotion/babel-preset-css-prop": "^11.11.0", "@jest/globals": "^29.7.0", "@mdx-js/loader": "^3.1.0", "@next/eslint-plugin-next": "^14.2.3", "@storybook/addon-a11y": "^9.0.8", "@storybook/addon-docs": "^9.0.8", "@storybook/addon-links": "^9.0.8", "@storybook/addon-onboarding": "^9.0.8", "@storybook/nextjs": "^9.0.8", "@swc/core": "^1.12.1", "@testing-library/jest-dom": "^6.4.5", "@testing-library/react": "^15.0.7", "@testing-library/react-hooks": "7.0.2", "@testing-library/user-event": "^14.5.2", "@types/cheerio": "^0.22.35", "@types/jest": "^29.5.4", "@types/node": "20.5.0", "@types/react": "18.2.20", "@types/react-dom": "18.2.7", "@types/react-slider": "^1.3.6", "@types/testing-library__jest-dom": "^5.14.9", "@typescript-eslint/eslint-plugin": "^7.12.0", "@typescript-eslint/parser": "^7.12.0", "chromatic": "^11.12.5", "clock-mock": "^2.0.2", "commitlint": "^17.7.1", "copy-webpack-plugin": "^11.0.0", "cspell": "^8.15.7", "dotenv": "^16.4.5", "eslint": "8.57.0", "eslint-config-next": "14.2.13", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jest": "^28.6.0", "eslint-plugin-jest-dom": "^5.4.0", "eslint-plugin-jsx-a11y": "6.8.0", "eslint-plugin-playwright": "^1.6.2", "eslint-plugin-react": "^7.34.2", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-storybook": "^0.8.0", "eslint-plugin-typescript-sort-keys": "^3.2.0", "fast-glob": "^3.3.1", "fetch-mock": "^9.11.0", "fs-extra": "^11.1.1", "globals": "^15.3.0", "gulp": "^5.0.0", "gulp-sass": "^5.1.0", "husky": "^9.0.11", "jest": "^29.6.4", "jest-environment-jsdom": "^29.7.0", "jquery": "^3.7.1", "json-schema-to-zod": "^1.1.1", "knip": "^5.36.7", "lint-staged": "^15.0.2", "mime": "^1.6.0", "netstorageapi": "^1.1.5", "pino-pretty": "^11.2.2", "plop": "^4.0.1", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.6", "react": "^18.3.1", "react-dom": "^18.3.1", "slick-carousel": "^1.8.1", "storybook": "^9.0.8", "storybook-addon-fetch-mock": "2.0.0", "stylelint": "^16.19.1", "swc-loader": "^0.2.6", "tailwind-merge": "^2.3.0", "tailwind-variants": "^0.2.1", "tailwindcss": "^3.4.3", "tailwindcss-animate": "^1.0.7", "ts-jest": "^29.1.1", "tsconfig-paths-webpack-plugin": "^4.1.0", "turbo": "^2.1.3", "typescript": "5.8.2", "typescript-eslint": "^7.11.0", "webpack": "^5.96.1"}, "overrides": {"axios": "^1.8.2", "jsdom": "20.0.3", "nwsapi": "2.2.5", "uuid": "9.0.1", "cross-spawn": "7.0.5", "form-data": "4.0.4"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix --ignore-pattern '**/{marketing-ui,plp-ui,sitewide,legacy,core,test_data}/*'", "prettier --write --ignore-unknown"], "!(*.js|*.jsx|*.ts|*.tsx)": "prettier --write --ignore-unknown"}, "engines": {"node": "^22.0.0", "npm": ">=10.0.0"}, "dependencies": {"@isaacs/ttlcache": "^1.4.1", "@playwright/test": "1.53.1", "@popperjs/core": "^2.11.8", "cheerio": "^1.0.0-rc.3", "esbuild": "^0.23.1", "newrelic": "^12.23.0", "next": "14.2.25", "next-logger": "^5.0.1", "react-popper": "^2.3.0", "react-slider": "^2.0.6", "swiper": "^11.2.6"}, "packageManager": "npm@10.8.3"}