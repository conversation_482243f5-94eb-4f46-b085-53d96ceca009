// @ts-check
import { expect } from '@playwright/test';
import test from '@/pom/base.page';
import { waitForHydration } from '@/utils/helpers';

const brand = process.env.BRAND;
const market = process.env.MARKET;
const breakpoint = process.env.BREAKPOINT;
const env = process.env.ENV;

test.describe(`My Account`, { tag: ['@run-all', '@do-not-set-segment'] }, () => {
  test.describe(`Profile`, () => {
    test.describe(`GIFT CARD`, () => {
      test.beforeEach(`Sign in to`, async ({ page, signInPage, sitewide, builder }) => {
        await signInPage.goToSignInPage();
        await waitForHydration(page);
        const customerInfo = builder.returningCustomer();
        await signInPage.signInWithEmailAndPassword(customerInfo.email, customerInfo.pw);
        await waitForHydration(page);

        await test.step(`Ensure that Sitewide Components are visible on page.`, async () => {
          await sitewide.checkSiteWideElementsAreOnPage();
        });
      });

      test(
        `${brand}-${market}-${breakpoint}-${env}: Existing user can click gift cards link and go to gift cards page.`,
        { tag: '@skip-ca' },
        async ({ myAccountPage, page }) => {
          await test.step(`Go to Gift Card page`, async () => {
            await expect(myAccountPage.reloadGiftCardLink, 'validate that the reload gift card link is accurate').toHaveAttribute(
              'href',
              /cashstar\.com\/reload/
            );
            await myAccountPage.giftCardLink.click();
            await waitForHydration(page);
            await expect(myAccountPage.giftCardText.first(), `validate gift card page is present`).toBeVisible();
          });
        }
      );

      test(
        `${brand}-${market}-${breakpoint}-${env}: Existing user can check gift card balance and see error when invalid card is entered.`,
        { tag: '@skip-preview' },
        async ({ myAccountPage }) => {
          await test.step(`Go to Gift Card page`, async () => {
            await myAccountPage.giftCardBalanceLink.click();
            await expect(myAccountPage.checkBalanceCardNumber).toBeVisible();
            await expect(myAccountPage.checkBalanceSubmit).toBeVisible();
            await expect(myAccountPage.checkBalanceCardPin).toBeVisible();
            // await myAccountPage.checkBalanceCardNumber.click();
            // await myAccountPage.checkBalanceCardNumber.fill('****************');
            // await myAccountPage.checkBalanceCardPin.click();
            // await myAccountPage.checkBalanceCardPin.fill('1234');
            // await myAccountPage.checkBalanceSubmit.click();
            // await expect(page.getByText('We don’t recognize that gift'), 'validate that an invalid card number shows an error').toBeVisible();
          });
        }
      );
    });
  });
});
