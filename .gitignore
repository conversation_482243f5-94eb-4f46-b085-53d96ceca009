# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
.pnp
.pnp.js

# testing
/coverage
/packages/*/coverage

# next.js
.next/
out/

# production
build

.npmrc

dist

# misc
.DS_Store
*.pem
.swc
!GapInc_Root_G2.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Custom .vscode ignore
.vscode/

# idea
.idea
*.iml

# Playwright
playwright-report
allure-results


##Ignoring newrelic logs
newrelic_agent.log

# local certs
**/certifcates

storybook-static/

test-results/
packages/ecom-next/netsorage-upload-files-using-wacli.sh
packages/ecom-next/netstorage_upload_logs.txt
user-journeys/artifacts
user-journeys/playwright-report
stageProductId*.json
skus/
**/github-test-data/

# turborepo cache
.turbo

#Visual Regression Testing Storybook
packages/**/test-results
packages/**/playwright-report
packages/**/blob-report
packages/**/playwright/.cache
packages/**/stories.json
packages/**/stories-legacy.json
packages/**/visual-regression.spec.ts-snapshots
packages/**/visual-regression-legacy.spec.ts-snapshots

#V0 deprecated tokens files
packages/core/V0-figma-tokens-used-in-css-files.csv
packages/core/V0-figma-tokens-used-in-tsx-files.csv


qemu_node*
/core
style.zip
site-reliability-data/
*darwin.png
report-*.zip
qemu_*
.github/instructions/