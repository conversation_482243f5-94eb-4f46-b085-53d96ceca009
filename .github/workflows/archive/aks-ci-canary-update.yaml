name: Update Canary Percent/Promote Canary for ${{ inputs.environment }} (Manual and Reusable)
permissions:
  id-token: write
  contents: write
on:
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        description: Which environment you want to deploy
        options:
          - prod
          - preview
      canary-percent:
        description: 'Canary percent to be ramped up'
        required: true
        default: '0'
      promote-canary:
        type: boolean
        description: 'Do we need to promote canary?'
        default: false
  workflow_call:
    inputs:
      environment:
        type: string
        description: Which environment you want to deploy
        required: true
        default: preview
      canary-percent:
        type: string
        description: 'Canary percent to be ramped up'
        required: true
        default: '0'
      promote-canary:
        type: boolean
        description: 'Do we need to promote canary?'
        required: true
        default: false
jobs:
  vars: # Job to set variables
    runs-on: ['self-hosted', arc-dind-rootless-enterprise]
    environment: ${{ inputs.environment }}
    env:
      ENVIRONMENT: ${{ inputs.environment }}
      CANARY_PERCENT: ${{ inputs.canary-percent }}
      PROMOTE_CANARY: ${{ inputs.promote-canary }}
    steps:
      - name: validate inputs
        run: |
          if [[ "${{ env.CANARY_PERCENT }}" != "100" && "${{ env.PROMOTE_CANARY }}" == "true" ]]; then
            echo "Error: Canary can only promoted when canary percentage is 100%"
            exit 1
          fi
      - name: checkout the repo #each jobs runs on its own instance of an action runner, so if we need to interact with the repo, we need to check it out
        uses: actions/checkout@v4
        with:
          ref: main
      - name: Setting Variables
        id: get_version_and_slack_channel
        run: |
          echo "environment=${ENVIRONMENT}"
          echo "canary-percent=${CANARY_PERCENT}"
          echo "promote-canary=${PROMOTE_CANARY}"
          echo "version=$(yq -r '.kubernetes.dockerImageTag' chartis/${ENVIRONMENT}.yaml)" >> $GITHUB_OUTPUT
          if [ ${ENVIRONMENT} == "preview" ];
            then
              echo "channel_id=C06D4JXG38S" >> $GITHUB_OUTPUT
              echo "app-name=ecom-next-preview" >> $GITHUB_OUTPUT
            else
              echo "channel_id=C06D20TGNSH" >> $GITHUB_OUTPUT
              echo "app-name=ecom-next" >> $GITHUB_OUTPUT
          fi
    outputs:
      deploy-file: 'chartis/${{ inputs.environment }}.yaml'
      app-name: ${{ steps.get_version_and_slack_channel.outputs.app-name }}
      version: ${{ steps.get_version_and_slack_channel.outputs.version }}
      channel-id: ${{ steps.get_version_and_slack_channel.outputs.channel_id }}
  canary-update:
    needs:
      - vars
    runs-on: [self-hosted]
    steps:
      - name: Generate token
        id: generate_token
        uses: tibdex/github-app-token@b62528385c34dbc9f38e5f4225ac829252d1ea92
        with:
          app_id: ${{ secrets.BRANCH_PROTECT_BYPASS_GITHUB_APP_ID }}
          private_key: ${{ secrets.BRANCH_PROTECT_BYPASS_GITHUB_APP_PRIVATE_KEY }}
      - name: checkout the repo #each jobs runs on its own instance of an action runner, so if we need to interact with the repo, we need to check it out
        uses: actions/checkout@v4
        with:
          token: ${{ steps.generate_token.outputs.token }}
          ref: main
      - name: update manifest with canary values for ${{ inputs.environment }} # updating chartis-manifest
        uses: cx-common/canary-values-update@main
        with:
          canary-percent: ${{ inputs.canary-percent }}
          promote-canary: ${{ inputs.promote-canary }}
          deploy-file: ${{ needs.vars.outputs.deploy-file }}
          github-token: ${{ steps.generate_token.outputs.token }}
          deploy-branch: main
  chartis-deploy:
    needs:
      - canary-update
      - vars
    uses: continuous-delivery-platform/chartis-deploy/.github/workflows/chartis-deploy.yml@v1
    with:
      chartis-file: ${{ needs.vars.outputs.deploy-file }}
      branch: main
      create-pre-approved-change: true
  report-slack-status:
    needs:
      - vars
      - chartis-deploy
    if: always()
    runs-on: [self-hosted, arc-dind-rootless-enterprise]
    steps:
      - name: Publish update to Slack
        uses: slackapi/slack-github-action@v1.24.0
        with:
          channel-id: ${{ needs.vars.outputs.channel-id }}
          payload: |
            {
              "attachments": [
                {
                  "color": "${{ needs.chartis-deploy.result == 'success' && '#00FF00' || '#FF0000' }}",
                  "blocks": [
                    {
                      "type": "header",
                      "text": {
                        "type": "plain_text",
                        "text": "Deploy ${{ inputs.environment }} Status - ${{ github.event.repository.name }} "
                      }
                    },
                    {
                      "type": "divider"
                    },
                    {
                      "type": "section",
                      "fields": [
                        {
                          "type": "mrkdwn",
                          "text": "*Activity:*\n Deploying ${{ github.event.repository.name }} with commit: <${{ github.server_url }}/${{ github.repository }}/releases/tag/${{ needs.vars.outputs.version }}|${{ needs.vars.outputs.version }}>"
                        },
                        {
                          "type": "mrkdwn",
                          "text": "*Status:*\n${{ needs.chartis-deploy.result }} ${{ needs.chartis-deploy.result == 'success' && ':white_check_mark:' || ':x:' }} (view latest <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}| run>)"
                        },
                        {
                          "type": "mrkdwn",
                          "text": "*User:*\n${{ github.actor }}"
                        },
                        {
                          "type": "mrkdwn",
                          "text": "*Version:*\n${{ needs.vars.outputs.version }}"
                        },
                        {
                          "type": "mrkdwn",
                          "text": "*Canary Percent:*\n${{ inputs.promote-canary && 'NA' ||  inputs.canary-percent }}"
                        },
                        {
                          "type": "mrkdwn",
                          "text": "*Promoted:*\n${{ inputs.promote-canary && 'Yes' || 'No'}}"
                        }
                      ]
                    }
                  ]
                }
              ]
            }
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
