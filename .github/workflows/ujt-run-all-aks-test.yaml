name: Run Playwright Tests
run-name: ${{ inputs.environment }}${{ !startsWith(github.event.workflow, '.github/workflows/ujt') && ' - canary' || '' }}${{ inputs.override-jobs && ' - override' || '' }} (User-Journey-Tests)

on:
  workflow_call:
    inputs:
      environment:
        description: 'Environment'
        required: false
        default: 'stage'
        type: string
      override-jobs:
        description: 'Filter by job variables (pipe separated)'
        required: false
        type: string
      bypass-observepoint-script:
        description: 'If checked, the ObservePoint script will not be run'
        required: false
        default: false
        type: boolean
      bypass-ujt-scripts-prep:
        description: 'If present, the UJT scripts prep step will be skipped and use the provided list of scripts'
        required: false
        type: string
      preview-date:
        description: 'If present, the UJT scripts prep step will be skipped and use the provided list of scripts'
        required: false
        type: string
      lt:
        description: 'If present, the UJTs to run on LambdaTest, put true'
        required: false
        type: string
      run-full-suite:
        description: 'If present, the full suite of tests will be run'
        required: false
        default: false
        type: boolean
    outputs:
      report-url:
        description: 'URL to the generated report'
        value: ${{ jobs.build-report.outputs.uploaded-url }}

  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment'
        required: false
        default: 'stage'
        type: choice
        options:
          - local
          - stage
          - wip-stage
          - preview
          - prod
      override-jobs:
        description: 'Filter by job variables (pipe separated)'
        required: false
        type: string
      feature-flags:
        description: 'Feature flags to enable (comma separated)'
        required: false
        type: string
      segments:
        description: 'Segments to enable (comma separated, dash notation like gap189-x)'
        required: false
        type: string
      run-full-suite:
        description: 'If present, the full suite of tests will be run'
        required: false
        default: false
        type: boolean
  schedule:
    - cron: '0 8 * * *'

env:
  ENV: ${{ inputs.environment }}
  LT: ${{ inputs.lt }}
  DEPLOYMENT_TEST: ${{ inputs.run-full-suite && 'false' || 'true' }}

jobs:
  ujt-scripts-prep:
    name: Load NPM Scripts for ${{ inputs.environment }} Environment
    runs-on: self-hosted
    steps:
      - id: scripts
        if: ${{ !inputs.bypass-ujt-scripts-prep && !(github.event.workflow == '.github/workflows/ujt-run-all-aks-test.yaml' && github.event_name == 'schedule') }}
        run: |
          case $ENV in
              local)
                  echo 'scripts<<EOF
                  [' >> $GITHUB_OUTPUT
                  echo '{script: "category-at-us-desktop-local"},
                      {script: "category-at-us-mobile-local"},
                      {script: "checkout-at-us-desktop-local"},
                      {script: "checkout-at-us-mobile-local"},
                      {script: "my-account-at-us-desktop-local"},
                      {script: "my-account-at-us-mobile-local"},
                      {script: "product-at-us-desktop-local"},
                      {script: "product-at-us-mobile-local"},
                      {script: "search-at-us-desktop-local"},
                      {script: "search-at-us-mobile-local"},
                      {script: "shopping-bag-at-us-desktop-local"},
                      {script: "shopping-bag-at-us-mobile-local"},
                      {script: "sitewide-at-us-desktop-local", shard: 1, total: 2},
                      {script: "sitewide-at-us-mobile-local", shard: 1, total: 2},
                      {script: "sitewide-at-us-desktop-local", shard: 2, total: 2},
                      {script: "sitewide-at-us-mobile-local", shard: 2, total: 2},
                      {script: "store-locator-at-us-desktop-local"},
                      {script: "store-locator-at-us-mobile-local"},
                      {script: "utility-at-us-desktop-local"},
                      {script: "utility-at-us-mobile-local"}'${{ inputs.override-jobs && format(' | grep -E ''{0}''', inputs.override-jobs) || ' | grep -v -E ''accessibility|store-locator''' }} >> $GITHUB_OUTPUT
                  echo ']' >> $GITHUB_OUTPUT
                  echo 'EOF' >> $GITHUB_OUTPUT
                  ;;
              stage|wip-stage|preview|prod)
                  echo 'scripts<<EOF
                  [' >> $GITHUB_OUTPUT
                  echo "{script: \"accessibility-at-us-desktop-${ENV}\"},
                      {script: \"accessibility-at-us-mobile-${ENV}\"},
                      {script: \"accessibility-at-ca-desktop-${ENV}\"},
                      {script: \"accessibility-at-ca-mobile-${ENV}\"},
                      {script: \"accessibility-br-us-desktop-${ENV}\"},
                      {script: \"accessibility-br-us-mobile-${ENV}\"},
                      {script: \"accessibility-br-ca-desktop-${ENV}\"},
                      {script: \"accessibility-br-ca-mobile-${ENV}\"},
                      {script: \"accessibility-gp-us-desktop-${ENV}\"},
                      {script: \"accessibility-gp-us-mobile-${ENV}\"},
                      {script: \"accessibility-gp-ca-desktop-${ENV}\"},
                      {script: \"accessibility-gp-ca-mobile-${ENV}\"},
                      {script: \"accessibility-on-us-desktop-${ENV}\"},
                      {script: \"accessibility-on-us-mobile-${ENV}\"},
                      {script: \"accessibility-on-ca-desktop-${ENV}\"},
                      {script: \"accessibility-on-ca-mobile-${ENV}\"},
                      {script: \"category-at-us-desktop-${ENV}\"},
                      {script: \"category-at-us-mobile-${ENV}\"},
                      {script: \"category-at-ca-desktop-${ENV}\"},
                      {script: \"category-at-ca-mobile-${ENV}\"},
                      {script: \"category-br-us-desktop-${ENV}\"},
                      {script: \"category-br-us-mobile-${ENV}\"},
                      {script: \"category-br-ca-desktop-${ENV}\"},
                      {script: \"category-br-ca-mobile-${ENV}\"},
                      {script: \"category-brf-us-desktop-${ENV}\"},
                      {script: \"category-brf-us-mobile-${ENV}\"},
                      {script: \"category-brf-ca-desktop-${ENV}\"},
                      {script: \"category-brf-ca-mobile-${ENV}\"},
                      {script: \"category-gp-us-desktop-${ENV}\"},
                      {script: \"category-gp-us-mobile-${ENV}\"},
                      {script: \"category-gp-ca-desktop-${ENV}\"},
                      {script: \"category-gp-ca-mobile-${ENV}\"},
                      {script: \"category-gpf-us-desktop-${ENV}\"},
                      {script: \"category-gpf-us-mobile-${ENV}\"},
                      {script: \"category-on-us-desktop-${ENV}\"},
                      {script: \"category-on-us-mobile-${ENV}\"},
                      {script: \"category-on-ca-desktop-${ENV}\"},
                      {script: \"category-on-ca-mobile-${ENV}\"},
                      {script: \"checkout-at-us-desktop-${ENV}\"},
                      {script: \"checkout-at-us-mobile-${ENV}\"},
                      {script: \"checkout-brf-ca-desktop-${ENV}\"},
                      {script: \"checkout-brf-ca-mobile-${ENV}\"},
                      {script: \"checkout-gpf-us-desktop-${ENV}\"},
                      {script: \"checkout-gpf-us-mobile-${ENV}\"},
                      {script: \"checkout-on-ca-desktop-${ENV}\"},
                      {script: \"checkout-on-ca-mobile-${ENV}\"},
                      {script: \"my-account-at-us-desktop-${ENV}\"},
                      {script: \"my-account-at-us-mobile-${ENV}\"},
                      {script: \"my-account-brf-ca-desktop-${ENV}\"},
                      {script: \"my-account-brf-ca-mobile-${ENV}\"},
                      {script: \"my-account-gpf-us-desktop-${ENV}\"},
                      {script: \"my-account-gpf-us-mobile-${ENV}\"},
                      {script: \"my-account-on-ca-desktop-${ENV}\"},
                      {script: \"my-account-on-ca-mobile-${ENV}\"},
                      {script: \"product-at-us-desktop-${ENV}\"},
                      {script: \"product-at-us-mobile-${ENV}\"},
                      {script: \"product-at-ca-desktop-${ENV}\"},
                      {script: \"product-at-ca-mobile-${ENV}\"},
                      {script: \"product-br-us-desktop-${ENV}\"},
                      {script: \"product-br-us-mobile-${ENV}\"},
                      {script: \"product-br-ca-desktop-${ENV}\"},
                      {script: \"product-br-ca-mobile-${ENV}\"},
                      {script: \"product-brf-us-desktop-${ENV}\"},
                      {script: \"product-brf-us-mobile-${ENV}\"},
                      {script: \"product-brf-ca-desktop-${ENV}\"},
                      {script: \"product-brf-ca-mobile-${ENV}\"},
                      {script: \"product-gp-us-desktop-${ENV}\"},
                      {script: \"product-gp-us-mobile-${ENV}\"},
                      {script: \"product-gp-ca-desktop-${ENV}\"},
                      {script: \"product-gp-ca-mobile-${ENV}\"},
                      {script: \"product-gpf-us-desktop-${ENV}\"},
                      {script: \"product-gpf-us-mobile-${ENV}\"},
                      {script: \"product-on-us-desktop-${ENV}\"},
                      {script: \"product-on-us-mobile-${ENV}\"},
                      {script: \"product-on-ca-desktop-${ENV}\"},
                      {script: \"product-on-ca-mobile-${ENV}\"},
                      {script: \"search-at-us-desktop-${ENV}\"},
                      {script: \"search-at-us-mobile-${ENV}\"},
                      {script: \"search-at-ca-desktop-${ENV}\"},
                      {script: \"search-at-ca-mobile-${ENV}\"},
                      {script: \"search-br-us-desktop-${ENV}\"},
                      {script: \"search-br-us-mobile-${ENV}\"},
                      {script: \"search-br-ca-desktop-${ENV}\"},
                      {script: \"search-br-ca-mobile-${ENV}\"},
                      {script: \"search-brf-us-desktop-${ENV}\"},
                      {script: \"search-brf-us-mobile-${ENV}\"},
                      {script: \"search-brf-ca-desktop-${ENV}\"},
                      {script: \"search-brf-ca-mobile-${ENV}\"},
                      {script: \"search-gp-us-desktop-${ENV}\"},
                      {script: \"search-gp-us-mobile-${ENV}\"},
                      {script: \"search-gp-ca-desktop-${ENV}\"},
                      {script: \"search-gp-ca-mobile-${ENV}\"},
                      {script: \"search-gpf-us-desktop-${ENV}\"},
                      {script: \"search-gpf-us-mobile-${ENV}\"},
                      {script: \"search-on-us-desktop-${ENV}\"},
                      {script: \"search-on-us-mobile-${ENV}\"},
                      {script: \"search-on-ca-desktop-${ENV}\"},
                      {script: \"search-on-ca-mobile-${ENV}\"},
                      {script: \"sitewide-at-us-desktop-${ENV}\", shard: 1, total: 2},
                      {script: \"sitewide-at-us-mobile-${ENV}\", shard: 1, total: 2},
                      {script: \"sitewide-at-ca-desktop-${ENV}\", shard: 1, total: 2},
                      {script: \"sitewide-at-ca-mobile-${ENV}\", shard: 1, total: 2},
                      {script: \"sitewide-at-us-desktop-${ENV}\", shard: 2, total: 2},
                      {script: \"sitewide-at-us-mobile-${ENV}\", shard: 2, total: 2},
                      {script: \"sitewide-at-ca-desktop-${ENV}\", shard: 2, total: 2},
                      {script: \"sitewide-at-ca-mobile-${ENV}\", shard: 2, total: 2},
                      {script: \"sitewide-br-us-desktop-${ENV}\"},
                      {script: \"sitewide-br-us-mobile-${ENV}\"},
                      {script: \"sitewide-br-ca-desktop-${ENV}\"},
                      {script: \"sitewide-br-ca-mobile-${ENV}\"},
                      {script: \"sitewide-brf-us-desktop-${ENV}\"},
                      {script: \"sitewide-brf-us-mobile-${ENV}\"},
                      {script: \"sitewide-brf-ca-desktop-${ENV}\"},
                      {script: \"sitewide-brf-ca-mobile-${ENV}\"},
                      {script: \"sitewide-gp-us-desktop-${ENV}\"},
                      {script: \"sitewide-gp-us-mobile-${ENV}\"},
                      {script: \"sitewide-gp-ca-desktop-${ENV}\"},
                      {script: \"sitewide-gp-ca-mobile-${ENV}\"},
                      {script: \"sitewide-gpf-us-desktop-${ENV}\"},
                      {script: \"sitewide-gpf-us-mobile-${ENV}\"},
                      {script: \"sitewide-on-us-desktop-${ENV}\"},
                      {script: \"sitewide-on-us-mobile-${ENV}\"},
                      {script: \"sitewide-on-ca-desktop-${ENV}\"},
                      {script: \"sitewide-on-ca-mobile-${ENV}\"},
                      {script: \"shopping-bag-gp-us-desktop-${ENV}\"},
                      {script: \"shopping-bag-gp-us-mobile-${ENV}\"},
                      {script: \"shopping-bag-gp-ca-desktop-${ENV}\"},
                      {script: \"shopping-bag-gp-ca-mobile-${ENV}\"},
                      {script: \"shopping-bag-gpf-us-desktop-${ENV}\"},
                      {script: \"shopping-bag-gpf-us-mobile-${ENV}\"},
                      {script: \"shopping-bag-brf-ca-desktop-${ENV}\"},
                      {script: \"shopping-bag-brf-ca-mobile-${ENV}\"},
                      {script: \"store-locator-at-us-desktop-${ENV}\"},
                      {script: \"store-locator-at-us-mobile-${ENV}\"},
                      {script: \"store-locator-at-ca-desktop-${ENV}\"},
                      {script: \"store-locator-at-ca-mobile-${ENV}\"},
                      {script: \"store-locator-br-us-desktop-${ENV}\"},
                      {script: \"store-locator-br-us-mobile-${ENV}\"},
                      {script: \"store-locator-br-ca-desktop-${ENV}\"},
                      {script: \"store-locator-br-ca-mobile-${ENV}\"},
                      {script: \"store-locator-brf-us-desktop-${ENV}\"},
                      {script: \"store-locator-brf-us-mobile-${ENV}\"},
                      {script: \"store-locator-gp-us-desktop-${ENV}\"},
                      {script: \"store-locator-gp-us-mobile-${ENV}\"},
                      {script: \"store-locator-gp-ca-desktop-${ENV}\"},
                      {script: \"store-locator-gp-ca-mobile-${ENV}\"},
                      {script: \"store-locator-gpf-us-desktop-${ENV}\"},
                      {script: \"store-locator-gpf-us-mobile-${ENV}\"},
                      {script: \"store-locator-on-us-desktop-${ENV}\"},
                      {script: \"store-locator-on-us-mobile-${ENV}\"},
                      {script: \"store-locator-on-ca-desktop-${ENV}\"},
                      {script: \"store-locator-on-ca-mobile-${ENV}\"},
                      {script: \"utility-at-us-desktop-${ENV}\"},
                      {script: \"utility-at-us-mobile-${ENV}\"},
                      {script: \"utility-at-ca-desktop-${ENV}\"},
                      {script: \"utility-at-ca-mobile-${ENV}\"},
                      {script: \"utility-br-us-desktop-${ENV}\"},
                      {script: \"utility-br-us-mobile-${ENV}\"},
                      {script: \"utility-br-ca-desktop-${ENV}\"},
                      {script: \"utility-br-ca-mobile-${ENV}\"},
                      {script: \"utility-brf-ca-desktop-${ENV}\"},
                      {script: \"utility-brf-ca-mobile-${ENV}\"},
                      {script: \"utility-gp-us-desktop-${ENV}\"},
                      {script: \"utility-gp-us-mobile-${ENV}\"},
                      {script: \"utility-gp-ca-desktop-${ENV}\"},
                      {script: \"utility-gp-ca-mobile-${ENV}\"},
                      {script: \"utility-gpf-us-desktop-${ENV}\"},
                      {script: \"utility-gpf-us-mobile-${ENV}\"},
                      {script: \"utility-on-us-desktop-${ENV}\"},
                      {script: \"utility-on-us-mobile-${ENV}\"},
                      {script: \"utility-on-ca-desktop-${ENV}\"},
                      {script: \"utility-on-ca-mobile-${ENV}\"}"${{ inputs.override-jobs && format(' | grep -E ''{0}''', inputs.override-jobs) || ' | grep -v -E ''accessibility''' }} \
                      ${{ inputs.environment == 'wip-stage' && ' | grep -E ''category|product|search''  | grep -v -E ''brf-ca''' || '' }} >> $GITHUB_OUTPUT
                  echo ']' >> $GITHUB_OUTPUT
                  echo 'EOF' >> $GITHUB_OUTPUT
                  ;;
          esac
    outputs:
      scripts: ${{ steps.scripts.outputs.scripts }}

  cache-server-dependencies:
    name: Cache Server Dependencies
    runs-on: self-hosted
    if: ${{ inputs.environment == 'local' || (github.event.workflow == '.github/workflows/ujt-run-all-aks-test.yaml' && github.event_name == 'schedule') }}
    container:
      image: gacr2pprod.azurecr.io/gap/pt-webapps/playwright:373afa5
      credentials:
        username: ${{ secrets.g_spn_0p_platform01_acr_readonly_01_username }}
        password: ${{ secrets.g_spn_0p_platform01_acr_readonly_01_password }}
      env:
        ARTIFACTORY_USR: ${{ secrets.ONLINEP_ARTIFACTORY_USERNAME }}
        ARTIFACTORY_PSW: ${{ secrets.ONLINEP_ARTIFACTORY_PASSWORD }}
        ARTIFACTORY_BASE_URL: 'https://gapinc.jfrog.io/gapinc'
      options: >-
        --init --ipc=host --cap-add=SYS_ADMIN
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ (github.event_name == 'pull_request' || startsWith(github.event.workflow, '.github/workflows/ujt')) && github.ref || 'main' }}
          repo: ecomfrontend/ecom-next
          fetch-depth: 0

      - name: Get npm cache directory
        id: npm-cache-dir
        shell: bash
        run: |
          echo "ecom-dir=$(pwd)/node_modules" >> ${GITHUB_OUTPUT}
          echo "arch=$(echo ${{ runner.arch }} | tr '[:upper:]' '[:lower:]')" >> ${GITHUB_OUTPUT}

      - uses: actions/cache@v4
        id: npm-cache-ecom-app
        with:
          path: ${{ steps.npm-cache-dir.outputs.ecom-dir }}
          key: node-cache-${{ runner.os }}-${{ steps.npm-cache-dir.outputs.arch }}-npm-${{ hashFiles('package-lock.json') }}

      - name: Install App dependencies
        if: ${{ steps.npm-cache-ecom-app.outputs.cache-hit != 'true' }}
        run: |
          user-journeys/scripts/generate_npmrc.sh
          npm i --no-audit

  run-playwright:
    name: ${{ matrix.job.script }}${{ (github.event_name != 'schedule' && matrix.job.total) && format('-shard-{0}/{1}', matrix.job.shard, matrix.job.total) || '' }}
    runs-on: self-hosted
    needs: ['ujt-scripts-prep', 'cache-server-dependencies']
    if: >-
      ${{ !cancelled() && (inputs.environment != 'local' || needs.cache-server-dependencies.result == 'success')
        && !(github.event.workflow == '.github/workflows/ujt-run-all-aks-test.yaml' && github.event_name == 'schedule')
      }}
    timeout-minutes: ${{ inputs.lt == 'true' && 60 || 30 }}
    outputs:
      build-failure: ${{ steps.build.outcome == 'failure' && 'true' || 'false' }}
    env:
      ALLURE_REPORT_PATH: 'user-journeys/allure-results'
    strategy:
      fail-fast: false
      max-parallel: ${{ inputs.lt && 5 || 0}}
      matrix:
        job: ${{ inputs.bypass-ujt-scripts-prep && fromJson(inputs.bypass-ujt-scripts-prep) || fromJson(needs.ujt-scripts-prep.outputs.scripts) }}
    container:
      image: gacr2pprod.azurecr.io/gap/pt-webapps/playwright:373afa5
      credentials:
        username: ${{ secrets.g_spn_0p_platform01_acr_readonly_01_username }}
        password: ${{ secrets.g_spn_0p_platform01_acr_readonly_01_password }}
      env:
        ARTIFACTORY_USR: ${{ secrets.ONLINEP_ARTIFACTORY_USERNAME }}
        ARTIFACTORY_PSW: ${{ secrets.ONLINEP_ARTIFACTORY_PASSWORD }}
        ARTIFACTORY_BASE_URL: 'https://gapinc.jfrog.io/gapinc'
        CANARY: ${{ !startsWith(github.event.workflow, '.github/workflows/ujt') && 'true' || 'false' }}
        TEST_ACCOUNT_PW: ${{ secrets.TEST_ACCOUNT_PW }}
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        LT_USERNAME: ${{ secrets.LT_USERNAME }}
        LT_ACCESS_KEY: ${{ secrets.LT_ACCESS_KEY }}
        PAYPAL_TEST_PASSWORD: ${{ secrets.PAYPAL_TEST_PASSWORD }}
        PREVIEW_DATE: ${{ inputs.preview-date }}
        FEATURE_FLAGS: ${{ inputs.feature-flags }}
        SEGMENTS: ${{ inputs.segments }}
        CHROME: 'true'
        SAFARI: 'false'
      options: >-
        ${{ inputs.environment == 'local' && '--add-host atol.stage.gaptechol.com:127.0.0.1 --add-host secure-atol.stage.gaptechol.com:127.0.0.1' || '' }}
        --init --ipc=host --cap-add=SYS_ADMIN

    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ (github.event_name == 'pull_request' || startsWith(github.event.workflow, '.github/workflows/ujt')) && github.ref || 'main' }}
          repo: ecomfrontend/ecom-next
          fetch-depth: 0

      - name: Generate npmrc
        run: user-journeys/scripts/generate_npmrc.sh

      - name: Get npm cache directory
        id: npm-cache-dir
        shell: bash
        run: |
          echo "ecom-dir=$(pwd)/node_modules" >> ${GITHUB_OUTPUT}
          cd user-journeys
          echo "ujt-dir=$(pwd)/node_modules" >> ${GITHUB_OUTPUT}
          echo "arch=$(echo ${{ runner.arch }} | tr '[:upper:]' '[:lower:]')" >> ${GITHUB_OUTPUT}

      - uses: actions/cache@v4
        if: ${{ inputs.environment == 'local' }}
        id: npm-cache-ecom-app
        with:
          path: ${{ steps.npm-cache-dir.outputs.ecom-dir }}
          key: node-cache-${{ runner.os }}-${{ steps.npm-cache-dir.outputs.arch }}-npm-${{ hashFiles('package-lock.json') }}

      - name: Build App
        id: build
        if: ${{ inputs.environment == 'local' }}
        run: |
          mv user-journeys/.env.local-config packages/ecom-next/.env.local
          npm run build

      - name: Start Nginx
        if: ${{ inputs.environment == 'local' }}
        run: /usr/sbin/nginx -g 'daemon on; master_process on;' &

      - uses: actions/cache@v4
        id: npm-cache-ujt
        with:
          path: ${{ steps.npm-cache-dir.outputs.ujt-dir }}
          key: ${{ runner.os }}-node-${{ hashFiles('user-journeys/package-lock.json') }}

      - name: Install dependencies
        if: steps.npm-cache-ujt.outputs.cache-hit != 'true'
        run: |
          cd user-journeys
          npm i --no-audit --fetch-timeout=120000

      - name: Run tests
        run: |
          cd user-journeys
          sleep 3
          npm run ${{ matrix.job.script }} ${{ matrix.job.total && format('-- --shard={0}/{1}', matrix.job.shard, matrix.job.total) || '' }}

      - name: Convert report PNGs to JPEGs
        if: ${{ !cancelled() && (steps.build.outcome == 'success' || inputs.environment != 'local') }}
        uses: onlineplatform/convert-png-to-jpeg-action@v1
        with:
          png-directory: user-journeys/allure-results
      - name: Fix JSON references to PNGs and remove excessive route logging
        if: ${{ !cancelled() && (steps.build.outcome == 'success' || inputs.environment != 'local') }}
        run: |
          find ${{ env.ALLURE_REPORT_PATH }} -type f -name '*.json*' -exec node user-journeys/scripts/strip-unwanted-reporting.js {} \;
          find ${{ env.ALLURE_REPORT_PATH }} -type f -name '*.json*' -exec sed -i 's/image\/png/image\/jpeg/g' {} \;
          find ${{ env.ALLURE_REPORT_PATH }} -type f -name '*.json*' -exec sed -i 's/attachment\.png/attachment\.jpeg/g' {} \;

      - name: Upload test report
        if: ${{ !cancelled() && (steps.build.outcome == 'success' || inputs.environment != 'local') }}
        uses: actions/upload-artifact@v3
        with:
          name: allure-results-${{ github.run_attempt}}
          path: |
            user-journeys/allure-results
            !user-journeys/allure-results/*.png
            !user-journeys/allure-results/*.zip
          retention-days: 1
          if-no-files-found: ignore

      - name: Upload test traces
        if: ${{ !cancelled() && (steps.build.outcome == 'success' || inputs.environment != 'local') }}
        uses: actions/upload-artifact@v3
        with:
          name: traces-${{ github.run_attempt}}
          path: |
            user-journeys/test-results
            !user-journeys/test-results/**/*.png
            !user-journeys/test-results/*.zip
          retention-days: 1
          if-no-files-found: ignore

  build-report:
    name: Build Allure Report
    needs: ['run-playwright', 'cache-server-dependencies']
    runs-on: self-hosted
    if: ${{ !cancelled() && (inputs.environment != 'local' || needs.cache-server-dependencies.result == 'success') && !(github.event.workflow == '.github/workflows/ujt-run-all-aks-test.yaml' && github.event_name == 'schedule') }}
    timeout-minutes: 10
    continue-on-error: true
    env:
      REPORT_CATEGORY: ${{ inputs.environment }}${{ !startsWith(github.event.workflow, '.github/workflows/ujt') && '-canary' || '' }}${{ (inputs.environment == 'local') && format('-{0}', (github.head_ref || github.ref_name || github.ref)) || '' }}${{ inputs.override-jobs && '-override' || '' }}${{ inputs.preview-date && '-future' || '' }}_User-Journey-Tests
    outputs:
      uploaded-url: ${{ steps.publish-allure.outputs.uploaded-url }}
    steps:
      - name: Download report contents from GitHub Actions Artifacts
        uses: actions/download-artifact@v3
        continue-on-error: true
        with:
          name: allure-results-${{ github.run_attempt}}
          path: allure-results

      - name: Count total assertions in UJT report
        id: assertion-count
        run: |
          echo "assertion-count=$(find $1 -name "*result.json*" -exec jq ".. | objects | .name? // empty" {} \; | grep -e '^"expect.' | wc -l)" >> $GITHUB_OUTPUT

      - uses: actions/setup-node@v4
        continue-on-error: true
        with:
          node-version: 20

      - name: Setup Java for Report Build
        uses: actions/setup-java@v4
        with:
          distribution: 'oracle'
          java-version: '17'

      - name: Record Executor Details
        run: |
          echo '{' > allure-results/executor.json
          echo '    "name":"GitHub Actions",' >> allure-results/executor.json
          echo '    "type": "github",' >> allure-results/executor.json
          echo '    "url": "${{ github.server_url }}/${{ github.repository }}/actions",'  >> allure-results/executor.json
          echo '    "buildOrder": "${{ github.run_id }}",' >> allure-results/executor.json
          echo '    "buildName": "Build: ${{ github.run_id }} Attempt: ${{ github.run_attempt }}",'  >> allure-results/executor.json
          echo '    "buildUrl": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id}}"' >> allure-results/executor.json
          echo '}' >> allure-results/executor.json

      - name: Install Allure Report Tools
        continue-on-error: true
        run: npm i -g allure-playwright allure-commandline --no-audit --fetch-timeout=120000

      - name: Download Report History
        continue-on-error: true
        run: |
          mkdir -p allure-results/history
          cd allure-results/history
          HISTORY_URL=$(curl --request GET \
            --url https://report-storage-bridge.aks.stage.azeus.gaptech.com/get-history \
            --header 'report-category: ${{ env.REPORT_CATEGORY }}')
          if [[ "$HISTORY_URL" == "No reports found." ]]; then
            echo "No reports found, exiting normally."
            exit 0  # Exit with success status code
          fi
          echo "Fetching history from URL $HISTORY_URL"
          declare -a files=("categories-trend" "duration-trend" "history-trend" "history" "retry-trend")
          for file in "${files[@]}"
          do
            curl --request GET --url ${HISTORY_URL}${file}.json -O
          done

      - name: Generate Allure Report
        run: npx allure generate allure-results allure-report --name "${{ env.REPORT_CATEGORY }}"

      - name: Upload Allure Report
        id: publish-allure
        uses: onlineplatform/upload-reports@v1
        continue-on-error: true
        with:
          report-path: allure-report/
          desired-url-path: ${{ env.REPORT_CATEGORY }}/${{ github.run_id }}-${{ github.run_attempt }}/

      - name: Post Report URL
        run: |
          echo "### UJT Report URL ${{ steps.publish-allure.outputs.uploaded-url }}" >> $GITHUB_STEP_SUMMARY
          echo "Assertion count: ${{ steps.assertion-count.outputs.assertion-count }}" >> $GITHUB_STEP_SUMMARY

      - name: Delete Reporting Artifacts
        uses: GeekyEggo/delete-artifact@54ab544f12cdb7b71613a16a2b5a37a9ade990af
        with:
          name: allure-results-${{ github.run_attempt}}

  launch-observepoint:
    name: Launch ObservePoint Audits and Journeys
    needs: ['run-playwright']
    if: ${{ (!startsWith(github.event.workflow, '.github/workflows/ujt') && inputs.environment == 'prod' && !inputs.bypass-observepoint-script) }}
    uses: ecomfrontend/ecom-next/.github/workflows/observepoint-audits-and-journeys.yaml@main
    secrets: inherit

  check-failures:
    name: Create PR Comment if Needed
    runs-on: self-hosted
    needs: [run-playwright, build-report]
    if: failure()
    steps:
      - name: Create PR comment
        if: ${{ inputs.environment == 'local' && github.event_name == 'pull_request' && needs.run-playwright.result == 'failure' }}
        uses: actions/github-script@v6
        with:
          github-token: ${{ secrets.ONLINEP_TOKEN }}
          script: |
            const issue_number = context.payload.pull_request.number;
            const runId = context.runId;
            const runUrl = `https://github.gapinc.com/${context.repo.owner}/${context.repo.repo}/actions/runs/${runId}`;

            let comment = `### There are user journey tests that have failed which is an indication that some functionality related to your PR is broken.\n\n`;
            comment += `Please start by reviewing the Allure report linked [here](${{ needs.build-report.outputs.uploaded-url }}). See [this]`;
            comment += `(https://gapinc.atlassian.net/wiki/spaces/ECOMNEXT/pages/1644824061/UJTs+-+Reading+the+Allure+Report) document on how to interpret the report.\n\n`;
            comment += `To resolve the issues, please try the following.\n\n`;
            comment += `1. Try to rebase on main. If the issue is already resolved and not caused by your branch, this will fix the issue.\n`;
            comment += `2. View the traces files to get more information about why your test failed (See [documentation]`;
            comment += `(https://gapinc.atlassian.net/wiki/spaces/ECOMNEXT/pages/1544588490/UJTs+-+Debugging+with+Opening+and+Viewing+a+Trace+File)). `;
            comment += `They can be found linked at the bottom of the workflow run [here](${runUrl}).\n`;
            comment += `3. To get a better understanding of the issue, try to reproduce the steps manually on the failing branch locally.\n\n`;
            comment += `Server logs are not accessible from the Playwright tests in CI. If they are needed, reproduce the issue locally and check the server output from `;
            comment += `your own machine.\n\n`;
            comment += `If you need to rerun the tests to see if you have resolved your issue, push the changes and it will run automatically.`

            if (${{ needs.run-playwright.outputs.build-failure }}) {
              comment = `## Build Failure\n\n`;
              comment += `> [!CAUTION]\n`
              comment += `> The user journeys did NOT run as the server has failed to build. Please address the errors in the build before attempting to run pull reqeust checks again.\n\n`;
              comment += `Please review the build logs [here](${runUrl}).`;
            }

            await github.rest.issues.createComment({
              ...context.repo,
              issue_number,
              body: comment
            });
