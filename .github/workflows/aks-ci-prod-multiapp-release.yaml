name: Deploy Multi App Prod On Latest Release Creation
permissions:
  id-token: write
  contents: write
on:
  release:
    types: [released] #deploy to prod happens on a release github event
concurrency:
  group: prod_deploy
jobs:
  vars: # Job to set variables
    runs-on: ['self-hosted', arc-dind-rootless-enterprise]
    environment: prod
    steps:
      - name: Setting Variables
        run: echo "Setting Variable"
    outputs:
      deploy-file: 'prod.yaml'
      team: 'pt-webapps'
      image-tag: ${{ github.event.release.tag_name }}
      prod-router-file: 'chartis/router/prod-dummy-router.yaml'
  chartis-deploy-prod-dummy-router:
    needs:
      - vars
    uses: continuous-delivery-platform/chartis-deploy/.github/workflows/chartis-deploy.yml@v1
    with:
      chartis-file: ${{ needs.vars.outputs.prod-router-file }}
      branch: main
      create-pre-approved-change: true
      # planned-change-request-number: CHG0037553
      # incident-number: INC
  chartis-deploy-canary-0:
    needs:
      - vars
    uses: ecomfrontend/ecom-next/.github/workflows/aks-canary-mutiapp-reset-deploy-validate.yaml@main
    with:
      deploy-env: prod
      image-tag: ${{ needs.vars.outputs.image-tag }}
      chartis-file: ${{ needs.vars.outputs.deploy-file }}
      use-canary: true
      promote-canary: false
      change-required: true
      team: ${{ needs.vars.outputs.team }}
      environment: prod
    secrets: inherit
  run-ujt-tests:
    needs:
      - chartis-deploy-canary-0
      - chartis-deploy-prod-dummy-router
    uses: ecomfrontend/ecom-next/.github/workflows/ujt-run-all-aks-test.yaml@main
    with:
      environment: prod
    secrets: inherit
  newrelic:
    runs-on: [self-hosted]
    needs:
      - run-ujt-tests
      - vars
    name: New Relic
    strategy:
      fail-fast: false
      matrix:
        app-guid: ${{ fromJSON(vars.PROD_APP_GUIDS) }}
    steps:
      - name: Update New Relic Deployment Marker
        uses: newrelic/deployment-marker-action@v2.2.0
        with:
          apiKey: ${{ secrets.NEW_RELIC_API_KEY }}
          region: 'US'
          guid: '${{ matrix.app-guid }}'
          version: '${{ needs.vars.outputs.image-tag }}'
          commit: '${{ needs.vars.outputs.image-tag }}'
          description: 'Release ${{ needs.vars.outputs.image-tag }}'
          deploymenttype: 'CANARY'
          groupId: 'App Release: ${{ needs.vars.outputs.image-tag }}'
          user: '${{ github.actor }}'
  report-slack-status:
    needs:
      - run-ujt-tests
      - vars
    if: always()
    runs-on: [self-hosted, arc-dind-rootless-enterprise]
    steps:
      - name: Publish update to Slack
        uses: slackapi/slack-github-action@v1.24.0
        with:
          channel-id: C06D20TGNSH
          payload: |
            {
              "attachments": [
                {
                  "color": "${{ needs.run-ujt-tests.result == 'success' && '#00FF00' || '#FF0000' }}",
                  "blocks": [
                    {
                      "type": "header",
                      "text": {
                        "type": "plain_text",
                        "text": "Deploy Prod Status - ${{ github.event.repository.name }} "
                      }
                    },
                    {
                      "type": "divider"
                    },
                    {
                      "type": "section",
                      "fields": [
                        {
                          "type": "mrkdwn",
                          "text": "*Activity:*\nDeploying ${{ github.event.repository.name }} with commit: <${{ github.server_url }}/${{ github.repository }}/releases/tag/${{ needs.vars.outputs.image-tag }}|${{ needs.vars.outputs.image-tag }}>"
                        },
                        {
                          "type": "mrkdwn",
                          "text": "*Status:*\n${{ needs.run-ujt-tests.result }} ${{ needs.run-ujt-tests.result == 'success' && ':white_check_mark:' || ':x:' }} (view latest <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}| run>)"
                        },
                        {
                          "type": "mrkdwn",
                          "text": "*User:*\n${{ github.actor }}"
                        },
                        {
                          "type": "mrkdwn",
                          "text": "*Version:*\n${{ needs.vars.outputs.image-tag }}"
                        },
                        {
                          "type": "mrkdwn",
                          "text": "*Canary Percent:*\n0"
                        },
                        {
                          "type": "mrkdwn",
                          "text": "*Promoted:*\nNo"
                        }
                      ]
                    }
                  ]
                }
              ]
            }
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
