name: Deploy Preview On PreRelease for Multi App
permissions:
  id-token: write
  contents: write
on:
  release:
    types: [prereleased]
jobs:
  vars: # Job to set variables
    runs-on: ['self-hosted', arc-dind-rootless-enterprise]
    environment: preview
    steps:
      - name: Setting Variables
        run: echo "Setting Variable"
    outputs:
      deploy-file: 'preview.yaml'
      team: 'pt-webapps'
      image-tag: ${{ github.event.release.tag_name }}
      preview-router-file: 'chartis/router/preview-dummy-router.yaml'
  docker-promote-image:
    needs:
      - vars #remote workflow that pushes defined image from nonprod container registry to prod container registry.
    uses: continuous-delivery-platform/image-management/.github/workflows/image-promote.yml@v1
    with:
      team: ${{ needs.vars.outputs.team }}
      image-name: ecom-next
      image-tag: ${{ needs.vars.outputs.image-tag }}
      ignore-existing-image-error: true
  chartis-deploy-preview-dummy-router:
    needs:
      - vars
    uses: continuous-delivery-platform/chartis-deploy/.github/workflows/chartis-deploy.yml@v1
    with:
      chartis-file: ${{ needs.vars.outputs.preview-router-file }}
      branch: main
      create-pre-approved-change: true
      # planned-change-request-number: CHG0037553
      # incident-number: INC
  chartis-deploy-canary-0:
    concurrency:
      group: prod_deploy
    needs:
      - vars
      - docker-promote-image
    uses: ecomfrontend/ecom-next/.github/workflows/aks-canary-mutiapp-reset-deploy-validate.yaml@main
    with:
      deploy-env: prod
      image-tag: ${{ needs.vars.outputs.image-tag }}
      chartis-file: preview.yaml
      use-canary: true
      promote-canary: false
      change-required: true
      team: ${{ needs.vars.outputs.team }}
      environment: preview
    secrets: inherit
  run-ujt-tests:
    needs:
      - chartis-deploy-canary-0
      - chartis-deploy-preview-dummy-router
    uses: ecomfrontend/ecom-next/.github/workflows/ujt-run-all-aks-test.yaml@main
    with:
      environment: preview
    secrets: inherit
  chartis-deploy-preview:
    needs:
      - run-ujt-tests
      - vars
    uses: ecomfrontend/ecom-next/.github/workflows/aks-chartis-multiapp-update-100-promote.yaml@main
    with:
      environment: preview
      image-tag: ${{ needs.vars.outputs.image-tag }}
      request-host: www.wip.prod.gaptecholapps.com
    secrets: inherit
  newrelic:
    runs-on: [self-hosted]
    needs:
      - chartis-deploy-preview
      - vars
    name: New Relic
    strategy:
      fail-fast: false
      matrix:
        app-guid: ${{ fromJSON(vars.PREVIEW_APP_GUIDS) }}
    steps:
      - name: Update New Relic Deployment Marker
        uses: newrelic/deployment-marker-action@v2.2.0
        with:
          apiKey: ${{ secrets.NEW_RELIC_API_KEY }}
          region: 'US'
          guid: '${{ matrix.app-guid }}'
          version: '${{ needs.vars.outputs.image-tag }}'
          commit: '${{ needs.vars.outputs.image-tag }}'
          description: 'Release ${{ needs.vars.outputs.image-tag }}'
          deploymenttype: 'CANARY'
          groupId: 'App Release: ${{ needs.vars.outputs.image-tag }}'
          user: '${{ github.actor }}'
  slack-notification:
    needs:
      - chartis-deploy-preview
      - vars
    if: always()
    runs-on: [self-hosted, arc-dind-rootless-enterprise]
    steps:
      - name: Publish update to Slack
        if: ${{ needs.chartis-deploy-preview.result != 'success' }}
        uses: slackapi/slack-github-action@v1.24.0
        with:
          channel-id: C06D4JXG38S
          payload: |
            {
              "attachments": [
                {
                  "color": "${{ needs.chartis-deploy-preview.result == 'success' && '#00FF00' || '#FF0000' }}",
                  "blocks": [
                    {
                      "type": "header",
                      "text": {
                        "type": "plain_text",
                        "text": "Deploy Preview Status - ${{ github.event.repository.name }} "
                      }
                    },
                    {
                      "type": "divider"
                    },
                    {
                      "type": "section",
                      "fields": [
                        {
                          "type": "mrkdwn",
                          "text": "*Activity:*\nDeploying ${{ github.event.repository.name }} with commit: <${{ github.server_url }}/${{ github.repository }}/releases/tag/${{ needs.vars.outputs.image-tag }}|${{ needs.vars.outputs.image-tag }}>"
                        },
                        {
                          "type": "mrkdwn",
                          "text": "*Status:*\n${{ needs.chartis-deploy-preview.result }} ${{ needs.chartis-deploy-preview.result == 'success' && ':white_check_mark:' || ':x:' }} (view latest <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}| run>)"
                        },
                        {
                          "type": "mrkdwn",
                          "text": "*User:*\n${{ github.actor }}"
                        },
                        {
                          "type": "mrkdwn",
                          "text": "*Version:*\n${{ needs.vars.outputs.image-tag }}"
                        },
                        {
                          "type": "mrkdwn",
                          "text": "*Canary Percent:*\n0"
                        },
                        {
                          "type": "mrkdwn",
                          "text": "*Promoted:*\nNo"
                        }
                      ]
                    }
                  ]
                }
              ]
            }
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
