version: 1-stable
appName: cms-next-storybook
pcfAppName: none
team: pt-webapps
environment: prod
affectedCI: 'Marketing Component Service'
persistence:
  ephemeral:
    writableDiskPaths:
      - /var/www/.npm
appSpec:
  appType: nodejs
environmentVariables:
  SERVER_PORT: '3000'
  TARGET_ENV: 'stage'
kubernetes:
  annotations:
    sidecar.istio.io/proxyMemory: 128Mi
    sidecar.istio.io/proxyMemoryLimit: 1Gi
    sidecar.istio.io/proxyCPU: 100m
    sidecar.istio.io/proxyCPULimit: 500m
  dockerImage: cms-next-storybook
  dockerImageTag: "86d26f1a54"
  port: '3000'
  health:
    livenessProbe:
      endpoint: /
      port: '3000'
      initialDelay: '5'
      timeout: '5'
      period: '10'
      failureThreshold: '5'
    readinessProbe:
      endpoint: /
      port: '3000'
      initialDelay: '5'
      timeout: '5'
      period: '10'
      failureThreshold: '5'
  autoscaling:
    minReplicas: '1'
    maxReplicas: '2'
    memoryThreshold: '70'
    cpuThreshold: '70'
  resources:
    requestsCpu: 100m
    requestsMemory: 512Mi
    limitsCpu: 1
    limitsMemory: 512Mi
