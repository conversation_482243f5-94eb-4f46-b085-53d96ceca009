version: 1-stable
appName: router
pcfAppName: none
team: pt-webapps
environment: stage
httpRouterOnly: true
affectedCI: sitewide-app
control:
  forceDeploy: true
externalServices:
  - host: internal-azeus-ecom-api.preview.app.prod.gaptecholapps.com
  - host: api.azeus.gaptech.com
  - host: collector.newrelic.com
  - host: internal-azeus.brol.preview.app.prod.gaptecholapps.com
  - host: browse-api-nginx-cache-preview.aks.prod.azeus.gaptech.com
  - host: stage.api.azeus.gaptech.com
  - host: cdn.optimizely.com
  - host: secure-internal-azeus-ecom-api.live.stage.gaptechol.com
  - host: azeus-brol.live.stage.gaptechol.com
  - host: internal-azeus-ecom-api.live.stage.gaptechol.com
  - host: browse-api-nginx-cache.aks.stage.azeus.gaptech.com
  - host: readservices-b2c.powerreviews.com
  - host: ws-catalog-api-service.stage.azeus.gaptech.com
  - host: ws-catalog-api-service.prod.azeus.gaptech.com
  - host: endpoint.dlp-webservices.prod.dlp.adeptmind.net
  - host: catalog-apis-omni-product-service.aks.stage.azeus.gaptech.com
  - host: catalog-apis-omni-product-service.aks.prod.azeus.gaptech.com
  - host: catalog-apis-omni-category-service.aks.stage.azeus.gaptech.com
  - host: catalog-apis-omni-category-service.aks.prod.azeus.gaptech.com
  - host: pmcs-preview.aks.prod.azeus.gaptech.com
  - host: pmcs.aks.stage.azeus.gaptech.com
  - host: catalog-apis-lookup-service.aks.prod.azeus.gaptech.com
  - host: catalog-apis-lookup-service.aks.stage.azeus.gaptech.com
httpRouting:
  - route:
      - destination:
          host: sitewide-next
