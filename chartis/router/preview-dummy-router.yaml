version: 1-stable
appName: router-preview
pcfAppName: none
team: pt-webapps
environment: prod
httpRouterOnly: true
affectedCI: 'Ecom New Shopping Bag UI'
externalServices:
  - host: internal-azeus-ecom-api.preview.app.prod.gaptecholapps.com
  - host: api.azeus.gaptech.com
  - host: collector.newrelic.com
  - host: internal-azeus.brol.preview.app.prod.gaptecholapps.com
  - host: cdn.optimizely.com
  - host: browse-api-nginx-cache-preview.aks.prod.azeus.gaptech.com
  - host: readservices-b2c.powerreviews.com
  - host: ws-catalog-api-service.prod.azeus.gaptech.com
  - host: endpoint.dlp-webservices.prod.dlp.adeptmind.net
  - host: catalog-apis-omni-product-service.aks.prod.azeus.gaptech.com
  - host: catalog-apis-omni-category-service.aks.prod.azeus.gaptech.com
  - host: pmcs-preview.aks.prod.azeus.gaptech.com
  - host: catalog-apis-lookup-service.aks.prod.azeus.gaptech.com
httpRouting:
  - route:
      - destination:
          host: sitewide-next-preview
