version: 1-stable
appName: category-next
pcfAppName: none
team: pt-webapps
environment: test
affectedCI: 'Category Page'
appSpec:
  appType: nodejs
  observability:
    metrics:
      newRelic:
        enabled: true
environmentVariables:
  NEW_RELIC_APP_NAME: category-next-dev
  NEW_RELIC_DISTRIBUTED_TRACING_ENABLED: true
  REDIS_READ_TIMEOUT: 100
  SERVER_PORT: '3000'
  TARGET_ENV: 'test'
  COOKIE_DOMAIN: 'gaptech.com'
  PMCS_SERVICE_URL: 'https://pmcs.aks.stage.azeus.gaptech.com'
  NAVIGATION_SERVICE_URL: 'https://browse-api-nginx-cache.aks.stage.azeus.gaptech.com'
  ECOM_CLIENT_API_BASE_URL_US: 'https://internal-azeus-ecom-api.live.test.gaptechol.com'
  ECOM_CLIENT_API_BASE_URL_CA: 'https://internal-azeus-ecom-api.live.test.gaptechol.ca'
  ECOM_CLIENT_API_BASE_URL_FACTORY_US: 'https://internal-azeus-ecom-api.live.test.factory-gaptechol.com'
  ECOM_CLIENT_API_BASE_URL_FACTORY_CA: 'https://internal-azeus-ecom-api.live.test.factory-gaptechol.ca'
  ECOM_SERVER_API_BASE_URL: 'https://internal-azeus-ecom-api.live.test.gaptechol.com'
  CATALOG_OMNI_PRODUCT_SERVICE_BASE_URL: 'https://catalog-apis-omni-product-service.aks.stage.azeus.gaptech.com'
  CATALOG_OMNI_CATEGORY_SERVICE_BASE_URL: 'https://catalog-apis-omni-category-service.aks.stage.azeus.gaptech.com'
  SECURE_ECOM_CLIENT_API_BASE_URL_US: 'https://secure-internal-azeus-ecom-api.live.test.gaptechol.com'
  SECURE_ECOM_CLIENT_API_BASE_URL_CA: 'https://secure-internal-azeus-ecom-api.live.test.gaptechol.ca'
  SECURE_ECOM_CLIENT_API_BASE_URL_FACTORY_US: 'https://secure-internal-azeus-ecom-api.live.test.factory-gaptechol.com'
  SECURE_ECOM_CLIENT_API_BASE_URL_FACTORY_CA: 'https://secure-internal-azeus-ecom-api.live.test.factory-gaptechol.ca'
  APIGEE_INTERNAL_URL: 'https://stage.api.azeus.gaptech.com'
  SERVER_OPTIMIZELY_CONFIG_URL: 'https://cdn.optimizely.com/public/6529002483/s/10738892798_10738892798.json'
  SHOPPING_BAG_SERVICE_URL: 'https://internal-azeus-ecom-api.preview.wip.stage.gaptecholapps.com/commerce/shopping-bags'
  NEW_EMAIL_REGISTRATION_SERVICE_URL: 'https://test.api.azeus.gaptech.com/commerce/communication-preference/v2/subscriptions/email'
  NEW_SMS_REGISTRATION_SERVICE_URL: 'https://test.api.azeus.gaptech.com/commerce/communication-preference/v2/subscriptions/sms'
  ASSET_CLUSTER_URL: 'https://azeus-brol.live.stage.gaptechol.com'
  NEXT_TELEMETRY_DISABLED: 1
  NEXT_OTEL_FETCH_DISABLED: 1
  NODE_OPTIONS: '-r newrelic --use-openssl-ca'
  BLOOMREACH_URL: 'https://staging-core.dxpapi.com'
  CATALOG_INTERNAL_API_BASE_URL: https://ws-catalog-api-service.stage.azeus.gaptech.com
  CATALOG_STYLE_LOOKUP_API_BASE_URL: https://catalog-apis-lookup-service.aks.stage.azeus.gaptech.com
  LOG_LEVEL: 'warn'
secrets:
  secretKeys:
    - key: azure-blob-key
      environmentVariableKey: AZURE_BLOB_KEY
    - key: azure-redis-key
      environmentVariableKey: AZURE_REDIS_KEY
    - key: new-relic-license-key
      environmentVariableKey: NEW_RELIC_LICENSE_KEY
    - key: nav-service-api-key
      environmentVariableKey: NAV_SERVICE_API_KEY
    - key: power-reviews-config
      environmentVariableKey: POWER_REVIEWS_CONFIG
    - key: social-gallery-config
      environmentVariableKey: SOCIAL_GALLERY_CONFIG
    - key: redirect-service-api-key
      environmentVariableKey: REDIRECT_SERVICE_API_KEY
    - key: private-fp-key
      environmentVariableKey: PRIVATE_FP_KEY
kubernetes:
  annotations:
    sidecar.istio.io/proxyMemory: 128Mi
    sidecar.istio.io/proxyMemoryLimit: 1Gi
    sidecar.istio.io/proxyCPU: 100m
    sidecar.istio.io/proxyCPULimit: 500m
  dockerImage: ecom-next
  dockerImageTag: "2b8d63254e"
  port: '3000'
  health:
    livenessProbe:
      endpoint: /healthz
      port: '3000'
      initialDelay: '5'
      timeout: '5'
      period: '10'
      failureThreshold: '5'
    readinessProbe:
      endpoint: /healthz
      port: '3000'
      initialDelay: '5'
      timeout: '5'
      period: '10'
      failureThreshold: '5'
  autoscaling:
    minReplicas: '1'
    maxReplicas: '2'
    memoryThreshold: '70'
    cpuThreshold: '70'
  resources:
    requestsCpu: 10m
    requestsMemory: 2Gi
    limitsCpu: 2
    limitsMemory: 2Gi
