version: 1-stable
appName: category-next-preview
pcfAppName: none
team: pt-webapps
environment: prod
affectedCI: 'Category Page'
appSpec:
  appType: nodejs
  observability:
    metrics:
      newRelic:
        enabled: true
environmentVariables:
  NEW_RELIC_APP_NAME: category-next-preview
  NEW_RELIC_DISTRIBUTED_TRACING_ENABLED: true
  REDIS_READ_TIMEOUT: 100
  SERVER_PORT: '3000'
  TARGET_ENV: 'preview'
  PMCS_SERVICE_URL: 'https://pmcs-preview.aks.prod.azeus.gaptech.com'
  PMCS_EDITIONS_SERVICE_URL: 'https://pmcs-preview.aks.prod.azeus.gaptech.com/edition'
  NAVIGATION_SERVICE_URL: 'http://browse-api-nginx-cache-preview'
  ECOM_CLIENT_API_BASE_WIP_URL_US: 'https://internal-azeus-ecom-api.preview.wip.prod.gaptecholapps.com'
  ECOM_CLIENT_API_BASE_WIP_URL_CA: 'https://internal-azeus-ecom-api.preview.wip.prod.gaptecholapps.ca'
  ECOM_CLIENT_API_BASE_WIP_URL_FACTORY_US: 'https://internal-azeus-ecom-api.preview.wip.prod.factory-gaptecholapps.com'
  ECOM_CLIENT_API_BASE_WIP_URL_FACTORY_CA: 'https://internal-azeus-ecom-api.preview.wip.prod.factory-gaptecholapps.ca'
  ECOM_CLIENT_API_BASE_URL_US: 'https://api.gap.com'
  ECOM_CLIENT_API_BASE_URL_CA: 'https://api.gapcanada.ca'
  ECOM_CLIENT_API_BASE_URL_FACTORY_US: 'https://api.gapfactory.com'
  ECOM_CLIENT_API_BASE_URL_FACTORY_CA: 'https://api.gapfactory.ca'
  ECOM_CLIENT_API_BASE_APP_URL_US: 'https://internal-azeus-ecom-api.preview.app.prod.gaptecholapps.com'
  ECOM_CLIENT_API_BASE_APP_URL_CA: 'https://internal-azeus-ecom-api.preview.app.prod.gaptecholapps.ca'
  ECOM_CLIENT_API_BASE_APP_URL_FACTORY_US: 'https://internal-azeus-ecom-api.preview.app.prod.factory-gaptecholapps.com'
  ECOM_CLIENT_API_BASE_APP_URL_FACTORY_CA: 'https://internal-azeus-ecom-api.preview.app.prod.factory-gaptecholapps.ca'
  SECURE_ECOM_CLIENT_API_WIP_BASE_URL_US: 'https://secure-internal-azeus-ecom-api.preview.wip.prod.gaptecholapps.com'
  SECURE_ECOM_CLIENT_API_WIP_BASE_URL_CA: 'https://secure-internal-azeus-ecom-api.preview.wip.prod.gaptecholapps.ca'
  SECURE_ECOM_CLIENT_API_WIP_BASE_URL_FACTORY_US: 'https://secure-internal-azeus-ecom-api.preview.wip.prod.factory-gaptecholapps.com'
  SECURE_ECOM_CLIENT_API_WIP_BASE_URL_FACTORY_CA: 'https://secure-internal-azeus-ecom-api.preview.wip.prod.factory-gaptecholapps.ca'
  SECURE_ECOM_CLIENT_API_APP_BASE_URL_US: 'https://secure-internal-azeus-ecom-api.preview.app.prod.gaptecholapps.com'
  SECURE_ECOM_CLIENT_API_APP_BASE_URL_CA: 'https://secure-internal-azeus-ecom-api.preview.app.prod.gaptecholapps.ca'
  SECURE_ECOM_CLIENT_API_APP_BASE_URL_FACTORY_US: 'https://secure-internal-azeus-ecom-api.preview.app.prod.factory-gaptecholapps.com'
  SECURE_ECOM_CLIENT_API_APP_BASE_URL_FACTORY_CA: 'https://secure-internal-azeus-ecom-api.preview.app.prod.factory-gaptecholapps.ca'
  ECOM_SERVER_API_BASE_URL: 'https://internal-azeus-ecom-api.preview.app.prod.gaptecholapps.com'
  CATALOG_OMNI_PRODUCT_SERVICE_BASE_URL: 'https://catalog-apis-omni-product-service.aks.prod.azeus.gaptech.com'
  CATALOG_OMNI_CATEGORY_SERVICE_BASE_URL: 'https://catalog-apis-omni-category-service.aks.prod.azeus.gaptech.com'
  SECURE_ECOM_CLIENT_API_BASE_URL_US: 'https://secure-internal-azeus-ecom-api.preview.wip.prod.gaptecholapps.com'
  SECURE_ECOM_CLIENT_API_BASE_URL_CA: 'https://secure-internal-azeus-ecom-api.preview.wip.prod.gaptecholapps.ca'
  SECURE_ECOM_CLIENT_API_BASE_URL_FACTORY_US: 'https://secure-internal-azeus-ecom-api.preview.wip.prod.factory-gaptecholapps.com'
  SECURE_ECOM_CLIENT_API_BASE_URL_FACTORY_CA: 'https://secure-internal-azeus-ecom-api.preview.wip.prod.factory-gaptecholapps.ca'
  APIGEE_INTERNAL_URL: 'https://api.azeus.gaptech.com'
  SERVER_OPTIMIZELY_CONFIG_URL: 'https://cdn.optimizely.com/public/6529002483/s/10738892798_10738892798.json'
  SHOPPING_BAG_SERVICE_URL: 'https://internal-azeus-ecom-api.preview.wip.prod.gaptecholapps.com/commerce/shopping-bags'
  NEW_EMAIL_REGISTRATION_SERVICE_URL: 'https://api.azeus.gaptech.com/commerce/communication-preference/v2/subscriptions/email'
  NEW_SMS_REGISTRATION_SERVICE_URL: 'https://api.azeus.gaptech.com/commerce/communication-preference/v2/subscriptions/sms'
  ASSET_CLUSTER_URL: 'https://internal-azeus.brol.preview.app.prod.gaptecholapps.com'
  NEXT_TELEMETRY_DISABLED: 1
  NEXT_OTEL_FETCH_DISABLED: 1
  NODE_OPTIONS: '-r newrelic --use-openssl-ca'
  BLOOMREACH_URL: 'https://core.dxpapi.com'
  CATALOG_INTERNAL_API_BASE_URL: https://ws-catalog-api-service.prod.azeus.gaptech.com
  CATALOG_STYLE_LOOKUP_API_BASE_URL: https://catalog-apis-lookup-service.aks.prod.azeus.gaptech.com
  LOG_LEVEL: 'warn'
secrets:
  secretKeys:
    - key: azure-blob-key-preview
      environmentVariableKey: AZURE_BLOB_KEY
    - key: azure-redis-key-preview
      environmentVariableKey: AZURE_REDIS_KEY
    - key: new-relic-license-key
      environmentVariableKey: NEW_RELIC_LICENSE_KEY
    - key: nav-service-api-key
      environmentVariableKey: NAV_SERVICE_API_KEY
    - key: power-reviews-config
      environmentVariableKey: POWER_REVIEWS_CONFIG
    - key: social-gallery-config-preview
      environmentVariableKey: SOCIAL_GALLERY_CONFIG
    - key: redirect-service-api-key
      environmentVariableKey: REDIRECT_SERVICE_API_KEY
    - key: private-fp-key
      environmentVariableKey: PRIVATE_FP_KEY
kubernetes:
  annotations:
    sidecar.istio.io/proxyMemory: 128Mi
    sidecar.istio.io/proxyMemoryLimit: 1Gi
    sidecar.istio.io/proxyCPU: 100m
    sidecar.istio.io/proxyCPULimit: 500m
  dockerImage: ecom-next
  dockerImageTag: "86d26f1a54"
  port: '3000'
  deploymentStrategy:
    canary:
      weight: 0
      sticky: true
      promoteCanary: false
      # cookieCode: 'ui'
      # cookieExpiration: 8h
      # cookieDomainSegments:
      #   domainSegments:
      #     - requestDomain: 'www.wip.prod.gaptecholapps.com'
      #     - requestDomain: 'onol.wip.prod.gaptecholapps.com'
      #     - requestDomain: 'brol.wip.prod.gaptecholapps.com'
      #     - requestDomain: 'atol.wip.prod.gaptecholapps.com'
      #     - requestDomain: 'www.app.prod.gaptecholapps.com'
      #     - requestDomain: 'onol.app.prod.gaptecholapps.com'
      #     - requestDomain: 'brol.app.prod.gaptecholapps.com'
      #     - requestDomain: 'atol.app.prod.gaptecholapps.com'
      #     - requestDomain: 'www.wip.prod.gaptecholapps.ca'
      #     - requestDomain: 'onol.wip.prod.gaptecholapps.ca'
      #     - requestDomain: 'brol.wip.prod.gaptecholapps.ca'
      #     - requestDomain: 'atol.wip.prod.gaptecholapps.ca'
      #     - requestDomain: 'www.app.prod.gaptecholapps.ca'
      #     - requestDomain: 'onol.app.prod.gaptecholapps.ca'
      #     - requestDomain: 'brol.app.prod.gaptecholapps.ca'
      #     - requestDomain: 'atol.app.prod.gaptecholapps.ca'
      #     - requestDomain: 'www.wip.prod.factory-gaptecholapps.com'
      #     - requestDomain: 'brfol.wip.prod.factory-gaptecholapps.com'
      #     - requestDomain: 'www.app.prod.factory-gaptecholapps.com'
      #     - requestDomain: 'brfol.app.prod.factory-gaptecholapps.com'
      #     - requestDomain: 'brfol.wip.prod.factory-gaptecholapps.ca'
      #     - requestDomain: 'brfol.app.prod.factory-gaptecholapps.ca'
      #     - requestDomain: 'secure.www.wip.prod.gaptecholapps.com'
      #     - requestDomain: 'secure.onol.wip.prod.gaptecholapps.com'
      #     - requestDomain: 'secure.brol.wip.prod.gaptecholapps.com'
      #     - requestDomain: 'secure.atol.wip.prod.gaptecholapps.com'
      #     - requestDomain: 'secure.www.app.prod.gaptecholapps.com'
      #     - requestDomain: 'secure.onol.app.prod.gaptecholapps.com'
      #     - requestDomain: 'secure.brol.app.prod.gaptecholapps.com'
      #     - requestDomain: 'secure.atol.app.prod.gaptecholapps.com'
      #     - requestDomain: 'secure.www.wip.prod.gaptecholapps.ca'
      #     - requestDomain: 'secure.onol.wip.prod.gaptecholapps.ca'
      #     - requestDomain: 'secure.brol.wip.prod.gaptecholapps.ca'
      #     - requestDomain: 'secure.atol.wip.prod.gaptecholapps.ca'
      #     - requestDomain: 'secure.www.wip.prod.factory-gaptecholapps.com'
      #     - requestDomain: 'secure.brfol.wip.prod.factory-gaptecholapps.com'
      #     - requestDomain: 'secure.www.app.prod.factory-gaptecholapps.com'
      #     - requestDomain: 'secure.brfol.app.prod.factory-gaptecholapps.com'
      #     - requestDomain: 'secure.brfol.wip.prod.factory-gaptecholapps.ca'
      #     - requestDomain: 'secure.brfol.app.prod.factory-gaptecholapps.ca'
      #     - requestDomain: 'secure.www.app.prod.gaptecholapps.ca'
      #     - requestDomain: 'secure.onol.app.prod.gaptecholapps.ca'
      #     - requestDomain: 'secure.brol.app.prod.gaptecholapps.ca'
      #     - requestDomain: 'secure.atol.app.prod.gaptecholapps.ca'
  health:
    livenessProbe:
      endpoint: /healthz
      port: '3000'
      initialDelay: '5'
      timeout: '5'
      period: '10'
      failureThreshold: '5'
    readinessProbe:
      endpoint: /healthz
      port: '3000'
      initialDelay: '5'
      timeout: '5'
      period: '10'
      failureThreshold: '5'
  autoscaling:
    minReplicas: '3'
    maxReplicas: '30'
    cpuThreshold: '50'
    memoryThreshold: '50'
  resources:
    requestsCpu: 250m
    requestsMemory: 2Gi
    limitsCpu: 2
    limitsMemory: 2Gi
