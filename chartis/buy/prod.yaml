version: 1-stable
appName: buy-next
team: pt-webapps
environment: prod
affectedCI: 'Checkout-UI-Prod'
pcfAppName: none
appSpec:
  appType: nodejs
  observability:
    metrics:
      newRelic:
        enabled: true
# control:
#   forceDeploy: true
environmentVariables:
  NEW_RELIC_APP_NAME: buy-next-prod
  NEW_RELIC_DISTRIBUTED_TRACING_ENABLED: true
  SERVER_PORT: '3000'
  REDIS_READ_TIMEOUT: 100
  TARGET_ENV: 'prod'
  PMCS_SERVICE_URL: 'https://pmcs.aks.prod.azeus.gaptech.com'
  NAVIGATION_SERVICE_URL: 'http://browse-api-nginx-cache'
  ECOM_CLIENT_API_BASE_URL_US: 'https://api.gap.com'
  ECOM_CLIENT_API_BASE_URL_CA: 'https://api.gapcanada.ca'
  ECOM_CLIENT_API_BASE_URL_FACTORY_US: 'https://api.gapfactory.com'
  ECOM_CLIENT_API_BASE_URL_FACTORY_CA: 'https://api.gapfactory.ca'
  ECOM_SERVER_API_BASE_URL: 'https://internal-azeus-ecom-api.live.prod.gap.com'
  CATALOG_OMNI_PRODUCT_SERVICE_BASE_URL: 'https://catalog-apis-omni-product-service.aks.prod.azeus.gaptech.com'
  CATALOG_OMNI_CATEGORY_SERVICE_BASE_URL: 'https://catalog-apis-omni-category-service.aks.prod.azeus.gaptech.com'
  SECURE_ECOM_CLIENT_API_BASE_URL_US: 'https://api.gap.com'
  SECURE_ECOM_CLIENT_API_BASE_URL_CA: 'https://api.gapcanada.ca'
  SECURE_ECOM_CLIENT_API_BASE_URL_FACTORY_US: 'https://api.gapfactory.com'
  SECURE_ECOM_CLIENT_API_BASE_URL_FACTORY_CA: 'https://api.gapfactory.ca'
  APIGEE_INTERNAL_URL: 'https://api.azeus.gaptech.com'
  SERVER_OPTIMIZELY_CONFIG_URL: 'https://cdn.optimizely.com/public/6529002483/s/10740912459_10740912459.json'
  SHOPPING_BAG_SERVICE_URL: 'https://internal-azeus-ecom-api.live.prod.gap.com/commerce/shopping-bags'
  NEW_EMAIL_REGISTRATION_SERVICE_URL: 'https://api.gap.com/commerce/communication-preference/v2/subscriptions/email'
  NEW_SMS_REGISTRATION_SERVICE_URL: 'https://api.gap.com/commerce/communication-preference/v2/subscriptions/sms'
  ASSET_CLUSTER_URL: 'https://azeus-bananarepublic.live.prod.gap.com'
  NEXT_TELEMETRY_DISABLED: 1
  NEXT_OTEL_FETCH_DISABLED: 1
  NODE_OPTIONS: '-r newrelic --use-openssl-ca'
  BLOOMREACH_URL: 'https://core.dxpapi.com'
  CATALOG_INTERNAL_API_BASE_URL: https://ws-catalog-api-service.prod.azeus.gaptech.com
  CATALOG_STYLE_LOOKUP_API_BASE_URL: https://catalog-apis-lookup-service.aks.prod.azeus.gaptech.com
  LOG_LEVEL: 'warn'
secrets:
  secretKeys:
    - key: azure-blob-key
      environmentVariableKey: AZURE_BLOB_KEY
    - key: azure-redis-key
      environmentVariableKey: AZURE_REDIS_KEY
    - key: new-relic-license-key
      environmentVariableKey: NEW_RELIC_LICENSE_KEY
    - key: nav-service-api-key
      environmentVariableKey: NAV_SERVICE_API_KEY
    - key: power-reviews-config
      environmentVariableKey: POWER_REVIEWS_CONFIG
    - key: social-gallery-config-prod
      environmentVariableKey: SOCIAL_GALLERY_CONFIG
    - key: redirect-service-api-key
      environmentVariableKey: REDIRECT_SERVICE_API_KEY
    - key: private-fp-key
      environmentVariableKey: PRIVATE_FP_KEY
kubernetes:
  annotations:
    sidecar.istio.io/proxyMemory: 128Mi
    sidecar.istio.io/proxyMemoryLimit: 1Gi
    sidecar.istio.io/proxyCPU: 100m
    sidecar.istio.io/proxyCPULimit: 500m
  dockerImage: ecom-next
  dockerImageTag: "a7a9bd89e0"
  port: '3000'
  deploymentStrategy:
    canary:
      weight: 25
      sticky: true
      promoteCanary: false
      cookieCode: 'ui'
      cookieExpiration: 1h
      cookieDomainSegments:
        domainSegments:
          - requestDomain: 'www.gap.com'
          - requestDomain: 'oldnavy.gap.com'
          - requestDomain: 'bananarepublic.gap.com'
          - requestDomain: 'athleta.gap.com'
          - requestDomain: 'www.gapcanada.ca'
          - requestDomain: 'oldnavy.gapcanada.ca'
          - requestDomain: 'bananarepublic.gapcanada.ca'
          - requestDomain: 'athleta.gapcanada.ca'
          - requestDomain: 'bananarepublicfactory.gapfactory.com'
          - requestDomain: 'www.gapfactory.com'
          - requestDomain: 'bananarepublicfactory.gapfactory.ca'
          - requestDomain: 'www.gapfactory.ca'
          - requestDomain: 'secure-www.gap.com'
          - requestDomain: 'secure-oldnavy.gap.com'
          - requestDomain: 'secure-bananarepublic.gap.com'
          - requestDomain: 'secure-athleta.gap.com'
          - requestDomain: 'secure-www.gapcanada.ca'
          - requestDomain: 'secure-oldnavy.gapcanada.ca'
          - requestDomain: 'secure-bananarepublic.gapcanada.ca'
          - requestDomain: 'secure-athleta.gapcanada.ca'
          - requestDomain: 'secure-bananarepublicfactory.gapfactory.com'
          - requestDomain: 'secure-www.gapfactory.com'
          - requestDomain: 'secure-bananarepublicfactory.gapfactory.ca'
          - requestDomain: 'secure-www.gapfactory.ca'
          - requestDomain: 'origin-azeus-bananarepublicfactory.live.prod.gapfactory.ca'
          - requestDomain: 'secure-origin-azeus-bananarepublicfactory.live.prod.gapfactory.ca'
          - requestDomain: 'origin-azeus-www.live.prod.gapfactory.ca'
          - requestDomain: 'origin-azeus-www.live.prod.gapfactory.com'
          - requestDomain: 'secure-origin-azeus-www.live.prod.gapfactory.com'
          - requestDomain: 'origin-azeus-bananarepublicfactory.live.prod.gapfactory.com'
          - requestDomain: 'secure-origin-azeus-bananarepublicfactory.live.prod.gapfactory.com'
          - requestDomain: 'origin-azeus-www.live.prod.gap.com'
          - requestDomain: 'secure-origin-azeus-www.live.prod.gap.com'
          - requestDomain: 'origin-azeus-oldnavy.live.prod.gap.com'
          - requestDomain: 'secure-origin-azeus-oldnavy.live.prod.gap.com'
          - requestDomain: 'origin-azeus-athleta.live.prod.gap.com'
          - requestDomain: 'secure-origin-azeus-athleta.live.prod.gap.com'
          - requestDomain: 'origin-azeus-bananarepublic.live.prod.gap.com'
          - requestDomain: 'secure-origin-azeus-bananarepublic.live.prod.gap.com'
          - requestDomain: 'origin-azeus-bananarepublic.live.prod.gapcanada.ca'
          - requestDomain: 'secure-origin-azeus-bananarepublic.live.prod.gapcanada.ca'
          - requestDomain: 'origin-azeus-athleta.live.prod.gapcanada.ca'
          - requestDomain: 'secure-origin-azeus-athleta.live.prod.gapcanada.ca'
          - requestDomain: 'origin-azeus-oldnavy.live.prod.gapcanada.ca'
          - requestDomain: 'secure-origin-azeus-oldnavy.live.prod.gapcanada.ca'
          - requestDomain: 'origin-azeus-www.live.prod.gapcanada.ca'
          - requestDomain: 'secure-origin-azeus-www.live.prod.gapcanada.ca'
  health:
    livenessProbe:
      endpoint: /healthz
      port: '3000'
      initialDelay: '5'
      timeout: '5'
      period: '10'
      failureThreshold: '5'
    readinessProbe:
      endpoint: /healthz
      port: '3000'
      initialDelay: '5'
      timeout: '5'
      period: '10'
      failureThreshold: '5'
  autoscaling:
    minReplicas: '70'
    maxReplicas: '1000'
    memoryThreshold: '50'
    cpuThreshold: '50'
  resources:
    requestsCpu: 250m
    requestsMemory: 2Gi
    limitsCpu: 2
    limitsMemory: 2Gi
