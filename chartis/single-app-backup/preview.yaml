version: 1-stable
appName: ecom-next-preview
pcfAppName: none
team: pt-webapps
environment: prod
affectedCI: Ecom-Next
appSpec:
  appType: nodejs
  observability:
    metrics:
      newRelic:
        enabled: true
dns:
  additionalNames:
    - atol-next-preview
    - brol-next-preview
    - onol-next-preview
    - www-next-preview
    - brfol-next-preview
# internalAccessOnly: true
argoCD:
  syncOptions:
    - Force=true
externalServices:
  - host: internal-azeus-ecom-api.preview.app.prod.gaptecholapps.com
  - host: api.azeus.gaptech.com
  - host: collector.newrelic.com
  - host: internal-azeus.brol.preview.app.prod.gaptecholapps.com
  - host: catalog-apis-omni-product-service.aks.prod.azeus.gaptech.com
  - host: cdn.optimizely.com
  - host: browse-api-nginx-cache-preview.aks.prod.azeus.gaptech.com
  - host: readservices-b2c.powerreviews.com
  - host: search-page-preview.apps.cfcommerce.prod.azeus.gaptech.com
environmentVariables:
  NEW_RELIC_APP_NAME: ecom-next-preview
  REDIS_READ_TIMEOUT: 100
  SERVER_PORT: '3000'
  TARGET_ENV: 'preview'
  PMCS_SERVICE_URL: 'https://browse-api-nginx-cache-preview.aks.prod.azeus.gaptech.com/pmcs'
  PMCS_EDITIONS_SERVICE_URL: 'https://browse-api-nginx-cache-preview.aks.prod.azeus.gaptech.com/pmcs/edition'
  NAVIGATION_SERVICE_URL: 'https://browse-api-nginx-cache-preview.aks.prod.azeus.gaptech.com'
  ECOM_CLIENT_API_BASE_URL_US: 'https://internal-azeus-ecom-api.preview.wip.prod.gaptecholapps.com'
  ECOM_CLIENT_API_BASE_URL_CA: 'https://internal-azeus-ecom-api.preview.wip.prod.gaptecholapps.ca'
  ECOM_CLIENT_API_BASE_URL_FACTORY_US: 'https://internal-azeus-ecom-api.preview.wip.prod.factory-gaptecholapps.com'
  ECOM_CLIENT_API_BASE_URL_FACTORY_CA: 'https://internal-azeus-ecom-api.preview.wip.prod.factory-gaptecholapps.ca'
  ECOM_SERVER_API_BASE_URL: 'https://internal-azeus-ecom-api.preview.app.prod.gaptecholapps.com'
  CATALOG_OMNI_PRODUCT_SERVICE_BASE_URL: 'https://catalog-apis-omni-product-service.aks.prod.azeus.gaptech.com'
  CATALOG_OMNI_CATEGORY_SERVICE_BASE_URL: 'https://catalog-apis-omni-category-service.aks.prod.azeus.gaptech.com'
  SECURE_ECOM_CLIENT_API_BASE_URL_US: 'https://secure-internal-azeus-ecom-api.preview.wip.prod.gaptecholapps.com'
  SECURE_ECOM_CLIENT_API_BASE_URL_CA: 'https://secure-internal-azeus-ecom-api.preview.wip.prod.gaptecholapps.ca'
  SECURE_ECOM_CLIENT_API_BASE_URL_FACTORY_US: 'https://secure-internal-azeus-ecom-api.preview.wip.prod.factory-gaptecholapps.com'
  SECURE_ECOM_CLIENT_API_BASE_URL_FACTORY_CA: 'https://secure-internal-azeus-ecom-api.preview.wip.prod.factory-gaptecholapps.ca'
  APIGEE_INTERNAL_URL: 'https://api.azeus.gaptech.com'
  SERVER_OPTIMIZELY_CONFIG_URL: 'https://cdn.optimizely.com/public/6529002483/s/10738892798_10738892798.json'
  SHOPPING_BAG_SERVICE_URL: 'https://internal-azeus-ecom-api.preview.wip.prod.gaptecholapps.com/commerce/shopping-bags'
  NEW_EMAIL_REGISTRATION_SERVICE_URL: 'https://api.azeus.gaptech.com/commerce/communication-preference/v2/subscriptions/email'
  NEW_SMS_REGISTRATION_SERVICE_URL: 'https://api.azeus.gaptech.com/commerce/communication-preference/v2/subscriptions/sms'
  ASSET_CLUSTER_URL: 'https://internal-azeus.brol.preview.app.prod.gaptecholapps.com'
  NEXT_TELEMETRY_DISABLED: 1
  NODE_OPTIONS: '-r newrelic --use-openssl-ca'
  UV_USE_IO_URING: 0
  BLOOMREACH_URL: 'https://core.dxpapi.com'
secrets:
  secretKeys:
    - key: azure-blob-key-preview
      environmentVariableKey: AZURE_BLOB_KEY
    - key: azure-redis-key-preview
      environmentVariableKey: AZURE_REDIS_KEY
    - key: new-relic-license-key
      environmentVariableKey: NEW_RELIC_LICENSE_KEY
    - key: nav-service-api-key
      environmentVariableKey: NAV_SERVICE_API_KEY
    - key: power-reviews-config
      environmentVariableKey: POWER_REVIEWS_CONFIG
    - key: social-gallery-config-preview
      environmentVariableKey: SOCIAL_GALLERY_CONFIG
    - key: redirect-service-api-key
      environmentVariableKey: REDIRECT_SERVICE_API_KEY
kubernetes:
  annotations:
    sidecar.istio.io/proxyMemory: 128Mi
    sidecar.istio.io/proxyMemoryLimit: 1Gi
    sidecar.istio.io/proxyCPU: 100m
    sidecar.istio.io/proxyCPULimit: 500m
  dockerImage: ecom-next
  dockerImageTag: '39b24b7e6'
  port: '3000'
  deploymentStrategy:
    canary:
      weight: 100
      sticky: true
      promoteCanary: true
      cookieCode: 'ui'
      cookieExpiration: 8h
      cookieDomainSegments:
        domainSegments:
          - requestDomain: 'www.wip.prod.gaptecholapps.com'
          - requestDomain: 'onol.wip.prod.gaptecholapps.com'
          - requestDomain: 'brol.wip.prod.gaptecholapps.com'
          - requestDomain: 'atol.wip.prod.gaptecholapps.com'
          - requestDomain: 'www.app.prod.gaptecholapps.com'
          - requestDomain: 'onol.app.prod.gaptecholapps.com'
          - requestDomain: 'brol.app.prod.gaptecholapps.com'
          - requestDomain: 'atol.app.prod.gaptecholapps.com'
          - requestDomain: 'www.wip.prod.gaptecholapps.ca'
          - requestDomain: 'onol.wip.prod.gaptecholapps.ca'
          - requestDomain: 'brol.wip.prod.gaptecholapps.ca'
          - requestDomain: 'atol.wip.prod.gaptecholapps.ca'
          - requestDomain: 'www.app.prod.gaptecholapps.ca'
          - requestDomain: 'onol.app.prod.gaptecholapps.ca'
          - requestDomain: 'brol.app.prod.gaptecholapps.ca'
          - requestDomain: 'atol.app.prod.gaptecholapps.ca'
          - requestDomain: 'www.wip.prod.factory-gaptecholapps.com'
          - requestDomain: 'brfol.wip.prod.factory-gaptecholapps.com'
          - requestDomain: 'www.app.prod.factory-gaptecholapps.com'
          - requestDomain: 'brfol.app.prod.factory-gaptecholapps.com'
          - requestDomain: 'brfol.wip.prod.factory-gaptecholapps.ca'
          - requestDomain: 'brfol.app.prod.factory-gaptecholapps.ca'
          - requestDomain: 'secure.www.wip.prod.gaptecholapps.com'
          - requestDomain: 'secure.onol.wip.prod.gaptecholapps.com'
          - requestDomain: 'secure.brol.wip.prod.gaptecholapps.com'
          - requestDomain: 'secure.atol.wip.prod.gaptecholapps.com'
          - requestDomain: 'secure.www.app.prod.gaptecholapps.com'
          - requestDomain: 'secure.onol.app.prod.gaptecholapps.com'
          - requestDomain: 'secure.brol.app.prod.gaptecholapps.com'
          - requestDomain: 'secure.atol.app.prod.gaptecholapps.com'
          - requestDomain: 'secure.www.wip.prod.gaptecholapps.ca'
          - requestDomain: 'secure.onol.wip.prod.gaptecholapps.ca'
          - requestDomain: 'secure.brol.wip.prod.gaptecholapps.ca'
          - requestDomain: 'secure.atol.wip.prod.gaptecholapps.ca'
          - requestDomain: 'secure.www.wip.prod.factory-gaptecholapps.com'
          - requestDomain: 'secure.brfol.wip.prod.factory-gaptecholapps.com'
          - requestDomain: 'secure.www.app.prod.factory-gaptecholapps.com'
          - requestDomain: 'secure.brfol.app.prod.factory-gaptecholapps.com'
          - requestDomain: 'secure.brfol.wip.prod.factory-gaptecholapps.ca'
          - requestDomain: 'secure.brfol.app.prod.factory-gaptecholapps.ca'
          - requestDomain: 'secure.www.app.prod.gaptecholapps.ca'
          - requestDomain: 'secure.onol.app.prod.gaptecholapps.ca'
          - requestDomain: 'secure.brol.app.prod.gaptecholapps.ca'
          - requestDomain: 'secure.atol.app.prod.gaptecholapps.ca'
  health:
    livenessProbe:
      endpoint: /healthz
      port: '3000'
      initialDelay: '5'
      timeout: '5'
      period: '10'
      failureThreshold: '5'
    readinessProbe:
      endpoint: /healthz
      port: '3000'
      initialDelay: '5'
      timeout: '5'
      period: '10'
      failureThreshold: '5'
  autoscaling:
    minReplicas: '2'
    maxReplicas: '30'
    cpuThreshold: '70'
    memoryThreshold: '70'
  resources:
    requestsCpu: 250m
    requestsMemory: 1.5Gi
    limitsCpu: 2
    limitsMemory: 2Gi
