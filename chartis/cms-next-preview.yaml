version: 1-stable
appName: cms-next-preview
pcfAppName: none
team: pt-webapps
environment: stage
affectedCI: 'Ecom-Next'
appSpec:
  appType: nodejs
  observability:
    metrics:
      newRelic:
        enabled: true
environmentVariables:
  NEW_RELIC_APP_NAME: cms-next
  NEW_RELIC_DISTRIBUTED_TRACING_ENABLED: true
  REDIS_READ_TIMEOUT: 1
  REDIS_CONNECT_TIMEOUT: 1
  DISABLE_REDIS: 'true'
  BLOB_STORAGE_TIMEOUT: 1
  SERVER_PORT: '3000'
  TARGET_ENV: 'preview'
  PMCS_SERVICE_URL: 'https://pmcs-preview.aks.prod.azeus.gaptech.com'
  PMCS_EDITIONS_SERVICE_URL: 'https://pmcs-preview.aks.prod.azeus.gaptech.com/edition'
  NAVIGATION_SERVICE_URL: 'https://browse-api-nginx-cache-preview.aks.prod.azeus.gaptech.com'
  ECOM_CLIENT_API_BASE_WIP_URL_US: 'https://ecom-api.wip.stage.gaptecholapps.com'
  ECOM_CLIENT_API_BASE_WIP_URL_CA: 'https://ecom-api.wip.stage.gaptecholapps.ca'
  ECOM_CLIENT_API_BASE_WIP_URL_FACTORY_US: 'https://ecom-api.wip.stage.gaptecholapps.com'
  ECOM_CLIENT_API_BASE_WIP_URL_FACTORY_CA: 'https://ecom-api.wip.stage.gaptecholapps.ca'
  ECOM_CLIENT_API_BASE_URL_US: 'https://api.azeus.gaptech.com'
  ECOM_CLIENT_API_BASE_URL_CA: 'https://api.azeus.gaptech.com'
  ECOM_CLIENT_API_BASE_URL_FACTORY_US: 'https://api.azeus.gaptech.com'
  ECOM_CLIENT_API_BASE_URL_FACTORY_CA: 'https://api.azeus.gaptech.com'
  ECOM_CLIENT_API_BASE_APP_URL_US: 'https://ecom-api.app.stage.gaptecholapps.com'
  ECOM_CLIENT_API_BASE_APP_URL_CA: 'https://ecom-api.app.stage.gaptecholapps.ca'
  ECOM_CLIENT_API_BASE_APP_URL_FACTORY_US: 'https://ecom-api.app.stage.factory-gaptecholapps.com'
  ECOM_CLIENT_API_BASE_APP_URL_FACTORY_CA: 'https://ecom-api.app.stage.factory-gaptecholapps.ca'
  SECURE_ECOM_CLIENT_API_WIP_BASE_URL_US: 'https://ecom-api.wip.stage.gaptecholapps.com'
  SECURE_ECOM_CLIENT_API_WIP_BASE_URL_CA: 'https://ecom-api.wip.stage.gaptecholapps.ca'
  SECURE_ECOM_CLIENT_API_WIP_BASE_URL_FACTORY_US: 'https://ecom-api.wip.stage.factory-gaptecholapps.com'
  SECURE_ECOM_CLIENT_API_WIP_BASE_URL_FACTORY_CA: 'https://ecom-api.wip.stage.factory-gaptecholapps.ca'
  SECURE_ECOM_CLIENT_API_APP_BASE_URL_US: 'https://ecom-api.app.stage.gaptecholapps.com'
  SECURE_ECOM_CLIENT_API_APP_BASE_URL_CA: 'https://ecom-api.app.stage.gaptecholapps.ca'
  SECURE_ECOM_CLIENT_API_APP_BASE_URL_FACTORY_US: 'https://ecom-api.app.stage.factory-gaptecholapps.com'
  SECURE_ECOM_CLIENT_API_APP_BASE_URL_FACTORY_CA: 'https://ecom-api.app.stage.factory-gaptecholapps.ca'
  ECOM_SERVER_API_BASE_URL: 'https://stage.api.azeus.gaptech.com'
  CATALOG_OMNI_PRODUCT_SERVICE_BASE_URL: 'https://catalog-apis-omni-product-service.aks.prod.azeus.gaptech.com'
  CATALOG_OMNI_CATEGORY_SERVICE_BASE_URL: 'https://catalog-apis-omni-category-service.aks.prod.azeus.gaptech.com'
  SECURE_ECOM_CLIENT_API_BASE_URL_US: 'https://ecom-api.stage.gaptechol.com'
  SECURE_ECOM_CLIENT_API_BASE_URL_CA: 'https://ecom-api.stage.gaptechol.ca'
  SECURE_ECOM_CLIENT_API_BASE_URL_FACTORY_US: 'https://ecom-api.stage.factory-gaptechol.com'
  SECURE_ECOM_CLIENT_API_BASE_URL_FACTORY_CA: 'https://ecom-api.stage.factory-gaptechol.ca'
  APIGEE_INTERNAL_URL: 'https://stage.api.azeus.gaptech.com'
  SERVER_OPTIMIZELY_CONFIG_URL: 'https://cdn.optimizely.com/public/6529002483/s/10738892798_10738892798.json'
  SHOPPING_BAG_SERVICE_URL: 'https://secure-internal-azeus-ecom-api.live.stage.gaptechol.com/commerce/shopping-bags'
  NEW_EMAIL_REGISTRATION_SERVICE_URL: 'https://secure-internal-azeus-ecom-api.live.stage.gaptechol.com/commerce/communication-preference/v2/subscriptions/email'
  NEW_SMS_REGISTRATION_SERVICE_URL: 'https://stage.api.azeus.gaptech.com/commerce/communication-preference/v2/subscriptions/sms'
  ASSET_CLUSTER_URL: 'https://internal-azeus.brol.preview.app.prod.gaptecholapps.com'
  NEXT_TELEMETRY_DISABLED: 1
  NEXT_OTEL_FETCH_DISABLED: 1
  NODE_OPTIONS: '-r newrelic --use-openssl-ca'
  BLOOMREACH_URL: 'https://staging-core.dxpapi.com'
  CATALOG_INTERNAL_API_BASE_URL: https://ws-catalog-api-service.prod.azeus.gaptech.com
  CATALOG_STYLE_LOOKUP_API_BASE_URL: https://catalog-apis-lookup-service.aks.prod.azeus.gaptech.com
  LOG_LEVEL: 'warn'
secrets:
  secretKeys:
    - key: azure-blob-key
      environmentVariableKey: AZURE_BLOB_KEY
    - key: azure-redis-key
      environmentVariableKey: AZURE_REDIS_KEY
    - key: new-relic-license-key
      environmentVariableKey: NEW_RELIC_LICENSE_KEY
    - key: power-reviews-config
      environmentVariableKey: POWER_REVIEWS_CONFIG
    - key: social-gallery-config
      environmentVariableKey: SOCIAL_GALLERY_CONFIG
    - key: nav-service-api-key
      environmentVariableKey: NAV_SERVICE_API_KEY
    - key: redirect-service-api-key
      environmentVariableKey: REDIRECT_SERVICE_API_KEY
    - key: private-fp-key
      environmentVariableKey: PRIVATE_FP_KEY
kubernetes:
  annotations:
    sidecar.istio.io/proxyMemory: 128Mi
    sidecar.istio.io/proxyMemoryLimit: 1Gi
    sidecar.istio.io/proxyCPU: 100m
    sidecar.istio.io/proxyCPULimit: 500m
  dockerImage: ecom-next
  dockerImageTag: "f22fa17604"
  port: '3000'
  deploymentStrategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
      interval: 5
  health:
    livenessProbe:
      endpoint: /healthz
      port: '3000'
      initialDelay: '5'
      timeout: '5'
      period: '10'
      failureThreshold: '5'
    readinessProbe:
      endpoint: /healthz
      port: '3000'
      initialDelay: '5'
      timeout: '5'
      period: '10'
      failureThreshold: '5'
  autoscaling:
    minReplicas: '2'
    maxReplicas: '5'
    cpuThreshold: '50'
    memoryThreshold: '50'
  resources:
    requestsCpu: 250m
    requestsMemory: 2Gi
    limitsCpu: 2
    limitsMemory: 2Gi
